{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { Spinner, Container, Row, Col } from 'react-bootstrap';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requireEmailVerification = false\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isEmailVerified,\n    loading\n  } = useAuth();\n  const location = useLocation();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        minHeight: '50vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(Spinner, {\n            animation: \"border\",\n            role: \"status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 12\n    }, this);\n  }\n  if (requireEmailVerification && !isEmailVerified) {\n    // Redirect to email verification notice\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/email-verification\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(ProtectedRoute, \"WRKhpmLLUZUlDWCTB7JCNUOONtc=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "Spinner", "Container", "Row", "Col", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "requireEmailVerification", "_s", "isAuthenticated", "isEmailVerified", "loading", "location", "className", "style", "minHeight", "animation", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { Spinner, Container, Row, Col } from 'react-bootstrap';\nimport { useAuth } from '../../contexts/AuthContext';\n\ninterface ProtectedRouteProps {\n  children: ReactNode;\n  requireEmailVerification?: boolean;\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ \n  children, \n  requireEmailVerification = false \n}) => {\n  const { isAuthenticated, isEmailVerified, loading } = useAuth();\n  const location = useLocation();\n\n  if (loading) {\n    return (\n      <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '50vh' }}>\n        <Row>\n          <Col className=\"text-center\">\n            <Spinner animation=\"border\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </Spinner>\n            <div className=\"mt-2\">Loading...</div>\n          </Col>\n        </Row>\n      </Container>\n    );\n  }\n\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  if (requireEmailVerification && !isEmailVerified) {\n    // Redirect to email verification notice\n    return <Navigate to=\"/email-verification\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AAC9D,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOrD,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,QAAQ;EACRC,wBAAwB,GAAG;AAC7B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,eAAe;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC/D,MAAMY,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,IAAIgB,OAAO,EAAE;IACX,oBACET,OAAA,CAACL,SAAS;MAACgB,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAT,QAAA,eACnGJ,OAAA,CAACJ,GAAG;QAAAQ,QAAA,eACFJ,OAAA,CAACH,GAAG;UAACc,SAAS,EAAC,aAAa;UAAAP,QAAA,gBAC1BJ,OAAA,CAACN,OAAO;YAACoB,SAAS,EAAC,QAAQ;YAACC,IAAI,EAAC,QAAQ;YAAAX,QAAA,eACvCJ,OAAA;cAAMW,SAAS,EAAC,iBAAiB;cAAAP,QAAA,EAAC;YAAU;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACVnB,OAAA;YAAKW,SAAS,EAAC,MAAM;YAAAP,QAAA,EAAC;UAAU;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,IAAI,CAACZ,eAAe,EAAE;IACpB;IACA,oBAAOP,OAAA,CAACR,QAAQ;MAAC4B,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAEZ;MAAS,CAAE;MAACa,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;EAEA,IAAId,wBAAwB,IAAI,CAACG,eAAe,EAAE;IAChD;IACA,oBAAOR,OAAA,CAACR,QAAQ;MAAC4B,EAAE,EAAC,qBAAqB;MAACG,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtD;EAEA,oBAAOnB,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACE,EAAA,CAjCIH,cAA6C;EAAA,QAIKL,OAAO,EAC5CL,WAAW;AAAA;AAAA+B,EAAA,GALxBrB,cAA6C;AAmCnD,eAAeA,cAAc;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}