{"ast": null, "code": "import deepmerge from '@mui/utils/deepmerge';\nimport cssVarsParser from \"./cssVarsParser.js\";\nfunction prepareCssVars(theme) {\n  let parserConfig = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    getSelector = defaultGetSelector,\n    disableCssColorScheme,\n    colorSchemeSelector: selector\n  } = parserConfig;\n  // @ts-ignore - ignore components do not exist\n  const {\n    colorSchemes = {},\n    components,\n    defaultColorScheme = 'light',\n    ...otherTheme\n  } = theme;\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n    [defaultColorScheme]: defaultScheme,\n    ...otherColorSchemes\n  } = colorSchemes;\n  Object.entries(otherColorSchemes || {}).forEach(_ref => {\n    let [key, scheme] = _ref;\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (defaultScheme) {\n    // default color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(defaultScheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[defaultColorScheme] = {\n      css,\n      vars\n    };\n  }\n  function defaultGetSelector(colorScheme, cssObject) {\n    let rule = selector;\n    if (selector === 'class') {\n      rule = '.%s';\n    }\n    if (selector === 'data') {\n      rule = '[data-%s]';\n    }\n    if (selector?.startsWith('data-') && !selector.includes('%s')) {\n      // 'data-joy-color-scheme' -> '[data-joy-color-scheme=\"%s\"]'\n      rule = `[${selector}=\"%s\"]`;\n    }\n    if (colorScheme) {\n      if (rule === 'media') {\n        if (theme.defaultColorScheme === colorScheme) {\n          return ':root';\n        }\n        const mode = colorSchemes[colorScheme]?.palette?.mode || colorScheme;\n        return {\n          [`@media (prefers-color-scheme: ${mode})`]: {\n            ':root': cssObject\n          }\n        };\n      }\n      if (rule) {\n        if (theme.defaultColorScheme === colorScheme) {\n          return `:root, ${rule.replace('%s', String(colorScheme))}`;\n        }\n        return rule.replace('%s', String(colorScheme));\n      }\n    }\n    return ':root';\n  }\n  const generateThemeVars = () => {\n    let vars = {\n      ...rootVars\n    };\n    Object.entries(colorSchemesMap).forEach(_ref2 => {\n      let [, {\n        vars: schemeVars\n      }] = _ref2;\n      vars = deepmerge(vars, schemeVars);\n    });\n    return vars;\n  };\n  const generateStyleSheets = () => {\n    const stylesheets = [];\n    const colorScheme = theme.defaultColorScheme || 'light';\n    function insertStyleSheet(key, css) {\n      if (Object.keys(css).length) {\n        stylesheets.push(typeof key === 'string' ? {\n          [key]: {\n            ...css\n          }\n        } : key);\n      }\n    }\n    insertStyleSheet(getSelector(undefined, {\n      ...rootCss\n    }), rootCss);\n    const {\n      [colorScheme]: defaultSchemeVal,\n      ...other\n    } = colorSchemesMap;\n    if (defaultSchemeVal) {\n      // default color scheme has to come before other color schemes\n      const {\n        css\n      } = defaultSchemeVal;\n      const cssColorSheme = colorSchemes[colorScheme]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(colorScheme, {\n        ...finalCss\n      }), finalCss);\n    }\n    Object.entries(other).forEach(_ref3 => {\n      let [key, {\n        css\n      }] = _ref3;\n      const cssColorSheme = colorSchemes[key]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(key, {\n        ...finalCss\n      }), finalCss);\n    });\n    return stylesheets;\n  };\n  return {\n    vars: themeVars,\n    generateThemeVars,\n    generateStyleSheets\n  };\n}\nexport default prepareCssVars;", "map": {"version": 3, "names": ["deepmerge", "cssVarsParser", "prepareCssVars", "theme", "parserConfig", "arguments", "length", "undefined", "getSelector", "defaultGetSelector", "disableCssColorScheme", "colorSchemeSelector", "selector", "colorSchemes", "components", "defaultColorScheme", "otherTheme", "vars", "rootVars", "css", "rootCss", "varsWithDefaults", "rootVarsWithDefaults", "themeVars", "colorSchemesMap", "defaultScheme", "otherColorSchemes", "Object", "entries", "for<PERSON>ach", "_ref", "key", "scheme", "colorScheme", "cssObject", "rule", "startsWith", "includes", "mode", "palette", "replace", "String", "generateThemeVars", "_ref2", "schemeVars", "generateStyleSheets", "stylesheets", "insertStyleSheet", "keys", "push", "defaultSchemeVal", "other", "cssColorSheme", "finalCss", "_ref3"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/system/esm/cssVars/prepareCssVars.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nimport cssVarsParser from \"./cssVarsParser.js\";\nfunction prepareCssVars(theme, parserConfig = {}) {\n  const {\n    getSelector = defaultGetSelector,\n    disableCssColorScheme,\n    colorSchemeSelector: selector\n  } = parserConfig;\n  // @ts-ignore - ignore components do not exist\n  const {\n    colorSchemes = {},\n    components,\n    defaultColorScheme = 'light',\n    ...otherTheme\n  } = theme;\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n    [defaultColorScheme]: defaultScheme,\n    ...otherColorSchemes\n  } = colorSchemes;\n  Object.entries(otherColorSchemes || {}).forEach(([key, scheme]) => {\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (defaultScheme) {\n    // default color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(defaultScheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[defaultColorScheme] = {\n      css,\n      vars\n    };\n  }\n  function defaultGetSelector(colorScheme, cssObject) {\n    let rule = selector;\n    if (selector === 'class') {\n      rule = '.%s';\n    }\n    if (selector === 'data') {\n      rule = '[data-%s]';\n    }\n    if (selector?.startsWith('data-') && !selector.includes('%s')) {\n      // 'data-joy-color-scheme' -> '[data-joy-color-scheme=\"%s\"]'\n      rule = `[${selector}=\"%s\"]`;\n    }\n    if (colorScheme) {\n      if (rule === 'media') {\n        if (theme.defaultColorScheme === colorScheme) {\n          return ':root';\n        }\n        const mode = colorSchemes[colorScheme]?.palette?.mode || colorScheme;\n        return {\n          [`@media (prefers-color-scheme: ${mode})`]: {\n            ':root': cssObject\n          }\n        };\n      }\n      if (rule) {\n        if (theme.defaultColorScheme === colorScheme) {\n          return `:root, ${rule.replace('%s', String(colorScheme))}`;\n        }\n        return rule.replace('%s', String(colorScheme));\n      }\n    }\n    return ':root';\n  }\n  const generateThemeVars = () => {\n    let vars = {\n      ...rootVars\n    };\n    Object.entries(colorSchemesMap).forEach(([, {\n      vars: schemeVars\n    }]) => {\n      vars = deepmerge(vars, schemeVars);\n    });\n    return vars;\n  };\n  const generateStyleSheets = () => {\n    const stylesheets = [];\n    const colorScheme = theme.defaultColorScheme || 'light';\n    function insertStyleSheet(key, css) {\n      if (Object.keys(css).length) {\n        stylesheets.push(typeof key === 'string' ? {\n          [key]: {\n            ...css\n          }\n        } : key);\n      }\n    }\n    insertStyleSheet(getSelector(undefined, {\n      ...rootCss\n    }), rootCss);\n    const {\n      [colorScheme]: defaultSchemeVal,\n      ...other\n    } = colorSchemesMap;\n    if (defaultSchemeVal) {\n      // default color scheme has to come before other color schemes\n      const {\n        css\n      } = defaultSchemeVal;\n      const cssColorSheme = colorSchemes[colorScheme]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(colorScheme, {\n        ...finalCss\n      }), finalCss);\n    }\n    Object.entries(other).forEach(([key, {\n      css\n    }]) => {\n      const cssColorSheme = colorSchemes[key]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(key, {\n        ...finalCss\n      }), finalCss);\n    });\n    return stylesheets;\n  };\n  return {\n    vars: themeVars,\n    generateThemeVars,\n    generateStyleSheets\n  };\n}\nexport default prepareCssVars;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,cAAcA,CAACC,KAAK,EAAqB;EAAA,IAAnBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC9C,MAAM;IACJG,WAAW,GAAGC,kBAAkB;IAChCC,qBAAqB;IACrBC,mBAAmB,EAAEC;EACvB,CAAC,GAAGR,YAAY;EAChB;EACA,MAAM;IACJS,YAAY,GAAG,CAAC,CAAC;IACjBC,UAAU;IACVC,kBAAkB,GAAG,OAAO;IAC5B,GAAGC;EACL,CAAC,GAAGb,KAAK;EACT,MAAM;IACJc,IAAI,EAAEC,QAAQ;IACdC,GAAG,EAAEC,OAAO;IACZC,gBAAgB,EAAEC;EACpB,CAAC,GAAGrB,aAAa,CAACe,UAAU,EAAEZ,YAAY,CAAC;EAC3C,IAAImB,SAAS,GAAGD,oBAAoB;EACpC,MAAME,eAAe,GAAG,CAAC,CAAC;EAC1B,MAAM;IACJ,CAACT,kBAAkB,GAAGU,aAAa;IACnC,GAAGC;EACL,CAAC,GAAGb,YAAY;EAChBc,MAAM,CAACC,OAAO,CAACF,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAACG,OAAO,CAACC,IAAA,IAAmB;IAAA,IAAlB,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAAF,IAAA;IAC5D,MAAM;MACJb,IAAI;MACJE,GAAG;MACHE;IACF,CAAC,GAAGpB,aAAa,CAAC+B,MAAM,EAAE5B,YAAY,CAAC;IACvCmB,SAAS,GAAGvB,SAAS,CAACuB,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACO,GAAG,CAAC,GAAG;MACrBZ,GAAG;MACHF;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIQ,aAAa,EAAE;IACjB;IACA,MAAM;MACJN,GAAG;MACHF,IAAI;MACJI;IACF,CAAC,GAAGpB,aAAa,CAACwB,aAAa,EAAErB,YAAY,CAAC;IAC9CmB,SAAS,GAAGvB,SAAS,CAACuB,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACT,kBAAkB,CAAC,GAAG;MACpCI,GAAG;MACHF;IACF,CAAC;EACH;EACA,SAASR,kBAAkBA,CAACwB,WAAW,EAAEC,SAAS,EAAE;IAClD,IAAIC,IAAI,GAAGvB,QAAQ;IACnB,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACxBuB,IAAI,GAAG,KAAK;IACd;IACA,IAAIvB,QAAQ,KAAK,MAAM,EAAE;MACvBuB,IAAI,GAAG,WAAW;IACpB;IACA,IAAIvB,QAAQ,EAAEwB,UAAU,CAAC,OAAO,CAAC,IAAI,CAACxB,QAAQ,CAACyB,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC7D;MACAF,IAAI,GAAG,IAAIvB,QAAQ,QAAQ;IAC7B;IACA,IAAIqB,WAAW,EAAE;MACf,IAAIE,IAAI,KAAK,OAAO,EAAE;QACpB,IAAIhC,KAAK,CAACY,kBAAkB,KAAKkB,WAAW,EAAE;UAC5C,OAAO,OAAO;QAChB;QACA,MAAMK,IAAI,GAAGzB,YAAY,CAACoB,WAAW,CAAC,EAAEM,OAAO,EAAED,IAAI,IAAIL,WAAW;QACpE,OAAO;UACL,CAAC,iCAAiCK,IAAI,GAAG,GAAG;YAC1C,OAAO,EAAEJ;UACX;QACF,CAAC;MACH;MACA,IAAIC,IAAI,EAAE;QACR,IAAIhC,KAAK,CAACY,kBAAkB,KAAKkB,WAAW,EAAE;UAC5C,OAAO,UAAUE,IAAI,CAACK,OAAO,CAAC,IAAI,EAAEC,MAAM,CAACR,WAAW,CAAC,CAAC,EAAE;QAC5D;QACA,OAAOE,IAAI,CAACK,OAAO,CAAC,IAAI,EAAEC,MAAM,CAACR,WAAW,CAAC,CAAC;MAChD;IACF;IACA,OAAO,OAAO;EAChB;EACA,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIzB,IAAI,GAAG;MACT,GAAGC;IACL,CAAC;IACDS,MAAM,CAACC,OAAO,CAACJ,eAAe,CAAC,CAACK,OAAO,CAACc,KAAA,IAEjC;MAAA,IAFkC,GAAG;QAC1C1B,IAAI,EAAE2B;MACR,CAAC,CAAC,GAAAD,KAAA;MACA1B,IAAI,GAAGjB,SAAS,CAACiB,IAAI,EAAE2B,UAAU,CAAC;IACpC,CAAC,CAAC;IACF,OAAO3B,IAAI;EACb,CAAC;EACD,MAAM4B,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMb,WAAW,GAAG9B,KAAK,CAACY,kBAAkB,IAAI,OAAO;IACvD,SAASgC,gBAAgBA,CAAChB,GAAG,EAAEZ,GAAG,EAAE;MAClC,IAAIQ,MAAM,CAACqB,IAAI,CAAC7B,GAAG,CAAC,CAACb,MAAM,EAAE;QAC3BwC,WAAW,CAACG,IAAI,CAAC,OAAOlB,GAAG,KAAK,QAAQ,GAAG;UACzC,CAACA,GAAG,GAAG;YACL,GAAGZ;UACL;QACF,CAAC,GAAGY,GAAG,CAAC;MACV;IACF;IACAgB,gBAAgB,CAACvC,WAAW,CAACD,SAAS,EAAE;MACtC,GAAGa;IACL,CAAC,CAAC,EAAEA,OAAO,CAAC;IACZ,MAAM;MACJ,CAACa,WAAW,GAAGiB,gBAAgB;MAC/B,GAAGC;IACL,CAAC,GAAG3B,eAAe;IACnB,IAAI0B,gBAAgB,EAAE;MACpB;MACA,MAAM;QACJ/B;MACF,CAAC,GAAG+B,gBAAgB;MACpB,MAAME,aAAa,GAAGvC,YAAY,CAACoB,WAAW,CAAC,EAAEM,OAAO,EAAED,IAAI;MAC9D,MAAMe,QAAQ,GAAG,CAAC3C,qBAAqB,IAAI0C,aAAa,GAAG;QACzDnB,WAAW,EAAEmB,aAAa;QAC1B,GAAGjC;MACL,CAAC,GAAG;QACF,GAAGA;MACL,CAAC;MACD4B,gBAAgB,CAACvC,WAAW,CAACyB,WAAW,EAAE;QACxC,GAAGoB;MACL,CAAC,CAAC,EAAEA,QAAQ,CAAC;IACf;IACA1B,MAAM,CAACC,OAAO,CAACuB,KAAK,CAAC,CAACtB,OAAO,CAACyB,KAAA,IAEvB;MAAA,IAFwB,CAACvB,GAAG,EAAE;QACnCZ;MACF,CAAC,CAAC,GAAAmC,KAAA;MACA,MAAMF,aAAa,GAAGvC,YAAY,CAACkB,GAAG,CAAC,EAAEQ,OAAO,EAAED,IAAI;MACtD,MAAMe,QAAQ,GAAG,CAAC3C,qBAAqB,IAAI0C,aAAa,GAAG;QACzDnB,WAAW,EAAEmB,aAAa;QAC1B,GAAGjC;MACL,CAAC,GAAG;QACF,GAAGA;MACL,CAAC;MACD4B,gBAAgB,CAACvC,WAAW,CAACuB,GAAG,EAAE;QAChC,GAAGsB;MACL,CAAC,CAAC,EAAEA,QAAQ,CAAC;IACf,CAAC,CAAC;IACF,OAAOP,WAAW;EACpB,CAAC;EACD,OAAO;IACL7B,IAAI,EAAEM,SAAS;IACfmB,iBAAiB;IACjBG;EACF,CAAC;AACH;AACA,eAAe3C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}