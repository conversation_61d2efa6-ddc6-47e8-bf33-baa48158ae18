{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport Button from '@mui/material/Button';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The LoadingButton component functionality is now part of the Button component from Material UI.', '', \"You should use `import Button from '@mui/material/Button'`\", \"or `import { Button } from '@mui/material'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n};\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedLoadingButton(props, ref) {\n  warn();\n  return /*#__PURE__*/_jsx(Button, {\n    ref: ref,\n    ...props\n  });\n});", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "jsx", "_jsx", "warnedOnce", "warn", "console", "join", "forwardRef", "DeprecatedLoadingButton", "props", "ref"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/LoadingButton/LoadingButton.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport Button from '@mui/material/Button';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The LoadingButton component functionality is now part of the Button component from Material UI.', '', \"You should use `import Button from '@mui/material/Button'`\", \"or `import { Button } from '@mui/material'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n};\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedLoadingButton(props, ref) {\n  warn();\n  return /*#__PURE__*/_jsx(Button, {\n    ref: ref,\n    ...props\n  });\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,sGAAsG,EAAE,EAAE,EAAE,4DAA4D,EAAE,6CAA6C,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAClPH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;;AAED;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACQ,UAAU,CAAC,SAASC,uBAAuBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxFN,IAAI,CAAC,CAAC;EACN,OAAO,aAAaF,IAAI,CAACF,MAAM,EAAE;IAC/BU,GAAG,EAAEA,GAAG;IACR,GAAGD;EACL,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}