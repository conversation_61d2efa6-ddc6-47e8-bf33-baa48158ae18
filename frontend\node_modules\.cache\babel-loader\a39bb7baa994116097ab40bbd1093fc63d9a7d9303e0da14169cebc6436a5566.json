{"ast": null, "code": "import clsx from 'clsx';\nimport extractEventHandlers from \"../extractEventHandlers/index.js\";\nimport omitEventHandlers from \"../omitEventHandlers/index.js\";\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n    const mergedStyle = {\n      ...additionalProps?.style,\n      ...externalForwardedProps?.style,\n      ...externalSlotProps?.style\n    };\n    const props = {\n      ...additionalProps,\n      ...externalForwardedProps,\n      ...externalSlotProps\n    };\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers({\n    ...externalForwardedProps,\n    ...externalSlotProps\n  });\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps?.className, additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n  const mergedStyle = {\n    ...internalSlotProps?.style,\n    ...additionalProps?.style,\n    ...externalForwardedProps?.style,\n    ...externalSlotProps?.style\n  };\n  const props = {\n    ...internalSlotProps,\n    ...additionalProps,\n    ...otherPropsWithoutEventHandlers,\n    ...componentsPropsWithoutEventHandlers\n  };\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nexport default mergeSlotProps;", "map": {"version": 3, "names": ["clsx", "extractEventHandlers", "omitEventHandlers", "mergeSlotProps", "parameters", "getSlotProps", "additionalProps", "externalSlotProps", "externalForwardedProps", "className", "joinedClasses", "mergedStyle", "style", "props", "length", "Object", "keys", "internalRef", "undefined", "eventHandlers", "componentsPropsWithoutEventHandlers", "otherPropsWithoutEventHandlers", "internalSlotProps", "ref"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/utils/esm/mergeSlotProps/mergeSlotProps.js"], "sourcesContent": ["import clsx from 'clsx';\nimport extractEventHandlers from \"../extractEventHandlers/index.js\";\nimport omitEventHandlers from \"../omitEventHandlers/index.js\";\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n    const mergedStyle = {\n      ...additionalProps?.style,\n      ...externalForwardedProps?.style,\n      ...externalSlotProps?.style\n    };\n    const props = {\n      ...additionalProps,\n      ...externalForwardedProps,\n      ...externalSlotProps\n    };\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers({\n    ...externalForwardedProps,\n    ...externalSlotProps\n  });\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps?.className, additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n  const mergedStyle = {\n    ...internalSlotProps?.style,\n    ...additionalProps?.style,\n    ...externalForwardedProps?.style,\n    ...externalSlotProps?.style\n  };\n  const props = {\n    ...internalSlotProps,\n    ...additionalProps,\n    ...otherPropsWithoutEventHandlers,\n    ...componentsPropsWithoutEventHandlers\n  };\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nexport default mergeSlotProps;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,MAAM;AACvB,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAClC,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,sBAAsB;IACtBC;EACF,CAAC,GAAGL,UAAU;EACd,IAAI,CAACC,YAAY,EAAE;IACjB;IACA;IACA,MAAMK,aAAa,GAAGV,IAAI,CAACM,eAAe,EAAEG,SAAS,EAAEA,SAAS,EAAED,sBAAsB,EAAEC,SAAS,EAAEF,iBAAiB,EAAEE,SAAS,CAAC;IAClI,MAAME,WAAW,GAAG;MAClB,GAAGL,eAAe,EAAEM,KAAK;MACzB,GAAGJ,sBAAsB,EAAEI,KAAK;MAChC,GAAGL,iBAAiB,EAAEK;IACxB,CAAC;IACD,MAAMC,KAAK,GAAG;MACZ,GAAGP,eAAe;MAClB,GAAGE,sBAAsB;MACzB,GAAGD;IACL,CAAC;IACD,IAAIG,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;MAC5BD,KAAK,CAACJ,SAAS,GAAGC,aAAa;IACjC;IACA,IAAIK,MAAM,CAACC,IAAI,CAACL,WAAW,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACvCD,KAAK,CAACD,KAAK,GAAGD,WAAW;IAC3B;IACA,OAAO;MACLE,KAAK;MACLI,WAAW,EAAEC;IACf,CAAC;EACH;;EAEA;EACA;;EAEA,MAAMC,aAAa,GAAGlB,oBAAoB,CAAC;IACzC,GAAGO,sBAAsB;IACzB,GAAGD;EACL,CAAC,CAAC;EACF,MAAMa,mCAAmC,GAAGlB,iBAAiB,CAACK,iBAAiB,CAAC;EAChF,MAAMc,8BAA8B,GAAGnB,iBAAiB,CAACM,sBAAsB,CAAC;EAChF,MAAMc,iBAAiB,GAAGjB,YAAY,CAACc,aAAa,CAAC;;EAErD;EACA;EACA;EACA;EACA,MAAMT,aAAa,GAAGV,IAAI,CAACsB,iBAAiB,EAAEb,SAAS,EAAEH,eAAe,EAAEG,SAAS,EAAEA,SAAS,EAAED,sBAAsB,EAAEC,SAAS,EAAEF,iBAAiB,EAAEE,SAAS,CAAC;EAChK,MAAME,WAAW,GAAG;IAClB,GAAGW,iBAAiB,EAAEV,KAAK;IAC3B,GAAGN,eAAe,EAAEM,KAAK;IACzB,GAAGJ,sBAAsB,EAAEI,KAAK;IAChC,GAAGL,iBAAiB,EAAEK;EACxB,CAAC;EACD,MAAMC,KAAK,GAAG;IACZ,GAAGS,iBAAiB;IACpB,GAAGhB,eAAe;IAClB,GAAGe,8BAA8B;IACjC,GAAGD;EACL,CAAC;EACD,IAAIV,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;IAC5BD,KAAK,CAACJ,SAAS,GAAGC,aAAa;EACjC;EACA,IAAIK,MAAM,CAACC,IAAI,CAACL,WAAW,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;IACvCD,KAAK,CAACD,KAAK,GAAGD,WAAW;EAC3B;EACA,OAAO;IACLE,KAAK;IACLI,WAAW,EAAEK,iBAAiB,CAACC;EACjC,CAAC;AACH;AACA,eAAepB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}