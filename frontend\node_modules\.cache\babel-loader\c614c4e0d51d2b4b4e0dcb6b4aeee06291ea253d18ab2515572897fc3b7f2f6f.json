{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableHeadUtilityClass(slot) {\n  return generateUtilityClass('MuiTableHead', slot);\n}\nconst tableHeadClasses = generateUtilityClasses('MuiTableHead', ['root']);\nexport default tableHeadClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTableHeadUtilityClass", "slot", "tableHeadClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/TableHead/tableHeadClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableHeadUtilityClass(slot) {\n  return generateUtilityClass('MuiTableHead', slot);\n}\nconst tableHeadClasses = generateUtilityClasses('MuiTableHead', ['root']);\nexport default tableHeadClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,oBAAoB,CAAC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGJ,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC;AACzE,eAAeI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}