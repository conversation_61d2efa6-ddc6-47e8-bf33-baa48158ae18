{"ast": null, "code": "import { useEffect, useRef } from 'react';\n\n/**\n * Runs an effect only when the dependencies have changed, skipping the\n * initial \"on mount\" run. Caution, if the dependency list never changes,\n * the effect is **never run**\n *\n * ```ts\n *  const ref = useRef<HTMLInput>(null);\n *\n *  // focuses an element only if the focus changes, and not on mount\n *  useUpdateEffect(() => {\n *    const element = ref.current?.children[focusedIdx] as HTMLElement\n *\n *    element?.focus()\n *\n *  }, [focusedIndex])\n * ```\n * @param effect An effect to run on mount\n *\n * @category effects\n */\nfunction useUpdateEffect(fn, deps) {\n  const isFirst = useRef(true);\n  useEffect(() => {\n    if (isFirst.current) {\n      isFirst.current = false;\n      return;\n    }\n    return fn();\n  }, deps);\n}\nexport default useUpdateEffect;", "map": {"version": 3, "names": ["useEffect", "useRef", "useUpdateEffect", "fn", "deps", "<PERSON><PERSON><PERSON><PERSON>", "current"], "sources": ["C:/laragon/www/frontend/node_modules/@restart/hooks/esm/useUpdateEffect.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\n\n/**\n * Runs an effect only when the dependencies have changed, skipping the\n * initial \"on mount\" run. Caution, if the dependency list never changes,\n * the effect is **never run**\n *\n * ```ts\n *  const ref = useRef<HTMLInput>(null);\n *\n *  // focuses an element only if the focus changes, and not on mount\n *  useUpdateEffect(() => {\n *    const element = ref.current?.children[focusedIdx] as HTMLElement\n *\n *    element?.focus()\n *\n *  }, [focusedIndex])\n * ```\n * @param effect An effect to run on mount\n *\n * @category effects\n */\nfunction useUpdateEffect(fn, deps) {\n  const isFirst = useRef(true);\n  useEffect(() => {\n    if (isFirst.current) {\n      isFirst.current = false;\n      return;\n    }\n    return fn();\n  }, deps);\n}\nexport default useUpdateEffect;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,EAAE,EAAEC,IAAI,EAAE;EACjC,MAAMC,OAAO,GAAGJ,MAAM,CAAC,IAAI,CAAC;EAC5BD,SAAS,CAAC,MAAM;IACd,IAAIK,OAAO,CAACC,OAAO,EAAE;MACnBD,OAAO,CAACC,OAAO,GAAG,KAAK;MACvB;IACF;IACA,OAAOH,EAAE,CAAC,CAAC;EACb,CAAC,EAAEC,IAAI,CAAC;AACV;AACA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}