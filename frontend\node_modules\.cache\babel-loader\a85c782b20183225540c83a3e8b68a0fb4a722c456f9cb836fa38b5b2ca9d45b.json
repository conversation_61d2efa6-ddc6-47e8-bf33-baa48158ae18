{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ButtonGroup = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    size,\n    vertical = false,\n    className,\n    role = 'group',\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    ...rest\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-group');\n  let baseClass = prefix;\n  if (vertical) baseClass = `${prefix}-vertical`;\n  return /*#__PURE__*/_jsx(Component, {\n    ...rest,\n    ref: ref,\n    role: role,\n    className: classNames(className, baseClass, size && `${prefix}-${size}`)\n  });\n});\nButtonGroup.displayName = 'ButtonGroup';\nexport default ButtonGroup;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "ButtonGroup", "forwardRef", "_ref", "ref", "bsPrefix", "size", "vertical", "className", "role", "as", "Component", "rest", "prefix", "baseClass", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/ButtonGroup.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ButtonGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  vertical = false,\n  className,\n  role = 'group',\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...rest\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-group');\n  let baseClass = prefix;\n  if (vertical) baseClass = `${prefix}-vertical`;\n  return /*#__PURE__*/_jsx(Component, {\n    ...rest,\n    ref: ref,\n    role: role,\n    className: classNames(className, baseClass, size && `${prefix}-${size}`)\n  });\n});\nButtonGroup.displayName = 'ButtonGroup';\nexport default ButtonGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAAC,IAAA,EAS/CC,GAAG,KAAK;EAAA,IATwC;IACjDC,QAAQ;IACRC,IAAI;IACJC,QAAQ,GAAG,KAAK;IAChBC,SAAS;IACTC,IAAI,GAAG,OAAO;IACd;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrB,GAAGC;EACL,CAAC,GAAAT,IAAA;EACC,MAAMU,MAAM,GAAGf,kBAAkB,CAACO,QAAQ,EAAE,WAAW,CAAC;EACxD,IAAIS,SAAS,GAAGD,MAAM;EACtB,IAAIN,QAAQ,EAAEO,SAAS,GAAG,GAAGD,MAAM,WAAW;EAC9C,OAAO,aAAab,IAAI,CAACW,SAAS,EAAE;IAClC,GAAGC,IAAI;IACPR,GAAG,EAAEA,GAAG;IACRK,IAAI,EAAEA,IAAI;IACVD,SAAS,EAAEZ,UAAU,CAACY,SAAS,EAAEM,SAAS,EAAER,IAAI,IAAI,GAAGO,MAAM,IAAIP,IAAI,EAAE;EACzE,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,WAAW,CAACc,WAAW,GAAG,aAAa;AACvC,eAAed,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}