{"ast": null, "code": "import extendTheme from \"./createThemeWithVars.js\";\nlet warnedOnce = false;\nexport default function deprecatedExtendTheme(...args) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The `experimental_extendTheme` has been stabilized.', '', \"You should use `import { extendTheme } from '@mui/material/styles'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return extendTheme(...args);\n}", "map": {"version": 3, "names": ["extendTheme", "warnedOnce", "deprecatedExtendTheme", "args", "console", "warn", "join"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/styles/experimental_extendTheme.js"], "sourcesContent": ["import extendTheme from \"./createThemeWithVars.js\";\nlet warnedOnce = false;\nexport default function deprecatedExtendTheme(...args) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The `experimental_extendTheme` has been stabilized.', '', \"You should use `import { extendTheme } from '@mui/material/styles'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return extendTheme(...args);\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,0BAA0B;AAClD,IAAIC,UAAU,GAAG,KAAK;AACtB,eAAe,SAASC,qBAAqBA,CAAC,GAAGC,IAAI,EAAE;EACrD,IAAI,CAACF,UAAU,EAAE;IACfG,OAAO,CAACC,IAAI,CAAC,CAAC,0DAA0D,EAAE,EAAE,EAAE,qEAAqE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChKL,UAAU,GAAG,IAAI;EACnB;EACA,OAAOD,WAAW,CAAC,GAAGG,IAAI,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}