{"ast": null, "code": "/**\n * @license React\n * react-is.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n  REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n  REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\nSymbol.for(\"react.provider\");\nvar REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n  REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n  REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n  REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n  REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n  REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n  REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n  REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"),\n  REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\nfunction typeOf(object) {\n  if (\"object\" === typeof object && null !== object) {\n    var $$typeof = object.$$typeof;\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        switch (object = object.type, object) {\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n          case REACT_SUSPENSE_LIST_TYPE:\n          case REACT_VIEW_TRANSITION_TYPE:\n            return object;\n          default:\n            switch (object = object && object.$$typeof, object) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n                return object;\n              case REACT_CONSUMER_TYPE:\n                return object;\n              default:\n                return $$typeof;\n            }\n        }\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n}\nexports.ContextConsumer = REACT_CONSUMER_TYPE;\nexports.ContextProvider = REACT_CONTEXT_TYPE;\nexports.Element = REACT_ELEMENT_TYPE;\nexports.ForwardRef = REACT_FORWARD_REF_TYPE;\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.Lazy = REACT_LAZY_TYPE;\nexports.Memo = REACT_MEMO_TYPE;\nexports.Portal = REACT_PORTAL_TYPE;\nexports.Profiler = REACT_PROFILER_TYPE;\nexports.StrictMode = REACT_STRICT_MODE_TYPE;\nexports.Suspense = REACT_SUSPENSE_TYPE;\nexports.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\nexports.isContextConsumer = function (object) {\n  return typeOf(object) === REACT_CONSUMER_TYPE;\n};\nexports.isContextProvider = function (object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n};\nexports.isElement = function (object) {\n  return \"object\" === typeof object && null !== object && object.$$typeof === REACT_ELEMENT_TYPE;\n};\nexports.isForwardRef = function (object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n};\nexports.isFragment = function (object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n};\nexports.isLazy = function (object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n};\nexports.isMemo = function (object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n};\nexports.isPortal = function (object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n};\nexports.isProfiler = function (object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n};\nexports.isStrictMode = function (object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n};\nexports.isSuspense = function (object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n};\nexports.isSuspenseList = function (object) {\n  return typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n};\nexports.isValidElementType = function (type) {\n  return \"string\" === typeof type || \"function\" === typeof type || type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || \"object\" === typeof type && null !== type && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_CONSUMER_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_CLIENT_REFERENCE || void 0 !== type.getModuleId) ? !0 : !1;\n};\nexports.typeOf = typeOf;", "map": {"version": 3, "names": ["REACT_ELEMENT_TYPE", "Symbol", "for", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_CONSUMER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_VIEW_TRANSITION_TYPE", "REACT_CLIENT_REFERENCE", "typeOf", "object", "$$typeof", "type", "exports", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Fragment", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "SuspenseList", "isContextConsumer", "isContextProvider", "isElement", "isForwardRef", "isFragment", "isLazy", "isMemo", "isPortal", "isProfiler", "isStrictMode", "isSuspense", "isSuspenseList", "isValidElementType", "getModuleId"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/utils/node_modules/react-is/cjs/react-is.production.js"], "sourcesContent": ["/**\n * @license React\n * react-is.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n  REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n  REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\nSymbol.for(\"react.provider\");\nvar REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n  REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n  REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n  REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n  REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n  REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n  REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n  REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"),\n  REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\nfunction typeOf(object) {\n  if (\"object\" === typeof object && null !== object) {\n    var $$typeof = object.$$typeof;\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        switch (((object = object.type), object)) {\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n          case REACT_SUSPENSE_LIST_TYPE:\n          case REACT_VIEW_TRANSITION_TYPE:\n            return object;\n          default:\n            switch (((object = object && object.$$typeof), object)) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n                return object;\n              case REACT_CONSUMER_TYPE:\n                return object;\n              default:\n                return $$typeof;\n            }\n        }\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n}\nexports.ContextConsumer = REACT_CONSUMER_TYPE;\nexports.ContextProvider = REACT_CONTEXT_TYPE;\nexports.Element = REACT_ELEMENT_TYPE;\nexports.ForwardRef = REACT_FORWARD_REF_TYPE;\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.Lazy = REACT_LAZY_TYPE;\nexports.Memo = REACT_MEMO_TYPE;\nexports.Portal = REACT_PORTAL_TYPE;\nexports.Profiler = REACT_PROFILER_TYPE;\nexports.StrictMode = REACT_STRICT_MODE_TYPE;\nexports.Suspense = REACT_SUSPENSE_TYPE;\nexports.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\nexports.isContextConsumer = function (object) {\n  return typeOf(object) === REACT_CONSUMER_TYPE;\n};\nexports.isContextProvider = function (object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n};\nexports.isElement = function (object) {\n  return (\n    \"object\" === typeof object &&\n    null !== object &&\n    object.$$typeof === REACT_ELEMENT_TYPE\n  );\n};\nexports.isForwardRef = function (object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n};\nexports.isFragment = function (object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n};\nexports.isLazy = function (object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n};\nexports.isMemo = function (object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n};\nexports.isPortal = function (object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n};\nexports.isProfiler = function (object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n};\nexports.isStrictMode = function (object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n};\nexports.isSuspense = function (object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n};\nexports.isSuspenseList = function (object) {\n  return typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n};\nexports.isValidElementType = function (type) {\n  return \"string\" === typeof type ||\n    \"function\" === typeof type ||\n    type === REACT_FRAGMENT_TYPE ||\n    type === REACT_PROFILER_TYPE ||\n    type === REACT_STRICT_MODE_TYPE ||\n    type === REACT_SUSPENSE_TYPE ||\n    type === REACT_SUSPENSE_LIST_TYPE ||\n    (\"object\" === typeof type &&\n      null !== type &&\n      (type.$$typeof === REACT_LAZY_TYPE ||\n        type.$$typeof === REACT_MEMO_TYPE ||\n        type.$$typeof === REACT_CONTEXT_TYPE ||\n        type.$$typeof === REACT_CONSUMER_TYPE ||\n        type.$$typeof === REACT_FORWARD_REF_TYPE ||\n        type.$$typeof === REACT_CLIENT_REFERENCE ||\n        void 0 !== type.getModuleId))\n    ? !0\n    : !1;\n};\nexports.typeOf = typeOf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ,IAAIA,kBAAkB,GAAGC,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC;EAC/DC,iBAAiB,GAAGF,MAAM,CAACC,GAAG,CAAC,cAAc,CAAC;EAC9CE,mBAAmB,GAAGH,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;EAClDG,sBAAsB,GAAGJ,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC;EACxDI,mBAAmB,GAAGL,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;AACpDD,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;AAC5B,IAAIK,mBAAmB,GAAGN,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;EACpDM,kBAAkB,GAAGP,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC;EAChDO,sBAAsB,GAAGR,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC;EACxDQ,mBAAmB,GAAGT,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;EAClDS,wBAAwB,GAAGV,MAAM,CAACC,GAAG,CAAC,qBAAqB,CAAC;EAC5DU,eAAe,GAAGX,MAAM,CAACC,GAAG,CAAC,YAAY,CAAC;EAC1CW,eAAe,GAAGZ,MAAM,CAACC,GAAG,CAAC,YAAY,CAAC;EAC1CY,0BAA0B,GAAGb,MAAM,CAACC,GAAG,CAAC,uBAAuB,CAAC;EAChEa,sBAAsB,GAAGd,MAAM,CAACC,GAAG,CAAC,wBAAwB,CAAC;AAC/D,SAASc,MAAMA,CAACC,MAAM,EAAE;EACtB,IAAI,QAAQ,KAAK,OAAOA,MAAM,IAAI,IAAI,KAAKA,MAAM,EAAE;IACjD,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;IAC9B,QAAQA,QAAQ;MACd,KAAKlB,kBAAkB;QACrB,QAAUiB,MAAM,GAAGA,MAAM,CAACE,IAAI,EAAGF,MAAM;UACrC,KAAKb,mBAAmB;UACxB,KAAKE,mBAAmB;UACxB,KAAKD,sBAAsB;UAC3B,KAAKK,mBAAmB;UACxB,KAAKC,wBAAwB;UAC7B,KAAKG,0BAA0B;YAC7B,OAAOG,MAAM;UACf;YACE,QAAUA,MAAM,GAAGA,MAAM,IAAIA,MAAM,CAACC,QAAQ,EAAGD,MAAM;cACnD,KAAKT,kBAAkB;cACvB,KAAKC,sBAAsB;cAC3B,KAAKI,eAAe;cACpB,KAAKD,eAAe;gBAClB,OAAOK,MAAM;cACf,KAAKV,mBAAmB;gBACtB,OAAOU,MAAM;cACf;gBACE,OAAOC,QAAQ;YACnB;QACJ;MACF,KAAKf,iBAAiB;QACpB,OAAOe,QAAQ;IACnB;EACF;AACF;AACAE,OAAO,CAACC,eAAe,GAAGd,mBAAmB;AAC7Ca,OAAO,CAACE,eAAe,GAAGd,kBAAkB;AAC5CY,OAAO,CAACG,OAAO,GAAGvB,kBAAkB;AACpCoB,OAAO,CAACI,UAAU,GAAGf,sBAAsB;AAC3CW,OAAO,CAACK,QAAQ,GAAGrB,mBAAmB;AACtCgB,OAAO,CAACM,IAAI,GAAGb,eAAe;AAC9BO,OAAO,CAACO,IAAI,GAAGf,eAAe;AAC9BQ,OAAO,CAACQ,MAAM,GAAGzB,iBAAiB;AAClCiB,OAAO,CAACS,QAAQ,GAAGvB,mBAAmB;AACtCc,OAAO,CAACU,UAAU,GAAGzB,sBAAsB;AAC3Ce,OAAO,CAACW,QAAQ,GAAGrB,mBAAmB;AACtCU,OAAO,CAACY,YAAY,GAAGrB,wBAAwB;AAC/CS,OAAO,CAACa,iBAAiB,GAAG,UAAUhB,MAAM,EAAE;EAC5C,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKV,mBAAmB;AAC/C,CAAC;AACDa,OAAO,CAACc,iBAAiB,GAAG,UAAUjB,MAAM,EAAE;EAC5C,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKT,kBAAkB;AAC9C,CAAC;AACDY,OAAO,CAACe,SAAS,GAAG,UAAUlB,MAAM,EAAE;EACpC,OACE,QAAQ,KAAK,OAAOA,MAAM,IAC1B,IAAI,KAAKA,MAAM,IACfA,MAAM,CAACC,QAAQ,KAAKlB,kBAAkB;AAE1C,CAAC;AACDoB,OAAO,CAACgB,YAAY,GAAG,UAAUnB,MAAM,EAAE;EACvC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKR,sBAAsB;AAClD,CAAC;AACDW,OAAO,CAACiB,UAAU,GAAG,UAAUpB,MAAM,EAAE;EACrC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKb,mBAAmB;AAC/C,CAAC;AACDgB,OAAO,CAACkB,MAAM,GAAG,UAAUrB,MAAM,EAAE;EACjC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKJ,eAAe;AAC3C,CAAC;AACDO,OAAO,CAACmB,MAAM,GAAG,UAAUtB,MAAM,EAAE;EACjC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKL,eAAe;AAC3C,CAAC;AACDQ,OAAO,CAACoB,QAAQ,GAAG,UAAUvB,MAAM,EAAE;EACnC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKd,iBAAiB;AAC7C,CAAC;AACDiB,OAAO,CAACqB,UAAU,GAAG,UAAUxB,MAAM,EAAE;EACrC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKX,mBAAmB;AAC/C,CAAC;AACDc,OAAO,CAACsB,YAAY,GAAG,UAAUzB,MAAM,EAAE;EACvC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKZ,sBAAsB;AAClD,CAAC;AACDe,OAAO,CAACuB,UAAU,GAAG,UAAU1B,MAAM,EAAE;EACrC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKP,mBAAmB;AAC/C,CAAC;AACDU,OAAO,CAACwB,cAAc,GAAG,UAAU3B,MAAM,EAAE;EACzC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKN,wBAAwB;AACpD,CAAC;AACDS,OAAO,CAACyB,kBAAkB,GAAG,UAAU1B,IAAI,EAAE;EAC3C,OAAO,QAAQ,KAAK,OAAOA,IAAI,IAC7B,UAAU,KAAK,OAAOA,IAAI,IAC1BA,IAAI,KAAKf,mBAAmB,IAC5Be,IAAI,KAAKb,mBAAmB,IAC5Ba,IAAI,KAAKd,sBAAsB,IAC/Bc,IAAI,KAAKT,mBAAmB,IAC5BS,IAAI,KAAKR,wBAAwB,IAChC,QAAQ,KAAK,OAAOQ,IAAI,IACvB,IAAI,KAAKA,IAAI,KACZA,IAAI,CAACD,QAAQ,KAAKL,eAAe,IAChCM,IAAI,CAACD,QAAQ,KAAKN,eAAe,IACjCO,IAAI,CAACD,QAAQ,KAAKV,kBAAkB,IACpCW,IAAI,CAACD,QAAQ,KAAKX,mBAAmB,IACrCY,IAAI,CAACD,QAAQ,KAAKT,sBAAsB,IACxCU,IAAI,CAACD,QAAQ,KAAKH,sBAAsB,IACxC,KAAK,CAAC,KAAKI,IAAI,CAAC2B,WAAW,CAAE,GAC/B,CAAC,CAAC,GACF,CAAC,CAAC;AACR,CAAC;AACD1B,OAAO,CAACJ,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}