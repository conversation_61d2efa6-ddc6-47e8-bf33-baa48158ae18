{"ast": null, "code": "import * as React from 'react';\nimport { ENTERING, EXITING } from 'react-transition-group/Transition';\nimport Fade from './Fade';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst fadeStyles = {\n  [ENTERING]: 'showing',\n  [EXITING]: 'showing show'\n};\nconst ToastFade = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/_jsx(Fade, {\n  ...props,\n  ref: ref,\n  transitionClasses: fadeStyles\n}));\nToastFade.displayName = 'ToastFade';\nexport default ToastFade;", "map": {"version": 3, "names": ["React", "ENTERING", "EXITING", "Fade", "jsx", "_jsx", "fadeStyles", "ToastFade", "forwardRef", "props", "ref", "transitionClasses", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/ToastFade.js"], "sourcesContent": ["import * as React from 'react';\nimport { ENTERING, EXITING } from 'react-transition-group/Transition';\nimport Fade from './Fade';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst fadeStyles = {\n  [ENTERING]: 'showing',\n  [EXITING]: 'showing show'\n};\nconst ToastFade = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/_jsx(Fade, {\n  ...props,\n  ref: ref,\n  transitionClasses: fadeStyles\n}));\nToastFade.displayName = 'ToastFade';\nexport default ToastFade;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,OAAO,QAAQ,mCAAmC;AACrE,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG;EACjB,CAACL,QAAQ,GAAG,SAAS;EACrB,CAACC,OAAO,GAAG;AACb,CAAC;AACD,MAAMK,SAAS,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK,aAAaL,IAAI,CAACF,IAAI,EAAE;EACtF,GAAGM,KAAK;EACRC,GAAG,EAAEA,GAAG;EACRC,iBAAiB,EAAEL;AACrB,CAAC,CAAC,CAAC;AACHC,SAAS,CAACK,WAAW,GAAG,WAAW;AACnC,eAAeL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}