{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The MobileDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { MobileDateRangePicker } from '@mui/x-date-pickers-pro'`\", \"or `import { MobileDateRangePicker } from '@mui/x-date-pickers-pro/MobileDateRangePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The MobileDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst MobileDateRangePicker = /*#__PURE__*/React.forwardRef(function DeprecatedMobileDateRangePicker() {\n  warn();\n  return null;\n});\nexport default MobileDateRangePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "MobileDateRangePicker", "forwardRef", "DeprecatedMobileDateRangePicker"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/MobileDateRangePicker/MobileDateRangePicker.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The MobileDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { MobileDateRangePicker } from '@mui/x-date-pickers-pro'`\", \"or `import { MobileDateRangePicker } from '@mui/x-date-pickers-pro/MobileDateRangePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The MobileDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst MobileDateRangePicker = /*#__PURE__*/React.forwardRef(function DeprecatedMobileDateRangePicker() {\n  warn();\n  return null;\n});\nexport default MobileDateRangePicker;"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,iGAAiG,EAAE,EAAE,EAAE,kFAAkF,EAAE,4FAA4F,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7ZH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,qBAAqB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,+BAA+BA,CAAA,EAAG;EACrGL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}