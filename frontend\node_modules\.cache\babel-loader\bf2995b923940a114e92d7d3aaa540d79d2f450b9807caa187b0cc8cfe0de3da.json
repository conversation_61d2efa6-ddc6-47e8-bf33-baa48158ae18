{"ast": null, "code": "import api,{endpoints}from'./api';class AuthService{constructor(){this.adminStatusCache=null;this.cacheExpiry=0;this.CACHE_DURATION=5*60*1000;}// 5 minutes\n// Register new user\nasync register(data){const response=await api.post(endpoints.register,data);const{user,token}=response.data;if(token){this.setAuthData(user,token);}return response.data;}// Login user\nasync login(credentials){const response=await api.post(endpoints.login,credentials);const{user,token}=response.data;if(token){this.setAuthData(user,token);}return response.data;}// Logout user\nasync logout(){try{await api.post(endpoints.logout);}catch(error){console.error('Logout error:',error);}finally{this.clearAuthData();}}// Get current user profile\nasync getProfile(){const response=await api.get(endpoints.profile);return response.data.user;}// Update user profile\nasync updateProfile(data){const response=await api.put(endpoints.updateProfile,data);const user=response.data.user;// Update stored user data\nlocalStorage.setItem('user',JSON.stringify(user));return user;}// Upload avatar\nasync uploadAvatar(file){const formData=new FormData();formData.append('avatar',file);const response=await api.post(endpoints.uploadAvatar,formData,{headers:{'Content-Type':'multipart/form-data'}});const user=response.data.user;localStorage.setItem('user',JSON.stringify(user));return user;}// Forgot password\nasync forgotPassword(email){const response=await api.post(endpoints.forgotPassword,{email});return response.data;}// Reset password\nasync resetPassword(data){const response=await api.post(endpoints.resetPassword,data);return response.data;}// Resend email verification\nasync resendVerification(){const response=await api.post(endpoints.resendVerification);return response.data;}// Check if current user is admin\nasync checkAdminStatus(){// Return cached result if still valid\nif(this.adminStatusCache&&Date.now()<this.cacheExpiry){return this.adminStatusCache;}try{const response=await api.get(endpoints.adminStatus);const result=response.data;// Cache the result\nthis.adminStatusCache=result;this.cacheExpiry=Date.now()+this.CACHE_DURATION;return result;}catch(error){var _error$response,_error$response$data;// If unauthorized or network error, assume not admin\nconst result={success:false,is_admin:false,message:((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'Failed to check admin status'};// Cache negative result for shorter time\nthis.adminStatusCache=result;this.cacheExpiry=Date.now()+30*1000;// 30 seconds\nreturn result;}}// Check if user is admin (cached)\nasync isAdmin(){const status=await this.checkAdminStatus();return status.is_admin;}// Clear admin status cache (call when user logs out or status changes)\nclearAdminCache(){this.adminStatusCache=null;this.cacheExpiry=0;}// Get CMS page edit URL\ngetCmsPageEditUrl(pageId){const baseUrl=process.env.REACT_APP_BACKEND_URL||'http://localhost:8000';return`${baseUrl}/admin/cms-pages/${pageId}/edit`;}// Create admin session and navigate to admin panel\nasync navigateToAdminPanel(targetPath){try{const response=await api.post(endpoints.adminSession);if(response.data.success){// Open SSO URL in new tab - this will create the web session and redirect to admin\nlet ssoUrl=response.data.admin_url;// If target path is provided, add it as redirect parameter\nif(targetPath){const baseUrl=process.env.REACT_APP_BACKEND_URL||'http://localhost:8000';const fullTargetUrl=`${baseUrl}${targetPath}`;ssoUrl+=`?redirect=${encodeURIComponent(fullTargetUrl)}`;}window.open(ssoUrl,'_blank','noopener,noreferrer');}else{throw new Error(response.data.message||'Failed to create admin session');}}catch(error){var _error$response2,_error$response2$data;throw new Error(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||'Failed to access admin panel');}}// Helper methods\nsetAuthData(user,token){localStorage.setItem('auth_token',token);localStorage.setItem('user',JSON.stringify(user));}clearAuthData(){localStorage.removeItem('auth_token');localStorage.removeItem('user');this.clearAdminCache();}getStoredUser(){const userStr=localStorage.getItem('user');return userStr?JSON.parse(userStr):null;}getStoredToken(){return localStorage.getItem('auth_token');}isAuthenticated(){return!!this.getStoredToken();}isEmailVerified(){const user=this.getStoredUser();return!!(user!==null&&user!==void 0&&user.email_verified_at);}}const authService=new AuthService();export default authService;", "map": {"version": 3, "names": ["api", "endpoints", "AuthService", "constructor", "adminStatusCache", "cacheExpiry", "CACHE_DURATION", "register", "data", "response", "post", "user", "token", "setAuthData", "login", "credentials", "logout", "error", "console", "clearAuthData", "getProfile", "get", "profile", "updateProfile", "put", "localStorage", "setItem", "JSON", "stringify", "uploadAvatar", "file", "formData", "FormData", "append", "headers", "forgotPassword", "email", "resetPassword", "resendVerification", "checkAdminStatus", "Date", "now", "adminStatus", "result", "_error$response", "_error$response$data", "success", "is_admin", "message", "isAdmin", "status", "clearAdminCache", "getCmsPageEditUrl", "pageId", "baseUrl", "process", "env", "REACT_APP_BACKEND_URL", "navigateToAdminPanel", "targetPath", "adminSession", "ssoUrl", "admin_url", "fullTargetUrl", "encodeURIComponent", "window", "open", "Error", "_error$response2", "_error$response2$data", "removeItem", "getStoredUser", "userStr", "getItem", "parse", "getStoredToken", "isAuthenticated", "isEmailVerified", "email_verified_at", "authService"], "sources": ["C:/laragon/www/frontend/src/services/authService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  phone?: string;\n  bio?: string;\n  avatar?: string;\n  date_of_birth?: string;\n  role: 'admin' | 'user';\n  is_active: boolean;\n  email_verified_at?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  password_confirmation: string;\n  phone?: string;\n  date_of_birth?: string;\n}\n\nexport interface AuthResponse {\n  message: string;\n  user: User;\n  token: string;\n  redirect_url?: string;\n}\n\nexport interface UpdateProfileData {\n  name?: string;\n  email?: string;\n  phone?: string;\n  bio?: string;\n  date_of_birth?: string;\n  current_password?: string;\n  password?: string;\n  password_confirmation?: string;\n}\n\nexport interface AdminStatusResponse {\n  success: boolean;\n  is_admin: boolean;\n  user?: User;\n  message?: string;\n}\n\nclass AuthService {\n  private adminStatusCache: AdminStatusResponse | null = null;\n  private cacheExpiry: number = 0;\n  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\n  // Register new user\n  async register(data: RegisterData): Promise<AuthResponse> {\n    const response = await api.post(endpoints.register, data);\n    const { user, token } = response.data;\n\n    if (token) {\n      this.setAuthData(user, token);\n    }\n\n    return response.data;\n  }\n\n  // Login user\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    const response = await api.post(endpoints.login, credentials);\n    const { user, token } = response.data;\n\n    if (token) {\n      this.setAuthData(user, token);\n    }\n\n    return response.data;\n  }\n\n  // Logout user\n  async logout(): Promise<void> {\n    try {\n      await api.post(endpoints.logout);\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      this.clearAuthData();\n    }\n  }\n\n  // Get current user profile\n  async getProfile(): Promise<User> {\n    const response = await api.get(endpoints.profile);\n    return response.data.user;\n  }\n\n  // Update user profile\n  async updateProfile(data: UpdateProfileData): Promise<User> {\n    const response = await api.put(endpoints.updateProfile, data);\n    const user = response.data.user;\n\n    // Update stored user data\n    localStorage.setItem('user', JSON.stringify(user));\n\n    return user;\n  }\n\n  // Upload avatar\n  async uploadAvatar(file: File): Promise<User> {\n    const formData = new FormData();\n    formData.append('avatar', file);\n\n    const response = await api.post(endpoints.uploadAvatar, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n\n    const user = response.data.user;\n    localStorage.setItem('user', JSON.stringify(user));\n\n    return user;\n  }\n\n  // Forgot password\n  async forgotPassword(email: string): Promise<{ message: string }> {\n    const response = await api.post(endpoints.forgotPassword, { email });\n    return response.data;\n  }\n\n  // Reset password\n  async resetPassword(data: {\n    token: string;\n    email: string;\n    password: string;\n    password_confirmation: string;\n  }): Promise<{ message: string }> {\n    const response = await api.post(endpoints.resetPassword, data);\n    return response.data;\n  }\n\n  // Resend email verification\n  async resendVerification(): Promise<{ message: string }> {\n    const response = await api.post(endpoints.resendVerification);\n    return response.data;\n  }\n\n  // Check if current user is admin\n  async checkAdminStatus(): Promise<AdminStatusResponse> {\n    // Return cached result if still valid\n    if (this.adminStatusCache && Date.now() < this.cacheExpiry) {\n      return this.adminStatusCache;\n    }\n\n    try {\n      const response = await api.get(endpoints.adminStatus);\n      const result: AdminStatusResponse = response.data;\n\n      // Cache the result\n      this.adminStatusCache = result;\n      this.cacheExpiry = Date.now() + this.CACHE_DURATION;\n\n      return result;\n    } catch (error: any) {\n      // If unauthorized or network error, assume not admin\n      const result: AdminStatusResponse = {\n        success: false,\n        is_admin: false,\n        message: error.response?.data?.message || 'Failed to check admin status'\n      };\n\n      // Cache negative result for shorter time\n      this.adminStatusCache = result;\n      this.cacheExpiry = Date.now() + (30 * 1000); // 30 seconds\n\n      return result;\n    }\n  }\n\n  // Check if user is admin (cached)\n  async isAdmin(): Promise<boolean> {\n    const status = await this.checkAdminStatus();\n    return status.is_admin;\n  }\n\n  // Clear admin status cache (call when user logs out or status changes)\n  clearAdminCache(): void {\n    this.adminStatusCache = null;\n    this.cacheExpiry = 0;\n  }\n\n  // Get CMS page edit URL\n  getCmsPageEditUrl(pageId: number): string {\n    const baseUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n    return `${baseUrl}/admin/cms-pages/${pageId}/edit`;\n  }\n\n  // Create admin session and navigate to admin panel\n  async navigateToAdminPanel(targetPath?: string): Promise<void> {\n    try {\n      const response = await api.post(endpoints.adminSession);\n      if (response.data.success) {\n        // Open SSO URL in new tab - this will create the web session and redirect to admin\n        let ssoUrl = response.data.admin_url;\n\n        // If target path is provided, add it as redirect parameter\n        if (targetPath) {\n          const baseUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n          const fullTargetUrl = `${baseUrl}${targetPath}`;\n          ssoUrl += `?redirect=${encodeURIComponent(fullTargetUrl)}`;\n        }\n\n        window.open(ssoUrl, '_blank', 'noopener,noreferrer');\n      } else {\n        throw new Error(response.data.message || 'Failed to create admin session');\n      }\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to access admin panel');\n    }\n  }\n\n  // Helper methods\n  setAuthData(user: User, token: string): void {\n    localStorage.setItem('auth_token', token);\n    localStorage.setItem('user', JSON.stringify(user));\n  }\n\n  clearAuthData(): void {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user');\n    this.clearAdminCache();\n  }\n\n  getStoredUser(): User | null {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  }\n\n  getStoredToken(): string | null {\n    return localStorage.getItem('auth_token');\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getStoredToken();\n  }\n\n  isEmailVerified(): boolean {\n    const user = this.getStoredUser();\n    return !!user?.email_verified_at;\n  }\n}\n\nconst authService = new AuthService();\nexport default authService;\n"], "mappings": "AAAA,MAAO,CAAAA,GAAG,EAAIC,SAAS,KAAQ,OAAO,CAwDtC,KAAM,CAAAC,WAAY,CAAAC,YAAA,OACRC,gBAAgB,CAA+B,IAAI,MACnDC,WAAW,CAAW,CAAC,MACdC,cAAc,CAAG,CAAC,CAAG,EAAE,CAAG,IAAI,EAAE;AAEjD;AACA,KAAM,CAAAC,QAAQA,CAACC,IAAkB,CAAyB,CACxD,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACM,QAAQ,CAAEC,IAAI,CAAC,CACzD,KAAM,CAAEG,IAAI,CAAEC,KAAM,CAAC,CAAGH,QAAQ,CAACD,IAAI,CAErC,GAAII,KAAK,CAAE,CACT,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEC,KAAK,CAAC,CAC/B,CAEA,MAAO,CAAAH,QAAQ,CAACD,IAAI,CACtB,CAEA;AACA,KAAM,CAAAM,KAAKA,CAACC,WAA6B,CAAyB,CAChE,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACa,KAAK,CAAEC,WAAW,CAAC,CAC7D,KAAM,CAAEJ,IAAI,CAAEC,KAAM,CAAC,CAAGH,QAAQ,CAACD,IAAI,CAErC,GAAII,KAAK,CAAE,CACT,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEC,KAAK,CAAC,CAC/B,CAEA,MAAO,CAAAH,QAAQ,CAACD,IAAI,CACtB,CAEA;AACA,KAAM,CAAAQ,MAAMA,CAAA,CAAkB,CAC5B,GAAI,CACF,KAAM,CAAAhB,GAAG,CAACU,IAAI,CAACT,SAAS,CAACe,MAAM,CAAC,CAClC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CAAC,OAAS,CACR,IAAI,CAACE,aAAa,CAAC,CAAC,CACtB,CACF,CAEA;AACA,KAAM,CAAAC,UAAUA,CAAA,CAAkB,CAChC,KAAM,CAAAX,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACqB,GAAG,CAACpB,SAAS,CAACqB,OAAO,CAAC,CACjD,MAAO,CAAAb,QAAQ,CAACD,IAAI,CAACG,IAAI,CAC3B,CAEA;AACA,KAAM,CAAAY,aAAaA,CAACf,IAAuB,CAAiB,CAC1D,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACwB,GAAG,CAACvB,SAAS,CAACsB,aAAa,CAAEf,IAAI,CAAC,CAC7D,KAAM,CAAAG,IAAI,CAAGF,QAAQ,CAACD,IAAI,CAACG,IAAI,CAE/B;AACAc,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC,CAElD,MAAO,CAAAA,IAAI,CACb,CAEA;AACA,KAAM,CAAAkB,YAAYA,CAACC,IAAU,CAAiB,CAC5C,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAEH,IAAI,CAAC,CAE/B,KAAM,CAAArB,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACU,IAAI,CAACT,SAAS,CAAC4B,YAAY,CAAEE,QAAQ,CAAE,CAChEG,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAvB,IAAI,CAAGF,QAAQ,CAACD,IAAI,CAACG,IAAI,CAC/Bc,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC,CAElD,MAAO,CAAAA,IAAI,CACb,CAEA;AACA,KAAM,CAAAwB,cAAcA,CAACC,KAAa,CAAgC,CAChE,KAAM,CAAA3B,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACkC,cAAc,CAAE,CAAEC,KAAM,CAAC,CAAC,CACpE,MAAO,CAAA3B,QAAQ,CAACD,IAAI,CACtB,CAEA;AACA,KAAM,CAAA6B,aAAaA,CAAC7B,IAKnB,CAAgC,CAC/B,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACoC,aAAa,CAAE7B,IAAI,CAAC,CAC9D,MAAO,CAAAC,QAAQ,CAACD,IAAI,CACtB,CAEA;AACA,KAAM,CAAA8B,kBAAkBA,CAAA,CAAiC,CACvD,KAAM,CAAA7B,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACqC,kBAAkB,CAAC,CAC7D,MAAO,CAAA7B,QAAQ,CAACD,IAAI,CACtB,CAEA;AACA,KAAM,CAAA+B,gBAAgBA,CAAA,CAAiC,CACrD;AACA,GAAI,IAAI,CAACnC,gBAAgB,EAAIoC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAACpC,WAAW,CAAE,CAC1D,MAAO,KAAI,CAACD,gBAAgB,CAC9B,CAEA,GAAI,CACF,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACqB,GAAG,CAACpB,SAAS,CAACyC,WAAW,CAAC,CACrD,KAAM,CAAAC,MAA2B,CAAGlC,QAAQ,CAACD,IAAI,CAEjD;AACA,IAAI,CAACJ,gBAAgB,CAAGuC,MAAM,CAC9B,IAAI,CAACtC,WAAW,CAAGmC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAACnC,cAAc,CAEnD,MAAO,CAAAqC,MAAM,CACf,CAAE,MAAO1B,KAAU,CAAE,KAAA2B,eAAA,CAAAC,oBAAA,CACnB;AACA,KAAM,CAAAF,MAA2B,CAAG,CAClCG,OAAO,CAAE,KAAK,CACdC,QAAQ,CAAE,KAAK,CACfC,OAAO,CAAE,EAAAJ,eAAA,CAAA3B,KAAK,CAACR,QAAQ,UAAAmC,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBpC,IAAI,UAAAqC,oBAAA,iBAApBA,oBAAA,CAAsBG,OAAO,GAAI,8BAC5C,CAAC,CAED;AACA,IAAI,CAAC5C,gBAAgB,CAAGuC,MAAM,CAC9B,IAAI,CAACtC,WAAW,CAAGmC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAI,EAAE,CAAG,IAAK,CAAE;AAE7C,MAAO,CAAAE,MAAM,CACf,CACF,CAEA;AACA,KAAM,CAAAM,OAAOA,CAAA,CAAqB,CAChC,KAAM,CAAAC,MAAM,CAAG,KAAM,KAAI,CAACX,gBAAgB,CAAC,CAAC,CAC5C,MAAO,CAAAW,MAAM,CAACH,QAAQ,CACxB,CAEA;AACAI,eAAeA,CAAA,CAAS,CACtB,IAAI,CAAC/C,gBAAgB,CAAG,IAAI,CAC5B,IAAI,CAACC,WAAW,CAAG,CAAC,CACtB,CAEA;AACA+C,iBAAiBA,CAACC,MAAc,CAAU,CACxC,KAAM,CAAAC,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,EAAI,uBAAuB,CAC5E,MAAO,GAAGH,OAAO,oBAAoBD,MAAM,OAAO,CACpD,CAEA;AACA,KAAM,CAAAK,oBAAoBA,CAACC,UAAmB,CAAiB,CAC7D,GAAI,CACF,KAAM,CAAAlD,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACU,IAAI,CAACT,SAAS,CAAC2D,YAAY,CAAC,CACvD,GAAInD,QAAQ,CAACD,IAAI,CAACsC,OAAO,CAAE,CACzB;AACA,GAAI,CAAAe,MAAM,CAAGpD,QAAQ,CAACD,IAAI,CAACsD,SAAS,CAEpC;AACA,GAAIH,UAAU,CAAE,CACd,KAAM,CAAAL,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,EAAI,uBAAuB,CAC5E,KAAM,CAAAM,aAAa,CAAG,GAAGT,OAAO,GAAGK,UAAU,EAAE,CAC/CE,MAAM,EAAI,aAAaG,kBAAkB,CAACD,aAAa,CAAC,EAAE,CAC5D,CAEAE,MAAM,CAACC,IAAI,CAACL,MAAM,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACtD,CAAC,IAAM,CACL,KAAM,IAAI,CAAAM,KAAK,CAAC1D,QAAQ,CAACD,IAAI,CAACwC,OAAO,EAAI,gCAAgC,CAAC,CAC5E,CACF,CAAE,MAAO/B,KAAU,CAAE,KAAAmD,gBAAA,CAAAC,qBAAA,CACnB,KAAM,IAAI,CAAAF,KAAK,CAAC,EAAAC,gBAAA,CAAAnD,KAAK,CAACR,QAAQ,UAAA2D,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB5D,IAAI,UAAA6D,qBAAA,iBAApBA,qBAAA,CAAsBrB,OAAO,GAAI,8BAA8B,CAAC,CAClF,CACF,CAEA;AACAnC,WAAWA,CAACF,IAAU,CAAEC,KAAa,CAAQ,CAC3Ca,YAAY,CAACC,OAAO,CAAC,YAAY,CAAEd,KAAK,CAAC,CACzCa,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC,CACpD,CAEAQ,aAAaA,CAAA,CAAS,CACpBM,YAAY,CAAC6C,UAAU,CAAC,YAAY,CAAC,CACrC7C,YAAY,CAAC6C,UAAU,CAAC,MAAM,CAAC,CAC/B,IAAI,CAACnB,eAAe,CAAC,CAAC,CACxB,CAEAoB,aAAaA,CAAA,CAAgB,CAC3B,KAAM,CAAAC,OAAO,CAAG/C,YAAY,CAACgD,OAAO,CAAC,MAAM,CAAC,CAC5C,MAAO,CAAAD,OAAO,CAAG7C,IAAI,CAAC+C,KAAK,CAACF,OAAO,CAAC,CAAG,IAAI,CAC7C,CAEAG,cAAcA,CAAA,CAAkB,CAC9B,MAAO,CAAAlD,YAAY,CAACgD,OAAO,CAAC,YAAY,CAAC,CAC3C,CAEAG,eAAeA,CAAA,CAAY,CACzB,MAAO,CAAC,CAAC,IAAI,CAACD,cAAc,CAAC,CAAC,CAChC,CAEAE,eAAeA,CAAA,CAAY,CACzB,KAAM,CAAAlE,IAAI,CAAG,IAAI,CAAC4D,aAAa,CAAC,CAAC,CACjC,MAAO,CAAC,EAAC5D,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEmE,iBAAiB,EAClC,CACF,CAEA,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAA7E,WAAW,CAAC,CAAC,CACrC,cAAe,CAAA6E,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}