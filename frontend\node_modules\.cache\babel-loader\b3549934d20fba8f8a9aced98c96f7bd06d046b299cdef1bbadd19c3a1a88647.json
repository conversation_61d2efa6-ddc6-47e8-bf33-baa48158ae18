{"ast": null, "code": "import clsx from 'clsx';\n\n/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param defaultProps\n * @param props\n * @param mergeClassNameAndStyle If `true`, merges `className` and `style` props instead of overriding them.\n *   When `false` (default), props override defaultProps. When `true`, `className` values are concatenated\n *   and `style` objects are merged with props taking precedence.\n * @returns resolved props\n */\nexport default function resolveProps(defaultProps, props) {\n  let mergeClassNameAndStyle = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  const output = {\n    ...props\n  };\n  for (const key in defaultProps) {\n    if (Object.prototype.hasOwnProperty.call(defaultProps, key)) {\n      const propName = key;\n      if (propName === 'components' || propName === 'slots') {\n        output[propName] = {\n          ...defaultProps[propName],\n          ...output[propName]\n        };\n      } else if (propName === 'componentsProps' || propName === 'slotProps') {\n        const defaultSlotProps = defaultProps[propName];\n        const slotProps = props[propName];\n        if (!slotProps) {\n          output[propName] = defaultSlotProps || {};\n        } else if (!defaultSlotProps) {\n          output[propName] = slotProps;\n        } else {\n          output[propName] = {\n            ...slotProps\n          };\n          for (const slotKey in defaultSlotProps) {\n            if (Object.prototype.hasOwnProperty.call(defaultSlotProps, slotKey)) {\n              const slotPropName = slotKey;\n              output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName], mergeClassNameAndStyle);\n            }\n          }\n        }\n      } else if (propName === 'className' && mergeClassNameAndStyle && props.className) {\n        output.className = clsx(defaultProps?.className, props?.className);\n      } else if (propName === 'style' && mergeClassNameAndStyle && props.style) {\n        output.style = {\n          ...defaultProps?.style,\n          ...props?.style\n        };\n      } else if (output[propName] === undefined) {\n        output[propName] = defaultProps[propName];\n      }\n    }\n  }\n  return output;\n}", "map": {"version": 3, "names": ["clsx", "resolveProps", "defaultProps", "props", "mergeClassNameAndStyle", "arguments", "length", "undefined", "output", "key", "Object", "prototype", "hasOwnProperty", "call", "propName", "defaultSlotProps", "slotProps", "<PERSON><PERSON><PERSON>", "slotPropName", "className", "style"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/utils/esm/resolveProps/resolveProps.js"], "sourcesContent": ["import clsx from 'clsx';\n\n/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param defaultProps\n * @param props\n * @param mergeClassNameAndStyle If `true`, merges `className` and `style` props instead of overriding them.\n *   When `false` (default), props override defaultProps. When `true`, `className` values are concatenated\n *   and `style` objects are merged with props taking precedence.\n * @returns resolved props\n */\nexport default function resolveProps(defaultProps, props, mergeClassNameAndStyle = false) {\n  const output = {\n    ...props\n  };\n  for (const key in defaultProps) {\n    if (Object.prototype.hasOwnProperty.call(defaultProps, key)) {\n      const propName = key;\n      if (propName === 'components' || propName === 'slots') {\n        output[propName] = {\n          ...defaultProps[propName],\n          ...output[propName]\n        };\n      } else if (propName === 'componentsProps' || propName === 'slotProps') {\n        const defaultSlotProps = defaultProps[propName];\n        const slotProps = props[propName];\n        if (!slotProps) {\n          output[propName] = defaultSlotProps || {};\n        } else if (!defaultSlotProps) {\n          output[propName] = slotProps;\n        } else {\n          output[propName] = {\n            ...slotProps\n          };\n          for (const slotKey in defaultSlotProps) {\n            if (Object.prototype.hasOwnProperty.call(defaultSlotProps, slotKey)) {\n              const slotPropName = slotKey;\n              output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName], mergeClassNameAndStyle);\n            }\n          }\n        }\n      } else if (propName === 'className' && mergeClassNameAndStyle && props.className) {\n        output.className = clsx(defaultProps?.className, props?.className);\n      } else if (propName === 'style' && mergeClassNameAndStyle && props.style) {\n        output.style = {\n          ...defaultProps?.style,\n          ...props?.style\n        };\n      } else if (output[propName] === undefined) {\n        output[propName] = defaultProps[propName];\n      }\n    }\n  }\n  return output;\n}"], "mappings": "AAAA,OAAOA,IAAI,MAAM,MAAM;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAEC,KAAK,EAAkC;EAAA,IAAhCC,sBAAsB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACtF,MAAMG,MAAM,GAAG;IACb,GAAGL;EACL,CAAC;EACD,KAAK,MAAMM,GAAG,IAAIP,YAAY,EAAE;IAC9B,IAAIQ,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,YAAY,EAAEO,GAAG,CAAC,EAAE;MAC3D,MAAMK,QAAQ,GAAGL,GAAG;MACpB,IAAIK,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,OAAO,EAAE;QACrDN,MAAM,CAACM,QAAQ,CAAC,GAAG;UACjB,GAAGZ,YAAY,CAACY,QAAQ,CAAC;UACzB,GAAGN,MAAM,CAACM,QAAQ;QACpB,CAAC;MACH,CAAC,MAAM,IAAIA,QAAQ,KAAK,iBAAiB,IAAIA,QAAQ,KAAK,WAAW,EAAE;QACrE,MAAMC,gBAAgB,GAAGb,YAAY,CAACY,QAAQ,CAAC;QAC/C,MAAME,SAAS,GAAGb,KAAK,CAACW,QAAQ,CAAC;QACjC,IAAI,CAACE,SAAS,EAAE;UACdR,MAAM,CAACM,QAAQ,CAAC,GAAGC,gBAAgB,IAAI,CAAC,CAAC;QAC3C,CAAC,MAAM,IAAI,CAACA,gBAAgB,EAAE;UAC5BP,MAAM,CAACM,QAAQ,CAAC,GAAGE,SAAS;QAC9B,CAAC,MAAM;UACLR,MAAM,CAACM,QAAQ,CAAC,GAAG;YACjB,GAAGE;UACL,CAAC;UACD,KAAK,MAAMC,OAAO,IAAIF,gBAAgB,EAAE;YACtC,IAAIL,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACE,gBAAgB,EAAEE,OAAO,CAAC,EAAE;cACnE,MAAMC,YAAY,GAAGD,OAAO;cAC5BT,MAAM,CAACM,QAAQ,CAAC,CAACI,YAAY,CAAC,GAAGjB,YAAY,CAACc,gBAAgB,CAACG,YAAY,CAAC,EAAEF,SAAS,CAACE,YAAY,CAAC,EAAEd,sBAAsB,CAAC;YAChI;UACF;QACF;MACF,CAAC,MAAM,IAAIU,QAAQ,KAAK,WAAW,IAAIV,sBAAsB,IAAID,KAAK,CAACgB,SAAS,EAAE;QAChFX,MAAM,CAACW,SAAS,GAAGnB,IAAI,CAACE,YAAY,EAAEiB,SAAS,EAAEhB,KAAK,EAAEgB,SAAS,CAAC;MACpE,CAAC,MAAM,IAAIL,QAAQ,KAAK,OAAO,IAAIV,sBAAsB,IAAID,KAAK,CAACiB,KAAK,EAAE;QACxEZ,MAAM,CAACY,KAAK,GAAG;UACb,GAAGlB,YAAY,EAAEkB,KAAK;UACtB,GAAGjB,KAAK,EAAEiB;QACZ,CAAC;MACH,CAAC,MAAM,IAAIZ,MAAM,CAACM,QAAQ,CAAC,KAAKP,SAAS,EAAE;QACzCC,MAAM,CAACM,QAAQ,CAAC,GAAGZ,YAAY,CAACY,QAAQ,CAAC;MAC3C;IACF;EACF;EACA,OAAON,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}