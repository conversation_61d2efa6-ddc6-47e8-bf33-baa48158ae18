{"ast": null, "code": "import api from'./api';class SettingsService{constructor(){this.cache=new Map();this.CACHE_DURATION=5*60*1000;}// 5 minutes\n/**\n   * Get all general settings\n   */async getSettings(){const cacheKey='general_settings';const cached=this.getFromCache(cacheKey);if(cached){return cached;}try{const response=await api.get('/settings');const settings=response.data.data;this.setCache(cacheKey,settings);return settings;}catch(error){console.error('Failed to fetch general settings:',error);// Return default values if API fails\nreturn this.getDefaultSettings();}}/**\n   * Get SEO meta settings\n   */async getSeoMeta(){const cacheKey='seo_meta_settings';const cached=this.getFromCache(cacheKey);if(cached){return cached;}try{const response=await api.get('/settings/seo-meta');const settings=response.data.data;this.setCache(cacheKey,settings);return settings;}catch(error){console.error('Failed to fetch SEO meta settings:',error);// Return default values if API fails\nreturn{title:'My Website',description:'Welcome to our website',keywords:'',site_name:'My Website',favicon:null};}}/**\n   * Get branding settings (site name and logo)\n   */async getBranding(){const cacheKey='branding_settings';const cached=this.getFromCache(cacheKey);if(cached){return cached;}try{const response=await api.get('/settings/branding');const settings=response.data.data;this.setCache(cacheKey,settings);return settings;}catch(error){console.error('Failed to fetch branding settings:',error);// Return default values if API fails\nreturn{site_name:'My Website',site_logo:null};}}/**\n   * Get a specific setting by key\n   */async getSetting(key){const cacheKey=`setting_${key}`;const cached=this.getFromCache(cacheKey);if(cached){return cached;}try{const response=await api.get(`/settings/${key}`);const value=response.data.data.value;this.setCache(cacheKey,value);return value;}catch(error){console.error(`Failed to fetch setting ${key}:`,error);return null;}}/**\n   * Clear all cached settings\n   */clearCache(){this.cache.clear();}/**\n   * Clear specific cached setting\n   */clearCachedSetting(key){this.cache.delete(key);}/**\n   * Get data from cache if not expired\n   */getFromCache(key){const cached=this.cache.get(key);if(!cached){return null;}const isExpired=Date.now()-cached.timestamp>this.CACHE_DURATION;if(isExpired){this.cache.delete(key);return null;}return cached.data;}/**\n   * Set data in cache with timestamp\n   */setCache(key,data){this.cache.set(key,{data,timestamp:Date.now()});}/**\n   * Get default settings when API fails\n   */getDefaultSettings(){return{site_name:'My Website',site_meta_title:'My Website - Welcome',site_meta_description:'Welcome to our website. We provide excellent services and products.',site_meta_keywords:'website, services, products, business',site_logo:null,site_favicon:null};}/**\n   * Preload settings for better performance\n   */async preloadSettings(){try{await Promise.all([this.getSettings(),this.getSeoMeta(),this.getBranding()]);}catch(error){console.error('Failed to preload settings:',error);}}/**\n   * Subscribe to settings changes (for real-time updates)\n   * This could be extended with WebSocket support in the future\n   */onSettingsChange(callback){// For now, we'll implement a simple polling mechanism\nconst interval=setInterval(async()=>{try{// Clear cache to force fresh fetch\nthis.clearCache();const settings=await this.getSettings();callback(settings);}catch(error){console.error('Failed to check for settings changes:',error);}},30000);// Check every 30 seconds\n// Return cleanup function\nreturn()=>clearInterval(interval);}}// Create and export singleton instance\nconst settingsService=new SettingsService();export default settingsService;", "map": {"version": 3, "names": ["api", "SettingsService", "constructor", "cache", "Map", "CACHE_DURATION", "getSettings", "cache<PERSON>ey", "cached", "getFromCache", "response", "get", "settings", "data", "setCache", "error", "console", "getDefaultSettings", "getSeoMeta", "title", "description", "keywords", "site_name", "favicon", "getBranding", "site_logo", "getSetting", "key", "value", "clearCache", "clear", "clearCachedSetting", "delete", "isExpired", "Date", "now", "timestamp", "set", "site_meta_title", "site_meta_description", "site_meta_keywords", "site_favicon", "preloadSettings", "Promise", "all", "onSettingsChange", "callback", "interval", "setInterval", "clearInterval", "settingsService"], "sources": ["C:/laragon/www/frontend/src/services/settingsService.ts"], "sourcesContent": ["import api from './api';\n\nexport interface GeneralSettings {\n  site_name: string;\n  site_meta_title: string;\n  site_meta_description: string;\n  site_meta_keywords: string;\n  site_logo: string | null;\n  site_favicon: string | null;\n}\n\nexport interface SeoMetaSettings {\n  title: string;\n  description: string;\n  keywords: string;\n  site_name: string;\n  favicon: string | null;\n}\n\nexport interface BrandingSettings {\n  site_name: string;\n  site_logo: string | null;\n}\n\nclass SettingsService {\n  private cache: Map<string, { data: any; timestamp: number }> = new Map();\n  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\n  /**\n   * Get all general settings\n   */\n  async getSettings(): Promise<GeneralSettings> {\n    const cacheKey = 'general_settings';\n    const cached = this.getFromCache(cacheKey);\n    \n    if (cached) {\n      return cached;\n    }\n\n    try {\n      const response = await api.get('/settings');\n      const settings = response.data.data;\n      \n      this.setCache(cacheKey, settings);\n      return settings;\n    } catch (error) {\n      console.error('Failed to fetch general settings:', error);\n      // Return default values if API fails\n      return this.getDefaultSettings();\n    }\n  }\n\n  /**\n   * Get SEO meta settings\n   */\n  async getSeoMeta(): Promise<SeoMetaSettings> {\n    const cacheKey = 'seo_meta_settings';\n    const cached = this.getFromCache(cacheKey);\n    \n    if (cached) {\n      return cached;\n    }\n\n    try {\n      const response = await api.get('/settings/seo-meta');\n      const settings = response.data.data;\n      \n      this.setCache(cacheKey, settings);\n      return settings;\n    } catch (error) {\n      console.error('Failed to fetch SEO meta settings:', error);\n      // Return default values if API fails\n      return {\n        title: 'My Website',\n        description: 'Welcome to our website',\n        keywords: '',\n        site_name: 'My Website',\n        favicon: null,\n      };\n    }\n  }\n\n  /**\n   * Get branding settings (site name and logo)\n   */\n  async getBranding(): Promise<BrandingSettings> {\n    const cacheKey = 'branding_settings';\n    const cached = this.getFromCache(cacheKey);\n    \n    if (cached) {\n      return cached;\n    }\n\n    try {\n      const response = await api.get('/settings/branding');\n      const settings = response.data.data;\n      \n      this.setCache(cacheKey, settings);\n      return settings;\n    } catch (error) {\n      console.error('Failed to fetch branding settings:', error);\n      // Return default values if API fails\n      return {\n        site_name: 'My Website',\n        site_logo: null,\n      };\n    }\n  }\n\n  /**\n   * Get a specific setting by key\n   */\n  async getSetting(key: string): Promise<any> {\n    const cacheKey = `setting_${key}`;\n    const cached = this.getFromCache(cacheKey);\n    \n    if (cached) {\n      return cached;\n    }\n\n    try {\n      const response = await api.get(`/settings/${key}`);\n      const value = response.data.data.value;\n      \n      this.setCache(cacheKey, value);\n      return value;\n    } catch (error) {\n      console.error(`Failed to fetch setting ${key}:`, error);\n      return null;\n    }\n  }\n\n  /**\n   * Clear all cached settings\n   */\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  /**\n   * Clear specific cached setting\n   */\n  clearCachedSetting(key: string): void {\n    this.cache.delete(key);\n  }\n\n  /**\n   * Get data from cache if not expired\n   */\n  private getFromCache(key: string): any | null {\n    const cached = this.cache.get(key);\n    \n    if (!cached) {\n      return null;\n    }\n\n    const isExpired = Date.now() - cached.timestamp > this.CACHE_DURATION;\n    \n    if (isExpired) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return cached.data;\n  }\n\n  /**\n   * Set data in cache with timestamp\n   */\n  private setCache(key: string, data: any): void {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n    });\n  }\n\n  /**\n   * Get default settings when API fails\n   */\n  private getDefaultSettings(): GeneralSettings {\n    return {\n      site_name: 'My Website',\n      site_meta_title: 'My Website - Welcome',\n      site_meta_description: 'Welcome to our website. We provide excellent services and products.',\n      site_meta_keywords: 'website, services, products, business',\n      site_logo: null,\n      site_favicon: null,\n    };\n  }\n\n  /**\n   * Preload settings for better performance\n   */\n  async preloadSettings(): Promise<void> {\n    try {\n      await Promise.all([\n        this.getSettings(),\n        this.getSeoMeta(),\n        this.getBranding(),\n      ]);\n    } catch (error) {\n      console.error('Failed to preload settings:', error);\n    }\n  }\n\n  /**\n   * Subscribe to settings changes (for real-time updates)\n   * This could be extended with WebSocket support in the future\n   */\n  onSettingsChange(callback: (settings: GeneralSettings) => void): () => void {\n    // For now, we'll implement a simple polling mechanism\n    const interval = setInterval(async () => {\n      try {\n        // Clear cache to force fresh fetch\n        this.clearCache();\n        const settings = await this.getSettings();\n        callback(settings);\n      } catch (error) {\n        console.error('Failed to check for settings changes:', error);\n      }\n    }, 30000); // Check every 30 seconds\n\n    // Return cleanup function\n    return () => clearInterval(interval);\n  }\n}\n\n// Create and export singleton instance\nconst settingsService = new SettingsService();\nexport default settingsService;\n"], "mappings": "AAAA,MAAO,CAAAA,GAAG,KAAM,OAAO,CAwBvB,KAAM,CAAAC,eAAgB,CAAAC,YAAA,OACZC,KAAK,CAAkD,GAAI,CAAAC,GAAG,CAAC,CAAC,MACvDC,cAAc,CAAG,CAAC,CAAG,EAAE,CAAG,IAAI,EAAE;AAEjD;AACF;AACA,KACE,KAAM,CAAAC,WAAWA,CAAA,CAA6B,CAC5C,KAAM,CAAAC,QAAQ,CAAG,kBAAkB,CACnC,KAAM,CAAAC,MAAM,CAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC,CAE1C,GAAIC,MAAM,CAAE,CACV,MAAO,CAAAA,MAAM,CACf,CAEA,GAAI,CACF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAV,GAAG,CAACW,GAAG,CAAC,WAAW,CAAC,CAC3C,KAAM,CAAAC,QAAQ,CAAGF,QAAQ,CAACG,IAAI,CAACA,IAAI,CAEnC,IAAI,CAACC,QAAQ,CAACP,QAAQ,CAAEK,QAAQ,CAAC,CACjC,MAAO,CAAAA,QAAQ,CACjB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD;AACA,MAAO,KAAI,CAACE,kBAAkB,CAAC,CAAC,CAClC,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAC,UAAUA,CAAA,CAA6B,CAC3C,KAAM,CAAAX,QAAQ,CAAG,mBAAmB,CACpC,KAAM,CAAAC,MAAM,CAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC,CAE1C,GAAIC,MAAM,CAAE,CACV,MAAO,CAAAA,MAAM,CACf,CAEA,GAAI,CACF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAV,GAAG,CAACW,GAAG,CAAC,oBAAoB,CAAC,CACpD,KAAM,CAAAC,QAAQ,CAAGF,QAAQ,CAACG,IAAI,CAACA,IAAI,CAEnC,IAAI,CAACC,QAAQ,CAACP,QAAQ,CAAEK,QAAQ,CAAC,CACjC,MAAO,CAAAA,QAAQ,CACjB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D;AACA,MAAO,CACLI,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,wBAAwB,CACrCC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,YAAY,CACvBC,OAAO,CAAE,IACX,CAAC,CACH,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAC,WAAWA,CAAA,CAA8B,CAC7C,KAAM,CAAAjB,QAAQ,CAAG,mBAAmB,CACpC,KAAM,CAAAC,MAAM,CAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC,CAE1C,GAAIC,MAAM,CAAE,CACV,MAAO,CAAAA,MAAM,CACf,CAEA,GAAI,CACF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAV,GAAG,CAACW,GAAG,CAAC,oBAAoB,CAAC,CACpD,KAAM,CAAAC,QAAQ,CAAGF,QAAQ,CAACG,IAAI,CAACA,IAAI,CAEnC,IAAI,CAACC,QAAQ,CAACP,QAAQ,CAAEK,QAAQ,CAAC,CACjC,MAAO,CAAAA,QAAQ,CACjB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D;AACA,MAAO,CACLO,SAAS,CAAE,YAAY,CACvBG,SAAS,CAAE,IACb,CAAC,CACH,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAC,UAAUA,CAACC,GAAW,CAAgB,CAC1C,KAAM,CAAApB,QAAQ,CAAG,WAAWoB,GAAG,EAAE,CACjC,KAAM,CAAAnB,MAAM,CAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC,CAE1C,GAAIC,MAAM,CAAE,CACV,MAAO,CAAAA,MAAM,CACf,CAEA,GAAI,CACF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAV,GAAG,CAACW,GAAG,CAAC,aAAagB,GAAG,EAAE,CAAC,CAClD,KAAM,CAAAC,KAAK,CAAGlB,QAAQ,CAACG,IAAI,CAACA,IAAI,CAACe,KAAK,CAEtC,IAAI,CAACd,QAAQ,CAACP,QAAQ,CAAEqB,KAAK,CAAC,CAC9B,MAAO,CAAAA,KAAK,CACd,CAAE,MAAOb,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2BY,GAAG,GAAG,CAAEZ,KAAK,CAAC,CACvD,MAAO,KAAI,CACb,CACF,CAEA;AACF;AACA,KACEc,UAAUA,CAAA,CAAS,CACjB,IAAI,CAAC1B,KAAK,CAAC2B,KAAK,CAAC,CAAC,CACpB,CAEA;AACF;AACA,KACEC,kBAAkBA,CAACJ,GAAW,CAAQ,CACpC,IAAI,CAACxB,KAAK,CAAC6B,MAAM,CAACL,GAAG,CAAC,CACxB,CAEA;AACF;AACA,KACUlB,YAAYA,CAACkB,GAAW,CAAc,CAC5C,KAAM,CAAAnB,MAAM,CAAG,IAAI,CAACL,KAAK,CAACQ,GAAG,CAACgB,GAAG,CAAC,CAElC,GAAI,CAACnB,MAAM,CAAE,CACX,MAAO,KAAI,CACb,CAEA,KAAM,CAAAyB,SAAS,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG3B,MAAM,CAAC4B,SAAS,CAAG,IAAI,CAAC/B,cAAc,CAErE,GAAI4B,SAAS,CAAE,CACb,IAAI,CAAC9B,KAAK,CAAC6B,MAAM,CAACL,GAAG,CAAC,CACtB,MAAO,KAAI,CACb,CAEA,MAAO,CAAAnB,MAAM,CAACK,IAAI,CACpB,CAEA;AACF;AACA,KACUC,QAAQA,CAACa,GAAW,CAAEd,IAAS,CAAQ,CAC7C,IAAI,CAACV,KAAK,CAACkC,GAAG,CAACV,GAAG,CAAE,CAClBd,IAAI,CACJuB,SAAS,CAAEF,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAAC,CACJ,CAEA;AACF;AACA,KACUlB,kBAAkBA,CAAA,CAAoB,CAC5C,MAAO,CACLK,SAAS,CAAE,YAAY,CACvBgB,eAAe,CAAE,sBAAsB,CACvCC,qBAAqB,CAAE,qEAAqE,CAC5FC,kBAAkB,CAAE,uCAAuC,CAC3Df,SAAS,CAAE,IAAI,CACfgB,YAAY,CAAE,IAChB,CAAC,CACH,CAEA;AACF;AACA,KACE,KAAM,CAAAC,eAAeA,CAAA,CAAkB,CACrC,GAAI,CACF,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAChB,IAAI,CAACtC,WAAW,CAAC,CAAC,CAClB,IAAI,CAACY,UAAU,CAAC,CAAC,CACjB,IAAI,CAACM,WAAW,CAAC,CAAC,CACnB,CAAC,CACJ,CAAE,MAAOT,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CACF,CAEA;AACF;AACA;AACA,KACE8B,gBAAgBA,CAACC,QAA6C,CAAc,CAC1E;AACA,KAAM,CAAAC,QAAQ,CAAGC,WAAW,CAAC,SAAY,CACvC,GAAI,CACF;AACA,IAAI,CAACnB,UAAU,CAAC,CAAC,CACjB,KAAM,CAAAjB,QAAQ,CAAG,KAAM,KAAI,CAACN,WAAW,CAAC,CAAC,CACzCwC,QAAQ,CAAClC,QAAQ,CAAC,CACpB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC/D,CACF,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX;AACA,MAAO,IAAMkC,aAAa,CAACF,QAAQ,CAAC,CACtC,CACF,CAEA;AACA,KAAM,CAAAG,eAAe,CAAG,GAAI,CAAAjD,eAAe,CAAC,CAAC,CAC7C,cAAe,CAAAiD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}