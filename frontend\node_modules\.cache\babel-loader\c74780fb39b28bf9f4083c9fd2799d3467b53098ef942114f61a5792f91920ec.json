{"ast": null, "code": "import { useLayoutEffect, useEffect, useRef, useMemo, useState, useCallback } from 'react';\nimport { computed, effect, untracked } from '@dnd-kit/state';\nimport { flushSync } from 'react-dom';\nimport { currentValue } from '@dnd-kit/react/utilities';\nfunction useConstant(initializer) {\n  const ref = useRef(null);\n  if (!ref.current) {\n    ref.current = initializer();\n  }\n  return ref.current;\n}\nvar canUseDOM = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\nvar useIsomorphicLayoutEffect = canUseDOM ? useLayoutEffect : useEffect;\nfunction useForceUpdate() {\n  const setState = useState(0)[1];\n  return useCallback(() => {\n    setState(value => value + 1);\n  }, [setState]);\n}\n\n// src/hooks/useSignal.ts\nfunction useSignal(signal) {\n  let sync = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  const previous = useRef(signal.peek());\n  const read = useRef(false);\n  const forceUpdate = useForceUpdate();\n  useIsomorphicLayoutEffect(() => effect(() => {\n    const previousValue = previous.current;\n    const currentValue2 = signal.value;\n    if (previousValue !== currentValue2) {\n      previous.current = currentValue2;\n      if (!read.current) return;\n      if (sync) {\n        flushSync(forceUpdate);\n      } else {\n        forceUpdate();\n      }\n    }\n  }), [signal, sync, forceUpdate]);\n  return {\n    get value() {\n      read.current = true;\n      return signal.peek();\n    }\n  };\n}\n\n// src/hooks/useComputed.ts\nfunction useComputed(compute) {\n  let dependencies = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  let sync = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  const $compute = useRef(compute);\n  $compute.current = compute;\n  return useSignal(useMemo(() => computed(() => $compute.current()), dependencies), sync);\n}\nfunction useDeepSignal(target, synchronous) {\n  const tracked = useRef(/* @__PURE__ */new Map());\n  const forceUpdate = useForceUpdate();\n  useIsomorphicLayoutEffect(() => {\n    if (!target) {\n      tracked.current.clear();\n      return;\n    }\n    return effect(() => {\n      var _a;\n      let stale = false;\n      let sync = false;\n      for (const entry of tracked.current) {\n        const [key] = entry;\n        const value = untracked(() => entry[1]);\n        const latestValue = target[key];\n        if (value !== latestValue) {\n          stale = true;\n          tracked.current.set(key, latestValue);\n          sync = (_a = synchronous == null ? void 0 : synchronous(key, value, latestValue)) != null ? _a : false;\n        }\n      }\n      if (stale) {\n        sync ? flushSync(forceUpdate) : forceUpdate();\n      }\n    });\n  }, [target]);\n  return useMemo(() => target ? new Proxy(target, {\n    get(target2, key) {\n      const value = target2[key];\n      tracked.current.set(key, value);\n      return value;\n    }\n  }) : target, [target]);\n}\n\n// src/hooks/useImmediateEffect.ts\nfunction useImmediateEffect(callback, _) {\n  callback();\n}\nfunction useLatest(value) {\n  const valueRef = useRef(value);\n  useIsomorphicLayoutEffect(() => {\n    valueRef.current = value;\n  }, [value]);\n  return valueRef;\n}\nfunction useOnValueChange(value, onChange) {\n  let effect3 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : useEffect;\n  let compare = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Object.is;\n  const tracked = useRef(value);\n  effect3(() => {\n    const oldValue = tracked.current;\n    if (!compare(value, oldValue)) {\n      tracked.current = value;\n      onChange(value, oldValue);\n    }\n  }, [onChange, value]);\n}\nfunction useOnElementChange(value, onChange) {\n  const previous = useRef(currentValue(value));\n  useIsomorphicLayoutEffect(() => {\n    const current = currentValue(value);\n    if (current !== previous.current) {\n      previous.current = current;\n      onChange(current);\n    }\n  });\n}\nexport { useComputed, useConstant, useDeepSignal, useImmediateEffect, useIsomorphicLayoutEffect, useLatest, useOnElementChange, useOnValueChange };", "map": {"version": 3, "names": ["useLayoutEffect", "useEffect", "useRef", "useMemo", "useState", "useCallback", "computed", "effect", "untracked", "flushSync", "currentValue", "useConstant", "initializer", "ref", "current", "canUseDOM", "window", "document", "createElement", "useIsomorphicLayoutEffect", "useForceUpdate", "setState", "value", "useSignal", "signal", "sync", "arguments", "length", "undefined", "previous", "peek", "read", "forceUpdate", "previousValue", "currentValue2", "useComputed", "compute", "dependencies", "$compute", "useDeepSignal", "target", "synchronous", "tracked", "Map", "clear", "_a", "stale", "entry", "key", "latestValue", "set", "Proxy", "get", "target2", "useImmediateEffect", "callback", "_", "useLatest", "valueRef", "useOnValueChange", "onChange", "effect3", "compare", "Object", "is", "oldValue", "useOnElementChange"], "sources": ["C:/laragon/www/frontend/node_modules/@dnd-kit/react/hooks.js"], "sourcesContent": ["import { useLayoutEffect, useEffect, useRef, useMemo, useState, useCallback } from 'react';\nimport { computed, effect, untracked } from '@dnd-kit/state';\nimport { flushSync } from 'react-dom';\nimport { currentValue } from '@dnd-kit/react/utilities';\n\nfunction useConstant(initializer) {\n  const ref = useRef(null);\n  if (!ref.current) {\n    ref.current = initializer();\n  }\n  return ref.current;\n}\nvar canUseDOM = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\nvar useIsomorphicLayoutEffect = canUseDOM ? useLayoutEffect : useEffect;\nfunction useForceUpdate() {\n  const setState = useState(0)[1];\n  return useCallback(() => {\n    setState((value) => value + 1);\n  }, [setState]);\n}\n\n// src/hooks/useSignal.ts\nfunction useSignal(signal, sync = false) {\n  const previous = useRef(signal.peek());\n  const read = useRef(false);\n  const forceUpdate = useForceUpdate();\n  useIsomorphicLayoutEffect(\n    () => effect(() => {\n      const previousValue = previous.current;\n      const currentValue2 = signal.value;\n      if (previousValue !== currentValue2) {\n        previous.current = currentValue2;\n        if (!read.current) return;\n        if (sync) {\n          flushSync(forceUpdate);\n        } else {\n          forceUpdate();\n        }\n      }\n    }),\n    [signal, sync, forceUpdate]\n  );\n  return {\n    get value() {\n      read.current = true;\n      return signal.peek();\n    }\n  };\n}\n\n// src/hooks/useComputed.ts\nfunction useComputed(compute, dependencies = [], sync = false) {\n  const $compute = useRef(compute);\n  $compute.current = compute;\n  return useSignal(\n    useMemo(() => computed(() => $compute.current()), dependencies),\n    sync\n  );\n}\nfunction useDeepSignal(target, synchronous) {\n  const tracked = useRef(/* @__PURE__ */ new Map());\n  const forceUpdate = useForceUpdate();\n  useIsomorphicLayoutEffect(() => {\n    if (!target) {\n      tracked.current.clear();\n      return;\n    }\n    return effect(() => {\n      var _a;\n      let stale = false;\n      let sync = false;\n      for (const entry of tracked.current) {\n        const [key] = entry;\n        const value = untracked(() => entry[1]);\n        const latestValue = target[key];\n        if (value !== latestValue) {\n          stale = true;\n          tracked.current.set(key, latestValue);\n          sync = (_a = synchronous == null ? void 0 : synchronous(key, value, latestValue)) != null ? _a : false;\n        }\n      }\n      if (stale) {\n        sync ? flushSync(forceUpdate) : forceUpdate();\n      }\n    });\n  }, [target]);\n  return useMemo(\n    () => target ? new Proxy(target, {\n      get(target2, key) {\n        const value = target2[key];\n        tracked.current.set(key, value);\n        return value;\n      }\n    }) : target,\n    [target]\n  );\n}\n\n// src/hooks/useImmediateEffect.ts\nfunction useImmediateEffect(callback, _) {\n  callback();\n}\nfunction useLatest(value) {\n  const valueRef = useRef(value);\n  useIsomorphicLayoutEffect(() => {\n    valueRef.current = value;\n  }, [value]);\n  return valueRef;\n}\nfunction useOnValueChange(value, onChange, effect3 = useEffect, compare = Object.is) {\n  const tracked = useRef(value);\n  effect3(() => {\n    const oldValue = tracked.current;\n    if (!compare(value, oldValue)) {\n      tracked.current = value;\n      onChange(value, oldValue);\n    }\n  }, [onChange, value]);\n}\nfunction useOnElementChange(value, onChange) {\n  const previous = useRef(currentValue(value));\n  useIsomorphicLayoutEffect(() => {\n    const current = currentValue(value);\n    if (current !== previous.current) {\n      previous.current = current;\n      onChange(current);\n    }\n  });\n}\n\nexport { useComputed, useConstant, useDeepSignal, useImmediateEffect, useIsomorphicLayoutEffect, useLatest, useOnElementChange, useOnValueChange };\n//# sourceMappingURL=hooks.js.map\n//# sourceMappingURL=hooks.js.map"], "mappings": "AAAA,SAASA,eAAe,EAAEC,SAAS,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC1F,SAASC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAC5D,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,YAAY,QAAQ,0BAA0B;AAEvD,SAASC,WAAWA,CAACC,WAAW,EAAE;EAChC,MAAMC,GAAG,GAAGX,MAAM,CAAC,IAAI,CAAC;EACxB,IAAI,CAACW,GAAG,CAACC,OAAO,EAAE;IAChBD,GAAG,CAACC,OAAO,GAAGF,WAAW,CAAC,CAAC;EAC7B;EACA,OAAOC,GAAG,CAACC,OAAO;AACpB;AACA,IAAIC,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,WAAW,IAAI,OAAOD,MAAM,CAACC,QAAQ,CAACC,aAAa,KAAK,WAAW;AAC/I,IAAIC,yBAAyB,GAAGJ,SAAS,GAAGf,eAAe,GAAGC,SAAS;AACvE,SAASmB,cAAcA,CAAA,EAAG;EACxB,MAAMC,QAAQ,GAAGjB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,OAAOC,WAAW,CAAC,MAAM;IACvBgB,QAAQ,CAAEC,KAAK,IAAKA,KAAK,GAAG,CAAC,CAAC;EAChC,CAAC,EAAE,CAACD,QAAQ,CAAC,CAAC;AAChB;;AAEA;AACA,SAASE,SAASA,CAACC,MAAM,EAAgB;EAAA,IAAdC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACrC,MAAMG,QAAQ,GAAG3B,MAAM,CAACsB,MAAM,CAACM,IAAI,CAAC,CAAC,CAAC;EACtC,MAAMC,IAAI,GAAG7B,MAAM,CAAC,KAAK,CAAC;EAC1B,MAAM8B,WAAW,GAAGZ,cAAc,CAAC,CAAC;EACpCD,yBAAyB,CACvB,MAAMZ,MAAM,CAAC,MAAM;IACjB,MAAM0B,aAAa,GAAGJ,QAAQ,CAACf,OAAO;IACtC,MAAMoB,aAAa,GAAGV,MAAM,CAACF,KAAK;IAClC,IAAIW,aAAa,KAAKC,aAAa,EAAE;MACnCL,QAAQ,CAACf,OAAO,GAAGoB,aAAa;MAChC,IAAI,CAACH,IAAI,CAACjB,OAAO,EAAE;MACnB,IAAIW,IAAI,EAAE;QACRhB,SAAS,CAACuB,WAAW,CAAC;MACxB,CAAC,MAAM;QACLA,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC,CAAC,EACF,CAACR,MAAM,EAAEC,IAAI,EAAEO,WAAW,CAC5B,CAAC;EACD,OAAO;IACL,IAAIV,KAAKA,CAAA,EAAG;MACVS,IAAI,CAACjB,OAAO,GAAG,IAAI;MACnB,OAAOU,MAAM,CAACM,IAAI,CAAC,CAAC;IACtB;EACF,CAAC;AACH;;AAEA;AACA,SAASK,WAAWA,CAACC,OAAO,EAAmC;EAAA,IAAjCC,YAAY,GAAAX,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAAED,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC3D,MAAMY,QAAQ,GAAGpC,MAAM,CAACkC,OAAO,CAAC;EAChCE,QAAQ,CAACxB,OAAO,GAAGsB,OAAO;EAC1B,OAAOb,SAAS,CACdpB,OAAO,CAAC,MAAMG,QAAQ,CAAC,MAAMgC,QAAQ,CAACxB,OAAO,CAAC,CAAC,CAAC,EAAEuB,YAAY,CAAC,EAC/DZ,IACF,CAAC;AACH;AACA,SAASc,aAAaA,CAACC,MAAM,EAAEC,WAAW,EAAE;EAC1C,MAAMC,OAAO,GAAGxC,MAAM,CAAC,eAAgB,IAAIyC,GAAG,CAAC,CAAC,CAAC;EACjD,MAAMX,WAAW,GAAGZ,cAAc,CAAC,CAAC;EACpCD,yBAAyB,CAAC,MAAM;IAC9B,IAAI,CAACqB,MAAM,EAAE;MACXE,OAAO,CAAC5B,OAAO,CAAC8B,KAAK,CAAC,CAAC;MACvB;IACF;IACA,OAAOrC,MAAM,CAAC,MAAM;MAClB,IAAIsC,EAAE;MACN,IAAIC,KAAK,GAAG,KAAK;MACjB,IAAIrB,IAAI,GAAG,KAAK;MAChB,KAAK,MAAMsB,KAAK,IAAIL,OAAO,CAAC5B,OAAO,EAAE;QACnC,MAAM,CAACkC,GAAG,CAAC,GAAGD,KAAK;QACnB,MAAMzB,KAAK,GAAGd,SAAS,CAAC,MAAMuC,KAAK,CAAC,CAAC,CAAC,CAAC;QACvC,MAAME,WAAW,GAAGT,MAAM,CAACQ,GAAG,CAAC;QAC/B,IAAI1B,KAAK,KAAK2B,WAAW,EAAE;UACzBH,KAAK,GAAG,IAAI;UACZJ,OAAO,CAAC5B,OAAO,CAACoC,GAAG,CAACF,GAAG,EAAEC,WAAW,CAAC;UACrCxB,IAAI,GAAG,CAACoB,EAAE,GAAGJ,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACO,GAAG,EAAE1B,KAAK,EAAE2B,WAAW,CAAC,KAAK,IAAI,GAAGJ,EAAE,GAAG,KAAK;QACxG;MACF;MACA,IAAIC,KAAK,EAAE;QACTrB,IAAI,GAAGhB,SAAS,CAACuB,WAAW,CAAC,GAAGA,WAAW,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACQ,MAAM,CAAC,CAAC;EACZ,OAAOrC,OAAO,CACZ,MAAMqC,MAAM,GAAG,IAAIW,KAAK,CAACX,MAAM,EAAE;IAC/BY,GAAGA,CAACC,OAAO,EAAEL,GAAG,EAAE;MAChB,MAAM1B,KAAK,GAAG+B,OAAO,CAACL,GAAG,CAAC;MAC1BN,OAAO,CAAC5B,OAAO,CAACoC,GAAG,CAACF,GAAG,EAAE1B,KAAK,CAAC;MAC/B,OAAOA,KAAK;IACd;EACF,CAAC,CAAC,GAAGkB,MAAM,EACX,CAACA,MAAM,CACT,CAAC;AACH;;AAEA;AACA,SAASc,kBAAkBA,CAACC,QAAQ,EAAEC,CAAC,EAAE;EACvCD,QAAQ,CAAC,CAAC;AACZ;AACA,SAASE,SAASA,CAACnC,KAAK,EAAE;EACxB,MAAMoC,QAAQ,GAAGxD,MAAM,CAACoB,KAAK,CAAC;EAC9BH,yBAAyB,CAAC,MAAM;IAC9BuC,QAAQ,CAAC5C,OAAO,GAAGQ,KAAK;EAC1B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,OAAOoC,QAAQ;AACjB;AACA,SAASC,gBAAgBA,CAACrC,KAAK,EAAEsC,QAAQ,EAA4C;EAAA,IAA1CC,OAAO,GAAAnC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGzB,SAAS;EAAA,IAAE6D,OAAO,GAAApC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGqC,MAAM,CAACC,EAAE;EACjF,MAAMtB,OAAO,GAAGxC,MAAM,CAACoB,KAAK,CAAC;EAC7BuC,OAAO,CAAC,MAAM;IACZ,MAAMI,QAAQ,GAAGvB,OAAO,CAAC5B,OAAO;IAChC,IAAI,CAACgD,OAAO,CAACxC,KAAK,EAAE2C,QAAQ,CAAC,EAAE;MAC7BvB,OAAO,CAAC5B,OAAO,GAAGQ,KAAK;MACvBsC,QAAQ,CAACtC,KAAK,EAAE2C,QAAQ,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEtC,KAAK,CAAC,CAAC;AACvB;AACA,SAAS4C,kBAAkBA,CAAC5C,KAAK,EAAEsC,QAAQ,EAAE;EAC3C,MAAM/B,QAAQ,GAAG3B,MAAM,CAACQ,YAAY,CAACY,KAAK,CAAC,CAAC;EAC5CH,yBAAyB,CAAC,MAAM;IAC9B,MAAML,OAAO,GAAGJ,YAAY,CAACY,KAAK,CAAC;IACnC,IAAIR,OAAO,KAAKe,QAAQ,CAACf,OAAO,EAAE;MAChCe,QAAQ,CAACf,OAAO,GAAGA,OAAO;MAC1B8C,QAAQ,CAAC9C,OAAO,CAAC;IACnB;EACF,CAAC,CAAC;AACJ;AAEA,SAASqB,WAAW,EAAExB,WAAW,EAAE4B,aAAa,EAAEe,kBAAkB,EAAEnC,yBAAyB,EAAEsC,SAAS,EAAES,kBAAkB,EAAEP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}