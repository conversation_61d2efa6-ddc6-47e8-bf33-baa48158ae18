{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport useControlled from '@mui/utils/useControlled';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport clamp from '@mui/utils/clamp';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport areArraysEqual from \"../utils/areArraysEqual.js\";\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction getNewValue(currentValue, step, direction, min, max) {\n  return direction === 1 ? Math.min(currentValue + step, max) : Math.max(currentValue - step, min);\n}\nfunction asc(a, b) {\n  return a - b;\n}\nfunction findClosest(values, currentValue) {\n  const {\n    index: closestIndex\n  } = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null) ?? {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex(_ref) {\n  let {\n    values,\n    newValue,\n    index\n  } = _ref;\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb(_ref2) {\n  let {\n    sliderRef,\n    activeIndex,\n    setActive\n  } = _ref2;\n  const doc = ownerDocument(sliderRef.current);\n  if (!sliderRef.current?.contains(doc.activeElement) || Number(doc?.activeElement?.getAttribute('data-index')) !== activeIndex) {\n    sliderRef.current?.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    shiftStep = 10,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(undefined);\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  // lastChangedValue is updated whenever onChange is triggered.\n  const lastChangedValue = React.useRef(null);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue ?? min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    lastChangedValue.current = value;\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => value == null ? min : clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef(null);\n  const handleRef = useForkRef(ref, sliderRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    if (isFocusVisible(event.target)) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers?.onFocus?.(event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    if (!isFocusVisible(event.target)) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers?.onBlur?.(event);\n  };\n  const changeValue = (event, valueInput) => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n    let newValue = valueInput;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue >= maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue <= marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, lastChangedValue.current ?? newValue);\n    }\n  };\n  const createHandleHiddenInputKeyDown = otherHandlers => event => {\n    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'PageUp', 'PageDown', 'Home', 'End'].includes(event.key)) {\n      event.preventDefault();\n      const index = Number(event.currentTarget.getAttribute('data-index'));\n      const value = values[index];\n      let newValue = null;\n      // Keys actions that change the value by more than the most granular `step`\n      // value are only applied if the step not `null`.\n      // When step is `null`, the `marks` prop is used instead to define valid values.\n      if (step != null) {\n        const stepSize = event.shiftKey ? shiftStep : step;\n        switch (event.key) {\n          case 'ArrowUp':\n            newValue = getNewValue(value, stepSize, 1, min, max);\n            break;\n          case 'ArrowRight':\n            newValue = getNewValue(value, stepSize, isRtl ? -1 : 1, min, max);\n            break;\n          case 'ArrowDown':\n            newValue = getNewValue(value, stepSize, -1, min, max);\n            break;\n          case 'ArrowLeft':\n            newValue = getNewValue(value, stepSize, isRtl ? 1 : -1, min, max);\n            break;\n          case 'PageUp':\n            newValue = getNewValue(value, shiftStep, 1, min, max);\n            break;\n          case 'PageDown':\n            newValue = getNewValue(value, shiftStep, -1, min, max);\n            break;\n          case 'Home':\n            newValue = min;\n            break;\n          case 'End':\n            newValue = max;\n            break;\n          default:\n            break;\n        }\n      } else if (marks) {\n        const maxMarksValue = marksValues[marksValues.length - 1];\n        const currentMarkIndex = marksValues.indexOf(value);\n        const decrementKeys = [isRtl ? 'ArrowRight' : 'ArrowLeft', 'ArrowDown', 'PageDown', 'Home'];\n        const incrementKeys = [isRtl ? 'ArrowLeft' : 'ArrowRight', 'ArrowUp', 'PageUp', 'End'];\n        if (decrementKeys.includes(event.key)) {\n          if (currentMarkIndex === 0) {\n            newValue = marksValues[0];\n          } else {\n            newValue = marksValues[currentMarkIndex - 1];\n          }\n        } else if (incrementKeys.includes(event.key)) {\n          if (currentMarkIndex === marksValues.length - 1) {\n            newValue = maxMarksValue;\n          } else {\n            newValue = marksValues[currentMarkIndex + 1];\n          }\n        }\n      }\n      if (newValue != null) {\n        changeValue(event, newValue);\n      }\n    }\n    otherHandlers?.onKeyDown?.(event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      document.activeElement?.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    otherHandlers.onChange?.(event);\n    // this handles value change by Pointer or Touch events\n    // @ts-ignore\n    changeValue(event, event.target.valueAsNumber);\n  };\n  const previousIndex = React.useRef(undefined);\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = _ref3 => {\n    let {\n      finger,\n      move = false\n    } = _ref3;\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.startsWith('vertical')) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.includes('-reverse')) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, lastChangedValue.current ?? newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      slider.removeEventListener('touchstart', handleTouchStart);\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    otherHandlers.onMouseDown?.(event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      ...externalProps,\n      ref: handleRef,\n      ...mergedEventHandlers\n    };\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    otherHandlers.onMouseOver?.(event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    otherHandlers.onMouseLeave?.(event);\n    setOpen(-1);\n  };\n  const getThumbProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return {\n      ...externalProps,\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  let cssWritingMode;\n  if (orientation === 'vertical') {\n    cssWritingMode = isRtl ? 'vertical-rl' : 'vertical-lr';\n  }\n  const getHiddenInputProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {}),\n      onKeyDown: createHandleHiddenInputKeyDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : parameters.step ?? undefined,\n      disabled,\n      ...externalProps,\n      ...mergedEventHandlers,\n      style: {\n        ...visuallyHidden,\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%',\n        writingMode: cssWritingMode\n      }\n    };\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}", "map": {"version": 3, "names": ["React", "ownerDocument", "useControlled", "useEnhancedEffect", "useEventCallback", "useForkRef", "isFocusVisible", "visuallyHidden", "clamp", "extractEventHandlers", "areArraysEqual", "INTENTIONAL_DRAG_COUNT_THRESHOLD", "getNewValue", "currentValue", "step", "direction", "min", "max", "Math", "asc", "a", "b", "findClosest", "values", "index", "closestIndex", "reduce", "acc", "value", "distance", "abs", "trackFinger", "event", "touchId", "current", "undefined", "changedTouches", "touchEvent", "i", "length", "touch", "identifier", "x", "clientX", "y", "clientY", "valueToPercent", "percentToValue", "percent", "getDecimalPrecision", "num", "parts", "toExponential", "split", "mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseInt", "decimalPart", "toString", "roundValueToStep", "nearest", "round", "Number", "toFixed", "setValueIndex", "_ref", "newValue", "output", "slice", "sort", "focusThumb", "_ref2", "sliderRef", "activeIndex", "setActive", "doc", "contains", "activeElement", "getAttribute", "querySelector", "focus", "areValuesEqual", "oldValue", "axisProps", "horizontal", "offset", "left", "leap", "width", "right", "vertical", "bottom", "height", "Identity", "cachedSupportsTouchActionNone", "doesSupportTouchActionNone", "CSS", "supports", "useSlider", "parameters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultValue", "disabled", "disableSwap", "isRtl", "marks", "marksProp", "name", "onChange", "onChangeCommitted", "orientation", "rootRef", "ref", "scale", "shiftStep", "tabIndex", "valueProp", "useRef", "active", "useState", "open", "<PERSON><PERSON><PERSON>", "dragging", "setDragging", "moveCount", "lastChangedValue", "valueDerived", "setValueState", "controlled", "default", "handleChange", "thumbIndex", "nativeEvent", "clonedEvent", "constructor", "type", "Object", "defineProperty", "writable", "range", "Array", "isArray", "map", "floor", "_", "marksV<PERSON>ues", "mark", "focusedThumbIndex", "setFocusedThumbIndex", "handleRef", "createHandleHiddenInputFocus", "otherHandlers", "currentTarget", "target", "onFocus", "createHandleHiddenInputBlur", "onBlur", "changeValue", "valueInput", "marksIndex", "indexOf", "maxMarksValue", "Infinity", "previousValue", "createHandleHiddenInputKeyDown", "includes", "key", "preventDefault", "stepSize", "shift<PERSON>ey", "currentMarkIndex", "decrementKeys", "incrementKeys", "onKeyDown", "document", "blur", "createHandleHiddenInputChange", "valueAsNumber", "previousIndex", "axis", "getFingerNewValue", "_ref3", "finger", "move", "slider", "getBoundingClientRect", "startsWith", "handleTouchMove", "buttons", "handleTouchEnd", "stopListening", "handleTouchStart", "addEventListener", "passive", "useCallback", "removeEventListener", "useEffect", "createHandleMouseDown", "onMouseDown", "defaultPrevented", "button", "trackOffset", "trackLeap", "getRootProps", "externalProps", "arguments", "externalHandlers", "ownEventHandlers", "mergedEventHandlers", "createHandleMouseOver", "onMouseOver", "createHandleMouseLeave", "onMouseLeave", "getThumbProps", "getThumbStyle", "pointerEvents", "cssWritingMode", "getHiddenInputProps", "style", "writingMode"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Slider/useSlider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport useControlled from '@mui/utils/useControlled';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport clamp from '@mui/utils/clamp';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport areArraysEqual from \"../utils/areArraysEqual.js\";\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction getNewValue(currentValue, step, direction, min, max) {\n  return direction === 1 ? Math.min(currentValue + step, max) : Math.max(currentValue - step, min);\n}\nfunction asc(a, b) {\n  return a - b;\n}\nfunction findClosest(values, currentValue) {\n  const {\n    index: closestIndex\n  } = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null) ?? {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  const doc = ownerDocument(sliderRef.current);\n  if (!sliderRef.current?.contains(doc.activeElement) || Number(doc?.activeElement?.getAttribute('data-index')) !== activeIndex) {\n    sliderRef.current?.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    shiftStep = 10,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(undefined);\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  // lastChangedValue is updated whenever onChange is triggered.\n  const lastChangedValue = React.useRef(null);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue ?? min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    lastChangedValue.current = value;\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => value == null ? min : clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef(null);\n  const handleRef = useForkRef(ref, sliderRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    if (isFocusVisible(event.target)) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers?.onFocus?.(event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    if (!isFocusVisible(event.target)) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers?.onBlur?.(event);\n  };\n  const changeValue = (event, valueInput) => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n    let newValue = valueInput;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue >= maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue <= marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, lastChangedValue.current ?? newValue);\n    }\n  };\n  const createHandleHiddenInputKeyDown = otherHandlers => event => {\n    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'PageUp', 'PageDown', 'Home', 'End'].includes(event.key)) {\n      event.preventDefault();\n      const index = Number(event.currentTarget.getAttribute('data-index'));\n      const value = values[index];\n      let newValue = null;\n      // Keys actions that change the value by more than the most granular `step`\n      // value are only applied if the step not `null`.\n      // When step is `null`, the `marks` prop is used instead to define valid values.\n      if (step != null) {\n        const stepSize = event.shiftKey ? shiftStep : step;\n        switch (event.key) {\n          case 'ArrowUp':\n            newValue = getNewValue(value, stepSize, 1, min, max);\n            break;\n          case 'ArrowRight':\n            newValue = getNewValue(value, stepSize, isRtl ? -1 : 1, min, max);\n            break;\n          case 'ArrowDown':\n            newValue = getNewValue(value, stepSize, -1, min, max);\n            break;\n          case 'ArrowLeft':\n            newValue = getNewValue(value, stepSize, isRtl ? 1 : -1, min, max);\n            break;\n          case 'PageUp':\n            newValue = getNewValue(value, shiftStep, 1, min, max);\n            break;\n          case 'PageDown':\n            newValue = getNewValue(value, shiftStep, -1, min, max);\n            break;\n          case 'Home':\n            newValue = min;\n            break;\n          case 'End':\n            newValue = max;\n            break;\n          default:\n            break;\n        }\n      } else if (marks) {\n        const maxMarksValue = marksValues[marksValues.length - 1];\n        const currentMarkIndex = marksValues.indexOf(value);\n        const decrementKeys = [isRtl ? 'ArrowRight' : 'ArrowLeft', 'ArrowDown', 'PageDown', 'Home'];\n        const incrementKeys = [isRtl ? 'ArrowLeft' : 'ArrowRight', 'ArrowUp', 'PageUp', 'End'];\n        if (decrementKeys.includes(event.key)) {\n          if (currentMarkIndex === 0) {\n            newValue = marksValues[0];\n          } else {\n            newValue = marksValues[currentMarkIndex - 1];\n          }\n        } else if (incrementKeys.includes(event.key)) {\n          if (currentMarkIndex === marksValues.length - 1) {\n            newValue = maxMarksValue;\n          } else {\n            newValue = marksValues[currentMarkIndex + 1];\n          }\n        }\n      }\n      if (newValue != null) {\n        changeValue(event, newValue);\n      }\n    }\n    otherHandlers?.onKeyDown?.(event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      document.activeElement?.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    otherHandlers.onChange?.(event);\n    // this handles value change by Pointer or Touch events\n    // @ts-ignore\n    changeValue(event, event.target.valueAsNumber);\n  };\n  const previousIndex = React.useRef(undefined);\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.startsWith('vertical')) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.includes('-reverse')) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, lastChangedValue.current ?? newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      slider.removeEventListener('touchstart', handleTouchStart);\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    otherHandlers.onMouseDown?.(event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      ...externalProps,\n      ref: handleRef,\n      ...mergedEventHandlers\n    };\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    otherHandlers.onMouseOver?.(event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    otherHandlers.onMouseLeave?.(event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return {\n      ...externalProps,\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  let cssWritingMode;\n  if (orientation === 'vertical') {\n    cssWritingMode = isRtl ? 'vertical-rl' : 'vertical-lr';\n  }\n  const getHiddenInputProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {}),\n      onKeyDown: createHandleHiddenInputKeyDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : parameters.step ?? undefined,\n      disabled,\n      ...externalProps,\n      ...mergedEventHandlers,\n      style: {\n        ...visuallyHidden,\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%',\n        writingMode: cssWritingMode\n      }\n    };\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,cAAc,MAAM,4BAA4B;AACvD,MAAMC,gCAAgC,GAAG,CAAC;AAC1C,SAASC,WAAWA,CAACC,YAAY,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC5D,OAAOF,SAAS,KAAK,CAAC,GAAGG,IAAI,CAACF,GAAG,CAACH,YAAY,GAAGC,IAAI,EAAEG,GAAG,CAAC,GAAGC,IAAI,CAACD,GAAG,CAACJ,YAAY,GAAGC,IAAI,EAAEE,GAAG,CAAC;AAClG;AACA,SAASG,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAOD,CAAC,GAAGC,CAAC;AACd;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEV,YAAY,EAAE;EACzC,MAAM;IACJW,KAAK,EAAEC;EACT,CAAC,GAAGF,MAAM,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,EAAEJ,KAAK,KAAK;IACvC,MAAMK,QAAQ,GAAGX,IAAI,CAACY,GAAG,CAACjB,YAAY,GAAGe,KAAK,CAAC;IAC/C,IAAID,GAAG,KAAK,IAAI,IAAIE,QAAQ,GAAGF,GAAG,CAACE,QAAQ,IAAIA,QAAQ,KAAKF,GAAG,CAACE,QAAQ,EAAE;MACxE,OAAO;QACLA,QAAQ;QACRL;MACF,CAAC;IACH;IACA,OAAOG,GAAG;EACZ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;EACd,OAAOF,YAAY;AACrB;AACA,SAASM,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACnC;EACA,IAAIA,OAAO,CAACC,OAAO,KAAKC,SAAS,IAAIH,KAAK,CAACI,cAAc,EAAE;IACzD,MAAMC,UAAU,GAAGL,KAAK;IACxB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACD,cAAc,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC5D,MAAME,KAAK,GAAGH,UAAU,CAACD,cAAc,CAACE,CAAC,CAAC;MAC1C,IAAIE,KAAK,CAACC,UAAU,KAAKR,OAAO,CAACC,OAAO,EAAE;QACxC,OAAO;UACLQ,CAAC,EAAEF,KAAK,CAACG,OAAO;UAChBC,CAAC,EAAEJ,KAAK,CAACK;QACX,CAAC;MACH;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACA,OAAO;IACLH,CAAC,EAAEV,KAAK,CAACW,OAAO;IAChBC,CAAC,EAAEZ,KAAK,CAACa;EACX,CAAC;AACH;AACA,OAAO,SAASC,cAAcA,CAAClB,KAAK,EAAEZ,GAAG,EAAEC,GAAG,EAAE;EAC9C,OAAO,CAACW,KAAK,GAAGZ,GAAG,IAAI,GAAG,IAAIC,GAAG,GAAGD,GAAG,CAAC;AAC1C;AACA,SAAS+B,cAAcA,CAACC,OAAO,EAAEhC,GAAG,EAAEC,GAAG,EAAE;EACzC,OAAO,CAACA,GAAG,GAAGD,GAAG,IAAIgC,OAAO,GAAGhC,GAAG;AACpC;AACA,SAASiC,mBAAmBA,CAACC,GAAG,EAAE;EAChC;EACA;EACA,IAAIhC,IAAI,CAACY,GAAG,CAACoB,GAAG,CAAC,GAAG,CAAC,EAAE;IACrB,MAAMC,KAAK,GAAGD,GAAG,CAACE,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,kBAAkB,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,CAACC,kBAAkB,GAAGA,kBAAkB,CAACf,MAAM,GAAG,CAAC,IAAIgB,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtF;EACA,MAAMK,WAAW,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOG,WAAW,GAAGA,WAAW,CAACjB,MAAM,GAAG,CAAC;AAC7C;AACA,SAASmB,gBAAgBA,CAAC9B,KAAK,EAAEd,IAAI,EAAEE,GAAG,EAAE;EAC1C,MAAM2C,OAAO,GAAGzC,IAAI,CAAC0C,KAAK,CAAC,CAAChC,KAAK,GAAGZ,GAAG,IAAIF,IAAI,CAAC,GAAGA,IAAI,GAAGE,GAAG;EAC7D,OAAO6C,MAAM,CAACF,OAAO,CAACG,OAAO,CAACb,mBAAmB,CAACnC,IAAI,CAAC,CAAC,CAAC;AAC3D;AACA,SAASiD,aAAaA,CAAAC,IAAA,EAInB;EAAA,IAJoB;IACrBzC,MAAM;IACN0C,QAAQ;IACRzC;EACF,CAAC,GAAAwC,IAAA;EACC,MAAME,MAAM,GAAG3C,MAAM,CAAC4C,KAAK,CAAC,CAAC;EAC7BD,MAAM,CAAC1C,KAAK,CAAC,GAAGyC,QAAQ;EACxB,OAAOC,MAAM,CAACE,IAAI,CAACjD,GAAG,CAAC;AACzB;AACA,SAASkD,UAAUA,CAAAC,KAAA,EAIhB;EAAA,IAJiB;IAClBC,SAAS;IACTC,WAAW;IACXC;EACF,CAAC,GAAAH,KAAA;EACC,MAAMI,GAAG,GAAGzE,aAAa,CAACsE,SAAS,CAACrC,OAAO,CAAC;EAC5C,IAAI,CAACqC,SAAS,CAACrC,OAAO,EAAEyC,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,IAAIf,MAAM,CAACa,GAAG,EAAEE,aAAa,EAAEC,YAAY,CAAC,YAAY,CAAC,CAAC,KAAKL,WAAW,EAAE;IAC7HD,SAAS,CAACrC,OAAO,EAAE4C,aAAa,CAAC,8BAA8BN,WAAW,IAAI,CAAC,CAACO,KAAK,CAAC,CAAC;EACzF;EACA,IAAIN,SAAS,EAAE;IACbA,SAAS,CAACD,WAAW,CAAC;EACxB;AACF;AACA,SAASQ,cAAcA,CAACf,QAAQ,EAAEgB,QAAQ,EAAE;EAC1C,IAAI,OAAOhB,QAAQ,KAAK,QAAQ,IAAI,OAAOgB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOhB,QAAQ,KAAKgB,QAAQ;EAC9B;EACA,IAAI,OAAOhB,QAAQ,KAAK,QAAQ,IAAI,OAAOgB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOvE,cAAc,CAACuD,QAAQ,EAAEgB,QAAQ,CAAC;EAC3C;EACA,OAAO,KAAK;AACd;AACA,MAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE;IACVC,MAAM,EAAEpC,OAAO,KAAK;MAClBqC,IAAI,EAAE,GAAGrC,OAAO;IAClB,CAAC,CAAC;IACFsC,IAAI,EAAEtC,OAAO,KAAK;MAChBuC,KAAK,EAAE,GAAGvC,OAAO;IACnB,CAAC;EACH,CAAC;EACD,oBAAoB,EAAE;IACpBoC,MAAM,EAAEpC,OAAO,KAAK;MAClBwC,KAAK,EAAE,GAAGxC,OAAO;IACnB,CAAC,CAAC;IACFsC,IAAI,EAAEtC,OAAO,KAAK;MAChBuC,KAAK,EAAE,GAAGvC,OAAO;IACnB,CAAC;EACH,CAAC;EACDyC,QAAQ,EAAE;IACRL,MAAM,EAAEpC,OAAO,KAAK;MAClB0C,MAAM,EAAE,GAAG1C,OAAO;IACpB,CAAC,CAAC;IACFsC,IAAI,EAAEtC,OAAO,KAAK;MAChB2C,MAAM,EAAE,GAAG3C,OAAO;IACpB,CAAC;EACH;AACF,CAAC;AACD,OAAO,MAAM4C,QAAQ,GAAGlD,CAAC,IAAIA,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAImD,6BAA6B;AACjC,SAASC,0BAA0BA,CAAA,EAAG;EACpC,IAAID,6BAA6B,KAAK1D,SAAS,EAAE;IAC/C,IAAI,OAAO4D,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,CAACC,QAAQ,KAAK,UAAU,EAAE;MACpEH,6BAA6B,GAAGE,GAAG,CAACC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC;IACtE,CAAC,MAAM;MACLH,6BAA6B,GAAG,IAAI;IACtC;EACF;EACA,OAAOA,6BAA6B;AACtC;AACA,OAAO,SAASI,SAASA,CAACC,UAAU,EAAE;EACpC,MAAM;IACJ,iBAAiB,EAAEC,cAAc;IACjCC,YAAY;IACZC,QAAQ,GAAG,KAAK;IAChBC,WAAW,GAAG,KAAK;IACnBC,KAAK,GAAG,KAAK;IACbC,KAAK,EAAEC,SAAS,GAAG,KAAK;IACxBxF,GAAG,GAAG,GAAG;IACTD,GAAG,GAAG,CAAC;IACP0F,IAAI;IACJC,QAAQ;IACRC,iBAAiB;IACjBC,WAAW,GAAG,YAAY;IAC1BC,OAAO,EAAEC,GAAG;IACZC,KAAK,GAAGpB,QAAQ;IAChB9E,IAAI,GAAG,CAAC;IACRmG,SAAS,GAAG,EAAE;IACdC,QAAQ;IACRtF,KAAK,EAAEuF;EACT,CAAC,GAAGjB,UAAU;EACd,MAAMjE,OAAO,GAAGjC,KAAK,CAACoH,MAAM,CAACjF,SAAS,CAAC;EACvC;EACA;EACA;EACA,MAAM,CAACkF,MAAM,EAAE5C,SAAS,CAAC,GAAGzE,KAAK,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxH,KAAK,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG1H,KAAK,CAACsH,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMK,SAAS,GAAG3H,KAAK,CAACoH,MAAM,CAAC,CAAC,CAAC;EACjC;EACA,MAAMQ,gBAAgB,GAAG5H,KAAK,CAACoH,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAM,CAACS,YAAY,EAAEC,aAAa,CAAC,GAAG5H,aAAa,CAAC;IAClD6H,UAAU,EAAEZ,SAAS;IACrBa,OAAO,EAAE5B,YAAY,IAAIpF,GAAG;IAC5B0F,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMuB,YAAY,GAAGtB,QAAQ,KAAK,CAAC3E,KAAK,EAAEJ,KAAK,EAAEsG,UAAU,KAAK;IAC9D;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAGnG,KAAK,CAACmG,WAAW,IAAInG,KAAK;IAC9C;IACA,MAAMoG,WAAW,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,IAAI,EAAEH,WAAW,CAAC;IAC9EI,MAAM,CAACC,cAAc,CAACJ,WAAW,EAAE,QAAQ,EAAE;MAC3CK,QAAQ,EAAE,IAAI;MACd7G,KAAK,EAAE;QACLA,KAAK;QACL8E;MACF;IACF,CAAC,CAAC;IACFkB,gBAAgB,CAAC1F,OAAO,GAAGN,KAAK;IAChC+E,QAAQ,CAACyB,WAAW,EAAExG,KAAK,EAAEsG,UAAU,CAAC;EAC1C,CAAC,CAAC;EACF,MAAMQ,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACf,YAAY,CAAC;EACzC,IAAItG,MAAM,GAAGmH,KAAK,GAAGb,YAAY,CAAC1D,KAAK,CAAC,CAAC,CAACC,IAAI,CAACjD,GAAG,CAAC,GAAG,CAAC0G,YAAY,CAAC;EACpEtG,MAAM,GAAGA,MAAM,CAACsH,GAAG,CAACjH,KAAK,IAAIA,KAAK,IAAI,IAAI,GAAGZ,GAAG,GAAGR,KAAK,CAACoB,KAAK,EAAEZ,GAAG,EAAEC,GAAG,CAAC,CAAC;EAC1E,MAAMuF,KAAK,GAAGC,SAAS,KAAK,IAAI,IAAI3F,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG6H,KAAK,CAACzH,IAAI,CAAC4H,KAAK,CAAC,CAAC7H,GAAG,GAAGD,GAAG,IAAIF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC+H,GAAG,CAAC,CAACE,CAAC,EAAEvH,KAAK,MAAM;IACpHI,KAAK,EAAEZ,GAAG,GAAGF,IAAI,GAAGU;EACtB,CAAC,CAAC,CAAC,GAAGiF,SAAS,IAAI,EAAE;EACrB,MAAMuC,WAAW,GAAGxC,KAAK,CAACqC,GAAG,CAACI,IAAI,IAAIA,IAAI,CAACrH,KAAK,CAAC;EACjD,MAAM,CAACsH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnJ,KAAK,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM/C,SAAS,GAAGvE,KAAK,CAACoH,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMgC,SAAS,GAAG/I,UAAU,CAAC0G,GAAG,EAAExC,SAAS,CAAC;EAC5C,MAAM8E,4BAA4B,GAAGC,aAAa,IAAItH,KAAK,IAAI;IAC7D,MAAMR,KAAK,GAAGqC,MAAM,CAAC7B,KAAK,CAACuH,aAAa,CAAC1E,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE,IAAIvE,cAAc,CAAC0B,KAAK,CAACwH,MAAM,CAAC,EAAE;MAChCL,oBAAoB,CAAC3H,KAAK,CAAC;IAC7B;IACAgG,OAAO,CAAChG,KAAK,CAAC;IACd8H,aAAa,EAAEG,OAAO,GAAGzH,KAAK,CAAC;EACjC,CAAC;EACD,MAAM0H,2BAA2B,GAAGJ,aAAa,IAAItH,KAAK,IAAI;IAC5D,IAAI,CAAC1B,cAAc,CAAC0B,KAAK,CAACwH,MAAM,CAAC,EAAE;MACjCL,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B;IACA3B,OAAO,CAAC,CAAC,CAAC,CAAC;IACX8B,aAAa,EAAEK,MAAM,GAAG3H,KAAK,CAAC;EAChC,CAAC;EACD,MAAM4H,WAAW,GAAGA,CAAC5H,KAAK,EAAE6H,UAAU,KAAK;IACzC,MAAMrI,KAAK,GAAGqC,MAAM,CAAC7B,KAAK,CAACuH,aAAa,CAAC1E,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE,MAAMjD,KAAK,GAAGL,MAAM,CAACC,KAAK,CAAC;IAC3B,MAAMsI,UAAU,GAAGd,WAAW,CAACe,OAAO,CAACnI,KAAK,CAAC;IAC7C,IAAIqC,QAAQ,GAAG4F,UAAU;IACzB,IAAIrD,KAAK,IAAI1F,IAAI,IAAI,IAAI,EAAE;MACzB,MAAMkJ,aAAa,GAAGhB,WAAW,CAACA,WAAW,CAACzG,MAAM,GAAG,CAAC,CAAC;MACzD,IAAI0B,QAAQ,IAAI+F,aAAa,EAAE;QAC7B/F,QAAQ,GAAG+F,aAAa;MAC1B,CAAC,MAAM,IAAI/F,QAAQ,IAAI+E,WAAW,CAAC,CAAC,CAAC,EAAE;QACrC/E,QAAQ,GAAG+E,WAAW,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL/E,QAAQ,GAAGA,QAAQ,GAAGrC,KAAK,GAAGoH,WAAW,CAACc,UAAU,GAAG,CAAC,CAAC,GAAGd,WAAW,CAACc,UAAU,GAAG,CAAC,CAAC;MACzF;IACF;IACA7F,QAAQ,GAAGzD,KAAK,CAACyD,QAAQ,EAAEjD,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIyH,KAAK,EAAE;MACT;MACA,IAAIpC,WAAW,EAAE;QACfrC,QAAQ,GAAGzD,KAAK,CAACyD,QAAQ,EAAE1C,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,IAAI,CAACyI,QAAQ,EAAE1I,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,IAAIyI,QAAQ,CAAC;MAC3F;MACA,MAAMC,aAAa,GAAGjG,QAAQ;MAC9BA,QAAQ,GAAGF,aAAa,CAAC;QACvBxC,MAAM;QACN0C,QAAQ;QACRzC;MACF,CAAC,CAAC;MACF,IAAIgD,WAAW,GAAGhD,KAAK;;MAEvB;MACA,IAAI,CAAC8E,WAAW,EAAE;QAChB9B,WAAW,GAAGP,QAAQ,CAAC8F,OAAO,CAACG,aAAa,CAAC;MAC/C;MACA7F,UAAU,CAAC;QACTE,SAAS;QACTC;MACF,CAAC,CAAC;IACJ;IACAsD,aAAa,CAAC7D,QAAQ,CAAC;IACvBkF,oBAAoB,CAAC3H,KAAK,CAAC;IAC3B,IAAIyG,YAAY,IAAI,CAACjD,cAAc,CAACf,QAAQ,EAAE4D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACjG,KAAK,EAAEiC,QAAQ,EAAEzC,KAAK,CAAC;IACtC;IACA,IAAIoF,iBAAiB,EAAE;MACrBA,iBAAiB,CAAC5E,KAAK,EAAE4F,gBAAgB,CAAC1F,OAAO,IAAI+B,QAAQ,CAAC;IAChE;EACF,CAAC;EACD,MAAMkG,8BAA8B,GAAGb,aAAa,IAAItH,KAAK,IAAI;IAC/D,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,CAAC,CAACoI,QAAQ,CAACpI,KAAK,CAACqI,GAAG,CAAC,EAAE;MAChHrI,KAAK,CAACsI,cAAc,CAAC,CAAC;MACtB,MAAM9I,KAAK,GAAGqC,MAAM,CAAC7B,KAAK,CAACuH,aAAa,CAAC1E,YAAY,CAAC,YAAY,CAAC,CAAC;MACpE,MAAMjD,KAAK,GAAGL,MAAM,CAACC,KAAK,CAAC;MAC3B,IAAIyC,QAAQ,GAAG,IAAI;MACnB;MACA;MACA;MACA,IAAInD,IAAI,IAAI,IAAI,EAAE;QAChB,MAAMyJ,QAAQ,GAAGvI,KAAK,CAACwI,QAAQ,GAAGvD,SAAS,GAAGnG,IAAI;QAClD,QAAQkB,KAAK,CAACqI,GAAG;UACf,KAAK,SAAS;YACZpG,QAAQ,GAAGrD,WAAW,CAACgB,KAAK,EAAE2I,QAAQ,EAAE,CAAC,EAAEvJ,GAAG,EAAEC,GAAG,CAAC;YACpD;UACF,KAAK,YAAY;YACfgD,QAAQ,GAAGrD,WAAW,CAACgB,KAAK,EAAE2I,QAAQ,EAAEhE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEvF,GAAG,EAAEC,GAAG,CAAC;YACjE;UACF,KAAK,WAAW;YACdgD,QAAQ,GAAGrD,WAAW,CAACgB,KAAK,EAAE2I,QAAQ,EAAE,CAAC,CAAC,EAAEvJ,GAAG,EAAEC,GAAG,CAAC;YACrD;UACF,KAAK,WAAW;YACdgD,QAAQ,GAAGrD,WAAW,CAACgB,KAAK,EAAE2I,QAAQ,EAAEhE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEvF,GAAG,EAAEC,GAAG,CAAC;YACjE;UACF,KAAK,QAAQ;YACXgD,QAAQ,GAAGrD,WAAW,CAACgB,KAAK,EAAEqF,SAAS,EAAE,CAAC,EAAEjG,GAAG,EAAEC,GAAG,CAAC;YACrD;UACF,KAAK,UAAU;YACbgD,QAAQ,GAAGrD,WAAW,CAACgB,KAAK,EAAEqF,SAAS,EAAE,CAAC,CAAC,EAAEjG,GAAG,EAAEC,GAAG,CAAC;YACtD;UACF,KAAK,MAAM;YACTgD,QAAQ,GAAGjD,GAAG;YACd;UACF,KAAK,KAAK;YACRiD,QAAQ,GAAGhD,GAAG;YACd;UACF;YACE;QACJ;MACF,CAAC,MAAM,IAAIuF,KAAK,EAAE;QAChB,MAAMwD,aAAa,GAAGhB,WAAW,CAACA,WAAW,CAACzG,MAAM,GAAG,CAAC,CAAC;QACzD,MAAMkI,gBAAgB,GAAGzB,WAAW,CAACe,OAAO,CAACnI,KAAK,CAAC;QACnD,MAAM8I,aAAa,GAAG,CAACnE,KAAK,GAAG,YAAY,GAAG,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC;QAC3F,MAAMoE,aAAa,GAAG,CAACpE,KAAK,GAAG,WAAW,GAAG,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC;QACtF,IAAImE,aAAa,CAACN,QAAQ,CAACpI,KAAK,CAACqI,GAAG,CAAC,EAAE;UACrC,IAAII,gBAAgB,KAAK,CAAC,EAAE;YAC1BxG,QAAQ,GAAG+E,WAAW,CAAC,CAAC,CAAC;UAC3B,CAAC,MAAM;YACL/E,QAAQ,GAAG+E,WAAW,CAACyB,gBAAgB,GAAG,CAAC,CAAC;UAC9C;QACF,CAAC,MAAM,IAAIE,aAAa,CAACP,QAAQ,CAACpI,KAAK,CAACqI,GAAG,CAAC,EAAE;UAC5C,IAAII,gBAAgB,KAAKzB,WAAW,CAACzG,MAAM,GAAG,CAAC,EAAE;YAC/C0B,QAAQ,GAAG+F,aAAa;UAC1B,CAAC,MAAM;YACL/F,QAAQ,GAAG+E,WAAW,CAACyB,gBAAgB,GAAG,CAAC,CAAC;UAC9C;QACF;MACF;MACA,IAAIxG,QAAQ,IAAI,IAAI,EAAE;QACpB2F,WAAW,CAAC5H,KAAK,EAAEiC,QAAQ,CAAC;MAC9B;IACF;IACAqF,aAAa,EAAEsB,SAAS,GAAG5I,KAAK,CAAC;EACnC,CAAC;EACD7B,iBAAiB,CAAC,MAAM;IACtB,IAAIkG,QAAQ,IAAI9B,SAAS,CAACrC,OAAO,CAACyC,QAAQ,CAACkG,QAAQ,CAACjG,aAAa,CAAC,EAAE;MAClE;MACA;MACA;MACA;MACAiG,QAAQ,CAACjG,aAAa,EAAEkG,IAAI,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACzE,QAAQ,CAAC,CAAC;EACd,IAAIA,QAAQ,IAAIgB,MAAM,KAAK,CAAC,CAAC,EAAE;IAC7B5C,SAAS,CAAC,CAAC,CAAC,CAAC;EACf;EACA,IAAI4B,QAAQ,IAAI6C,iBAAiB,KAAK,CAAC,CAAC,EAAE;IACxCC,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC1B;EACA,MAAM4B,6BAA6B,GAAGzB,aAAa,IAAItH,KAAK,IAAI;IAC9DsH,aAAa,CAAC3C,QAAQ,GAAG3E,KAAK,CAAC;IAC/B;IACA;IACA4H,WAAW,CAAC5H,KAAK,EAAEA,KAAK,CAACwH,MAAM,CAACwB,aAAa,CAAC;EAChD,CAAC;EACD,MAAMC,aAAa,GAAGjL,KAAK,CAACoH,MAAM,CAACjF,SAAS,CAAC;EAC7C,IAAI+I,IAAI,GAAGrE,WAAW;EACtB,IAAIN,KAAK,IAAIM,WAAW,KAAK,YAAY,EAAE;IACzCqE,IAAI,IAAI,UAAU;EACpB;EACA,MAAMC,iBAAiB,GAAGC,KAAA,IAGpB;IAAA,IAHqB;MACzBC,MAAM;MACNC,IAAI,GAAG;IACT,CAAC,GAAAF,KAAA;IACC,MAAM;MACJlJ,OAAO,EAAEqJ;IACX,CAAC,GAAGhH,SAAS;IACb,MAAM;MACJgB,KAAK;MACLI,MAAM;MACND,MAAM;MACNL;IACF,CAAC,GAAGkG,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAClC,IAAIxI,OAAO;IACX,IAAIkI,IAAI,CAACO,UAAU,CAAC,UAAU,CAAC,EAAE;MAC/BzI,OAAO,GAAG,CAAC0C,MAAM,GAAG2F,MAAM,CAACzI,CAAC,IAAI+C,MAAM;IACxC,CAAC,MAAM;MACL3C,OAAO,GAAG,CAACqI,MAAM,CAAC3I,CAAC,GAAG2C,IAAI,IAAIE,KAAK;IACrC;IACA,IAAI2F,IAAI,CAACd,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7BpH,OAAO,GAAG,CAAC,GAAGA,OAAO;IACvB;IACA,IAAIiB,QAAQ;IACZA,QAAQ,GAAGlB,cAAc,CAACC,OAAO,EAAEhC,GAAG,EAAEC,GAAG,CAAC;IAC5C,IAAIH,IAAI,EAAE;MACRmD,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ,EAAEnD,IAAI,EAAEE,GAAG,CAAC;IAClD,CAAC,MAAM;MACL,MAAMS,YAAY,GAAGH,WAAW,CAAC0H,WAAW,EAAE/E,QAAQ,CAAC;MACvDA,QAAQ,GAAG+E,WAAW,CAACvH,YAAY,CAAC;IACtC;IACAwC,QAAQ,GAAGzD,KAAK,CAACyD,QAAQ,EAAEjD,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIuD,WAAW,GAAG,CAAC;IACnB,IAAIkE,KAAK,EAAE;MACT,IAAI,CAAC4C,IAAI,EAAE;QACT9G,WAAW,GAAGlD,WAAW,CAACC,MAAM,EAAE0C,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACLO,WAAW,GAAGyG,aAAa,CAAC/I,OAAO;MACrC;;MAEA;MACA,IAAIoE,WAAW,EAAE;QACfrC,QAAQ,GAAGzD,KAAK,CAACyD,QAAQ,EAAE1C,MAAM,CAACiD,WAAW,GAAG,CAAC,CAAC,IAAI,CAACyF,QAAQ,EAAE1I,MAAM,CAACiD,WAAW,GAAG,CAAC,CAAC,IAAIyF,QAAQ,CAAC;MACvG;MACA,MAAMC,aAAa,GAAGjG,QAAQ;MAC9BA,QAAQ,GAAGF,aAAa,CAAC;QACvBxC,MAAM;QACN0C,QAAQ;QACRzC,KAAK,EAAEgD;MACT,CAAC,CAAC;;MAEF;MACA,IAAI,EAAE8B,WAAW,IAAIgF,IAAI,CAAC,EAAE;QAC1B9G,WAAW,GAAGP,QAAQ,CAAC8F,OAAO,CAACG,aAAa,CAAC;QAC7Ce,aAAa,CAAC/I,OAAO,GAAGsC,WAAW;MACrC;IACF;IACA,OAAO;MACLP,QAAQ;MACRO;IACF,CAAC;EACH,CAAC;EACD,MAAMkH,eAAe,GAAGtL,gBAAgB,CAAC+H,WAAW,IAAI;IACtD,MAAMkD,MAAM,GAAGtJ,WAAW,CAACoG,WAAW,EAAElG,OAAO,CAAC;IAChD,IAAI,CAACoJ,MAAM,EAAE;MACX;IACF;IACA1D,SAAS,CAACzF,OAAO,IAAI,CAAC;;IAEtB;IACA;IACA,IAAIiG,WAAW,CAACG,IAAI,KAAK,WAAW,IAAIH,WAAW,CAACwD,OAAO,KAAK,CAAC,EAAE;MACjE;MACAC,cAAc,CAACzD,WAAW,CAAC;MAC3B;IACF;IACA,MAAM;MACJlE,QAAQ;MACRO;IACF,CAAC,GAAG2G,iBAAiB,CAAC;MACpBE,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACFjH,UAAU,CAAC;MACTE,SAAS;MACTC,WAAW;MACXC;IACF,CAAC,CAAC;IACFqD,aAAa,CAAC7D,QAAQ,CAAC;IACvB,IAAI,CAACwD,QAAQ,IAAIE,SAAS,CAACzF,OAAO,GAAGvB,gCAAgC,EAAE;MACrE+G,WAAW,CAAC,IAAI,CAAC;IACnB;IACA,IAAIO,YAAY,IAAI,CAACjD,cAAc,CAACf,QAAQ,EAAE4D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACE,WAAW,EAAElE,QAAQ,EAAEO,WAAW,CAAC;IAClD;EACF,CAAC,CAAC;EACF,MAAMoH,cAAc,GAAGxL,gBAAgB,CAAC+H,WAAW,IAAI;IACrD,MAAMkD,MAAM,GAAGtJ,WAAW,CAACoG,WAAW,EAAElG,OAAO,CAAC;IAChDyF,WAAW,CAAC,KAAK,CAAC;IAClB,IAAI,CAAC2D,MAAM,EAAE;MACX;IACF;IACA,MAAM;MACJpH;IACF,CAAC,GAAGkH,iBAAiB,CAAC;MACpBE,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACF7G,SAAS,CAAC,CAAC,CAAC,CAAC;IACb,IAAI0D,WAAW,CAACG,IAAI,KAAK,UAAU,EAAE;MACnCd,OAAO,CAAC,CAAC,CAAC,CAAC;IACb;IACA,IAAIZ,iBAAiB,EAAE;MACrBA,iBAAiB,CAACuB,WAAW,EAAEP,gBAAgB,CAAC1F,OAAO,IAAI+B,QAAQ,CAAC;IACtE;IACAhC,OAAO,CAACC,OAAO,GAAGC,SAAS;;IAE3B;IACA0J,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAG1L,gBAAgB,CAAC+H,WAAW,IAAI;IACvD,IAAI9B,QAAQ,EAAE;MACZ;IACF;IACA;IACA,IAAI,CAACP,0BAA0B,CAAC,CAAC,EAAE;MACjCqC,WAAW,CAACmC,cAAc,CAAC,CAAC;IAC9B;IACA,MAAM9H,KAAK,GAAG2F,WAAW,CAAC/F,cAAc,CAAC,CAAC,CAAC;IAC3C,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB;MACAP,OAAO,CAACC,OAAO,GAAGM,KAAK,CAACC,UAAU;IACpC;IACA,MAAM4I,MAAM,GAAGtJ,WAAW,CAACoG,WAAW,EAAElG,OAAO,CAAC;IAChD,IAAIoJ,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJpH,QAAQ;QACRO;MACF,CAAC,GAAG2G,iBAAiB,CAAC;QACpBE;MACF,CAAC,CAAC;MACFhH,UAAU,CAAC;QACTE,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFqD,aAAa,CAAC7D,QAAQ,CAAC;MACvB,IAAIgE,YAAY,IAAI,CAACjD,cAAc,CAACf,QAAQ,EAAE4D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACE,WAAW,EAAElE,QAAQ,EAAEO,WAAW,CAAC;MAClD;IACF;IACAmD,SAAS,CAACzF,OAAO,GAAG,CAAC;IACrB,MAAMwC,GAAG,GAAGzE,aAAa,CAACsE,SAAS,CAACrC,OAAO,CAAC;IAC5CwC,GAAG,CAACqH,gBAAgB,CAAC,WAAW,EAAEL,eAAe,EAAE;MACjDM,OAAO,EAAE;IACX,CAAC,CAAC;IACFtH,GAAG,CAACqH,gBAAgB,CAAC,UAAU,EAAEH,cAAc,EAAE;MAC/CI,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMH,aAAa,GAAG7L,KAAK,CAACiM,WAAW,CAAC,MAAM;IAC5C,MAAMvH,GAAG,GAAGzE,aAAa,CAACsE,SAAS,CAACrC,OAAO,CAAC;IAC5CwC,GAAG,CAACwH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrDhH,GAAG,CAACwH,mBAAmB,CAAC,SAAS,EAAEN,cAAc,CAAC;IAClDlH,GAAG,CAACwH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrDhH,GAAG,CAACwH,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;EACrD,CAAC,EAAE,CAACA,cAAc,EAAEF,eAAe,CAAC,CAAC;EACrC1L,KAAK,CAACmM,SAAS,CAAC,MAAM;IACpB,MAAM;MACJjK,OAAO,EAAEqJ;IACX,CAAC,GAAGhH,SAAS;IACbgH,MAAM,CAACQ,gBAAgB,CAAC,YAAY,EAAED,gBAAgB,EAAE;MACtDE,OAAO,EAAElG,0BAA0B,CAAC;IACtC,CAAC,CAAC;IACF,OAAO,MAAM;MACXyF,MAAM,CAACW,mBAAmB,CAAC,YAAY,EAAEJ,gBAAgB,CAAC;MAC1DD,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,EAAEC,gBAAgB,CAAC,CAAC;EACrC9L,KAAK,CAACmM,SAAS,CAAC,MAAM;IACpB,IAAI9F,QAAQ,EAAE;MACZwF,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACxF,QAAQ,EAAEwF,aAAa,CAAC,CAAC;EAC7B,MAAMO,qBAAqB,GAAG9C,aAAa,IAAItH,KAAK,IAAI;IACtDsH,aAAa,CAAC+C,WAAW,GAAGrK,KAAK,CAAC;IAClC,IAAIqE,QAAQ,EAAE;MACZ;IACF;IACA,IAAIrE,KAAK,CAACsK,gBAAgB,EAAE;MAC1B;IACF;;IAEA;IACA,IAAItK,KAAK,CAACuK,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;;IAEA;IACAvK,KAAK,CAACsI,cAAc,CAAC,CAAC;IACtB,MAAMe,MAAM,GAAGtJ,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC;IAC1C,IAAIoJ,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJpH,QAAQ;QACRO;MACF,CAAC,GAAG2G,iBAAiB,CAAC;QACpBE;MACF,CAAC,CAAC;MACFhH,UAAU,CAAC;QACTE,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFqD,aAAa,CAAC7D,QAAQ,CAAC;MACvB,IAAIgE,YAAY,IAAI,CAACjD,cAAc,CAACf,QAAQ,EAAE4D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACjG,KAAK,EAAEiC,QAAQ,EAAEO,WAAW,CAAC;MAC5C;IACF;IACAmD,SAAS,CAACzF,OAAO,GAAG,CAAC;IACrB,MAAMwC,GAAG,GAAGzE,aAAa,CAACsE,SAAS,CAACrC,OAAO,CAAC;IAC5CwC,GAAG,CAACqH,gBAAgB,CAAC,WAAW,EAAEL,eAAe,EAAE;MACjDM,OAAO,EAAE;IACX,CAAC,CAAC;IACFtH,GAAG,CAACqH,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;EACjD,CAAC;EACD,MAAMY,WAAW,GAAG1J,cAAc,CAAC4F,KAAK,GAAGnH,MAAM,CAAC,CAAC,CAAC,GAAGP,GAAG,EAAEA,GAAG,EAAEC,GAAG,CAAC;EACrE,MAAMwL,SAAS,GAAG3J,cAAc,CAACvB,MAAM,CAACA,MAAM,CAACgB,MAAM,GAAG,CAAC,CAAC,EAAEvB,GAAG,EAAEC,GAAG,CAAC,GAAGuL,WAAW;EACnF,MAAME,YAAY,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBC,aAAa,GAAAC,SAAA,CAAArK,MAAA,QAAAqK,SAAA,QAAAzK,SAAA,GAAAyK,SAAA,MAAG,CAAC,CAAC;IACtC,MAAMC,gBAAgB,GAAGpM,oBAAoB,CAACkM,aAAa,CAAC;IAC5D,MAAMG,gBAAgB,GAAG;MACvBT,WAAW,EAAED,qBAAqB,CAACS,gBAAgB,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,MAAME,mBAAmB,GAAG;MAC1B,GAAGF,gBAAgB;MACnB,GAAGC;IACL,CAAC;IACD,OAAO;MACL,GAAGH,aAAa;MAChB5F,GAAG,EAAEqC,SAAS;MACd,GAAG2D;IACL,CAAC;EACH,CAAC;EACD,MAAMC,qBAAqB,GAAG1D,aAAa,IAAItH,KAAK,IAAI;IACtDsH,aAAa,CAAC2D,WAAW,GAAGjL,KAAK,CAAC;IAClC,MAAMR,KAAK,GAAGqC,MAAM,CAAC7B,KAAK,CAACuH,aAAa,CAAC1E,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE2C,OAAO,CAAChG,KAAK,CAAC;EAChB,CAAC;EACD,MAAM0L,sBAAsB,GAAG5D,aAAa,IAAItH,KAAK,IAAI;IACvDsH,aAAa,CAAC6D,YAAY,GAAGnL,KAAK,CAAC;IACnCwF,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,MAAM4F,aAAa,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBT,aAAa,GAAAC,SAAA,CAAArK,MAAA,QAAAqK,SAAA,QAAAzK,SAAA,GAAAyK,SAAA,MAAG,CAAC,CAAC;IACvC,MAAMC,gBAAgB,GAAGpM,oBAAoB,CAACkM,aAAa,CAAC;IAC5D,MAAMG,gBAAgB,GAAG;MACvBG,WAAW,EAAED,qBAAqB,CAACH,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC1DM,YAAY,EAAED,sBAAsB,CAACL,gBAAgB,IAAI,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO;MACL,GAAGF,aAAa;MAChB,GAAGE,gBAAgB;MACnB,GAAGC;IACL,CAAC;EACH,CAAC;EACD,MAAMO,aAAa,GAAG7L,KAAK,IAAI;IAC7B,OAAO;MACL;MACA8L,aAAa,EAAEjG,MAAM,KAAK,CAAC,CAAC,IAAIA,MAAM,KAAK7F,KAAK,GAAG,MAAM,GAAGW;IAC9D,CAAC;EACH,CAAC;EACD,IAAIoL,cAAc;EAClB,IAAI1G,WAAW,KAAK,UAAU,EAAE;IAC9B0G,cAAc,GAAGhH,KAAK,GAAG,aAAa,GAAG,aAAa;EACxD;EACA,MAAMiH,mBAAmB,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBb,aAAa,GAAAC,SAAA,CAAArK,MAAA,QAAAqK,SAAA,QAAAzK,SAAA,GAAAyK,SAAA,MAAG,CAAC,CAAC;IAC7C,MAAMC,gBAAgB,GAAGpM,oBAAoB,CAACkM,aAAa,CAAC;IAC5D,MAAMG,gBAAgB,GAAG;MACvBnG,QAAQ,EAAEoE,6BAA6B,CAAC8B,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC/DpD,OAAO,EAAEJ,4BAA4B,CAACwD,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC7DlD,MAAM,EAAED,2BAA2B,CAACmD,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC3DjC,SAAS,EAAET,8BAA8B,CAAC0C,gBAAgB,IAAI,CAAC,CAAC;IAClE,CAAC;IACD,MAAME,mBAAmB,GAAG;MAC1B,GAAGF,gBAAgB;MACnB,GAAGC;IACL,CAAC;IACD,OAAO;MACL5F,QAAQ;MACR,iBAAiB,EAAEf,cAAc;MACjC,kBAAkB,EAAEU,WAAW;MAC/B,eAAe,EAAEG,KAAK,CAAC/F,GAAG,CAAC;MAC3B,eAAe,EAAE+F,KAAK,CAAChG,GAAG,CAAC;MAC3B0F,IAAI;MACJ4B,IAAI,EAAE,OAAO;MACbtH,GAAG,EAAEkF,UAAU,CAAClF,GAAG;MACnBC,GAAG,EAAEiF,UAAU,CAACjF,GAAG;MACnBH,IAAI,EAAEoF,UAAU,CAACpF,IAAI,KAAK,IAAI,IAAIoF,UAAU,CAACM,KAAK,GAAG,KAAK,GAAGN,UAAU,CAACpF,IAAI,IAAIqB,SAAS;MACzFkE,QAAQ;MACR,GAAGsG,aAAa;MAChB,GAAGI,mBAAmB;MACtBU,KAAK,EAAE;QACL,GAAGlN,cAAc;QACjBQ,SAAS,EAAEwF,KAAK,GAAG,KAAK,GAAG,KAAK;QAChC;QACAhB,KAAK,EAAE,MAAM;QACbI,MAAM,EAAE,MAAM;QACd+H,WAAW,EAAEH;MACf;IACF,CAAC;EACH,CAAC;EACD,OAAO;IACLlG,MAAM;IACN6D,IAAI,EAAEA,IAAI;IACVhG,SAAS;IACTuC,QAAQ;IACRyB,iBAAiB;IACjBsE,mBAAmB;IACnBd,YAAY;IACZU,aAAa;IACb5G,KAAK,EAAEA,KAAK;IACZe,IAAI;IACJmB,KAAK;IACL5B,OAAO,EAAEsC,SAAS;IAClBqD,SAAS;IACTD,WAAW;IACXjL,MAAM;IACN8L;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}