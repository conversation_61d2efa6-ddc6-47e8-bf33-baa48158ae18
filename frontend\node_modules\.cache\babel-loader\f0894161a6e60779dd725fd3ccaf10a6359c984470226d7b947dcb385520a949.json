{"ast": null, "code": "import React from'react';import{Container,Row,<PERSON>,<PERSON>,Badge}from'react-bootstrap';import <PERSON>uck<PERSON><PERSON><PERSON> from'../../puck/PuckRenderer';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CmsPageTemplate=_ref=>{let{page,content,className}=_ref;const formatDate=dateString=>{return new Date(dateString).toLocaleDateString('en-US',{year:'numeric',month:'long',day:'numeric',hour:'2-digit',minute:'2-digit'});};return/*#__PURE__*/_jsx(\"div\",{className:`cms-page-template ${className||''}`,children:/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{lg:10,xl:8,children:/*#__PURE__*/_jsxs(Card,{className:\"border-0 shadow-sm\",children:[page.featured_image&&/*#__PURE__*/_jsx(Card.Img,{variant:\"top\",src:page.featured_image,alt:page.title,style:{height:'400px',objectFit:'cover',borderRadius:'0.375rem 0.375rem 0 0'}}),/*#__PURE__*/_jsxs(Card.Body,{className:\"p-4 p-md-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[page.is_featured&&/*#__PURE__*/_jsxs(Badge,{bg:\"warning\",text:\"dark\",className:\"me-2\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star me-1\"}),\"Featured\"]}),/*#__PURE__*/_jsxs(Badge,{bg:\"success\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check-circle me-1\"}),\"Published\"]})]}),/*#__PURE__*/_jsx(\"h1\",{className:\"display-5 fw-bold mb-3 text-dark\",children:page.title}),page.meta_description&&/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-light border-start border-primary border-4 mb-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"mb-0 text-muted fst-italic\",children:page.meta_description})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-muted small\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex flex-wrap align-items-center gap-3\",children:[page.creator&&/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user me-1\"}),\"By \",/*#__PURE__*/_jsx(\"strong\",{children:page.creator.name})]}),/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-calendar me-1\"}),\"Published \",formatDate(page.published_at||page.created_at)]}),page.updated_at!==page.created_at&&page.updater&&/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-edit me-1\"}),\"Updated by \",/*#__PURE__*/_jsx(\"strong\",{children:page.updater.name})]})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"cms-content\",children:page.editor_type==='puck'?/*#__PURE__*/_jsx(PuckRenderer,{data:page.puck_data,renderedContent:content,className:\"puck-content\"}):/*#__PURE__*/_jsx(\"div\",{className:\"html-content\",dangerouslySetInnerHTML:{__html:content},style:{lineHeight:1.7,fontSize:'1.1rem',color:'#333'}})}),(page.meta_keywords||page.updated_at!==page.created_at)&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-5 pt-4 border-top\",children:[page.meta_keywords&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"text-muted mb-2\",children:\"Tags:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex flex-wrap gap-2\",children:page.meta_keywords.split(',').map((keyword,index)=>/*#__PURE__*/_jsx(Badge,{bg:\"light\",text:\"dark\",className:\"border\",children:keyword.trim()},index))})]}),page.updated_at!==page.created_at&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-muted small\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-clock me-1\"}),\"Last updated: \",formatDate(page.updated_at)]})]})]})]})})})})});};export default CmsPageTemplate;", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "Card", "Badge", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "CmsPageTemplate", "_ref", "page", "content", "className", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "children", "lg", "xl", "featured_image", "Img", "variant", "src", "alt", "title", "style", "height", "objectFit", "borderRadius", "Body", "is_featured", "bg", "text", "meta_description", "creator", "name", "published_at", "created_at", "updated_at", "updater", "editor_type", "data", "puck_data", "renderedContent", "dangerouslySetInnerHTML", "__html", "lineHeight", "fontSize", "color", "meta_keywords", "split", "map", "keyword", "index", "trim"], "sources": ["C:/laragon/www/frontend/src/components/cms/templates/CmsPageTemplate.tsx"], "sourcesContent": ["import React from 'react';\nimport { Container, Row, Col, Card, Badge } from 'react-bootstrap';\nimport { CmsPage } from '../../../services/cmsService';\nimport PuckRenderer from '../../puck/PuckRenderer';\n\ninterface CmsPageTemplateProps {\n  page: CmsPage;\n  content: string;\n  className?: string;\n}\n\nconst CmsPageTemplate: React.FC<CmsPageTemplateProps> = ({ page, content, className }) => {\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  return (\n    <div className={`cms-page-template ${className || ''}`}>\n      <Container>\n        <Row className=\"justify-content-center\">\n          <Col lg={10} xl={8}>\n            <Card className=\"border-0 shadow-sm\">\n              {page.featured_image && (\n                <Card.Img \n                  variant=\"top\" \n                  src={page.featured_image} \n                  alt={page.title}\n                  style={{ \n                    height: '400px', \n                    objectFit: 'cover',\n                    borderRadius: '0.375rem 0.375rem 0 0'\n                  }}\n                />\n              )}\n              \n              <Card.Body className=\"p-4 p-md-5\">\n                {/* Page Header */}\n                <div className=\"mb-4\">\n                  <div className=\"mb-3\">\n                    {page.is_featured && (\n                      <Badge bg=\"warning\" text=\"dark\" className=\"me-2\">\n                        <i className=\"fas fa-star me-1\"></i>\n                        Featured\n                      </Badge>\n                    )}\n                    <Badge bg=\"success\">\n                      <i className=\"fas fa-check-circle me-1\"></i>\n                      Published\n                    </Badge>\n                  </div>\n                  \n                  <h1 className=\"display-5 fw-bold mb-3 text-dark\">\n                    {page.title}\n                  </h1>\n                  \n                  {page.meta_description && (\n                    <div className=\"alert alert-light border-start border-primary border-4 mb-4\">\n                      <p className=\"mb-0 text-muted fst-italic\">\n                        {page.meta_description}\n                      </p>\n                    </div>\n                  )}\n                  \n                  <div className=\"text-muted small\">\n                    <div className=\"d-flex flex-wrap align-items-center gap-3\">\n                      {page.creator && (\n                        <span>\n                          <i className=\"fas fa-user me-1\"></i>\n                          By <strong>{page.creator.name}</strong>\n                        </span>\n                      )}\n                      \n                      <span>\n                        <i className=\"fas fa-calendar me-1\"></i>\n                        Published {formatDate(page.published_at || page.created_at)}\n                      </span>\n                      \n                      {page.updated_at !== page.created_at && page.updater && (\n                        <span>\n                          <i className=\"fas fa-edit me-1\"></i>\n                          Updated by <strong>{page.updater.name}</strong>\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Page Content */}\n                <div className=\"cms-content\">\n                  {page.editor_type === 'puck' ? (\n                    <PuckRenderer \n                      data={page.puck_data} \n                      renderedContent={content}\n                      className=\"puck-content\"\n                    />\n                  ) : (\n                    <div\n                      className=\"html-content\"\n                      dangerouslySetInnerHTML={{ __html: content }}\n                      style={{\n                        lineHeight: 1.7,\n                        fontSize: '1.1rem',\n                        color: '#333',\n                      }}\n                    />\n                  )}\n                </div>\n\n                {/* Page Footer */}\n                {(page.meta_keywords || page.updated_at !== page.created_at) && (\n                  <div className=\"mt-5 pt-4 border-top\">\n                    {page.meta_keywords && (\n                      <div className=\"mb-3\">\n                        <h6 className=\"text-muted mb-2\">Tags:</h6>\n                        <div className=\"d-flex flex-wrap gap-2\">\n                          {page.meta_keywords.split(',').map((keyword, index) => (\n                            <Badge \n                              key={index} \n                              bg=\"light\" \n                              text=\"dark\" \n                              className=\"border\"\n                            >\n                              {keyword.trim()}\n                            </Badge>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                    \n                    {page.updated_at !== page.created_at && (\n                      <div className=\"text-muted small\">\n                        <i className=\"fas fa-clock me-1\"></i>\n                        Last updated: {formatDate(page.updated_at)}\n                      </div>\n                    )}\n                  </div>\n                )}\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      </Container>\n    </div>\n  );\n};\n\nexport default CmsPageTemplate;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,KAAQ,iBAAiB,CAElE,MAAO,CAAAC,YAAY,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQnD,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAAkC,IAAjC,CAAEC,IAAI,CAAEC,OAAO,CAAEC,SAAU,CAAC,CAAAH,IAAA,CACnF,KAAM,CAAAI,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtDC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAAC,CAED,mBACEhB,IAAA,QAAKO,SAAS,CAAE,qBAAqBA,SAAS,EAAI,EAAE,EAAG,CAAAU,QAAA,cACrDjB,IAAA,CAACP,SAAS,EAAAwB,QAAA,cACRjB,IAAA,CAACN,GAAG,EAACa,SAAS,CAAC,wBAAwB,CAAAU,QAAA,cACrCjB,IAAA,CAACL,GAAG,EAACuB,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAF,QAAA,cACjBf,KAAA,CAACN,IAAI,EAACW,SAAS,CAAC,oBAAoB,CAAAU,QAAA,EACjCZ,IAAI,CAACe,cAAc,eAClBpB,IAAA,CAACJ,IAAI,CAACyB,GAAG,EACPC,OAAO,CAAC,KAAK,CACbC,GAAG,CAAElB,IAAI,CAACe,cAAe,CACzBI,GAAG,CAAEnB,IAAI,CAACoB,KAAM,CAChBC,KAAK,CAAE,CACLC,MAAM,CAAE,OAAO,CACfC,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,uBAChB,CAAE,CACH,CACF,cAED3B,KAAA,CAACN,IAAI,CAACkC,IAAI,EAACvB,SAAS,CAAC,YAAY,CAAAU,QAAA,eAE/Bf,KAAA,QAAKK,SAAS,CAAC,MAAM,CAAAU,QAAA,eACnBf,KAAA,QAAKK,SAAS,CAAC,MAAM,CAAAU,QAAA,EAClBZ,IAAI,CAAC0B,WAAW,eACf7B,KAAA,CAACL,KAAK,EAACmC,EAAE,CAAC,SAAS,CAACC,IAAI,CAAC,MAAM,CAAC1B,SAAS,CAAC,MAAM,CAAAU,QAAA,eAC9CjB,IAAA,MAAGO,SAAS,CAAC,kBAAkB,CAAI,CAAC,WAEtC,EAAO,CACR,cACDL,KAAA,CAACL,KAAK,EAACmC,EAAE,CAAC,SAAS,CAAAf,QAAA,eACjBjB,IAAA,MAAGO,SAAS,CAAC,0BAA0B,CAAI,CAAC,YAE9C,EAAO,CAAC,EACL,CAAC,cAENP,IAAA,OAAIO,SAAS,CAAC,kCAAkC,CAAAU,QAAA,CAC7CZ,IAAI,CAACoB,KAAK,CACT,CAAC,CAEJpB,IAAI,CAAC6B,gBAAgB,eACpBlC,IAAA,QAAKO,SAAS,CAAC,6DAA6D,CAAAU,QAAA,cAC1EjB,IAAA,MAAGO,SAAS,CAAC,4BAA4B,CAAAU,QAAA,CACtCZ,IAAI,CAAC6B,gBAAgB,CACrB,CAAC,CACD,CACN,cAEDlC,IAAA,QAAKO,SAAS,CAAC,kBAAkB,CAAAU,QAAA,cAC/Bf,KAAA,QAAKK,SAAS,CAAC,2CAA2C,CAAAU,QAAA,EACvDZ,IAAI,CAAC8B,OAAO,eACXjC,KAAA,SAAAe,QAAA,eACEjB,IAAA,MAAGO,SAAS,CAAC,kBAAkB,CAAI,CAAC,MACjC,cAAAP,IAAA,WAAAiB,QAAA,CAASZ,IAAI,CAAC8B,OAAO,CAACC,IAAI,CAAS,CAAC,EACnC,CACP,cAEDlC,KAAA,SAAAe,QAAA,eACEjB,IAAA,MAAGO,SAAS,CAAC,sBAAsB,CAAI,CAAC,aAC9B,CAACC,UAAU,CAACH,IAAI,CAACgC,YAAY,EAAIhC,IAAI,CAACiC,UAAU,CAAC,EACvD,CAAC,CAENjC,IAAI,CAACkC,UAAU,GAAKlC,IAAI,CAACiC,UAAU,EAAIjC,IAAI,CAACmC,OAAO,eAClDtC,KAAA,SAAAe,QAAA,eACEjB,IAAA,MAAGO,SAAS,CAAC,kBAAkB,CAAI,CAAC,cACzB,cAAAP,IAAA,WAAAiB,QAAA,CAASZ,IAAI,CAACmC,OAAO,CAACJ,IAAI,CAAS,CAAC,EAC3C,CACP,EACE,CAAC,CACH,CAAC,EACH,CAAC,cAGNpC,IAAA,QAAKO,SAAS,CAAC,aAAa,CAAAU,QAAA,CACzBZ,IAAI,CAACoC,WAAW,GAAK,MAAM,cAC1BzC,IAAA,CAACF,YAAY,EACX4C,IAAI,CAAErC,IAAI,CAACsC,SAAU,CACrBC,eAAe,CAAEtC,OAAQ,CACzBC,SAAS,CAAC,cAAc,CACzB,CAAC,cAEFP,IAAA,QACEO,SAAS,CAAC,cAAc,CACxBsC,uBAAuB,CAAE,CAAEC,MAAM,CAAExC,OAAQ,CAAE,CAC7CoB,KAAK,CAAE,CACLqB,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,QAAQ,CAClBC,KAAK,CAAE,MACT,CAAE,CACH,CACF,CACE,CAAC,CAGL,CAAC5C,IAAI,CAAC6C,aAAa,EAAI7C,IAAI,CAACkC,UAAU,GAAKlC,IAAI,CAACiC,UAAU,gBACzDpC,KAAA,QAAKK,SAAS,CAAC,sBAAsB,CAAAU,QAAA,EAClCZ,IAAI,CAAC6C,aAAa,eACjBhD,KAAA,QAAKK,SAAS,CAAC,MAAM,CAAAU,QAAA,eACnBjB,IAAA,OAAIO,SAAS,CAAC,iBAAiB,CAAAU,QAAA,CAAC,OAAK,CAAI,CAAC,cAC1CjB,IAAA,QAAKO,SAAS,CAAC,wBAAwB,CAAAU,QAAA,CACpCZ,IAAI,CAAC6C,aAAa,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAChDtD,IAAA,CAACH,KAAK,EAEJmC,EAAE,CAAC,OAAO,CACVC,IAAI,CAAC,MAAM,CACX1B,SAAS,CAAC,QAAQ,CAAAU,QAAA,CAEjBoC,OAAO,CAACE,IAAI,CAAC,CAAC,EALVD,KAMA,CACR,CAAC,CACC,CAAC,EACH,CACN,CAEAjD,IAAI,CAACkC,UAAU,GAAKlC,IAAI,CAACiC,UAAU,eAClCpC,KAAA,QAAKK,SAAS,CAAC,kBAAkB,CAAAU,QAAA,eAC/BjB,IAAA,MAAGO,SAAS,CAAC,mBAAmB,CAAI,CAAC,iBACvB,CAACC,UAAU,CAACH,IAAI,CAACkC,UAAU,CAAC,EACvC,CACN,EACE,CACN,EACQ,CAAC,EACR,CAAC,CACJ,CAAC,CACH,CAAC,CACG,CAAC,CACT,CAAC,CAEV,CAAC,CAED,cAAe,CAAApC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}