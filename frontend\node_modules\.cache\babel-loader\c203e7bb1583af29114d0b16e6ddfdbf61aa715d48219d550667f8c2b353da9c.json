{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Tabs from '@mui/material/Tabs';\nimport { useTabContext, getTabId, getPanelId } from \"../TabContext/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabList = /*#__PURE__*/React.forwardRef(function TabList(props, ref) {\n  const {\n    children: childrenProp,\n    ...other\n  } = props;\n  const context = useTabContext();\n  if (context === null) {\n    throw new TypeError('No TabContext provided');\n  }\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    return /*#__PURE__*/React.cloneElement(child, {\n      // SOMEDAY: `Tabs` will set those themselves\n      'aria-controls': getPanelId(context, child.props.value),\n      id: getTabId(context, child.props.value)\n    });\n  });\n  return /*#__PURE__*/_jsx(Tabs, {\n    ...other,\n    ref: ref,\n    value: context.value,\n    children: children\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TabList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A list of `<Tab />` elements.\n   */\n  children: PropTypes.node\n} : void 0;\nexport default TabList;", "map": {"version": 3, "names": ["React", "PropTypes", "Tabs", "useTabContext", "getTabId", "getPanelId", "jsx", "_jsx", "TabList", "forwardRef", "props", "ref", "children", "childrenProp", "other", "context", "TypeError", "Children", "map", "child", "isValidElement", "cloneElement", "value", "id", "process", "env", "NODE_ENV", "propTypes", "node"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TabList/TabList.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Tabs from '@mui/material/Tabs';\nimport { useTabContext, getTabId, getPanelId } from \"../TabContext/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabList = /*#__PURE__*/React.forwardRef(function TabList(props, ref) {\n  const {\n    children: childrenProp,\n    ...other\n  } = props;\n  const context = useTabContext();\n  if (context === null) {\n    throw new TypeError('No TabContext provided');\n  }\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    return /*#__PURE__*/React.cloneElement(child, {\n      // SOMEDAY: `Tabs` will set those themselves\n      'aria-controls': getPanelId(context, child.props.value),\n      id: getTabId(context, child.props.value)\n    });\n  });\n  return /*#__PURE__*/_jsx(Tabs, {\n    ...other,\n    ref: ref,\n    value: context.value,\n    children: children\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TabList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A list of `<Tab />` elements.\n   */\n  children: PropTypes.node\n} : void 0;\nexport default TabList;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,wBAAwB;AAC5E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,SAASD,OAAOA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACzE,MAAM;IACJC,QAAQ,EAAEC,YAAY;IACtB,GAAGC;EACL,CAAC,GAAGJ,KAAK;EACT,MAAMK,OAAO,GAAGZ,aAAa,CAAC,CAAC;EAC/B,IAAIY,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC/C;EACA,MAAMJ,QAAQ,GAAGZ,KAAK,CAACiB,QAAQ,CAACC,GAAG,CAACL,YAAY,EAAEM,KAAK,IAAI;IACzD,IAAI,EAAE,aAAanB,KAAK,CAACoB,cAAc,CAACD,KAAK,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;IACA,OAAO,aAAanB,KAAK,CAACqB,YAAY,CAACF,KAAK,EAAE;MAC5C;MACA,eAAe,EAAEd,UAAU,CAACU,OAAO,EAAEI,KAAK,CAACT,KAAK,CAACY,KAAK,CAAC;MACvDC,EAAE,EAAEnB,QAAQ,CAACW,OAAO,EAAEI,KAAK,CAACT,KAAK,CAACY,KAAK;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAaf,IAAI,CAACL,IAAI,EAAE;IAC7B,GAAGY,KAAK;IACRH,GAAG,EAAEA,GAAG;IACRW,KAAK,EAAEP,OAAO,CAACO,KAAK;IACpBV,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlB,OAAO,CAACmB,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;EACEf,QAAQ,EAAEX,SAAS,CAAC2B;AACtB,CAAC,GAAG,KAAK,CAAC;AACV,eAAepB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}