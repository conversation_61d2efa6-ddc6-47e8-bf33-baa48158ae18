{"ast": null, "code": "export { default } from \"./TableContainer.js\";\nexport { default as tableContainerClasses } from \"./tableContainerClasses.js\";\nexport * from \"./tableContainerClasses.js\";", "map": {"version": 3, "names": ["default", "tableContainerClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/TableContainer/index.js"], "sourcesContent": ["export { default } from \"./TableContainer.js\";\nexport { default as tableContainerClasses } from \"./tableContainerClasses.js\";\nexport * from \"./tableContainerClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,4BAA4B;AAC7E,cAAc,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}