{"ast": null, "code": "import { configurator, Sensor, Plugin, CorePlugin, DragDropManager as DragDropManager$1, Draggable as Draggable$1, Droppable as Droppable$1, descriptor } from '@dnd-kit/abstract';\nimport { ScrollDirection, Listeners, isElement, scrollIntoViewIfNeeded, DOMRectangle, getDocument, isHTMLElement, getFrameTransform, scheduler, isTextInput, isKeyboardEvent, isPointerEvent, canScroll, detectScrollIntent, getElementFromPoint, getScrollableAncestors, generateUniqueId, isSafari, Styles, getComputedStyles, parseTranslate, supportsPopover, showPopover, getWindow, supportsStyle, getFrameElement, cloneElement, getFinalKeyframe, animateTransform, ProxiedElements, PositionObserver } from '@dnd-kit/dom/utilities';\nimport { effect, computed, deepEqual, signal, batch, untracked, effects, reactive } from '@dnd-kit/state';\nimport { exceedsDistance, Axes, Point, Rectangle } from '@dnd-kit/geometry';\nimport { defaultCollisionDetection } from '@dnd-kit/collision';\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __knownSymbol = (name, symbol) => (symbol = Symbol[name]) ? symbol : Symbol.for(\"Symbol.\" + name);\nvar __typeError = msg => {\n  throw TypeError(msg);\n};\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __name = (target, value) => __defProp(target, \"name\", {\n  value,\n  configurable: true\n});\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols) for (var prop of __getOwnPropSymbols(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\nvar __decoratorStart = base => {\n  var _a4;\n  return [,,, __create((_a4 = base == null ? void 0 : base[__knownSymbol(\"metadata\")]) != null ? _a4 : null)];\n};\nvar __decoratorStrings = [\"class\", \"method\", \"getter\", \"setter\", \"accessor\", \"field\", \"value\", \"get\", \"set\"];\nvar __expectFn = fn => fn !== void 0 && typeof fn !== \"function\" ? __typeError(\"Function expected\") : fn;\nvar __decoratorContext = (kind, name, done, metadata, fns) => ({\n  kind: __decoratorStrings[kind],\n  name,\n  metadata,\n  addInitializer: fn => done._ ? __typeError(\"Already initialized\") : fns.push(__expectFn(fn || null))\n});\nvar __decoratorMetadata = (array, target) => __defNormalProp(target, __knownSymbol(\"metadata\"), array[3]);\nvar __runInitializers = (array, flags, self, value) => {\n  for (var i = 0, fns = array[flags >> 1], n = fns && fns.length; i < n; i++) flags & 1 ? fns[i].call(self) : value = fns[i].call(self, value);\n  return value;\n};\nvar __decorateElement = (array, flags, name, decorators, target, extra) => {\n  var fn,\n    it,\n    done,\n    ctx,\n    access,\n    k = flags & 7,\n    s = !!(flags & 8),\n    p = !!(flags & 16);\n  var j = k > 3 ? array.length + 1 : k ? s ? 1 : 2 : 0,\n    key = __decoratorStrings[k + 5];\n  var initializers = k > 3 && (array[j - 1] = []),\n    extraInitializers = array[j] || (array[j] = []);\n  var desc = k && (!p && !s && (target = target.prototype), k < 5 && (k > 3 || !p) && __getOwnPropDesc(k < 4 ? target : {\n    get [name]() {\n      return __privateGet(this, extra);\n    },\n    set [name](x) {\n      return __privateSet(this, extra, x);\n    }\n  }, name));\n  k ? p && k < 4 && __name(extra, (k > 2 ? \"set \" : k > 1 ? \"get \" : \"\") + name) : __name(target, name);\n  for (var i = decorators.length - 1; i >= 0; i--) {\n    ctx = __decoratorContext(k, name, done = {}, array[3], extraInitializers);\n    if (k) {\n      ctx.static = s, ctx.private = p, access = ctx.access = {\n        has: p ? x => __privateIn(target, x) : x => name in x\n      };\n      if (k ^ 3) access.get = p ? x => (k ^ 1 ? __privateGet : __privateMethod)(x, target, k ^ 4 ? extra : desc.get) : x => x[name];\n      if (k > 2) access.set = p ? (x, y) => __privateSet(x, target, y, k ^ 4 ? extra : desc.set) : (x, y) => x[name] = y;\n    }\n    it = (0, decorators[i])(k ? k < 4 ? p ? extra : desc[key] : k > 4 ? void 0 : {\n      get: desc.get,\n      set: desc.set\n    } : target, ctx), done._ = 1;\n    if (k ^ 4 || it === void 0) __expectFn(it) && (k > 4 ? initializers.unshift(it) : k ? p ? extra = it : desc[key] = it : target = it);else if (typeof it !== \"object\" || it === null) __typeError(\"Object expected\");else __expectFn(fn = it.get) && (desc.get = fn), __expectFn(fn = it.set) && (desc.set = fn), __expectFn(fn = it.init) && initializers.unshift(fn);\n  }\n  return k || __decoratorMetadata(array, target), desc && __defProp(target, name, desc), p ? k ^ 4 ? extra : desc : target;\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateIn = (member, obj) => Object(obj) !== obj ? __typeError('Cannot use the \"in\" operator on this value') : member.has(obj);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), setter ? setter.call(obj, value) : member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\n\n// src/core/plugins/accessibility/defaults.ts\nvar defaultAttributes = {\n  role: \"button\",\n  roleDescription: \"draggable\"\n};\nvar defaultDescriptionIdPrefix = `dnd-kit-description`;\nvar defaultAnnouncementIdPrefix = `dnd-kit-announcement`;\nvar defaultScreenReaderInstructions = {\n  draggable: `To pick up a draggable item, press the space bar. While dragging, use the arrow keys to move the item in a given direction. Press space again to drop the item in its new position, or press escape to cancel.`\n};\nvar defaultAnnouncements = {\n  dragstart({\n    operation: {\n      source\n    }\n  }) {\n    if (!source) return;\n    return `Picked up draggable item ${source.id}.`;\n  },\n  dragover({\n    operation: {\n      source,\n      target\n    }\n  }) {\n    if (!source || source.id === (target == null ? void 0 : target.id)) return;\n    if (target) {\n      return `Draggable item ${source.id} was moved over droppable target ${target.id}.`;\n    }\n    return `Draggable item ${source.id} is no longer over a droppable target.`;\n  },\n  dragend({\n    operation: {\n      source,\n      target\n    },\n    canceled\n  }) {\n    if (!source) return;\n    if (canceled) {\n      return `Dragging was cancelled. Draggable item ${source.id} was dropped.`;\n    }\n    if (target) {\n      return `Draggable item ${source.id} was dropped over droppable target ${target.id}`;\n    }\n    return `Draggable item ${source.id} was dropped.`;\n  }\n};\n\n// src/core/plugins/accessibility/utilities.ts\nfunction isFocusable(element) {\n  const tagName = element.tagName.toLowerCase();\n  return [\"input\", \"select\", \"textarea\", \"a\", \"button\"].includes(tagName);\n}\n\n// src/core/plugins/accessibility/HiddenText.ts\nfunction createHiddenText(id, value) {\n  const element = document.createElement(\"div\");\n  element.id = id;\n  element.style.setProperty(\"display\", \"none\");\n  element.textContent = value;\n  return element;\n}\n\n// src/core/plugins/accessibility/LiveRegion.ts\nfunction createLiveRegion(id) {\n  const element = document.createElement(\"div\");\n  element.id = id;\n  element.setAttribute(\"role\", \"status\");\n  element.setAttribute(\"aria-live\", \"polite\");\n  element.setAttribute(\"aria-atomic\", \"true\");\n  element.style.setProperty(\"position\", \"fixed\");\n  element.style.setProperty(\"width\", \"1px\");\n  element.style.setProperty(\"height\", \"1px\");\n  element.style.setProperty(\"margin\", \"-1px\");\n  element.style.setProperty(\"border\", \"0\");\n  element.style.setProperty(\"padding\", \"0\");\n  element.style.setProperty(\"overflow\", \"hidden\");\n  element.style.setProperty(\"clip\", \"rect(0 0 0 0)\");\n  element.style.setProperty(\"clip-path\", \"inset(100%)\");\n  element.style.setProperty(\"white-space\", \"nowrap\");\n  return element;\n}\n\n// src/core/plugins/accessibility/Accessibility.ts\nvar debouncedEvents = [\"dragover\", \"dragmove\"];\nvar Accessibility = class extends Plugin {\n  constructor(manager, options) {\n    super(manager);\n    const {\n      id,\n      idPrefix: {\n        description: descriptionPrefix = defaultDescriptionIdPrefix,\n        announcement: announcementPrefix = defaultAnnouncementIdPrefix\n      } = {},\n      announcements = defaultAnnouncements,\n      screenReaderInstructions = defaultScreenReaderInstructions,\n      debounce: debounceMs = 500\n    } = options != null ? options : {};\n    const descriptionId = id ? `${descriptionPrefix}-${id}` : generateUniqueId(descriptionPrefix);\n    const announcementId = id ? `${announcementPrefix}-${id}` : generateUniqueId(announcementPrefix);\n    let hiddenTextElement;\n    let liveRegionElement;\n    let liveRegionTextNode;\n    let latestAnnouncement;\n    const updateAnnouncement = (value = latestAnnouncement) => {\n      if (!liveRegionTextNode || !value) return;\n      if ((liveRegionTextNode == null ? void 0 : liveRegionTextNode.nodeValue) !== value) {\n        liveRegionTextNode.nodeValue = value;\n      }\n    };\n    const scheduleUpdateAnnouncement = () => scheduler.schedule(updateAnnouncement);\n    const debouncedUpdateAnnouncement = debounce(scheduleUpdateAnnouncement, debounceMs);\n    const eventListeners = Object.entries(announcements).map(([eventName, getAnnouncement]) => {\n      return this.manager.monitor.addEventListener(eventName, (event, manager2) => {\n        const element = liveRegionTextNode;\n        if (!element) return;\n        const announcement = getAnnouncement == null ? void 0 : getAnnouncement(event, manager2);\n        if (announcement && element.nodeValue !== announcement) {\n          latestAnnouncement = announcement;\n          if (debouncedEvents.includes(eventName)) {\n            debouncedUpdateAnnouncement();\n          } else {\n            scheduleUpdateAnnouncement();\n            debouncedUpdateAnnouncement.cancel();\n          }\n        }\n      });\n    });\n    const initialize = () => {\n      let elements = [];\n      if (!(hiddenTextElement == null ? void 0 : hiddenTextElement.isConnected)) {\n        hiddenTextElement = createHiddenText(descriptionId, screenReaderInstructions.draggable);\n        elements.push(hiddenTextElement);\n      }\n      if (!(liveRegionElement == null ? void 0 : liveRegionElement.isConnected)) {\n        liveRegionElement = createLiveRegion(announcementId);\n        liveRegionTextNode = document.createTextNode(\"\");\n        liveRegionElement.appendChild(liveRegionTextNode);\n        elements.push(liveRegionElement);\n      }\n      if (elements.length > 0) {\n        document.body.append(...elements);\n      }\n    };\n    const mutations = /* @__PURE__ */new Set();\n    function executeMutations() {\n      for (const operation of mutations) {\n        operation();\n      }\n    }\n    this.registerEffect(() => {\n      var _a4;\n      mutations.clear();\n      for (const draggable of this.manager.registry.draggables.value) {\n        const activator = (_a4 = draggable.handle) != null ? _a4 : draggable.element;\n        if (activator) {\n          if (!hiddenTextElement || !liveRegionElement) {\n            mutations.add(initialize);\n          }\n          if ((!isFocusable(activator) || isSafari()) && !activator.hasAttribute(\"tabindex\")) {\n            mutations.add(() => activator.setAttribute(\"tabindex\", \"0\"));\n          }\n          if (!activator.hasAttribute(\"role\") && !(activator.tagName.toLowerCase() === \"button\")) {\n            mutations.add(() => activator.setAttribute(\"role\", defaultAttributes.role));\n          }\n          if (!activator.hasAttribute(\"aria-roledescription\")) {\n            mutations.add(() => activator.setAttribute(\"aria-roledescription\", defaultAttributes.roleDescription));\n          }\n          if (!activator.hasAttribute(\"aria-describedby\")) {\n            mutations.add(() => activator.setAttribute(\"aria-describedby\", descriptionId));\n          }\n          for (const key of [\"aria-pressed\", \"aria-grabbed\"]) {\n            const value = String(draggable.isDragging);\n            if (activator.getAttribute(key) !== value) {\n              mutations.add(() => activator.setAttribute(key, value));\n            }\n          }\n          const disabled = String(draggable.disabled);\n          if (activator.getAttribute(\"aria-disabled\") !== disabled) {\n            mutations.add(() => activator.setAttribute(\"aria-disabled\", disabled));\n          }\n        }\n      }\n      if (mutations.size > 0) {\n        scheduler.schedule(executeMutations);\n      }\n    });\n    this.destroy = () => {\n      super.destroy();\n      hiddenTextElement == null ? void 0 : hiddenTextElement.remove();\n      liveRegionElement == null ? void 0 : liveRegionElement.remove();\n      eventListeners.forEach(unsubscribe => unsubscribe());\n    };\n  }\n};\nfunction debounce(fn, wait) {\n  let timeout;\n  const debounced = () => {\n    clearTimeout(timeout);\n    timeout = setTimeout(fn, wait);\n  };\n  debounced.cancel = () => clearTimeout(timeout);\n  return debounced;\n}\nvar Cursor = class extends Plugin {\n  constructor(manager, options) {\n    super(manager, options);\n    this.manager = manager;\n    const doc = computed(() => {\n      var _a4;\n      return getDocument((_a4 = this.manager.dragOperation.source) == null ? void 0 : _a4.element);\n    });\n    this.destroy = effect(() => {\n      var _a4;\n      const {\n        dragOperation\n      } = this.manager;\n      const {\n        cursor = \"grabbing\"\n      } = (_a4 = this.options) != null ? _a4 : {};\n      if (dragOperation.status.initialized) {\n        const document2 = doc.value;\n        const style = document2.createElement(\"style\");\n        style.textContent = `* { cursor: ${cursor} !important; }`;\n        document2.head.appendChild(style);\n        return () => style.remove();\n      }\n    });\n  }\n};\n\n// src/core/plugins/feedback/constants.ts\nvar ATTR_PREFIX = \"data-dnd-\";\nvar DROPPING_ATTRIBUTE = `${ATTR_PREFIX}dropping`;\nvar CSS_PREFIX = \"--dnd-\";\nvar ATTRIBUTE = `${ATTR_PREFIX}dragging`;\nvar PLACEHOLDER_ATTRIBUTE = `${ATTR_PREFIX}placeholder`;\nvar IGNORED_ATTRIBUTES = [ATTRIBUTE, PLACEHOLDER_ATTRIBUTE, \"popover\", \"aria-pressed\", \"aria-grabbing\"];\nvar IGNORED_STYLES = [\"view-transition-name\"];\nvar CSS_RULES = `\n  :root [${ATTRIBUTE}] {\n    position: fixed !important;\n    pointer-events: none !important;\n    touch-action: none;\n    z-index: calc(infinity);\n    will-change: translate;\n    top: var(${CSS_PREFIX}top, 0px) !important;\n    left: var(${CSS_PREFIX}left, 0px) !important;\n    right: unset !important;\n    bottom: unset !important;\n    width: var(${CSS_PREFIX}width, auto);\n    max-width: var(${CSS_PREFIX}width, auto);\n    height: var(${CSS_PREFIX}height, auto);\n    max-height: var(${CSS_PREFIX}height, auto);\n    transition: var(${CSS_PREFIX}transition) !important;\n  }\n\n  :root [${PLACEHOLDER_ATTRIBUTE}] {\n    transition: none;\n  }\n\n  :root [${PLACEHOLDER_ATTRIBUTE}='hidden'] {\n    visibility: hidden;\n  }\n\n  [${ATTRIBUTE}] * {\n    pointer-events: none !important;\n  }\n\n  [${ATTRIBUTE}]:not([${DROPPING_ATTRIBUTE}]) {\n    translate: var(${CSS_PREFIX}translate) !important;\n  }\n\n  [${ATTRIBUTE}][style*='${CSS_PREFIX}scale'] {\n    scale: var(${CSS_PREFIX}scale) !important;\n    transform-origin: var(${CSS_PREFIX}transform-origin) !important;\n  }\n\n  @layer {\n    :where([${ATTRIBUTE}][popover]) {\n      overflow: visible;\n      background: unset;\n      border: unset;\n      margin: unset;\n      padding: unset;\n      color: inherit;\n\n      &:is(input, button) {\n        border: revert;\n        background: revert;\n      }\n    }\n  }\n  [${ATTRIBUTE}]::backdrop, [${ATTR_PREFIX}overlay]:not([${ATTRIBUTE}]) {\n    display: none;\n    visibility: hidden;\n  }\n`.replace(/\\n+/g, \" \").replace(/\\s+/g, \" \").trim();\nfunction createPlaceholder(source, type = \"hidden\") {\n  return untracked(() => {\n    const {\n      element,\n      manager\n    } = source;\n    if (!element || !manager) return;\n    const containedDroppables = findContainedDroppables(element, manager.registry.droppables);\n    const cleanup = [];\n    const placeholder = cloneElement(element);\n    const {\n      remove\n    } = placeholder;\n    proxyDroppableElements(containedDroppables, placeholder, cleanup);\n    configurePlaceholder(placeholder, type);\n    placeholder.remove = () => {\n      cleanup.forEach(fn => fn());\n      remove.call(placeholder);\n    };\n    return placeholder;\n  });\n}\nfunction findContainedDroppables(element, droppables) {\n  const containedDroppables = /* @__PURE__ */new Map();\n  for (const droppable of droppables) {\n    if (!droppable.element) continue;\n    if (element === droppable.element || element.contains(droppable.element)) {\n      const identifierAttribute = `${ATTR_PREFIX}${generateUniqueId(\"dom-id\")}`;\n      droppable.element.setAttribute(identifierAttribute, \"\");\n      containedDroppables.set(droppable, identifierAttribute);\n    }\n  }\n  return containedDroppables;\n}\nfunction proxyDroppableElements(containedDroppables, placeholder, cleanup) {\n  for (const [droppable, identifierAttribute] of containedDroppables) {\n    if (!droppable.element) continue;\n    const selector = `[${identifierAttribute}]`;\n    const clonedElement = placeholder.matches(selector) ? placeholder : placeholder.querySelector(selector);\n    droppable.element.removeAttribute(identifierAttribute);\n    if (!clonedElement) continue;\n    const originalElement = droppable.element;\n    droppable.proxy = clonedElement;\n    clonedElement.removeAttribute(identifierAttribute);\n    ProxiedElements.set(originalElement, clonedElement);\n    cleanup.push(() => {\n      ProxiedElements.delete(originalElement);\n      droppable.proxy = void 0;\n    });\n  }\n}\nfunction configurePlaceholder(placeholder, type = \"hidden\") {\n  placeholder.setAttribute(\"inert\", \"true\");\n  placeholder.setAttribute(\"tab-index\", \"-1\");\n  placeholder.setAttribute(\"aria-hidden\", \"true\");\n  placeholder.setAttribute(PLACEHOLDER_ATTRIBUTE, type);\n}\nfunction isSameFrame(element, target) {\n  if (element === target) return true;\n  return getFrameElement(element) === getFrameElement(target);\n}\nfunction preventPopoverClose(event) {\n  const {\n    target\n  } = event;\n  if (\"newState\" in event && event.newState === \"closed\" && isElement(target) && target.hasAttribute(\"popover\")) {\n    requestAnimationFrame(() => showPopover(target));\n  }\n}\nfunction isTableRow(element) {\n  return element.tagName === \"TR\";\n}\n\n// src/core/plugins/feedback/Feedback.ts\nvar styleSheetRegistry = /* @__PURE__ */new Map();\nvar _overlay_dec, _a, _init, _overlay, _Feedback_instances, render_fn, injectStyles_fn;\nvar _Feedback = class _Feedback extends (_a = Plugin, _overlay_dec = [reactive], _a) {\n  constructor(manager, options) {\n    super(manager, options);\n    __privateAdd(this, _Feedback_instances);\n    __privateAdd(this, _overlay, __runInitializers(_init, 8, this)), __runInitializers(_init, 11, this);\n    this.state = {\n      initial: {},\n      current: {}\n    };\n    this.registerEffect(__privateMethod(this, _Feedback_instances, injectStyles_fn));\n    this.registerEffect(__privateMethod(this, _Feedback_instances, render_fn));\n  }\n  destroy() {\n    super.destroy();\n    for (const [doc, registration] of styleSheetRegistry.entries()) {\n      if (registration.instances.has(this)) {\n        registration.instances.delete(this);\n        if (registration.instances.size === 0) {\n          registration.cleanup();\n          styleSheetRegistry.delete(doc);\n        }\n      }\n    }\n  }\n};\n_init = __decoratorStart(_a);\n_overlay = new WeakMap();\n_Feedback_instances = new WeakSet();\nrender_fn = function () {\n  var _a4, _b2, _c3;\n  const {\n    state,\n    manager,\n    options\n  } = this;\n  const {\n    dragOperation\n  } = manager;\n  const {\n    position,\n    source,\n    status\n  } = dragOperation;\n  if (status.idle) {\n    state.current = {};\n    state.initial = {};\n    return;\n  }\n  if (!source) return;\n  const {\n    element,\n    feedback\n  } = source;\n  if (!element || feedback === \"none\" || !status.initialized || status.initializing) {\n    return;\n  }\n  const {\n    initial\n  } = state;\n  const feedbackElement = (_a4 = this.overlay) != null ? _a4 : element;\n  const frameTransform = getFrameTransform(feedbackElement);\n  const elementFrameTransform = getFrameTransform(element);\n  const crossFrame = !isSameFrame(element, feedbackElement);\n  const shape = new DOMRectangle(element, {\n    frameTransform: crossFrame ? elementFrameTransform : null,\n    ignoreTransforms: !crossFrame\n  });\n  const scaleDelta = {\n    x: elementFrameTransform.scaleX / frameTransform.scaleX,\n    y: elementFrameTransform.scaleY / frameTransform.scaleY\n  };\n  let {\n    width,\n    height,\n    top,\n    left\n  } = shape;\n  if (crossFrame) {\n    width = width / scaleDelta.x;\n    height = height / scaleDelta.y;\n  }\n  let elementMutationObserver;\n  let documentMutationObserver;\n  const styles = new Styles(feedbackElement);\n  const {\n    transition,\n    translate,\n    boxSizing,\n    paddingBlockStart,\n    paddingBlockEnd,\n    paddingInlineStart,\n    paddingInlineEnd,\n    borderInlineStartWidth,\n    borderInlineEndWidth,\n    borderBlockStartWidth,\n    borderBlockEndWidth\n  } = getComputedStyles(element);\n  const clone = feedback === \"clone\";\n  const contentBox = boxSizing === \"content-box\";\n  const widthOffset = contentBox ? parseInt(paddingInlineStart) + parseInt(paddingInlineEnd) + parseInt(borderInlineStartWidth) + parseInt(borderInlineEndWidth) : 0;\n  const heightOffset = contentBox ? parseInt(paddingBlockStart) + parseInt(paddingBlockEnd) + parseInt(borderBlockStartWidth) + parseInt(borderBlockEndWidth) : 0;\n  const placeholder = feedback !== \"move\" && !this.overlay ? createPlaceholder(source, clone ? \"clone\" : \"hidden\") : null;\n  const isKeyboardOperation = untracked(() => isKeyboardEvent(manager.dragOperation.activatorEvent));\n  if (translate !== \"none\") {\n    const parsedTranslate = parseTranslate(translate);\n    if (parsedTranslate && !initial.translate) {\n      initial.translate = parsedTranslate;\n    }\n  }\n  if (!initial.transformOrigin) {\n    const current = untracked(() => position.current);\n    initial.transformOrigin = {\n      x: (current.x - left * frameTransform.scaleX - frameTransform.x) / (width * frameTransform.scaleX),\n      y: (current.y - top * frameTransform.scaleY - frameTransform.y) / (height * frameTransform.scaleY)\n    };\n  }\n  const {\n    transformOrigin\n  } = initial;\n  const relativeTop = top * frameTransform.scaleY + frameTransform.y;\n  const relativeLeft = left * frameTransform.scaleX + frameTransform.x;\n  if (!initial.coordinates) {\n    initial.coordinates = {\n      x: relativeLeft,\n      y: relativeTop\n    };\n    if (scaleDelta.x !== 1 || scaleDelta.y !== 1) {\n      const {\n        scaleX,\n        scaleY\n      } = elementFrameTransform;\n      const {\n        x: tX2,\n        y: tY2\n      } = transformOrigin;\n      initial.coordinates.x += (width * scaleX - width) * tX2;\n      initial.coordinates.y += (height * scaleY - height) * tY2;\n    }\n  }\n  if (!initial.dimensions) {\n    initial.dimensions = {\n      width,\n      height\n    };\n  }\n  if (!initial.frameTransform) {\n    initial.frameTransform = frameTransform;\n  }\n  const coordinatesDelta = {\n    x: initial.coordinates.x - relativeLeft,\n    y: initial.coordinates.y - relativeTop\n  };\n  const sizeDelta = {\n    width: (initial.dimensions.width * initial.frameTransform.scaleX - width * frameTransform.scaleX) * transformOrigin.x,\n    height: (initial.dimensions.height * initial.frameTransform.scaleY - height * frameTransform.scaleY) * transformOrigin.y\n  };\n  const delta = {\n    x: coordinatesDelta.x / frameTransform.scaleX + sizeDelta.width,\n    y: coordinatesDelta.y / frameTransform.scaleY + sizeDelta.height\n  };\n  const projected = {\n    left: left + delta.x,\n    top: top + delta.y\n  };\n  feedbackElement.setAttribute(ATTRIBUTE, \"true\");\n  const transform = untracked(() => dragOperation.transform);\n  const initialTranslate = (_b2 = initial.translate) != null ? _b2 : {\n    x: 0,\n    y: 0\n  };\n  const tX = transform.x * frameTransform.scaleX + initialTranslate.x;\n  const tY = transform.y * frameTransform.scaleY + initialTranslate.y;\n  const translateString = `${tX}px ${tY}px 0`;\n  const transitionString = transition ? `${transition}, translate 0ms linear` : \"\";\n  styles.set({\n    width: width - widthOffset,\n    height: height - heightOffset,\n    top: projected.top,\n    left: projected.left,\n    translate: translateString,\n    transition: transitionString,\n    scale: crossFrame ? `${scaleDelta.x} ${scaleDelta.y}` : \"\",\n    \"transform-origin\": `${transformOrigin.x * 100}% ${transformOrigin.y * 100}%`\n  }, CSS_PREFIX);\n  if (placeholder) {\n    element.insertAdjacentElement(\"afterend\", placeholder);\n    if (options == null ? void 0 : options.rootElement) {\n      const root = typeof options.rootElement === \"function\" ? options.rootElement(source) : options.rootElement;\n      root.appendChild(element);\n    }\n  }\n  if (supportsPopover(feedbackElement)) {\n    if (!feedbackElement.hasAttribute(\"popover\")) {\n      feedbackElement.setAttribute(\"popover\", \"manual\");\n    }\n    showPopover(feedbackElement);\n    feedbackElement.addEventListener(\"beforetoggle\", preventPopoverClose);\n  }\n  const resizeObserver = new ResizeObserver(() => {\n    if (!placeholder) return;\n    const placeholderShape = new DOMRectangle(placeholder, {\n      frameTransform,\n      ignoreTransforms: true\n    });\n    const origin = transformOrigin != null ? transformOrigin : {\n      x: 1,\n      y: 1\n    };\n    const dX = (width - placeholderShape.width) * origin.x + delta.x;\n    const dY = (height - placeholderShape.height) * origin.y + delta.y;\n    styles.set({\n      width: placeholderShape.width - widthOffset,\n      height: placeholderShape.height - heightOffset,\n      top: top + dY,\n      left: left + dX\n    }, CSS_PREFIX);\n    elementMutationObserver == null ? void 0 : elementMutationObserver.takeRecords();\n    if (isTableRow(element) && isTableRow(placeholder)) {\n      const cells = Array.from(element.cells);\n      const placeholderCells = Array.from(placeholder.cells);\n      for (const [index, cell] of cells.entries()) {\n        const placeholderCell = placeholderCells[index];\n        cell.style.width = `${placeholderCell.offsetWidth}px`;\n      }\n    }\n    dragOperation.shape = new DOMRectangle(feedbackElement);\n  });\n  const initialShape = new DOMRectangle(feedbackElement);\n  untracked(() => dragOperation.shape = initialShape);\n  const feedbackWindow = getWindow(feedbackElement);\n  const handleWindowResize = event => {\n    this.manager.actions.stop({\n      event\n    });\n  };\n  if (isKeyboardOperation) {\n    feedbackWindow.addEventListener(\"resize\", handleWindowResize);\n  }\n  if (untracked(() => source.status) === \"idle\") {\n    requestAnimationFrame(() => source.status = \"dragging\");\n  }\n  if (placeholder) {\n    resizeObserver.observe(placeholder);\n    elementMutationObserver = new MutationObserver(mutations => {\n      let hasChildrenMutations = false;\n      for (const mutation of mutations) {\n        if (mutation.target !== element) {\n          hasChildrenMutations = true;\n          continue;\n        }\n        if (mutation.type !== \"attributes\") {\n          continue;\n        }\n        const attributeName = mutation.attributeName;\n        if (attributeName.startsWith(\"aria-\") || IGNORED_ATTRIBUTES.includes(attributeName)) {\n          continue;\n        }\n        const attributeValue = element.getAttribute(attributeName);\n        if (attributeName === \"style\") {\n          if (supportsStyle(element) && supportsStyle(placeholder)) {\n            const styles2 = element.style;\n            for (const key of Array.from(placeholder.style)) {\n              if (styles2.getPropertyValue(key) === \"\") {\n                placeholder.style.removeProperty(key);\n              }\n            }\n            for (const key of Array.from(styles2)) {\n              if (IGNORED_STYLES.includes(key) || key.startsWith(CSS_PREFIX)) {\n                continue;\n              }\n              const value = styles2.getPropertyValue(key);\n              placeholder.style.setProperty(key, value);\n            }\n          }\n        } else if (attributeValue !== null) {\n          placeholder.setAttribute(attributeName, attributeValue);\n        } else {\n          placeholder.removeAttribute(attributeName);\n        }\n      }\n      if (hasChildrenMutations && clone) {\n        placeholder.innerHTML = element.innerHTML;\n      }\n    });\n    elementMutationObserver.observe(element, {\n      attributes: true,\n      subtree: true,\n      childList: true\n    });\n    documentMutationObserver = new MutationObserver(entries => {\n      for (const entry of entries) {\n        if (entry.addedNodes.length === 0) continue;\n        for (const node of Array.from(entry.addedNodes)) {\n          if (node.contains(element) && element.nextElementSibling !== placeholder) {\n            element.insertAdjacentElement(\"afterend\", placeholder);\n            showPopover(feedbackElement);\n            return;\n          }\n          if (node.contains(placeholder) && placeholder.previousElementSibling !== element) {\n            placeholder.insertAdjacentElement(\"beforebegin\", element);\n            showPopover(feedbackElement);\n            return;\n          }\n        }\n      }\n    });\n    documentMutationObserver.observe(element.ownerDocument.body, {\n      childList: true,\n      subtree: true\n    });\n  }\n  const id = (_c3 = manager.dragOperation.source) == null ? void 0 : _c3.id;\n  const restoreFocus = () => {\n    var _a5;\n    if (!isKeyboardOperation || id == null) {\n      return;\n    }\n    const draggable = manager.registry.draggables.get(id);\n    const element2 = (_a5 = draggable == null ? void 0 : draggable.handle) != null ? _a5 : draggable == null ? void 0 : draggable.element;\n    if (isHTMLElement(element2)) {\n      element2.focus();\n    }\n  };\n  const cleanup = () => {\n    elementMutationObserver == null ? void 0 : elementMutationObserver.disconnect();\n    documentMutationObserver == null ? void 0 : documentMutationObserver.disconnect();\n    resizeObserver.disconnect();\n    feedbackWindow.removeEventListener(\"resize\", handleWindowResize);\n    if (supportsPopover(feedbackElement)) {\n      feedbackElement.removeEventListener(\"beforetoggle\", preventPopoverClose);\n      feedbackElement.removeAttribute(\"popover\");\n    }\n    feedbackElement.removeAttribute(ATTRIBUTE);\n    styles.reset();\n    source.status = \"idle\";\n    const moved = state.current.translate != null;\n    if (placeholder && (moved || placeholder.parentElement !== feedbackElement.parentElement) && feedbackElement.isConnected) {\n      placeholder.replaceWith(feedbackElement);\n    }\n    placeholder == null ? void 0 : placeholder.remove();\n  };\n  const cleanupEffects = effects(\n  // Update transform on move\n  () => {\n    var _a5;\n    const {\n      transform: transform2,\n      status: status2\n    } = dragOperation;\n    if (!transform2.x && !transform2.y && !state.current.translate) {\n      return;\n    }\n    if (status2.dragging) {\n      const initialTranslate2 = (_a5 = initial.translate) != null ? _a5 : {\n        x: 0,\n        y: 0\n      };\n      const translate2 = {\n        x: transform2.x / frameTransform.scaleX + initialTranslate2.x,\n        y: transform2.y / frameTransform.scaleY + initialTranslate2.y\n      };\n      const previousTranslate = state.current.translate;\n      const modifiers = untracked(() => dragOperation.modifiers);\n      const currentShape = untracked(() => {\n        var _a6;\n        return (_a6 = dragOperation.shape) == null ? void 0 : _a6.current;\n      });\n      const translateTransition = isKeyboardOperation ? \"250ms cubic-bezier(0.25, 1, 0.5, 1)\" : \"0ms linear\";\n      styles.set({\n        transition: `${transition}, translate ${translateTransition}`,\n        translate: `${translate2.x}px ${translate2.y}px 0`\n      }, CSS_PREFIX);\n      elementMutationObserver == null ? void 0 : elementMutationObserver.takeRecords();\n      if (currentShape && currentShape !== initialShape && previousTranslate && !modifiers.length) {\n        const delta2 = Point.delta(translate2, previousTranslate);\n        dragOperation.shape = Rectangle.from(currentShape.boundingRectangle).translate(\n        // Need to take into account frame transform when optimistically updating shape\n        delta2.x * frameTransform.scaleX, delta2.y * frameTransform.scaleY);\n      } else {\n        dragOperation.shape = new DOMRectangle(feedbackElement);\n      }\n      state.current.translate = translate2;\n    }\n  },\n  // Drop animation\n  function () {\n    if (dragOperation.status.dropped) {\n      this.dispose();\n      source.status = \"dropping\";\n      let translate2 = state.current.translate;\n      const moved = translate2 != null;\n      if (!translate2 && element !== feedbackElement) {\n        translate2 = {\n          x: 0,\n          y: 0\n        };\n      }\n      if (!translate2) {\n        cleanup();\n        return;\n      }\n      const dropAnimation = () => {\n        var _a5, _b3;\n        {\n          showPopover(feedbackElement);\n          const [, animation] = (_a5 = getFinalKeyframe(feedbackElement, keyframe => \"translate\" in keyframe)) != null ? _a5 : [];\n          animation == null ? void 0 : animation.pause();\n          const target = placeholder != null ? placeholder : element;\n          const options2 = {\n            frameTransform: isSameFrame(feedbackElement, target) ? null : void 0\n          };\n          const current = new DOMRectangle(feedbackElement, options2);\n          const currentTranslate = (_b3 = parseTranslate(getComputedStyles(feedbackElement).translate)) != null ? _b3 : translate2;\n          const final = new DOMRectangle(target, options2);\n          const delta2 = Rectangle.delta(current, final, source.alignment);\n          const finalTranslate = {\n            x: currentTranslate.x - delta2.x,\n            y: currentTranslate.y - delta2.y\n          };\n          const heightKeyframes = Math.round(current.intrinsicHeight) !== Math.round(final.intrinsicHeight) ? {\n            minHeight: [`${current.intrinsicHeight}px`, `${final.intrinsicHeight}px`],\n            maxHeight: [`${current.intrinsicHeight}px`, `${final.intrinsicHeight}px`]\n          } : {};\n          const widthKeyframes = Math.round(current.intrinsicWidth) !== Math.round(final.intrinsicWidth) ? {\n            minWidth: [`${current.intrinsicWidth}px`, `${final.intrinsicWidth}px`],\n            maxWidth: [`${current.intrinsicWidth}px`, `${final.intrinsicWidth}px`]\n          } : {};\n          styles.set({\n            transition\n          }, CSS_PREFIX);\n          feedbackElement.setAttribute(DROPPING_ATTRIBUTE, \"\");\n          elementMutationObserver == null ? void 0 : elementMutationObserver.takeRecords();\n          animateTransform({\n            element: feedbackElement,\n            keyframes: __spreadProps(__spreadValues(__spreadValues({}, heightKeyframes), widthKeyframes), {\n              translate: [`${currentTranslate.x}px ${currentTranslate.y}px 0`, `${finalTranslate.x}px ${finalTranslate.y}px 0`]\n            }),\n            options: {\n              duration: moved || feedbackElement !== element ? 250 : 0,\n              easing: \"ease\"\n            }\n          }).then(() => {\n            feedbackElement.removeAttribute(DROPPING_ATTRIBUTE);\n            animation == null ? void 0 : animation.finish();\n            cleanup();\n            requestAnimationFrame(restoreFocus);\n          });\n        }\n      };\n      manager.renderer.rendering.then(dropAnimation);\n    }\n  });\n  return () => {\n    cleanup();\n    cleanupEffects();\n  };\n};\ninjectStyles_fn = function () {\n  var _a4, _b2;\n  const {\n    status,\n    source,\n    target\n  } = this.manager.dragOperation;\n  if (status.initializing) {\n    const sourceDocument = getDocument((_a4 = source == null ? void 0 : source.element) != null ? _a4 : null);\n    const targetDocument = getDocument((_b2 = target == null ? void 0 : target.element) != null ? _b2 : null);\n    const documents = /* @__PURE__ */new Set([sourceDocument, targetDocument]);\n    for (const doc of documents) {\n      let registration = styleSheetRegistry.get(doc);\n      if (!registration) {\n        const style = document.createElement(\"style\");\n        style.textContent = CSS_RULES;\n        doc.head.prepend(style);\n        const mutationObserver = new MutationObserver(entries => {\n          for (const entry of entries) {\n            if (entry.type === \"childList\") {\n              const removedNodes = Array.from(entry.removedNodes);\n              if (removedNodes.length > 0 && removedNodes.includes(style)) {\n                doc.head.prepend(style);\n              }\n            }\n          }\n        });\n        mutationObserver.observe(doc.head, {\n          childList: true\n        });\n        registration = {\n          cleanup: () => {\n            mutationObserver.disconnect();\n            style.remove();\n          },\n          instances: /* @__PURE__ */new Set()\n        };\n        styleSheetRegistry.set(doc, registration);\n      }\n      registration.instances.add(this);\n    }\n  }\n};\n__decorateElement(_init, 4, \"overlay\", _overlay_dec, _Feedback, _overlay);\n__decoratorMetadata(_init, _Feedback);\n_Feedback.configure = configurator(_Feedback);\nvar Feedback = _Feedback;\nvar LOCKED = true;\nvar UNLOCKED = false;\nvar _dec, _a2, _dec2, _b, _init2, __b, __a;\n_b = (_dec2 = [reactive], ScrollDirection.Forward), _a2 = (_dec = [reactive], ScrollDirection.Reverse);\nvar ScrollLock = class {\n  constructor() {\n    __privateAdd(this, __b, __runInitializers(_init2, 8, this, LOCKED)), __runInitializers(_init2, 11, this);\n    __privateAdd(this, __a, __runInitializers(_init2, 12, this, LOCKED)), __runInitializers(_init2, 15, this);\n  }\n  isLocked(direction) {\n    if (direction === ScrollDirection.Idle) {\n      return false;\n    }\n    if (direction == null) {\n      return this[ScrollDirection.Forward] === LOCKED && this[ScrollDirection.Reverse] === LOCKED;\n    }\n    return this[direction] === LOCKED;\n  }\n  unlock(direction) {\n    if (direction === ScrollDirection.Idle) {\n      return;\n    }\n    this[direction] = UNLOCKED;\n  }\n};\n_init2 = __decoratorStart(null);\n__b = new WeakMap();\n__a = new WeakMap();\n__decorateElement(_init2, 4, _b, _dec2, ScrollLock, __b);\n__decorateElement(_init2, 4, _a2, _dec, ScrollLock, __a);\n__decoratorMetadata(_init2, ScrollLock);\n\n// src/core/plugins/scrolling/ScrollIntent.ts\nvar DIRECTIONS = [ScrollDirection.Forward, ScrollDirection.Reverse];\nvar ScrollIntent = class {\n  constructor() {\n    this.x = new ScrollLock();\n    this.y = new ScrollLock();\n  }\n  isLocked() {\n    return this.x.isLocked() && this.y.isLocked();\n  }\n};\nvar ScrollIntentTracker = class extends Plugin {\n  constructor(manager) {\n    super(manager);\n    const scrollIntent = signal(new ScrollIntent());\n    let previousDelta = null;\n    this.signal = scrollIntent;\n    effect(() => {\n      const {\n        status\n      } = manager.dragOperation;\n      if (!status.initialized) {\n        previousDelta = null;\n        scrollIntent.value = new ScrollIntent();\n        return;\n      }\n      const {\n        delta\n      } = manager.dragOperation.position;\n      if (previousDelta) {\n        const directions = {\n          x: getDirection(delta.x, previousDelta.x),\n          y: getDirection(delta.y, previousDelta.y)\n        };\n        const intent = scrollIntent.peek();\n        batch(() => {\n          for (const axis of Axes) {\n            for (const direction of DIRECTIONS) {\n              if (directions[axis] === direction) {\n                intent[axis].unlock(direction);\n              }\n            }\n          }\n          scrollIntent.value = intent;\n        });\n      }\n      previousDelta = delta;\n    });\n  }\n  get current() {\n    return this.signal.peek();\n  }\n};\nfunction getDirection(a, b) {\n  return Math.sign(a - b);\n}\n\n// src/core/plugins/scrolling/Scroller.ts\nvar _autoScrolling_dec, _a3, _init3, _autoScrolling, _meta, _scroll;\nvar Scroller = class extends (_a3 = CorePlugin, _autoScrolling_dec = [reactive], _a3) {\n  constructor(manager) {\n    super(manager);\n    __privateAdd(this, _autoScrolling, __runInitializers(_init3, 8, this, false)), __runInitializers(_init3, 11, this);\n    __privateAdd(this, _meta);\n    __privateAdd(this, _scroll, () => {\n      if (!__privateGet(this, _meta)) {\n        return;\n      }\n      const {\n        element,\n        by\n      } = __privateGet(this, _meta);\n      if (by.y) element.scrollTop += by.y;\n      if (by.x) element.scrollLeft += by.x;\n    });\n    this.scroll = options => {\n      var _a4;\n      if (this.disabled) {\n        return false;\n      }\n      const elements = this.getScrollableElements();\n      if (!elements) {\n        __privateSet(this, _meta, void 0);\n        return false;\n      }\n      const {\n        position\n      } = this.manager.dragOperation;\n      const currentPosition = position == null ? void 0 : position.current;\n      if (currentPosition) {\n        const {\n          by\n        } = options != null ? options : {};\n        const intent = by ? {\n          x: getScrollIntent(by.x),\n          y: getScrollIntent(by.y)\n        } : void 0;\n        const scrollIntent = intent ? void 0 : this.scrollIntentTracker.current;\n        if (scrollIntent == null ? void 0 : scrollIntent.isLocked()) {\n          return false;\n        }\n        for (const scrollableElement of elements) {\n          const elementCanScroll = canScroll(scrollableElement, by);\n          if (elementCanScroll.x || elementCanScroll.y) {\n            const {\n              speed,\n              direction\n            } = detectScrollIntent(scrollableElement, currentPosition, intent);\n            if (scrollIntent) {\n              for (const axis of Axes) {\n                if (scrollIntent[axis].isLocked(direction[axis])) {\n                  speed[axis] = 0;\n                  direction[axis] = 0;\n                }\n              }\n            }\n            if (direction.x || direction.y) {\n              const {\n                x,\n                y\n              } = by != null ? by : direction;\n              const scrollLeftBy = x * speed.x;\n              const scrollTopBy = y * speed.y;\n              if (scrollLeftBy || scrollTopBy) {\n                const previousScrollBy = (_a4 = __privateGet(this, _meta)) == null ? void 0 : _a4.by;\n                if (this.autoScrolling && previousScrollBy) {\n                  const scrollIntentMismatch = previousScrollBy.x && !scrollLeftBy || previousScrollBy.y && !scrollTopBy;\n                  if (scrollIntentMismatch) continue;\n                }\n                __privateSet(this, _meta, {\n                  element: scrollableElement,\n                  by: {\n                    x: scrollLeftBy,\n                    y: scrollTopBy\n                  }\n                });\n                scheduler.schedule(__privateGet(this, _scroll));\n                return true;\n              }\n            }\n          }\n        }\n      }\n      __privateSet(this, _meta, void 0);\n      return false;\n    };\n    let previousElementFromPoint = null;\n    let previousScrollableElements = null;\n    const elementFromPoint = computed(() => {\n      const {\n        position,\n        source\n      } = manager.dragOperation;\n      if (!position) {\n        return null;\n      }\n      const element = getElementFromPoint(getDocument(source == null ? void 0 : source.element), position.current);\n      if (element) {\n        previousElementFromPoint = element;\n      }\n      return element != null ? element : previousElementFromPoint;\n    });\n    const scrollableElements = computed(() => {\n      const element = elementFromPoint.value;\n      const {\n        documentElement\n      } = getDocument(element);\n      if (!element || element === documentElement) {\n        const {\n          target\n        } = manager.dragOperation;\n        const targetElement = target == null ? void 0 : target.element;\n        if (targetElement) {\n          const elements = getScrollableAncestors(targetElement, {\n            excludeElement: false\n          });\n          previousScrollableElements = elements;\n          return elements;\n        }\n      }\n      if (element) {\n        const elements = getScrollableAncestors(element, {\n          excludeElement: false\n        });\n        if (this.autoScrolling && previousScrollableElements && elements.size < (previousScrollableElements == null ? void 0 : previousScrollableElements.size)) {\n          return previousScrollableElements;\n        }\n        previousScrollableElements = elements;\n        return elements;\n      }\n      previousScrollableElements = null;\n      return null;\n    }, deepEqual);\n    this.getScrollableElements = () => {\n      return scrollableElements.value;\n    };\n    this.scrollIntentTracker = new ScrollIntentTracker(manager);\n    this.destroy = manager.monitor.addEventListener(\"dragmove\", event => {\n      if (this.disabled || event.defaultPrevented || !isKeyboardEvent(manager.dragOperation.activatorEvent) || !event.by) {\n        return;\n      }\n      if (this.scroll({\n        by: event.by\n      })) {\n        event.preventDefault();\n      }\n    });\n  }\n};\n_init3 = __decoratorStart(_a3);\n_autoScrolling = new WeakMap();\n_meta = new WeakMap();\n_scroll = new WeakMap();\n__decorateElement(_init3, 4, \"autoScrolling\", _autoScrolling_dec, Scroller, _autoScrolling);\n__decoratorMetadata(_init3, Scroller);\nfunction getScrollIntent(value) {\n  if (value > 0) {\n    return ScrollDirection.Forward;\n  }\n  if (value < 0) {\n    return ScrollDirection.Reverse;\n  }\n  return ScrollDirection.Idle;\n}\n\n// src/utilities/scheduling/scheduler.ts\nvar Scheduler = class {\n  constructor(scheduler5) {\n    this.scheduler = scheduler5;\n    this.pending = false;\n    this.tasks = /* @__PURE__ */new Set();\n    this.resolvers = /* @__PURE__ */new Set();\n    this.flush = () => {\n      const {\n        tasks,\n        resolvers\n      } = this;\n      this.pending = false;\n      this.tasks = /* @__PURE__ */new Set();\n      this.resolvers = /* @__PURE__ */new Set();\n      for (const task of tasks) {\n        task();\n      }\n      for (const resolve of resolvers) {\n        resolve();\n      }\n    };\n  }\n  schedule(task) {\n    this.tasks.add(task);\n    if (!this.pending) {\n      this.pending = true;\n      this.scheduler(this.flush);\n    }\n    return new Promise(resolve => this.resolvers.add(resolve));\n  }\n};\nvar scheduler3 = new Scheduler(callback => {\n  if (typeof requestAnimationFrame === \"function\") {\n    requestAnimationFrame(callback);\n  } else {\n    callback();\n  }\n});\n\n// src/core/plugins/scrolling/AutoScroller.ts\nvar AUTOSCROLL_INTERVAL = 10;\nvar AutoScroller = class extends Plugin {\n  constructor(manager, _options) {\n    super(manager);\n    const scroller = manager.registry.plugins.get(Scroller);\n    if (!scroller) {\n      throw new Error(\"AutoScroller plugin depends on Scroller plugin\");\n    }\n    this.destroy = effect(() => {\n      if (this.disabled) {\n        return;\n      }\n      const {\n        position: _,\n        status\n      } = manager.dragOperation;\n      if (status.dragging) {\n        const canScroll2 = scroller.scroll();\n        if (canScroll2) {\n          scroller.autoScrolling = true;\n          const interval = setInterval(() => scheduler3.schedule(scroller.scroll), AUTOSCROLL_INTERVAL);\n          return () => {\n            clearInterval(interval);\n          };\n        } else {\n          scroller.autoScrolling = false;\n        }\n      }\n    });\n  }\n};\nvar listenerOptions = {\n  capture: true,\n  passive: true\n};\nvar _timeout;\nvar ScrollListener = class extends CorePlugin {\n  constructor(manager) {\n    super(manager);\n    __privateAdd(this, _timeout);\n    this.handleScroll = () => {\n      if (__privateGet(this, _timeout) == null) {\n        __privateSet(this, _timeout, setTimeout(() => {\n          this.manager.collisionObserver.forceUpdate(false);\n          __privateSet(this, _timeout, void 0);\n        }, 50));\n      }\n    };\n    const {\n      dragOperation\n    } = this.manager;\n    this.destroy = effect(() => {\n      var _a4, _b2, _c3;\n      const enabled = dragOperation.status.dragging;\n      if (enabled) {\n        const root = (_c3 = (_b2 = (_a4 = dragOperation.source) == null ? void 0 : _a4.element) == null ? void 0 : _b2.ownerDocument) != null ? _c3 : document;\n        root.addEventListener(\"scroll\", this.handleScroll, listenerOptions);\n        return () => {\n          root.removeEventListener(\"scroll\", this.handleScroll, listenerOptions);\n        };\n      }\n    });\n  }\n};\n_timeout = new WeakMap();\nvar PreventSelection = class extends Plugin {\n  constructor(manager) {\n    super(manager);\n    this.manager = manager;\n    this.destroy = effect(() => {\n      const {\n        dragOperation\n      } = this.manager;\n      if (dragOperation.status.initialized) {\n        const style = document.createElement(\"style\");\n        style.textContent = `* { user-select: none !important; -webkit-user-select: none !important; }`;\n        document.head.appendChild(style);\n        removeSelection();\n        document.addEventListener(\"selectionchange\", removeSelection, {\n          capture: true\n        });\n        return () => {\n          document.removeEventListener(\"selectionchange\", removeSelection, {\n            capture: true\n          });\n          style.remove();\n        };\n      }\n    });\n  }\n};\nfunction removeSelection() {\n  var _a4;\n  (_a4 = document.getSelection()) == null ? void 0 : _a4.removeAllRanges();\n}\nvar defaults = Object.freeze({\n  offset: 10,\n  keyboardCodes: {\n    start: [\"Space\", \"Enter\"],\n    cancel: [\"Escape\"],\n    end: [\"Space\", \"Enter\", \"Tab\"],\n    up: [\"ArrowUp\"],\n    down: [\"ArrowDown\"],\n    left: [\"ArrowLeft\"],\n    right: [\"ArrowRight\"]\n  },\n  shouldActivate(args) {\n    var _a4;\n    const {\n      event,\n      source\n    } = args;\n    const target = (_a4 = source.handle) != null ? _a4 : source.element;\n    return event.target === target;\n  }\n});\nvar _cleanupFunctions;\nvar _KeyboardSensor = class _KeyboardSensor extends Sensor {\n  constructor(manager, options) {\n    super(manager);\n    this.manager = manager;\n    this.options = options;\n    __privateAdd(this, _cleanupFunctions, []);\n    this.listeners = new Listeners();\n    this.handleSourceKeyDown = (event, source, options) => {\n      if (this.disabled || event.defaultPrevented) {\n        return;\n      }\n      if (!isElement(event.target)) {\n        return;\n      }\n      if (source.disabled) {\n        return;\n      }\n      const {\n        keyboardCodes = defaults.keyboardCodes,\n        shouldActivate = defaults.shouldActivate\n      } = options != null ? options : {};\n      if (!keyboardCodes.start.includes(event.code)) {\n        return;\n      }\n      if (!this.manager.dragOperation.status.idle) {\n        return;\n      }\n      if (shouldActivate({\n        event,\n        source,\n        manager: this.manager\n      })) {\n        this.handleStart(event, source, options);\n      }\n    };\n  }\n  bind(source, options = this.options) {\n    const unbind = effect(() => {\n      var _a4;\n      const target = (_a4 = source.handle) != null ? _a4 : source.element;\n      const listener = event => {\n        if (isKeyboardEvent(event)) {\n          this.handleSourceKeyDown(event, source, options);\n        }\n      };\n      if (target) {\n        target.addEventListener(\"keydown\", listener);\n        return () => {\n          target.removeEventListener(\"keydown\", listener);\n        };\n      }\n    });\n    return unbind;\n  }\n  handleStart(event, source, options) {\n    const {\n      element\n    } = source;\n    if (!element) {\n      throw new Error(\"Source draggable does not have an associated element\");\n    }\n    event.preventDefault();\n    event.stopImmediatePropagation();\n    scrollIntoViewIfNeeded(element);\n    const {\n      center\n    } = new DOMRectangle(element);\n    const controller = this.manager.actions.start({\n      event,\n      coordinates: {\n        x: center.x,\n        y: center.y\n      },\n      source\n    });\n    if (controller.signal.aborted) return this.cleanup();\n    this.sideEffects();\n    const sourceDocument = getDocument(element);\n    const listeners = [this.listeners.bind(sourceDocument, [{\n      type: \"keydown\",\n      listener: event2 => this.handleKeyDown(event2, source, options),\n      options: {\n        capture: true\n      }\n    }])];\n    __privateGet(this, _cleanupFunctions).push(...listeners);\n  }\n  handleKeyDown(event, _source, options) {\n    const {\n      keyboardCodes = defaults.keyboardCodes\n    } = options != null ? options : {};\n    if (isKeycode(event, [...keyboardCodes.end, ...keyboardCodes.cancel])) {\n      event.preventDefault();\n      const canceled = isKeycode(event, keyboardCodes.cancel);\n      this.handleEnd(event, canceled);\n      return;\n    }\n    if (isKeycode(event, keyboardCodes.up)) {\n      this.handleMove(\"up\", event);\n    } else if (isKeycode(event, keyboardCodes.down)) {\n      this.handleMove(\"down\", event);\n    }\n    if (isKeycode(event, keyboardCodes.left)) {\n      this.handleMove(\"left\", event);\n    } else if (isKeycode(event, keyboardCodes.right)) {\n      this.handleMove(\"right\", event);\n    }\n  }\n  handleEnd(event, canceled) {\n    this.manager.actions.stop({\n      event,\n      canceled\n    });\n    this.cleanup();\n  }\n  handleMove(direction, event) {\n    var _a4, _b2;\n    const {\n      shape\n    } = this.manager.dragOperation;\n    const factor = event.shiftKey ? 5 : 1;\n    let by = {\n      x: 0,\n      y: 0\n    };\n    let offset = (_b2 = (_a4 = this.options) == null ? void 0 : _a4.offset) != null ? _b2 : defaults.offset;\n    if (typeof offset === \"number\") {\n      offset = {\n        x: offset,\n        y: offset\n      };\n    }\n    if (!shape) {\n      return;\n    }\n    switch (direction) {\n      case \"up\":\n        by = {\n          x: 0,\n          y: -offset.y * factor\n        };\n        break;\n      case \"down\":\n        by = {\n          x: 0,\n          y: offset.y * factor\n        };\n        break;\n      case \"left\":\n        by = {\n          x: -offset.x * factor,\n          y: 0\n        };\n        break;\n      case \"right\":\n        by = {\n          x: offset.x * factor,\n          y: 0\n        };\n        break;\n    }\n    if (by.x || by.y) {\n      event.preventDefault();\n      this.manager.actions.move({\n        event,\n        by\n      });\n    }\n  }\n  sideEffects() {\n    const autoScroller = this.manager.registry.plugins.get(AutoScroller);\n    if ((autoScroller == null ? void 0 : autoScroller.disabled) === false) {\n      autoScroller.disable();\n      __privateGet(this, _cleanupFunctions).push(() => {\n        autoScroller.enable();\n      });\n    }\n  }\n  cleanup() {\n    __privateGet(this, _cleanupFunctions).forEach(cleanup => cleanup());\n    __privateSet(this, _cleanupFunctions, []);\n  }\n  destroy() {\n    this.cleanup();\n    this.listeners.clear();\n  }\n};\n_cleanupFunctions = new WeakMap();\n_KeyboardSensor.configure = configurator(_KeyboardSensor);\n_KeyboardSensor.defaults = defaults;\nvar KeyboardSensor = _KeyboardSensor;\nfunction isKeycode(event, codes) {\n  return codes.includes(event.code);\n}\nvar defaults2 = Object.freeze({\n  activationConstraints(event, source) {\n    var _a4;\n    const {\n      pointerType,\n      target\n    } = event;\n    if (pointerType === \"mouse\" && isElement(target) && (source.handle === target || ((_a4 = source.handle) == null ? void 0 : _a4.contains(target)))) {\n      return void 0;\n    }\n    if (pointerType === \"touch\") {\n      return {\n        delay: {\n          value: 250,\n          tolerance: 5\n        }\n      };\n    }\n    if (isTextInput(target) && !event.defaultPrevented) {\n      return {\n        delay: {\n          value: 200,\n          tolerance: 0\n        }\n      };\n    }\n    return {\n      delay: {\n        value: 200,\n        tolerance: 10\n      },\n      distance: {\n        value: 5\n      }\n    };\n  }\n});\nvar _cleanup, _clearTimeout;\nvar _PointerSensor = class _PointerSensor extends Sensor {\n  constructor(manager, options) {\n    super(manager);\n    this.manager = manager;\n    this.options = options;\n    __privateAdd(this, _cleanup, /* @__PURE__ */new Set());\n    __privateAdd(this, _clearTimeout);\n    this.listeners = new Listeners();\n    this.latest = {\n      event: void 0,\n      coordinates: void 0\n    };\n    this.handleMove = () => {\n      const {\n        event,\n        coordinates: to\n      } = this.latest;\n      if (!event || !to) {\n        return;\n      }\n      this.manager.actions.move({\n        event,\n        to\n      });\n    };\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handlePointerUp = this.handlePointerUp.bind(this);\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n  }\n  activationConstraints(event, source) {\n    var _a4;\n    const {\n      activationConstraints = defaults2.activationConstraints\n    } = (_a4 = this.options) != null ? _a4 : {};\n    const constraints = typeof activationConstraints === \"function\" ? activationConstraints(event, source) : activationConstraints;\n    return constraints;\n  }\n  bind(source, options = this.options) {\n    const unbind = effect(() => {\n      var _a4;\n      const controller = new AbortController();\n      const {\n        signal: signal3\n      } = controller;\n      const listener = event => {\n        if (isPointerEvent(event)) {\n          this.handlePointerDown(event, source, options);\n        }\n      };\n      let targets = [(_a4 = source.handle) != null ? _a4 : source.element];\n      if (options == null ? void 0 : options.activatorElements) {\n        if (Array.isArray(options.activatorElements)) {\n          targets = options.activatorElements;\n        } else {\n          targets = options.activatorElements(source);\n        }\n      }\n      for (const target of targets) {\n        if (!target) continue;\n        patchWindow(target.ownerDocument.defaultView);\n        target.addEventListener(\"pointerdown\", listener, {\n          signal: signal3\n        });\n      }\n      return () => controller.abort();\n    });\n    return unbind;\n  }\n  handlePointerDown(event, source, options = {}) {\n    if (this.disabled || !event.isPrimary || event.button !== 0 || !isElement(event.target) || source.disabled || isCapturedBySensor(event) || !this.manager.dragOperation.status.idle) {\n      return;\n    }\n    const {\n      target\n    } = event;\n    const isNativeDraggable = isHTMLElement(target) && target.draggable && target.getAttribute(\"draggable\") === \"true\";\n    const offset = getFrameTransform(source.element);\n    this.initialCoordinates = {\n      x: event.clientX * offset.scaleX + offset.x,\n      y: event.clientY * offset.scaleY + offset.y\n    };\n    const constraints = this.activationConstraints(event, source);\n    event.sensor = this;\n    if (!(constraints == null ? void 0 : constraints.delay) && !(constraints == null ? void 0 : constraints.distance)) {\n      this.handleStart(source, event);\n    } else {\n      const {\n        delay\n      } = constraints;\n      if (delay) {\n        const timeout = setTimeout(() => this.handleStart(source, event), delay.value);\n        __privateSet(this, _clearTimeout, () => {\n          clearTimeout(timeout);\n          __privateSet(this, _clearTimeout, void 0);\n        });\n      }\n    }\n    const ownerDocument = getDocument(event.target);\n    const unbindListeners = this.listeners.bind(ownerDocument, [{\n      type: \"pointermove\",\n      listener: event2 => this.handlePointerMove(event2, source)\n    }, {\n      type: \"pointerup\",\n      listener: this.handlePointerUp,\n      options: {\n        capture: true\n      }\n    }, {\n      // Cancel activation if there is a competing Drag and Drop interaction\n      type: \"dragstart\",\n      listener: isNativeDraggable ? this.handleCancel : preventDefault,\n      options: {\n        capture: true\n      }\n    }]);\n    const cleanup = () => {\n      var _a4;\n      unbindListeners();\n      (_a4 = __privateGet(this, _clearTimeout)) == null ? void 0 : _a4.call(this);\n      this.initialCoordinates = void 0;\n    };\n    __privateGet(this, _cleanup).add(cleanup);\n  }\n  handlePointerMove(event, source) {\n    const coordinates = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    const offset = getFrameTransform(source.element);\n    coordinates.x = coordinates.x * offset.scaleX + offset.x;\n    coordinates.y = coordinates.y * offset.scaleY + offset.y;\n    if (this.manager.dragOperation.status.dragging) {\n      event.preventDefault();\n      event.stopPropagation();\n      this.latest.event = event;\n      this.latest.coordinates = coordinates;\n      scheduler.schedule(this.handleMove);\n      return;\n    }\n    if (!this.initialCoordinates) {\n      return;\n    }\n    const delta = {\n      x: coordinates.x - this.initialCoordinates.x,\n      y: coordinates.y - this.initialCoordinates.y\n    };\n    const constraints = this.activationConstraints(event, source);\n    const {\n      distance,\n      delay\n    } = constraints != null ? constraints : {};\n    if (distance) {\n      if (distance.tolerance != null && exceedsDistance(delta, distance.tolerance)) {\n        return this.handleCancel(event);\n      }\n      if (exceedsDistance(delta, distance.value)) {\n        return this.handleStart(source, event);\n      }\n    }\n    if (delay) {\n      if (exceedsDistance(delta, delay.tolerance)) {\n        return this.handleCancel(event);\n      }\n    }\n  }\n  handlePointerUp(event) {\n    const {\n      status\n    } = this.manager.dragOperation;\n    if (!status.idle) {\n      event.preventDefault();\n      event.stopPropagation();\n      const canceled = !status.initialized;\n      this.manager.actions.stop({\n        event,\n        canceled\n      });\n    }\n    this.cleanup();\n  }\n  handleKeyDown(event) {\n    if (event.key === \"Escape\") {\n      event.preventDefault();\n      this.handleCancel(event);\n    }\n  }\n  handleStart(source, event) {\n    var _a4;\n    const {\n      manager,\n      initialCoordinates\n    } = this;\n    (_a4 = __privateGet(this, _clearTimeout)) == null ? void 0 : _a4.call(this);\n    if (!initialCoordinates || !manager.dragOperation.status.idle) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n    const controller = manager.actions.start({\n      coordinates: initialCoordinates,\n      event,\n      source\n    });\n    if (controller.signal.aborted) return this.cleanup();\n    event.preventDefault();\n    const ownerDocument = getDocument(event.target);\n    const pointerCaptureTarget = ownerDocument.body;\n    pointerCaptureTarget.setPointerCapture(event.pointerId);\n    const unbind = this.listeners.bind(ownerDocument, [{\n      // Prevent scrolling on touch devices\n      type: \"touchmove\",\n      listener: preventDefault,\n      options: {\n        passive: false\n      }\n    }, {\n      // Prevent click events\n      type: \"click\",\n      listener: preventDefault\n    }, {\n      type: \"contextmenu\",\n      listener: preventDefault\n    }, {\n      type: \"keydown\",\n      listener: this.handleKeyDown\n    }, {\n      type: \"lostpointercapture\",\n      listener: event2 => {\n        if (event2.target !== pointerCaptureTarget) return;\n        this.handlePointerUp(event2);\n      }\n    }]);\n    __privateGet(this, _cleanup).add(unbind);\n  }\n  handleCancel(event) {\n    const {\n      dragOperation\n    } = this.manager;\n    if (dragOperation.status.initialized) {\n      this.manager.actions.stop({\n        event,\n        canceled: true\n      });\n    }\n    this.cleanup();\n  }\n  cleanup() {\n    this.latest = {\n      event: void 0,\n      coordinates: void 0\n    };\n    __privateGet(this, _cleanup).forEach(cleanup => cleanup());\n    __privateGet(this, _cleanup).clear();\n  }\n  destroy() {\n    this.cleanup();\n    this.listeners.clear();\n  }\n};\n_cleanup = new WeakMap();\n_clearTimeout = new WeakMap();\n_PointerSensor.configure = configurator(_PointerSensor);\n_PointerSensor.defaults = defaults2;\nvar PointerSensor = _PointerSensor;\nfunction isCapturedBySensor(event) {\n  return \"sensor\" in event;\n}\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction noop() {}\nvar windows = /* @__PURE__ */new WeakSet();\nfunction patchWindow(window) {\n  if (!window || windows.has(window)) {\n    return;\n  }\n  window.addEventListener(\"touchmove\", noop, {\n    capture: false,\n    passive: false\n  });\n  windows.add(window);\n}\n\n// src/core/manager/manager.ts\nvar defaultPreset = {\n  modifiers: [],\n  plugins: [Accessibility, AutoScroller, Cursor, Feedback, PreventSelection],\n  sensors: [PointerSensor, KeyboardSensor]\n};\nvar DragDropManager = class extends DragDropManager$1 {\n  constructor(input = {}) {\n    const {\n      plugins = defaultPreset.plugins,\n      sensors = defaultPreset.sensors,\n      modifiers = []\n    } = input;\n    super(__spreadProps(__spreadValues({}, input), {\n      plugins: [ScrollListener, Scroller, ...plugins],\n      sensors,\n      modifiers\n    }));\n  }\n};\nvar _feedback_dec, _element_dec, _handle_dec, _c, _init4, _handle, _element, _feedback;\nvar Draggable = class extends (_c = Draggable$1, _handle_dec = [reactive], _element_dec = [reactive], _feedback_dec = [reactive], _c) {\n  constructor(_a4, manager) {\n    var _b2 = _a4,\n      {\n        element,\n        effects: effects2 = () => [],\n        handle,\n        feedback = \"default\"\n      } = _b2,\n      input = __objRest(_b2, [\"element\", \"effects\", \"handle\", \"feedback\"]);\n    super(__spreadValues({\n      effects: () => [...effects2(), () => {\n        var _a5, _b3;\n        const {\n          manager: manager2\n        } = this;\n        if (!manager2) return;\n        const sensors = (_b3 = (_a5 = this.sensors) == null ? void 0 : _a5.map(descriptor)) != null ? _b3 : [...manager2.sensors];\n        const unbindFunctions = sensors.map(entry => {\n          const sensorInstance = entry instanceof Sensor ? entry : manager2.registry.register(entry.plugin);\n          const options = entry instanceof Sensor ? void 0 : entry.options;\n          const unbind = sensorInstance.bind(this, options);\n          return unbind;\n        });\n        return function cleanup() {\n          unbindFunctions.forEach(unbind => unbind());\n        };\n      }]\n    }, input), manager);\n    __privateAdd(this, _handle, __runInitializers(_init4, 8, this)), __runInitializers(_init4, 11, this);\n    __privateAdd(this, _element, __runInitializers(_init4, 12, this)), __runInitializers(_init4, 15, this);\n    __privateAdd(this, _feedback, __runInitializers(_init4, 16, this)), __runInitializers(_init4, 19, this);\n    this.element = element;\n    this.handle = handle;\n    this.feedback = feedback;\n  }\n};\n_init4 = __decoratorStart(_c);\n_handle = new WeakMap();\n_element = new WeakMap();\n_feedback = new WeakMap();\n__decorateElement(_init4, 4, \"handle\", _handle_dec, Draggable, _handle);\n__decorateElement(_init4, 4, \"element\", _element_dec, Draggable, _element);\n__decorateElement(_init4, 4, \"feedback\", _feedback_dec, Draggable, _feedback);\n__decoratorMetadata(_init4, Draggable);\nvar _proxy_dec, _element_dec2, _c2, _init5, _element2, _d, element_get, element_set, _Droppable_instances, _proxy;\nvar Droppable = class extends (_c2 = Droppable$1, _element_dec2 = [reactive], _proxy_dec = [reactive], _c2) {\n  constructor(_a4, manager) {\n    var _b2 = _a4,\n      {\n        element,\n        effects: effects2 = () => []\n      } = _b2,\n      input = __objRest(_b2, [\"element\", \"effects\"]);\n    const {\n      collisionDetector = defaultCollisionDetection\n    } = input;\n    const updateShape = boundingClientRect => {\n      const {\n        manager: manager2,\n        element: element2\n      } = this;\n      if (!element2 || boundingClientRect === null) {\n        this.shape = void 0;\n        return void 0;\n      }\n      if (!manager2) return;\n      const updatedShape = new DOMRectangle(element2);\n      const shape = untracked(() => this.shape);\n      if (updatedShape && (shape == null ? void 0 : shape.equals(updatedShape))) {\n        return shape;\n      }\n      this.shape = updatedShape;\n      return updatedShape;\n    };\n    const observePosition = signal(false);\n    super(__spreadProps(__spreadValues({}, input), {\n      collisionDetector,\n      effects: () => [...effects2(), () => {\n        const {\n          element: element2,\n          manager: manager2\n        } = this;\n        if (!manager2) return;\n        const {\n          dragOperation\n        } = manager2;\n        const {\n          source\n        } = dragOperation;\n        observePosition.value = Boolean(source && dragOperation.status.initialized && element2 && !this.disabled && this.accepts(source));\n      }, () => {\n        const {\n          element: element2\n        } = this;\n        if (observePosition.value && element2) {\n          const positionObserver = new PositionObserver(element2, updateShape);\n          return () => {\n            positionObserver.disconnect();\n            this.shape = void 0;\n          };\n        }\n      }, () => {\n        var _a5;\n        if ((_a5 = this.manager) == null ? void 0 : _a5.dragOperation.status.initialized) {\n          return () => {\n            this.shape = void 0;\n          };\n        }\n      }]\n    }), manager);\n    __privateAdd(this, _Droppable_instances);\n    __privateAdd(this, _element2, __runInitializers(_init5, 8, this)), __runInitializers(_init5, 11, this);\n    __privateAdd(this, _proxy, __runInitializers(_init5, 12, this)), __runInitializers(_init5, 15, this);\n    this.element = element;\n    this.refreshShape = () => updateShape();\n  }\n  set element(element) {\n    __privateSet(this, _Droppable_instances, element, element_set);\n  }\n  get element() {\n    var _a4;\n    return (_a4 = this.proxy) != null ? _a4 : __privateGet(this, _Droppable_instances, element_get);\n  }\n};\n_init5 = __decoratorStart(_c2);\n_element2 = new WeakMap();\n_Droppable_instances = new WeakSet();\n_proxy = new WeakMap();\n_d = __decorateElement(_init5, 20, \"#element\", _element_dec2, _Droppable_instances, _element2), element_get = _d.get, element_set = _d.set;\n__decorateElement(_init5, 4, \"proxy\", _proxy_dec, Droppable, _proxy);\n__decoratorMetadata(_init5, Droppable);\nexport { Accessibility, AutoScroller, Cursor, DragDropManager, Draggable, Droppable, Feedback, KeyboardSensor, PointerSensor, PreventSelection, ScrollListener, Scroller, defaultPreset };\n\n//# sourceMappingURL=index.js.map", "map": {"version": 3, "names": ["defaultAttributes", "role", "roleDescription", "defaultDescriptionIdPrefix", "defaultAnnouncementIdPrefix", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "dragstart", "operation", "source", "id", "dragover", "target", "dragend", "canceled", "isFocusable", "element", "tagName", "toLowerCase", "includes", "createHiddenText", "value", "document", "createElement", "style", "setProperty", "textContent", "createLiveRegion", "setAttribute", "debouncedEvents", "Accessibility", "Plugin", "constructor", "manager", "options", "idPrefix", "description", "descriptionPrefix", "announcement", "announcementPrefix", "announcements", "screenReaderInstructions", "debounce", "debounceMs", "descriptionId", "generateUniqueId", "announcementId", "hiddenTextElement", "liveRegionElement", "liveRegionTextNode", "latestAnnouncement", "updateAnnouncement", "nodeValue", "scheduleUpdateAnnouncement", "scheduler", "schedule", "debouncedUpdateAnnouncement", "eventListeners", "Object", "entries", "map", "eventName", "getAnnouncement", "monitor", "addEventListener", "event", "manager2", "cancel", "initialize", "elements", "isConnected", "push", "createTextNode", "append<PERSON><PERSON><PERSON>", "length", "body", "append", "mutations", "Set", "executeMutations", "registerEffect", "_a4", "clear", "registry", "draggables", "activator", "handle", "add", "<PERSON><PERSON><PERSON><PERSON>", "hasAttribute", "key", "String", "isDragging", "getAttribute", "disabled", "size", "destroy", "remove", "for<PERSON>ach", "unsubscribe", "fn", "wait", "timeout", "debounced", "clearTimeout", "setTimeout", "<PERSON><PERSON><PERSON>", "doc", "computed", "getDocument", "dragOperation", "effect", "cursor", "status", "initialized", "document2", "head", "ATTR_PREFIX", "DROPPING_ATTRIBUTE", "CSS_PREFIX", "ATTRIBUTE", "PLACEHOLDER_ATTRIBUTE", "IGNORED_ATTRIBUTES", "IGNORED_STYLES", "CSS_RULES", "replace", "trim", "createPlaceholder", "type", "untracked", "containedDroppables", "findContainedDroppables", "droppables", "cleanup", "placeholder", "cloneElement", "proxyDroppableElements", "configurePlaceholder", "call", "Map", "droppable", "contains", "identifierAttribute", "set", "selector", "clonedElement", "matches", "querySelector", "removeAttribute", "originalElement", "proxy", "ProxiedElements", "delete", "isSameFrame", "getFrameElement", "preventPopoverClose", "newState", "isElement", "requestAnimationFrame", "showPopover", "isTableRow", "styleSheetRegistry", "_overlay_dec", "_a", "_init", "_overlay", "_Feedback_instances", "render_fn", "injectStyles_fn", "_<PERSON><PERSON><PERSON>", "reactive", "__privateAdd", "__runInitializers", "state", "initial", "current", "__privateMethod", "registration", "instances", "has", "__decoratorStart", "WeakMap", "WeakSet", "_b2", "_c3", "position", "idle", "feedback", "initializing", "feedbackElement", "overlay", "frameTransform", "getFrameTransform", "elementFrameTransform", "crossFrame", "shape", "DOMRectangle", "ignoreTransforms", "scaleDelta", "x", "scaleX", "y", "scaleY", "width", "height", "top", "left", "elementMutationObserver", "documentMutationObserver", "styles", "Styles", "transition", "translate", "boxSizing", "paddingBlockStart", "paddingBlockEnd", "paddingInlineStart", "paddingInlineEnd", "borderInlineStartWidth", "borderInlineEndWidth", "borderBlockStartWidth", "borderBlockEndWidth", "getComputedStyles", "clone", "contentBox", "widthOffset", "parseInt", "heightOffset", "isKeyboardOperation", "isKeyboardEvent", "activatorEvent", "parsedTranslate", "parseTranslate", "transform<PERSON><PERSON>in", "relativeTop", "relativeLeft", "coordinates", "tX2", "tY2", "dimensions", "coordinates<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "delta", "projected", "transform", "initialTranslate", "tX", "tY", "translateString", "transitionString", "scale", "insertAdjacentElement", "rootElement", "root", "supportsPopover", "resizeObserver", "ResizeObserver", "placeholder<PERSON><PERSON><PERSON>", "origin", "dX", "dY", "takeRecords", "cells", "Array", "from", "placeholder<PERSON><PERSON><PERSON>", "index", "cell", "placeholder<PERSON><PERSON>", "offsetWidth", "initialShape", "feedbackWindow", "getWindow", "handleWindowResize", "actions", "stop", "observe", "MutationObserver", "hasChildrenMutations", "mutation", "attributeName", "startsWith", "attributeValue", "supportsStyle", "styles2", "getPropertyValue", "removeProperty", "innerHTML", "attributes", "subtree", "childList", "entry", "addedNodes", "node", "nextElement<PERSON><PERSON>ling", "previousElementSibling", "ownerDocument", "restoreFocus", "_a5", "get", "element2", "isHTMLElement", "focus", "disconnect", "removeEventListener", "reset", "moved", "parentElement", "replaceWith", "cleanupEffects", "effects", "transform2", "status2", "dragging", "initialTranslate2", "translate2", "previousTranslate", "modifiers", "currentShape", "_a6", "translateTransition", "delta2", "Point", "Rectangle", "boundingRectangle", "dropped", "dispose", "dropAnimation", "_b3", "animation", "getFinalKeyframe", "keyframe", "pause", "options2", "currentTranslate", "final", "alignment", "finalTranslate", "heightKeyframes", "Math", "round", "intrinsicHeight", "minHeight", "maxHeight", "widthKeyframes", "intrinsicWidth", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "animateTransform", "keyframes", "__spreadProps", "__spreadValues", "duration", "easing", "then", "finish", "renderer", "rendering", "sourceDocument", "targetDocument", "documents", "prepend", "mutationObserver", "removedNodes", "__decorateElement", "__decoratorMetadata", "configure", "configurator", "<PERSON><PERSON><PERSON>", "LOCKED", "UNLOCKED", "_dec", "_a2", "_dec2", "_b", "_init2", "__b", "__a", "ScrollDirection", "Forward", "Reverse", "ScrollLock", "isLocked", "direction", "Idle", "unlock", "DIRECTIONS", "ScrollIntent", "ScrollIntentTracker", "scrollIntent", "signal", "previousDel<PERSON>", "directions", "getDirection", "intent", "peek", "batch", "axis", "Axes", "a", "b", "sign", "_autoScrolling_dec", "_a3", "_init3", "_autoScrolling", "_meta", "_scroll", "<PERSON><PERSON><PERSON>", "CorePlugin", "__privateGet", "by", "scrollTop", "scrollLeft", "scroll", "getScrollableElements", "__privateSet", "currentPosition", "getScrollIntent", "scrollIntentTracker", "scrollableElement", "elementCanScroll", "canScroll", "speed", "detectScrollIntent", "scrollLeftBy", "scrollTopBy", "previousScrollBy", "autoScrolling", "scrollIntentMismatch", "previousElementFromPoint", "previousScrollableElements", "elementFromPoint", "getElementFromPoint", "scrollableElements", "documentElement", "targetElement", "getScrollableAncestors", "excludeElement", "deepEqual", "defaultPrevented", "preventDefault", "Scheduler", "scheduler5", "pending", "tasks", "resolvers", "flush", "task", "resolve", "Promise", "scheduler3", "callback", "AUTOSCROLL_INTERVAL", "AutoScroller", "_options", "scroller", "plugins", "Error", "_", "canScroll2", "interval", "setInterval", "clearInterval", "listenerOptions", "capture", "passive", "_timeout", "ScrollListener", "handleScroll", "collisionObserver", "forceUpdate", "enabled", "PreventSelection", "removeSelection", "getSelection", "removeAllRanges", "defaults", "freeze", "offset", "keyboardCodes", "start", "end", "up", "down", "right", "shouldActivate", "args", "_cleanupFunctions", "_KeyboardSensor", "Sensor", "listeners", "Listeners", "handleSourceKeyDown", "code", "handleStart", "bind", "unbind", "listener", "stopImmediatePropagation", "scrollIntoViewIfNeeded", "center", "controller", "aborted", "sideEffects", "event2", "handleKeyDown", "_source", "isKeycode", "handleEnd", "handleMove", "factor", "shift<PERSON>ey", "move", "autoScroller", "disable", "enable", "KeyboardSensor", "codes", "defaults2", "activationConstraints", "pointerType", "delay", "tolerance", "isTextInput", "distance", "_cleanup", "_clearTimeout", "_PointerSensor", "latest", "to", "handleCancel", "handlePointerUp", "constraints", "AbortController", "signal3", "isPointerEvent", "handlePointerDown", "targets", "activatorElements", "isArray", "patchWindow", "defaultView", "abort", "isPrimary", "button", "isCapturedBySensor", "isNativeDraggable", "initialCoordinates", "clientX", "clientY", "sensor", "unbindListeners", "handlePointerMove", "stopPropagation", "exceedsDistance", "pointerCaptureTarget", "setPointerCapture", "pointerId", "PointerSensor", "noop", "windows", "window", "defaultPreset", "sensors", "DragDropManager", "DragDropManager$1", "input", "_feedback_dec", "_element_dec", "_handle_dec", "_c", "_init4", "_handle", "_element", "_feedback", "Draggable", "Draggable$1", "effects2", "__objRest", "descriptor", "unbindFunctions", "sensorInstance", "register", "plugin", "_proxy_dec", "_element_dec2", "_c2", "_init5", "_element2", "_d", "element_get", "element_set", "_Droppable_instances", "_proxy", "Droppable", "Droppable$1", "collisionDetector", "defaultCollisionDetection", "updateShape", "boundingClientRect", "updatedShape", "equals", "observePosition", "Boolean", "accepts", "positionObserver", "PositionObserver", "refreshShape"], "sources": ["C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\accessibility\\defaults.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\accessibility\\utilities.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\accessibility\\HiddenText.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\accessibility\\LiveRegion.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\accessibility\\Accessibility.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\cursor\\Cursor.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\feedback\\constants.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\feedback\\utilities.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\feedback\\Feedback.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\scrolling\\ScrollLock.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\scrolling\\ScrollIntent.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\scrolling\\Scroller.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scheduling\\scheduler.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\scrolling\\AutoScroller.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\scrolling\\ScrollListener.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\plugins\\selection\\PreventSelection.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\sensors\\keyboard\\KeyboardSensor.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\sensors\\pointer\\PointerSensor.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\manager\\manager.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\entities\\draggable\\draggable.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\core\\entities\\droppable\\droppable.ts"], "sourcesContent": ["import type {Announcements, ScreenReaderInstructions} from './types.ts';\n\nexport const defaultAttributes = {\n  role: 'button',\n  roleDescription: 'draggable',\n  tabIndex: 0,\n};\n\nexport const defaultDescriptionIdPrefix = `dnd-kit-description`;\nexport const defaultAnnouncementIdPrefix = `dnd-kit-announcement`;\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `To pick up a draggable item, press the space bar. While dragging, use the arrow keys to move the item in a given direction. Press space again to drop the item in its new position, or press escape to cancel.`,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  dragstart({operation: {source}}) {\n    if (!source) return;\n\n    return `Picked up draggable item ${source.id}.`;\n  },\n  dragover({operation: {source, target}}) {\n    if (!source || source.id === target?.id) return;\n\n    if (target) {\n      return `Draggable item ${source.id} was moved over droppable target ${target.id}.`;\n    }\n\n    return `Draggable item ${source.id} is no longer over a droppable target.`;\n  },\n  dragend({operation: {source, target}, canceled}) {\n    if (!source) return;\n\n    if (canceled) {\n      return `Dragging was cancelled. Draggable item ${source.id} was dropped.`;\n    }\n\n    if (target) {\n      return `Draggable item ${source.id} was dropped over droppable target ${target.id}`;\n    }\n\n    return `Draggable item ${source.id} was dropped.`;\n  },\n};\n", "export function isFocusable(element: Element) {\n  const tagName = element.tagName.toLowerCase();\n\n  return ['input', 'select', 'textarea', 'a', 'button'].includes(tagName);\n}\n", "export function createHiddenText(id: string, value: string) {\n  const element = document.createElement('div');\n\n  element.id = id;\n  element.style.setProperty('display', 'none');\n  element.textContent = value;\n\n  return element;\n}\n", "export function createLiveRegion(id: string) {\n  const element = document.createElement('div');\n\n  element.id = id;\n  element.setAttribute('role', 'status');\n  element.setAttribute('aria-live', 'polite');\n  element.setAttribute('aria-atomic', 'true');\n  element.style.setProperty('position', 'fixed');\n  element.style.setProperty('width', '1px');\n  element.style.setProperty('height', '1px');\n  element.style.setProperty('margin', '-1px');\n  element.style.setProperty('border', '0');\n  element.style.setProperty('padding', '0');\n  element.style.setProperty('overflow', 'hidden');\n  element.style.setProperty('clip', 'rect(0 0 0 0)');\n  element.style.setProperty('clip-path', 'inset(100%)');\n  element.style.setProperty('white-space', 'nowrap');\n\n  return element;\n}\n", "import {Plugin} from '@dnd-kit/abstract';\nimport {isS<PERSON><PERSON>, generateUniqueId, scheduler} from '@dnd-kit/dom/utilities';\n\nimport type {DragDropManager} from '../../manager/index.ts';\nimport {\n  defaultAnnouncements,\n  defaultAttributes,\n  defaultAnnouncementIdPrefix,\n  defaultDescriptionIdPrefix,\n  defaultScreenReaderInstructions,\n} from './defaults.ts';\nimport type {Announcements, ScreenReaderInstructions} from './types.ts';\nimport {isFocusable} from './utilities.ts';\nimport {createHiddenText} from './HiddenText.ts';\nimport {createLiveRegion} from './LiveRegion.ts';\n\ninterface Options {\n  /**\n   * Optional id that should be used for the accessibility plugin's screen reader instructions and announcements.\n   */\n  id?: string;\n  /**\n   * Optional id prefix to use for the accessibility plugin's screen reader instructions and announcements.\n   */\n  idPrefix?: {\n    description?: string;\n    announcement?: string;\n  };\n  /**\n   * The announcements to use for the accessibility plugin.\n   */\n  announcements?: Announcements;\n  /**\n   * The screen reader instructions to use for the accessibility plugin.\n   */\n  screenReaderInstructions?: ScreenReaderInstructions;\n  /**\n   * The number of milliseconds to debounce the announcement updates.\n   *\n   * @remarks\n   * Only the `dragover` and `dragmove` announcements are debounced.\n   *\n   * @default 500\n   */\n  debounce?: number;\n}\n\nconst debouncedEvents = ['dragover', 'dragmove'];\n\nexport class Accessibility extends Plugin<DragDropManager> {\n  constructor(manager: DragDropManager, options?: Options) {\n    super(manager);\n\n    const {\n      id,\n      idPrefix: {\n        description: descriptionPrefix = defaultDescriptionIdPrefix,\n        announcement: announcementPrefix = defaultAnnouncementIdPrefix,\n      } = {},\n      announcements = defaultAnnouncements,\n      screenReaderInstructions = defaultScreenReaderInstructions,\n      debounce: debounceMs = 500,\n    } = options ?? {};\n\n    const descriptionId = id\n      ? `${descriptionPrefix}-${id}`\n      : generateUniqueId(descriptionPrefix);\n    const announcementId = id\n      ? `${announcementPrefix}-${id}`\n      : generateUniqueId(announcementPrefix);\n\n    let hiddenTextElement: HTMLElement | undefined;\n    let liveRegionElement: HTMLElement | undefined;\n    let liveRegionTextNode: Node | undefined;\n    let latestAnnouncement: string | undefined;\n\n    const updateAnnouncement = (value = latestAnnouncement) => {\n      if (!liveRegionTextNode || !value) return;\n      if (liveRegionTextNode?.nodeValue !== value) {\n        liveRegionTextNode.nodeValue = value;\n      }\n    };\n    const scheduleUpdateAnnouncement = () =>\n      scheduler.schedule(updateAnnouncement);\n    const debouncedUpdateAnnouncement = debounce(\n      scheduleUpdateAnnouncement,\n      debounceMs\n    );\n\n    const eventListeners = Object.entries(announcements).map(\n      ([eventName, getAnnouncement]) => {\n        return this.manager.monitor.addEventListener(\n          eventName as keyof Announcements,\n          (event: any, manager: DragDropManager) => {\n            const element = liveRegionTextNode;\n            if (!element) return;\n\n            const announcement = getAnnouncement?.(event, manager);\n\n            if (announcement && element.nodeValue !== announcement) {\n              latestAnnouncement = announcement;\n\n              if (debouncedEvents.includes(eventName)) {\n                debouncedUpdateAnnouncement();\n              } else {\n                scheduleUpdateAnnouncement();\n                debouncedUpdateAnnouncement.cancel();\n              }\n            }\n          }\n        );\n      }\n    );\n\n    const initialize = () => {\n      let elements = [];\n\n      if (!hiddenTextElement?.isConnected) {\n        hiddenTextElement = createHiddenText(\n          descriptionId,\n          screenReaderInstructions.draggable\n        );\n        elements.push(hiddenTextElement);\n      }\n\n      if (!liveRegionElement?.isConnected) {\n        liveRegionElement = createLiveRegion(announcementId);\n        liveRegionTextNode = document.createTextNode('');\n        liveRegionElement.appendChild(liveRegionTextNode);\n        elements.push(liveRegionElement);\n      }\n\n      if (elements.length > 0) {\n        document.body.append(...elements);\n      }\n    };\n\n    const mutations = new Set<() => void>();\n\n    function executeMutations() {\n      for (const operation of mutations) {\n        operation();\n      }\n    }\n\n    this.registerEffect(() => {\n      mutations.clear();\n\n      // Re-run effect when any of the draggable elements change\n      for (const draggable of this.manager.registry.draggables.value) {\n        const activator = draggable.handle ?? draggable.element;\n\n        if (activator) {\n          if (!hiddenTextElement || !liveRegionElement) {\n            mutations.add(initialize);\n          }\n\n          if (\n            (!isFocusable(activator) || isSafari()) &&\n            !activator.hasAttribute('tabindex')\n          ) {\n            mutations.add(() => activator.setAttribute('tabindex', '0'));\n          }\n\n          if (\n            !activator.hasAttribute('role') &&\n            !(activator.tagName.toLowerCase() === 'button')\n          ) {\n            mutations.add(() =>\n              activator.setAttribute('role', defaultAttributes.role)\n            );\n          }\n\n          if (!activator.hasAttribute('aria-roledescription')) {\n            mutations.add(() =>\n              activator.setAttribute(\n                'aria-roledescription',\n                defaultAttributes.roleDescription\n              )\n            );\n          }\n\n          if (!activator.hasAttribute('aria-describedby')) {\n            mutations.add(() =>\n              activator.setAttribute('aria-describedby', descriptionId)\n            );\n          }\n\n          for (const key of ['aria-pressed', 'aria-grabbed']) {\n            const value = String(draggable.isDragging);\n\n            if (activator.getAttribute(key) !== value) {\n              mutations.add(() => activator.setAttribute(key, value));\n            }\n          }\n\n          const disabled = String(draggable.disabled);\n\n          if (activator.getAttribute('aria-disabled') !== disabled) {\n            mutations.add(() =>\n              activator.setAttribute('aria-disabled', disabled)\n            );\n          }\n        }\n      }\n\n      if (mutations.size > 0) {\n        scheduler.schedule(executeMutations);\n      }\n    });\n\n    this.destroy = () => {\n      super.destroy();\n      hiddenTextElement?.remove();\n      liveRegionElement?.remove();\n      eventListeners.forEach((unsubscribe) => unsubscribe());\n    };\n  }\n}\n\nfunction debounce(fn: () => void, wait: number) {\n  let timeout: NodeJS.Timeout | undefined;\n  const debounced = () => {\n    clearTimeout(timeout);\n    timeout = setTimeout(fn, wait);\n  };\n\n  debounced.cancel = () => clearTimeout(timeout);\n\n  return debounced;\n}\n", "import {Plugin} from '@dnd-kit/abstract';\nimport {computed, effect} from '@dnd-kit/state';\nimport {getDocument} from '@dnd-kit/dom/utilities';\n\nimport {DragDropManager} from '../../manager/index.ts';\n\ninterface CursorPluginOptions {\n  /**\n   * The style of the cursor to be applied to the document body.\n   * @default 'grabbing'\n   */\n  cursor?: string;\n}\n\nexport class Cursor extends Plugin<DragDropManager> {\n  constructor(\n    public manager: DragDropManager,\n    options?: CursorPluginOptions\n  ) {\n    super(manager, options);\n\n    const doc = computed(() =>\n      getDocument(this.manager.dragOperation.source?.element)\n    );\n\n    this.destroy = effect(() => {\n      const {dragOperation} = this.manager;\n      const {cursor = 'grabbing'} = this.options ?? {};\n\n      if (dragOperation.status.initialized) {\n        const document = doc.value;\n        const style = document.createElement('style');\n        style.textContent = `* { cursor: ${cursor} !important; }`;\n        document.head.appendChild(style);\n\n        return () => style.remove();\n      }\n    });\n  }\n}\n", "export const ATTR_PREFIX = 'data-dnd-';\nexport const DROPPING_ATTRIBUTE = `${ATTR_PREFIX}dropping`;\nexport const CSS_PREFIX = '--dnd-';\nexport const ATTRIBUTE = `${ATTR_PREFIX}dragging`;\nexport const PLACEHOLDER_ATTRIBUTE = `${ATTR_PREFIX}placeholder`;\n\nexport const IGNORED_ATTRIBUTES = [\n  ATTRIBUTE,\n  PLACEHOLDER_ATTRIBUTE,\n  'popover',\n  'aria-pressed',\n  'aria-grabbing',\n];\n\nexport const IGNORED_STYLES = ['view-transition-name'];\n\nexport const CSS_RULES = `\n  :root [${ATTRIBUTE}] {\n    position: fixed !important;\n    pointer-events: none !important;\n    touch-action: none;\n    z-index: calc(infinity);\n    will-change: translate;\n    top: var(${CSS_PREFIX}top, 0px) !important;\n    left: var(${CSS_PREFIX}left, 0px) !important;\n    right: unset !important;\n    bottom: unset !important;\n    width: var(${CSS_PREFIX}width, auto);\n    max-width: var(${CSS_PREFIX}width, auto);\n    height: var(${CSS_PREFIX}height, auto);\n    max-height: var(${CSS_PREFIX}height, auto);\n    transition: var(${CSS_PREFIX}transition) !important;\n  }\n\n  :root [${PLACEHOLDER_ATTRIBUTE}] {\n    transition: none;\n  }\n\n  :root [${PLACEHOLDER_ATTRIBUTE}='hidden'] {\n    visibility: hidden;\n  }\n\n  [${ATTRIBUTE}] * {\n    pointer-events: none !important;\n  }\n\n  [${ATTRIBUTE}]:not([${DROPPING_ATTRIBUTE}]) {\n    translate: var(${CSS_PREFIX}translate) !important;\n  }\n\n  [${ATTRIBUTE}][style*='${CSS_PREFIX}scale'] {\n    scale: var(${CSS_PREFIX}scale) !important;\n    transform-origin: var(${CSS_PREFIX}transform-origin) !important;\n  }\n\n  @layer {\n    :where([${ATTRIBUTE}][popover]) {\n      overflow: visible;\n      background: unset;\n      border: unset;\n      margin: unset;\n      padding: unset;\n      color: inherit;\n\n      &:is(input, button) {\n        border: revert;\n        background: revert;\n      }\n    }\n  }\n  [${ATTRIBUTE}]::backdrop, [${ATTR_PREFIX}overlay]:not([${ATTRIBUTE}]) {\n    display: none;\n    visibility: hidden;\n  }\n`\n  .replace(/\\n+/g, ' ')\n  .replace(/\\s+/g, ' ')\n  .trim();\n", "import {untracked} from '@dnd-kit/state';\nimport {\n  cloneElement,\n  generateUniqueId,\n  getFrameElement,\n  showPopover,\n  ProxiedElements,\n  isElement,\n} from '@dnd-kit/dom/utilities';\n\nimport type {Draggable, Droppable} from '../../entities/index.ts';\nimport {ATTR_PREFIX, PLACEHOLDER_ATTRIBUTE} from './constants.ts';\n\n/**\n * Creates a placeholder element for a draggable source\n * The placeholder maintains the original element's dimensions and position\n */\nexport function createPlaceholder(\n  source: Draggable,\n  type = 'hidden'\n): Element | undefined {\n  return untracked(() => {\n    const {element, manager} = source;\n\n    if (!element || !manager) return;\n\n    const containedDroppables = findContainedDroppables(\n      element,\n      manager.registry.droppables\n    );\n    const cleanup: Array<() => void> = [];\n    const placeholder = cloneElement(element);\n    const {remove} = placeholder;\n\n    proxyDroppableElements(containedDroppables, placeholder, cleanup);\n    configurePlaceholder(placeholder, type);\n\n    // Override remove to handle cleanup of proxies\n    placeholder.remove = () => {\n      cleanup.forEach((fn) => fn());\n      remove.call(placeholder);\n    };\n\n    return placeholder;\n  });\n}\n\n/**\n * Maps droppable elements contained within the source element\n * Returns a map of droppables to their temporary identifier attributes\n */\nfunction findContainedDroppables(\n  element: Element,\n  droppables: Iterable<Droppable>\n): Map<Droppable, string> {\n  const containedDroppables = new Map<Droppable, string>();\n\n  for (const droppable of droppables) {\n    if (!droppable.element) continue;\n\n    if (element === droppable.element || element.contains(droppable.element)) {\n      const identifierAttribute = `${ATTR_PREFIX}${generateUniqueId('dom-id')}`;\n      droppable.element.setAttribute(identifierAttribute, '');\n      containedDroppables.set(droppable, identifierAttribute);\n    }\n  }\n\n  return containedDroppables;\n}\n\n/**\n * Sets up proxy relationships between original droppable elements and their clones\n */\nfunction proxyDroppableElements(\n  containedDroppables: Map<Droppable, string>,\n  placeholder: Element,\n  cleanup: Array<() => void>\n): void {\n  for (const [droppable, identifierAttribute] of containedDroppables) {\n    if (!droppable.element) continue;\n\n    const selector = `[${identifierAttribute}]`;\n    const clonedElement = placeholder.matches(selector)\n      ? placeholder\n      : placeholder.querySelector(selector);\n\n    droppable.element.removeAttribute(identifierAttribute);\n\n    if (!clonedElement) continue;\n\n    const originalElement = droppable.element;\n\n    droppable.proxy = clonedElement;\n    clonedElement.removeAttribute(identifierAttribute);\n\n    ProxiedElements.set(originalElement, clonedElement);\n\n    cleanup.push(() => {\n      ProxiedElements.delete(originalElement);\n      droppable.proxy = undefined;\n    });\n  }\n}\n\n/**\n * Configures accessibility and visual attributes for the placeholder\n */\nfunction configurePlaceholder(placeholder: Element, type = 'hidden'): void {\n  placeholder.setAttribute('inert', 'true');\n  placeholder.setAttribute('tab-index', '-1');\n  placeholder.setAttribute('aria-hidden', 'true');\n  placeholder.setAttribute(PLACEHOLDER_ATTRIBUTE, type);\n}\n\n/**\n * Checks if two elements are in the same frame context\n */\nexport function isSameFrame(element: Element, target: Element): boolean {\n  if (element === target) return true;\n  return getFrameElement(element) === getFrameElement(target);\n}\n\n/**\n * Prevent an element with the `popover` attribute from being closed\n */\nexport function preventPopoverClose(event: Event) {\n  const {target} = event;\n\n  if (\n    'newState' in event &&\n    event.newState === 'closed' &&\n    isElement(target) &&\n    target.hasAttribute('popover')\n  ) {\n    requestAnimationFrame(() => showPopover(target));\n  }\n}\n\nexport function isTableRow(element: Element): element is HTMLTableRowElement {\n  return element.tagName === 'TR';\n}\n", "import {\n  effects,\n  reactive,\n  untracked,\n  type CleanupFunction,\n} from '@dnd-kit/state';\nimport {configurator, Plugin} from '@dnd-kit/abstract';\nimport {\n  animateTransform,\n  DOMRectangle,\n  getComputedStyles,\n  getDocument,\n  getFinalKeyframe,\n  getFrameTransform,\n  getWindow,\n  isHTMLElement,\n  isKeyboardEvent,\n  parseTranslate,\n  showPopover,\n  Styles,\n  supportsPopover,\n  supportsStyle,\n} from '@dnd-kit/dom/utilities';\nimport {Coordinates, Point, Rectangle} from '@dnd-kit/geometry';\n\nimport type {DragDropManager} from '../../manager/index.ts';\nimport type {Draggable} from '../../entities/index.ts';\n\nimport {\n  ATTRIBUTE,\n  CSS_PREFIX,\n  CSS_RULES,\n  DROPPING_ATTRIBUTE,\n  IGNORED_ATTRIBUTES,\n  IGNORED_STYLES,\n} from './constants.ts';\nimport {\n  createPlaceholder,\n  isSameFrame,\n  isTableRow,\n  preventPopoverClose,\n} from './utilities.ts';\n\nexport interface FeedbackOptions {\n  rootElement?: Element | ((source: Draggable) => Element);\n}\n\ninterface State {\n  current: {\n    translate?: Coordinates;\n  };\n  initial: {\n    dimensions?: {width: number; height: number};\n    coordinates?: Coordinates;\n    frameTransform?: {x: number; y: number; scaleX: number; scaleY: number};\n    translate?: Coordinates;\n    transformOrigin?: Coordinates;\n  };\n}\n\ninterface StyleSheetRegistration {\n  cleanup: CleanupFunction;\n  instances: Set<Feedback>;\n}\n\nconst styleSheetRegistry = new Map<Document, StyleSheetRegistration>();\n\nexport class Feedback extends Plugin<DragDropManager, FeedbackOptions> {\n  @reactive\n  public accessor overlay: Element | undefined;\n\n  private state: State = {\n    initial: {},\n    current: {},\n  };\n\n  constructor(manager: DragDropManager, options?: FeedbackOptions) {\n    super(manager, options);\n\n    this.registerEffect(this.#injectStyles);\n    this.registerEffect(this.#render);\n  }\n\n  #render() {\n    const {state, manager, options} = this;\n    const {dragOperation} = manager;\n    const {position, source, status} = dragOperation;\n\n    if (status.idle) {\n      state.current = {};\n      state.initial = {};\n      return;\n    }\n\n    if (!source) return;\n\n    const {element, feedback} = source;\n\n    if (\n      !element ||\n      feedback === 'none' ||\n      !status.initialized ||\n      status.initializing\n    ) {\n      return;\n    }\n\n    const {initial} = state;\n    const feedbackElement = this.overlay ?? element;\n    const frameTransform = getFrameTransform(feedbackElement);\n    const elementFrameTransform = getFrameTransform(element);\n    const crossFrame = !isSameFrame(element, feedbackElement);\n    const shape = new DOMRectangle(element, {\n      frameTransform: crossFrame ? elementFrameTransform : null,\n      ignoreTransforms: !crossFrame,\n    });\n    const scaleDelta = {\n      x: elementFrameTransform.scaleX / frameTransform.scaleX,\n      y: elementFrameTransform.scaleY / frameTransform.scaleY,\n    };\n\n    let {width, height, top, left} = shape;\n\n    if (crossFrame) {\n      width = width / scaleDelta.x;\n      height = height / scaleDelta.y;\n    }\n\n    let elementMutationObserver: MutationObserver | undefined;\n    let documentMutationObserver: MutationObserver | undefined;\n    const styles = new Styles(feedbackElement);\n    const {\n      transition,\n      translate,\n      boxSizing,\n      paddingBlockStart,\n      paddingBlockEnd,\n      paddingInlineStart,\n      paddingInlineEnd,\n      borderInlineStartWidth,\n      borderInlineEndWidth,\n      borderBlockStartWidth,\n      borderBlockEndWidth,\n    } = getComputedStyles(element);\n    const clone = feedback === 'clone';\n    const contentBox = boxSizing === 'content-box';\n    const widthOffset = contentBox\n      ? parseInt(paddingInlineStart) +\n        parseInt(paddingInlineEnd) +\n        parseInt(borderInlineStartWidth) +\n        parseInt(borderInlineEndWidth)\n      : 0;\n    const heightOffset = contentBox\n      ? parseInt(paddingBlockStart) +\n        parseInt(paddingBlockEnd) +\n        parseInt(borderBlockStartWidth) +\n        parseInt(borderBlockEndWidth)\n      : 0;\n\n    const placeholder =\n      feedback !== 'move' && !this.overlay\n        ? createPlaceholder(source, clone ? 'clone' : 'hidden')\n        : null;\n    const isKeyboardOperation = untracked(() =>\n      isKeyboardEvent(manager.dragOperation.activatorEvent)\n    );\n\n    if (translate !== 'none') {\n      const parsedTranslate = parseTranslate(translate);\n\n      if (parsedTranslate && !initial.translate) {\n        initial.translate = parsedTranslate;\n      }\n    }\n\n    if (!initial.transformOrigin) {\n      const current = untracked(() => position.current);\n\n      initial.transformOrigin = {\n        x:\n          (current.x - left * frameTransform.scaleX - frameTransform.x) /\n          (width * frameTransform.scaleX),\n        y:\n          (current.y - top * frameTransform.scaleY - frameTransform.y) /\n          (height * frameTransform.scaleY),\n      };\n    }\n\n    const {transformOrigin} = initial;\n    const relativeTop = top * frameTransform.scaleY + frameTransform.y;\n    const relativeLeft = left * frameTransform.scaleX + frameTransform.x;\n\n    if (!initial.coordinates) {\n      initial.coordinates = {\n        x: relativeLeft,\n        y: relativeTop,\n      };\n\n      // Compoensate for transformOrigin when scaling\n      if (scaleDelta.x !== 1 || scaleDelta.y !== 1) {\n        const {scaleX, scaleY} = elementFrameTransform;\n        const {x: tX, y: tY} = transformOrigin;\n\n        initial.coordinates.x += (width * scaleX - width) * tX;\n        initial.coordinates.y += (height * scaleY - height) * tY;\n      }\n    }\n\n    if (!initial.dimensions) {\n      initial.dimensions = {width, height};\n    }\n\n    if (!initial.frameTransform) {\n      initial.frameTransform = frameTransform;\n    }\n\n    const coordinatesDelta = {\n      x: initial.coordinates.x - relativeLeft,\n      y: initial.coordinates.y - relativeTop,\n    };\n\n    const sizeDelta = {\n      width:\n        (initial.dimensions.width * initial.frameTransform.scaleX -\n          width * frameTransform.scaleX) *\n        transformOrigin.x,\n      height:\n        (initial.dimensions.height * initial.frameTransform.scaleY -\n          height * frameTransform.scaleY) *\n        transformOrigin.y,\n    };\n\n    const delta = {\n      x: coordinatesDelta.x / frameTransform.scaleX + sizeDelta.width,\n      y: coordinatesDelta.y / frameTransform.scaleY + sizeDelta.height,\n    };\n\n    const projected = {\n      left: left + delta.x,\n      top: top + delta.y,\n    };\n\n    feedbackElement.setAttribute(ATTRIBUTE, 'true');\n\n    const transform = untracked(() => dragOperation.transform);\n    const initialTranslate = initial.translate ?? {x: 0, y: 0};\n    const tX = transform.x * frameTransform.scaleX + initialTranslate.x;\n    const tY = transform.y * frameTransform.scaleY + initialTranslate.y;\n    const translateString = `${tX}px ${tY}px 0`;\n    const transitionString = transition\n      ? `${transition}, translate 0ms linear`\n      : '';\n\n    styles.set(\n      {\n        width: width - widthOffset,\n        height: height - heightOffset,\n        top: projected.top,\n        left: projected.left,\n        translate: translateString,\n        transition: transitionString,\n        scale: crossFrame ? `${scaleDelta.x} ${scaleDelta.y}` : '',\n        'transform-origin': `${transformOrigin.x * 100}% ${transformOrigin.y * 100}%`,\n      },\n      CSS_PREFIX\n    );\n\n    if (placeholder) {\n      element.insertAdjacentElement('afterend', placeholder);\n\n      if (options?.rootElement) {\n        const root =\n          typeof options.rootElement === 'function'\n            ? options.rootElement(source)\n            : options.rootElement;\n\n        root.appendChild(element);\n      }\n    }\n\n    if (supportsPopover(feedbackElement)) {\n      if (!feedbackElement.hasAttribute('popover')) {\n        feedbackElement.setAttribute('popover', 'manual');\n      }\n      showPopover(feedbackElement);\n      feedbackElement.addEventListener('beforetoggle', preventPopoverClose);\n    }\n\n    const resizeObserver = new ResizeObserver(() => {\n      if (!placeholder) return;\n\n      const placeholderShape = new DOMRectangle(placeholder, {\n        frameTransform,\n        ignoreTransforms: true,\n      });\n      const origin = transformOrigin ?? {x: 1, y: 1};\n      const dX = (width - placeholderShape.width) * origin.x + delta.x;\n      const dY = (height - placeholderShape.height) * origin.y + delta.y;\n\n      styles.set(\n        {\n          width: placeholderShape.width - widthOffset,\n          height: placeholderShape.height - heightOffset,\n          top: top + dY,\n          left: left + dX,\n        },\n        CSS_PREFIX\n      );\n      elementMutationObserver?.takeRecords();\n\n      /* Table cells need to have their width set explicitly because the feedback element is position fixed */\n      if (isTableRow(element) && isTableRow(placeholder)) {\n        const cells = Array.from(element.cells);\n        const placeholderCells = Array.from(placeholder.cells);\n\n        for (const [index, cell] of cells.entries()) {\n          const placeholderCell = placeholderCells[index];\n\n          cell.style.width = `${placeholderCell.offsetWidth}px`;\n        }\n      }\n\n      dragOperation.shape = new DOMRectangle(feedbackElement);\n    });\n\n    /* Initialize drag operation shape */\n    const initialShape = new DOMRectangle(feedbackElement);\n    untracked(() => (dragOperation.shape = initialShape));\n\n    const feedbackWindow = getWindow(feedbackElement);\n    const handleWindowResize = (event: Event) => {\n      this.manager.actions.stop({event});\n    };\n\n    if (isKeyboardOperation) {\n      feedbackWindow.addEventListener('resize', handleWindowResize);\n    }\n\n    if (untracked(() => source.status) === 'idle') {\n      requestAnimationFrame(() => (source.status = 'dragging'));\n    }\n\n    if (placeholder) {\n      resizeObserver.observe(placeholder);\n\n      elementMutationObserver = new MutationObserver((mutations) => {\n        let hasChildrenMutations = false;\n\n        for (const mutation of mutations) {\n          if (mutation.target !== element) {\n            hasChildrenMutations = true;\n            continue;\n          }\n\n          if (mutation.type !== 'attributes') {\n            // Should never happen, but defensive programming just in case\n            continue;\n          }\n\n          // Attribute name is guaranteed to be non-null if type is \"attributes\"\n          // https://developer.mozilla.org/en-US/docs/Web/API/MutationRecord/attributeName#value\n          const attributeName = mutation.attributeName!;\n\n          if (\n            attributeName.startsWith('aria-') ||\n            IGNORED_ATTRIBUTES.includes(attributeName)\n          ) {\n            continue;\n          }\n\n          const attributeValue = element.getAttribute(attributeName);\n\n          if (attributeName === 'style') {\n            if (supportsStyle(element) && supportsStyle(placeholder)) {\n              const styles = element.style;\n\n              for (const key of Array.from(placeholder.style)) {\n                if (styles.getPropertyValue(key) === '') {\n                  placeholder.style.removeProperty(key);\n                }\n              }\n\n              for (const key of Array.from(styles)) {\n                if (\n                  IGNORED_STYLES.includes(key) ||\n                  key.startsWith(CSS_PREFIX)\n                ) {\n                  continue;\n                }\n\n                const value = styles.getPropertyValue(key);\n\n                placeholder.style.setProperty(key, value);\n              }\n            }\n          } else if (attributeValue !== null) {\n            placeholder.setAttribute(attributeName, attributeValue);\n          } else {\n            placeholder.removeAttribute(attributeName);\n          }\n        }\n\n        if (hasChildrenMutations && clone) {\n          placeholder.innerHTML = element.innerHTML;\n        }\n      });\n\n      elementMutationObserver.observe(element, {\n        attributes: true,\n        subtree: true,\n        childList: true,\n      });\n\n      /* Make sure the placeholder and the source element positions are always in sync */\n      documentMutationObserver = new MutationObserver((entries) => {\n        for (const entry of entries) {\n          if (entry.addedNodes.length === 0) continue;\n\n          for (const node of Array.from(entry.addedNodes)) {\n            if (\n              node.contains(element) &&\n              element.nextElementSibling !== placeholder\n            ) {\n              /* Update the position of the placeholder when the source element is moved */\n              element.insertAdjacentElement('afterend', placeholder);\n              /* Force the source element to be promoted back to the top layer */\n              showPopover(feedbackElement);\n              return;\n            }\n\n            if (\n              node.contains(placeholder) &&\n              placeholder.previousElementSibling !== element\n            ) {\n              /* Update the position of the source element when the placeholder is moved */\n              placeholder.insertAdjacentElement('beforebegin', element);\n              /* Force the source element to be promoted back to the top layer */\n              showPopover(feedbackElement);\n              return;\n            }\n          }\n        }\n      });\n\n      /* Observe mutations on the element's owner document body */\n      documentMutationObserver.observe(element.ownerDocument.body, {\n        childList: true,\n        subtree: true,\n      });\n    }\n\n    const id = manager.dragOperation.source?.id;\n\n    const restoreFocus = () => {\n      if (!isKeyboardOperation || id == null) {\n        return;\n      }\n\n      const draggable = manager.registry.draggables.get(id);\n      const element = draggable?.handle ?? draggable?.element;\n\n      if (isHTMLElement(element)) {\n        element.focus();\n      }\n    };\n    const cleanup = () => {\n      elementMutationObserver?.disconnect();\n      documentMutationObserver?.disconnect();\n      resizeObserver.disconnect();\n      feedbackWindow.removeEventListener('resize', handleWindowResize);\n\n      if (supportsPopover(feedbackElement)) {\n        feedbackElement.removeEventListener(\n          'beforetoggle',\n          preventPopoverClose\n        );\n        feedbackElement.removeAttribute('popover');\n      }\n\n      feedbackElement.removeAttribute(ATTRIBUTE);\n      styles.reset();\n\n      source.status = 'idle';\n\n      const moved = state.current.translate != null;\n\n      if (\n        placeholder &&\n        (moved ||\n          placeholder.parentElement !== feedbackElement.parentElement) &&\n        feedbackElement.isConnected\n      ) {\n        placeholder.replaceWith(feedbackElement);\n      }\n\n      placeholder?.remove();\n    };\n\n    const cleanupEffects = effects(\n      // Update transform on move\n      () => {\n        const {transform, status} = dragOperation;\n\n        if (!transform.x && !transform.y && !state.current.translate) {\n          return;\n        }\n\n        if (status.dragging) {\n          const initialTranslate = initial.translate ?? {x: 0, y: 0};\n          const translate = {\n            x: transform.x / frameTransform.scaleX + initialTranslate.x,\n            y: transform.y / frameTransform.scaleY + initialTranslate.y,\n          };\n          const previousTranslate = state.current.translate;\n          const modifiers = untracked(() => dragOperation.modifiers);\n          const currentShape = untracked(() => dragOperation.shape?.current);\n          const translateTransition = isKeyboardOperation\n            ? '250ms cubic-bezier(0.25, 1, 0.5, 1)'\n            : '0ms linear';\n\n          styles.set(\n            {\n              transition: `${transition}, translate ${translateTransition}`,\n              translate: `${translate.x}px ${translate.y}px 0`,\n            },\n            CSS_PREFIX\n          );\n          elementMutationObserver?.takeRecords();\n\n          if (\n            currentShape &&\n            currentShape !== initialShape &&\n            previousTranslate &&\n            !modifiers.length\n          ) {\n            const delta = Point.delta(translate, previousTranslate);\n\n            dragOperation.shape = Rectangle.from(\n              currentShape.boundingRectangle\n            ).translate(\n              // Need to take into account frame transform when optimistically updating shape\n              delta.x * frameTransform.scaleX,\n              delta.y * frameTransform.scaleY\n            );\n          } else {\n            dragOperation.shape = new DOMRectangle(feedbackElement);\n          }\n\n          state.current.translate = translate;\n        }\n      },\n      // Drop animation\n      function () {\n        if (dragOperation.status.dropped) {\n          // Dispose of the effect\n          this.dispose();\n\n          source.status = 'dropping';\n\n          let translate = state.current.translate;\n          const moved = translate != null;\n\n          if (!translate && element !== feedbackElement) {\n            translate = {\n              x: 0,\n              y: 0,\n            };\n          }\n\n          if (!translate) {\n            cleanup();\n            return;\n          }\n\n          const dropAnimation = () => {\n            {\n              /* Force the source element to be promoted to the top layer before animating it */\n              showPopover(feedbackElement);\n\n              // Pause any translate transitions that are running on the feedback element\n              const [, animation] =\n                getFinalKeyframe(\n                  feedbackElement,\n                  (keyframe) => 'translate' in keyframe\n                ) ?? [];\n\n              animation?.pause();\n\n              const target = placeholder ?? element;\n              const options = {\n                frameTransform: isSameFrame(feedbackElement, target)\n                  ? null\n                  : undefined,\n              };\n              const current = new DOMRectangle(feedbackElement, options);\n              // With a keyboard activator, since there is a transition on the translate property,\n              // the translate value may not be the same as the computed value if the transition is still running.\n              const currentTranslate =\n                parseTranslate(getComputedStyles(feedbackElement).translate) ??\n                translate;\n              const final = new DOMRectangle(target, options);\n              const delta = Rectangle.delta(current, final, source.alignment);\n              const finalTranslate = {\n                x: currentTranslate.x - delta.x,\n                y: currentTranslate.y - delta.y,\n              };\n              const heightKeyframes =\n                Math.round(current.intrinsicHeight) !==\n                Math.round(final.intrinsicHeight)\n                  ? {\n                      minHeight: [\n                        `${current.intrinsicHeight}px`,\n                        `${final.intrinsicHeight}px`,\n                      ],\n                      maxHeight: [\n                        `${current.intrinsicHeight}px`,\n                        `${final.intrinsicHeight}px`,\n                      ],\n                    }\n                  : {};\n              const widthKeyframes =\n                Math.round(current.intrinsicWidth) !==\n                Math.round(final.intrinsicWidth)\n                  ? {\n                      minWidth: [\n                        `${current.intrinsicWidth}px`,\n                        `${final.intrinsicWidth}px`,\n                      ],\n                      maxWidth: [\n                        `${current.intrinsicWidth}px`,\n                        `${final.intrinsicWidth}px`,\n                      ],\n                    }\n                  : {};\n\n              styles.set({transition}, CSS_PREFIX);\n              feedbackElement.setAttribute(DROPPING_ATTRIBUTE, '');\n              elementMutationObserver?.takeRecords();\n\n              animateTransform({\n                element: feedbackElement,\n                keyframes: {\n                  ...heightKeyframes,\n                  ...widthKeyframes,\n                  translate: [\n                    `${currentTranslate.x}px ${currentTranslate.y}px 0`,\n                    `${finalTranslate.x}px ${finalTranslate.y}px 0`,\n                  ],\n                },\n                options: {\n                  duration: moved || feedbackElement !== element ? 250 : 0,\n                  easing: 'ease',\n                },\n              }).then(() => {\n                feedbackElement.removeAttribute(DROPPING_ATTRIBUTE);\n                animation?.finish();\n                cleanup();\n                requestAnimationFrame(restoreFocus);\n              });\n            }\n          };\n\n          manager.renderer.rendering.then(dropAnimation);\n        }\n      }\n    );\n\n    return () => {\n      cleanup();\n      cleanupEffects();\n    };\n  }\n\n  #injectStyles() {\n    const {status, source, target} = this.manager.dragOperation;\n\n    if (status.initializing) {\n      const sourceDocument = getDocument(source?.element ?? null);\n      const targetDocument = getDocument(target?.element ?? null);\n      const documents = new Set([sourceDocument, targetDocument]);\n\n      for (const doc of documents) {\n        let registration = styleSheetRegistry.get(doc);\n\n        if (!registration) {\n          const style = document.createElement('style');\n          style.textContent = CSS_RULES;\n          doc.head.prepend(style);\n          const mutationObserver = new MutationObserver((entries) => {\n            for (const entry of entries) {\n              if (entry.type === 'childList') {\n                const removedNodes = Array.from(entry.removedNodes);\n\n                if (removedNodes.length > 0 && removedNodes.includes(style)) {\n                  // Re-inject the style tag if it gets removed from the DOM\n                  doc.head.prepend(style);\n                }\n              }\n            }\n          });\n          mutationObserver.observe(doc.head, {childList: true});\n\n          registration = {\n            cleanup: () => {\n              mutationObserver.disconnect();\n              style.remove();\n            },\n            instances: new Set(),\n          };\n          styleSheetRegistry.set(doc, registration);\n        }\n\n        // Track this instance for this document\n        registration.instances.add(this);\n      }\n    }\n  }\n\n  public destroy(): void {\n    super.destroy();\n\n    // Clean up documents this instance was tracking\n    for (const [doc, registration] of styleSheetRegistry.entries()) {\n      if (registration.instances.has(this)) {\n        registration.instances.delete(this);\n\n        // If no more instances are using this document, clean it up\n        if (registration.instances.size === 0) {\n          registration.cleanup();\n          styleSheetRegistry.delete(doc);\n        }\n      }\n    }\n  }\n\n  static configure = configurator(Feedback);\n}\n", "import {reactive} from '@dnd-kit/state';\nimport {ScrollDirection as Direction} from '@dnd-kit/dom/utilities';\n\nconst LOCKED = true;\nconst UNLOCKED = false;\n\nexport class ScrollLock {\n  @reactive private accessor [Direction.Forward] = LOCKED;\n  @reactive private accessor [Direction.Reverse] = LOCKED;\n\n  public isLocked(direction?: Direction): boolean {\n    if (direction === Direction.Idle) {\n      return false;\n    }\n\n    if (direction == null) {\n      return (\n        this[Direction.Forward] === LOCKED && this[Direction.Reverse] === LOCKED\n      );\n    }\n\n    return this[direction] === LOCKED;\n  }\n\n  public unlock(direction: Direction) {\n    if (direction === Direction.Idle) {\n      return;\n    }\n\n    this[direction] = UNLOCKED;\n  }\n}\n", "import {batch, effect, signal, type Signal} from '@dnd-kit/state';\nimport {Plugin} from '@dnd-kit/abstract';\nimport {Axes} from '@dnd-kit/geometry';\nimport type {Coordinates} from '@dnd-kit/geometry';\nimport {ScrollDirection} from '@dnd-kit/dom/utilities';\n\nimport type {DragDropManager} from '../../manager/index.ts';\n\nimport {ScrollLock} from './ScrollLock.ts';\n\nconst DIRECTIONS = [ScrollDirection.Forward, ScrollDirection.Reverse];\n\nclass ScrollIntent {\n  public x = new ScrollLock();\n  public y = new ScrollLock();\n\n  public isLocked(): boolean {\n    return this.x.isLocked() && this.y.isLocked();\n  }\n}\n\nexport class ScrollIntentTracker extends Plugin<DragDropManager> {\n  private signal: Signal<ScrollIntent | null>;\n\n  constructor(manager: DragDropManager) {\n    super(manager);\n\n    const scrollIntent = signal<ScrollIntent>(new ScrollIntent());\n    let previousDelta: Coordinates | null = null;\n\n    this.signal = scrollIntent;\n\n    effect(() => {\n      const {status} = manager.dragOperation;\n\n      if (!status.initialized) {\n        previousDelta = null;\n        scrollIntent.value = new ScrollIntent();\n        return;\n      }\n\n      const {delta} = manager.dragOperation.position;\n\n      if (previousDelta) {\n        const directions = {\n          x: getDirection(delta.x, previousDelta.x),\n          y: getDirection(delta.y, previousDelta.y),\n        };\n\n        const intent = scrollIntent.peek();\n\n        batch(() => {\n          for (const axis of Axes) {\n            for (const direction of DIRECTIONS) {\n              if (directions[axis] === direction) {\n                intent[axis].unlock(direction);\n              }\n            }\n          }\n\n          scrollIntent.value = intent;\n        });\n      }\n\n      previousDelta = delta;\n    });\n  }\n\n  get current(): ScrollIntent | null {\n    return this.signal.peek();\n  }\n}\n\nfunction getDirection(a: number, b: number): ScrollDirection {\n  return Math.sign(a - b);\n}\n", "import {CorePlugin} from '@dnd-kit/abstract';\nimport {computed, deepEqual, reactive} from '@dnd-kit/state';\nimport {\n  canScroll,\n  detectScrollIntent,\n  getScrollableAncestors,\n  getElementFromPoint,\n  ScrollDirection,\n  scheduler,\n  isKeyboardEvent,\n  getDocument,\n  getFrameTransform,\n} from '@dnd-kit/dom/utilities';\nimport {Axes, type Coordinates} from '@dnd-kit/geometry';\n\nimport type {DragDropManager} from '../../manager/index.ts';\n\nimport {ScrollIntentTracker} from './ScrollIntent.ts';\n\nexport class Scroller extends CorePlugin<DragDropManager> {\n  public getScrollableElements: () => Set<Element> | null;\n\n  private scrollIntentTracker: ScrollIntentTracker;\n\n  @reactive\n  public accessor autoScrolling = false;\n\n  constructor(manager: DragDropManager) {\n    super(manager);\n\n    let previousElementFromPoint: Element | null = null;\n    let previousScrollableElements: Set<Element> | null = null;\n    const elementFromPoint = computed(() => {\n      const {position, source} = manager.dragOperation;\n\n      if (!position) {\n        return null;\n      }\n\n      const element = getElementFromPoint(\n        getDocument(source?.element),\n        position.current\n      );\n\n      if (element) {\n        previousElementFromPoint = element;\n      }\n\n      return element ?? previousElementFromPoint;\n    });\n    const scrollableElements = computed(() => {\n      const element = elementFromPoint.value;\n      const {documentElement} = getDocument(element);\n\n      if (!element || element === documentElement) {\n        const {target} = manager.dragOperation;\n        const targetElement = target?.element;\n\n        if (targetElement) {\n          const elements = getScrollableAncestors(targetElement, {\n            excludeElement: false,\n          });\n          previousScrollableElements = elements;\n\n          return elements;\n        }\n      }\n\n      if (element) {\n        const elements = getScrollableAncestors(element, {\n          excludeElement: false,\n        });\n\n        if (\n          this.autoScrolling &&\n          previousScrollableElements &&\n          elements.size < previousScrollableElements?.size\n        ) {\n          return previousScrollableElements;\n        }\n\n        previousScrollableElements = elements;\n\n        return elements;\n      }\n\n      previousScrollableElements = null;\n\n      return null;\n    }, deepEqual);\n\n    this.getScrollableElements = () => {\n      return scrollableElements.value;\n    };\n\n    this.scrollIntentTracker = new ScrollIntentTracker(manager);\n\n    this.destroy = manager.monitor.addEventListener('dragmove', (event) => {\n      if (\n        this.disabled ||\n        event.defaultPrevented ||\n        !isKeyboardEvent(manager.dragOperation.activatorEvent) ||\n        !event.by\n      ) {\n        return;\n      }\n\n      // Prevent the move event if we can scroll to the new coordinates\n      if (this.scroll({by: event.by})) {\n        event.preventDefault();\n      }\n    });\n  }\n\n  #meta: {element: Element; by: Coordinates} | undefined;\n\n  #scroll = () => {\n    if (!this.#meta) {\n      return;\n    }\n\n    const {element, by} = this.#meta;\n\n    if (by.y) element.scrollTop += by.y;\n    if (by.x) element.scrollLeft += by.x;\n  };\n\n  public scroll = (options?: {by: Coordinates}): boolean => {\n    if (this.disabled) {\n      return false;\n    }\n\n    const elements = this.getScrollableElements();\n\n    if (!elements) {\n      this.#meta = undefined;\n      return false;\n    }\n\n    const {position} = this.manager.dragOperation;\n    const currentPosition = position?.current;\n\n    if (currentPosition) {\n      const {by} = options ?? {};\n      const intent = by\n        ? {\n            x: getScrollIntent(by.x),\n            y: getScrollIntent(by.y),\n          }\n        : undefined;\n      const scrollIntent = intent\n        ? undefined\n        : this.scrollIntentTracker.current;\n\n      if (scrollIntent?.isLocked()) {\n        return false;\n      }\n\n      for (const scrollableElement of elements) {\n        const elementCanScroll = canScroll(scrollableElement, by);\n\n        if (elementCanScroll.x || elementCanScroll.y) {\n          const {speed, direction} = detectScrollIntent(\n            scrollableElement,\n            currentPosition,\n            intent\n          );\n\n          if (scrollIntent) {\n            for (const axis of Axes) {\n              if (scrollIntent[axis].isLocked(direction[axis])) {\n                speed[axis] = 0;\n                direction[axis] = 0;\n              }\n            }\n          }\n\n          if (direction.x || direction.y) {\n            const {x, y} = by ?? direction;\n            const scrollLeftBy = x * speed.x;\n            const scrollTopBy = y * speed.y;\n\n            if (scrollLeftBy || scrollTopBy) {\n              const previousScrollBy = this.#meta?.by;\n\n              if (this.autoScrolling && previousScrollBy) {\n                const scrollIntentMismatch =\n                  (previousScrollBy.x && !scrollLeftBy) ||\n                  (previousScrollBy.y && !scrollTopBy);\n\n                if (scrollIntentMismatch) continue;\n              }\n\n              this.#meta = {\n                element: scrollableElement,\n                by: {\n                  x: scrollLeftBy,\n                  y: scrollTopBy,\n                },\n              };\n\n              scheduler.schedule(this.#scroll);\n\n              return true;\n            }\n          }\n        }\n      }\n    }\n\n    this.#meta = undefined;\n    return false;\n  };\n}\n\nfunction getScrollIntent(value: number) {\n  if (value > 0) {\n    return ScrollDirection.Forward;\n  }\n\n  if (value < 0) {\n    return ScrollDirection.Reverse;\n  }\n\n  return ScrollDirection.Idle;\n}\n", "type Callback = () => void;\n\nexport class Scheduler<T extends (callback: Callback) => any> {\n  constructor(private scheduler: T) {}\n\n  private pending: boolean = false;\n  private tasks: Set<() => void> = new Set();\n  private resolvers: Set<() => void> = new Set();\n\n  public schedule(task: () => void): Promise<void> {\n    this.tasks.add(task);\n\n    if (!this.pending) {\n      this.pending = true;\n      this.scheduler(this.flush);\n    }\n\n    return new Promise<void>((resolve) => this.resolvers.add(resolve));\n  }\n\n  public flush = () => {\n    const {tasks, resolvers} = this;\n\n    this.pending = false;\n    this.tasks = new Set();\n    this.resolvers = new Set();\n\n    for (const task of tasks) {\n      task();\n    }\n\n    for (const resolve of resolvers) {\n      resolve();\n    }\n  };\n}\n\nexport const scheduler = new Scheduler((callback) => {\n  if (typeof requestAnimationFrame === 'function') {\n    requestAnimationFrame(callback);\n  } else {\n    callback();\n  }\n});\n", "import {Plugin} from '@dnd-kit/abstract';\nimport {effect} from '@dnd-kit/state';\nimport type {CleanupFunction} from '@dnd-kit/state';\n\nimport type {DragDropManager} from '../../manager/index.ts';\nimport {Scroller} from './Scroller.ts';\nimport {scheduler} from '../../../utilities/scheduling/scheduler.ts';\n\ninterface Options {}\n\nconst AUTOSCROLL_INTERVAL = 10;\n\nexport class AutoScroller extends Plugin<DragDropManager> {\n  public destroy: CleanupFunction;\n\n  constructor(manager: DragDropManager, _options?: Options) {\n    super(manager);\n\n    const scroller = manager.registry.plugins.get(Scroller);\n\n    if (!scroller) {\n      throw new Error('AutoScroller plugin depends on Scroller plugin');\n    }\n\n    this.destroy = effect(() => {\n      if (this.disabled) {\n        return;\n      }\n\n      // We consume the position from the drag operation\n      // so that this effect is run when the position changes\n      const {position: _, status} = manager.dragOperation;\n\n      if (status.dragging) {\n        const canScroll = scroller.scroll();\n\n        if (canScroll) {\n          scroller.autoScrolling = true;\n          const interval = setInterval(\n            () => scheduler.schedule(scroller.scroll),\n            AUTOSCROLL_INTERVAL\n          );\n\n          return () => {\n            clearInterval(interval);\n          };\n        } else {\n          scroller.autoScrolling = false;\n        }\n      }\n    });\n  }\n}\n", "import {CorePlugin} from '@dnd-kit/abstract';\nimport {effect} from '@dnd-kit/state';\n\nimport type {DragDropManager} from '../../manager/index.ts';\n\nconst listenerOptions: AddEventListenerOptions = {\n  capture: true,\n  passive: true,\n};\n\nexport class ScrollListener extends CorePlugin<DragDropManager> {\n  #timeout: NodeJS.Timeout | undefined;\n\n  constructor(manager: DragDropManager) {\n    super(manager);\n\n    const {dragOperation} = this.manager;\n\n    this.destroy = effect(() => {\n      const enabled = dragOperation.status.dragging;\n\n      if (enabled) {\n        const root = dragOperation.source?.element?.ownerDocument ?? document;\n\n        root.addEventListener('scroll', this.handleScroll, listenerOptions);\n\n        return () => {\n          root.removeEventListener(\n            'scroll',\n            this.handleScroll,\n            listenerOptions\n          );\n        };\n      }\n    });\n  }\n\n  private handleScroll = () => {\n    if (this.#timeout == null) {\n      this.#timeout = setTimeout(() => {\n        this.manager.collisionObserver.forceUpdate(false);\n        this.#timeout = undefined;\n      }, 50);\n    }\n  };\n}\n", "import {Plugin} from '@dnd-kit/abstract';\nimport {effect} from '@dnd-kit/state';\n\nimport {DragDropManager} from '../../manager/index.ts';\n\nexport class PreventSelection extends Plugin<DragDropManager> {\n  constructor(public manager: DragDropManager) {\n    super(manager);\n\n    this.destroy = effect(() => {\n      const {dragOperation} = this.manager;\n\n      if (dragOperation.status.initialized) {\n        const style = document.createElement('style');\n        style.textContent = `* { user-select: none !important; -webkit-user-select: none !important; }`;\n        document.head.appendChild(style);\n\n        removeSelection();\n        document.addEventListener('selectionchange', removeSelection, {\n          capture: true,\n        });\n\n        return () => {\n          document.removeEventListener('selectionchange', removeSelection, {\n            capture: true,\n          });\n          style.remove();\n        };\n      }\n    });\n  }\n}\n\nfunction removeSelection() {\n  document.getSelection()?.removeAllRanges();\n}\n", "import {configurator, Sensor} from '@dnd-kit/abstract';\nimport {effect} from '@dnd-kit/state';\nimport type {CleanupFunction} from '@dnd-kit/state';\nimport {\n  getDocument,\n  isElement,\n  isKeyboardEvent,\n  scrollIntoViewIfNeeded,\n  Listeners,\n  DOMRectangle,\n} from '@dnd-kit/dom/utilities';\n\nimport type {DragDropManager} from '../../manager/index.ts';\nimport type {Draggable} from '../../entities/index.ts';\nimport {AutoScroller} from '../../plugins/index.ts';\n\nexport type KeyCode = KeyboardEvent['code'];\n\nexport type KeyboardCodes = {\n  start: KeyCode[];\n  cancel: KeyCode[];\n  end: KeyCode[];\n  up: KeyCode[];\n  down: KeyCode[];\n  left: KeyCode[];\n  right: KeyCode[];\n};\n\nexport interface KeyboardSensorOptions {\n  /**\n   * The offset by which the keyboard sensor should move the draggable.\n   *\n   * @default 10\n   */\n  offset?: number | {x: number; y: number};\n  /**\n   * The keyboard codes that activate the keyboard sensor.\n   *\n   * @default {\n   *   start: ['Space', 'Enter'],\n   *   cancel: ['Escape'],\n   *   end: ['Space', 'Enter', 'Tab'],\n   *   up: ['ArrowUp'],\n   *   down: ['ArrowDown'],\n   *   left: ['ArrowLeft'],\n   *   right: ['ArrowRight']\n   * }\n   */\n  keyboardCodes?: KeyboardCodes;\n  /**\n   * Function that determines if the keyboard sensor should activate.\n   */\n  shouldActivate?(args: {\n    event: KeyboardEvent;\n    source: Draggable;\n    manager: DragDropManager;\n  }): boolean;\n}\n\nconst defaults = Object.freeze<Required<KeyboardSensorOptions>>({\n  offset: 10,\n  keyboardCodes: {\n    start: ['Space', 'Enter'],\n    cancel: ['Escape'],\n    end: ['Space', 'Enter', 'Tab'],\n    up: ['ArrowUp'],\n    down: ['ArrowDown'],\n    left: ['ArrowLeft'],\n    right: ['ArrowRight'],\n  },\n  shouldActivate(args: {\n    event: KeyboardEvent;\n    source: Draggable;\n    manager: DragDropManager;\n  }) {\n    const {event, source} = args;\n    const target = source.handle ?? source.element;\n    return event.target === target;\n  },\n});\n\n/**\n * The KeyboardSensor class is an input sensor that handles Keyboard events.\n */\nexport class KeyboardSensor extends Sensor<\n  DragDropManager,\n  KeyboardSensorOptions\n> {\n  constructor(\n    public manager: DragDropManager,\n    public options?: KeyboardSensorOptions\n  ) {\n    super(manager);\n  }\n\n  #cleanupFunctions: CleanupFunction[] = [];\n\n  protected listeners = new Listeners();\n\n  public bind(source: Draggable, options = this.options) {\n    const unbind = effect(() => {\n      const target = source.handle ?? source.element;\n      const listener: EventListener = (event: Event) => {\n        if (isKeyboardEvent(event)) {\n          this.handleSourceKeyDown(event, source, options);\n        }\n      };\n\n      if (target) {\n        target.addEventListener('keydown', listener);\n\n        return () => {\n          target.removeEventListener('keydown', listener);\n        };\n      }\n    });\n\n    return unbind;\n  }\n\n  protected handleSourceKeyDown = (\n    event: KeyboardEvent,\n    source: Draggable,\n    options: KeyboardSensorOptions | undefined\n  ) => {\n    if (this.disabled || event.defaultPrevented) {\n      return;\n    }\n\n    if (!isElement(event.target)) {\n      return;\n    }\n\n    if (source.disabled) {\n      return;\n    }\n\n    const {\n      keyboardCodes = defaults.keyboardCodes,\n      shouldActivate = defaults.shouldActivate,\n    } = options ?? {};\n\n    if (!keyboardCodes.start.includes(event.code)) {\n      return;\n    }\n\n    if (!this.manager.dragOperation.status.idle) {\n      return;\n    }\n\n    if (shouldActivate({event, source, manager: this.manager})) {\n      this.handleStart(event, source, options);\n    }\n  };\n\n  protected handleStart(\n    event: KeyboardEvent,\n    source: Draggable,\n    options: KeyboardSensorOptions | undefined\n  ) {\n    const {element} = source;\n\n    if (!element) {\n      throw new Error('Source draggable does not have an associated element');\n    }\n\n    event.preventDefault();\n    event.stopImmediatePropagation();\n\n    scrollIntoViewIfNeeded(element);\n\n    const {center} = new DOMRectangle(element);\n    const controller = this.manager.actions.start({\n      event,\n      coordinates: {\n        x: center.x,\n        y: center.y,\n      },\n      source,\n    });\n\n    if (controller.signal.aborted) return this.cleanup();\n\n    this.sideEffects();\n\n    const sourceDocument = getDocument(element);\n    const listeners = [\n      this.listeners.bind(sourceDocument, [\n        {\n          type: 'keydown',\n          listener: (event: KeyboardEvent) =>\n            this.handleKeyDown(event, source, options),\n          options: {capture: true},\n        },\n      ]),\n    ];\n\n    this.#cleanupFunctions.push(...listeners);\n  }\n\n  protected handleKeyDown(\n    event: KeyboardEvent,\n    _source: Draggable,\n    options: KeyboardSensorOptions | undefined\n  ) {\n    const {keyboardCodes = defaults.keyboardCodes} = options ?? {};\n\n    if (isKeycode(event, [...keyboardCodes.end, ...keyboardCodes.cancel])) {\n      event.preventDefault();\n      const canceled = isKeycode(event, keyboardCodes.cancel);\n\n      this.handleEnd(event, canceled);\n      return;\n    }\n\n    if (isKeycode(event, keyboardCodes.up)) {\n      this.handleMove('up', event);\n    } else if (isKeycode(event, keyboardCodes.down)) {\n      this.handleMove('down', event);\n    }\n\n    if (isKeycode(event, keyboardCodes.left)) {\n      this.handleMove('left', event);\n    } else if (isKeycode(event, keyboardCodes.right)) {\n      this.handleMove('right', event);\n    }\n  }\n\n  protected handleEnd(event: Event, canceled: boolean) {\n    this.manager.actions.stop({\n      event,\n      canceled,\n    });\n\n    this.cleanup();\n  }\n\n  protected handleMove(\n    direction: 'up' | 'down' | 'left' | 'right',\n    event: KeyboardEvent\n  ) {\n    const {shape} = this.manager.dragOperation;\n    const factor = event.shiftKey ? 5 : 1;\n    let by = {\n      x: 0,\n      y: 0,\n    };\n    let offset = this.options?.offset ?? defaults.offset;\n\n    if (typeof offset === 'number') {\n      offset = {x: offset, y: offset};\n    }\n\n    if (!shape) {\n      return;\n    }\n\n    switch (direction) {\n      case 'up':\n        by = {x: 0, y: -offset.y * factor};\n        break;\n      case 'down':\n        by = {x: 0, y: offset.y * factor};\n        break;\n      case 'left':\n        by = {x: -offset.x * factor, y: 0};\n        break;\n      case 'right':\n        by = {x: offset.x * factor, y: 0};\n        break;\n    }\n\n    if (by.x || by.y) {\n      event.preventDefault();\n\n      this.manager.actions.move({\n        event,\n        by,\n      });\n    }\n  }\n\n  private sideEffects() {\n    const autoScroller = this.manager.registry.plugins.get(AutoScroller as any);\n\n    if (autoScroller?.disabled === false) {\n      autoScroller.disable();\n\n      this.#cleanupFunctions.push(() => {\n        autoScroller.enable();\n      });\n    }\n  }\n\n  protected cleanup() {\n    this.#cleanupFunctions.forEach((cleanup) => cleanup());\n    this.#cleanupFunctions = [];\n  }\n\n  public destroy() {\n    this.cleanup();\n    // Remove all event listeners\n    this.listeners.clear();\n  }\n\n  static configure = configurator(KeyboardSensor);\n\n  static defaults = defaults;\n}\n\nfunction isKeycode(event: KeyboardEvent, codes: KeyCode[]) {\n  return codes.includes(event.code);\n}\n", "import {effect} from '@dnd-kit/state';\nimport type {CleanupFunction} from '@dnd-kit/state';\nimport {Sensor, configurator} from '@dnd-kit/abstract';\nimport {\n  exceedsDistance,\n  type Distance,\n  type Coordinates,\n} from '@dnd-kit/geometry';\nimport {\n  getDocument,\n  isElement,\n  isHTMLElement,\n  isPointerEvent,\n  Listeners,\n  getFrameTransform,\n  scheduler,\n  isTextInput,\n} from '@dnd-kit/dom/utilities';\n\nimport type {DragDropManager} from '../../manager/index.ts';\nimport type {Draggable} from '../../entities/index.ts';\n\nexport interface DelayConstraint {\n  value: number;\n  tolerance: Distance;\n}\n\nexport interface DistanceConstraint {\n  value: Distance;\n  tolerance?: Distance;\n}\n\nexport interface ActivationConstraints {\n  distance?: DistanceConstraint;\n  delay?: DelayConstraint;\n}\n\ntype Maybe<T> = T | undefined;\n\nexport interface PointerSensorOptions {\n  activationConstraints?:\n    | ActivationConstraints\n    | ((\n        event: PointerEvent,\n        source: Draggable\n      ) => ActivationConstraints | undefined);\n  activatorElements?:\n    | Maybe<Element>[]\n    | ((source: Draggable) => Maybe<Element>[]);\n}\n\nconst defaults = Object.freeze<PointerSensorOptions>({\n  activationConstraints(event, source) {\n    const {pointerType, target} = event;\n\n    if (\n      pointerType === 'mouse' &&\n      isElement(target) &&\n      (source.handle === target || source.handle?.contains(target))\n    ) {\n      return undefined;\n    }\n\n    if (pointerType === 'touch') {\n      return {\n        delay: {value: 250, tolerance: 5},\n      };\n    }\n\n    if (isTextInput(target) && !event.defaultPrevented) {\n      return {\n        delay: {value: 200, tolerance: 0},\n      };\n    }\n\n    return {\n      delay: {value: 200, tolerance: 10},\n      distance: {value: 5},\n    };\n  },\n});\n\ntype LatestState = {\n  event: PointerEvent | undefined;\n  coordinates: Coordinates | undefined;\n};\n\n/**\n * The PointerSensor class is an input sensor that handles Pointer events,\n * such as mouse, touch and pen interactions.\n */\nexport class PointerSensor extends Sensor<\n  DragDropManager,\n  PointerSensorOptions\n> {\n  #cleanup: Set<CleanupFunction> = new Set();\n\n  #clearTimeout: CleanupFunction | undefined;\n\n  protected listeners = new Listeners();\n\n  protected initialCoordinates: Coordinates | undefined;\n\n  constructor(\n    public manager: DragDropManager,\n    public options?: PointerSensorOptions\n  ) {\n    super(manager);\n\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handlePointerUp = this.handlePointerUp.bind(this);\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n  }\n\n  protected activationConstraints(event: PointerEvent, source: Draggable) {\n    const {activationConstraints = defaults.activationConstraints} =\n      this.options ?? {};\n\n    const constraints =\n      typeof activationConstraints === 'function'\n        ? activationConstraints(event, source)\n        : activationConstraints;\n\n    return constraints;\n  }\n\n  public bind(source: Draggable, options = this.options) {\n    const unbind = effect(() => {\n      const controller = new AbortController();\n      const {signal} = controller;\n      const listener: EventListener = (event: Event) => {\n        if (isPointerEvent(event)) {\n          this.handlePointerDown(event, source, options);\n        }\n      };\n      let targets = [source.handle ?? source.element];\n\n      if (options?.activatorElements) {\n        if (Array.isArray(options.activatorElements)) {\n          targets = options.activatorElements;\n        } else {\n          targets = options.activatorElements(source);\n        }\n      }\n\n      for (const target of targets) {\n        if (!target) continue;\n\n        patchWindow(target.ownerDocument.defaultView);\n        target.addEventListener('pointerdown', listener, {signal});\n      }\n\n      return () => controller.abort();\n    });\n\n    return unbind;\n  }\n\n  protected handlePointerDown(\n    event: PointerEvent,\n    source: Draggable,\n    options: PointerSensorOptions = {}\n  ) {\n    if (\n      this.disabled ||\n      !event.isPrimary ||\n      event.button !== 0 ||\n      !isElement(event.target) ||\n      source.disabled ||\n      isCapturedBySensor(event) ||\n      !this.manager.dragOperation.status.idle\n    ) {\n      return;\n    }\n\n    const {target} = event;\n    const isNativeDraggable =\n      isHTMLElement(target) &&\n      target.draggable &&\n      target.getAttribute('draggable') === 'true';\n\n    const offset = getFrameTransform(source.element);\n\n    this.initialCoordinates = {\n      x: event.clientX * offset.scaleX + offset.x,\n      y: event.clientY * offset.scaleY + offset.y,\n    };\n\n    const constraints = this.activationConstraints(event, source);\n\n    (event as any).sensor = this;\n\n    if (!constraints?.delay && !constraints?.distance) {\n      this.handleStart(source, event);\n    } else {\n      const {delay} = constraints;\n\n      if (delay) {\n        const timeout = setTimeout(\n          () => this.handleStart(source, event),\n          delay.value\n        );\n\n        this.#clearTimeout = () => {\n          clearTimeout(timeout);\n          this.#clearTimeout = undefined;\n        };\n      }\n    }\n\n    const ownerDocument = getDocument(event.target);\n\n    const unbindListeners = this.listeners.bind(ownerDocument, [\n      {\n        type: 'pointermove',\n        listener: (event: PointerEvent) =>\n          this.handlePointerMove(event, source),\n      },\n      {\n        type: 'pointerup',\n        listener: this.handlePointerUp,\n        options: {\n          capture: true,\n        },\n      },\n      {\n        // Cancel activation if there is a competing Drag and Drop interaction\n        type: 'dragstart',\n        listener: isNativeDraggable ? this.handleCancel : preventDefault,\n        options: {\n          capture: true,\n        },\n      },\n    ]);\n\n    const cleanup = () => {\n      unbindListeners();\n      this.#clearTimeout?.();\n      this.initialCoordinates = undefined;\n    };\n\n    this.#cleanup.add(cleanup);\n  }\n\n  private latest: LatestState = {\n    event: undefined,\n    coordinates: undefined,\n  };\n\n  protected handleMove = () => {\n    const {event, coordinates: to} = this.latest;\n\n    if (!event || !to) {\n      return;\n    }\n\n    this.manager.actions.move({event, to});\n  };\n\n  protected handlePointerMove(event: PointerEvent, source: Draggable) {\n    const coordinates = {\n      x: event.clientX,\n      y: event.clientY,\n    };\n\n    const offset = getFrameTransform(source.element);\n\n    coordinates.x = coordinates.x * offset.scaleX + offset.x;\n    coordinates.y = coordinates.y * offset.scaleY + offset.y;\n\n    if (this.manager.dragOperation.status.dragging) {\n      event.preventDefault();\n      event.stopPropagation();\n\n      this.latest.event = event;\n      this.latest.coordinates = coordinates;\n\n      scheduler.schedule(this.handleMove);\n      return;\n    }\n\n    if (!this.initialCoordinates) {\n      return;\n    }\n\n    const delta = {\n      x: coordinates.x - this.initialCoordinates.x,\n      y: coordinates.y - this.initialCoordinates.y,\n    };\n    const constraints = this.activationConstraints(event, source);\n    const {distance, delay} = constraints ?? {};\n\n    if (distance) {\n      if (\n        distance.tolerance != null &&\n        exceedsDistance(delta, distance.tolerance)\n      ) {\n        return this.handleCancel(event);\n      }\n      if (exceedsDistance(delta, distance.value)) {\n        return this.handleStart(source, event);\n      }\n    }\n\n    if (delay) {\n      if (exceedsDistance(delta, delay.tolerance)) {\n        return this.handleCancel(event);\n      }\n    }\n  }\n\n  private handlePointerUp(event: PointerEvent) {\n    // End the drag and drop operation\n    const {status} = this.manager.dragOperation;\n\n    if (!status.idle) {\n      // Prevent the default behaviour of the event\n      event.preventDefault();\n      event.stopPropagation();\n\n      const canceled = !status.initialized;\n      this.manager.actions.stop({event, canceled});\n    }\n\n    this.cleanup();\n  }\n\n  protected handleKeyDown(event: KeyboardEvent) {\n    if (event.key === 'Escape') {\n      event.preventDefault();\n      this.handleCancel(event);\n    }\n  }\n\n  protected handleStart(source: Draggable, event: PointerEvent) {\n    const {manager, initialCoordinates} = this;\n\n    this.#clearTimeout?.();\n\n    if (!initialCoordinates || !manager.dragOperation.status.idle) {\n      return;\n    }\n\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    const controller = manager.actions.start({\n      coordinates: initialCoordinates,\n      event,\n      source,\n    });\n\n    if (controller.signal.aborted) return this.cleanup();\n\n    event.preventDefault();\n\n    const ownerDocument = getDocument(event.target);\n    const pointerCaptureTarget = ownerDocument.body;\n\n    pointerCaptureTarget.setPointerCapture(event.pointerId);\n\n    const unbind = this.listeners.bind(ownerDocument, [\n      {\n        // Prevent scrolling on touch devices\n        type: 'touchmove',\n        listener: preventDefault,\n        options: {\n          passive: false,\n        },\n      },\n      {\n        // Prevent click events\n        type: 'click',\n        listener: preventDefault,\n      },\n      {\n        type: 'contextmenu',\n        listener: preventDefault,\n      },\n      {\n        type: 'keydown',\n        listener: this.handleKeyDown,\n      },\n      {\n        type: 'lostpointercapture',\n        listener: (event: PointerEvent) => {\n          if (event.target !== pointerCaptureTarget) return;\n\n          this.handlePointerUp(event);\n        },\n      },\n    ]);\n\n    this.#cleanup.add(unbind);\n  }\n\n  protected handleCancel(event: Event) {\n    const {dragOperation} = this.manager;\n\n    if (dragOperation.status.initialized) {\n      this.manager.actions.stop({event, canceled: true});\n    }\n\n    this.cleanup();\n  }\n\n  protected cleanup() {\n    this.latest = {\n      event: undefined,\n      coordinates: undefined,\n    };\n    this.#cleanup.forEach((cleanup) => cleanup());\n    this.#cleanup.clear();\n  }\n\n  public destroy() {\n    this.cleanup();\n    this.listeners.clear();\n  }\n\n  static configure = configurator(PointerSensor);\n\n  static defaults = defaults;\n}\n\nfunction isCapturedBySensor(event: Event) {\n  return 'sensor' in event;\n}\n\nfunction preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nfunction noop() {}\n\nconst windows = new WeakSet<Window>();\n\nfunction patchWindow(window: Window | null) {\n  if (!window || windows.has(window)) {\n    return;\n  }\n\n  window.addEventListener('touchmove', noop, {\n    capture: false,\n    passive: false,\n  });\n  windows.add(window);\n}\n", "import {\n  DragDropManager as AbstractDragDropManager,\n  DragDropManagerInput,\n  type Data,\n  type Modifiers,\n  type Plugins,\n  type Sensors,\n} from '@dnd-kit/abstract';\n\nimport type {Draggable, Droppable} from '../entities/index.ts';\nimport {\n  Accessibility,\n  AutoScroller,\n  Cursor,\n  Fe<PERSON><PERSON>,\n  Scroller,\n  ScrollListener,\n  PreventSelection,\n} from '../plugins/index.ts';\nimport {KeyboardSensor, PointerSensor} from '../sensors/index.ts';\n\nexport interface Input extends DragDropManagerInput<DragDropManager> {}\n\nexport const defaultPreset: {\n  modifiers: Modifiers<DragDropManager>;\n  plugins: Plugins<DragDropManager>;\n  sensors: Sensors<DragDropManager>;\n} = {\n  modifiers: [],\n  plugins: [Accessibility, AutoScroller, Cursor, Feedback, PreventSelection],\n  sensors: [PointerSensor, KeyboardSensor],\n};\n\nexport class DragDropManager<\n  T extends Data = Data,\n  U extends Draggable<T> = Draggable<T>,\n  V extends Droppable<T> = Droppable<T>,\n> extends AbstractDragDropManager<U, V> {\n  constructor(input: Input = {}) {\n    const {\n      plugins = defaultPreset.plugins,\n      sensors = defaultPreset.sensors,\n      modifiers = [],\n    } = input;\n\n    super({\n      ...input,\n      plugins: [ScrollListener, Scroller, ...plugins],\n      sensors,\n      modifiers,\n    });\n  }\n}\n", "import {\n  Draggable as AbstractDraggable,\n  Sensor,\n  descriptor,\n} from '@dnd-kit/abstract';\nimport type {Data, DraggableInput} from '@dnd-kit/abstract';\nimport {reactive} from '@dnd-kit/state';\n\nimport type {DragDropManager} from '../../manager/index.ts';\nimport type {Sensors} from '../../sensors/index.ts';\n\nexport type FeedbackType = 'default' | 'move' | 'clone' | 'none';\n\nexport interface Input<T extends Data = Data> extends DraggableInput<T> {\n  handle?: Element;\n  element?: Element;\n  feedback?: FeedbackType;\n  sensors?: Sensors;\n}\n\nexport class Draggable<T extends Data = Data> extends AbstractDraggable<\n  T,\n  DragDropManager\n> {\n  constructor(\n    {\n      element,\n      effects = () => [],\n      handle,\n      feedback = 'default',\n      ...input\n    }: Input<T>,\n    manager: DragDropManager | undefined\n  ) {\n    super(\n      {\n        effects: () => [\n          ...effects(),\n          () => {\n            const {manager} = this;\n\n            if (!manager) return;\n\n            const sensors = this.sensors?.map(descriptor) ?? [\n              ...manager.sensors,\n            ];\n            const unbindFunctions = sensors.map((entry) => {\n              const sensorInstance =\n                entry instanceof Sensor\n                  ? entry\n                  : manager.registry.register(entry.plugin);\n              const options =\n                entry instanceof Sensor ? undefined : entry.options;\n\n              const unbind = sensorInstance.bind(this, options);\n              return unbind;\n            });\n\n            return function cleanup() {\n              unbindFunctions.forEach((unbind) => unbind());\n            };\n          },\n        ],\n        ...input,\n      },\n      manager\n    );\n\n    this.element = element;\n    this.handle = handle;\n    this.feedback = feedback;\n  }\n\n  @reactive\n  public accessor handle: Element | undefined;\n\n  @reactive\n  public accessor element: Element | undefined;\n\n  @reactive\n  public accessor feedback: FeedbackType;\n}\n", "import {Droppable as AbstractDroppable} from '@dnd-kit/abstract';\nimport type {\n  Data,\n  DroppableInput as AbstractDroppableInput,\n} from '@dnd-kit/abstract';\nimport {defaultCollisionDetection} from '@dnd-kit/collision';\nimport type {CollisionDetector} from '@dnd-kit/collision';\nimport {reactive, signal, untracked} from '@dnd-kit/state';\nimport type {BoundingRectangle, Shape} from '@dnd-kit/geometry';\nimport {DOMRectangle, PositionObserver} from '@dnd-kit/dom/utilities';\n\nimport type {DragDropManager} from '../../manager/manager.ts';\n\ntype OptionalInput = 'collisionDetector';\n\nexport interface Input<T extends Data = Data>\n  extends Omit<AbstractDroppableInput<T>, OptionalInput> {\n  collisionDetector?: CollisionDetector;\n  element?: Element;\n}\n\nexport class Droppable<T extends Data = Data> extends AbstractDroppable<\n  T,\n  DragDropManager\n> {\n  constructor(\n    {element, effects = () => [], ...input}: Input<T>,\n    manager: DragDropManager | undefined\n  ) {\n    const {collisionDetector = defaultCollisionDetection} = input;\n    const updateShape = (boundingClientRect?: BoundingRectangle | null) => {\n      const {manager, element} = this;\n\n      if (!element || boundingClientRect === null) {\n        this.shape = undefined;\n        return undefined;\n      }\n\n      if (!manager) return;\n\n      const updatedShape = new DOMRectangle(element);\n\n      const shape = untracked(() => this.shape);\n      if (updatedShape && shape?.equals(updatedShape)) {\n        return shape;\n      }\n\n      this.shape = updatedShape;\n\n      return updatedShape;\n    };\n\n    const observePosition = signal(false);\n\n    super(\n      {\n        ...input,\n        collisionDetector,\n        effects: () => [\n          ...effects(),\n          () => {\n            const {element, manager} = this;\n            if (!manager) return;\n\n            const {dragOperation} = manager;\n            const {source} = dragOperation;\n\n            observePosition.value = Boolean(\n              source &&\n                dragOperation.status.initialized &&\n                element &&\n                !this.disabled &&\n                this.accepts(source)\n            );\n          },\n          () => {\n            const {element} = this;\n\n            if (observePosition.value && element) {\n              const positionObserver = new PositionObserver(\n                element,\n                updateShape\n              );\n\n              return () => {\n                positionObserver.disconnect();\n                this.shape = undefined;\n              };\n            }\n          },\n          () => {\n            if (this.manager?.dragOperation.status.initialized) {\n              return () => {\n                this.shape = undefined;\n              };\n            }\n          },\n        ],\n      },\n      manager\n    );\n\n    this.element = element;\n    this.refreshShape = () => updateShape();\n  }\n\n  @reactive\n  accessor #element: Element | undefined;\n\n  @reactive\n  public accessor proxy: Element | undefined;\n\n  set element(element: Element | undefined) {\n    this.#element = element;\n  }\n\n  get element() {\n    return this.proxy ?? this.#element;\n  }\n\n  public refreshShape: () => Shape | undefined;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAMA,iBAAoB;EAC/BC,IAAM;EACNC,eAAiB;AAEnB;AAEO,IAAMC,0BAA6B;AACnC,IAAMC,2BAA8B;AAEpC,IAAMC,+BAA4D;EACvEC,SAAW;AACb;AAEO,IAAMC,oBAAsC;EACjDC,UAAU;IAACC,SAAA,EAAW;MAACC;IAAA;EAAA,CAAU;IAC/B,IAAI,CAACA,MAAQ;IAEN,mCAA4BA,MAAA,CAAOC,EAAE;EAAA,CAC9C;EACAC,SAAS;IAACH,SAAA,EAAW;MAACC,MAAQ;MAAAG;IAAA;EAAA,CAAU;IACtC,IAAI,CAACH,MAAA,IAAUA,MAAO,CAAAC,EAAA,MAAOE,MAAA,oBAAAA,MAAA,CAAQF,EAAI;IAEzC,IAAIE,MAAQ;MACV,OAAO,kBAAkBH,MAAA,CAAOC,EAAE,oCAAoCE,MAAA,CAAOF,EAAE;IAAA;IAG1E,yBAAkBD,MAAA,CAAOC,EAAE;EAAA,CACpC;EACAG,QAAQ;IAACL,SAAW;MAACC,MAAA;MAAQG;IAAM;IAAGE;EAAA,CAAW;IAC/C,IAAI,CAACL,MAAQ;IAEb,IAAIK,QAAU;MACL,iDAA0CL,MAAA,CAAOC,EAAE;IAAA;IAG5D,IAAIE,MAAQ;MACV,OAAO,kBAAkBH,MAAA,CAAOC,EAAE,sCAAsCE,MAAA,CAAOF,EAAE;IAAA;IAG5E,yBAAkBD,MAAA,CAAOC,EAAE;EAAA;AAEtC;;;AC3CO,SAASK,YAAYC,OAAkB;EACtC,MAAAC,OAAA,GAAUD,OAAQ,CAAAC,OAAA,CAAQC,WAAY;EAErC,QAAC,SAAS,QAAU,cAAY,KAAK,QAAQ,EAAEC,QAAA,CAASF,OAAO;AACxE;;;ACJO,SAASG,iBAAiBV,EAAA,EAAYW,KAAe;EACpD,MAAAL,OAAA,GAAUM,QAAS,CAAAC,aAAA,CAAc,KAAK;EAE5CP,OAAA,CAAQN,EAAK,GAAAA,EAAA;EACLM,OAAA,CAAAQ,KAAA,CAAMC,WAAY,YAAW,MAAM;EAC3CT,OAAA,CAAQU,WAAc,GAAAL,KAAA;EAEf,OAAAL,OAAA;AACT;;;ACRO,SAASW,iBAAiBjB,EAAY;EACrC,MAAAM,OAAA,GAAUM,QAAS,CAAAC,aAAA,CAAc,KAAK;EAE5CP,OAAA,CAAQN,EAAK,GAAAA,EAAA;EACLM,OAAA,CAAAY,YAAA,CAAa,QAAQ,QAAQ;EAC7BZ,OAAA,CAAAY,YAAA,CAAa,aAAa,QAAQ;EAClCZ,OAAA,CAAAY,YAAA,CAAa,eAAe,MAAM;EAClCZ,OAAA,CAAAQ,KAAA,CAAMC,WAAY,aAAY,OAAO;EACrCT,OAAA,CAAAQ,KAAA,CAAMC,WAAY,UAAS,KAAK;EAChCT,OAAA,CAAAQ,KAAA,CAAMC,WAAY,WAAU,KAAK;EACjCT,OAAA,CAAAQ,KAAA,CAAMC,WAAY,WAAU,MAAM;EAClCT,OAAA,CAAAQ,KAAA,CAAMC,WAAY,WAAU,GAAG;EAC/BT,OAAA,CAAAQ,KAAA,CAAMC,WAAY,YAAW,GAAG;EAChCT,OAAA,CAAAQ,KAAA,CAAMC,WAAY,aAAY,QAAQ;EACtCT,OAAA,CAAAQ,KAAA,CAAMC,WAAY,SAAQ,eAAe;EACzCT,OAAA,CAAAQ,KAAA,CAAMC,WAAY,cAAa,aAAa;EAC5CT,OAAA,CAAAQ,KAAA,CAAMC,WAAY,gBAAe,QAAQ;EAE1C,OAAAT,OAAA;AACT;;;AC4BA,IAAMa,eAAA,GAAkB,CAAC,YAAY,UAAU;AAElC,IAAAC,aAAA,GAAN,cAA4BC,MAAwB;EACzDC,YAAYC,OAAA,EAA0BC,OAAmB;IACvD,MAAMD,OAAO;IAEP;MACJvB,EAAA;MACAyB,QAAU;QACRC,WAAA,EAAaC,iBAAoB,GAAAnC,0BAAA;QACjCoC,YAAA,EAAcC,kBAAqB,GAAApC;MAAA,IACjC,EAAC;MACLqC,aAAgB,GAAAlC,oBAAA;MAChBmC,wBAA2B,GAAArC,+BAAA;MAC3BsC,QAAA,EAAUC,UAAa;IAAA,CACzB,GAAIT,OAAA,WAAAA,OAAA,GAAW,EAAC;IAEV,MAAAU,aAAA,GAAgBlC,EAAA,GAClB,GAAG2B,iBAAiB,IAAI3B,EAAE,KAC1BmC,gBAAA,CAAiBR,iBAAiB;IAChC,MAAAS,cAAA,GAAiBpC,EAAA,GACnB,GAAG6B,kBAAkB,IAAI7B,EAAE,KAC3BmC,gBAAA,CAAiBN,kBAAkB;IAEnC,IAAAQ,iBAAA;IACA,IAAAC,iBAAA;IACA,IAAAC,kBAAA;IACA,IAAAC,kBAAA;IAEE,MAAAC,kBAAA,GAAqBA,CAAC9B,KAAA,GAAQ6B,kBAAuB;MACrD,KAACD,kBAAsB,KAAC5B,KAAO;MAC/B,KAAA4B,kBAAA,oBAAAA,kBAAA,CAAoBG,SAAA,MAAc/B,KAAO;QAC3C4B,kBAAA,CAAmBG,SAAY,GAAA/B,KAAA;MAAA;IACjC,CACF;IACA,MAAMgC,0BAA6B,GAAAA,CAAA,KACjCC,SAAU,CAAAC,QAAA,CAASJ,kBAAkB;IACvC,MAAMK,2BAA8B,GAAAd,QAAA,CAClCW,0BAAA,EACAV,UAAA,CACF;IAEA,MAAMc,cAAiB,GAAAC,MAAA,CAAOC,OAAQ,CAAAnB,aAAa,CAAE,CAAAoB,GAAA,CACnD,CAAC,CAACC,SAAW,EAAAC,eAAe,CAAM;MACzB,YAAK7B,OAAA,CAAQ8B,OAAQ,CAAAC,gBAAA,CAC1BH,SAAA,EACA,CAACI,KAAA,EAAYC,QAA6B;QACxC,MAAMlD,OAAU,GAAAiC,kBAAA;QAChB,IAAI,CAACjC,OAAS;QAER,MAAAsB,YAAA,GAAewB,eAAA,oBAAAA,eAAA,CAAkBG,KAAO,EAAAC,QAAA;QAE1C,IAAA5B,YAAA,IAAgBtB,OAAQ,CAAAoC,SAAA,KAAcd,YAAc;UACjCY,kBAAA,GAAAZ,YAAA;UAEjB,IAAAT,eAAA,CAAgBV,QAAS,CAAA0C,SAAS,CAAG;YACXL,2BAAA;UAAA,CACvB;YACsBH,0BAAA;YAC3BG,2BAAA,CAA4BW,MAAO;UAAA;QACrC;MACF,CACF,CACF;IAAA,CACF,CACF;IAEA,MAAMC,UAAA,GAAaA,CAAA,KAAM;MACvB,IAAIC,QAAA,GAAW,EAAC;MAEZ,MAACtB,iBAAA,oBAAAA,iBAAA,CAAmBuB,WAAa;QACfvB,iBAAA,GAAA3B,gBAAA,CAClBwB,aAAA,EACAH,wBAAyB,CAAApC,SAAA,CAC3B;QACAgE,QAAA,CAASE,IAAA,CAAKxB,iBAAiB;MAAA;MAG7B,MAACC,iBAAA,oBAAAA,iBAAA,CAAmBsB,WAAa;QACnCtB,iBAAA,GAAoBrB,gBAAA,CAAiBmB,cAAc;QAC9BG,kBAAA,GAAA3B,QAAA,CAASkD,cAAA,CAAe,EAAE;QAC/CxB,iBAAA,CAAkByB,WAAA,CAAYxB,kBAAkB;QAChDoB,QAAA,CAASE,IAAA,CAAKvB,iBAAiB;MAAA;MAG7B,IAAAqB,QAAA,CAASK,MAAA,GAAS,CAAG;QACdpD,QAAA,CAAAqD,IAAA,CAAKC,MAAO,IAAGP,QAAQ;MAAA;IAClC,CACF;IAEM,MAAAQ,SAAA,sBAAgBC,GAAgB;IAEtC,SAASC,gBAAmBA,CAAA;MAC1B,WAAWvE,SAAA,IAAaqE,SAAW;QACvBrE,SAAA;MAAA;IACZ;IAGF,KAAKwE,cAAA,CAAe,MAAM;MAjJ9B,IAAAC,GAAA;MAkJMJ,SAAA,CAAUK,KAAM;MAGhB,WAAW7E,SAAa,SAAK4B,OAAQ,CAAAkD,QAAA,CAASC,UAAA,CAAW/D,KAAO;QAC9D,MAAMgE,SAAA,IAAYJ,GAAA,GAAA5E,SAAA,CAAUiF,MAAV,YAAAL,GAAA,GAAoB5E,SAAU,CAAAW,OAAA;QAEhD,IAAIqE,SAAW;UACT,KAACtC,iBAAqB,KAACC,iBAAmB;YAC5C6B,SAAA,CAAUU,GAAA,CAAInB,UAAU;UAAA;UAIvB,MAACrD,WAAY,CAAAsE,SAAS,CAAK,IAAAG,QAAA,OAC5B,CAACH,SAAA,CAAUI,YAAa,WAAU,CAClC;YACAZ,SAAA,CAAUU,GAAA,CAAI,MAAMF,SAAA,CAAUzD,YAAa,aAAY,GAAG,CAAC;UAAA;UAI3D,KAACyD,SAAU,CAAAI,YAAA,CAAa,MAAM,KAC9B,EAAEJ,SAAU,CAAApE,OAAA,CAAQC,WAAY,OAAM,QACtC;YACU2D,SAAA,CAAAU,GAAA,CAAI,MACZF,SAAA,CAAUzD,YAAa,SAAQ7B,iBAAA,CAAkBC,IAAI,EACvD;UAAA;UAGF,IAAI,CAACqF,SAAA,CAAUI,YAAa,uBAAsB,CAAG;YACzCZ,SAAA,CAAAU,GAAA,CAAI,MACZF,SAAU,CAAAzD,YAAA,CACR,wBACA7B,iBAAkB,CAAAE,eAAA,CACpB,CACF;UAAA;UAGF,IAAI,CAACoF,SAAA,CAAUI,YAAa,mBAAkB,CAAG;YACrCZ,SAAA,CAAAU,GAAA,CAAI,MACZF,SAAA,CAAUzD,YAAa,qBAAoBgB,aAAa,EAC1D;UAAA;UAGF,WAAW8C,GAAO,KAAC,cAAgB,gBAAc,CAAG;YAC5C,MAAArE,KAAA,GAAQsE,MAAO,CAAAtF,SAAA,CAAUuF,UAAU;YAEzC,IAAIP,SAAU,CAAAQ,YAAA,CAAaH,GAAG,MAAMrE,KAAO;cACzCwD,SAAA,CAAUU,GAAA,CAAI,MAAMF,SAAA,CAAUzD,YAAa,CAAA8D,GAAA,EAAKrE,KAAK,CAAC;YAAA;UACxD;UAGI,MAAAyE,QAAA,GAAWH,MAAO,CAAAtF,SAAA,CAAUyF,QAAQ;UAE1C,IAAIT,SAAU,CAAAQ,YAAA,CAAa,eAAe,MAAMC,QAAU;YAC9CjB,SAAA,CAAAU,GAAA,CAAI,MACZF,SAAA,CAAUzD,YAAa,kBAAiBkE,QAAQ,EAClD;UAAA;QACF;MACF;MAGE,IAAAjB,SAAA,CAAUkB,IAAA,GAAO,CAAG;QACtBzC,SAAA,CAAUC,QAAA,CAASwB,gBAAgB;MAAA;IACrC,CACD;IAED,KAAKiB,OAAA,GAAU,MAAM;MACnB,MAAMA,OAAQ;MACKjD,iBAAA,oBAAAA,iBAAA,CAAAkD,MAAA;MACAjD,iBAAA,oBAAAA,iBAAA,CAAAiD,MAAA;MACnBxC,cAAA,CAAeyC,OAAQ,CAACC,WAAgB,IAAAA,WAAA,EAAa;IAAA,CACvD;EAAA;AAEJ;AAEA,SAASzD,SAAS0D,EAAA,EAAgBC,IAAc;EAC1C,IAAAC,OAAA;EACJ,MAAMC,SAAA,GAAYA,CAAA,KAAM;IACtBC,YAAA,CAAaF,OAAO;IACVA,OAAA,GAAAG,UAAA,CAAWL,EAAA,EAAIC,IAAI;EAAA,CAC/B;EAEUE,SAAA,CAAApC,MAAA,GAAS,MAAMqC,YAAA,CAAaF,OAAO;EAEtC,OAAAC,SAAA;AACT;ACxNa,IAAAG,MAAA,GAAN,cAAqB3E,MAAwB;EAClDC,YACSC,OAAA,EACPC,OACA;IACA,MAAMD,OAAA,EAASC,OAAO;IAHf,KAAAD,OAAA,GAAAA,OAAA;IAKP,MAAM0E,GAAM,GAAAC,QAAA,CAAS,MAAG;MArB5B,IAAA3B,GAAA;MAsBM,OAAA4B,WAAA,EAAY5B,GAAA,OAAK,CAAAhD,OAAA,CAAQ6E,aAAA,CAAcrG,MAA3B,qBAAAwE,GAAA,CAAmCjE,OAAO;IAAA,EACxD;IAEK,KAAAgF,OAAA,GAAUe,MAAA,CAAO,MAAM;MAzBhC,IAAA9B,GAAA;MA0BY;QAAC6B;MAAa,IAAI,IAAK,CAAA7E,OAAA;MACvB;QAAC+E,MAAA,GAAS;MAAU,KAAI/B,GAAA,OAAK,CAAA/C,OAAA,KAAL,OAAA+C,GAAA,GAAgB,EAAC;MAE3C,IAAA6B,aAAA,CAAcG,MAAA,CAAOC,WAAa;QACpC,MAAMC,SAAA,GAAWR,GAAI,CAAAtF,KAAA;QACf,MAAAG,KAAA,GAAQ2F,SAAS,CAAA5F,aAAA,CAAc,OAAO;QACtCC,KAAA,CAAAE,WAAA,GAAc,eAAesF,MAAM;QACzCG,SAAA,CAASC,IAAK,CAAA3C,WAAA,CAAYjD,KAAK;QAExB,aAAMA,KAAA,CAAMyE,MAAO;MAAA;IAC5B,CACD;EAAA;AAEL;;;ACvCO,IAAMoB,WAAc;AACpB,IAAMC,kBAAA,GAAqB,GAAGD,WAAW;AACzC,IAAME,UAAa;AACnB,IAAMC,SAAA,GAAY,GAAGH,WAAW;AAChC,IAAMI,qBAAA,GAAwB,GAAGJ,WAAW;AAE5C,IAAMK,kBAAqB,IAChCF,SAAA,EACAC,qBAAA,EACA,WACA,gBACA,gBACF;AAEO,IAAME,cAAA,GAAiB,CAAC,sBAAsB;AAE9C,IAAMC,SAAY;AAAA,WACdJ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAMLD,UAAU;AAAA,gBACTA,UAAU;AAAA;AAAA;AAAA,iBAGTA,UAAU;AAAA,qBACNA,UAAU;AAAA,kBACbA,UAAU;AAAA,sBACNA,UAAU;AAAA,sBACVA,UAAU;AAAA;;AAAA,WAGrBE,qBAAqB;AAAA;AAAA;;AAAA,WAIrBA,qBAAqB;AAAA;AAAA;;AAAA,KAI3BD,SAAS;AAAA;AAAA;;AAAA,KAITA,SAAS,UAAUF,kBAAkB;AAAA,qBACrBC,UAAU;AAAA;;AAAA,KAG1BC,SAAS,aAAaD,UAAU;AAAA,iBACpBA,UAAU;AAAA,4BACCA,UAAU;AAAA;;AAAA;AAAA,cAIxBC,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAclBA,SAAS,iBAAiBH,WAAW,iBAAiBG,SAAS;AAAA;AAAA;AAAA;AAAA,CAKjE,CAAAK,OAAA,CAAQ,QAAQ,GAAG,EACnBA,OAAA,CAAQ,MAAQ,KAAG,EACnBC,IAAK;AC5DD,SAASC,kBACdtH,MACA,EAAAuH,IAAA,GAAO,QACc;EACrB,OAAOC,SAAA,CAAU,MAAM;IACf;MAACjH,OAAS;MAAAiB;IAAA,CAAW,GAAAxB,MAAA;IAEvB,KAACO,OAAW,KAACiB,OAAS;IAE1B,MAAMiG,mBAAsB,GAAAC,uBAAA,CAC1BnH,OAAA,EACAiB,OAAA,CAAQkD,QAAS,CAAAiD,UAAA,CACnB;IACA,MAAMC,OAAA,GAA6B,EAAC;IAC9B,MAAAC,WAAA,GAAcC,YAAA,CAAavH,OAAO;IAClC;MAACiF;IAAA,CAAU,GAAAqC,WAAA;IAEME,sBAAA,CAAAN,mBAAA,EAAqBI,WAAA,EAAaD,OAAO;IAChEI,oBAAA,CAAqBH,WAAA,EAAaN,IAAI;IAGtCM,WAAA,CAAYrC,MAAA,GAAS,MAAM;MACzBoC,OAAA,CAAQnC,OAAQ,CAACE,EAAO,IAAAA,EAAA,EAAI;MAC5BH,MAAA,CAAOyC,IAAA,CAAKJ,WAAW;IAAA,CACzB;IAEO,OAAAA,WAAA;EAAA,CACR;AACH;AAMA,SAASH,wBACPnH,OAAA,EACAoH,UACwB;EAClB,MAAAF,mBAAA,sBAA0BS,GAAuB;EAEvD,WAAWC,SAAA,IAAaR,UAAY;IAC9B,KAACQ,SAAA,CAAU5H,OAAS;IAExB,IAAIA,OAAA,KAAY4H,SAAU,CAAA5H,OAAA,IAAWA,OAAA,CAAQ6H,QAAS,CAAAD,SAAA,CAAU5H,OAAO,CAAG;MACxE,MAAM8H,mBAAA,GAAsB,GAAGzB,WAAW,GAAGxE,gBAAA,CAAiB,QAAQ,CAAC;MAC7D+F,SAAA,CAAA5H,OAAA,CAAQY,YAAa,CAAAkH,mBAAA,EAAqB,EAAE;MAClCZ,mBAAA,CAAAa,GAAA,CAAIH,SAAA,EAAWE,mBAAmB;IAAA;EACxD;EAGK,OAAAZ,mBAAA;AACT;AAKA,SAASM,uBACPN,mBACA,EAAAI,WAAA,EACAD,OACM;EACN,WAAW,CAACO,SAAA,EAAWE,mBAAmB,KAAKZ,mBAAqB;IAC9D,KAACU,SAAA,CAAU5H,OAAS;IAElB,MAAAgI,QAAA,GAAW,IAAIF,mBAAmB;IAClC,MAAAG,aAAA,GAAgBX,WAAA,CAAYY,OAAQ,CAAAF,QAAQ,IAC9CV,WACA,GAAAA,WAAA,CAAYa,aAAA,CAAcH,QAAQ;IAE5BJ,SAAA,CAAA5H,OAAA,CAAQoI,eAAA,CAAgBN,mBAAmB;IAErD,IAAI,CAACG,aAAe;IAEpB,MAAMI,eAAA,GAAkBT,SAAU,CAAA5H,OAAA;IAElC4H,SAAA,CAAUU,KAAQ,GAAAL,aAAA;IAClBA,aAAA,CAAcG,eAAA,CAAgBN,mBAAmB;IAEjCS,eAAA,CAAAR,GAAA,CAAIM,eAAA,EAAiBJ,aAAa;IAElDZ,OAAA,CAAQ9D,IAAA,CAAK,MAAM;MACjBgF,eAAA,CAAgBC,MAAA,CAAOH,eAAe;MACtCT,SAAA,CAAUU,KAAQ;IAAA,CACnB;EAAA;AAEL;AAKA,SAASb,qBAAqBH,WAAsB,EAAAN,IAAA,GAAO,QAAgB;EAC7DM,WAAA,CAAA1G,YAAA,CAAa,SAAS,MAAM;EAC5B0G,WAAA,CAAA1G,YAAA,CAAa,aAAa,IAAI;EAC9B0G,WAAA,CAAA1G,YAAA,CAAa,eAAe,MAAM;EAClC0G,WAAA,CAAA1G,YAAA,CAAa6F,qBAAA,EAAuBO,IAAI;AACtD;AAKO,SAASyB,YAAYzI,OAAA,EAAkBJ,MAA0B;EAClE,IAAAI,OAAA,KAAYJ,MAAA,EAAe;EAC/B,OAAO8I,eAAgB,CAAA1I,OAAO,CAAM,KAAA0I,eAAA,CAAgB9I,MAAM;AAC5D;AAKO,SAAS+I,oBAAoB1F,KAAc;EAC1C;IAACrD;EAAA,CAAU,GAAAqD,KAAA;EAGf,kBAAcA,KACd,IAAAA,KAAA,CAAM2F,QAAa,iBACnBC,SAAU,CAAAjJ,MAAM,CAChB,IAAAA,MAAA,CAAO6E,YAAa,UAAS,CAC7B;IACsBqE,qBAAA,OAAMC,WAAY,CAAAnJ,MAAM,CAAC;EAAA;AAEnD;AAEO,SAASoJ,WAAWhJ,OAAkD;EAC3E,OAAOA,OAAA,CAAQC,OAAY;AAC7B;;;AC3EA,IAAMgJ,kBAAA,sBAAyBtB,GAAsC;AAjErE,IAAAuB,YAAA,EAAAC,EAAA,EAAAC,KAAA,EAAAC,QAAA,EAAAC,mBAAA,EAAAC,SAAA,EAAAC,eAAA;AAmEO,IAAMC,SAAA,GAAN,MAAMA,SAAA,UAAiBN,EAAA,GAAApI,MAC5B,EAAAmI,YAAA,IAACQ,QAAA,GAD2BP,EAAyC;EASrEnI,YAAYC,OAAA,EAA0BC,OAA2B;IAC/D,MAAMD,OAAA,EAASC,OAAO;IAVnByI,YAAA,OAAAL,mBAAA;IAELK,YAAA,OAAgBN,QAAhB,EAAAO,iBAAA,CAAAR,KAAA,aAAAQ,iBAAA,CAAAR,KAAA;IAEA,KAAQS,KAAe;MACrBC,OAAA,EAAS,EAAC;MACVC,OAAA,EAAS;IAAC,CACZ;IAKO,KAAA/F,cAAA,CAAegG,eAAA,OAAKV,mBAAa,EAAAE,eAAA;IACjC,KAAAxF,cAAA,CAAegG,eAAA,OAAKV,mBAAO,EAAAC,SAAA;EAAA;EA8nB3BvE,OAAgBA,CAAA;IACrB,MAAMA,OAAQ;IAGd,WAAW,CAACW,GAAK,EAAAsE,YAAY,CAAK,IAAAhB,kBAAA,CAAmBtG,OAAA,EAAW;MAC9D,IAAIsH,YAAa,CAAAC,SAAA,CAAUC,GAAI,KAAI,CAAG;QACvBF,YAAA,CAAAC,SAAA,CAAU1B,MAAA,CAAO,IAAI;QAG9B,IAAAyB,YAAA,CAAaC,SAAU,CAAAnF,IAAA,KAAS,CAAG;UACrCkF,YAAA,CAAa5C,OAAQ;UACrB4B,kBAAA,CAAmBT,MAAA,CAAO7C,GAAG;QAAA;MAC/B;IACF;EACF;AAIJ;AA7pBOyD,KAAA,GAAAgB,gBAAA,CAAAjB,EAAA;AAEWE,QAAA,OAAAgB,OAAA;AAFXf,mBAAA,OAAAgB,OAAA;AAgBLf,SAAA,GAAO,SAAAA,CAAA,EAAG;EAnFZ,IAAAtF,GAAA,EAAAsG,GAAA,EAAAC,GAAA;EAoFI,MAAM;IAACX,KAAA;IAAO5I,OAAS;IAAAC;EAAA,CAAW;EAC5B;IAAC4E;EAAA,CAAiB,GAAA7E,OAAA;EACxB,MAAM;IAACwJ,QAAA;IAAUhL,MAAQ;IAAAwG;EAAA,CAAU,GAAAH,aAAA;EAEnC,IAAIG,MAAA,CAAOyE,IAAM;IACfb,KAAA,CAAME,OAAA,GAAU,EAAC;IACjBF,KAAA,CAAMC,OAAA,GAAU,EAAC;IACjB;EAAA;EAGF,IAAI,CAACrK,MAAQ;EAEP;IAACO,OAAS;IAAA2K;EAAA,CAAY,GAAAlL,MAAA;EAG1B,KAACO,OAAA,IACD2K,QAAa,eACb,CAAC1E,MAAO,CAAAC,WAAA,IACRD,MAAA,CAAO2E,YACP;IACA;EAAA;EAGI;IAACd;EAAA,CAAW,GAAAD,KAAA;EAClB,MAAMgB,eAAkB,IAAA5G,GAAA,OAAK,CAAA6G,OAAA,KAAL,OAAA7G,GAAgB,GAAAjE,OAAA;EAClC,MAAA+K,cAAA,GAAiBC,iBAAA,CAAkBH,eAAe;EAClD,MAAAI,qBAAA,GAAwBD,iBAAA,CAAkBhL,OAAO;EACvD,MAAMkL,UAAa,IAACzC,WAAY,CAAAzI,OAAA,EAAS6K,eAAe;EAClD,MAAAM,KAAA,GAAQ,IAAIC,YAAA,CAAapL,OAAS;IACtC+K,cAAA,EAAgBG,UAAA,GAAaD,qBAAwB;IACrDI,gBAAA,EAAkB,CAACH;EAAA,CACpB;EACD,MAAMI,UAAa;IACjBC,CAAA,EAAGN,qBAAsB,CAAAO,MAAA,GAAST,cAAe,CAAAS,MAAA;IACjDC,CAAA,EAAGR,qBAAsB,CAAAS,MAAA,GAASX,cAAe,CAAAW;EAAA,CACnD;EAEA,IAAI;IAACC,KAAA;IAAOC,MAAQ;IAAAC,GAAA;IAAKC;EAAA,CAAQ,GAAAX,KAAA;EAEjC,IAAID,UAAY;IACdS,KAAA,GAAQA,KAAA,GAAQL,UAAW,CAAAC,CAAA;IAC3BK,MAAA,GAASA,MAAA,GAASN,UAAW,CAAAG,CAAA;EAAA;EAG3B,IAAAM,uBAAA;EACA,IAAAC,wBAAA;EACE,MAAAC,MAAA,GAAS,IAAIC,MAAA,CAAOrB,eAAe;EACnC;IACJsB,UAAA;IACAC,SAAA;IACAC,SAAA;IACAC,iBAAA;IACAC,eAAA;IACAC,kBAAA;IACAC,gBAAA;IACAC,sBAAA;IACAC,oBAAA;IACAC,qBAAA;IACAC;EAAA,CACF,GAAIC,iBAAA,CAAkB9M,OAAO;EAC7B,MAAM+M,KAAA,GAAQpC,QAAa;EAC3B,MAAMqC,UAAA,GAAaX,SAAc;EACjC,MAAMY,WAAc,GAAAD,UAAA,GAChBE,QAAS,CAAAV,kBAAkB,CAC3B,GAAAU,QAAA,CAAST,gBAAgB,IACzBS,QAAS,CAAAR,sBAAsB,CAC/B,GAAAQ,QAAA,CAASP,oBAAoB,CAC7B;EACJ,MAAMQ,YAAe,GAAAH,UAAA,GACjBE,QAAS,CAAAZ,iBAAiB,CAC1B,GAAAY,QAAA,CAASX,eAAe,IACxBW,QAAS,CAAAN,qBAAqB,CAC9B,GAAAM,QAAA,CAASL,mBAAmB,CAC5B;EAEE,MAAAvF,WAAA,GACJqD,QAAa,eAAU,CAAC,KAAKG,OACzB,GAAA/D,iBAAA,CAAkBtH,MAAQ,EAAAsN,KAAA,GAAQ,OAAU,WAAQ,CACpD;EACN,MAAMK,mBAAsB,GAAAnG,SAAA,CAAU,MACpCoG,eAAA,CAAgBpM,OAAQ,CAAA6E,aAAA,CAAcwH,cAAc,EACtD;EAEA,IAAIlB,SAAA,KAAc,MAAQ;IAClB,MAAAmB,eAAA,GAAkBC,cAAA,CAAepB,SAAS;IAE5C,IAAAmB,eAAA,IAAmB,CAACzD,OAAA,CAAQsC,SAAW;MACzCtC,OAAA,CAAQsC,SAAY,GAAAmB,eAAA;IAAA;EACtB;EAGE,KAACzD,OAAA,CAAQ2D,eAAiB;IAC5B,MAAM1D,OAAU,GAAA9C,SAAA,CAAU,MAAMwD,QAAA,CAASV,OAAO;IAEhDD,OAAA,CAAQ2D,eAAkB;MACxBlC,CAAA,GACGxB,OAAA,CAAQwB,CAAI,GAAAO,IAAA,GAAOf,cAAA,CAAeS,MAAS,GAAAT,cAAA,CAAeQ,CAC1D,KAAAI,KAAA,GAAQZ,cAAe,CAAAS,MAAA;MAC1BC,CAAA,GACG1B,OAAA,CAAQ0B,CAAI,GAAAI,GAAA,GAAMd,cAAA,CAAeW,MAAS,GAAAX,cAAA,CAAeU,CACzD,KAAAG,MAAA,GAASb,cAAe,CAAAW,MAAA;IAAA,CAC7B;EAAA;EAGI;IAAC+B;EAAA,CAAmB,GAAA3D,OAAA;EAC1B,MAAM4D,WAAc,GAAA7B,GAAA,GAAMd,cAAe,CAAAW,MAAA,GAASX,cAAe,CAAAU,CAAA;EACjE,MAAMkC,YAAe,GAAA7B,IAAA,GAAOf,cAAe,CAAAS,MAAA,GAAST,cAAe,CAAAQ,CAAA;EAE/D,KAACzB,OAAA,CAAQ8D,WAAa;IACxB9D,OAAA,CAAQ8D,WAAc;MACpBrC,CAAG,EAAAoC,YAAA;MACHlC,CAAG,EAAAiC;IAAA,CACL;IAGA,IAAIpC,UAAW,CAAAC,CAAA,KAAM,CAAK,IAAAD,UAAA,CAAWG,CAAA,KAAM,CAAG;MACtC;QAACD,MAAQ;QAAAE;MAAA,CAAU,GAAAT,qBAAA;MACzB,MAAM;QAACM,CAAA,EAAGsC,GAAI;QAAApC,CAAA,EAAGqC;MAAA,CAAM,GAAAL,eAAA;MAEvB3D,OAAA,CAAQ8D,WAAY,CAAArC,CAAA,KAAMI,KAAQ,GAAAH,MAAA,GAASG,KAAS,IAAAkC,GAAA;MACpD/D,OAAA,CAAQ8D,WAAY,CAAAnC,CAAA,KAAMG,MAAS,GAAAF,MAAA,GAASE,MAAU,IAAAkC,GAAA;IAAA;EACxD;EAGE,KAAChE,OAAA,CAAQiE,UAAY;IACfjE,OAAA,CAAAiE,UAAA,GAAa;MAACpC,KAAA;MAAOC;IAAM;EAAA;EAGjC,KAAC9B,OAAA,CAAQiB,cAAgB;IAC3BjB,OAAA,CAAQiB,cAAiB,GAAAA,cAAA;EAAA;EAG3B,MAAMiD,gBAAmB;IACvBzC,CAAA,EAAGzB,OAAQ,CAAA8D,WAAA,CAAYrC,CAAI,GAAAoC,YAAA;IAC3BlC,CAAA,EAAG3B,OAAQ,CAAA8D,WAAA,CAAYnC,CAAI,GAAAiC;EAAA,CAC7B;EAEA,MAAMO,SAAY;IAChBtC,KAAA,GACG7B,OAAQ,CAAAiE,UAAA,CAAWpC,KAAQ,GAAA7B,OAAA,CAAQiB,cAAA,CAAeS,MACjD,GAAAG,KAAA,GAAQZ,cAAe,CAAAS,MAAA,IACzBiC,eAAgB,CAAAlC,CAAA;IAClBK,MAAA,GACG9B,OAAQ,CAAAiE,UAAA,CAAWnC,MAAS,GAAA9B,OAAA,CAAQiB,cAAA,CAAeW,MAClD,GAAAE,MAAA,GAASb,cAAe,CAAAW,MAAA,IAC1B+B,eAAgB,CAAAhC;EAAA,CACpB;EAEA,MAAMyC,KAAQ;IACZ3C,CAAG,EAAAyC,gBAAA,CAAiBzC,CAAI,GAAAR,cAAA,CAAeS,MAAA,GAASyC,SAAU,CAAAtC,KAAA;IAC1DF,CAAG,EAAAuC,gBAAA,CAAiBvC,CAAI,GAAAV,cAAA,CAAeW,MAAA,GAASuC,SAAU,CAAArC;EAAA,CAC5D;EAEA,MAAMuC,SAAY;IAChBrC,IAAA,EAAMA,IAAA,GAAOoC,KAAM,CAAA3C,CAAA;IACnBM,GAAA,EAAKA,GAAA,GAAMqC,KAAM,CAAAzC;EAAA,CACnB;EAEgBZ,eAAA,CAAAjK,YAAA,CAAa4F,SAAA,EAAW,MAAM;EAE9C,MAAM4H,SAAY,GAAAnH,SAAA,CAAU,MAAMnB,aAAA,CAAcsI,SAAS;EACnD,MAAAC,gBAAA,IAAmB9D,GAAA,GAAAT,OAAA,CAAQsC,SAAR,YAAA7B,GAAA,GAAqB;IAACgB,CAAA,EAAG,CAAG;IAAAE,CAAA,EAAG;EAAC;EACzD,MAAM6C,EAAK,GAAAF,SAAA,CAAU7C,CAAI,GAAAR,cAAA,CAAeS,MAAA,GAAS6C,gBAAiB,CAAA9C,CAAA;EAClE,MAAMgD,EAAK,GAAAH,SAAA,CAAU3C,CAAI,GAAAV,cAAA,CAAeW,MAAA,GAAS2C,gBAAiB,CAAA5C,CAAA;EAClE,MAAM+C,eAAkB,MAAGF,EAAE,MAAMC,EAAE;EACrC,MAAME,gBAAmB,GAAAtC,UAAA,GACrB,GAAGA,UAAU,wBACb;EAEGF,MAAA,CAAAlE,GAAA,CACL;IACE4D,KAAA,EAAOA,KAAQ,GAAAsB,WAAA;IACfrB,MAAA,EAAQA,MAAS,GAAAuB,YAAA;IACjBtB,GAAA,EAAKsC,SAAU,CAAAtC,GAAA;IACfC,IAAA,EAAMqC,SAAU,CAAArC,IAAA;IAChBM,SAAW,EAAAoC,eAAA;IACXrC,UAAY,EAAAsC,gBAAA;IACZC,KAAA,EAAOxD,UAAA,GAAa,GAAGI,UAAA,CAAWC,CAAC,IAAID,UAAA,CAAWG,CAAC,EAAK;IACxD,oBAAoB,GAAGgC,eAAgB,CAAAlC,CAAA,GAAI,GAAG,KAAKkC,eAAA,CAAgBhC,CAAA,GAAI,GAAG;EAAA,CAC5E,EACAlF,UAAA,CACF;EAEA,IAAIe,WAAa;IACPtH,OAAA,CAAA2O,qBAAA,CAAsB,YAAYrH,WAAW;IAErD,IAAIpG,OAAA,oBAAAA,OAAA,CAAS0N,WAAa;MAClB,MAAAC,IAAA,GACJ,OAAO3N,OAAQ,CAAA0N,WAAA,KAAgB,aAC3B1N,OAAQ,CAAA0N,WAAA,CAAYnP,MAAM,IAC1ByB,OAAQ,CAAA0N,WAAA;MAEdC,IAAA,CAAKpL,WAAA,CAAYzD,OAAO;IAAA;EAC1B;EAGE,IAAA8O,eAAA,CAAgBjE,eAAe,CAAG;IACpC,IAAI,CAACA,eAAA,CAAgBpG,YAAa,UAAS,CAAG;MAC5BoG,eAAA,CAAAjK,YAAA,CAAa,WAAW,QAAQ;IAAA;IAElDmI,WAAA,CAAY8B,eAAe;IACXA,eAAA,CAAA7H,gBAAA,CAAiB,gBAAgB2F,mBAAmB;EAAA;EAGhE,MAAAoG,cAAA,GAAiB,IAAIC,cAAA,CAAe,MAAM;IAC9C,IAAI,CAAC1H,WAAa;IAEZ,MAAA2H,gBAAA,GAAmB,IAAI7D,YAAA,CAAa9D,WAAa;MACrDyD,cAAA;MACAM,gBAAkB;IAAA,CACnB;IACD,MAAM6D,MAAA,GAASzB,eAAmB,WAAAA,eAAA;MAAClC,CAAG;MAAGE,CAAA,EAAG;IAAC;IAC7C,MAAM0D,EAAA,IAAMxD,KAAQ,GAAAsD,gBAAA,CAAiBtD,KAAS,IAAAuD,MAAA,CAAO3D,CAAA,GAAI2C,KAAM,CAAA3C,CAAA;IAC/D,MAAM6D,EAAA,IAAMxD,MAAS,GAAAqD,gBAAA,CAAiBrD,MAAU,IAAAsD,MAAA,CAAOzD,CAAA,GAAIyC,KAAM,CAAAzC,CAAA;IAE1DQ,MAAA,CAAAlE,GAAA,CACL;MACE4D,KAAA,EAAOsD,gBAAA,CAAiBtD,KAAQ,GAAAsB,WAAA;MAChCrB,MAAA,EAAQqD,gBAAA,CAAiBrD,MAAS,GAAAuB,YAAA;MAClCtB,GAAA,EAAKA,GAAM,GAAAuD,EAAA;MACXtD,IAAA,EAAMA,IAAO,GAAAqD;IAAA,CACf,EACA5I,UAAA,CACF;IACyBwF,uBAAA,oBAAAA,uBAAA,CAAAsD,WAAA;IAGzB,IAAIrG,UAAW,CAAAhJ,OAAO,CAAK,IAAAgJ,UAAA,CAAW1B,WAAW,CAAG;MAClD,MAAMgI,KAAQ,GAAAC,KAAA,CAAMC,IAAK,CAAAxP,OAAA,CAAQsP,KAAK;MACtC,MAAMG,gBAAmB,GAAAF,KAAA,CAAMC,IAAK,CAAAlI,WAAA,CAAYgI,KAAK;MAErD,WAAW,CAACI,KAAO,EAAAC,IAAI,CAAK,IAAAL,KAAA,CAAM3M,OAAA,EAAW;QACrC,MAAAiN,eAAA,GAAkBH,gBAAA,CAAiBC,KAAK;QAE9CC,IAAA,CAAKnP,KAAM,CAAAmL,KAAA,GAAQ,GAAGiE,eAAA,CAAgBC,WAAW;MAAA;IACnD;IAGY/J,aAAA,CAAAqF,KAAA,GAAQ,IAAIC,YAAA,CAAaP,eAAe;EAAA,CACvD;EAGK,MAAAiF,YAAA,GAAe,IAAI1E,YAAA,CAAaP,eAAe;EACrD5D,SAAU,OAAOnB,aAAc,CAAAqF,KAAA,GAAQ2E,YAAa;EAE9C,MAAAC,cAAA,GAAiBC,SAAA,CAAUnF,eAAe;EAC1C,MAAAoF,kBAAA,GAAsBhN,KAAiB;IAC3C,KAAKhC,OAAQ,CAAAiP,OAAA,CAAQC,IAAK;MAAClN;IAAA,CAAM;EAAA,CACnC;EAEA,IAAImK,mBAAqB;IACR2C,cAAA,CAAA/M,gBAAA,CAAiB,UAAUiN,kBAAkB;EAAA;EAG9D,IAAIhJ,SAAU,OAAMxH,MAAO,CAAAwG,MAAM,MAAM,MAAQ;IACvB6C,qBAAA,OAAOrJ,MAAO,CAAAwG,MAAA,GAAS,UAAW;EAAA;EAG1D,IAAIqB,WAAa;IACfyH,cAAA,CAAeqB,OAAA,CAAQ9I,WAAW;IAERyE,uBAAA,OAAIsE,gBAAiB,CAACxM,SAAc;MAC5D,IAAIyM,oBAAuB;MAE3B,WAAWC,QAAA,IAAY1M,SAAW;QAC5B,IAAA0M,QAAA,CAAS3Q,MAAA,KAAWI,OAAS;UACRsQ,oBAAA;UACvB;QAAA;QAGE,IAAAC,QAAA,CAASvJ,IAAA,KAAS,YAAc;UAElC;QAAA;QAKF,MAAMwJ,aAAA,GAAgBD,QAAS,CAAAC,aAAA;QAE/B,IACEA,aAAA,CAAcC,UAAW,QAAO,KAChC/J,kBAAmB,CAAAvG,QAAA,CAASqQ,aAAa,CACzC;UACA;QAAA;QAGI,MAAAE,cAAA,GAAiB1Q,OAAQ,CAAA6E,YAAA,CAAa2L,aAAa;QAEzD,IAAIA,aAAA,KAAkB,OAAS;UAC7B,IAAIG,aAAc,CAAA3Q,OAAO,CAAK,IAAA2Q,aAAA,CAAcrJ,WAAW,CAAG;YACxD,MAAMsJ,OAAA,GAAS5Q,OAAQ,CAAAQ,KAAA;YAEvB,WAAWkE,GAAO,IAAA6K,KAAA,CAAMC,IAAK,CAAAlI,WAAA,CAAY9G,KAAK,CAAG;cAC/C,IAAIoQ,OAAO,CAAAC,gBAAA,CAAiBnM,GAAG,MAAM,EAAI;gBAC3B4C,WAAA,CAAA9G,KAAA,CAAMsQ,cAAA,CAAepM,GAAG;cAAA;YACtC;YAGF,WAAWA,GAAO,IAAA6K,KAAA,CAAMC,IAAK,CAAAoB,OAAM,CAAG;cACpC,IACEjK,cAAA,CAAexG,QAAS,CAAAuE,GAAG,KAC3BA,GAAI,CAAA+L,UAAA,CAAWlK,UAAU,CACzB;gBACA;cAAA;cAGI,MAAAlG,KAAA,GAAQuQ,OAAO,CAAAC,gBAAA,CAAiBnM,GAAG;cAE7B4C,WAAA,CAAA9G,KAAA,CAAMC,WAAY,CAAAiE,GAAA,EAAKrE,KAAK;YAAA;UAC1C;QACF,CACF,UAAWqQ,cAAA,KAAmB,IAAM;UACtBpJ,WAAA,CAAA1G,YAAA,CAAa4P,aAAA,EAAeE,cAAc;QAAA,CACjD;UACLpJ,WAAA,CAAYc,eAAA,CAAgBoI,aAAa;QAAA;MAC3C;MAGF,IAAIF,oBAAA,IAAwBvD,KAAO;QACjCzF,WAAA,CAAYyJ,SAAA,GAAY/Q,OAAQ,CAAA+Q,SAAA;MAAA;IAClC,CACD;IAEDhF,uBAAA,CAAwBqE,OAAA,CAAQpQ,OAAS;MACvCgR,UAAY;MACZC,OAAS;MACTC,SAAW;IAAA,CACZ;IAG0BlF,wBAAA,OAAIqE,gBAAiB,CAAC1N,OAAY;MAC3D,WAAWwO,KAAA,IAASxO,OAAS;QACvB,IAAAwO,KAAA,CAAMC,UAAW,CAAA1N,MAAA,KAAW,CAAG;QAEnC,WAAW2N,IAAQ,IAAA9B,KAAA,CAAMC,IAAK,CAAA2B,KAAA,CAAMC,UAAU,CAAG;UAC/C,IACEC,IAAA,CAAKxJ,QAAS,CAAA7H,OAAO,CACrB,IAAAA,OAAA,CAAQsR,kBAAA,KAAuBhK,WAC/B;YAEQtH,OAAA,CAAA2O,qBAAA,CAAsB,YAAYrH,WAAW;YAErDyB,WAAA,CAAY8B,eAAe;YAC3B;UAAA;UAGF,IACEwG,IAAA,CAAKxJ,QAAS,CAAAP,WAAW,CACzB,IAAAA,WAAA,CAAYiK,sBAAA,KAA2BvR,OACvC;YAEYsH,WAAA,CAAAqH,qBAAA,CAAsB,eAAe3O,OAAO;YAExD+I,WAAA,CAAY8B,eAAe;YAC3B;UAAA;QACF;MACF;IACF,CACD;IAGwBmB,wBAAA,CAAAoE,OAAA,CAAQpQ,OAAQ,CAAAwR,aAAA,CAAc7N,IAAM;MAC3DuN,SAAW;MACXD,OAAS;IAAA,CACV;EAAA;EAGH,MAAMvR,EAAA,IAAK8K,GAAA,GAAAvJ,OAAA,CAAQ6E,aAAc,CAAArG,MAAA,KAAtB,gBAAA+K,GAA8B,CAAA9K,EAAA;EAEzC,MAAM+R,YAAA,GAAeA,CAAA,KAAM;IArc/B,IAAAC,GAAA;IAscU,KAACtE,mBAAuB,IAAA1N,EAAA,IAAM,IAAM;MACtC;IAAA;IAGF,MAAML,SAAY,GAAA4B,OAAA,CAAQkD,QAAS,CAAAC,UAAA,CAAWuN,GAAA,CAAIjS,EAAE;IACpD,MAAMkS,QAAA,IAAUF,GAAA,GAAArS,SAAA,oBAAAA,SAAA,CAAWiF,MAAX,YAAAoN,GAAA,GAAqBrS,SAAW,oBAAAA,SAAA,CAAAW,OAAA;IAE5C,IAAA6R,aAAA,CAAcD,QAAO,CAAG;MAC1BA,QAAA,CAAQE,KAAM;IAAA;EAChB,CACF;EACA,MAAMzK,OAAA,GAAUA,CAAA,KAAM;IACK0E,uBAAA,oBAAAA,uBAAA,CAAAgG,UAAA;IACC/F,wBAAA,oBAAAA,wBAAA,CAAA+F,UAAA;IAC1BhD,cAAA,CAAegD,UAAW;IACXhC,cAAA,CAAAiC,mBAAA,CAAoB,UAAU/B,kBAAkB;IAE3D,IAAAnB,eAAA,CAAgBjE,eAAe,CAAG;MACpBA,eAAA,CAAAmH,mBAAA,CACd,gBACArJ,mBAAA,CACF;MACAkC,eAAA,CAAgBzC,eAAA,CAAgB,SAAS;IAAA;IAG3CyC,eAAA,CAAgBzC,eAAA,CAAgB5B,SAAS;IACzCyF,MAAA,CAAOgG,KAAM;IAEbxS,MAAA,CAAOwG,MAAS;IAEV,MAAAiM,KAAA,GAAQrI,KAAM,CAAAE,OAAA,CAAQqC,SAAa;IAEzC,IACE9E,WAAA,KACC4K,KACC,IAAA5K,WAAA,CAAY6K,aAAA,KAAkBtH,eAAgB,CAAAsH,aAAA,KAChDtH,eAAA,CAAgBvH,WAChB;MACAgE,WAAA,CAAY8K,WAAA,CAAYvH,eAAe;IAAA;IAG5BvD,WAAA,oBAAAA,WAAA,CAAArC,MAAA;EAAA,CACf;EAEA,MAAMoN,cAAiB,GAAAC,OAAA;EAAA;EAErB,MAAM;IApfZ,IAAAZ,GAAA;IAqfQ,MAAM;MAACtD,SAAA,EAAAmE,UAAW;MAAAtM,MAAA,EAAAuM;IAAA,CAAU,GAAA1M,aAAA;IAExB,KAACyM,UAAA,CAAUhH,CAAK,KAACgH,UAAA,CAAU9G,CAAK,KAAC5B,KAAM,CAAAE,OAAA,CAAQqC,SAAW;MAC5D;IAAA;IAGF,IAAIoG,OAAA,CAAOC,QAAU;MACb,MAAAC,iBAAA,IAAmBhB,GAAA,GAAA5H,OAAA,CAAQsC,SAAR,YAAAsF,GAAA,GAAqB;QAACnG,CAAA,EAAG,CAAG;QAAAE,CAAA,EAAG;MAAC;MACzD,MAAMkH,UAAY;QAChBpH,CAAG,EAAAgH,UAAA,CAAUhH,CAAI,GAAAR,cAAA,CAAeS,MAAA,GAASkH,iBAAiB,CAAAnH,CAAA;QAC1DE,CAAG,EAAA8G,UAAA,CAAU9G,CAAI,GAAAV,cAAA,CAAeW,MAAA,GAASgH,iBAAiB,CAAAjH;MAAA,CAC5D;MACM,MAAAmH,iBAAA,GAAoB/I,KAAA,CAAME,OAAQ,CAAAqC,SAAA;MACxC,MAAMyG,SAAY,GAAA5L,SAAA,CAAU,MAAMnB,aAAA,CAAc+M,SAAS;MACnD,MAAAC,YAAA,GAAe7L,SAAA,CAAU,MAAG;QAngB5C,IAAA8L,GAAA;QAmgB+C,QAAAA,GAAA,GAAAjN,aAAA,CAAcqF,KAAd,qBAAA4H,GAAqB,CAAAhJ,OAAA;MAAA,CAAO;MAC3D,MAAAiJ,mBAAA,GAAsB5F,mBAAA,GACxB,qCACA;MAEGnB,MAAA,CAAAlE,GAAA,CACL;QACEoE,UAAY,KAAGA,UAAU,eAAe6G,mBAAmB;QAC3D5G,SAAA,EAAW,GAAGuG,UAAA,CAAUpH,CAAC,MAAMoH,UAAA,CAAUlH,CAAC;MAAA,CAC5C,EACAlF,UAAA,CACF;MACyBwF,uBAAA,oBAAAA,uBAAA,CAAAsD,WAAA;MAEzB,IACEyD,YAAA,IACAA,YAAiB,KAAAhD,YAAA,IACjB8C,iBACA,KAACC,SAAA,CAAUnP,MACX;QACA,MAAMuP,MAAQ,GAAAC,KAAA,CAAMhF,KAAM,CAAAyE,UAAA,EAAWC,iBAAiB;QAEtD9M,aAAA,CAAcqF,KAAA,GAAQgI,SAAU,CAAA3D,IAAA,CAC9BsD,YAAa,CAAAM,iBAAA,CACb,CAAAhH,SAAA;QAAA;QAEA6G,MAAA,CAAM1H,CAAA,GAAIR,cAAe,CAAAS,MAAA,EACzByH,MAAA,CAAMxH,CAAA,GAAIV,cAAe,CAAAW,MAAA,CAC3B;MAAA,CACK;QACS5F,aAAA,CAAAqF,KAAA,GAAQ,IAAIC,YAAA,CAAaP,eAAe;MAAA;MAGxDhB,KAAA,CAAME,OAAA,CAAQqC,SAAY,GAAAuG,UAAA;IAAA;EAC5B,CACF;EAAA;EAEA,YAAY;IACN,IAAA7M,aAAA,CAAcG,MAAA,CAAOoN,OAAS;MAEhC,KAAKC,OAAQ;MAEb7T,MAAA,CAAOwG,MAAS;MAEZ,IAAA0M,UAAA,GAAY9I,KAAA,CAAME,OAAQ,CAAAqC,SAAA;MAC9B,MAAM8F,KAAA,GAAQS,UAAa;MAEvB,KAACA,UAAa,IAAA3S,OAAA,KAAY6K,eAAiB;QAC7C8H,UAAY;UACVpH,CAAG;UACHE,CAAG;QAAA,CACL;MAAA;MAGF,IAAI,CAACkH,UAAW;QACNtL,OAAA;QACR;MAAA;MAGF,MAAMkM,aAAA,GAAgBA,CAAA,KAAM;QA9jBtC,IAAA7B,GAAA,EAAA8B,GAAA;QA+jBY;UAEEzK,WAAA,CAAY8B,eAAe;UAG3B,MAAM,GAAG4I,SAAS,KAChB/B,GAAA,GAAAgC,gBAAA,CACE7I,eAAA,EACC8I,QAAA,IAAa,WAAe,IAAAA,QAAA,CAC/B,KAHA,OAAAjC,GAAA,GAGK,EAAC;UAEG+B,SAAA,oBAAAA,SAAA,CAAAG,KAAA;UAEX,MAAMhU,MAAA,GAAS0H,WAAe,WAAAA,WAAA,GAAAtH,OAAA;UAC9B,MAAM6T,QAAU;YACd9I,cAAgB,EAAAtC,WAAA,CAAYoC,eAAiB,EAAAjL,MAAM,IAC/C,IACA;UAAA,CACN;UACA,MAAMmK,OAAU,OAAIqB,YAAa,CAAAP,eAAA,EAAiBgJ,QAAO;UAGnD,MAAAC,gBAAA,IACJN,GAAA,GAAAhG,cAAe,CAAAV,iBAAA,CAAkBjC,eAAe,CAAE,CAAAuB,SAAS,CAA3D,YAAAoH,GACA,GAAAb,UAAA;UACF,MAAMoB,KAAQ,OAAI3I,YAAa,CAAAxL,MAAA,EAAQiU,QAAO;UAC9C,MAAMZ,MAAA,GAAQE,SAAU,CAAAjF,KAAA,CAAMnE,OAAS,EAAAgK,KAAA,EAAOtU,MAAA,CAAOuU,SAAS;UAC9D,MAAMC,cAAiB;YACrB1I,CAAA,EAAGuI,gBAAiB,CAAAvI,CAAA,GAAI0H,MAAM,CAAA1H,CAAA;YAC9BE,CAAA,EAAGqI,gBAAiB,CAAArI,CAAA,GAAIwH,MAAM,CAAAxH;UAAA,CAChC;UACM,MAAAyI,eAAA,GACJC,IAAK,CAAAC,KAAA,CAAMrK,OAAQ,CAAAsK,eAAe,MAClCF,IAAK,CAAAC,KAAA,CAAML,KAAM,CAAAM,eAAe,CAC5B;YACEC,SAAW,GACT,GAAGvK,OAAA,CAAQsK,eAAe,MAC1B,GAAGN,KAAA,CAAMM,eAAe,KAC1B;YACAE,SAAW,GACT,GAAGxK,OAAA,CAAQsK,eAAe,MAC1B,GAAGN,KAAA,CAAMM,eAAe;UAC1B,IAEF,EAAC;UACD,MAAAG,cAAA,GACJL,IAAK,CAAAC,KAAA,CAAMrK,OAAQ,CAAA0K,cAAc,MACjCN,IAAK,CAAAC,KAAA,CAAML,KAAM,CAAAU,cAAc,CAC3B;YACEC,QAAU,GACR,GAAG3K,OAAA,CAAQ0K,cAAc,MACzB,GAAGV,KAAA,CAAMU,cAAc,KACzB;YACAE,QAAU,GACR,GAAG5K,OAAA,CAAQ0K,cAAc,MACzB,GAAGV,KAAA,CAAMU,cAAc;UACzB,IAEF,EAAC;UAEPxI,MAAA,CAAOlE,GAAI;YAACoE;UAAU,GAAG5F,UAAU;UACnBsE,eAAA,CAAAjK,YAAA,CAAa0F,kBAAA,EAAoB,EAAE;UAC1ByF,uBAAA,oBAAAA,uBAAA,CAAAsD,WAAA;UAERuF,gBAAA;YACf5U,OAAS,EAAA6K,eAAA;YACTgK,SAAA,EAAWC,aACN,CAAAC,cAAA,CAAAA,cAAA,KAAAb,eAAA,GACAM,cAFM;cAGTpI,SAAW,GACT,GAAG0H,gBAAA,CAAiBvI,CAAC,MAAMuI,gBAAA,CAAiBrI,CAAC,QAC7C,GAAGwI,cAAA,CAAe1I,CAAC,MAAM0I,cAAA,CAAexI,CAAC;YAC3C,CACF;YACAvK,OAAS;cACP8T,QAAU,EAAA9C,KAAA,IAASrH,eAAoB,KAAA7K,OAAA,GAAU,GAAM;cACvDiV,MAAQ;YAAA;UACV,CACD,CAAE,CAAAC,IAAA,CAAK,MAAM;YACZrK,eAAA,CAAgBzC,eAAA,CAAgB9B,kBAAkB;YACvCmN,SAAA,oBAAAA,SAAA,CAAA0B,MAAA;YACH9N,OAAA;YACRyB,qBAAA,CAAsB2I,YAAY;UAAA,CACnC;QAAA;MACH,CACF;MAEQxQ,OAAA,CAAAmU,QAAA,CAASC,SAAU,CAAAH,IAAA,CAAK3B,aAAa;IAAA;EAC/C,CACF,CACF;EAEA,OAAO,MAAM;IACHlM,OAAA;IACOgL,cAAA;EAAA,CACjB;AACF;AAEA7I,eAAA,GAAa,SAAAA,CAAA,EAAG;EAjqBlB,IAAAvF,GAAA,EAAAsG,GAAA;EAkqBI,MAAM;IAACtE,MAAQ;IAAAxG,MAAA;IAAQG;EAAM,IAAI,KAAKqB,OAAQ,CAAA6E,aAAA;EAE9C,IAAIG,MAAA,CAAO2E,YAAc;IACvB,MAAM0K,cAAA,GAAiBzP,WAAY,EAAA5B,GAAA,GAAAxE,MAAA,oBAAAA,MAAA,CAAQO,OAAR,YAAAiE,GAAA,GAAmB,IAAI;IAC1D,MAAMsR,cAAA,GAAiB1P,WAAY,EAAA0E,GAAA,GAAA3K,MAAA,oBAAAA,MAAA,CAAQI,OAAR,YAAAuK,GAAA,GAAmB,IAAI;IAC1D,MAAMiL,SAAA,kBAAgB,IAAA1R,GAAA,CAAI,CAACwR,cAAA,EAAgBC,cAAc,CAAC;IAE1D,WAAW5P,GAAA,IAAO6P,SAAW;MACvB,IAAAvL,YAAA,GAAehB,kBAAmB,CAAA0I,GAAA,CAAIhM,GAAG;MAE7C,IAAI,CAACsE,YAAc;QACX,MAAAzJ,KAAA,GAAQF,QAAS,CAAAC,aAAA,CAAc,OAAO;QAC5CC,KAAA,CAAME,WAAc,GAAAkG,SAAA;QAChBjB,GAAA,CAAAS,IAAA,CAAKqP,OAAA,CAAQjV,KAAK;QACtB,MAAMkV,gBAAmB,OAAIrF,gBAAiB,CAAC1N,OAAY;UACzD,WAAWwO,KAAA,IAASxO,OAAS;YACvB,IAAAwO,KAAA,CAAMnK,IAAA,KAAS,WAAa;cAC9B,MAAM2O,YAAe,GAAApG,KAAA,CAAMC,IAAK,CAAA2B,KAAA,CAAMwE,YAAY;cAElD,IAAIA,YAAA,CAAajS,MAAS,QAAKiS,YAAa,CAAAxV,QAAA,CAASK,KAAK,CAAG;gBAEvDmF,GAAA,CAAAS,IAAA,CAAKqP,OAAA,CAAQjV,KAAK;cAAA;YACxB;UACF;QACF,CACD;QACDkV,gBAAA,CAAiBtF,OAAA,CAAQzK,GAAI,CAAAS,IAAA,EAAM;UAAC8K,SAAA,EAAW;QAAA,CAAK;QAErCjH,YAAA;UACb5C,OAAA,EAASA,CAAA,KAAM;YACbqO,gBAAA,CAAiB3D,UAAW;YAC5BvR,KAAA,CAAMyE,MAAO;UAAA,CACf;UACAiF,SAAA,qBAAepG,GAAI;QAAA,CACrB;QACmBmF,kBAAA,CAAAlB,GAAA,CAAIpC,GAAA,EAAKsE,YAAY;MAAA;MAI7BA,YAAA,CAAAC,SAAA,CAAU3F,GAAA,CAAI,IAAI;IAAA;EACjC;AAEJ;AAvoBAqR,iBAAgB,CAAAxM,KAAA,gBADhBF,YAAA,EADWO,SAEK,EAAAJ,QAAA;AAFXwM,mBAAM,CAAAzM,KAAA,EAAAK,SAAA;AAAAA,SA4pBJ,CAAAqM,SAAA,GAAYC,YAAA,CAAatM,SAAQ;AA5pBnC,IAAMuM,QAAN,GAAAvM,SAAA;AChEP,IAAMwM,MAAS;AACf,IAAMC,QAAW;AAJjB,IAAAC,IAAA,EAAAC,GAAA,EAAAC,KAAA,EAAAC,EAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA;AAOEH,EAAA,IAAAD,KAAA,IAAC3M,QAA2B,GAAAgN,eAAA,CAAUC,OAAA,GACtCP,GAAA,IAAAD,IAAA,IAACzM,QAAA,GAA2BgN,eAAU,CAAAE,OAAA;AAFjC,IAAMC,UAAA,GAAN,MAAiB;EAAjB7V,YAAA;IACK2I,YAAA,OAAV6M,GAAA,EAAiD5M,iBAAvC,CAAA2M,MAAA,GAAuC,QAAAN,MAAA,IAAvCrM,iBAAA,CAAA2M,MAAA;IACA5M,YAAA,OAAV8M,GAAA,EAAiD7M,iBAAvC,CAAA2M,MAAA,IAAuC,QAAAN,MAAA,IAAvCrM,iBAAA,CAAA2M,MAAA;EAAA;EAEHO,SAASC,SAAgC;IAC1C,IAAAA,SAAA,KAAcL,eAAA,CAAUM,IAAM;MACzB;IAAA;IAGT,IAAID,SAAA,IAAa,IAAM;MAEnB,YAAKL,eAAA,CAAUC,OAAO,MAAMV,MAAA,IAAU,IAAK,CAAAS,eAAA,CAAUE,OAAO,CAAM,KAAAX,MAAA;IAAA;IAI/D,YAAKc,SAAS,CAAM,KAAAd,MAAA;EAAA;EAGtBgB,OAAOF,SAAsB;IAC9B,IAAAA,SAAA,KAAcL,eAAA,CAAUM,IAAM;MAChC;IAAA;IAGF,KAAKD,SAAS,CAAI,GAAAb,QAAA;EAAA;AAEtB;AAzBOK,MAAA,GAAAnM,gBAAA;AACLoM,GAAA,OAAAnM,OAAA;AACAoM,GAAA,OAAApM,OAAA;AADUuL,iBAAA,CAAAW,MAAA,KAAVD,EAAA,EAAAD,KAAA,EADWQ,UACX,EAAAL,GAAA;AACUZ,iBAAA,CAAAW,MAAA,KAAVH,GAAA,EAAAD,IAAA,EAFWU,UAEX,EAAAJ,GAAA;AAFKZ,mBAAA,CAAAU,MAAM,EAAAM,UAAA;;;ACIb,IAAMK,UAAa,IAACR,eAAgB,CAAAC,OAAA,EAASD,eAAA,CAAgBE,OAAO;AAEpE,IAAMO,YAAA,GAAN,MAAmB;EAAnBnW,YAAA;IACS,KAAAuK,CAAA,GAAI,IAAIsL,UAAW;IACnB,KAAApL,CAAA,GAAI,IAAIoL,UAAW;EAAA;EAEnBC,QAAoBA,CAAA;IACzB,OAAO,KAAKvL,CAAE,CAAAuL,QAAA,EAAc,SAAKrL,CAAA,CAAEqL,QAAS;EAAA;AAEhD;AAEO,IAAMM,mBAAA,GAAN,cAAkCrW,MAAwB;EAG/DC,YAAYC,OAA0B;IACpC,MAAMA,OAAO;IAEb,MAAMoW,YAAe,GAAAC,MAAA,CAAqB,IAAIH,YAAA,EAAc;IAC5D,IAAII,aAAoC;IAExC,KAAKD,MAAS,GAAAD,YAAA;IAEdtR,MAAA,CAAO,MAAM;MACL;QAACE;MAAM,IAAIhF,OAAQ,CAAA6E,aAAA;MAErB,KAACG,MAAA,CAAOC,WAAa;QACPqR,aAAA;QACHF,YAAA,CAAAhX,KAAA,GAAQ,IAAI8W,YAAa;QACtC;MAAA;MAGF,MAAM;QAACjJ;MAAA,CAAS,GAAAjN,OAAA,CAAQ6E,aAAc,CAAA2E,QAAA;MAEtC,IAAI8M,aAAe;QACjB,MAAMC,UAAa;UACjBjM,CAAG,EAAAkM,YAAA,CAAavJ,KAAM,CAAA3C,CAAA,EAAGgM,aAAA,CAAchM,CAAC;UACxCE,CAAG,EAAAgM,YAAA,CAAavJ,KAAM,CAAAzC,CAAA,EAAG8L,aAAA,CAAc9L,CAAC;QAAA,CAC1C;QAEM,MAAAiM,MAAA,GAASL,YAAA,CAAaM,IAAK;QAEjCC,KAAA,CAAM,MAAM;UACV,WAAWC,IAAA,IAAQC,IAAM;YACvB,WAAWf,SAAA,IAAaG,UAAY;cAC9B,IAAAM,UAAA,CAAWK,IAAI,MAAMd,SAAW;gBAC3BW,MAAA,CAAAG,IAAI,CAAE,CAAAZ,MAAA,CAAOF,SAAS;cAAA;YAC/B;UACF;UAGFM,YAAA,CAAahX,KAAQ,GAAAqX,MAAA;QAAA,CACtB;MAAA;MAGaH,aAAA,GAAArJ,KAAA;IAAA,CACjB;EAAA;EAGH,IAAInE,OAA+BA,CAAA;IAC1B,YAAKuN,MAAA,CAAOK,IAAK;EAAA;AAE5B;AAEA,SAASF,aAAaM,CAAA,EAAWC,CAA4B;EACpD,OAAA7D,IAAA,CAAK8D,IAAK,CAAAF,CAAA,GAAIC,CAAC;AACxB;;;AC3EA,IAAAE,kBAAA,EAAAC,GAAA,EAAAC,MAAA,EAAAC,cAAA,EAAAC,KAAA,EAAAC,OAAA;AAmBO,IAAMC,QAAA,GAAN,eAAuBL,GAAA,GAAAM,UAK5B,EAAAP,kBAAA,IAACxO,QAAA,GAL2ByO,GAA4B;EAQxDnX,YAAYC,OAA0B;IACpC,MAAMA,OAAO;IAHf0I,YAAA,OAAgB0O,cAAA,EAAgBzO,iBAAhC,CAAAwO,MAAA,GAAgC,iBAAhCxO,iBAAA,CAAAwO,MAAA;IAyFAzO,YAAA,OAAA2O,KAAA;IAEA3O,YAAA,OAAA4O,OAAA,EAAU,MAAM;MACV,KAACG,YAAA,OAAKJ,KAAO;QACf;MAAA;MAGF,MAAM;QAACtY,OAAA;QAAS2Y;MAAE,IAAID,YAAK,OAAAJ,KAAA;MAE3B,IAAIK,EAAG,CAAAlN,CAAA,EAAWzL,OAAA,CAAA4Y,SAAA,IAAaD,EAAG,CAAAlN,CAAA;MAClC,IAAIkN,EAAG,CAAApN,CAAA,EAAWvL,OAAA,CAAA6Y,UAAA,IAAcF,EAAG,CAAApN,CAAA;IAAA,CACrC;IAEO,KAAAuN,MAAA,GAAU5X,OAAyC;MA/H5D,IAAA+C,GAAA;MAgII,IAAI,KAAKa,QAAU;QACV;MAAA;MAGH,MAAAzB,QAAA,GAAW,KAAK0V,qBAAsB;MAE5C,IAAI,CAAC1V,QAAU;QACb2V,YAAA,OAAKV,KAAQ;QACN;MAAA;MAGT,MAAM;QAAC7N;MAAA,CAAY,QAAKxJ,OAAQ,CAAA6E,aAAA;MAChC,MAAMmT,eAAA,GAAkBxO,QAAU,oBAAAA,QAAA,CAAAV,OAAA;MAElC,IAAIkP,eAAiB;QACnB,MAAM;UAACN;QAAA,CAAM,GAAAzX,OAAA,WAAAA,OAAA,GAAW,EAAC;QACzB,MAAMwW,MAAA,GAASiB,EACX;UACEpN,CAAA,EAAG2N,eAAgB,CAAAP,EAAA,CAAGpN,CAAC;UACvBE,CAAA,EAAGyN,eAAgB,CAAAP,EAAA,CAAGlN,CAAC;QAAA,CAEzB;QACJ,MAAM4L,YAAe,GAAAK,MAAA,GACjB,MACA,QAAKyB,mBAAoB,CAAApP,OAAA;QAE7B,IAAIsN,YAAA,oBAAAA,YAAA,CAAcP,QAAY;UACrB;QAAA;QAGT,WAAWsC,iBAAA,IAAqB/V,QAAU;UAClC,MAAAgW,gBAAA,GAAmBC,SAAU,CAAAF,iBAAA,EAAmBT,EAAE;UAEpD,IAAAU,gBAAA,CAAiB9N,CAAK,IAAA8N,gBAAA,CAAiB5N,CAAG;YACtC;cAAC8N,KAAO;cAAAxC;YAAA,CAAa,GAAAyC,kBAAA,CACzBJ,iBAAA,EACAH,eAAA,EACAvB,MAAA,CACF;YAEA,IAAIL,YAAc;cAChB,WAAWQ,IAAA,IAAQC,IAAM;gBACvB,IAAIT,YAAA,CAAaQ,IAAI,EAAEf,QAAA,CAASC,SAAU,CAAAc,IAAI,CAAC,CAAG;kBAChD0B,KAAA,CAAM1B,IAAI,CAAI;kBACdd,SAAA,CAAUc,IAAI,CAAI;gBAAA;cACpB;YACF;YAGE,IAAAd,SAAA,CAAUxL,CAAK,IAAAwL,SAAA,CAAUtL,CAAG;cAC9B,MAAM;gBAACF,CAAA;gBAAGE;cAAC,IAAIkN,EAAM,WAAAA,EAAA,GAAA5B,SAAA;cACf,MAAA0C,YAAA,GAAelO,CAAA,GAAIgO,KAAM,CAAAhO,CAAA;cACzB,MAAAmO,WAAA,GAAcjO,CAAA,GAAI8N,KAAM,CAAA9N,CAAA;cAE9B,IAAIgO,YAAA,IAAgBC,WAAa;gBAC/B,MAAMC,gBAAmB,IAAA1V,GAAA,GAAAyU,YAAK,OAAAJ,KAAA,MAAL,gBAAArU,GAAY,CAAA0U,EAAA;gBAEjC,SAAKiB,aAAA,IAAiBD,gBAAkB;kBAC1C,MAAME,oBAAA,GACHF,gBAAiB,CAAApO,CAAA,IAAK,CAACkO,YACvB,IAAAE,gBAAA,CAAiBlO,CAAA,IAAK,CAACiO,WAAA;kBAE1B,IAAIG,oBAAsB;gBAAA;gBAG5Bb,YAAA,OAAKV,KAAQ;kBACXtY,OAAS,EAAAoZ,iBAAA;kBACTT,EAAI;oBACFpN,CAAG,EAAAkO,YAAA;oBACHhO,CAAG,EAAAiO;kBAAA;gBACL,CACF;gBAEApX,SAAA,CAAUC,QAAS,CAAAmW,YAAA,OAAKH,OAAO;gBAExB;cAAA;YACT;UACF;QACF;MACF;MAGFS,YAAA,OAAKV,KAAQ;MACN;IAAA,CACT;IAtLE,IAAIwB,wBAA2C;IAC/C,IAAIC,0BAAkD;IAChD,MAAAC,gBAAA,GAAmBpU,QAAA,CAAS,MAAM;MACtC,MAAM;QAAC6E,QAAA;QAAUhL;MAAM,IAAIwB,OAAQ,CAAA6E,aAAA;MAEnC,IAAI,CAAC2E,QAAU;QACN;MAAA;MAGT,MAAMzK,OAAU,GAAAia,mBAAA,CACdpU,WAAA,CAAYpG,MAAA,oBAAAA,MAAA,CAAQO,OAAO,GAC3ByK,QAAS,CAAAV,OAAA,CACX;MAEA,IAAI/J,OAAS;QACgB8Z,wBAAA,GAAA9Z,OAAA;MAAA;MAG7B,OAAOA,OAAW,WAAAA,OAAA,GAAA8Z,wBAAA;IAAA,CACnB;IACK,MAAAI,kBAAA,GAAqBtU,QAAA,CAAS,MAAM;MACxC,MAAM5F,OAAA,GAAUga,gBAAiB,CAAA3Z,KAAA;MACjC,MAAM;QAAC8Z;MAAA,CAAmB,GAAAtU,WAAA,CAAY7F,OAAO;MAEzC,KAACA,OAAW,IAAAA,OAAA,KAAYma,eAAiB;QACrC;UAACva;QAAM,IAAIqB,OAAQ,CAAA6E,aAAA;QACzB,MAAMsU,aAAA,GAAgBxa,MAAQ,oBAAAA,MAAA,CAAAI,OAAA;QAE9B,IAAIoa,aAAe;UACX,MAAA/W,QAAA,GAAWgX,sBAAA,CAAuBD,aAAe;YACrDE,cAAgB;UAAA,CACjB;UAC4BP,0BAAA,GAAA1W,QAAA;UAEtB,OAAAA,QAAA;QAAA;MACT;MAGF,IAAIrD,OAAS;QACL,MAAAqD,QAAA,GAAWgX,sBAAA,CAAuBra,OAAS;UAC/Csa,cAAgB;QAAA,CACjB;QAED,IACE,KAAKV,aACL,IAAAG,0BAAA,IACA1W,QAAS,CAAA0B,IAAA,IAAOgV,0BAAA,oBAAAA,0BAAA,CAA4BhV,IAC5C;UACO,OAAAgV,0BAAA;QAAA;QAGoBA,0BAAA,GAAA1W,QAAA;QAEtB,OAAAA,QAAA;MAAA;MAGoB0W,0BAAA;MAEtB;IAAA,GACNQ,SAAS;IAEZ,KAAKxB,qBAAA,GAAwB,MAAM;MACjC,OAAOmB,kBAAmB,CAAA7Z,KAAA;IAAA,CAC5B;IAEK,KAAA8Y,mBAAA,GAAsB,IAAI/B,mBAAA,CAAoBnW,OAAO;IAE1D,KAAK+D,OAAA,GAAU/D,OAAQ,CAAA8B,OAAA,CAAQC,gBAAiB,aAAaC,KAAU;MACrE,IACE,IAAK,CAAA6B,QAAA,IACL7B,KAAM,CAAAuX,gBAAA,IACN,CAACnN,eAAA,CAAgBpM,OAAQ,CAAA6E,aAAA,CAAcwH,cAAc,KACrD,CAACrK,KAAA,CAAM0V,EACP;QACA;MAAA;MAIF,IAAI,KAAKG,MAAO;QAACH,EAAA,EAAI1V,KAAM,CAAA0V;MAAA,CAAG,CAAG;QAC/B1V,KAAA,CAAMwX,cAAe;MAAA;IACvB,CACD;EAAA;AAsGL;AAlMOrC,MAAA,GAAAhO,gBAAA,CAAA+N,GAAA;AAMWE,cAAA,OAAAhO,OAAA;AAyFhBiO,KAAA,OAAAjO,OAAA;AAEAkO,OAAA,OAAAlO,OAAA;AA3FAuL,iBAAA,CAAAwC,MAAA,KAAgB,eADhB,EAAAF,kBAAA,EALWM,QAMK,EAAAH,cAAA;AANXxC,mBAAA,CAAAuC,MAAM,EAAAI,QAAA;AAoMb,SAASU,gBAAgB7Y,KAAe;EACtC,IAAIA,KAAA,GAAQ,CAAG;IACb,OAAOqW,eAAgB,CAAAC,OAAA;EAAA;EAGzB,IAAItW,KAAA,GAAQ,CAAG;IACb,OAAOqW,eAAgB,CAAAE,OAAA;EAAA;EAGzB,OAAOF,eAAgB,CAAAM,IAAA;AACzB;;;AC/NO,IAAM0D,SAAA,GAAN,MAAuD;EAC5D1Z,YAAoB2Z,UAAc;IAAd,KAAArY,SAAA,GAAAqY,UAAA;IAEpB,KAAQC,OAAmB;IACnB,KAAAC,KAAA,sBAA6B/W,GAAI;IACjC,KAAAgX,SAAA,sBAAiChX,GAAI;IAa7C,KAAOiX,KAAA,GAAQ,MAAM;MACb;QAACF,KAAO;QAAAC;MAAA,CAAa;MAE3B,KAAKF,OAAU;MACV,KAAAC,KAAA,sBAAY/W,GAAI;MAChB,KAAAgX,SAAA,sBAAgBhX,GAAI;MAEzB,WAAWkX,IAAA,IAAQH,KAAO;QACnBG,IAAA;MAAA;MAGP,WAAWC,OAAA,IAAWH,SAAW;QACvBG,OAAA;MAAA;IACV,CACF;EAAA;EAzBO1Y,SAASyY,IAAiC;IAC1C,KAAAH,KAAA,CAAMtW,GAAA,CAAIyW,IAAI;IAEf,KAAC,KAAKJ,OAAS;MACjB,KAAKA,OAAU;MACV,KAAAtY,SAAA,CAAU,KAAKyY,KAAK;IAAA;IAGpB,WAAIG,OAAA,CAAeD,OAAA,IAAY,KAAKH,SAAU,CAAAvW,GAAA,CAAI0W,OAAO,CAAC;EAAA;AAkBrE;AAEO,IAAME,UAAY,OAAIT,SAAU,CAACU,QAAa;EAC/C,WAAOtS,qBAAA,KAA0B,UAAY;IAC/CA,qBAAA,CAAsBsS,QAAQ;EAAA,CACzB;IACIA,QAAA;EAAA;AAEb,CAAC;;;ACjCD,IAAMC,mBAAsB;AAEf,IAAAC,YAAA,GAAN,cAA2Bva,MAAwB;EAGxDC,YAAYC,OAAA,EAA0Bsa,QAAoB;IACxD,MAAMta,OAAO;IAEb,MAAMua,QAAW,GAAAva,OAAA,CAAQkD,QAAS,CAAAsX,OAAA,CAAQ9J,GAAA,CAAI6G,QAAQ;IAEtD,IAAI,CAACgD,QAAU;MACP,UAAIE,KAAA,CAAM,gDAAgD;IAAA;IAG7D,KAAA1W,OAAA,GAAUe,MAAA,CAAO,MAAM;MAC1B,IAAI,KAAKjB,QAAU;QACjB;MAAA;MAKF,MAAM;QAAC2F,QAAA,EAAUkR,CAAG;QAAA1V;MAAA,IAAUhF,OAAQ,CAAA6E,aAAA;MAEtC,IAAIG,MAAA,CAAOwM,QAAU;QACb,MAAAmJ,UAAA,GAAYJ,QAAA,CAAS1C,MAAO;QAElC,IAAI8C,UAAW;UACbJ,QAAA,CAAS5B,aAAgB;UACzB,MAAMiC,QAAW,GAAAC,WAAA,CACf,MAAMX,UAAA,CAAU5Y,QAAS,CAAAiZ,QAAA,CAAS1C,MAAM,GACxCuC,mBAAA,CACF;UAEA,OAAO,MAAM;YACXU,aAAA,CAAcF,QAAQ;UAAA,CACxB;QAAA,CACK;UACLL,QAAA,CAAS5B,aAAgB;QAAA;MAC3B;IACF,CACD;EAAA;AAEL;AC/CA,IAAMoC,eAA2C;EAC/CC,OAAS;EACTC,OAAS;AACX;AARA,IAAAC,QAAA;AAUa,IAAAC,cAAA,GAAN,cAA6B3D,UAA4B;EAG9DzX,YAAYC,OAA0B;IACpC,MAAMA,OAAO;IAHf0I,YAAA,OAAAwS,QAAA;IA0BA,KAAQE,YAAA,GAAe,MAAM;MACvB,IAAA3D,YAAA,OAAKyD,QAAA,KAAY,IAAM;QACpBnD,YAAA,OAAAmD,QAAA,EAAW1W,UAAA,CAAW,MAAM;UAC1B,KAAAxE,OAAA,CAAQqb,iBAAkB,CAAAC,WAAA,CAAY,KAAK;UAChDvD,YAAA,OAAKmD,QAAW;QAAA,GACf,EAAE;MAAA;IACP,CACF;IA5BQ;MAACrW;IAAa,IAAI,IAAK,CAAA7E,OAAA;IAExB,KAAA+D,OAAA,GAAUe,MAAA,CAAO,MAAM;MAlBhC,IAAA9B,GAAA,EAAAsG,GAAA,EAAAC,GAAA;MAmBY,MAAAgS,OAAA,GAAU1W,aAAA,CAAcG,MAAO,CAAAwM,QAAA;MAErC,IAAI+J,OAAS;QACX,MAAM3N,IAAO,IAAArE,GAAA,IAAAD,GAAA,IAAAtG,GAAA,GAAA6B,aAAc,CAAArG,MAAA,KAAd,gBAAAwE,GAAA,CAAsBjE,OAAtB,qBAAAuK,GAA+B,CAAAiH,aAAA,KAA/B,OAAAhH,GAAgD,GAAAlK,QAAA;QAE7DuO,IAAA,CAAK7L,gBAAiB,WAAU,IAAK,CAAAqZ,YAAA,EAAcL,eAAe;QAElE,OAAO,MAAM;UACNnN,IAAA,CAAAmD,mBAAA,CACH,UACA,IAAK,CAAAqK,YAAA,EACLL,eAAA,CACF;QAAA,CACF;MAAA;IACF,CACD;EAAA;AAWL;AAlCEG,QAAA,OAAA9R,OAAA;ACNW,IAAAoS,gBAAA,GAAN,cAA+B1b,MAAwB;EAC5DC,YAAmBC,OAA0B;IAC3C,MAAMA,OAAO;IADI,KAAAA,OAAA,GAAAA,OAAA;IAGZ,KAAA+D,OAAA,GAAUe,MAAA,CAAO,MAAM;MACpB;QAACD;MAAa,IAAI,IAAK,CAAA7E,OAAA;MAEzB,IAAA6E,aAAA,CAAcG,MAAA,CAAOC,WAAa;QAC9B,MAAA1F,KAAA,GAAQF,QAAS,CAAAC,aAAA,CAAc,OAAO;QAC5CC,KAAA,CAAME,WAAc;QACXJ,QAAA,CAAA8F,IAAA,CAAK3C,WAAA,CAAYjD,KAAK;QAEfkc,eAAA;QACPpc,QAAA,CAAA0C,gBAAA,CAAiB,mBAAmB0Z,eAAiB;UAC5DT,OAAS;QAAA,CACV;QAED,OAAO,MAAM;UACF3b,QAAA,CAAA0R,mBAAA,CAAoB,mBAAmB0K,eAAiB;YAC/DT,OAAS;UAAA,CACV;UACDzb,KAAA,CAAMyE,MAAO;QAAA,CACf;MAAA;IACF,CACD;EAAA;AAEL;AAEA,SAASyX,eAAkBA,CAAA;EAjC3B,IAAAzY,GAAA;EAkCE,CAAAA,GAAA,GAAA3D,QAAA,CAASqc,YAAa,OAAtB,gBAAA1Y,GAAyB,CAAA2Y,eAAA;AAC3B;ACwBA,IAAMC,QAAA,GAAWna,MAAA,CAAOoa,MAAwC;EAC9DC,MAAQ;EACRC,aAAe;IACbC,KAAA,EAAO,CAAC,SAAS,OAAO;IACxB9Z,MAAA,EAAQ,CAAC,QAAQ;IACjB+Z,GAAK,GAAC,OAAS,WAAS,KAAK;IAC7BC,EAAA,EAAI,CAAC,SAAS;IACdC,IAAA,EAAM,CAAC,WAAW;IAClBtR,IAAA,EAAM,CAAC,WAAW;IAClBuR,KAAA,EAAO,CAAC,YAAY;EAAA,CACtB;EACAC,eAAeC,IAIZ;IA1EL,IAAAtZ,GAAA;IA2EU;MAAChB,KAAO;MAAAxD;IAAA,CAAU,GAAA8d,IAAA;IACxB,MAAM3d,MAAA,IAASqE,GAAA,GAAAxE,MAAA,CAAO6E,MAAP,YAAAL,GAAA,GAAiBxE,MAAO,CAAAO,OAAA;IACvC,OAAOiD,KAAA,CAAMrD,MAAW,KAAAA,MAAA;EAAA;AAE5B,CAAC;AA/ED,IAAA4d,iBAAA;AAoFO,IAAMC,eAAA,GAAN,MAAMA,eAAA,SAAuBC,MAGlC;EACA1c,YACSC,OAAA,EACAC,OACP;IACA,MAAMD,OAAO;IAHN,KAAAA,OAAA,GAAAA,OAAA;IACA,KAAAC,OAAA,GAAAA,OAAA;IAKTyI,YAAA,OAAA6T,iBAAA,EAAuC,EAAC;IAE9B,KAAAG,SAAA,GAAY,IAAIC,SAAU;IAuBpC,KAAUC,mBAAsB,IAC9B5a,KACA,EAAAxD,MAAA,EACAyB,OACG;MACC,SAAK4D,QAAY,IAAA7B,KAAA,CAAMuX,gBAAkB;QAC3C;MAAA;MAGF,IAAI,CAAC3R,SAAA,CAAU5F,KAAM,CAAArD,MAAM,CAAG;QAC5B;MAAA;MAGF,IAAIH,MAAA,CAAOqF,QAAU;QACnB;MAAA;MAGI;QACJkY,aAAA,GAAgBH,QAAS,CAAAG,aAAA;QACzBM,cAAA,GAAiBT,QAAS,CAAAS;MAAA,CAC5B,GAAIpc,OAAA,WAAAA,OAAA,GAAW,EAAC;MAEhB,IAAI,CAAC8b,aAAc,CAAAC,KAAA,CAAM9c,QAAS,CAAA8C,KAAA,CAAM6a,IAAI,CAAG;QAC7C;MAAA;MAGF,IAAI,CAAC,KAAK7c,OAAQ,CAAA6E,aAAA,CAAcG,MAAA,CAAOyE,IAAM;QAC3C;MAAA;MAGE,IAAA4S,cAAA,CAAe;QAACra,KAAO;QAAAxD,MAAA;QAAQwB,OAAA,EAAS,IAAK,CAAAA;MAAA,CAAQ,CAAG;QACrD,KAAA8c,WAAA,CAAY9a,KAAO,EAAAxD,MAAA,EAAQyB,OAAO;MAAA;IACzC,CACF;EAAA;EAtDO8c,IAAKA,CAAAve,MAAA,EAAmByB,OAAU,QAAKA,OAAS;IAC/C,MAAA+c,MAAA,GAASlY,MAAA,CAAO,MAAM;MApGhC,IAAA9B,GAAA;MAqGM,MAAMrE,MAAA,IAASqE,GAAA,GAAAxE,MAAA,CAAO6E,MAAP,YAAAL,GAAA,GAAiBxE,MAAO,CAAAO,OAAA;MACjC,MAAAke,QAAA,GAA2Bjb,KAAiB;QAC5C,IAAAoK,eAAA,CAAgBpK,KAAK,CAAG;UACrB,KAAA4a,mBAAA,CAAoB5a,KAAO,EAAAxD,MAAA,EAAQyB,OAAO;QAAA;MACjD,CACF;MAEA,IAAItB,MAAQ;QACHA,MAAA,CAAAoD,gBAAA,CAAiB,WAAWkb,QAAQ;QAE3C,OAAO,MAAM;UACJte,MAAA,CAAAoS,mBAAA,CAAoB,WAAWkM,QAAQ;QAAA,CAChD;MAAA;IACF,CACD;IAEM,OAAAD,MAAA;EAAA;EAsCCF,YACR9a,KACA,EAAAxD,MAAA,EACAyB,OACA;IACM;MAAClB;IAAA,CAAW,GAAAP,MAAA;IAElB,IAAI,CAACO,OAAS;MACN,UAAI0b,KAAA,CAAM,sDAAsD;IAAA;IAGxEzY,KAAA,CAAMwX,cAAe;IACrBxX,KAAA,CAAMkb,wBAAyB;IAE/BC,sBAAA,CAAuBpe,OAAO;IAE9B,MAAM;MAACqe;IAAA,CAAU,OAAIjT,YAAA,CAAapL,OAAO;IACzC,MAAMse,UAAa,QAAKrd,OAAQ,CAAAiP,OAAA,CAAQ+M,KAAM;MAC5Cha,KAAA;MACA2K,WAAa;QACXrC,CAAA,EAAG8S,MAAO,CAAA9S,CAAA;QACVE,CAAA,EAAG4S,MAAO,CAAA5S;MAAA,CACZ;MACAhM;IAAA,CACD;IAED,IAAI6e,UAAW,CAAAhH,MAAA,CAAOiH,OAAS,SAAO,KAAKlX,OAAQ;IAEnD,KAAKmX,WAAY;IAEX,MAAAlJ,cAAA,GAAiBzP,WAAA,CAAY7F,OAAO;IAC1C,MAAM2d,SAAY,IAChB,KAAKA,SAAU,CAAAK,IAAA,CAAK1I,cAAgB,GAClC;MACEtO,IAAM;MACNkX,QAAA,EAAWO,MAAA,IACT,KAAKC,aAAc,CAAAD,MAAA,EAAOhf,MAAA,EAAQyB,OAAO;MAC3CA,OAAA,EAAS;QAAC+a,OAAA,EAAS;MAAI;IAAA,EAE1B,EACH;IAEKvD,YAAA,OAAA8E,iBAAA,EAAkBja,IAAK,IAAGoa,SAAS;EAAA;EAGhCe,cACRzb,KACA,EAAA0b,OAAA,EACAzd,OACA;IACA,MAAM;MAAC8b,aAAgB,GAAAH,QAAA,CAASG;IAAa,IAAI9b,OAAA,WAAAA,OAAA,GAAW,EAAC;IAEzD,IAAA0d,SAAA,CAAU3b,KAAO,GAAC,GAAG+Z,aAAA,CAAcE,GAAA,EAAK,GAAGF,aAAA,CAAc7Z,MAAM,CAAC,CAAG;MACrEF,KAAA,CAAMwX,cAAe;MACrB,MAAM3a,QAAW,GAAA8e,SAAA,CAAU3b,KAAO,EAAA+Z,aAAA,CAAc7Z,MAAM;MAEjD,KAAA0b,SAAA,CAAU5b,KAAA,EAAOnD,QAAQ;MAC9B;IAAA;IAGF,IAAI8e,SAAU,CAAA3b,KAAA,EAAO+Z,aAAc,CAAAG,EAAE,CAAG;MACjC,KAAA2B,UAAA,CAAW,MAAM7b,KAAK;IAAA,CAClB,UAAA2b,SAAA,CAAU3b,KAAO,EAAA+Z,aAAA,CAAcI,IAAI,CAAG;MAC1C,KAAA0B,UAAA,CAAW,QAAQ7b,KAAK;IAAA;IAG/B,IAAI2b,SAAU,CAAA3b,KAAA,EAAO+Z,aAAc,CAAAlR,IAAI,CAAG;MACnC,KAAAgT,UAAA,CAAW,QAAQ7b,KAAK;IAAA,CACpB,UAAA2b,SAAA,CAAU3b,KAAO,EAAA+Z,aAAA,CAAcK,KAAK,CAAG;MAC3C,KAAAyB,UAAA,CAAW,SAAS7b,KAAK;IAAA;EAChC;EAGQ4b,UAAU5b,KAAA,EAAcnD,QAAmB;IAC9C,KAAAmB,OAAA,CAAQiP,OAAA,CAAQC,IAAK;MACxBlN,KAAA;MACAnD;IAAA,CACD;IAED,KAAKuH,OAAQ;EAAA;EAGLyX,WACR/H,SAAA,EACA9T,KACA;IAhPJ,IAAAgB,GAAA,EAAAsG,GAAA;IAiPI,MAAM;MAACY;IAAA,CAAS,QAAKlK,OAAQ,CAAA6E,aAAA;IACvB,MAAAiZ,MAAA,GAAS9b,KAAM,CAAA+b,QAAA,GAAW,CAAI;IACpC,IAAIrG,EAAK;MACPpN,CAAG;MACHE,CAAG;IAAA,CACL;IACI,IAAAsR,MAAA,IAASxS,GAAA,IAAAtG,GAAA,OAAK,CAAA/C,OAAA,KAAL,gBAAA+C,GAAc,CAAA8Y,MAAA,KAAd,OAAAxS,GAAA,GAAwBsS,QAAS,CAAAE,MAAA;IAE1C,WAAOA,MAAA,KAAW,QAAU;MAC9BA,MAAA,GAAS;QAACxR,CAAA,EAAGwR,MAAQ;QAAAtR,CAAA,EAAGsR;MAAM;IAAA;IAGhC,IAAI,CAAC5R,KAAO;MACV;IAAA;IAGF,QAAQ4L,SAAW;MACjB,KAAK;QACH4B,EAAA,GAAK;UAACpN,CAAG;UAAGE,CAAA,EAAG,CAACsR,MAAA,CAAOtR,CAAA,GAAIsT;QAAM;QACjC;MACF,KAAK;QACHpG,EAAA,GAAK;UAACpN,CAAG;UAAGE,CAAG,EAAAsR,MAAA,CAAOtR,CAAA,GAAIsT;QAAM;QAChC;MACF,KAAK;QACHpG,EAAA,GAAK;UAACpN,CAAG,GAACwR,MAAA,CAAOxR,CAAI,GAAAwT,MAAA;UAAQtT,CAAA,EAAG;QAAC;QACjC;MACF,KAAK;QACHkN,EAAA,GAAK;UAACpN,CAAG,EAAAwR,MAAA,CAAOxR,CAAI,GAAAwT,MAAA;UAAQtT,CAAA,EAAG;QAAC;QAChC;IAAA;IAGA,IAAAkN,EAAA,CAAGpN,CAAK,IAAAoN,EAAA,CAAGlN,CAAG;MAChBxI,KAAA,CAAMwX,cAAe;MAEhB,KAAAxZ,OAAA,CAAQiP,OAAA,CAAQ+O,IAAK;QACxBhc,KAAA;QACA0V;MAAA,CACD;IAAA;EACH;EAGM6F,WAAcA,CAAA;IACpB,MAAMU,YAAA,GAAe,IAAK,CAAAje,OAAA,CAAQkD,QAAS,CAAAsX,OAAA,CAAQ9J,GAAA,CAAI2J,YAAmB;IAEtE,KAAA4D,YAAA,oBAAAA,YAAA,CAAcpa,QAAA,MAAa,KAAO;MACpCoa,YAAA,CAAaC,OAAQ;MAEhBzG,YAAA,OAAA8E,iBAAA,EAAkBja,IAAA,CAAK,MAAM;QAChC2b,YAAA,CAAaE,MAAO;MAAA,CACrB;IAAA;EACH;EAGQ/X,OAAUA,CAAA;IAClBqR,YAAA,OAAK8E,iBAAkB,EAAAtY,OAAA,CAASmC,OAAA,IAAYA,OAAA,EAAS;IACrD2R,YAAA,OAAKwE,iBAAA,EAAoB,EAAC;EAAA;EAGrBxY,OAAUA,CAAA;IACf,KAAKqC,OAAQ;IAEb,KAAKsW,SAAA,CAAUzZ,KAAM;EAAA;AAMzB;AArNEsZ,iBAAA,OAAAnT,OAAA;AAXWoT,eA6NJ,CAAA3H,SAAA,GAAYC,YAAA,CAAa0H,eAAc;AA7NnCA,eAAA,CA+NJZ,QAAW,GAAAA,QAAA;AA/Nb,IAAMwC,cAAN,GAAA5B,eAAA;AAkOP,SAASmB,UAAU3b,KAAA,EAAsBqc,KAAkB;EAClD,OAAAA,KAAA,CAAMnf,QAAS,CAAA8C,KAAA,CAAM6a,IAAI;AAClC;ACrQA,IAAMyB,SAAA,GAAW7c,MAAA,CAAOoa,MAA6B;EACnD0C,sBAAsBvc,KAAA,EAAOxD,MAAQ;IApDvC,IAAAwE,GAAA;IAqDU;MAACwb,WAAa;MAAA7f;IAAA,CAAU,GAAAqD,KAAA;IAE9B,IACEwc,WAAgB,gBAChB5W,SAAU,CAAAjJ,MAAM,MACfH,MAAO,CAAA6E,MAAA,KAAW1E,MAAU,MAAAqE,GAAA,GAAAxE,MAAO,CAAA6E,MAAA,KAAP,gBAAAL,GAAA,CAAe4D,QAAA,CAASjI,MACrD;MACO;IAAA;IAGT,IAAI6f,WAAA,KAAgB,OAAS;MACpB;QACLC,KAAO;UAACrf,KAAO;UAAKsf,SAAA,EAAW;QAAC;MAAA,CAClC;IAAA;IAGF,IAAIC,WAAY,CAAAhgB,MAAM,CAAK,KAACqD,KAAA,CAAMuX,gBAAkB;MAC3C;QACLkF,KAAO;UAACrf,KAAO;UAAKsf,SAAA,EAAW;QAAC;MAAA,CAClC;IAAA;IAGK;MACLD,KAAO;QAACrf,KAAO;QAAKsf,SAAA,EAAW;MAAE;MACjCE,QAAA,EAAU;QAACxf,KAAA,EAAO;MAAC;IAAA,CACrB;EAAA;AAEJ,CAAC;AAhFD,IAAAyf,QAAA,EAAAC,aAAA;AA2FO,IAAMC,cAAA,GAAN,MAAMA,cAAA,SAAsBtC,MAGjC;EASA1c,YACSC,OAAA,EACAC,OACP;IACA,MAAMD,OAAO;IAHN,KAAAA,OAAA,GAAAA,OAAA;IACA,KAAAC,OAAA,GAAAA,OAAA;IAVTyI,YAAA,OAAAmW,QAAA,qBAAqChc,GAAI;IAEzC6F,YAAA,OAAAoW,aAAA;IAEU,KAAApC,SAAA,GAAY,IAAIC,SAAU;IAiJpC,KAAQqC,MAAsB;MAC5Bhd,KAAO;MACP2K,WAAa;IAAA,CACf;IAEA,KAAUkR,UAAA,GAAa,MAAM;MAC3B,MAAM;QAAC7b,KAAA;QAAO2K,WAAa,EAAAsS;MAAA,IAAM,IAAK,CAAAD,MAAA;MAElC,KAAChd,KAAS,KAACid,EAAI;QACjB;MAAA;MAGF,KAAKjf,OAAA,CAAQiP,OAAQ,CAAA+O,IAAA,CAAK;QAAChc,KAAA;QAAOid;MAAA,CAAG;IAAA,CACvC;IApJE,KAAKC,YAAe,QAAKA,YAAa,CAAAnC,IAAA,CAAK,IAAI;IAC/C,KAAKoC,eAAkB,QAAKA,eAAgB,CAAApC,IAAA,CAAK,IAAI;IACrD,KAAKU,aAAgB,QAAKA,aAAc,CAAAV,IAAA,CAAK,IAAI;EAAA;EAGzCwB,sBAAsBvc,KAAA,EAAqBxD,MAAmB;IAlH1E,IAAAwE,GAAA;IAmHU;MAACub,qBAAwB,GAAAD,SAAA,CAASC;IAAqB,KAC3Dvb,GAAA,OAAK,CAAA/C,OAAA,KAAL,OAAA+C,GAAA,GAAgB,EAAC;IAEnB,MAAMoc,WAAA,GACJ,OAAOb,qBAAA,KAA0B,aAC7BA,qBAAsB,CAAAvc,KAAA,EAAOxD,MAAM,CACnC,GAAA+f,qBAAA;IAEC,OAAAa,WAAA;EAAA;EAGFrC,IAAKA,CAAAve,MAAA,EAAmByB,OAAU,QAAKA,OAAS;IAC/C,MAAA+c,MAAA,GAASlY,MAAA,CAAO,MAAM;MA/HhC,IAAA9B,GAAA;MAgIY,MAAAqa,UAAA,GAAa,IAAIgC,eAAgB;MACjC;QAAChJ,MAAA,EAAAiJ;MAAA,CAAU,GAAAjC,UAAA;MACX,MAAAJ,QAAA,GAA2Bjb,KAAiB;QAC5C,IAAAud,cAAA,CAAevd,KAAK,CAAG;UACpB,KAAAwd,iBAAA,CAAkBxd,KAAO,EAAAxD,MAAA,EAAQyB,OAAO;QAAA;MAC/C,CACF;MACI,IAAAwf,OAAA,GAAU,EAACzc,GAAA,GAAAxE,MAAA,CAAO6E,MAAA,KAAP,OAAAL,GAAA,GAAiBxE,MAAA,CAAOO,OAAO;MAE9C,IAAIkB,OAAA,oBAAAA,OAAA,CAASyf,iBAAmB;QAC9B,IAAIpR,KAAM,CAAAqR,OAAA,CAAQ1f,OAAQ,CAAAyf,iBAAiB,CAAG;UAC5CD,OAAA,GAAUxf,OAAQ,CAAAyf,iBAAA;QAAA,CACb;UACKD,OAAA,GAAAxf,OAAA,CAAQyf,iBAAA,CAAkBlhB,MAAM;QAAA;MAC5C;MAGF,WAAWG,MAAA,IAAU8gB,OAAS;QAC5B,IAAI,CAAC9gB,MAAQ;QAEDihB,WAAA,CAAAjhB,MAAA,CAAO4R,aAAA,CAAcsP,WAAW;QAC5ClhB,MAAA,CAAOoD,gBAAA,CAAiB,aAAe,EAAAkb,QAAA,EAAU;UAAC5G,MAAA,EAAAiJ;QAAA,CAAO;MAAA;MAGpD,aAAMjC,UAAA,CAAWyC,KAAM;IAAA,CAC/B;IAEM,OAAA9C,MAAA;EAAA;EAGCwC,iBACRA,CAAAxd,KAAA,EACAxD,MACA,EAAAyB,OAAA,GAAgC,EAChC;IAEE,SAAK4D,QAAA,IACL,CAAC7B,KAAA,CAAM+d,SAAA,IACP/d,KAAM,CAAAge,MAAA,KAAW,CACjB,KAACpY,SAAU,CAAA5F,KAAA,CAAMrD,MAAM,CACvB,IAAAH,MAAA,CAAOqF,QACP,IAAAoc,kBAAA,CAAmBje,KAAK,KACxB,CAAC,IAAK,CAAAhC,OAAA,CAAQ6E,aAAc,CAAAG,MAAA,CAAOyE,IACnC;MACA;IAAA;IAGI;MAAC9K;IAAA,CAAU,GAAAqD,KAAA;IACX,MAAAke,iBAAA,GACJtP,aAAA,CAAcjS,MAAM,KACpBA,MAAA,CAAOP,SACP,IAAAO,MAAA,CAAOiF,YAAa,YAAW,CAAM;IAEjC,MAAAkY,MAAA,GAAS/R,iBAAkB,CAAAvL,MAAA,CAAOO,OAAO;IAE/C,KAAKohB,kBAAqB;MACxB7V,CAAG,EAAAtI,KAAA,CAAMoe,OAAU,GAAAtE,MAAA,CAAOvR,MAAA,GAASuR,MAAO,CAAAxR,CAAA;MAC1CE,CAAG,EAAAxI,KAAA,CAAMqe,OAAU,GAAAvE,MAAA,CAAOrR,MAAA,GAASqR,MAAO,CAAAtR;IAAA,CAC5C;IAEA,MAAM4U,WAAc,QAAKb,qBAAsB,CAAAvc,KAAA,EAAOxD,MAAM;IAE3DwD,KAAA,CAAcse,MAAS;IAExB,IAAI,EAAClB,WAAA,oBAAAA,WAAA,CAAaX,KAAS,OAACW,WAAA,oBAAAA,WAAA,CAAaR,QAAU;MAC5C,KAAA9B,WAAA,CAAYte,MAAA,EAAQwD,KAAK;IAAA,CACzB;MACC;QAACyc;MAAA,CAAS,GAAAW,WAAA;MAEhB,IAAIX,KAAO;QACT,MAAMpa,OAAU,GAAAG,UAAA,CACd,MAAM,KAAKsY,WAAY,CAAAte,MAAA,EAAQwD,KAAK,GACpCyc,KAAM,CAAArf,KAAA,CACR;QAEA2Y,YAAA,OAAK+G,aAAA,EAAgB,MAAM;UACzBva,YAAA,CAAaF,OAAO;UACpB0T,YAAA,OAAK+G,aAAgB;QAAA,CACvB;MAAA;IACF;IAGI,MAAAvO,aAAA,GAAgB3L,WAAY,CAAA5C,KAAA,CAAMrD,MAAM;IAE9C,MAAM4hB,eAAkB,QAAK7D,SAAU,CAAAK,IAAA,CAAKxM,aAAe,GACzD;MACExK,IAAM;MACNkX,QAAA,EAAWO,MAAA,IACT,IAAK,CAAAgD,iBAAA,CAAkBhD,MAAA,EAAOhf,MAAM;IAAA,CACxC,EACA;MACEuH,IAAM;MACNkX,QAAA,EAAU,IAAK,CAAAkC,eAAA;MACflf,OAAS;QACP+a,OAAS;MAAA;IACX,CACF,EACA;MAAA;MAEEjV,IAAM;MACNkX,QAAA,EAAUiD,iBAAoB,QAAKhB,YAAe,GAAA1F,cAAA;MAClDvZ,OAAS;QACP+a,OAAS;MAAA;IACX,EAEH;IAED,MAAM5U,OAAA,GAAUA,CAAA,KAAM;MA3O1B,IAAApD,GAAA;MA4OsBud,eAAA;MAChB,CAAAvd,GAAA,GAAAyU,YAAA,OAAKqH,aAAL,sBAAA9b,GAAA,CAAAyD,IAAA;MACA,KAAK0Z,kBAAqB;IAAA,CAC5B;IAEK1I,YAAA,OAAAoH,QAAA,EAASvb,GAAA,CAAI8C,OAAO;EAAA;EAkBjBoa,kBAAkBxe,KAAA,EAAqBxD,MAAmB;IAClE,MAAMmO,WAAc;MAClBrC,CAAA,EAAGtI,KAAM,CAAAoe,OAAA;MACT5V,CAAA,EAAGxI,KAAM,CAAAqe;IAAA,CACX;IAEM,MAAAvE,MAAA,GAAS/R,iBAAkB,CAAAvL,MAAA,CAAOO,OAAO;IAE/C4N,WAAA,CAAYrC,CAAI,GAAAqC,WAAA,CAAYrC,CAAI,GAAAwR,MAAA,CAAOvR,MAAA,GAASuR,MAAO,CAAAxR,CAAA;IACvDqC,WAAA,CAAYnC,CAAI,GAAAmC,WAAA,CAAYnC,CAAI,GAAAsR,MAAA,CAAOrR,MAAA,GAASqR,MAAO,CAAAtR,CAAA;IAEvD,IAAI,IAAK,CAAAxK,OAAA,CAAQ6E,aAAc,CAAAG,MAAA,CAAOwM,QAAU;MAC9CxP,KAAA,CAAMwX,cAAe;MACrBxX,KAAA,CAAMye,eAAgB;MAEtB,KAAKzB,MAAA,CAAOhd,KAAQ,GAAAA,KAAA;MACpB,KAAKgd,MAAA,CAAOrS,WAAc,GAAAA,WAAA;MAE1BtL,SAAA,CAAUC,QAAS,MAAKuc,UAAU;MAClC;IAAA;IAGE,KAAC,KAAKsC,kBAAoB;MAC5B;IAAA;IAGF,MAAMlT,KAAQ;MACZ3C,CAAG,EAAAqC,WAAA,CAAYrC,CAAI,QAAK6V,kBAAmB,CAAA7V,CAAA;MAC3CE,CAAG,EAAAmC,WAAA,CAAYnC,CAAI,QAAK2V,kBAAmB,CAAA3V;IAAA,CAC7C;IACA,MAAM4U,WAAc,QAAKb,qBAAsB,CAAAvc,KAAA,EAAOxD,MAAM;IAC5D,MAAM;MAACogB,QAAA;MAAUH;IAAK,IAAIW,WAAA,WAAAA,WAAA,GAAe,EAAC;IAE1C,IAAIR,QAAU;MACZ,IACEA,QAAA,CAASF,SAAa,YACtBgC,eAAA,CAAgBzT,KAAO,EAAA2R,QAAA,CAASF,SAAS,CACzC;QACO,YAAKQ,YAAA,CAAald,KAAK;MAAA;MAEhC,IAAI0e,eAAgB,CAAAzT,KAAA,EAAO2R,QAAS,CAAAxf,KAAK,CAAG;QACnC,YAAK0d,WAAY,CAAAte,MAAA,EAAQwD,KAAK;MAAA;IACvC;IAGF,IAAIyc,KAAO;MACT,IAAIiC,eAAgB,CAAAzT,KAAA,EAAOwR,KAAM,CAAAC,SAAS,CAAG;QACpC,YAAKQ,YAAA,CAAald,KAAK;MAAA;IAChC;EACF;EAGMmd,gBAAgBnd,KAAqB;IAE3C,MAAM;MAACgD;IAAA,CAAU,QAAKhF,OAAQ,CAAA6E,aAAA;IAE1B,KAACG,MAAA,CAAOyE,IAAM;MAEhBzH,KAAA,CAAMwX,cAAe;MACrBxX,KAAA,CAAMye,eAAgB;MAEhB,MAAA5hB,QAAA,GAAW,CAACmG,MAAO,CAAAC,WAAA;MACzB,KAAKjF,OAAA,CAAQiP,OAAQ,CAAAC,IAAA,CAAK;QAAClN,KAAA;QAAOnD;MAAA,CAAS;IAAA;IAG7C,KAAKuH,OAAQ;EAAA;EAGLqX,cAAczb,KAAsB;IACxC,IAAAA,KAAA,CAAMyB,GAAA,KAAQ,QAAU;MAC1BzB,KAAA,CAAMwX,cAAe;MACrB,KAAK0F,YAAA,CAAald,KAAK;IAAA;EACzB;EAGQ8a,YAAYte,MAAA,EAAmBwD,KAAqB;IA9UhE,IAAAgB,GAAA;IA+UU;MAAChD,OAAS;MAAAmgB;IAAA,CAAsB;IAEtC,CAAAnd,GAAA,GAAAyU,YAAA,OAAKqH,aAAL,sBAAA9b,GAAA,CAAAyD,IAAA;IAEA,IAAI,CAAC0Z,kBAAsB,KAACngB,OAAQ,CAAA6E,aAAA,CAAcG,MAAA,CAAOyE,IAAM;MAC7D;IAAA;IAGF,IAAIzH,KAAA,CAAMuX,gBAAkB;MAC1B;IAAA;IAGI,MAAA8D,UAAA,GAAard,OAAQ,CAAAiP,OAAA,CAAQ+M,KAAM;MACvCrP,WAAa,EAAAwT,kBAAA;MACbne,KAAA;MACAxD;IAAA,CACD;IAED,IAAI6e,UAAW,CAAAhH,MAAA,CAAOiH,OAAS,SAAO,KAAKlX,OAAQ;IAEnDpE,KAAA,CAAMwX,cAAe;IAEf,MAAAjJ,aAAA,GAAgB3L,WAAY,CAAA5C,KAAA,CAAMrD,MAAM;IAC9C,MAAMgiB,oBAAA,GAAuBpQ,aAAc,CAAA7N,IAAA;IAEtBie,oBAAA,CAAAC,iBAAA,CAAkB5e,KAAA,CAAM6e,SAAS;IAEtD,MAAM7D,MAAS,QAAKN,SAAU,CAAAK,IAAA,CAAKxM,aAAe,GAChD;MAAA;MAEExK,IAAM;MACNkX,QAAU,EAAAzD,cAAA;MACVvZ,OAAS;QACPgb,OAAS;MAAA;IACX,CACF,EACA;MAAA;MAEElV,IAAM;MACNkX,QAAU,EAAAzD;IAAA,CACZ,EACA;MACEzT,IAAM;MACNkX,QAAU,EAAAzD;IAAA,CACZ,EACA;MACEzT,IAAM;MACNkX,QAAA,EAAU,IAAK,CAAAQ;IAAA,CACjB,EACA;MACE1X,IAAM;MACNkX,QAAA,EAAWO,MAAwB;QAC7B,IAAAA,MAAA,CAAM7e,MAAA,KAAWgiB,oBAAsB;QAE3C,KAAKxB,eAAA,CAAgB3B,MAAK;MAAA;IAC5B,EAEH;IAEI/F,YAAA,OAAAoH,QAAA,EAASvb,GAAA,CAAI0Z,MAAM;EAAA;EAGhBkC,aAAald,KAAc;IAC7B;MAAC6C;IAAa,IAAI,IAAK,CAAA7E,OAAA;IAEzB,IAAA6E,aAAA,CAAcG,MAAA,CAAOC,WAAa;MACpC,KAAKjF,OAAA,CAAQiP,OAAQ,CAAAC,IAAA,CAAK;QAAClN,KAAO;QAAAnD,QAAA,EAAU;MAAA,CAAK;IAAA;IAGnD,KAAKuH,OAAQ;EAAA;EAGLA,OAAUA,CAAA;IAClB,KAAK4Y,MAAS;MACZhd,KAAO;MACP2K,WAAa;IAAA,CACf;IACA8K,YAAA,OAAKoH,QAAS,EAAA5a,OAAA,CAASmC,OAAA,IAAYA,OAAA,EAAS;IAC5CqR,YAAA,OAAKoH,QAAA,EAAS5b,KAAM;EAAA;EAGfc,OAAUA,CAAA;IACf,KAAKqC,OAAQ;IACb,KAAKsW,SAAA,CAAUzZ,KAAM;EAAA;AAMzB;AAzUE4b,QAAA,OAAAzV,OAAA;AAEA0V,aAAA,OAAA1V,OAAA;AANW2V,cA0UJ,CAAAlK,SAAA,GAAYC,YAAA,CAAaiK,cAAa;AA1UlCA,cAAA,CA4UJnD,QAAW,GAAA0C,SAAA;AA5Ub,IAAMwC,aAAN,GAAA/B,cAAA;AA+UP,SAASkB,mBAAmBje,KAAc;EACxC,OAAO,QAAY,IAAAA,KAAA;AACrB;AAEA,SAASwX,eAAexX,KAAc;EACpCA,KAAA,CAAMwX,cAAe;AACvB;AAEA,SAASuH,IAAOA,CAAA,GAAC;AAEjB,IAAMC,OAAA,sBAAc3X,OAAgB;AAEpC,SAASuW,YAAYqB,MAAuB;EAC1C,IAAI,CAACA,MAAA,IAAUD,OAAQ,CAAA9X,GAAA,CAAI+X,MAAM,CAAG;IAClC;EAAA;EAGKA,MAAA,CAAAlf,gBAAA,CAAiB,aAAagf,IAAM;IACzC/F,OAAS;IACTC,OAAS;EAAA,CACV;EACD+F,OAAA,CAAQ1d,GAAA,CAAI2d,MAAM;AACpB;;;ACzaO,IAAMC,aAIT;EACFtP,SAAA,EAAW,EAAC;EACZ4I,OAAA,EAAS,CAAC3a,aAAA,EAAewa,YAAc,EAAA5V,MAAA,EAAQsQ,QAAA,EAAUyG,gBAAgB;EACzE2F,OAAA,EAAS,CAACL,aAAA,EAAe1C,cAAc;AACzC;AAEa,IAAAgD,eAAA,GAAN,cAIGC,iBAA8B;EACtCthB,YAAYuhB,KAAe,KAAI;IACvB;MACJ9G,OAAA,GAAU0G,aAAc,CAAA1G,OAAA;MACxB2G,OAAA,GAAUD,aAAc,CAAAC,OAAA;MACxBvP,SAAA,GAAY;IAAC,CACX,GAAA0P,KAAA;IAEJ,MAAMzN,aAAA,CAAAC,cAAA,KACDwN,KADC;MAEJ9G,OAAS,GAACW,cAAgB,EAAA5D,QAAA,EAAU,GAAGiD,OAAO;MAC9C2G,OAAA;MACAvP;IAAA,CACD;EAAA;AAEL;ACpDA,IAAA2P,aAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,EAAA,EAAAC,MAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,SAAA;AAoBa,IAAAC,SAAA,GAAN,eAA+CL,EAqDpD,GAAAM,WAAA,EAAAP,WAAA,IAAChZ,QAAA,GAGD+Y,YAAC,IAAA/Y,QAAA,GAGD8Y,aAAC,IAAA9Y,QAAA,GA3DmDiZ,EAGpD;EACA3hB,YACEiD,GAAA,EAOAhD,OACA;IARA,IAAAsJ,GAAA,GAAAtG,GACE;MAAA;QAAAjE,OAAA;QACAsS,OAAA,EAAA4Q,QAAU,GAAA5Q,CAAA,KAAM,EAAC;QACjBhO,MAAA;QACAqG,QAAW;MAAA,CA7BjB,GAyBIJ,GAKK;MAAAgY,KAAA,GAAAY,SAAA,CALL5Y,GAKK,GAJH,WACA,WACA,UACA;IAKF,MACEwK,cAAA;MACEzC,OAAA,EAASA,CAAA,KAAM,CACb,GAAG4Q,QAAQ,IACX,MAAM;QAtChB,IAAAxR,GAAA,EAAA8B,GAAA;QAuCkB;UAACvS,OAAA,EAAAiC;QAAA,CAAW;QAElB,IAAI,CAACA,QAAS;QAER,MAAAkf,OAAA,IAAU5O,GAAA,IAAA9B,GAAA,OAAK,CAAA0Q,OAAA,KAAL,gBAAA1Q,GAAc,CAAA9O,GAAA,CAAIwgB,UAAlB,aAAA5P,GAAiC,IAC/C,GAAGtQ,QAAQ,CAAAkf,OAAA,CACb;QACA,MAAMiB,eAAkB,GAAAjB,OAAA,CAAQxf,GAAI,CAACuO,KAAU;UACvC,MAAAmS,cAAA,GACJnS,KAAA,YAAiBuM,MACb,GAAAvM,KAAA,GACAjO,QAAA,CAAQiB,QAAS,CAAAof,QAAA,CAASpS,KAAA,CAAMqS,MAAM;UAC5C,MAAMtiB,OACJ,GAAAiQ,KAAA,YAAiBuM,MAAS,YAAYvM,KAAM,CAAAjQ,OAAA;UAE9C,MAAM+c,MAAS,GAAAqF,cAAA,CAAetF,IAAK,OAAM9c,OAAO;UACzC,OAAA+c,MAAA;QAAA,CACR;QAED,OAAO,SAAS5W,OAAUA,CAAA;UACxBgc,eAAA,CAAgBne,OAAQ,CAAC+Y,MAAW,IAAAA,MAAA,EAAQ;QAAA,CAC9C;MAAA;IAEJ,CACG,EAAAsE,KAAA,GAELthB,OAAA,CACF;IAQF0I,YAAA,OAAgBkZ,OAAhB,EAAAjZ,iBAAA,CAAAgZ,MAAA,aAAAhZ,iBAAA,CAAAgZ,MAAA;IAGAjZ,YAAA,OAAgBmZ,QAAhB,EAAAlZ,iBAAA,CAAAgZ,MAAA,cAAAhZ,iBAAA,CAAAgZ,MAAA;IAGAjZ,YAAA,OAAgBoZ,SAAhB,EAAAnZ,iBAAA,CAAAgZ,MAAA,cAAAhZ,iBAAA,CAAAgZ,MAAA;IAZE,KAAK5iB,OAAU,GAAAA,OAAA;IACf,KAAKsE,MAAS,GAAAA,MAAA;IACd,KAAKqG,QAAW,GAAAA,QAAA;EAAA;AAWpB;AA7DOiY,MAAA,GAAAxY,gBAAA,CAAAuY,EAAA;AAsDWE,OAAA,OAAAxY,OAAA;AAGAyY,QAAA,OAAAzY,OAAA;AAGA0Y,SAAA,OAAA1Y,OAAA;AANhBuL,iBAAA,CAAAgN,MAAA,KAAgB,QADhB,EAAAF,WAAA,EArDWM,SAsDK,EAAAH,OAAA;AAGhBjN,iBAAA,CAAAgN,MAAA,KAAgB,SADhB,EAAAH,YAAA,EAxDWO,SAyDK,EAAAF,QAAA;AAGhBlN,iBAAA,CAAAgN,MAAA,KAAgB,UADhB,EAAAJ,aAAA,EA3DWQ,SA4DK,EAAAD,SAAA;AA5DXlN,mBAAA,CAAA+M,MAAM,EAAAI,SAAA;ACpBb,IAAAS,UAAA,EAAAC,aAAA,EAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA,EAAAC,EAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,oBAAA,EAAAC,MAAA;AAqBa,IAAAC,SAAA,GAAN,eAA+CR,GAAA,GAAAS,WAAA,EAqFpDV,aAAA,IAACha,QAAA,GAGD+Z,UAAC,IAAA/Z,QAAA,GAxFmDia,GAGpD;EACA3iB,YACEiD,GAAA,EACAhD,OACA;IAFA,IAAAsJ,GAAA,GAAAtG,GAAA;MAAC;QAASjE,OAAA;QAAAsS,OAAA,EAAA4Q,QAAU,GAAA5Q,CAAA,KAAM;MAAC,CAA3B,GAAA/H,GAAA;MAAiCgY,KAAjC,GAAAY,SAAA,CAAA5Y,GAAA,EAAiC,CAAhC,SAAS;IAGJ;MAAC8Z,iBAAoB,GAAAC;IAAA,CAA6B,GAAA/B,KAAA;IAClD,MAAAgC,WAAA,GAAeC,kBAAkD;MACrE,MAAM;QAACvjB,OAAA,EAAAiC,QAAS;QAAAlD,OAAA,EAAA4R;MAAA,CAAW;MAEvB,KAACA,QAAW,IAAA4S,kBAAA,KAAuB,IAAM;QAC3C,KAAKrZ,KAAQ;QACN;MAAA;MAGT,IAAI,CAACjI,QAAS;MAER,MAAAuhB,YAAA,GAAe,IAAIrZ,YAAA,CAAawG,QAAO;MAE7C,MAAMzG,KAAQ,GAAAlE,SAAA,CAAU,MAAM,KAAKkE,KAAK;MACpC,IAAAsZ,YAAA,KAAgBtZ,KAAO,oBAAAA,KAAA,CAAAuZ,MAAA,CAAOD,YAAe;QACxC,OAAAtZ,KAAA;MAAA;MAGT,KAAKA,KAAQ,GAAAsZ,YAAA;MAEN,OAAAA,YAAA;IAAA,CACT;IAEM,MAAAE,eAAA,GAAkBrN,MAAA,CAAO,KAAK;IAEpC,MACExC,aAAA,CAAAC,cAAA,KACKwN,KADL;MAEE8B,iBAAA;MACA/R,OAAA,EAASA,CAAA,KAAM,CACb,GAAG4Q,QAAQ,IACX,MAAM;QACJ,MAAM;UAACljB,OAAA,EAAA4R,QAAS;UAAA3Q,OAAA,EAAAiC;QAAA,CAAW;QAC3B,IAAI,CAACA,QAAS;QAER;UAAC4C;QAAA,CAAiB,GAAA5C,QAAA;QAClB;UAACzD;QAAA,CAAU,GAAAqG,aAAA;QAEjB6e,eAAA,CAAgBtkB,KAAQ,GAAAukB,OAAA,CACtBnlB,MAAA,IACEqG,aAAc,CAAAG,MAAA,CAAOC,WACrB,IAAA0L,QAAA,IACA,CAAC,IAAK,CAAA9M,QAAA,IACN,IAAK,CAAA+f,OAAA,CAAQplB,MAAM,EACvB;MAAA,CACF,EACA,MAAM;QACE;UAACO,OAAA,EAAA4R;QAAA,CAAW;QAEd,IAAA+S,eAAA,CAAgBtkB,KAAA,IAASuR,QAAS;UACpC,MAAMkT,gBAAA,GAAmB,IAAIC,gBAAA,CAC3BnT,QAAA,EACA2S,WAAA,CACF;UAEA,OAAO,MAAM;YACXO,gBAAA,CAAiB/S,UAAW;YAC5B,KAAK5G,KAAQ;UAAA,CACf;QAAA;MACF,CACF,EACA,MAAM;QA1FhB,IAAAuG,GAAA;QA2FY,KAAIA,GAAA,OAAK,CAAAzQ,OAAA,KAAL,gBAAAyQ,GAAc,CAAA5L,aAAA,CAAcG,MAAA,CAAOC,WAAa;UAClD,OAAO,MAAM;YACX,KAAKiF,KAAQ;UAAA,CACf;QAAA;MACF;IAEJ,CACF,GACAlK,OAAA,CACF;IA/EG0I,YAAA,OAAAsa,oBAAA;IAsFLta,YAAA,OAASka,SAAT,EAAAja,iBAAA,CAAAga,MAAA,aAAAha,iBAAA,CAAAga,MAAA;IAGAja,YAAA,OAAgBua,MAAhB,EAAAta,iBAAA,CAAAga,MAAA,cAAAha,iBAAA,CAAAga,MAAA;IARE,KAAK5jB,OAAU,GAAAA,OAAA;IACV,KAAAglB,YAAA,GAAe,MAAMT,WAAY;EAAA;EASxC,IAAIvkB,QAAQA,OAA8B;IACxCgZ,YAAA,OAAKiL,oBAAA,EAAWjkB,OAAX,EAAAgkB,WAAA;EAAA;EAGP,IAAIhkB,OAAUA,CAAA;IApHhB,IAAAiE,GAAA;IAqHI,QAAOA,GAAA,QAAKqE,KAAL,YAAArE,GAAA,GAAcyU,YAAK,OAAAuL,oBAAA,EAAAF,WAAA;EAAA;AAI9B;AApGOH,MAAA,GAAAxZ,gBAAA,CAAAuZ,GAAA;AAsFIE,SAAA,OAAAxZ,OAAA;AAtFJ4Z,oBAAA,OAAA3Z,OAAA;AAyFW4Z,MAAA,OAAA7Z,OAAA;AAHhByZ,EAAA,GAAAlO,iBAAA,CAAAgO,MAAA,IADA,cAAAF,aAAA,EACSO,oBAAA,EAAAJ,SAAA,GAAAE,WAAA,GAATD,EAAA,CAAAnS,GAAA,EAASqS,WAAT,GAAAF,EAAA,CAAA/b,GAAA;AAGA6N,iBAAA,CAAAgO,MAAA,KAAgB,OADhB,EAAAH,UAAA,EAxFWU,SAyFK,EAAAD,MAAA;AAzFXrO,mBAAA,CAAA+N,MAAM,EAAAO,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}