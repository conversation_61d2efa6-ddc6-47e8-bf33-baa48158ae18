{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"4\",\n  r: \"2\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M15.89 8.11C15.5 7.72 14.83 7 13.53 7h-2.54c-2.46-.01-4.51-1.8-4.92-4.15-.08-.49-.49-.85-.98-.85-.61 0-1.09.54-1 1.14C4.53 5.8 6.47 7.95 9 8.71V21c0 .55.45 1 1 1s1-.45 1-1v-5h2v5c0 .55.45 1 1 1s1-.45 1-1V10.05l3.24 3.24c.39.39 1.02.39 1.41 0s.39-1.02 0-1.41z\"\n}, \"1\")], 'EmojiPeopleRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "cx", "cy", "r", "d"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/icons-material/esm/EmojiPeopleRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"4\",\n  r: \"2\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M15.89 8.11C15.5 7.72 14.83 7 13.53 7h-2.54c-2.46-.01-4.51-1.8-4.92-4.15-.08-.49-.49-.85-.98-.85-.61 0-1.09.54-1 1.14C4.53 5.8 6.47 7.95 9 8.71V21c0 .55.45 1 1 1s1-.45 1-1v-5h2v5c0 .55.45 1 1 1s1-.45 1-1V10.05l3.24 3.24c.39.39 1.02.39 1.41 0s.39-1.02 0-1.41z\"\n}, \"1\")], 'EmojiPeopleRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,QAAQ,EAAE;EACxDC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,GAAG;EACPC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaH,IAAI,CAAC,MAAM,EAAE;EACjCI,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}