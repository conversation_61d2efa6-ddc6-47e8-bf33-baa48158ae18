{"ast": null, "code": "export function toModifierMap(modifiers) {\n  const result = {};\n  if (!Array.isArray(modifiers)) {\n    return modifiers || result;\n  }\n\n  // eslint-disable-next-line no-unused-expressions\n  modifiers == null ? void 0 : modifiers.forEach(m => {\n    result[m.name] = m;\n  });\n  return result;\n}\nexport function toModifierArray() {\n  let map = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  if (Array.isArray(map)) return map;\n  return Object.keys(map).map(k => {\n    map[k].name = k;\n    return map[k];\n  });\n}\nexport default function mergeOptionsWithPopperConfig(_ref) {\n  let {\n    enabled,\n    enableEvents,\n    placement,\n    flip,\n    offset,\n    fixed,\n    containerPadding,\n    arrowElement,\n    popperConfig = {}\n  } = _ref;\n  var _modifiers$eventListe, _modifiers$preventOve, _modifiers$preventOve2, _modifiers$offset, _modifiers$arrow;\n  const modifiers = toModifierMap(popperConfig.modifiers);\n  return Object.assign({}, popperConfig, {\n    placement,\n    enabled,\n    strategy: fixed ? 'fixed' : popperConfig.strategy,\n    modifiers: toModifierArray(Object.assign({}, modifiers, {\n      eventListeners: {\n        enabled: enableEvents,\n        options: (_modifiers$eventListe = modifiers.eventListeners) == null ? void 0 : _modifiers$eventListe.options\n      },\n      preventOverflow: Object.assign({}, modifiers.preventOverflow, {\n        options: containerPadding ? Object.assign({\n          padding: containerPadding\n        }, (_modifiers$preventOve = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve.options) : (_modifiers$preventOve2 = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve2.options\n      }),\n      offset: {\n        options: Object.assign({\n          offset\n        }, (_modifiers$offset = modifiers.offset) == null ? void 0 : _modifiers$offset.options)\n      },\n      arrow: Object.assign({}, modifiers.arrow, {\n        enabled: !!arrowElement,\n        options: Object.assign({}, (_modifiers$arrow = modifiers.arrow) == null ? void 0 : _modifiers$arrow.options, {\n          element: arrowElement\n        })\n      }),\n      flip: Object.assign({\n        enabled: !!flip\n      }, modifiers.flip)\n    }))\n  });\n}", "map": {"version": 3, "names": ["toModifierMap", "modifiers", "result", "Array", "isArray", "for<PERSON>ach", "m", "name", "toModifierArray", "map", "arguments", "length", "undefined", "Object", "keys", "k", "mergeOptionsWithPopperConfig", "_ref", "enabled", "enableEvents", "placement", "flip", "offset", "fixed", "containerPadding", "arrowElement", "popperConfig", "_modifiers$eventListe", "_modifiers$preventOve", "_modifiers$preventOve2", "_modifiers$offset", "_modifiers$arrow", "assign", "strategy", "eventListeners", "options", "preventOverflow", "padding", "arrow", "element"], "sources": ["C:/laragon/www/frontend/node_modules/@restart/ui/esm/mergeOptionsWithPopperConfig.js"], "sourcesContent": ["export function toModifierMap(modifiers) {\n  const result = {};\n  if (!Array.isArray(modifiers)) {\n    return modifiers || result;\n  }\n\n  // eslint-disable-next-line no-unused-expressions\n  modifiers == null ? void 0 : modifiers.forEach(m => {\n    result[m.name] = m;\n  });\n  return result;\n}\nexport function toModifierArray(map = {}) {\n  if (Array.isArray(map)) return map;\n  return Object.keys(map).map(k => {\n    map[k].name = k;\n    return map[k];\n  });\n}\nexport default function mergeOptionsWithPopperConfig({\n  enabled,\n  enableEvents,\n  placement,\n  flip,\n  offset,\n  fixed,\n  containerPadding,\n  arrowElement,\n  popperConfig = {}\n}) {\n  var _modifiers$eventListe, _modifiers$preventOve, _modifiers$preventOve2, _modifiers$offset, _modifiers$arrow;\n  const modifiers = toModifierMap(popperConfig.modifiers);\n  return Object.assign({}, popperConfig, {\n    placement,\n    enabled,\n    strategy: fixed ? 'fixed' : popperConfig.strategy,\n    modifiers: toModifierArray(Object.assign({}, modifiers, {\n      eventListeners: {\n        enabled: enableEvents,\n        options: (_modifiers$eventListe = modifiers.eventListeners) == null ? void 0 : _modifiers$eventListe.options\n      },\n      preventOverflow: Object.assign({}, modifiers.preventOverflow, {\n        options: containerPadding ? Object.assign({\n          padding: containerPadding\n        }, (_modifiers$preventOve = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve.options) : (_modifiers$preventOve2 = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve2.options\n      }),\n      offset: {\n        options: Object.assign({\n          offset\n        }, (_modifiers$offset = modifiers.offset) == null ? void 0 : _modifiers$offset.options)\n      },\n      arrow: Object.assign({}, modifiers.arrow, {\n        enabled: !!arrowElement,\n        options: Object.assign({}, (_modifiers$arrow = modifiers.arrow) == null ? void 0 : _modifiers$arrow.options, {\n          element: arrowElement\n        })\n      }),\n      flip: Object.assign({\n        enabled: !!flip\n      }, modifiers.flip)\n    }))\n  });\n}"], "mappings": "AAAA,OAAO,SAASA,aAAaA,CAACC,SAAS,EAAE;EACvC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,EAAE;IAC7B,OAAOA,SAAS,IAAIC,MAAM;EAC5B;;EAEA;EACAD,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,OAAO,CAACC,CAAC,IAAI;IAClDJ,MAAM,CAACI,CAAC,CAACC,IAAI,CAAC,GAAGD,CAAC;EACpB,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf;AACA,OAAO,SAASM,eAAeA,CAAA,EAAW;EAAA,IAAVC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACtC,IAAIP,KAAK,CAACC,OAAO,CAACK,GAAG,CAAC,EAAE,OAAOA,GAAG;EAClC,OAAOI,MAAM,CAACC,IAAI,CAACL,GAAG,CAAC,CAACA,GAAG,CAACM,CAAC,IAAI;IAC/BN,GAAG,CAACM,CAAC,CAAC,CAACR,IAAI,GAAGQ,CAAC;IACf,OAAON,GAAG,CAACM,CAAC,CAAC;EACf,CAAC,CAAC;AACJ;AACA,eAAe,SAASC,4BAA4BA,CAAAC,IAAA,EAUjD;EAAA,IAVkD;IACnDC,OAAO;IACPC,YAAY;IACZC,SAAS;IACTC,IAAI;IACJC,MAAM;IACNC,KAAK;IACLC,gBAAgB;IAChBC,YAAY;IACZC,YAAY,GAAG,CAAC;EAClB,CAAC,GAAAT,IAAA;EACC,IAAIU,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,iBAAiB,EAAEC,gBAAgB;EAC7G,MAAM9B,SAAS,GAAGD,aAAa,CAAC0B,YAAY,CAACzB,SAAS,CAAC;EACvD,OAAOY,MAAM,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAEN,YAAY,EAAE;IACrCN,SAAS;IACTF,OAAO;IACPe,QAAQ,EAAEV,KAAK,GAAG,OAAO,GAAGG,YAAY,CAACO,QAAQ;IACjDhC,SAAS,EAAEO,eAAe,CAACK,MAAM,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAE/B,SAAS,EAAE;MACtDiC,cAAc,EAAE;QACdhB,OAAO,EAAEC,YAAY;QACrBgB,OAAO,EAAE,CAACR,qBAAqB,GAAG1B,SAAS,CAACiC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,qBAAqB,CAACQ;MACvG,CAAC;MACDC,eAAe,EAAEvB,MAAM,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAE/B,SAAS,CAACmC,eAAe,EAAE;QAC5DD,OAAO,EAAEX,gBAAgB,GAAGX,MAAM,CAACmB,MAAM,CAAC;UACxCK,OAAO,EAAEb;QACX,CAAC,EAAE,CAACI,qBAAqB,GAAG3B,SAAS,CAACmC,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,qBAAqB,CAACO,OAAO,CAAC,GAAG,CAACN,sBAAsB,GAAG5B,SAAS,CAACmC,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,sBAAsB,CAACM;MAC5M,CAAC,CAAC;MACFb,MAAM,EAAE;QACNa,OAAO,EAAEtB,MAAM,CAACmB,MAAM,CAAC;UACrBV;QACF,CAAC,EAAE,CAACQ,iBAAiB,GAAG7B,SAAS,CAACqB,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGQ,iBAAiB,CAACK,OAAO;MACxF,CAAC;MACDG,KAAK,EAAEzB,MAAM,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAE/B,SAAS,CAACqC,KAAK,EAAE;QACxCpB,OAAO,EAAE,CAAC,CAACO,YAAY;QACvBU,OAAO,EAAEtB,MAAM,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAE,CAACD,gBAAgB,GAAG9B,SAAS,CAACqC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,gBAAgB,CAACI,OAAO,EAAE;UAC3GI,OAAO,EAAEd;QACX,CAAC;MACH,CAAC,CAAC;MACFJ,IAAI,EAAER,MAAM,CAACmB,MAAM,CAAC;QAClBd,OAAO,EAAE,CAAC,CAACG;MACb,CAAC,EAAEpB,SAAS,CAACoB,IAAI;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}