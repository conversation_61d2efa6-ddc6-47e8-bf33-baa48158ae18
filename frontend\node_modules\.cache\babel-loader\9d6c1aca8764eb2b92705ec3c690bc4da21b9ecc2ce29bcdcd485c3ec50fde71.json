{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\FileReUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Box, Typography, Alert, LinearProgress, Paper, Chip, IconButton, Tooltip } from '@mui/material';\nimport { CloudUpload as UploadIcon, Close as CloseIcon, InsertDriveFile as FileIcon } from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService from '../../services/printingService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FileReUpload = ({\n  open,\n  onClose,\n  orderId,\n  file,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [reason, setReason] = useState('');\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [error, setError] = useState(null);\n  const [dragActive, setDragActive] = useState(false);\n  const onDrop = useCallback(acceptedFiles => {\n    if (acceptedFiles.length > 0) {\n      setSelectedFile(acceptedFiles[0]);\n      setError(null);\n    }\n  }, []);\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.png', '.jpg', '.jpeg', '.tiff', '.svg'],\n      'application/pdf': ['.pdf'],\n      'application/postscript': ['.eps'],\n      'application/illustrator': ['.ai']\n    },\n    maxFiles: 1,\n    onDragEnter: () => setDragActive(true),\n    onDragLeave: () => setDragActive(false)\n  });\n  const handleReUpload = async () => {\n    if (!selectedFile) {\n      setError('Please select a file to upload');\n      return;\n    }\n    if (!reason.trim()) {\n      setError('Please provide a reason for re-uploading this file');\n      return;\n    }\n    setUploading(true);\n    setUploadProgress(0);\n    setError(null);\n    try {\n      // Simulate progress for better UX\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => Math.min(prev + 10, 90));\n      }, 200);\n      const response = await printingService.reUploadFile(orderId, file.id, selectedFile, reason.trim());\n      clearInterval(progressInterval);\n      setUploadProgress(100);\n      if (response.success && response.data) {\n        setTimeout(() => {\n          onSuccess(response.data);\n          handleClose();\n        }, 500);\n      } else {\n        throw new Error(response.message || 'Failed to re-upload file');\n      }\n    } catch (err) {\n      var _err$response;\n      console.error('Re-upload error:', err);\n      let errorMessage = 'Failed to re-upload file. Please try again.';\n      if ((_err$response = err.response) !== null && _err$response !== void 0 && _err$response.data) {\n        if (typeof err.response.data === 'string') {\n          errorMessage = err.response.data;\n        } else if (err.response.data.message) {\n          errorMessage = err.response.data.message;\n        } else if (err.response.data.error) {\n          errorMessage = err.response.data.error;\n        } else if (err.response.data.errors) {\n          // Handle validation errors\n          const errors = Object.values(err.response.data.errors).flat();\n          errorMessage = errors.join('. ');\n        } else {\n          // Handle HTTP status codes with user-friendly messages\n          switch (err.response.status) {\n            case 413:\n              errorMessage = 'File is too large. Please compress your file or use a smaller image and try again.';\n              break;\n            case 422:\n              errorMessage = 'File validation failed. Please check your file format, size, and quality requirements.';\n              break;\n            case 403:\n              errorMessage = 'Access denied. You may not have permission to modify this file.';\n              break;\n            case 404:\n              errorMessage = 'File or order not found. Please refresh the page and try again.';\n              break;\n            case 500:\n              errorMessage = 'Server error occurred. Please try again in a few moments.';\n              break;\n            default:\n              errorMessage = 'An unexpected error occurred. Please try again or contact support if the problem persists.';\n          }\n        }\n      } else if (err.message) {\n        // Handle network errors\n        if (err.message.includes('Network Error')) {\n          errorMessage = 'Network connection error. Please check your internet connection and try again.';\n        } else if (err.message.includes('timeout')) {\n          errorMessage = 'Upload timed out. Your file might be too large or your connection is slow. Please try again.';\n        } else {\n          errorMessage = err.message;\n        }\n      }\n      setError(errorMessage);\n      setUploadProgress(0);\n    } finally {\n      setUploading(false);\n    }\n  };\n  const handleClose = () => {\n    if (!uploading) {\n      setSelectedFile(null);\n      setReason('');\n      setError(null);\n      setUploadProgress(0);\n      onClose();\n    }\n  };\n  const removeSelectedFile = () => {\n    setSelectedFile(null);\n    setError(null);\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Re-upload File\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleClose,\n          disabled: uploading,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          color: \"text.secondary\",\n          gutterBottom: true,\n          children: \"Current File\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            bgcolor: 'grey.50'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 2,\n            children: [/*#__PURE__*/_jsxDEV(FileIcon, {\n              color: \"primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              flex: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"medium\",\n                children: file.original_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [file.formatted_file_size, \" \\u2022 \", file.file_type_label, \" \\u2022 Version \", file.version]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: `Version ${file.version}`,\n              size: \"small\",\n              color: \"primary\",\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          children: \"Select New File\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), !selectedFile ? /*#__PURE__*/_jsxDEV(Paper, {\n          ...getRootProps(),\n          sx: {\n            p: 4,\n            border: '2px dashed',\n            borderColor: isDragActive ? 'primary.main' : 'grey.300',\n            bgcolor: isDragActive ? 'primary.50' : 'grey.50',\n            cursor: 'pointer',\n            textAlign: 'center',\n            transition: 'all 0.2s ease',\n            '&:hover': {\n              borderColor: 'primary.main',\n              bgcolor: 'primary.50'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            ...getInputProps()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(UploadIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            gutterBottom: true,\n            children: isDragActive ? 'Drop the file here...' : 'Drag & drop a file here, or click to select'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Supported formats: PDF, PNG, JPG, JPEG, TIFF, SVG, EPS, AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            border: '1px solid',\n            borderColor: 'success.main'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 2,\n            children: [/*#__PURE__*/_jsxDEV(FileIcon, {\n              color: \"success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              flex: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"medium\",\n                children: selectedFile.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: formatFileSize(selectedFile.size)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Remove file\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: removeSelectedFile,\n                disabled: uploading,\n                children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mb: 3,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Reason for re-upload\",\n          placeholder: \"e.g., Updated design per customer feedback, Fixed quality issues, etc.\",\n          value: reason,\n          onChange: e => setReason(e.target.value),\n          multiline: true,\n          rows: 3,\n          required: true,\n          disabled: uploading,\n          helperText: \"Please provide a clear reason for replacing this file\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), uploading && /*#__PURE__*/_jsxDEV(Box, {\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          gutterBottom: true,\n          children: [\"Uploading... \", uploadProgress, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n          variant: \"determinate\",\n          value: uploadProgress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        disabled: uploading,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleReUpload,\n        disabled: !selectedFile || !reason.trim() || uploading,\n        startIcon: uploading ? undefined : /*#__PURE__*/_jsxDEV(UploadIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 46\n        }, this),\n        children: uploading ? 'Uploading...' : 'Re-upload File'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(FileReUpload, \"Q5hlpC2o0ea22zjZ+PhNGKcQPcw=\", false, function () {\n  return [useDropzone];\n});\n_c = FileReUpload;\nexport default FileReUpload;\nvar _c;\n$RefreshReg$(_c, \"FileReUpload\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Box", "Typography", "<PERSON><PERSON>", "LinearProgress", "Paper", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "CloudUpload", "UploadIcon", "Close", "CloseIcon", "InsertDriveFile", "FileIcon", "useDropzone", "printingService", "jsxDEV", "_jsxDEV", "FileReUpload", "open", "onClose", "orderId", "file", "onSuccess", "onError", "_s", "selectedFile", "setSelectedFile", "reason", "setReason", "uploading", "setUploading", "uploadProgress", "setUploadProgress", "error", "setError", "dragActive", "setDragActive", "onDrop", "acceptedFiles", "length", "getRootProps", "getInputProps", "isDragActive", "accept", "maxFiles", "onDragEnter", "onDragLeave", "handleReUpload", "trim", "progressInterval", "setInterval", "prev", "Math", "min", "response", "reUploadFile", "id", "clearInterval", "success", "data", "setTimeout", "handleClose", "Error", "message", "err", "_err$response", "console", "errorMessage", "errors", "Object", "values", "flat", "join", "status", "includes", "removeSelectedFile", "formatFileSize", "bytes", "k", "sizes", "i", "floor", "log", "parseFloat", "pow", "toFixed", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "display", "justifyContent", "alignItems", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "mb", "color", "gutterBottom", "sx", "p", "bgcolor", "gap", "flex", "fontWeight", "original_name", "formatted_file_size", "file_type_label", "version", "label", "size", "border", "borderColor", "cursor", "textAlign", "transition", "fontSize", "name", "title", "placeholder", "value", "onChange", "e", "target", "multiline", "rows", "required", "helperText", "severity", "startIcon", "undefined", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/FileReUpload.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport {\n  <PERSON><PERSON>,\n  DialogTitle,\n  DialogContent,\n  <PERSON>alogActions,\n  Button,\n  TextField,\n  Box,\n  Typography,\n  Alert,\n  LinearProgress,\n  Paper,\n  Chip,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  CloudUpload as UploadIcon,\n  Close as CloseIcon,\n  InsertDriveFile as FileIcon,\n} from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService, { OrderFile, FileReUploadResponse } from '../../services/printingService';\n\ninterface FileReUploadProps {\n  open: boolean;\n  onClose: () => void;\n  orderId: number;\n  file: OrderFile;\n  onSuccess: (updatedFile: OrderFile) => void;\n  onError: (error: string) => void;\n}\n\nconst FileReUpload: React.FC<FileReUploadProps> = ({\n  open,\n  onClose,\n  orderId,\n  file,\n  onSuccess,\n  onError,\n}) => {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [reason, setReason] = useState('');\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [error, setError] = useState<string | null>(null);\n  const [dragActive, setDragActive] = useState(false);\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    if (acceptedFiles.length > 0) {\n      setSelectedFile(acceptedFiles[0]);\n      setError(null);\n    }\n  }, []);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.png', '.jpg', '.jpeg', '.tiff', '.svg'],\n      'application/pdf': ['.pdf'],\n      'application/postscript': ['.eps'],\n      'application/illustrator': ['.ai'],\n    },\n    maxFiles: 1,\n    onDragEnter: () => setDragActive(true),\n    onDragLeave: () => setDragActive(false),\n  });\n\n  const handleReUpload = async () => {\n    if (!selectedFile) {\n      setError('Please select a file to upload');\n      return;\n    }\n\n    if (!reason.trim()) {\n      setError('Please provide a reason for re-uploading this file');\n      return;\n    }\n\n    setUploading(true);\n    setUploadProgress(0);\n    setError(null);\n\n    try {\n      // Simulate progress for better UX\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => Math.min(prev + 10, 90));\n      }, 200);\n\n      const response: FileReUploadResponse = await printingService.reUploadFile(\n        orderId,\n        file.id,\n        selectedFile,\n        reason.trim()\n      );\n\n      clearInterval(progressInterval);\n      setUploadProgress(100);\n\n      if (response.success && response.data) {\n        setTimeout(() => {\n          onSuccess(response.data!);\n          handleClose();\n        }, 500);\n      } else {\n        throw new Error(response.message || 'Failed to re-upload file');\n      }\n    } catch (err: any) {\n      console.error('Re-upload error:', err);\n\n      let errorMessage = 'Failed to re-upload file. Please try again.';\n\n      if (err.response?.data) {\n        if (typeof err.response.data === 'string') {\n          errorMessage = err.response.data;\n        } else if (err.response.data.message) {\n          errorMessage = err.response.data.message;\n        } else if (err.response.data.error) {\n          errorMessage = err.response.data.error;\n        } else if (err.response.data.errors) {\n          // Handle validation errors\n          const errors = Object.values(err.response.data.errors).flat();\n          errorMessage = errors.join('. ');\n        } else {\n          // Handle HTTP status codes with user-friendly messages\n          switch (err.response.status) {\n            case 413:\n              errorMessage = 'File is too large. Please compress your file or use a smaller image and try again.';\n              break;\n            case 422:\n              errorMessage = 'File validation failed. Please check your file format, size, and quality requirements.';\n              break;\n            case 403:\n              errorMessage = 'Access denied. You may not have permission to modify this file.';\n              break;\n            case 404:\n              errorMessage = 'File or order not found. Please refresh the page and try again.';\n              break;\n            case 500:\n              errorMessage = 'Server error occurred. Please try again in a few moments.';\n              break;\n            default:\n              errorMessage = 'An unexpected error occurred. Please try again or contact support if the problem persists.';\n          }\n        }\n      } else if (err.message) {\n        // Handle network errors\n        if (err.message.includes('Network Error')) {\n          errorMessage = 'Network connection error. Please check your internet connection and try again.';\n        } else if (err.message.includes('timeout')) {\n          errorMessage = 'Upload timed out. Your file might be too large or your connection is slow. Please try again.';\n        } else {\n          errorMessage = err.message;\n        }\n      }\n\n      setError(errorMessage);\n      setUploadProgress(0);\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!uploading) {\n      setSelectedFile(null);\n      setReason('');\n      setError(null);\n      setUploadProgress(0);\n      onClose();\n    }\n  };\n\n  const removeSelectedFile = () => {\n    setSelectedFile(null);\n    setError(null);\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <Dialog open={open} onClose={handleClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n          <Typography variant=\"h6\">Re-upload File</Typography>\n          <IconButton onClick={handleClose} disabled={uploading}>\n            <CloseIcon />\n          </IconButton>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent>\n        <Box mb={3}>\n          <Typography variant=\"subtitle2\" color=\"text.secondary\" gutterBottom>\n            Current File\n          </Typography>\n          <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>\n            <Box display=\"flex\" alignItems=\"center\" gap={2}>\n              <FileIcon color=\"primary\" />\n              <Box flex={1}>\n                <Typography variant=\"body2\" fontWeight=\"medium\">\n                  {file.original_name}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {file.formatted_file_size} • {file.file_type_label} • Version {file.version}\n                </Typography>\n              </Box>\n              <Chip\n                label={`Version ${file.version}`}\n                size=\"small\"\n                color=\"primary\"\n                variant=\"outlined\"\n              />\n            </Box>\n          </Paper>\n        </Box>\n\n        <Box mb={3}>\n          <Typography variant=\"subtitle2\" gutterBottom>\n            Select New File\n          </Typography>\n          \n          {!selectedFile ? (\n            <Paper\n              {...getRootProps()}\n              sx={{\n                p: 4,\n                border: '2px dashed',\n                borderColor: isDragActive ? 'primary.main' : 'grey.300',\n                bgcolor: isDragActive ? 'primary.50' : 'grey.50',\n                cursor: 'pointer',\n                textAlign: 'center',\n                transition: 'all 0.2s ease',\n                '&:hover': {\n                  borderColor: 'primary.main',\n                  bgcolor: 'primary.50',\n                },\n              }}\n            >\n              <input {...getInputProps()} />\n              <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n              <Typography variant=\"body1\" gutterBottom>\n                {isDragActive\n                  ? 'Drop the file here...'\n                  : 'Drag & drop a file here, or click to select'}\n              </Typography>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Supported formats: PDF, PNG, JPG, JPEG, TIFF, SVG, EPS, AI\n              </Typography>\n            </Paper>\n          ) : (\n            <Paper sx={{ p: 2, border: '1px solid', borderColor: 'success.main' }}>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <FileIcon color=\"success\" />\n                <Box flex={1}>\n                  <Typography variant=\"body2\" fontWeight=\"medium\">\n                    {selectedFile.name}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {formatFileSize(selectedFile.size)}\n                  </Typography>\n                </Box>\n                <Tooltip title=\"Remove file\">\n                  <IconButton size=\"small\" onClick={removeSelectedFile} disabled={uploading}>\n                    <CloseIcon />\n                  </IconButton>\n                </Tooltip>\n              </Box>\n            </Paper>\n          )}\n        </Box>\n\n        <Box mb={3}>\n          <TextField\n            fullWidth\n            label=\"Reason for re-upload\"\n            placeholder=\"e.g., Updated design per customer feedback, Fixed quality issues, etc.\"\n            value={reason}\n            onChange={(e) => setReason(e.target.value)}\n            multiline\n            rows={3}\n            required\n            disabled={uploading}\n            helperText=\"Please provide a clear reason for replacing this file\"\n          />\n        </Box>\n\n        {uploading && (\n          <Box mb={2}>\n            <Typography variant=\"body2\" gutterBottom>\n              Uploading... {uploadProgress}%\n            </Typography>\n            <LinearProgress variant=\"determinate\" value={uploadProgress} />\n          </Box>\n        )}\n\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n      </DialogContent>\n\n      <DialogActions>\n        <Button onClick={handleClose} disabled={uploading}>\n          Cancel\n        </Button>\n        <Button\n          variant=\"contained\"\n          onClick={handleReUpload}\n          disabled={!selectedFile || !reason.trim() || uploading}\n          startIcon={uploading ? undefined : <UploadIcon />}\n        >\n          {uploading ? 'Uploading...' : 'Re-upload File'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default FileReUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,cAAc,EACdC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,WAAW,IAAIC,UAAU,EACzBC,KAAK,IAAIC,SAAS,EAClBC,eAAe,IAAIC,QAAQ,QACtB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,eAAe,MAA2C,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWlG,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,IAAI;EACJC,OAAO;EACPC,OAAO;EACPC,IAAI;EACJC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACoC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM8C,MAAM,GAAG7C,WAAW,CAAE8C,aAAqB,IAAK;IACpD,IAAIA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MAC5Bb,eAAe,CAACY,aAAa,CAAC,CAAC,CAAC,CAAC;MACjCJ,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEM,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAG7B,WAAW,CAAC;IAChEwB,MAAM;IACNM,MAAM,EAAE;MACN,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;MACrD,iBAAiB,EAAE,CAAC,MAAM,CAAC;MAC3B,wBAAwB,EAAE,CAAC,MAAM,CAAC;MAClC,yBAAyB,EAAE,CAAC,KAAK;IACnC,CAAC;IACDC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAEA,CAAA,KAAMT,aAAa,CAAC,IAAI,CAAC;IACtCU,WAAW,EAAEA,CAAA,KAAMV,aAAa,CAAC,KAAK;EACxC,CAAC,CAAC;EAEF,MAAMW,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACtB,YAAY,EAAE;MACjBS,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACF;IAEA,IAAI,CAACP,MAAM,CAACqB,IAAI,CAAC,CAAC,EAAE;MAClBd,QAAQ,CAAC,oDAAoD,CAAC;MAC9D;IACF;IAEAJ,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,CAAC,CAAC;IACpBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAMe,gBAAgB,GAAGC,WAAW,CAAC,MAAM;QACzClB,iBAAiB,CAACmB,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;MACpD,CAAC,EAAE,GAAG,CAAC;MAEP,MAAMG,QAA8B,GAAG,MAAMxC,eAAe,CAACyC,YAAY,CACvEnC,OAAO,EACPC,IAAI,CAACmC,EAAE,EACP/B,YAAY,EACZE,MAAM,CAACqB,IAAI,CAAC,CACd,CAAC;MAEDS,aAAa,CAACR,gBAAgB,CAAC;MAC/BjB,iBAAiB,CAAC,GAAG,CAAC;MAEtB,IAAIsB,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,EAAE;QACrCC,UAAU,CAAC,MAAM;UACftC,SAAS,CAACgC,QAAQ,CAACK,IAAK,CAAC;UACzBE,WAAW,CAAC,CAAC;QACf,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACR,QAAQ,CAACS,OAAO,IAAI,0BAA0B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA;MACjBC,OAAO,CAACjC,KAAK,CAAC,kBAAkB,EAAE+B,GAAG,CAAC;MAEtC,IAAIG,YAAY,GAAG,6CAA6C;MAEhE,KAAAF,aAAA,GAAID,GAAG,CAACV,QAAQ,cAAAW,aAAA,eAAZA,aAAA,CAAcN,IAAI,EAAE;QACtB,IAAI,OAAOK,GAAG,CAACV,QAAQ,CAACK,IAAI,KAAK,QAAQ,EAAE;UACzCQ,YAAY,GAAGH,GAAG,CAACV,QAAQ,CAACK,IAAI;QAClC,CAAC,MAAM,IAAIK,GAAG,CAACV,QAAQ,CAACK,IAAI,CAACI,OAAO,EAAE;UACpCI,YAAY,GAAGH,GAAG,CAACV,QAAQ,CAACK,IAAI,CAACI,OAAO;QAC1C,CAAC,MAAM,IAAIC,GAAG,CAACV,QAAQ,CAACK,IAAI,CAAC1B,KAAK,EAAE;UAClCkC,YAAY,GAAGH,GAAG,CAACV,QAAQ,CAACK,IAAI,CAAC1B,KAAK;QACxC,CAAC,MAAM,IAAI+B,GAAG,CAACV,QAAQ,CAACK,IAAI,CAACS,MAAM,EAAE;UACnC;UACA,MAAMA,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACN,GAAG,CAACV,QAAQ,CAACK,IAAI,CAACS,MAAM,CAAC,CAACG,IAAI,CAAC,CAAC;UAC7DJ,YAAY,GAAGC,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC;QAClC,CAAC,MAAM;UACL;UACA,QAAQR,GAAG,CAACV,QAAQ,CAACmB,MAAM;YACzB,KAAK,GAAG;cACNN,YAAY,GAAG,oFAAoF;cACnG;YACF,KAAK,GAAG;cACNA,YAAY,GAAG,wFAAwF;cACvG;YACF,KAAK,GAAG;cACNA,YAAY,GAAG,iEAAiE;cAChF;YACF,KAAK,GAAG;cACNA,YAAY,GAAG,iEAAiE;cAChF;YACF,KAAK,GAAG;cACNA,YAAY,GAAG,2DAA2D;cAC1E;YACF;cACEA,YAAY,GAAG,4FAA4F;UAC/G;QACF;MACF,CAAC,MAAM,IAAIH,GAAG,CAACD,OAAO,EAAE;QACtB;QACA,IAAIC,GAAG,CAACD,OAAO,CAACW,QAAQ,CAAC,eAAe,CAAC,EAAE;UACzCP,YAAY,GAAG,gFAAgF;QACjG,CAAC,MAAM,IAAIH,GAAG,CAACD,OAAO,CAACW,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC1CP,YAAY,GAAG,8FAA8F;QAC/G,CAAC,MAAM;UACLA,YAAY,GAAGH,GAAG,CAACD,OAAO;QAC5B;MACF;MAEA7B,QAAQ,CAACiC,YAAY,CAAC;MACtBnC,iBAAiB,CAAC,CAAC,CAAC;IACtB,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM+B,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAChC,SAAS,EAAE;MACdH,eAAe,CAAC,IAAI,CAAC;MACrBE,SAAS,CAAC,EAAE,CAAC;MACbM,QAAQ,CAAC,IAAI,CAAC;MACdF,iBAAiB,CAAC,CAAC,CAAC;MACpBb,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMwD,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjD,eAAe,CAAC,IAAI,CAAC;IACrBQ,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAM0C,cAAc,GAAIC,KAAa,IAAa;IAChD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAG5B,IAAI,CAAC6B,KAAK,CAAC7B,IAAI,CAAC8B,GAAG,CAACL,KAAK,CAAC,GAAGzB,IAAI,CAAC8B,GAAG,CAACJ,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAGzB,IAAI,CAACgC,GAAG,CAACN,CAAC,EAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,oBACEhE,OAAA,CAACvB,MAAM;IAACyB,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAE0C,WAAY;IAACyB,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC/DxE,OAAA,CAACtB,WAAW;MAAA8F,QAAA,eACVxE,OAAA,CAACjB,GAAG;QAAC0F,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,eAAe;QAACC,UAAU,EAAC,QAAQ;QAAAH,QAAA,gBACpExE,OAAA,CAAChB,UAAU;UAAC4F,OAAO,EAAC,IAAI;UAAAJ,QAAA,EAAC;QAAc;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACpDhF,OAAA,CAACX,UAAU;UAAC4F,OAAO,EAAEpC,WAAY;UAACqC,QAAQ,EAAErE,SAAU;UAAA2D,QAAA,eACpDxE,OAAA,CAACN,SAAS;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdhF,OAAA,CAACrB,aAAa;MAAA6F,QAAA,gBACZxE,OAAA,CAACjB,GAAG;QAACoG,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACTxE,OAAA,CAAChB,UAAU;UAAC4F,OAAO,EAAC,WAAW;UAACQ,KAAK,EAAC,gBAAgB;UAACC,YAAY;UAAAb,QAAA,EAAC;QAEpE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhF,OAAA,CAACb,KAAK;UAACmG,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAAhB,QAAA,eACtCxE,OAAA,CAACjB,GAAG;YAAC0F,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACc,GAAG,EAAE,CAAE;YAAAjB,QAAA,gBAC7CxE,OAAA,CAACJ,QAAQ;cAACwF,KAAK,EAAC;YAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5BhF,OAAA,CAACjB,GAAG;cAAC2G,IAAI,EAAE,CAAE;cAAAlB,QAAA,gBACXxE,OAAA,CAAChB,UAAU;gBAAC4F,OAAO,EAAC,OAAO;gBAACe,UAAU,EAAC,QAAQ;gBAAAnB,QAAA,EAC5CnE,IAAI,CAACuF;cAAa;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACbhF,OAAA,CAAChB,UAAU;gBAAC4F,OAAO,EAAC,SAAS;gBAACQ,KAAK,EAAC,gBAAgB;gBAAAZ,QAAA,GACjDnE,IAAI,CAACwF,mBAAmB,EAAC,UAAG,EAACxF,IAAI,CAACyF,eAAe,EAAC,kBAAW,EAACzF,IAAI,CAAC0F,OAAO;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNhF,OAAA,CAACZ,IAAI;cACH4G,KAAK,EAAE,WAAW3F,IAAI,CAAC0F,OAAO,EAAG;cACjCE,IAAI,EAAC,OAAO;cACZb,KAAK,EAAC,SAAS;cACfR,OAAO,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENhF,OAAA,CAACjB,GAAG;QAACoG,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACTxE,OAAA,CAAChB,UAAU;UAAC4F,OAAO,EAAC,WAAW;UAACS,YAAY;UAAAb,QAAA,EAAC;QAE7C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ,CAACvE,YAAY,gBACZT,OAAA,CAACb,KAAK;UAAA,GACAqC,YAAY,CAAC,CAAC;UAClB8D,EAAE,EAAE;YACFC,CAAC,EAAE,CAAC;YACJW,MAAM,EAAE,YAAY;YACpBC,WAAW,EAAEzE,YAAY,GAAG,cAAc,GAAG,UAAU;YACvD8D,OAAO,EAAE9D,YAAY,GAAG,YAAY,GAAG,SAAS;YAChD0E,MAAM,EAAE,SAAS;YACjBC,SAAS,EAAE,QAAQ;YACnBC,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE;cACTH,WAAW,EAAE,cAAc;cAC3BX,OAAO,EAAE;YACX;UACF,CAAE;UAAAhB,QAAA,gBAEFxE,OAAA;YAAA,GAAWyB,aAAa,CAAC;UAAC;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9BhF,OAAA,CAACR,UAAU;YAAC8F,EAAE,EAAE;cAAEiB,QAAQ,EAAE,EAAE;cAAEnB,KAAK,EAAE,UAAU;cAAED,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DhF,OAAA,CAAChB,UAAU;YAAC4F,OAAO,EAAC,OAAO;YAACS,YAAY;YAAAb,QAAA,EACrC9C,YAAY,GACT,uBAAuB,GACvB;UAA6C;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACbhF,OAAA,CAAChB,UAAU;YAAC4F,OAAO,EAAC,SAAS;YAACQ,KAAK,EAAC,gBAAgB;YAAAZ,QAAA,EAAC;UAErD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,gBAERhF,OAAA,CAACb,KAAK;UAACmG,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEW,MAAM,EAAE,WAAW;YAAEC,WAAW,EAAE;UAAe,CAAE;UAAA3B,QAAA,eACpExE,OAAA,CAACjB,GAAG;YAAC0F,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACc,GAAG,EAAE,CAAE;YAAAjB,QAAA,gBAC7CxE,OAAA,CAACJ,QAAQ;cAACwF,KAAK,EAAC;YAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5BhF,OAAA,CAACjB,GAAG;cAAC2G,IAAI,EAAE,CAAE;cAAAlB,QAAA,gBACXxE,OAAA,CAAChB,UAAU;gBAAC4F,OAAO,EAAC,OAAO;gBAACe,UAAU,EAAC,QAAQ;gBAAAnB,QAAA,EAC5C/D,YAAY,CAAC+F;cAAI;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACbhF,OAAA,CAAChB,UAAU;gBAAC4F,OAAO,EAAC,SAAS;gBAACQ,KAAK,EAAC,gBAAgB;gBAAAZ,QAAA,EACjDZ,cAAc,CAACnD,YAAY,CAACwF,IAAI;cAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNhF,OAAA,CAACV,OAAO;cAACmH,KAAK,EAAC,aAAa;cAAAjC,QAAA,eAC1BxE,OAAA,CAACX,UAAU;gBAAC4G,IAAI,EAAC,OAAO;gBAAChB,OAAO,EAAEtB,kBAAmB;gBAACuB,QAAQ,EAAErE,SAAU;gBAAA2D,QAAA,eACxExE,OAAA,CAACN,SAAS;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENhF,OAAA,CAACjB,GAAG;QAACoG,EAAE,EAAE,CAAE;QAAAX,QAAA,eACTxE,OAAA,CAAClB,SAAS;UACRyF,SAAS;UACTyB,KAAK,EAAC,sBAAsB;UAC5BU,WAAW,EAAC,wEAAwE;UACpFC,KAAK,EAAEhG,MAAO;UACdiG,QAAQ,EAAGC,CAAC,IAAKjG,SAAS,CAACiG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC3CI,SAAS;UACTC,IAAI,EAAE,CAAE;UACRC,QAAQ;UACR/B,QAAQ,EAAErE,SAAU;UACpBqG,UAAU,EAAC;QAAuD;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELnE,SAAS,iBACRb,OAAA,CAACjB,GAAG;QAACoG,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACTxE,OAAA,CAAChB,UAAU;UAAC4F,OAAO,EAAC,OAAO;UAACS,YAAY;UAAAb,QAAA,GAAC,eAC1B,EAACzD,cAAc,EAAC,GAC/B;QAAA;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhF,OAAA,CAACd,cAAc;UAAC0F,OAAO,EAAC,aAAa;UAAC+B,KAAK,EAAE5F;QAAe;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CACN,EAEA/D,KAAK,iBACJjB,OAAA,CAACf,KAAK;QAACkI,QAAQ,EAAC,OAAO;QAAC7B,EAAE,EAAE;UAAEH,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EACnCvD;MAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhBhF,OAAA,CAACpB,aAAa;MAAA4F,QAAA,gBACZxE,OAAA,CAACnB,MAAM;QAACoG,OAAO,EAAEpC,WAAY;QAACqC,QAAQ,EAAErE,SAAU;QAAA2D,QAAA,EAAC;MAEnD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThF,OAAA,CAACnB,MAAM;QACL+F,OAAO,EAAC,WAAW;QACnBK,OAAO,EAAElD,cAAe;QACxBmD,QAAQ,EAAE,CAACzE,YAAY,IAAI,CAACE,MAAM,CAACqB,IAAI,CAAC,CAAC,IAAInB,SAAU;QACvDuG,SAAS,EAAEvG,SAAS,GAAGwG,SAAS,gBAAGrH,OAAA,CAACR,UAAU;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAR,QAAA,EAEjD3D,SAAS,GAAG,cAAc,GAAG;MAAgB;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACxE,EAAA,CAnSIP,YAAyC;EAAA,QAsBSJ,WAAW;AAAA;AAAAyH,EAAA,GAtB7DrH,YAAyC;AAqS/C,eAAeA,YAAY;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}