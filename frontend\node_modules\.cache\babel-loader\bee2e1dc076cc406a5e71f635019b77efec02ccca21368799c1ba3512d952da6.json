{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionButton from './AccordionButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionHeader = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'h2',\n    'aria-controls': ariaControls,\n    bsPrefix,\n    className,\n    children,\n    onClick,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-header');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix),\n    children: /*#__PURE__*/_jsx(AccordionButton, {\n      onClick: onClick,\n      \"aria-controls\": ariaControls,\n      children: children\n    })\n  });\n});\nAccordionHeader.displayName = 'AccordionHeader';\nexport default AccordionHeader;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "Accordion<PERSON><PERSON><PERSON>", "jsx", "_jsx", "Accordi<PERSON><PERSON><PERSON><PERSON>", "forwardRef", "_ref", "ref", "as", "Component", "ariaControls", "bsPrefix", "className", "children", "onClick", "props", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/AccordionHeader.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionButton from './AccordionButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionHeader = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'h2',\n  'aria-controls': ariaControls,\n  bsPrefix,\n  className,\n  children,\n  onClick,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-header');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix),\n    children: /*#__PURE__*/_jsx(AccordionButton, {\n      onClick: onClick,\n      \"aria-controls\": ariaControls,\n      children: children\n    })\n  });\n});\nAccordionHeader.displayName = 'AccordionHeader';\nexport default AccordionHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAAC,IAAA,EASnDC,GAAG,KAAK;EAAA,IAT4C;IACrD;IACAC,EAAE,EAAEC,SAAS,GAAG,IAAI;IACpB,eAAe,EAAEC,YAAY;IAC7BC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACRC,OAAO;IACP,GAAGC;EACL,CAAC,GAAAT,IAAA;EACCK,QAAQ,GAAGX,kBAAkB,CAACW,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaR,IAAI,CAACM,SAAS,EAAE;IAClCF,GAAG,EAAEA,GAAG;IACR,GAAGQ,KAAK;IACRH,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAED,QAAQ,CAAC;IAC1CE,QAAQ,EAAE,aAAaV,IAAI,CAACF,eAAe,EAAE;MAC3Ca,OAAO,EAAEA,OAAO;MAChB,eAAe,EAAEJ,YAAY;MAC7BG,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,eAAe,CAACY,WAAW,GAAG,iBAAiB;AAC/C,eAAeZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}