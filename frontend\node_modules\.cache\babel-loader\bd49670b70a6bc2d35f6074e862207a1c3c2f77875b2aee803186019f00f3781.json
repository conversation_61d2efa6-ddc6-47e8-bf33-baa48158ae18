{"ast": null, "code": "export { default } from \"./TableSortLabel.js\";\nexport { default as tableSortLabelClasses } from \"./tableSortLabelClasses.js\";\nexport * from \"./tableSortLabelClasses.js\";", "map": {"version": 3, "names": ["default", "tableSortLabelClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/TableSortLabel/index.js"], "sourcesContent": ["export { default } from \"./TableSortLabel.js\";\nexport { default as tableSortLabelClasses } from \"./tableSortLabelClasses.js\";\nexport * from \"./tableSortLabelClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,4BAA4B;AAC7E,cAAc,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}