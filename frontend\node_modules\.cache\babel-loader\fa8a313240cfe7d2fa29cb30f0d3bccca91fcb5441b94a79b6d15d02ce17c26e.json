{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport default api;\n\n// API endpoints\nexport const endpoints = {\n  // Auth endpoints\n  register: '/register',\n  login: '/login',\n  logout: '/logout',\n  forgotPassword: '/forgot-password',\n  resetPassword: '/reset-password',\n  verifyEmail: '/email/verify',\n  resendVerification: '/email/verification-notification',\n  // User endpoints\n  profile: '/user',\n  updateProfile: '/user',\n  uploadAvatar: '/user/avatar',\n  adminStatus: '/admin-status',\n  adminSession: '/admin-session',\n  // CMS endpoints\n  pages: '/pages',\n  page: slug => `/pages/${slug}`,\n  pageContent: slug => `/pages/${slug}/content`,\n  homepage: '/pages/home/<USER>',\n  // Admin CMS endpoints\n  adminPages: '/admin/cms-pages',\n  adminPage: id => `/admin/cms-pages/${id}`,\n  // Credit endpoints\n  creditBalance: '/credit/balance',\n  creditPackages: '/credit/packages',\n  creditTransactions: '/credit/transactions',\n  creditStatistics: '/credit/statistics',\n  // Payment endpoints\n  createPayment: '/payment/create',\n  checkPaymentStatus: '/payment/status',\n  paymentConfig: '/payment/config',\n  billplzCallback: '/billplz/callback',\n  // Printing endpoints\n  printing: '/printing',\n  orders: '/orders',\n  // Settings endpoints\n  settings: '/settings',\n  settingsSeoMeta: '/settings/seo-meta',\n  settingsBranding: '/settings/branding',\n  settingByKey: key => `/settings/${key}`\n};", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "endpoints", "register", "login", "logout", "forgotPassword", "resetPassword", "verifyEmail", "resendVerification", "profile", "updateProfile", "uploadAvatar", "adminStatus", "adminSession", "pages", "page", "slug", "pageContent", "homepage", "adminPages", "adminPage", "id", "creditBalance", "creditPackages", "creditTransactions", "creditStatistics", "createPayment", "checkPaymentStatus", "paymentConfig", "billplzCallback", "printing", "orders", "settings", "settingsSeoMeta", "settingsBranding", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key"], "sources": ["C:/laragon/www/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport default api;\n\n// API endpoints\nexport const endpoints = {\n  // Auth endpoints\n  register: '/register',\n  login: '/login',\n  logout: '/logout',\n  forgotPassword: '/forgot-password',\n  resetPassword: '/reset-password',\n  verifyEmail: '/email/verify',\n  resendVerification: '/email/verification-notification',\n\n  // User endpoints\n  profile: '/user',\n  updateProfile: '/user',\n  uploadAvatar: '/user/avatar',\n  adminStatus: '/admin-status',\n  adminSession: '/admin-session',\n\n  // CMS endpoints\n  pages: '/pages',\n  page: (slug: string) => `/pages/${slug}`,\n  pageContent: (slug: string) => `/pages/${slug}/content`,\n  homepage: '/pages/home/<USER>',\n\n  // Admin CMS endpoints\n  adminPages: '/admin/cms-pages',\n  adminPage: (id: number) => `/admin/cms-pages/${id}`,\n\n  // Credit endpoints\n  creditBalance: '/credit/balance',\n  creditPackages: '/credit/packages',\n  creditTransactions: '/credit/transactions',\n  creditStatistics: '/credit/statistics',\n\n  // Payment endpoints\n  createPayment: '/payment/create',\n  checkPaymentStatus: '/payment/status',\n  paymentConfig: '/payment/config',\n  billplzCallback: '/billplz/callback',\n\n  // Printing endpoints\n  printing: '/printing',\n  orders: '/orders',\n\n  // Settings endpoints\n  settings: '/settings',\n  settingsSeoMeta: '/settings/seo-meta',\n  settingsBranding: '/settings/branding',\n  settingByKey: (key: string) => `/settings/${key}`,\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;IACrCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAeZ,GAAG;;AAElB;AACA,OAAO,MAAMsB,SAAS,GAAG;EACvB;EACAC,QAAQ,EAAE,WAAW;EACrBC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,SAAS;EACjBC,cAAc,EAAE,kBAAkB;EAClCC,aAAa,EAAE,iBAAiB;EAChCC,WAAW,EAAE,eAAe;EAC5BC,kBAAkB,EAAE,kCAAkC;EAEtD;EACAC,OAAO,EAAE,OAAO;EAChBC,aAAa,EAAE,OAAO;EACtBC,YAAY,EAAE,cAAc;EAC5BC,WAAW,EAAE,eAAe;EAC5BC,YAAY,EAAE,gBAAgB;EAE9B;EACAC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAGC,IAAY,IAAK,UAAUA,IAAI,EAAE;EACxCC,WAAW,EAAGD,IAAY,IAAK,UAAUA,IAAI,UAAU;EACvDE,QAAQ,EAAE,qBAAqB;EAE/B;EACAC,UAAU,EAAE,kBAAkB;EAC9BC,SAAS,EAAGC,EAAU,IAAK,oBAAoBA,EAAE,EAAE;EAEnD;EACAC,aAAa,EAAE,iBAAiB;EAChCC,cAAc,EAAE,kBAAkB;EAClCC,kBAAkB,EAAE,sBAAsB;EAC1CC,gBAAgB,EAAE,oBAAoB;EAEtC;EACAC,aAAa,EAAE,iBAAiB;EAChCC,kBAAkB,EAAE,iBAAiB;EACrCC,aAAa,EAAE,iBAAiB;EAChCC,eAAe,EAAE,mBAAmB;EAEpC;EACAC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,SAAS;EAEjB;EACAC,QAAQ,EAAE,WAAW;EACrBC,eAAe,EAAE,oBAAoB;EACrCC,gBAAgB,EAAE,oBAAoB;EACtCC,YAAY,EAAGC,GAAW,IAAK,aAAaA,GAAG;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}