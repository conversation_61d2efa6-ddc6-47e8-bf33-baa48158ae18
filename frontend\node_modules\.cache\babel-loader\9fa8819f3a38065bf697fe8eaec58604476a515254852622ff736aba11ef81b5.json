{"ast": null, "code": "// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n    case 1:\n      return x ^ y ^ z;\n    case 2:\n      return x & y ^ x & z ^ y & z;\n    case 3:\n      return x ^ y ^ z;\n  }\n}\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\nfunction sha1(bytes) {\n  const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n    for (let i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n  bytes.push(0x80);\n  const l = bytes.length / 4 + 2;\n  const N = Math.ceil(l / 16);\n  const M = new Array(N);\n  for (let i = 0; i < N; ++i) {\n    const arr = new Uint32Array(16);\n    for (let j = 0; j < 16; ++j) {\n      arr[j] = bytes[i * 64 + j * 4] << 24 | bytes[i * 64 + j * 4 + 1] << 16 | bytes[i * 64 + j * 4 + 2] << 8 | bytes[i * 64 + j * 4 + 3];\n    }\n    M[i] = arr;\n  }\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n  for (let i = 0; i < N; ++i) {\n    const W = new Uint32Array(80);\n    for (let t = 0; t < 16; ++t) {\n      W[t] = M[i][t];\n    }\n    for (let t = 16; t < 80; ++t) {\n      W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n    let a = H[0];\n    let b = H[1];\n    let c = H[2];\n    let d = H[3];\n    let e = H[4];\n    for (let t = 0; t < 80; ++t) {\n      const s = Math.floor(t / 20);\n      const T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\nexport default sha1;", "map": {"version": 3, "names": ["f", "s", "x", "y", "z", "ROTL", "n", "sha1", "bytes", "K", "H", "msg", "unescape", "encodeURIComponent", "i", "length", "push", "charCodeAt", "Array", "isArray", "prototype", "slice", "call", "l", "N", "Math", "ceil", "M", "arr", "Uint32Array", "j", "pow", "floor", "W", "t", "a", "b", "c", "d", "e", "T"], "sources": ["C:/laragon/www/frontend/node_modules/uuid/dist/esm-browser/sha1.js"], "sourcesContent": ["// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n\n    case 1:\n      return x ^ y ^ z;\n\n    case 2:\n      return x & y ^ x & z ^ y & z;\n\n    case 3:\n      return x ^ y ^ z;\n  }\n}\n\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\n\nfunction sha1(bytes) {\n  const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n\n    for (let i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n\n  bytes.push(0x80);\n  const l = bytes.length / 4 + 2;\n  const N = Math.ceil(l / 16);\n  const M = new Array(N);\n\n  for (let i = 0; i < N; ++i) {\n    const arr = new Uint32Array(16);\n\n    for (let j = 0; j < 16; ++j) {\n      arr[j] = bytes[i * 64 + j * 4] << 24 | bytes[i * 64 + j * 4 + 1] << 16 | bytes[i * 64 + j * 4 + 2] << 8 | bytes[i * 64 + j * 4 + 3];\n    }\n\n    M[i] = arr;\n  }\n\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n\n  for (let i = 0; i < N; ++i) {\n    const W = new Uint32Array(80);\n\n    for (let t = 0; t < 16; ++t) {\n      W[t] = M[i][t];\n    }\n\n    for (let t = 16; t < 80; ++t) {\n      W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n\n    let a = H[0];\n    let b = H[1];\n    let c = H[2];\n    let d = H[3];\n    let e = H[4];\n\n    for (let t = 0; t < 80; ++t) {\n      const s = Math.floor(t / 20);\n      const T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\n\nexport default sha1;"], "mappings": "AAAA;AACA;AACA,SAASA,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACrB,QAAQH,CAAC;IACP,KAAK,CAAC;MACJ,OAAOC,CAAC,GAAGC,CAAC,GAAG,CAACD,CAAC,GAAGE,CAAC;IAEvB,KAAK,CAAC;MACJ,OAAOF,CAAC,GAAGC,CAAC,GAAGC,CAAC;IAElB,KAAK,CAAC;MACJ,OAAOF,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGE,CAAC,GAAGD,CAAC,GAAGC,CAAC;IAE9B,KAAK,CAAC;MACJ,OAAOF,CAAC,GAAGC,CAAC,GAAGC,CAAC;EACpB;AACF;AAEA,SAASC,IAAIA,CAACH,CAAC,EAAEI,CAAC,EAAE;EAClB,OAAOJ,CAAC,IAAII,CAAC,GAAGJ,CAAC,KAAK,EAAE,GAAGI,CAAC;AAC9B;AAEA,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,MAAMC,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EAC1D,MAAMC,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EAEtE,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMG,GAAG,GAAGC,QAAQ,CAACC,kBAAkB,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEjDA,KAAK,GAAG,EAAE;IAEV,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAE,EAAED,CAAC,EAAE;MACnCN,KAAK,CAACQ,IAAI,CAACL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC,MAAM,IAAI,CAACI,KAAK,CAACC,OAAO,CAACX,KAAK,CAAC,EAAE;IAChC;IACAA,KAAK,GAAGU,KAAK,CAACE,SAAS,CAACC,KAAK,CAACC,IAAI,CAACd,KAAK,CAAC;EAC3C;EAEAA,KAAK,CAACQ,IAAI,CAAC,IAAI,CAAC;EAChB,MAAMO,CAAC,GAAGf,KAAK,CAACO,MAAM,GAAG,CAAC,GAAG,CAAC;EAC9B,MAAMS,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACH,CAAC,GAAG,EAAE,CAAC;EAC3B,MAAMI,CAAC,GAAG,IAAIT,KAAK,CAACM,CAAC,CAAC;EAEtB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,EAAE,EAAEV,CAAC,EAAE;IAC1B,MAAMc,GAAG,GAAG,IAAIC,WAAW,CAAC,EAAE,CAAC;IAE/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC3BF,GAAG,CAACE,CAAC,CAAC,GAAGtB,KAAK,CAACM,CAAC,GAAG,EAAE,GAAGgB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAGtB,KAAK,CAACM,CAAC,GAAG,EAAE,GAAGgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAGtB,KAAK,CAACM,CAAC,GAAG,EAAE,GAAGgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGtB,KAAK,CAACM,CAAC,GAAG,EAAE,GAAGgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrI;IAEAH,CAAC,CAACb,CAAC,CAAC,GAAGc,GAAG;EACZ;EAEAD,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAChB,KAAK,CAACO,MAAM,GAAG,CAAC,IAAI,CAAC,GAAGU,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EACvDJ,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACO,KAAK,CAACL,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACvCG,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAChB,KAAK,CAACO,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU;EAElD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,EAAE,EAAEV,CAAC,EAAE;IAC1B,MAAMmB,CAAC,GAAG,IAAIJ,WAAW,CAAC,EAAE,CAAC;IAE7B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC3BD,CAAC,CAACC,CAAC,CAAC,GAAGP,CAAC,CAACb,CAAC,CAAC,CAACoB,CAAC,CAAC;IAChB;IAEA,KAAK,IAAIA,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC5BD,CAAC,CAACC,CAAC,CAAC,GAAG7B,IAAI,CAAC4B,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,GAAG,EAAE,CAAC,GAAGD,CAAC,CAACC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7D;IAEA,IAAIC,CAAC,GAAGzB,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI0B,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI2B,CAAC,GAAG3B,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI4B,CAAC,GAAG5B,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI6B,CAAC,GAAG7B,CAAC,CAAC,CAAC,CAAC;IAEZ,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC3B,MAAMjC,CAAC,GAAGwB,IAAI,CAACO,KAAK,CAACE,CAAC,GAAG,EAAE,CAAC;MAC5B,MAAMM,CAAC,GAAGnC,IAAI,CAAC8B,CAAC,EAAE,CAAC,CAAC,GAAGnC,CAAC,CAACC,CAAC,EAAEmC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGC,CAAC,GAAG9B,CAAC,CAACR,CAAC,CAAC,GAAGgC,CAAC,CAACC,CAAC,CAAC,KAAK,CAAC;MAC5DK,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGhC,IAAI,CAAC+B,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;MACrBA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGK,CAAC;IACP;IAEA9B,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGyB,CAAC,KAAK,CAAC;IACrBzB,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG0B,CAAC,KAAK,CAAC;IACrB1B,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG2B,CAAC,KAAK,CAAC;IACrB3B,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG4B,CAAC,KAAK,CAAC;IACrB5B,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG6B,CAAC,KAAK,CAAC;EACvB;EAEA,OAAO,CAAC7B,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAClW;AAEA,eAAeH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}