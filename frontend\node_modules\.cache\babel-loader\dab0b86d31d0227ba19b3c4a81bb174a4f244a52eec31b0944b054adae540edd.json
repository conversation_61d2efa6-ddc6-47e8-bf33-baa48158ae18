{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from \"../Timeline/TimelineContext.js\";\nimport { getTimelineOppositeContentUtilityClass } from \"./timelineOppositeContentClasses.js\";\nimport convertTimelinePositionToClass from \"../internal/convertTimelinePositionToClass.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineOppositeContentUtilityClass, classes);\n};\nconst TimelineOppositeContentRoot = styled(Typography, {\n  name: 'MuiTimelineOppositeContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return {\n    padding: '6px 16px',\n    marginRight: 'auto',\n    textAlign: 'right',\n    flex: 1,\n    ...(ownerState.position === 'left' && {\n      textAlign: 'left'\n    })\n  };\n});\nconst TimelineOppositeContent = /*#__PURE__*/React.forwardRef(function TimelineOppositeContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineOppositeContent'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = {\n    ...props,\n    position: positionContext || 'left'\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineOppositeContentRoot, {\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineOppositeContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nTimelineOppositeContent.muiName = 'TimelineOppositeContent';\nexport default TimelineOppositeContent;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "styled", "useThemeProps", "composeClasses", "Typography", "TimelineContext", "getTimelineOppositeContentUtilityClass", "convertTimelinePositionToClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "position", "classes", "slots", "root", "TimelineOppositeContentRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "padding", "marginRight", "textAlign", "flex", "TimelineOppositeContent", "forwardRef", "inProps", "ref", "className", "other", "positionContext", "useContext", "component", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool", "mui<PERSON><PERSON>"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineOppositeContent/TimelineOppositeContent.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from \"../Timeline/TimelineContext.js\";\nimport { getTimelineOppositeContentUtilityClass } from \"./timelineOppositeContentClasses.js\";\nimport convertTimelinePositionToClass from \"../internal/convertTimelinePositionToClass.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineOppositeContentUtilityClass, classes);\n};\nconst TimelineOppositeContentRoot = styled(Typography, {\n  name: 'MuiTimelineOppositeContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => ({\n  padding: '6px 16px',\n  marginRight: 'auto',\n  textAlign: 'right',\n  flex: 1,\n  ...(ownerState.position === 'left' && {\n    textAlign: 'left'\n  })\n}));\nconst TimelineOppositeContent = /*#__PURE__*/React.forwardRef(function TimelineOppositeContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineOppositeContent'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = {\n    ...props,\n    position: positionContext || 'left'\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineOppositeContentRoot, {\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineOppositeContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nTimelineOppositeContent.muiName = 'TimelineOppositeContent';\nexport default TimelineOppositeContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,SAASC,sCAAsC,QAAQ,qCAAqC;AAC5F,OAAOC,8BAA8B,MAAM,+CAA+C;AAC1F,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAER,8BAA8B,CAACK,QAAQ,CAAC;EACzD,CAAC;EACD,OAAOT,cAAc,CAACW,KAAK,EAAER,sCAAsC,EAAEO,OAAO,CAAC;AAC/E,CAAC;AACD,MAAMG,2BAA2B,GAAGf,MAAM,CAACG,UAAU,EAAE;EACrDa,IAAI,EAAE,4BAA4B;EAClCC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACd,8BAA8B,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC;EACnF;AACF,CAAC,CAAC,CAACU,IAAA;EAAA,IAAC;IACFX;EACF,CAAC,GAAAW,IAAA;EAAA,OAAM;IACLC,OAAO,EAAE,UAAU;IACnBC,WAAW,EAAE,MAAM;IACnBC,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE,CAAC;IACP,IAAIf,UAAU,CAACC,QAAQ,KAAK,MAAM,IAAI;MACpCa,SAAS,EAAE;IACb,CAAC;EACH,CAAC;AAAA,CAAC,CAAC;AACH,MAAME,uBAAuB,GAAG,aAAa7B,KAAK,CAAC8B,UAAU,CAAC,SAASD,uBAAuBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3G,MAAMV,KAAK,GAAGlB,aAAa,CAAC;IAC1BkB,KAAK,EAAES,OAAO;IACdZ,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJc,SAAS;IACT,GAAGC;EACL,CAAC,GAAGZ,KAAK;EACT,MAAM;IACJR,QAAQ,EAAEqB;EACZ,CAAC,GAAGnC,KAAK,CAACoC,UAAU,CAAC7B,eAAe,CAAC;EACrC,MAAMM,UAAU,GAAG;IACjB,GAAGS,KAAK;IACRR,QAAQ,EAAEqB,eAAe,IAAI;EAC/B,CAAC;EACD,MAAMpB,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,2BAA2B,EAAE;IACpDmB,SAAS,EAAE,KAAK;IAChBJ,SAAS,EAAE/B,IAAI,CAACa,OAAO,CAACE,IAAI,EAAEgB,SAAS,CAAC;IACxCpB,UAAU,EAAEA,UAAU;IACtBmB,GAAG,EAAEA,GAAG;IACR,GAAGE;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,uBAAuB,CAACY,SAAS,CAAC,yBAAyB;EACjG;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEzC,SAAS,CAAC0C,IAAI;EACxB;AACF;AACA;EACE5B,OAAO,EAAEd,SAAS,CAAC2C,MAAM;EACzB;AACF;AACA;EACEX,SAAS,EAAEhC,SAAS,CAAC4C,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAE7C,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAAC+C,OAAO,CAAC/C,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC2C,MAAM,EAAE3C,SAAS,CAACiD,IAAI,CAAC,CAAC,CAAC,EAAEjD,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC2C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACVf,uBAAuB,CAACsB,OAAO,GAAG,yBAAyB;AAC3D,eAAetB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}