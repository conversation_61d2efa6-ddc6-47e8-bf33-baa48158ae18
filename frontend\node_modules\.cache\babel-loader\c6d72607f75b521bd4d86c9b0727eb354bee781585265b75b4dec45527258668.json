{"ast": null, "code": "export { default } from \"./TimelineDot.js\";\nexport { default as timelineDotClasses } from \"./timelineDotClasses.js\";\nexport * from \"./timelineDotClasses.js\";", "map": {"version": 3, "names": ["default", "timelineDotClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineDot/index.js"], "sourcesContent": ["export { default } from \"./TimelineDot.js\";\nexport { default as timelineDotClasses } from \"./timelineDotClasses.js\";\nexport * from \"./timelineDotClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,yBAAyB;AACvE,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}