{"ast": null, "code": "import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport default function withTheme() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: withTheme is no longer exported from @mui/material/styles.\\n' + 'You have to import it from @mui/styles.\\n' + 'See https://mui.com/r/migration-v4/#mui-material-styles for more details.' : _formatErrorMessage(16));\n}", "map": {"version": 3, "names": ["_formatErrorMessage", "withTheme", "Error", "process", "env", "NODE_ENV"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/styles/withTheme.js"], "sourcesContent": ["import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport default function withTheme() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: withTheme is no longer exported from @mui/material/styles.\\n' + 'You have to import it from @mui/styles.\\n' + 'See https://mui.com/r/migration-v4/#mui-material-styles for more details.' : _formatErrorMessage(16));\n}"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,kCAAkC;AAClE,eAAe,SAASC,SAASA,CAAA,EAAG;EAClC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,mEAAmE,GAAG,2CAA2C,GAAG,2EAA2E,GAAGL,mBAAmB,CAAC,EAAE,CAAC,CAAC;AACpR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}