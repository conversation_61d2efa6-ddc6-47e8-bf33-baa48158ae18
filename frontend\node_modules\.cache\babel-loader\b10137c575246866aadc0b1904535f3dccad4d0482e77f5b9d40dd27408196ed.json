{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Container = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  fluid = false,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  className,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'container');\n  const suffix = typeof fluid === 'string' ? `-${fluid}` : '-fluid';\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, fluid ? `${prefix}${suffix}` : prefix)\n  });\n});\nContainer.displayName = 'Container';\nexport default Container;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "Container", "forwardRef", "bsPrefix", "fluid", "as", "Component", "className", "props", "ref", "prefix", "suffix", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Container.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Container = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  fluid = false,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  className,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'container');\n  const suffix = typeof fluid === 'string' ? `-${fluid}` : '-fluid';\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, fluid ? `${prefix}${suffix}` : prefix)\n  });\n});\nContainer.displayName = 'Container';\nexport default Container;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EAC/CC,QAAQ;EACRC,KAAK,GAAG,KAAK;EACb;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrBC,SAAS;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGZ,kBAAkB,CAACK,QAAQ,EAAE,WAAW,CAAC;EACxD,MAAMQ,MAAM,GAAG,OAAOP,KAAK,KAAK,QAAQ,GAAG,IAAIA,KAAK,EAAE,GAAG,QAAQ;EACjE,OAAO,aAAaJ,IAAI,CAACM,SAAS,EAAE;IAClCG,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRD,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAEH,KAAK,GAAG,GAAGM,MAAM,GAAGC,MAAM,EAAE,GAAGD,MAAM;EACxE,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,SAAS,CAACW,WAAW,GAAG,WAAW;AACnC,eAAeX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}