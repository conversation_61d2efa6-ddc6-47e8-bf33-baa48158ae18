{"ast": null, "code": "import { useContext, useCallback } from 'react';\nimport * as React from 'react';\nimport { useSSRSafeId } from './ssr';\nimport DropdownContext from './DropdownContext';\nimport { Fragment as _Fragment, jsx as _jsx } from \"react/jsx-runtime\";\nexport const isRoleMenu = el => {\n  var _el$getAttribute;\n  return ((_el$getAttribute = el.getAttribute('role')) == null ? void 0 : _el$getAttribute.toLowerCase()) === 'menu';\n};\nconst noop = () => {};\n\n/**\n * Wires up Dropdown toggle functionality, returning a set a props to attach\n * to the element that functions as the dropdown toggle (generally a button).\n *\n * @memberOf Dropdown\n */\nexport function useDropdownToggle() {\n  const id = useSSRSafeId();\n  const {\n    show = false,\n    toggle = noop,\n    setToggle,\n    menuElement\n  } = useContext(DropdownContext) || {};\n  const handleClick = useCallback(e => {\n    toggle(!show, e);\n  }, [show, toggle]);\n  const props = {\n    id,\n    ref: setToggle || noop,\n    onClick: handleClick,\n    'aria-expanded': !!show\n  };\n\n  // This is maybe better down in an effect, but\n  // the component is going to update anyway when the menu element\n  // is set so might return new props.\n  if (menuElement && isRoleMenu(menuElement)) {\n    props['aria-haspopup'] = true;\n  }\n  return [props, {\n    show,\n    toggle\n  }];\n}\n/**\n * Also exported as `<Dropdown.Toggle>` from `Dropdown`.\n *\n * @displayName DropdownToggle\n * @memberOf Dropdown\n */\nfunction DropdownToggle({\n  children\n}) {\n  const [props, meta] = useDropdownToggle();\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: children(props, meta)\n  });\n}\nDropdownToggle.displayName = 'DropdownToggle';\n\n/** @component */\nexport default DropdownToggle;", "map": {"version": 3, "names": ["useContext", "useCallback", "React", "useSSRSafeId", "DropdownContext", "Fragment", "_Fragment", "jsx", "_jsx", "isRoleMenu", "el", "_el$getAttribute", "getAttribute", "toLowerCase", "noop", "useDropdownToggle", "id", "show", "toggle", "<PERSON><PERSON><PERSON><PERSON>", "menuElement", "handleClick", "e", "props", "ref", "onClick", "DropdownToggle", "children", "meta", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/@restart/ui/esm/DropdownToggle.js"], "sourcesContent": ["import { useContext, useCallback } from 'react';\nimport * as React from 'react';\nimport { useSSRSafeId } from './ssr';\nimport DropdownContext from './DropdownContext';\nimport { Fragment as _Fragment, jsx as _jsx } from \"react/jsx-runtime\";\nexport const isRoleMenu = el => {\n  var _el$getAttribute;\n  return ((_el$getAttribute = el.getAttribute('role')) == null ? void 0 : _el$getAttribute.toLowerCase()) === 'menu';\n};\nconst noop = () => {};\n\n/**\n * Wires up Dropdown toggle functionality, returning a set a props to attach\n * to the element that functions as the dropdown toggle (generally a button).\n *\n * @memberOf Dropdown\n */\nexport function useDropdownToggle() {\n  const id = useSSRSafeId();\n  const {\n    show = false,\n    toggle = noop,\n    setToggle,\n    menuElement\n  } = useContext(DropdownContext) || {};\n  const handleClick = useCallback(e => {\n    toggle(!show, e);\n  }, [show, toggle]);\n  const props = {\n    id,\n    ref: setToggle || noop,\n    onClick: handleClick,\n    'aria-expanded': !!show\n  };\n\n  // This is maybe better down in an effect, but\n  // the component is going to update anyway when the menu element\n  // is set so might return new props.\n  if (menuElement && isRoleMenu(menuElement)) {\n    props['aria-haspopup'] = true;\n  }\n  return [props, {\n    show,\n    toggle\n  }];\n}\n/**\n * Also exported as `<Dropdown.Toggle>` from `Dropdown`.\n *\n * @displayName DropdownToggle\n * @memberOf Dropdown\n */\nfunction DropdownToggle({\n  children\n}) {\n  const [props, meta] = useDropdownToggle();\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: children(props, meta)\n  });\n}\nDropdownToggle.displayName = 'DropdownToggle';\n\n/** @component */\nexport default DropdownToggle;"], "mappings": "AAAA,SAASA,UAAU,EAAEC,WAAW,QAAQ,OAAO;AAC/C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,OAAO;AACpC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,EAAEC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AACtE,OAAO,MAAMC,UAAU,GAAGC,EAAE,IAAI;EAC9B,IAAIC,gBAAgB;EACpB,OAAO,CAAC,CAACA,gBAAgB,GAAGD,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,gBAAgB,CAACE,WAAW,CAAC,CAAC,MAAM,MAAM;AACpH,CAAC;AACD,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,MAAMC,EAAE,GAAGb,YAAY,CAAC,CAAC;EACzB,MAAM;IACJc,IAAI,GAAG,KAAK;IACZC,MAAM,GAAGJ,IAAI;IACbK,SAAS;IACTC;EACF,CAAC,GAAGpB,UAAU,CAACI,eAAe,CAAC,IAAI,CAAC,CAAC;EACrC,MAAMiB,WAAW,GAAGpB,WAAW,CAACqB,CAAC,IAAI;IACnCJ,MAAM,CAAC,CAACD,IAAI,EAAEK,CAAC,CAAC;EAClB,CAAC,EAAE,CAACL,IAAI,EAAEC,MAAM,CAAC,CAAC;EAClB,MAAMK,KAAK,GAAG;IACZP,EAAE;IACFQ,GAAG,EAAEL,SAAS,IAAIL,IAAI;IACtBW,OAAO,EAAEJ,WAAW;IACpB,eAAe,EAAE,CAAC,CAACJ;EACrB,CAAC;;EAED;EACA;EACA;EACA,IAAIG,WAAW,IAAIX,UAAU,CAACW,WAAW,CAAC,EAAE;IAC1CG,KAAK,CAAC,eAAe,CAAC,GAAG,IAAI;EAC/B;EACA,OAAO,CAACA,KAAK,EAAE;IACbN,IAAI;IACJC;EACF,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,cAAcA,CAAC;EACtBC;AACF,CAAC,EAAE;EACD,MAAM,CAACJ,KAAK,EAAEK,IAAI,CAAC,GAAGb,iBAAiB,CAAC,CAAC;EACzC,OAAO,aAAaP,IAAI,CAACF,SAAS,EAAE;IAClCqB,QAAQ,EAAEA,QAAQ,CAACJ,KAAK,EAAEK,IAAI;EAChC,CAAC,CAAC;AACJ;AACAF,cAAc,CAACG,WAAW,GAAG,gBAAgB;;AAE7C;AACA,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}