{"ast": null, "code": "export { default } from \"./InputLabel.js\";\nexport { default as inputLabelClasses } from \"./inputLabelClasses.js\";\nexport * from \"./inputLabelClasses.js\";", "map": {"version": 3, "names": ["default", "inputLabelClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/InputLabel/index.js"], "sourcesContent": ["export { default } from \"./InputLabel.js\";\nexport { default as inputLabelClasses } from \"./inputLabelClasses.js\";\nexport * from \"./inputLabelClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,wBAAwB;AACrE,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}