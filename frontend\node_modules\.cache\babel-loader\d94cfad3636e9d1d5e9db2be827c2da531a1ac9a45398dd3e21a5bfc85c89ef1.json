{"ast": null, "code": "export { default } from \"./CircularProgress.js\";\nexport { default as circularProgressClasses } from \"./circularProgressClasses.js\";\nexport * from \"./circularProgressClasses.js\";", "map": {"version": 3, "names": ["default", "circularProgressClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/CircularProgress/index.js"], "sourcesContent": ["export { default } from \"./CircularProgress.js\";\nexport { default as circularProgressClasses } from \"./circularProgressClasses.js\";\nexport * from \"./circularProgressClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASA,OAAO,IAAIC,uBAAuB,QAAQ,8BAA8B;AACjF,cAAc,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}