{"ast": null, "code": "import React from'react';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>}from'react-bootstrap';import{<PERSON>}from'react-router-dom';import{Helmet}from'react-helmet-async';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const NotFound=()=>{return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"Page Not Found - 404\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:\"The page you're looking for doesn't exist or has been moved.\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"robots\",content:\"noindex, nofollow\"})]}),/*#__PURE__*/_jsx(Container,{className:\"py-5\",children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{lg:8,xl:6,children:/*#__PURE__*/_jsx(Card,{className:\"border-0 shadow-sm text-center\",children:/*#__PURE__*/_jsxs(Card.Body,{className:\"p-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-inline-flex align-items-center justify-content-center rounded-circle bg-light\",style:{width:'120px',height:'120px'},children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-search text-muted\",style:{fontSize:'3rem'}})})}),/*#__PURE__*/_jsx(\"h1\",{className:\"display-4 fw-bold text-dark mb-3\",children:\"404\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"h4 text-muted mb-4\",children:\"Page Not Found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"lead text-muted mb-4\",children:\"Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex flex-column flex-sm-row gap-3 justify-content-center\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"btn btn-primary btn-lg px-4\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-home me-2\"}),\"Go Home\"]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-secondary\",size:\"lg\",className:\"px-4\",onClick:()=>window.history.back(),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-left me-2\"}),\"Go Back\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-5 pt-4 border-top\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"text-muted mb-3\",children:\"What can you do?\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"row g-3 text-start\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-md-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check-circle text-success me-2 mt-1\"}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"Check the URL for typos\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check-circle text-success me-2 mt-1\"}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"Use the search function\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check-circle text-success me-2 mt-1\"}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"Visit our homepage\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check-circle text-success me-2 mt-1\"}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"Contact support if needed\"})]})})]})]})]})})})})})]});};export default NotFound;", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "<PERSON><PERSON>", "Card", "Link", "<PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "NotFound", "children", "name", "content", "className", "lg", "xl", "Body", "style", "width", "height", "fontSize", "to", "variant", "size", "onClick", "window", "history", "back"], "sources": ["C:/laragon/www/frontend/src/pages/NotFound.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';\nimport { <PERSON> } from 'react-router-dom';\nimport { Helmet } from 'react-helmet-async';\n\nconst NotFound: React.FC = () => {\n  return (\n    <>\n      <Helmet>\n        <title>Page Not Found - 404</title>\n        <meta name=\"description\" content=\"The page you're looking for doesn't exist or has been moved.\" />\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\n      </Helmet>\n\n      <Container className=\"py-5\">\n        <Row className=\"justify-content-center\">\n          <Col lg={8} xl={6}>\n            <Card className=\"border-0 shadow-sm text-center\">\n              <Card.Body className=\"p-5\">\n                {/* 404 Icon */}\n                <div className=\"mb-4\">\n                  <div \n                    className=\"d-inline-flex align-items-center justify-content-center rounded-circle bg-light\"\n                    style={{ width: '120px', height: '120px' }}\n                  >\n                    <i className=\"fas fa-search text-muted\" style={{ fontSize: '3rem' }}></i>\n                  </div>\n                </div>\n\n                {/* Error Message */}\n                <h1 className=\"display-4 fw-bold text-dark mb-3\">404</h1>\n                <h2 className=\"h4 text-muted mb-4\">Page Not Found</h2>\n                \n                <p className=\"lead text-muted mb-4\">\n                  Sorry, we couldn't find the page you're looking for. \n                  It might have been moved, deleted, or you entered the wrong URL.\n                </p>\n\n                {/* Action Buttons */}\n                <div className=\"d-flex flex-column flex-sm-row gap-3 justify-content-center\">\n                  <Link\n                    to=\"/\"\n                    className=\"btn btn-primary btn-lg px-4\"\n                  >\n                    <i className=\"fas fa-home me-2\"></i>\n                    Go Home\n                  </Link>\n                  \n                  <Button \n                    variant=\"outline-secondary\" \n                    size=\"lg\"\n                    className=\"px-4\"\n                    onClick={() => window.history.back()}\n                  >\n                    <i className=\"fas fa-arrow-left me-2\"></i>\n                    Go Back\n                  </Button>\n                </div>\n\n                {/* Additional Help */}\n                <div className=\"mt-5 pt-4 border-top\">\n                  <h6 className=\"text-muted mb-3\">What can you do?</h6>\n                  <div className=\"row g-3 text-start\">\n                    <div className=\"col-md-6\">\n                      <div className=\"d-flex\">\n                        <i className=\"fas fa-check-circle text-success me-2 mt-1\"></i>\n                        <small className=\"text-muted\">\n                          Check the URL for typos\n                        </small>\n                      </div>\n                    </div>\n                    <div className=\"col-md-6\">\n                      <div className=\"d-flex\">\n                        <i className=\"fas fa-check-circle text-success me-2 mt-1\"></i>\n                        <small className=\"text-muted\">\n                          Use the search function\n                        </small>\n                      </div>\n                    </div>\n                    <div className=\"col-md-6\">\n                      <div className=\"d-flex\">\n                        <i className=\"fas fa-check-circle text-success me-2 mt-1\"></i>\n                        <small className=\"text-muted\">\n                          Visit our homepage\n                        </small>\n                      </div>\n                    </div>\n                    <div className=\"col-md-6\">\n                      <div className=\"d-flex\">\n                        <i className=\"fas fa-check-circle text-success me-2 mt-1\"></i>\n                        <small className=\"text-muted\">\n                          Contact support if needed\n                        </small>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      </Container>\n    </>\n  );\n};\n\nexport default NotFound;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,MAAM,CAAEC,IAAI,KAAQ,iBAAiB,CACnE,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,MAAM,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE5C,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC/B,mBACEH,KAAA,CAAAE,SAAA,EAAAE,QAAA,eACEJ,KAAA,CAACJ,MAAM,EAAAQ,QAAA,eACLN,IAAA,UAAAM,QAAA,CAAO,sBAAoB,CAAO,CAAC,cACnCN,IAAA,SAAMO,IAAI,CAAC,aAAa,CAACC,OAAO,CAAC,8DAA8D,CAAE,CAAC,cAClGR,IAAA,SAAMO,IAAI,CAAC,QAAQ,CAACC,OAAO,CAAC,mBAAmB,CAAE,CAAC,EAC5C,CAAC,cAETR,IAAA,CAACR,SAAS,EAACiB,SAAS,CAAC,MAAM,CAAAH,QAAA,cACzBN,IAAA,CAACP,GAAG,EAACgB,SAAS,CAAC,wBAAwB,CAAAH,QAAA,cACrCN,IAAA,CAACN,GAAG,EAACgB,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cAChBN,IAAA,CAACJ,IAAI,EAACa,SAAS,CAAC,gCAAgC,CAAAH,QAAA,cAC9CJ,KAAA,CAACN,IAAI,CAACgB,IAAI,EAACH,SAAS,CAAC,KAAK,CAAAH,QAAA,eAExBN,IAAA,QAAKS,SAAS,CAAC,MAAM,CAAAH,QAAA,cACnBN,IAAA,QACES,SAAS,CAAC,iFAAiF,CAC3FI,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAO,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAAAT,QAAA,cAE3CN,IAAA,MAAGS,SAAS,CAAC,0BAA0B,CAACI,KAAK,CAAE,CAAEG,QAAQ,CAAE,MAAO,CAAE,CAAI,CAAC,CACtE,CAAC,CACH,CAAC,cAGNhB,IAAA,OAAIS,SAAS,CAAC,kCAAkC,CAAAH,QAAA,CAAC,KAAG,CAAI,CAAC,cACzDN,IAAA,OAAIS,SAAS,CAAC,oBAAoB,CAAAH,QAAA,CAAC,gBAAc,CAAI,CAAC,cAEtDN,IAAA,MAAGS,SAAS,CAAC,sBAAsB,CAAAH,QAAA,CAAC,uHAGpC,CAAG,CAAC,cAGJJ,KAAA,QAAKO,SAAS,CAAC,6DAA6D,CAAAH,QAAA,eAC1EJ,KAAA,CAACL,IAAI,EACHoB,EAAE,CAAC,GAAG,CACNR,SAAS,CAAC,6BAA6B,CAAAH,QAAA,eAEvCN,IAAA,MAAGS,SAAS,CAAC,kBAAkB,CAAI,CAAC,UAEtC,EAAM,CAAC,cAEPP,KAAA,CAACP,MAAM,EACLuB,OAAO,CAAC,mBAAmB,CAC3BC,IAAI,CAAC,IAAI,CACTV,SAAS,CAAC,MAAM,CAChBW,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE,CAAAjB,QAAA,eAErCN,IAAA,MAAGS,SAAS,CAAC,wBAAwB,CAAI,CAAC,UAE5C,EAAQ,CAAC,EACN,CAAC,cAGNP,KAAA,QAAKO,SAAS,CAAC,sBAAsB,CAAAH,QAAA,eACnCN,IAAA,OAAIS,SAAS,CAAC,iBAAiB,CAAAH,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACrDJ,KAAA,QAAKO,SAAS,CAAC,oBAAoB,CAAAH,QAAA,eACjCN,IAAA,QAAKS,SAAS,CAAC,UAAU,CAAAH,QAAA,cACvBJ,KAAA,QAAKO,SAAS,CAAC,QAAQ,CAAAH,QAAA,eACrBN,IAAA,MAAGS,SAAS,CAAC,4CAA4C,CAAI,CAAC,cAC9DT,IAAA,UAAOS,SAAS,CAAC,YAAY,CAAAH,QAAA,CAAC,yBAE9B,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACNN,IAAA,QAAKS,SAAS,CAAC,UAAU,CAAAH,QAAA,cACvBJ,KAAA,QAAKO,SAAS,CAAC,QAAQ,CAAAH,QAAA,eACrBN,IAAA,MAAGS,SAAS,CAAC,4CAA4C,CAAI,CAAC,cAC9DT,IAAA,UAAOS,SAAS,CAAC,YAAY,CAAAH,QAAA,CAAC,yBAE9B,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACNN,IAAA,QAAKS,SAAS,CAAC,UAAU,CAAAH,QAAA,cACvBJ,KAAA,QAAKO,SAAS,CAAC,QAAQ,CAAAH,QAAA,eACrBN,IAAA,MAAGS,SAAS,CAAC,4CAA4C,CAAI,CAAC,cAC9DT,IAAA,UAAOS,SAAS,CAAC,YAAY,CAAAH,QAAA,CAAC,oBAE9B,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACNN,IAAA,QAAKS,SAAS,CAAC,UAAU,CAAAH,QAAA,cACvBJ,KAAA,QAAKO,SAAS,CAAC,QAAQ,CAAAH,QAAA,eACrBN,IAAA,MAAGS,SAAS,CAAC,4CAA4C,CAAI,CAAC,cAC9DT,IAAA,UAAOS,SAAS,CAAC,YAAY,CAAAH,QAAA,CAAC,2BAE9B,CAAO,CAAC,EACL,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,EACG,CAAC,CACR,CAAC,CACJ,CAAC,CACH,CAAC,CACG,CAAC,EACZ,CAAC,CAEP,CAAC,CAED,cAAe,CAAAD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}