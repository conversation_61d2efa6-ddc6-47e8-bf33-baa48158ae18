{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\puck\\\\PuckRenderer.tsx\";\nimport React from 'react';\nimport { Container } from 'react-bootstrap';\nimport './PuckRenderer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PuckRenderer = ({\n  data,\n  renderedContent,\n  className = ''\n}) => {\n  // If we have pre-rendered content from the backend, use it\n  if (renderedContent) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `puck-renderer ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        dangerouslySetInnerHTML: {\n          __html: renderedContent\n        },\n        style: {\n          lineHeight: 1.6,\n          fontFamily: \"'Figtree', sans-serif\",\n          color: '#333',\n          backgroundColor: '#fff'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If we have Puck data but no rendered content, try to render basic components\n  if (data && data.content) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `puck-renderer ${className}`,\n      children: data.content.map((component, index) => /*#__PURE__*/_jsxDEV(PuckComponent, {\n        component: component\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Fallback if no content is available\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: `py-5 text-center ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Welcome\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"lead\",\n      children: \"Homepage content is being loaded...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n\n// Basic Puck component renderer\n_c = PuckRenderer;\nconst PuckComponent = ({\n  component\n}) => {\n  const {\n    type,\n    props\n  } = component;\n  switch (type) {\n    case 'Hero':\n      return /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"hero\",\n        style: {\n          minHeight: props.minHeight || '400px',\n          textAlign: props.textAlign || 'center',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          flexDirection: 'column',\n          padding: '60px 20px',\n          backgroundColor: '#f8f9fa',\n          borderRadius: '8px',\n          margin: '20px 0'\n        },\n        children: [props.title && /*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: '3rem',\n            fontWeight: 600,\n            margin: '0 0 20px 0',\n            color: '#1f2937'\n          },\n          children: props.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this), props.subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '1.25rem',\n            margin: '0 0 30px 0',\n            color: '#6b7280'\n          },\n          children: props.subtitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), props.primaryButton && /*#__PURE__*/_jsxDEV(\"a\", {\n          href: props.primaryButton.href || '#',\n          className: \"btn btn-primary\",\n          style: {\n            display: 'inline-block',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            textDecoration: 'none',\n            fontWeight: 500,\n            backgroundColor: '#3b82f6',\n            color: 'white'\n          },\n          children: props.primaryButton.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this);\n    case 'Text':\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-component\",\n        style: {\n          textAlign: props.textAlign || 'left',\n          margin: '20px 0'\n        },\n        dangerouslySetInnerHTML: {\n          __html: props.content || ''\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this);\n    case 'Container':\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-component\",\n        style: {\n          maxWidth: props.maxWidth || '1200px',\n          padding: props.padding || '20px',\n          margin: '0 auto',\n          backgroundColor: props.backgroundColor || 'transparent'\n        },\n        children: props.children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this);\n    default:\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"unknown-component\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Unknown component type: \", type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this);\n  }\n};\n_c2 = PuckComponent;\nexport default PuckRenderer;\nvar _c, _c2;\n$RefreshReg$(_c, \"PuckRenderer\");\n$RefreshReg$(_c2, \"PuckComponent\");", "map": {"version": 3, "names": ["React", "Container", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "renderedContent", "className", "children", "dangerouslySetInnerHTML", "__html", "style", "lineHeight", "fontFamily", "color", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "map", "component", "index", "PuckComponent", "_c", "type", "props", "minHeight", "textAlign", "display", "alignItems", "justifyContent", "flexDirection", "padding", "borderRadius", "margin", "title", "fontSize", "fontWeight", "subtitle", "primaryButton", "href", "textDecoration", "text", "max<PERSON><PERSON><PERSON>", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/puck/PuckRenderer.tsx"], "sourcesContent": ["import React from 'react';\nimport { Container } from 'react-bootstrap';\nimport './PuckRenderer.css';\n\ninterface PuckRendererProps {\n  data?: any;\n  renderedContent?: string;\n  className?: string;\n}\n\nconst PuckRenderer: React.FC<PuckRendererProps> = ({ \n  data, \n  renderedContent, \n  className = '' \n}) => {\n  // If we have pre-rendered content from the backend, use it\n  if (renderedContent) {\n    return (\n      <div className={`puck-renderer ${className}`}>\n        <div\n          dangerouslySetInnerHTML={{ __html: renderedContent }}\n          style={{\n            lineHeight: 1.6,\n            fontFamily: \"'Figtree', sans-serif\",\n            color: '#333',\n            backgroundColor: '#fff',\n          }}\n        />\n      </div>\n    );\n  }\n\n  // If we have Puck data but no rendered content, try to render basic components\n  if (data && data.content) {\n    return (\n      <div className={`puck-renderer ${className}`}>\n        {data.content.map((component: any, index: number) => (\n          <PuckComponent key={index} component={component} />\n        ))}\n      </div>\n    );\n  }\n\n  // Fallback if no content is available\n  return (\n    <Container className={`py-5 text-center ${className}`}>\n      <h2>Welcome</h2>\n      <p className=\"lead\">Homepage content is being loaded...</p>\n    </Container>\n  );\n};\n\n// Basic Puck component renderer\nconst PuckComponent: React.FC<{ component: any }> = ({ component }) => {\n  const { type, props } = component;\n\n  switch (type) {\n    case 'Hero':\n      return (\n        <section \n          className=\"hero\" \n          style={{\n            minHeight: props.minHeight || '400px',\n            textAlign: props.textAlign || 'center',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            flexDirection: 'column',\n            padding: '60px 20px',\n            backgroundColor: '#f8f9fa',\n            borderRadius: '8px',\n            margin: '20px 0',\n          }}\n        >\n          {props.title && (\n            <h1 style={{\n              fontSize: '3rem',\n              fontWeight: 600,\n              margin: '0 0 20px 0',\n              color: '#1f2937',\n            }}>\n              {props.title}\n            </h1>\n          )}\n          {props.subtitle && (\n            <p style={{\n              fontSize: '1.25rem',\n              margin: '0 0 30px 0',\n              color: '#6b7280',\n            }}>\n              {props.subtitle}\n            </p>\n          )}\n          {props.primaryButton && (\n            <a \n              href={props.primaryButton.href || '#'}\n              className=\"btn btn-primary\"\n              style={{\n                display: 'inline-block',\n                padding: '12px 24px',\n                borderRadius: '6px',\n                textDecoration: 'none',\n                fontWeight: 500,\n                backgroundColor: '#3b82f6',\n                color: 'white',\n              }}\n            >\n              {props.primaryButton.text}\n            </a>\n          )}\n        </section>\n      );\n\n    case 'Text':\n      return (\n        <div \n          className=\"text-component\" \n          style={{\n            textAlign: props.textAlign || 'left',\n            margin: '20px 0',\n          }}\n          dangerouslySetInnerHTML={{ __html: props.content || '' }}\n        />\n      );\n\n    case 'Container':\n      return (\n        <div \n          className=\"container-component\" \n          style={{\n            maxWidth: props.maxWidth || '1200px',\n            padding: props.padding || '20px',\n            margin: '0 auto',\n            backgroundColor: props.backgroundColor || 'transparent',\n          }}\n        >\n          {props.children}\n        </div>\n      );\n\n    default:\n      return (\n        <div className=\"unknown-component\">\n          <p>Unknown component type: {type}</p>\n        </div>\n      );\n  }\n};\n\nexport default PuckRenderer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ5B,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,IAAI;EACJC,eAAe;EACfC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ;EACA,IAAID,eAAe,EAAE;IACnB,oBACEH,OAAA;MAAKI,SAAS,EAAE,iBAAiBA,SAAS,EAAG;MAAAC,QAAA,eAC3CL,OAAA;QACEM,uBAAuB,EAAE;UAAEC,MAAM,EAAEJ;QAAgB,CAAE;QACrDK,KAAK,EAAE;UACLC,UAAU,EAAE,GAAG;UACfC,UAAU,EAAE,uBAAuB;UACnCC,KAAK,EAAE,MAAM;UACbC,eAAe,EAAE;QACnB;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;;EAEA;EACA,IAAId,IAAI,IAAIA,IAAI,CAACe,OAAO,EAAE;IACxB,oBACEjB,OAAA;MAAKI,SAAS,EAAE,iBAAiBA,SAAS,EAAG;MAAAC,QAAA,EAC1CH,IAAI,CAACe,OAAO,CAACC,GAAG,CAAC,CAACC,SAAc,EAAEC,KAAa,kBAC9CpB,OAAA,CAACqB,aAAa;QAAaF,SAAS,EAAEA;MAAU,GAA5BC,KAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAyB,CACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;;EAEA;EACA,oBACEhB,OAAA,CAACF,SAAS;IAACM,SAAS,EAAE,oBAAoBA,SAAS,EAAG;IAAAC,QAAA,gBACpDL,OAAA;MAAAK,QAAA,EAAI;IAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChBhB,OAAA;MAAGI,SAAS,EAAC,MAAM;MAAAC,QAAA,EAAC;IAAmC;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClD,CAAC;AAEhB,CAAC;;AAED;AAAAM,EAAA,GA1CMrB,YAAyC;AA2C/C,MAAMoB,aAA2C,GAAGA,CAAC;EAAEF;AAAU,CAAC,KAAK;EACrE,MAAM;IAAEI,IAAI;IAAEC;EAAM,CAAC,GAAGL,SAAS;EAEjC,QAAQI,IAAI;IACV,KAAK,MAAM;MACT,oBACEvB,OAAA;QACEI,SAAS,EAAC,MAAM;QAChBI,KAAK,EAAE;UACLiB,SAAS,EAAED,KAAK,CAACC,SAAS,IAAI,OAAO;UACrCC,SAAS,EAAEF,KAAK,CAACE,SAAS,IAAI,QAAQ;UACtCC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,aAAa,EAAE,QAAQ;UACvBC,OAAO,EAAE,WAAW;UACpBnB,eAAe,EAAE,SAAS;UAC1BoB,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAA5B,QAAA,GAEDmB,KAAK,CAACU,KAAK,iBACVlC,OAAA;UAAIQ,KAAK,EAAE;YACT2B,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfH,MAAM,EAAE,YAAY;YACpBtB,KAAK,EAAE;UACT,CAAE;UAAAN,QAAA,EACCmB,KAAK,CAACU;QAAK;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACL,EACAQ,KAAK,CAACa,QAAQ,iBACbrC,OAAA;UAAGQ,KAAK,EAAE;YACR2B,QAAQ,EAAE,SAAS;YACnBF,MAAM,EAAE,YAAY;YACpBtB,KAAK,EAAE;UACT,CAAE;UAAAN,QAAA,EACCmB,KAAK,CAACa;QAAQ;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACJ,EACAQ,KAAK,CAACc,aAAa,iBAClBtC,OAAA;UACEuC,IAAI,EAAEf,KAAK,CAACc,aAAa,CAACC,IAAI,IAAI,GAAI;UACtCnC,SAAS,EAAC,iBAAiB;UAC3BI,KAAK,EAAE;YACLmB,OAAO,EAAE,cAAc;YACvBI,OAAO,EAAE,WAAW;YACpBC,YAAY,EAAE,KAAK;YACnBQ,cAAc,EAAE,MAAM;YACtBJ,UAAU,EAAE,GAAG;YACfxB,eAAe,EAAE,SAAS;YAC1BD,KAAK,EAAE;UACT,CAAE;UAAAN,QAAA,EAEDmB,KAAK,CAACc,aAAa,CAACG;QAAI;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAGd,KAAK,MAAM;MACT,oBACEhB,OAAA;QACEI,SAAS,EAAC,gBAAgB;QAC1BI,KAAK,EAAE;UACLkB,SAAS,EAAEF,KAAK,CAACE,SAAS,IAAI,MAAM;UACpCO,MAAM,EAAE;QACV,CAAE;QACF3B,uBAAuB,EAAE;UAAEC,MAAM,EAAEiB,KAAK,CAACP,OAAO,IAAI;QAAG;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAGN,KAAK,WAAW;MACd,oBACEhB,OAAA;QACEI,SAAS,EAAC,qBAAqB;QAC/BI,KAAK,EAAE;UACLkC,QAAQ,EAAElB,KAAK,CAACkB,QAAQ,IAAI,QAAQ;UACpCX,OAAO,EAAEP,KAAK,CAACO,OAAO,IAAI,MAAM;UAChCE,MAAM,EAAE,QAAQ;UAChBrB,eAAe,EAAEY,KAAK,CAACZ,eAAe,IAAI;QAC5C,CAAE;QAAAP,QAAA,EAEDmB,KAAK,CAACnB;MAAQ;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAGV;MACE,oBACEhB,OAAA;QAAKI,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCL,OAAA;UAAAK,QAAA,GAAG,0BAAwB,EAACkB,IAAI;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;EAEZ;AACF,CAAC;AAAC2B,GAAA,GA9FItB,aAA2C;AAgGjD,eAAepB,YAAY;AAAC,IAAAqB,EAAA,EAAAqB,GAAA;AAAAC,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}