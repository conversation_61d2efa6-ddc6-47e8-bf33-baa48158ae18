{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport MenuList from \"../MenuList/index.js\";\nimport Popover, { PopoverPaper } from \"../Popover/index.js\";\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getMenuUtilityClass } from \"./menuClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RTL_ORIGIN = {\n  vertical: 'top',\n  horizontal: 'right'\n};\nconst LTR_ORIGIN = {\n  vertical: 'top',\n  horizontal: 'left'\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper'],\n    list: ['list']\n  };\n  return composeClasses(slots, getMenuUtilityClass, classes);\n};\nconst MenuRoot = styled(Popover, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiMenu',\n  slot: 'Root'\n})({});\nexport const MenuPaper = styled(PopoverPaper, {\n  name: 'MuiMenu',\n  slot: 'Paper'\n})({\n  // specZ: The maximum height of a simple menu should be one or more rows less than the view\n  // height. This ensures a tappable area outside of the simple menu with which to dismiss\n  // the menu.\n  maxHeight: 'calc(100% - 96px)',\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch'\n});\nconst MenuMenuList = styled(MenuList, {\n  name: 'MuiMenu',\n  slot: 'List'\n})({\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n});\nconst Menu = /*#__PURE__*/React.forwardRef(function Menu(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiMenu'\n  });\n  const {\n    autoFocus = true,\n    children,\n    className,\n    disableAutoFocusItem = false,\n    MenuListProps = {},\n    onClose,\n    open,\n    PaperProps = {},\n    PopoverClasses,\n    transitionDuration = 'auto',\n    TransitionProps: {\n      onEntering,\n      ...TransitionProps\n    } = {},\n    variant = 'selectedMenu',\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const isRtl = useRtl();\n  const ownerState = {\n    ...props,\n    autoFocus,\n    disableAutoFocusItem,\n    MenuListProps,\n    onEntering,\n    PaperProps,\n    transitionDuration,\n    TransitionProps,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const autoFocusItem = autoFocus && !disableAutoFocusItem && open;\n  const menuListActionsRef = React.useRef(null);\n  const handleEntering = (element, isAppearing) => {\n    if (menuListActionsRef.current) {\n      menuListActionsRef.current.adjustStyleForScrollbar(element, {\n        direction: isRtl ? 'rtl' : 'ltr'\n      });\n    }\n    if (onEntering) {\n      onEntering(element, isAppearing);\n    }\n  };\n  const handleListKeyDown = event => {\n    if (event.key === 'Tab') {\n      event.preventDefault();\n      if (onClose) {\n        onClose(event, 'tabKeyDown');\n      }\n    }\n  };\n\n  /**\n   * the index of the item should receive focus\n   * in a `variant=\"selectedMenu\"` it's the first `selected` item\n   * otherwise it's the very first item.\n   */\n  let activeItemIndex = -1;\n  // since we inject focus related props into children we have to do a lookahead\n  // to check if there is a `selected` item. We're looking for the last `selected`\n  // item and use the first valid item as a fallback\n  React.Children.map(children, (child, index) => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Menu component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    if (!child.props.disabled) {\n      if (variant === 'selectedMenu' && child.props.selected) {\n        activeItemIndex = index;\n      } else if (activeItemIndex === -1) {\n        activeItemIndex = index;\n      }\n    }\n  });\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      list: MenuListProps,\n      transition: TransitionProps,\n      paper: PaperProps,\n      ...slotProps\n    }\n  };\n  const rootSlotProps = useSlotProps({\n    elementType: slots.root,\n    externalSlotProps: slotProps.root,\n    ownerState,\n    className: [classes.root, className]\n  });\n  const [PaperSlot, paperSlotProps] = useSlot('paper', {\n    className: classes.paper,\n    elementType: MenuPaper,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    ownerState\n  });\n  const [ListSlot, listSlotProps] = useSlot('list', {\n    className: clsx(classes.list, MenuListProps.className),\n    elementType: MenuMenuList,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onKeyDown: event => {\n        handleListKeyDown(event);\n        handlers.onKeyDown?.(event);\n      }\n    }),\n    ownerState\n  });\n  const resolvedTransitionProps = typeof externalForwardedProps.slotProps.transition === 'function' ? externalForwardedProps.slotProps.transition(ownerState) : externalForwardedProps.slotProps.transition;\n  return /*#__PURE__*/_jsx(MenuRoot, {\n    onClose: onClose,\n    anchorOrigin: {\n      vertical: 'bottom',\n      horizontal: isRtl ? 'right' : 'left'\n    },\n    transformOrigin: isRtl ? RTL_ORIGIN : LTR_ORIGIN,\n    slots: {\n      root: slots.root,\n      paper: PaperSlot,\n      backdrop: slots.backdrop,\n      ...(slots.transition && {\n        // TODO: pass `slots.transition` directly once `TransitionComponent` is removed from Popover\n        transition: slots.transition\n      })\n    },\n    slotProps: {\n      root: rootSlotProps,\n      paper: paperSlotProps,\n      backdrop: typeof slotProps.backdrop === 'function' ? slotProps.backdrop(ownerState) : slotProps.backdrop,\n      transition: {\n        ...resolvedTransitionProps,\n        onEntering: function () {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          handleEntering(...args);\n          resolvedTransitionProps?.onEntering?.(...args);\n        }\n      }\n    },\n    open: open,\n    ref: ref,\n    transitionDuration: transitionDuration,\n    ownerState: ownerState,\n    ...other,\n    classes: PopoverClasses,\n    children: /*#__PURE__*/_jsx(ListSlot, {\n      actions: menuListActionsRef,\n      autoFocus: autoFocus && (activeItemIndex === -1 || disableAutoFocusItem),\n      autoFocusItem: autoFocusItem,\n      variant: variant,\n      ...listSlotProps,\n      children: children\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Menu.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * An HTML element, or a function that returns one.\n   * It's used to set the position of the menu.\n   */\n  anchorEl: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true` (Default) will focus the `[role=\"menu\"]` if no focusable child is found. Disabled\n   * children are not focusable. If you set this prop to `false` focus will be placed\n   * on the parent modal container. This has severe accessibility implications\n   * and should only be considered if you manage focus otherwise.\n   * @default true\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Menu contents, normally `MenuItem`s.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When opening the menu will not focus the active item but the `[role=\"menu\"]`\n   * unless `autoFocus` is also set to `false`. Not using the default means not\n   * following WAI-ARIA authoring practices. Please be considerate about possible\n   * accessibility implications.\n   * @default false\n   */\n  disableAutoFocusItem: PropTypes.bool,\n  /**\n   * Props applied to the [`MenuList`](https://mui.com/material-ui/api/menu-list/) element.\n   * @deprecated use the `slotProps.list` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  MenuListProps: PropTypes.object,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`, `\"tabKeyDown\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes.object,\n  /**\n   * `classes` prop applied to the [`Popover`](https://mui.com/material-ui/api/popover/) element.\n   */\n  PopoverClasses: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    list: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    list: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The length of the transition in `ms`, or 'auto'\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated use the `slotProps.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object,\n  /**\n   * The variant to use. Use `menu` to prevent selected items from impacting the initial focus.\n   * @default 'selectedMenu'\n   */\n  variant: PropTypes.oneOf(['menu', 'selectedMenu'])\n} : void 0;\nexport default Menu;", "map": {"version": 3, "names": ["React", "isFragment", "PropTypes", "clsx", "composeClasses", "HTMLElementType", "useRtl", "useSlotProps", "MenuList", "Popover", "PopoverPaper", "rootShouldForwardProp", "styled", "useDefaultProps", "getMenuUtilityClass", "useSlot", "jsx", "_jsx", "RTL_ORIGIN", "vertical", "horizontal", "LTR_ORIGIN", "useUtilityClasses", "ownerState", "classes", "slots", "root", "paper", "list", "MenuRoot", "shouldForwardProp", "prop", "name", "slot", "MenuPaper", "maxHeight", "WebkitOverflowScrolling", "MenuMenuList", "outline", "<PERSON><PERSON>", "forwardRef", "inProps", "ref", "props", "autoFocus", "children", "className", "disableAutoFocusItem", "MenuListProps", "onClose", "open", "PaperProps", "PopoverClasses", "transitionDuration", "TransitionProps", "onEntering", "variant", "slotProps", "other", "isRtl", "autoFocusItem", "menuListActionsRef", "useRef", "handleEntering", "element", "isAppearing", "current", "adjustStyleForScrollbar", "direction", "handleListKeyDown", "event", "key", "preventDefault", "activeItemIndex", "Children", "map", "child", "index", "isValidElement", "process", "env", "NODE_ENV", "console", "error", "join", "disabled", "selected", "externalForwardedProps", "transition", "rootSlotProps", "elementType", "externalSlotProps", "PaperSlot", "paperSlotProps", "shouldForwardComponentProp", "ListSlot", "listSlotProps", "getSlotProps", "handlers", "onKeyDown", "resolvedTransitionProps", "anchor<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "backdrop", "_len", "arguments", "length", "args", "Array", "_key", "actions", "propTypes", "anchorEl", "oneOfType", "func", "bool", "node", "object", "string", "isRequired", "shape", "sx", "arrayOf", "oneOf", "number", "appear", "enter", "exit"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Menu/Menu.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport MenuList from \"../MenuList/index.js\";\nimport Popover, { PopoverPaper } from \"../Popover/index.js\";\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getMenuUtilityClass } from \"./menuClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RTL_ORIGIN = {\n  vertical: 'top',\n  horizontal: 'right'\n};\nconst LTR_ORIGIN = {\n  vertical: 'top',\n  horizontal: 'left'\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper'],\n    list: ['list']\n  };\n  return composeClasses(slots, getMenuUtilityClass, classes);\n};\nconst MenuRoot = styled(Popover, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiMenu',\n  slot: 'Root'\n})({});\nexport const MenuPaper = styled(PopoverPaper, {\n  name: 'MuiMenu',\n  slot: 'Paper'\n})({\n  // specZ: The maximum height of a simple menu should be one or more rows less than the view\n  // height. This ensures a tappable area outside of the simple menu with which to dismiss\n  // the menu.\n  maxHeight: 'calc(100% - 96px)',\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch'\n});\nconst MenuMenuList = styled(MenuList, {\n  name: 'MuiMenu',\n  slot: 'List'\n})({\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n});\nconst Menu = /*#__PURE__*/React.forwardRef(function Menu(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiMenu'\n  });\n  const {\n    autoFocus = true,\n    children,\n    className,\n    disableAutoFocusItem = false,\n    MenuListProps = {},\n    onClose,\n    open,\n    PaperProps = {},\n    PopoverClasses,\n    transitionDuration = 'auto',\n    TransitionProps: {\n      onEntering,\n      ...TransitionProps\n    } = {},\n    variant = 'selectedMenu',\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const isRtl = useRtl();\n  const ownerState = {\n    ...props,\n    autoFocus,\n    disableAutoFocusItem,\n    MenuListProps,\n    onEntering,\n    PaperProps,\n    transitionDuration,\n    TransitionProps,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const autoFocusItem = autoFocus && !disableAutoFocusItem && open;\n  const menuListActionsRef = React.useRef(null);\n  const handleEntering = (element, isAppearing) => {\n    if (menuListActionsRef.current) {\n      menuListActionsRef.current.adjustStyleForScrollbar(element, {\n        direction: isRtl ? 'rtl' : 'ltr'\n      });\n    }\n    if (onEntering) {\n      onEntering(element, isAppearing);\n    }\n  };\n  const handleListKeyDown = event => {\n    if (event.key === 'Tab') {\n      event.preventDefault();\n      if (onClose) {\n        onClose(event, 'tabKeyDown');\n      }\n    }\n  };\n\n  /**\n   * the index of the item should receive focus\n   * in a `variant=\"selectedMenu\"` it's the first `selected` item\n   * otherwise it's the very first item.\n   */\n  let activeItemIndex = -1;\n  // since we inject focus related props into children we have to do a lookahead\n  // to check if there is a `selected` item. We're looking for the last `selected`\n  // item and use the first valid item as a fallback\n  React.Children.map(children, (child, index) => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Menu component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    if (!child.props.disabled) {\n      if (variant === 'selectedMenu' && child.props.selected) {\n        activeItemIndex = index;\n      } else if (activeItemIndex === -1) {\n        activeItemIndex = index;\n      }\n    }\n  });\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      list: MenuListProps,\n      transition: TransitionProps,\n      paper: PaperProps,\n      ...slotProps\n    }\n  };\n  const rootSlotProps = useSlotProps({\n    elementType: slots.root,\n    externalSlotProps: slotProps.root,\n    ownerState,\n    className: [classes.root, className]\n  });\n  const [PaperSlot, paperSlotProps] = useSlot('paper', {\n    className: classes.paper,\n    elementType: MenuPaper,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    ownerState\n  });\n  const [ListSlot, listSlotProps] = useSlot('list', {\n    className: clsx(classes.list, MenuListProps.className),\n    elementType: MenuMenuList,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onKeyDown: event => {\n        handleListKeyDown(event);\n        handlers.onKeyDown?.(event);\n      }\n    }),\n    ownerState\n  });\n  const resolvedTransitionProps = typeof externalForwardedProps.slotProps.transition === 'function' ? externalForwardedProps.slotProps.transition(ownerState) : externalForwardedProps.slotProps.transition;\n  return /*#__PURE__*/_jsx(MenuRoot, {\n    onClose: onClose,\n    anchorOrigin: {\n      vertical: 'bottom',\n      horizontal: isRtl ? 'right' : 'left'\n    },\n    transformOrigin: isRtl ? RTL_ORIGIN : LTR_ORIGIN,\n    slots: {\n      root: slots.root,\n      paper: PaperSlot,\n      backdrop: slots.backdrop,\n      ...(slots.transition && {\n        // TODO: pass `slots.transition` directly once `TransitionComponent` is removed from Popover\n        transition: slots.transition\n      })\n    },\n    slotProps: {\n      root: rootSlotProps,\n      paper: paperSlotProps,\n      backdrop: typeof slotProps.backdrop === 'function' ? slotProps.backdrop(ownerState) : slotProps.backdrop,\n      transition: {\n        ...resolvedTransitionProps,\n        onEntering: (...args) => {\n          handleEntering(...args);\n          resolvedTransitionProps?.onEntering?.(...args);\n        }\n      }\n    },\n    open: open,\n    ref: ref,\n    transitionDuration: transitionDuration,\n    ownerState: ownerState,\n    ...other,\n    classes: PopoverClasses,\n    children: /*#__PURE__*/_jsx(ListSlot, {\n      actions: menuListActionsRef,\n      autoFocus: autoFocus && (activeItemIndex === -1 || disableAutoFocusItem),\n      autoFocusItem: autoFocusItem,\n      variant: variant,\n      ...listSlotProps,\n      children: children\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Menu.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * An HTML element, or a function that returns one.\n   * It's used to set the position of the menu.\n   */\n  anchorEl: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true` (Default) will focus the `[role=\"menu\"]` if no focusable child is found. Disabled\n   * children are not focusable. If you set this prop to `false` focus will be placed\n   * on the parent modal container. This has severe accessibility implications\n   * and should only be considered if you manage focus otherwise.\n   * @default true\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Menu contents, normally `MenuItem`s.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When opening the menu will not focus the active item but the `[role=\"menu\"]`\n   * unless `autoFocus` is also set to `false`. Not using the default means not\n   * following WAI-ARIA authoring practices. Please be considerate about possible\n   * accessibility implications.\n   * @default false\n   */\n  disableAutoFocusItem: PropTypes.bool,\n  /**\n   * Props applied to the [`MenuList`](https://mui.com/material-ui/api/menu-list/) element.\n   * @deprecated use the `slotProps.list` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  MenuListProps: PropTypes.object,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`, `\"tabKeyDown\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes.object,\n  /**\n   * `classes` prop applied to the [`Popover`](https://mui.com/material-ui/api/popover/) element.\n   */\n  PopoverClasses: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    list: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    list: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The length of the transition in `ms`, or 'auto'\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated use the `slotProps.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object,\n  /**\n   * The variant to use. Use `menu` to prevent selected items from impacting the initial focus.\n   * @default 'selectedMenu'\n   */\n  variant: PropTypes.oneOf(['menu', 'selectedMenu'])\n} : void 0;\nexport default Menu;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,OAAO,IAAIC,YAAY,QAAQ,qBAAqB;AAC3D,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,mBAAmB,QAAQ,kBAAkB;AACtD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG;EACjBC,QAAQ,EAAE,KAAK;EACfC,UAAU,EAAE;AACd,CAAC;AACD,MAAMC,UAAU,GAAG;EACjBF,QAAQ,EAAE,KAAK;EACfC,UAAU,EAAE;AACd,CAAC;AACD,MAAME,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOxB,cAAc,CAACqB,KAAK,EAAEX,mBAAmB,EAAEU,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMK,QAAQ,GAAGjB,MAAM,CAACH,OAAO,EAAE;EAC/BqB,iBAAiB,EAAEC,IAAI,IAAIpB,qBAAqB,CAACoB,IAAI,CAAC,IAAIA,IAAI,KAAK,SAAS;EAC5EC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,OAAO,MAAMC,SAAS,GAAGtB,MAAM,CAACF,YAAY,EAAE;EAC5CsB,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACD;EACA;EACA;EACAE,SAAS,EAAE,mBAAmB;EAC9B;EACAC,uBAAuB,EAAE;AAC3B,CAAC,CAAC;AACF,MAAMC,YAAY,GAAGzB,MAAM,CAACJ,QAAQ,EAAE;EACpCwB,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACD;EACAK,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMC,IAAI,GAAG,aAAavC,KAAK,CAACwC,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAMC,KAAK,GAAG9B,eAAe,CAAC;IAC5B8B,KAAK,EAAEF,OAAO;IACdT,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJY,SAAS,GAAG,IAAI;IAChBC,QAAQ;IACRC,SAAS;IACTC,oBAAoB,GAAG,KAAK;IAC5BC,aAAa,GAAG,CAAC,CAAC;IAClBC,OAAO;IACPC,IAAI;IACJC,UAAU,GAAG,CAAC,CAAC;IACfC,cAAc;IACdC,kBAAkB,GAAG,MAAM;IAC3BC,eAAe,EAAE;MACfC,UAAU;MACV,GAAGD;IACL,CAAC,GAAG,CAAC,CAAC;IACNE,OAAO,GAAG,cAAc;IACxB/B,KAAK,GAAG,CAAC,CAAC;IACVgC,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAGf,KAAK;EACT,MAAMgB,KAAK,GAAGrD,MAAM,CAAC,CAAC;EACtB,MAAMiB,UAAU,GAAG;IACjB,GAAGoB,KAAK;IACRC,SAAS;IACTG,oBAAoB;IACpBC,aAAa;IACbO,UAAU;IACVJ,UAAU;IACVE,kBAAkB;IAClBC,eAAe;IACfE;EACF,CAAC;EACD,MAAMhC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMqC,aAAa,GAAGhB,SAAS,IAAI,CAACG,oBAAoB,IAAIG,IAAI;EAChE,MAAMW,kBAAkB,GAAG7D,KAAK,CAAC8D,MAAM,CAAC,IAAI,CAAC;EAC7C,MAAMC,cAAc,GAAGA,CAACC,OAAO,EAAEC,WAAW,KAAK;IAC/C,IAAIJ,kBAAkB,CAACK,OAAO,EAAE;MAC9BL,kBAAkB,CAACK,OAAO,CAACC,uBAAuB,CAACH,OAAO,EAAE;QAC1DI,SAAS,EAAET,KAAK,GAAG,KAAK,GAAG;MAC7B,CAAC,CAAC;IACJ;IACA,IAAIJ,UAAU,EAAE;MACdA,UAAU,CAACS,OAAO,EAAEC,WAAW,CAAC;IAClC;EACF,CAAC;EACD,MAAMI,iBAAiB,GAAGC,KAAK,IAAI;IACjC,IAAIA,KAAK,CAACC,GAAG,KAAK,KAAK,EAAE;MACvBD,KAAK,CAACE,cAAc,CAAC,CAAC;MACtB,IAAIvB,OAAO,EAAE;QACXA,OAAO,CAACqB,KAAK,EAAE,YAAY,CAAC;MAC9B;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,IAAIG,eAAe,GAAG,CAAC,CAAC;EACxB;EACA;EACA;EACAzE,KAAK,CAAC0E,QAAQ,CAACC,GAAG,CAAC9B,QAAQ,EAAE,CAAC+B,KAAK,EAAEC,KAAK,KAAK;IAC7C,IAAI,EAAE,aAAa7E,KAAK,CAAC8E,cAAc,CAACF,KAAK,CAAC,EAAE;MAC9C;IACF;IACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIhF,UAAU,CAAC2E,KAAK,CAAC,EAAE;QACrBM,OAAO,CAACC,KAAK,CAAC,CAAC,+DAA+D,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MACrI;IACF;IACA,IAAI,CAACR,KAAK,CAACjC,KAAK,CAAC0C,QAAQ,EAAE;MACzB,IAAI7B,OAAO,KAAK,cAAc,IAAIoB,KAAK,CAACjC,KAAK,CAAC2C,QAAQ,EAAE;QACtDb,eAAe,GAAGI,KAAK;MACzB,CAAC,MAAM,IAAIJ,eAAe,KAAK,CAAC,CAAC,EAAE;QACjCA,eAAe,GAAGI,KAAK;MACzB;IACF;EACF,CAAC,CAAC;EACF,MAAMU,sBAAsB,GAAG;IAC7B9D,KAAK;IACLgC,SAAS,EAAE;MACT7B,IAAI,EAAEoB,aAAa;MACnBwC,UAAU,EAAElC,eAAe;MAC3B3B,KAAK,EAAEwB,UAAU;MACjB,GAAGM;IACL;EACF,CAAC;EACD,MAAMgC,aAAa,GAAGlF,YAAY,CAAC;IACjCmF,WAAW,EAAEjE,KAAK,CAACC,IAAI;IACvBiE,iBAAiB,EAAElC,SAAS,CAAC/B,IAAI;IACjCH,UAAU;IACVuB,SAAS,EAAE,CAACtB,OAAO,CAACE,IAAI,EAAEoB,SAAS;EACrC,CAAC,CAAC;EACF,MAAM,CAAC8C,SAAS,EAAEC,cAAc,CAAC,GAAG9E,OAAO,CAAC,OAAO,EAAE;IACnD+B,SAAS,EAAEtB,OAAO,CAACG,KAAK;IACxB+D,WAAW,EAAExD,SAAS;IACtBqD,sBAAsB;IACtBO,0BAA0B,EAAE,IAAI;IAChCvE;EACF,CAAC,CAAC;EACF,MAAM,CAACwE,QAAQ,EAAEC,aAAa,CAAC,GAAGjF,OAAO,CAAC,MAAM,EAAE;IAChD+B,SAAS,EAAE3C,IAAI,CAACqB,OAAO,CAACI,IAAI,EAAEoB,aAAa,CAACF,SAAS,CAAC;IACtD4C,WAAW,EAAErD,YAAY;IACzByD,0BAA0B,EAAE,IAAI;IAChCP,sBAAsB;IACtBU,YAAY,EAAEC,QAAQ,KAAK;MACzB,GAAGA,QAAQ;MACXC,SAAS,EAAE7B,KAAK,IAAI;QAClBD,iBAAiB,CAACC,KAAK,CAAC;QACxB4B,QAAQ,CAACC,SAAS,GAAG7B,KAAK,CAAC;MAC7B;IACF,CAAC,CAAC;IACF/C;EACF,CAAC,CAAC;EACF,MAAM6E,uBAAuB,GAAG,OAAOb,sBAAsB,CAAC9B,SAAS,CAAC+B,UAAU,KAAK,UAAU,GAAGD,sBAAsB,CAAC9B,SAAS,CAAC+B,UAAU,CAACjE,UAAU,CAAC,GAAGgE,sBAAsB,CAAC9B,SAAS,CAAC+B,UAAU;EACzM,OAAO,aAAavE,IAAI,CAACY,QAAQ,EAAE;IACjCoB,OAAO,EAAEA,OAAO;IAChBoD,YAAY,EAAE;MACZlF,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAEuC,KAAK,GAAG,OAAO,GAAG;IAChC,CAAC;IACD2C,eAAe,EAAE3C,KAAK,GAAGzC,UAAU,GAAGG,UAAU;IAChDI,KAAK,EAAE;MACLC,IAAI,EAAED,KAAK,CAACC,IAAI;MAChBC,KAAK,EAAEiE,SAAS;MAChBW,QAAQ,EAAE9E,KAAK,CAAC8E,QAAQ;MACxB,IAAI9E,KAAK,CAAC+D,UAAU,IAAI;QACtB;QACAA,UAAU,EAAE/D,KAAK,CAAC+D;MACpB,CAAC;IACH,CAAC;IACD/B,SAAS,EAAE;MACT/B,IAAI,EAAE+D,aAAa;MACnB9D,KAAK,EAAEkE,cAAc;MACrBU,QAAQ,EAAE,OAAO9C,SAAS,CAAC8C,QAAQ,KAAK,UAAU,GAAG9C,SAAS,CAAC8C,QAAQ,CAAChF,UAAU,CAAC,GAAGkC,SAAS,CAAC8C,QAAQ;MACxGf,UAAU,EAAE;QACV,GAAGY,uBAAuB;QAC1B7C,UAAU,EAAE,SAAAA,CAAA,EAAa;UAAA,SAAAiD,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;YAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;UAAA;UAClB9C,cAAc,CAAC,GAAG4C,IAAI,CAAC;UACvBP,uBAAuB,EAAE7C,UAAU,GAAG,GAAGoD,IAAI,CAAC;QAChD;MACF;IACF,CAAC;IACDzD,IAAI,EAAEA,IAAI;IACVR,GAAG,EAAEA,GAAG;IACRW,kBAAkB,EAAEA,kBAAkB;IACtC9B,UAAU,EAAEA,UAAU;IACtB,GAAGmC,KAAK;IACRlC,OAAO,EAAE4B,cAAc;IACvBP,QAAQ,EAAE,aAAa5B,IAAI,CAAC8E,QAAQ,EAAE;MACpCe,OAAO,EAAEjD,kBAAkB;MAC3BjB,SAAS,EAAEA,SAAS,KAAK6B,eAAe,KAAK,CAAC,CAAC,IAAI1B,oBAAoB,CAAC;MACxEa,aAAa,EAAEA,aAAa;MAC5BJ,OAAO,EAAEA,OAAO;MAChB,GAAGwC,aAAa;MAChBnD,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFkC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1C,IAAI,CAACwE,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEC,QAAQ,EAAE9G,SAAS,CAAC,sCAAsC+G,SAAS,CAAC,CAAC5G,eAAe,EAAEH,SAAS,CAACgH,IAAI,CAAC,CAAC;EACtG;AACF;AACA;AACA;AACA;AACA;AACA;EACEtE,SAAS,EAAE1C,SAAS,CAACiH,IAAI;EACzB;AACF;AACA;EACEtE,QAAQ,EAAE3C,SAAS,CAACkH,IAAI;EACxB;AACF;AACA;EACE5F,OAAO,EAAEtB,SAAS,CAACmH,MAAM;EACzB;AACF;AACA;EACEvE,SAAS,EAAE5C,SAAS,CAACoH,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;EACEvE,oBAAoB,EAAE7C,SAAS,CAACiH,IAAI;EACpC;AACF;AACA;AACA;AACA;EACEnE,aAAa,EAAE9C,SAAS,CAACmH,MAAM;EAC/B;AACF;AACA;AACA;AACA;AACA;EACEpE,OAAO,EAAE/C,SAAS,CAACgH,IAAI;EACvB;AACF;AACA;EACEhE,IAAI,EAAEhD,SAAS,CAACiH,IAAI,CAACI,UAAU;EAC/B;AACF;AACA;EACEpE,UAAU,EAAEjD,SAAS,CAACmH,MAAM;EAC5B;AACF;AACA;EACEjE,cAAc,EAAElD,SAAS,CAACmH,MAAM;EAChC;AACF;AACA;AACA;EACE5D,SAAS,EAAEvD,SAAS,CAACsH,KAAK,CAAC;IACzBjB,QAAQ,EAAErG,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACgH,IAAI,EAAEhH,SAAS,CAACmH,MAAM,CAAC,CAAC;IACjEzF,IAAI,EAAE1B,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACgH,IAAI,EAAEhH,SAAS,CAACmH,MAAM,CAAC,CAAC;IAC7D1F,KAAK,EAAEzB,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACgH,IAAI,EAAEhH,SAAS,CAACmH,MAAM,CAAC,CAAC;IAC9D3F,IAAI,EAAExB,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACgH,IAAI,EAAEhH,SAAS,CAACmH,MAAM,CAAC,CAAC;IAC7D7B,UAAU,EAAEtF,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACgH,IAAI,EAAEhH,SAAS,CAACmH,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE5F,KAAK,EAAEvB,SAAS,CAACsH,KAAK,CAAC;IACrBjB,QAAQ,EAAErG,SAAS,CAACwF,WAAW;IAC/B9D,IAAI,EAAE1B,SAAS,CAACwF,WAAW;IAC3B/D,KAAK,EAAEzB,SAAS,CAACwF,WAAW;IAC5BhE,IAAI,EAAExB,SAAS,CAACwF,WAAW;IAC3BF,UAAU,EAAEtF,SAAS,CAACwF;EACxB,CAAC,CAAC;EACF;AACF;AACA;EACE+B,EAAE,EAAEvH,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACwH,OAAO,CAACxH,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACgH,IAAI,EAAEhH,SAAS,CAACmH,MAAM,EAAEnH,SAAS,CAACiH,IAAI,CAAC,CAAC,CAAC,EAAEjH,SAAS,CAACgH,IAAI,EAAEhH,SAAS,CAACmH,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEhE,kBAAkB,EAAEnD,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACyH,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAEzH,SAAS,CAAC0H,MAAM,EAAE1H,SAAS,CAACsH,KAAK,CAAC;IACpGK,MAAM,EAAE3H,SAAS,CAAC0H,MAAM;IACxBE,KAAK,EAAE5H,SAAS,CAAC0H,MAAM;IACvBG,IAAI,EAAE7H,SAAS,CAAC0H;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;AACA;EACEtE,eAAe,EAAEpD,SAAS,CAACmH,MAAM;EACjC;AACF;AACA;AACA;EACE7D,OAAO,EAAEtD,SAAS,CAACyH,KAAK,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC;AACnD,CAAC,GAAG,KAAK,CAAC;AACV,eAAepF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}