{"ast": null, "code": "export { default } from \"./BottomNavigationAction.js\";\nexport { default as bottomNavigationActionClasses } from \"./bottomNavigationActionClasses.js\";\nexport * from \"./bottomNavigationActionClasses.js\";", "map": {"version": 3, "names": ["default", "bottomNavigationActionClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/BottomNavigationAction/index.js"], "sourcesContent": ["export { default } from \"./BottomNavigationAction.js\";\nexport { default as bottomNavigationActionClasses } from \"./bottomNavigationActionClasses.js\";\nexport * from \"./bottomNavigationActionClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,6BAA6B;AACrD,SAASA,OAAO,IAAIC,6BAA6B,QAAQ,oCAAoC;AAC7F,cAAc,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}