{"ast": null, "code": "export { default } from \"./MenuItem.js\";\nexport * from \"./menuItemClasses.js\";\nexport { default as menuItemClasses } from \"./menuItemClasses.js\";", "map": {"version": 3, "names": ["default", "menuItemClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/MenuItem/index.js"], "sourcesContent": ["export { default } from \"./MenuItem.js\";\nexport * from \"./menuItemClasses.js\";\nexport { default as menuItemClasses } from \"./menuItemClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC,cAAc,sBAAsB;AACpC,SAASA,OAAO,IAAIC,eAAe,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}