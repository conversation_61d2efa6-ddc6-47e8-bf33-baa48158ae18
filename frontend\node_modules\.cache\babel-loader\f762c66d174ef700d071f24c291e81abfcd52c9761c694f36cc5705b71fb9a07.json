{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DateTimePicker } from '@mui/x-date-pickers'`\", \"or `import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DateTimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDateTimePicker() {\n  warn();\n  return null;\n});\nexport default DateTimePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "DateTimePicker", "forwardRef", "DeprecatedDateTimePicker"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/DateTimePicker/DateTimePicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DateTimePicker } from '@mui/x-date-pickers'`\", \"or `import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DateTimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDateTimePicker() {\n  warn();\n  return null;\n});\nexport default DateTimePicker;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,uFAAuF,EAAE,EAAE,EAAE,uEAAuE,EAAE,0EAA0E,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACtXH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,wBAAwBA,CAAA,EAAG;EACvFL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}