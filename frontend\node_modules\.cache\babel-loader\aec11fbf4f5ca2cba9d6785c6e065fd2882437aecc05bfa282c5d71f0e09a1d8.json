{"ast": null, "code": "import api, { endpoints } from './api';\nclass AuthService {\n  constructor() {\n    this.adminStatusCache = null;\n    this.cacheExpiry = 0;\n    this.CACHE_DURATION = 5 * 60 * 1000;\n  }\n  // 5 minutes\n\n  // Register new user\n  async register(data) {\n    const response = await api.post(endpoints.register, data);\n    const {\n      user,\n      token\n    } = response.data;\n    if (token) {\n      this.setAuthData(user, token);\n    }\n    return response.data;\n  }\n\n  // Login user\n  async login(credentials) {\n    const response = await api.post(endpoints.login, credentials);\n    const {\n      user,\n      token\n    } = response.data;\n    if (token) {\n      this.setAuthData(user, token);\n    }\n    return response.data;\n  }\n\n  // Logout user\n  async logout() {\n    try {\n      await api.post(endpoints.logout);\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      this.clearAuthData();\n    }\n  }\n\n  // Get current user profile\n  async getProfile() {\n    const response = await api.get(endpoints.profile);\n    return response.data.user;\n  }\n\n  // Update user profile\n  async updateProfile(data) {\n    const response = await api.put(endpoints.updateProfile, data);\n    const user = response.data.user;\n\n    // Update stored user data\n    localStorage.setItem('user', JSON.stringify(user));\n    return user;\n  }\n\n  // Upload avatar\n  async uploadAvatar(file) {\n    const formData = new FormData();\n    formData.append('avatar', file);\n    const response = await api.post(endpoints.uploadAvatar, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    const user = response.data.user;\n    localStorage.setItem('user', JSON.stringify(user));\n    return user;\n  }\n\n  // Forgot password\n  async forgotPassword(email) {\n    const response = await api.post(endpoints.forgotPassword, {\n      email\n    });\n    return response.data;\n  }\n\n  // Reset password\n  async resetPassword(data) {\n    const response = await api.post(endpoints.resetPassword, data);\n    return response.data;\n  }\n\n  // Resend email verification\n  async resendVerification() {\n    const response = await api.post(endpoints.resendVerification);\n    return response.data;\n  }\n\n  // Check if current user is admin\n  async checkAdminStatus() {\n    // Return cached result if still valid\n    if (this.adminStatusCache && Date.now() < this.cacheExpiry) {\n      return this.adminStatusCache;\n    }\n    try {\n      const response = await api.get(endpoints.adminStatus);\n      const result = response.data;\n\n      // Cache the result\n      this.adminStatusCache = result;\n      this.cacheExpiry = Date.now() + this.CACHE_DURATION;\n      return result;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      // If unauthorized or network error, assume not admin\n      const result = {\n        success: false,\n        is_admin: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to check admin status'\n      };\n\n      // Cache negative result for shorter time\n      this.adminStatusCache = result;\n      this.cacheExpiry = Date.now() + 30 * 1000; // 30 seconds\n\n      return result;\n    }\n  }\n\n  // Check if user is admin (cached)\n  async isAdmin() {\n    const status = await this.checkAdminStatus();\n    return status.is_admin;\n  }\n\n  // Clear admin status cache (call when user logs out or status changes)\n  clearAdminCache() {\n    this.adminStatusCache = null;\n    this.cacheExpiry = 0;\n  }\n\n  // Get page edit URL\n  getPageEditUrl(pageId) {\n    const baseUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n    return `${baseUrl}/admin/pages/${pageId}/edit`;\n  }\n\n  // Create admin session and navigate to admin panel\n  async navigateToAdminPanel(targetPath) {\n    try {\n      const response = await api.post(endpoints.adminSession);\n      if (response.data.success) {\n        // Open SSO URL in new tab - this will create the web session and redirect to admin\n        let ssoUrl = response.data.admin_url;\n\n        // If target path is provided, add it as redirect parameter\n        if (targetPath) {\n          const baseUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n          const fullTargetUrl = `${baseUrl}${targetPath}`;\n          ssoUrl += `?redirect=${encodeURIComponent(fullTargetUrl)}`;\n        }\n        window.open(ssoUrl, '_blank', 'noopener,noreferrer');\n      } else {\n        throw new Error(response.data.message || 'Failed to create admin session');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to access admin panel');\n    }\n  }\n\n  // Helper methods\n  setAuthData(user, token) {\n    localStorage.setItem('auth_token', token);\n    localStorage.setItem('user', JSON.stringify(user));\n  }\n  clearAuthData() {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user');\n    this.clearAdminCache();\n  }\n  getStoredUser() {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  }\n  getStoredToken() {\n    return localStorage.getItem('auth_token');\n  }\n  isAuthenticated() {\n    return !!this.getStoredToken();\n  }\n  isEmailVerified() {\n    const user = this.getStoredUser();\n    return !!(user !== null && user !== void 0 && user.email_verified_at);\n  }\n}\nconst authService = new AuthService();\nexport default authService;", "map": {"version": 3, "names": ["api", "endpoints", "AuthService", "constructor", "adminStatusCache", "cacheExpiry", "CACHE_DURATION", "register", "data", "response", "post", "user", "token", "setAuthData", "login", "credentials", "logout", "error", "console", "clearAuthData", "getProfile", "get", "profile", "updateProfile", "put", "localStorage", "setItem", "JSON", "stringify", "uploadAvatar", "file", "formData", "FormData", "append", "headers", "forgotPassword", "email", "resetPassword", "resendVerification", "checkAdminStatus", "Date", "now", "adminStatus", "result", "_error$response", "_error$response$data", "success", "is_admin", "message", "isAdmin", "status", "clearAdminCache", "getPageEditUrl", "pageId", "baseUrl", "process", "env", "REACT_APP_BACKEND_URL", "navigateToAdminPanel", "targetPath", "adminSession", "ssoUrl", "admin_url", "fullTargetUrl", "encodeURIComponent", "window", "open", "Error", "_error$response2", "_error$response2$data", "removeItem", "getStoredUser", "userStr", "getItem", "parse", "getStoredToken", "isAuthenticated", "isEmailVerified", "email_verified_at", "authService"], "sources": ["C:/laragon/www/frontend/src/services/authService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  phone?: string;\n  bio?: string;\n  avatar?: string;\n  date_of_birth?: string;\n  role: 'admin' | 'user';\n  is_active: boolean;\n  email_verified_at?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  password_confirmation: string;\n  phone?: string;\n  date_of_birth?: string;\n}\n\nexport interface AuthResponse {\n  message: string;\n  user: User;\n  token: string;\n  redirect_url?: string;\n}\n\nexport interface UpdateProfileData {\n  name?: string;\n  email?: string;\n  phone?: string;\n  bio?: string;\n  date_of_birth?: string;\n  current_password?: string;\n  password?: string;\n  password_confirmation?: string;\n}\n\nexport interface AdminStatusResponse {\n  success: boolean;\n  is_admin: boolean;\n  user?: User;\n  message?: string;\n}\n\nclass AuthService {\n  private adminStatusCache: AdminStatusResponse | null = null;\n  private cacheExpiry: number = 0;\n  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\n  // Register new user\n  async register(data: RegisterData): Promise<AuthResponse> {\n    const response = await api.post(endpoints.register, data);\n    const { user, token } = response.data;\n\n    if (token) {\n      this.setAuthData(user, token);\n    }\n\n    return response.data;\n  }\n\n  // Login user\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    const response = await api.post(endpoints.login, credentials);\n    const { user, token } = response.data;\n\n    if (token) {\n      this.setAuthData(user, token);\n    }\n\n    return response.data;\n  }\n\n  // Logout user\n  async logout(): Promise<void> {\n    try {\n      await api.post(endpoints.logout);\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      this.clearAuthData();\n    }\n  }\n\n  // Get current user profile\n  async getProfile(): Promise<User> {\n    const response = await api.get(endpoints.profile);\n    return response.data.user;\n  }\n\n  // Update user profile\n  async updateProfile(data: UpdateProfileData): Promise<User> {\n    const response = await api.put(endpoints.updateProfile, data);\n    const user = response.data.user;\n\n    // Update stored user data\n    localStorage.setItem('user', JSON.stringify(user));\n\n    return user;\n  }\n\n  // Upload avatar\n  async uploadAvatar(file: File): Promise<User> {\n    const formData = new FormData();\n    formData.append('avatar', file);\n\n    const response = await api.post(endpoints.uploadAvatar, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n\n    const user = response.data.user;\n    localStorage.setItem('user', JSON.stringify(user));\n\n    return user;\n  }\n\n  // Forgot password\n  async forgotPassword(email: string): Promise<{ message: string }> {\n    const response = await api.post(endpoints.forgotPassword, { email });\n    return response.data;\n  }\n\n  // Reset password\n  async resetPassword(data: {\n    token: string;\n    email: string;\n    password: string;\n    password_confirmation: string;\n  }): Promise<{ message: string }> {\n    const response = await api.post(endpoints.resetPassword, data);\n    return response.data;\n  }\n\n  // Resend email verification\n  async resendVerification(): Promise<{ message: string }> {\n    const response = await api.post(endpoints.resendVerification);\n    return response.data;\n  }\n\n  // Check if current user is admin\n  async checkAdminStatus(): Promise<AdminStatusResponse> {\n    // Return cached result if still valid\n    if (this.adminStatusCache && Date.now() < this.cacheExpiry) {\n      return this.adminStatusCache;\n    }\n\n    try {\n      const response = await api.get(endpoints.adminStatus);\n      const result: AdminStatusResponse = response.data;\n\n      // Cache the result\n      this.adminStatusCache = result;\n      this.cacheExpiry = Date.now() + this.CACHE_DURATION;\n\n      return result;\n    } catch (error: any) {\n      // If unauthorized or network error, assume not admin\n      const result: AdminStatusResponse = {\n        success: false,\n        is_admin: false,\n        message: error.response?.data?.message || 'Failed to check admin status'\n      };\n\n      // Cache negative result for shorter time\n      this.adminStatusCache = result;\n      this.cacheExpiry = Date.now() + (30 * 1000); // 30 seconds\n\n      return result;\n    }\n  }\n\n  // Check if user is admin (cached)\n  async isAdmin(): Promise<boolean> {\n    const status = await this.checkAdminStatus();\n    return status.is_admin;\n  }\n\n  // Clear admin status cache (call when user logs out or status changes)\n  clearAdminCache(): void {\n    this.adminStatusCache = null;\n    this.cacheExpiry = 0;\n  }\n\n  // Get page edit URL\n  getPageEditUrl(pageId: number): string {\n    const baseUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n    return `${baseUrl}/admin/pages/${pageId}/edit`;\n  }\n\n  // Create admin session and navigate to admin panel\n  async navigateToAdminPanel(targetPath?: string): Promise<void> {\n    try {\n      const response = await api.post(endpoints.adminSession);\n      if (response.data.success) {\n        // Open SSO URL in new tab - this will create the web session and redirect to admin\n        let ssoUrl = response.data.admin_url;\n\n        // If target path is provided, add it as redirect parameter\n        if (targetPath) {\n          const baseUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n          const fullTargetUrl = `${baseUrl}${targetPath}`;\n          ssoUrl += `?redirect=${encodeURIComponent(fullTargetUrl)}`;\n        }\n\n        window.open(ssoUrl, '_blank', 'noopener,noreferrer');\n      } else {\n        throw new Error(response.data.message || 'Failed to create admin session');\n      }\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to access admin panel');\n    }\n  }\n\n  // Helper methods\n  setAuthData(user: User, token: string): void {\n    localStorage.setItem('auth_token', token);\n    localStorage.setItem('user', JSON.stringify(user));\n  }\n\n  clearAuthData(): void {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user');\n    this.clearAdminCache();\n  }\n\n  getStoredUser(): User | null {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  }\n\n  getStoredToken(): string | null {\n    return localStorage.getItem('auth_token');\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getStoredToken();\n  }\n\n  isEmailVerified(): boolean {\n    const user = this.getStoredUser();\n    return !!user?.email_verified_at;\n  }\n}\n\nconst authService = new AuthService();\nexport default authService;\n"], "mappings": "AAAA,OAAOA,GAAG,IAAIC,SAAS,QAAQ,OAAO;AAwDtC,MAAMC,WAAW,CAAC;EAAAC,YAAA;IAAA,KACRC,gBAAgB,GAA+B,IAAI;IAAA,KACnDC,WAAW,GAAW,CAAC;IAAA,KACdC,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;EAAA;EAAE;;EAEjD;EACA,MAAMC,QAAQA,CAACC,IAAkB,EAAyB;IACxD,MAAMC,QAAQ,GAAG,MAAMT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACM,QAAQ,EAAEC,IAAI,CAAC;IACzD,MAAM;MAAEG,IAAI;MAAEC;IAAM,CAAC,GAAGH,QAAQ,CAACD,IAAI;IAErC,IAAII,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,CAACF,IAAI,EAAEC,KAAK,CAAC;IAC/B;IAEA,OAAOH,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAMM,KAAKA,CAACC,WAA6B,EAAyB;IAChE,MAAMN,QAAQ,GAAG,MAAMT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACa,KAAK,EAAEC,WAAW,CAAC;IAC7D,MAAM;MAAEJ,IAAI;MAAEC;IAAM,CAAC,GAAGH,QAAQ,CAACD,IAAI;IAErC,IAAII,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,CAACF,IAAI,EAAEC,KAAK,CAAC;IAC/B;IAEA,OAAOH,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAMQ,MAAMA,CAAA,EAAkB;IAC5B,IAAI;MACF,MAAMhB,GAAG,CAACU,IAAI,CAACT,SAAS,CAACe,MAAM,CAAC;IAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR,IAAI,CAACE,aAAa,CAAC,CAAC;IACtB;EACF;;EAEA;EACA,MAAMC,UAAUA,CAAA,EAAkB;IAChC,MAAMX,QAAQ,GAAG,MAAMT,GAAG,CAACqB,GAAG,CAACpB,SAAS,CAACqB,OAAO,CAAC;IACjD,OAAOb,QAAQ,CAACD,IAAI,CAACG,IAAI;EAC3B;;EAEA;EACA,MAAMY,aAAaA,CAACf,IAAuB,EAAiB;IAC1D,MAAMC,QAAQ,GAAG,MAAMT,GAAG,CAACwB,GAAG,CAACvB,SAAS,CAACsB,aAAa,EAAEf,IAAI,CAAC;IAC7D,MAAMG,IAAI,GAAGF,QAAQ,CAACD,IAAI,CAACG,IAAI;;IAE/B;IACAc,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC;IAElD,OAAOA,IAAI;EACb;;EAEA;EACA,MAAMkB,YAAYA,CAACC,IAAU,EAAiB;IAC5C,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,IAAI,CAAC;IAE/B,MAAMrB,QAAQ,GAAG,MAAMT,GAAG,CAACU,IAAI,CAACT,SAAS,CAAC4B,YAAY,EAAEE,QAAQ,EAAE;MAChEG,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,MAAMvB,IAAI,GAAGF,QAAQ,CAACD,IAAI,CAACG,IAAI;IAC/Bc,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC;IAElD,OAAOA,IAAI;EACb;;EAEA;EACA,MAAMwB,cAAcA,CAACC,KAAa,EAAgC;IAChE,MAAM3B,QAAQ,GAAG,MAAMT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACkC,cAAc,EAAE;MAAEC;IAAM,CAAC,CAAC;IACpE,OAAO3B,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAM6B,aAAaA,CAAC7B,IAKnB,EAAgC;IAC/B,MAAMC,QAAQ,GAAG,MAAMT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACoC,aAAa,EAAE7B,IAAI,CAAC;IAC9D,OAAOC,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAM8B,kBAAkBA,CAAA,EAAiC;IACvD,MAAM7B,QAAQ,GAAG,MAAMT,GAAG,CAACU,IAAI,CAACT,SAAS,CAACqC,kBAAkB,CAAC;IAC7D,OAAO7B,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAM+B,gBAAgBA,CAAA,EAAiC;IACrD;IACA,IAAI,IAAI,CAACnC,gBAAgB,IAAIoC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACpC,WAAW,EAAE;MAC1D,OAAO,IAAI,CAACD,gBAAgB;IAC9B;IAEA,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMT,GAAG,CAACqB,GAAG,CAACpB,SAAS,CAACyC,WAAW,CAAC;MACrD,MAAMC,MAA2B,GAAGlC,QAAQ,CAACD,IAAI;;MAEjD;MACA,IAAI,CAACJ,gBAAgB,GAAGuC,MAAM;MAC9B,IAAI,CAACtC,WAAW,GAAGmC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACnC,cAAc;MAEnD,OAAOqC,MAAM;IACf,CAAC,CAAC,OAAO1B,KAAU,EAAE;MAAA,IAAA2B,eAAA,EAAAC,oBAAA;MACnB;MACA,MAAMF,MAA2B,GAAG;QAClCG,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,EAAAJ,eAAA,GAAA3B,KAAK,CAACR,QAAQ,cAAAmC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpC,IAAI,cAAAqC,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI;MAC5C,CAAC;;MAED;MACA,IAAI,CAAC5C,gBAAgB,GAAGuC,MAAM;MAC9B,IAAI,CAACtC,WAAW,GAAGmC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAI,EAAE,GAAG,IAAK,CAAC,CAAC;;MAE7C,OAAOE,MAAM;IACf;EACF;;EAEA;EACA,MAAMM,OAAOA,CAAA,EAAqB;IAChC,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACX,gBAAgB,CAAC,CAAC;IAC5C,OAAOW,MAAM,CAACH,QAAQ;EACxB;;EAEA;EACAI,eAAeA,CAAA,EAAS;IACtB,IAAI,CAAC/C,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG,CAAC;EACtB;;EAEA;EACA+C,cAAcA,CAACC,MAAc,EAAU;IACrC,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,uBAAuB;IAC5E,OAAO,GAAGH,OAAO,gBAAgBD,MAAM,OAAO;EAChD;;EAEA;EACA,MAAMK,oBAAoBA,CAACC,UAAmB,EAAiB;IAC7D,IAAI;MACF,MAAMlD,QAAQ,GAAG,MAAMT,GAAG,CAACU,IAAI,CAACT,SAAS,CAAC2D,YAAY,CAAC;MACvD,IAAInD,QAAQ,CAACD,IAAI,CAACsC,OAAO,EAAE;QACzB;QACA,IAAIe,MAAM,GAAGpD,QAAQ,CAACD,IAAI,CAACsD,SAAS;;QAEpC;QACA,IAAIH,UAAU,EAAE;UACd,MAAML,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,uBAAuB;UAC5E,MAAMM,aAAa,GAAG,GAAGT,OAAO,GAAGK,UAAU,EAAE;UAC/CE,MAAM,IAAI,aAAaG,kBAAkB,CAACD,aAAa,CAAC,EAAE;QAC5D;QAEAE,MAAM,CAACC,IAAI,CAACL,MAAM,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACtD,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAAC1D,QAAQ,CAACD,IAAI,CAACwC,OAAO,IAAI,gCAAgC,CAAC;MAC5E;IACF,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAAmD,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIF,KAAK,CAAC,EAAAC,gBAAA,GAAAnD,KAAK,CAACR,QAAQ,cAAA2D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5D,IAAI,cAAA6D,qBAAA,uBAApBA,qBAAA,CAAsBrB,OAAO,KAAI,8BAA8B,CAAC;IAClF;EACF;;EAEA;EACAnC,WAAWA,CAACF,IAAU,EAAEC,KAAa,EAAQ;IAC3Ca,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEd,KAAK,CAAC;IACzCa,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC;EACpD;EAEAQ,aAAaA,CAAA,EAAS;IACpBM,YAAY,CAAC6C,UAAU,CAAC,YAAY,CAAC;IACrC7C,YAAY,CAAC6C,UAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,CAACnB,eAAe,CAAC,CAAC;EACxB;EAEAoB,aAAaA,CAAA,EAAgB;IAC3B,MAAMC,OAAO,GAAG/C,YAAY,CAACgD,OAAO,CAAC,MAAM,CAAC;IAC5C,OAAOD,OAAO,GAAG7C,IAAI,CAAC+C,KAAK,CAACF,OAAO,CAAC,GAAG,IAAI;EAC7C;EAEAG,cAAcA,CAAA,EAAkB;IAC9B,OAAOlD,YAAY,CAACgD,OAAO,CAAC,YAAY,CAAC;EAC3C;EAEAG,eAAeA,CAAA,EAAY;IACzB,OAAO,CAAC,CAAC,IAAI,CAACD,cAAc,CAAC,CAAC;EAChC;EAEAE,eAAeA,CAAA,EAAY;IACzB,MAAMlE,IAAI,GAAG,IAAI,CAAC4D,aAAa,CAAC,CAAC;IACjC,OAAO,CAAC,EAAC5D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmE,iBAAiB;EAClC;AACF;AAEA,MAAMC,WAAW,GAAG,IAAI7E,WAAW,CAAC,CAAC;AACrC,eAAe6E,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}