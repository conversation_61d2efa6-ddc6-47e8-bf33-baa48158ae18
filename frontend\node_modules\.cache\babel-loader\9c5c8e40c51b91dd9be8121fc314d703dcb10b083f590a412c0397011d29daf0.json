{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { globalCss } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\n\n// to determine if the global styles are static or dynamic\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst isDynamicSupport = typeof globalCss({}) === 'function';\nexport const html = (theme, enableColorScheme) => ({\n  WebkitFontSmoothing: 'antialiased',\n  // Antialiasing.\n  MozOsxFontSmoothing: 'grayscale',\n  // Antialiasing.\n  // Change from `box-sizing: content-box` so that `width`\n  // is not affected by `padding` or `border`.\n  boxSizing: 'border-box',\n  // Fix font resize problem in iOS\n  WebkitTextSizeAdjust: '100%',\n  // When used under CssVarsProvider, colorScheme should not be applied dynamically because it will generate the stylesheet twice for server-rendered applications.\n  ...(enableColorScheme && !theme.vars && {\n    colorScheme: theme.palette.mode\n  })\n});\nexport const body = theme => ({\n  color: (theme.vars || theme).palette.text.primary,\n  ...theme.typography.body1,\n  backgroundColor: (theme.vars || theme).palette.background.default,\n  '@media print': {\n    // Save printer ink.\n    backgroundColor: (theme.vars || theme).palette.common.white\n  }\n});\nexport const styles = function (theme) {\n  let enableColorScheme = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  const colorSchemeStyles = {};\n  if (enableColorScheme && theme.colorSchemes && typeof theme.getColorSchemeSelector === 'function') {\n    Object.entries(theme.colorSchemes).forEach(_ref => {\n      let [key, scheme] = _ref;\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        colorSchemeStyles[selector] = {\n          ':root': {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        colorSchemeStyles[selector.replace(/\\s*&/, '')] = {\n          colorScheme: scheme.palette?.mode\n        };\n      }\n    });\n  }\n  let defaultStyles = {\n    html: html(theme, enableColorScheme),\n    '*, *::before, *::after': {\n      boxSizing: 'inherit'\n    },\n    'strong, b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    body: {\n      margin: 0,\n      // Remove the margin in all browsers.\n      ...body(theme),\n      // Add support for document.body.requestFullScreen().\n      // Other elements, if background transparent, are not supported.\n      '&::backdrop': {\n        backgroundColor: (theme.vars || theme).palette.background.default\n      }\n    },\n    ...colorSchemeStyles\n  };\n  const themeOverrides = theme.components?.MuiCssBaseline?.styleOverrides;\n  if (themeOverrides) {\n    defaultStyles = [defaultStyles, themeOverrides];\n  }\n  return defaultStyles;\n};\n\n// `ecs` stands for enableColorScheme. This is internal logic to make it work with Pigment CSS, so shorter is better.\nconst SELECTOR = 'mui-ecs';\nconst staticStyles = theme => {\n  const result = styles(theme, false);\n  const baseStyles = Array.isArray(result) ? result[0] : result;\n  if (!theme.vars && baseStyles) {\n    baseStyles.html[`:root:has(${SELECTOR})`] = {\n      colorScheme: theme.palette.mode\n    };\n  }\n  if (theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(_ref2 => {\n      let [key, scheme] = _ref2;\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        baseStyles[selector] = {\n          [`:root:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        baseStyles[selector.replace(/\\s*&/, '')] = {\n          [`&:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      }\n    });\n  }\n  return result;\n};\nconst GlobalStyles = globalCss(isDynamicSupport ? _ref3 => {\n  let {\n    theme,\n    enableColorScheme\n  } = _ref3;\n  return styles(theme, enableColorScheme);\n} : _ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return staticStyles(theme);\n});\n\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n */\nfunction CssBaseline(inProps) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCssBaseline'\n  });\n  const {\n    children,\n    enableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [isDynamicSupport && /*#__PURE__*/_jsx(GlobalStyles, {\n      enableColorScheme: enableColorScheme\n    }), !isDynamicSupport && !enableColorScheme && /*#__PURE__*/_jsx(\"span\", {\n      className: SELECTOR,\n      style: {\n        display: 'none'\n      }\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  enableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;", "map": {"version": 3, "names": ["React", "PropTypes", "globalCss", "useDefaultProps", "jsx", "_jsx", "jsxs", "_jsxs", "isDynamicSupport", "html", "theme", "enableColorScheme", "WebkitFontSmoothing", "MozOsxFontSmoothing", "boxSizing", "WebkitTextSizeAdjust", "vars", "colorScheme", "palette", "mode", "body", "color", "text", "primary", "typography", "body1", "backgroundColor", "background", "default", "common", "white", "styles", "arguments", "length", "undefined", "colorSchemeStyles", "colorSchemes", "getColorSchemeSelector", "Object", "entries", "for<PERSON>ach", "_ref", "key", "scheme", "selector", "startsWith", "replace", "defaultStyles", "fontWeight", "fontWeightBold", "margin", "themeOverrides", "components", "MuiCssBaseline", "styleOverrides", "SELECTOR", "staticStyles", "result", "baseStyles", "Array", "isArray", "_ref2", "GlobalStyles", "_ref3", "_ref4", "CssBaseline", "inProps", "props", "name", "children", "Fragment", "className", "style", "display", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/CssBaseline/CssBaseline.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { globalCss } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\n\n// to determine if the global styles are static or dynamic\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst isDynamicSupport = typeof globalCss({}) === 'function';\nexport const html = (theme, enableColorScheme) => ({\n  WebkitFontSmoothing: 'antialiased',\n  // Antialiasing.\n  MozOsxFontSmoothing: 'grayscale',\n  // Antialiasing.\n  // Change from `box-sizing: content-box` so that `width`\n  // is not affected by `padding` or `border`.\n  boxSizing: 'border-box',\n  // Fix font resize problem in iOS\n  WebkitTextSizeAdjust: '100%',\n  // When used under CssVarsProvider, colorScheme should not be applied dynamically because it will generate the stylesheet twice for server-rendered applications.\n  ...(enableColorScheme && !theme.vars && {\n    colorScheme: theme.palette.mode\n  })\n});\nexport const body = theme => ({\n  color: (theme.vars || theme).palette.text.primary,\n  ...theme.typography.body1,\n  backgroundColor: (theme.vars || theme).palette.background.default,\n  '@media print': {\n    // Save printer ink.\n    backgroundColor: (theme.vars || theme).palette.common.white\n  }\n});\nexport const styles = (theme, enableColorScheme = false) => {\n  const colorSchemeStyles = {};\n  if (enableColorScheme && theme.colorSchemes && typeof theme.getColorSchemeSelector === 'function') {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        colorSchemeStyles[selector] = {\n          ':root': {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        colorSchemeStyles[selector.replace(/\\s*&/, '')] = {\n          colorScheme: scheme.palette?.mode\n        };\n      }\n    });\n  }\n  let defaultStyles = {\n    html: html(theme, enableColorScheme),\n    '*, *::before, *::after': {\n      boxSizing: 'inherit'\n    },\n    'strong, b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    body: {\n      margin: 0,\n      // Remove the margin in all browsers.\n      ...body(theme),\n      // Add support for document.body.requestFullScreen().\n      // Other elements, if background transparent, are not supported.\n      '&::backdrop': {\n        backgroundColor: (theme.vars || theme).palette.background.default\n      }\n    },\n    ...colorSchemeStyles\n  };\n  const themeOverrides = theme.components?.MuiCssBaseline?.styleOverrides;\n  if (themeOverrides) {\n    defaultStyles = [defaultStyles, themeOverrides];\n  }\n  return defaultStyles;\n};\n\n// `ecs` stands for enableColorScheme. This is internal logic to make it work with Pigment CSS, so shorter is better.\nconst SELECTOR = 'mui-ecs';\nconst staticStyles = theme => {\n  const result = styles(theme, false);\n  const baseStyles = Array.isArray(result) ? result[0] : result;\n  if (!theme.vars && baseStyles) {\n    baseStyles.html[`:root:has(${SELECTOR})`] = {\n      colorScheme: theme.palette.mode\n    };\n  }\n  if (theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        baseStyles[selector] = {\n          [`:root:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        baseStyles[selector.replace(/\\s*&/, '')] = {\n          [`&:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      }\n    });\n  }\n  return result;\n};\nconst GlobalStyles = globalCss(isDynamicSupport ? ({\n  theme,\n  enableColorScheme\n}) => styles(theme, enableColorScheme) : ({\n  theme\n}) => staticStyles(theme));\n\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n */\nfunction CssBaseline(inProps) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCssBaseline'\n  });\n  const {\n    children,\n    enableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [isDynamicSupport && /*#__PURE__*/_jsx(GlobalStyles, {\n      enableColorScheme: enableColorScheme\n    }), !isDynamicSupport && !enableColorScheme && /*#__PURE__*/_jsx(\"span\", {\n      className: SELECTOR,\n      style: {\n        display: 'none'\n      }\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  enableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,eAAe,QAAQ,kCAAkC;;AAElE;AACA,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,gBAAgB,GAAG,OAAON,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,UAAU;AAC5D,OAAO,MAAMO,IAAI,GAAGA,CAACC,KAAK,EAAEC,iBAAiB,MAAM;EACjDC,mBAAmB,EAAE,aAAa;EAClC;EACAC,mBAAmB,EAAE,WAAW;EAChC;EACA;EACA;EACAC,SAAS,EAAE,YAAY;EACvB;EACAC,oBAAoB,EAAE,MAAM;EAC5B;EACA,IAAIJ,iBAAiB,IAAI,CAACD,KAAK,CAACM,IAAI,IAAI;IACtCC,WAAW,EAAEP,KAAK,CAACQ,OAAO,CAACC;EAC7B,CAAC;AACH,CAAC,CAAC;AACF,OAAO,MAAMC,IAAI,GAAGV,KAAK,KAAK;EAC5BW,KAAK,EAAE,CAACX,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACI,IAAI,CAACC,OAAO;EACjD,GAAGb,KAAK,CAACc,UAAU,CAACC,KAAK;EACzBC,eAAe,EAAE,CAAChB,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACS,UAAU,CAACC,OAAO;EACjE,cAAc,EAAE;IACd;IACAF,eAAe,EAAE,CAAChB,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACW,MAAM,CAACC;EACxD;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,MAAM,GAAG,SAAAA,CAACrB,KAAK,EAAgC;EAAA,IAA9BC,iBAAiB,GAAAqB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACrD,MAAMG,iBAAiB,GAAG,CAAC,CAAC;EAC5B,IAAIxB,iBAAiB,IAAID,KAAK,CAAC0B,YAAY,IAAI,OAAO1B,KAAK,CAAC2B,sBAAsB,KAAK,UAAU,EAAE;IACjGC,MAAM,CAACC,OAAO,CAAC7B,KAAK,CAAC0B,YAAY,CAAC,CAACI,OAAO,CAACC,IAAA,IAAmB;MAAA,IAAlB,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAAF,IAAA;MACvD,MAAMG,QAAQ,GAAGlC,KAAK,CAAC2B,sBAAsB,CAACK,GAAG,CAAC;MAClD,IAAIE,QAAQ,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;QAC5B;QACAV,iBAAiB,CAACS,QAAQ,CAAC,GAAG;UAC5B,OAAO,EAAE;YACP3B,WAAW,EAAE0B,MAAM,CAACzB,OAAO,EAAEC;UAC/B;QACF,CAAC;MACH,CAAC,MAAM;QACL;QACAgB,iBAAiB,CAACS,QAAQ,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG;UAChD7B,WAAW,EAAE0B,MAAM,CAACzB,OAAO,EAAEC;QAC/B,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,IAAI4B,aAAa,GAAG;IAClBtC,IAAI,EAAEA,IAAI,CAACC,KAAK,EAAEC,iBAAiB,CAAC;IACpC,wBAAwB,EAAE;MACxBG,SAAS,EAAE;IACb,CAAC;IACD,WAAW,EAAE;MACXkC,UAAU,EAAEtC,KAAK,CAACc,UAAU,CAACyB;IAC/B,CAAC;IACD7B,IAAI,EAAE;MACJ8B,MAAM,EAAE,CAAC;MACT;MACA,GAAG9B,IAAI,CAACV,KAAK,CAAC;MACd;MACA;MACA,aAAa,EAAE;QACbgB,eAAe,EAAE,CAAChB,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACS,UAAU,CAACC;MAC5D;IACF,CAAC;IACD,GAAGO;EACL,CAAC;EACD,MAAMgB,cAAc,GAAGzC,KAAK,CAAC0C,UAAU,EAAEC,cAAc,EAAEC,cAAc;EACvE,IAAIH,cAAc,EAAE;IAClBJ,aAAa,GAAG,CAACA,aAAa,EAAEI,cAAc,CAAC;EACjD;EACA,OAAOJ,aAAa;AACtB,CAAC;;AAED;AACA,MAAMQ,QAAQ,GAAG,SAAS;AAC1B,MAAMC,YAAY,GAAG9C,KAAK,IAAI;EAC5B,MAAM+C,MAAM,GAAG1B,MAAM,CAACrB,KAAK,EAAE,KAAK,CAAC;EACnC,MAAMgD,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;EAC7D,IAAI,CAAC/C,KAAK,CAACM,IAAI,IAAI0C,UAAU,EAAE;IAC7BA,UAAU,CAACjD,IAAI,CAAC,aAAa8C,QAAQ,GAAG,CAAC,GAAG;MAC1CtC,WAAW,EAAEP,KAAK,CAACQ,OAAO,CAACC;IAC7B,CAAC;EACH;EACA,IAAIT,KAAK,CAAC0B,YAAY,EAAE;IACtBE,MAAM,CAACC,OAAO,CAAC7B,KAAK,CAAC0B,YAAY,CAAC,CAACI,OAAO,CAACqB,KAAA,IAAmB;MAAA,IAAlB,CAACnB,GAAG,EAAEC,MAAM,CAAC,GAAAkB,KAAA;MACvD,MAAMjB,QAAQ,GAAGlC,KAAK,CAAC2B,sBAAsB,CAACK,GAAG,CAAC;MAClD,IAAIE,QAAQ,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;QAC5B;QACAa,UAAU,CAACd,QAAQ,CAAC,GAAG;UACrB,CAAC,mBAAmBW,QAAQ,IAAI,GAAG;YACjCtC,WAAW,EAAE0B,MAAM,CAACzB,OAAO,EAAEC;UAC/B;QACF,CAAC;MACH,CAAC,MAAM;QACL;QACAuC,UAAU,CAACd,QAAQ,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG;UACzC,CAAC,eAAeS,QAAQ,IAAI,GAAG;YAC7BtC,WAAW,EAAE0B,MAAM,CAACzB,OAAO,EAAEC;UAC/B;QACF,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,OAAOsC,MAAM;AACf,CAAC;AACD,MAAMK,YAAY,GAAG5D,SAAS,CAACM,gBAAgB,GAAGuD,KAAA;EAAA,IAAC;IACjDrD,KAAK;IACLC;EACF,CAAC,GAAAoD,KAAA;EAAA,OAAKhC,MAAM,CAACrB,KAAK,EAAEC,iBAAiB,CAAC;AAAA,IAAGqD,KAAA;EAAA,IAAC;IACxCtD;EACF,CAAC,GAAAsD,KAAA;EAAA,OAAKR,YAAY,CAAC9C,KAAK,CAAC;AAAA,EAAC;;AAE1B;AACA;AACA;AACA,SAASuD,WAAWA,CAACC,OAAO,EAAE;EAC5B,MAAMC,KAAK,GAAGhE,eAAe,CAAC;IAC5BgE,KAAK,EAAED,OAAO;IACdE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJC,QAAQ;IACR1D,iBAAiB,GAAG;EACtB,CAAC,GAAGwD,KAAK;EACT,OAAO,aAAa5D,KAAK,CAACP,KAAK,CAACsE,QAAQ,EAAE;IACxCD,QAAQ,EAAE,CAAC7D,gBAAgB,IAAI,aAAaH,IAAI,CAACyD,YAAY,EAAE;MAC7DnD,iBAAiB,EAAEA;IACrB,CAAC,CAAC,EAAE,CAACH,gBAAgB,IAAI,CAACG,iBAAiB,IAAI,aAAaN,IAAI,CAAC,MAAM,EAAE;MACvEkE,SAAS,EAAEhB,QAAQ;MACnBiB,KAAK,EAAE;QACLC,OAAO,EAAE;MACX;IACF,CAAC,CAAC,EAAEJ,QAAQ;EACd,CAAC,CAAC;AACJ;AACAK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,WAAW,CAACY,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACER,QAAQ,EAAEpE,SAAS,CAAC6E,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACEnE,iBAAiB,EAAEV,SAAS,CAAC8E;AAC/B,CAAC,GAAG,KAAK,CAAC;AACV,eAAed,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}