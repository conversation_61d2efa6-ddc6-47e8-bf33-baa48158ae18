{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ButtonGroupButtonContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ButtonGroupButtonContext.displayName = 'ButtonGroupButtonContext';\n}\nexport default ButtonGroupButtonContext;", "map": {"version": 3, "names": ["React", "ButtonGroupButtonContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/ButtonGroup/ButtonGroupButtonContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ButtonGroupButtonContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ButtonGroupButtonContext.displayName = 'ButtonGroupButtonContext';\n}\nexport default ButtonGroupButtonContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAC5E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,wBAAwB,CAACM,WAAW,GAAG,0BAA0B;AACnE;AACA,eAAeN,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}