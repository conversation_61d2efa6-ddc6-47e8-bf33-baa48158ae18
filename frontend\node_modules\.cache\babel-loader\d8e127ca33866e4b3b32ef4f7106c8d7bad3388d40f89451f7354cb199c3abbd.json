{"ast": null, "code": "export function isUnitless(value) {\n  return String(parseFloat(value)).length === String(value).length;\n}\n\n// Ported from Compass\n// https://github.com/Compass/compass/blob/master/core/stylesheets/compass/typography/_units.scss\n// Emulate the sass function \"unit\"\nexport function getUnit(input) {\n  return String(input).match(/[\\d.\\-+]*\\s*(.*)/)[1] || '';\n}\n\n// Emulate the sass function \"unitless\"\nexport function toUnitless(length) {\n  return parseFloat(length);\n}\n\n// Convert any CSS <length> or <percentage> value to any another.\n// From https://github.com/KyleAMathews/convert-css-length\nexport function convertLength(baseFontSize) {\n  return (length, toUnit) => {\n    const fromUnit = getUnit(length);\n\n    // Optimize for cases where `from` and `to` units are accidentally the same.\n    if (fromUnit === toUnit) {\n      return length;\n    }\n\n    // Convert input length to pixels.\n    let pxLength = toUnitless(length);\n    if (fromUnit !== 'px') {\n      if (fromUnit === 'em') {\n        pxLength = toUnitless(length) * toUnitless(baseFontSize);\n      } else if (fromUnit === 'rem') {\n        pxLength = toUnitless(length) * toUnitless(baseFontSize);\n      }\n    }\n\n    // Convert length in pixels to the output unit\n    let outputLength = pxLength;\n    if (toUnit !== 'px') {\n      if (toUnit === 'em') {\n        outputLength = pxLength / toUnitless(baseFontSize);\n      } else if (toUnit === 'rem') {\n        outputLength = pxLength / toUnitless(baseFontSize);\n      } else {\n        return length;\n      }\n    }\n    return parseFloat(outputLength.toFixed(5)) + toUnit;\n  };\n}\nexport function alignProperty(_ref) {\n  let {\n    size,\n    grid\n  } = _ref;\n  const sizeBelow = size - size % grid;\n  const sizeAbove = sizeBelow + grid;\n  return size - sizeBelow < sizeAbove - size ? sizeBelow : sizeAbove;\n}\n\n// fontGrid finds a minimal grid (in rem) for the fontSize values so that the\n// lineHeight falls under a x pixels grid, 4px in the case of Material Design,\n// without changing the relative line height\nexport function fontGrid(_ref2) {\n  let {\n    lineHeight,\n    pixels,\n    htmlFontSize\n  } = _ref2;\n  return pixels / (lineHeight * htmlFontSize);\n}\n\n/**\n * generate a responsive version of a given CSS property\n * @example\n * responsiveProperty({\n *   cssProperty: 'fontSize',\n *   min: 15,\n *   max: 20,\n *   unit: 'px',\n *   breakpoints: [300, 600],\n * })\n *\n * // this returns\n *\n * {\n *   fontSize: '15px',\n *   '@media (min-width:300px)': {\n *     fontSize: '17.5px',\n *   },\n *   '@media (min-width:600px)': {\n *     fontSize: '20px',\n *   },\n * }\n * @param {Object} params\n * @param {string} params.cssProperty - The CSS property to be made responsive\n * @param {number} params.min - The smallest value of the CSS property\n * @param {number} params.max - The largest value of the CSS property\n * @param {string} [params.unit] - The unit to be used for the CSS property\n * @param {Array.number} [params.breakpoints]  - An array of breakpoints\n * @param {number} [params.alignStep] - Round scaled value to fall under this grid\n * @returns {Object} responsive styles for {params.cssProperty}\n */\nexport function responsiveProperty(_ref3) {\n  let {\n    cssProperty,\n    min,\n    max,\n    unit = 'rem',\n    breakpoints = [600, 900, 1200],\n    transform = null\n  } = _ref3;\n  const output = {\n    [cssProperty]: `${min}${unit}`\n  };\n  const factor = (max - min) / breakpoints[breakpoints.length - 1];\n  breakpoints.forEach(breakpoint => {\n    let value = min + factor * breakpoint;\n    if (transform !== null) {\n      value = transform(value);\n    }\n    output[`@media (min-width:${breakpoint}px)`] = {\n      [cssProperty]: `${Math.round(value * 10000) / 10000}${unit}`\n    };\n  });\n  return output;\n}", "map": {"version": 3, "names": ["isUnitless", "value", "String", "parseFloat", "length", "getUnit", "input", "match", "toUni<PERSON>s", "convertLength", "baseFontSize", "toUnit", "fromUnit", "px<PERSON><PERSON>th", "outputLength", "toFixed", "alignProperty", "_ref", "size", "grid", "sizeBelow", "sizeAbove", "fontGrid", "_ref2", "lineHeight", "pixels", "htmlFontSize", "responsiveProperty", "_ref3", "cssProperty", "min", "max", "unit", "breakpoints", "transform", "output", "factor", "for<PERSON>ach", "breakpoint", "Math", "round"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/styles/cssUtils.js"], "sourcesContent": ["export function isUnitless(value) {\n  return String(parseFloat(value)).length === String(value).length;\n}\n\n// Ported from Compass\n// https://github.com/Compass/compass/blob/master/core/stylesheets/compass/typography/_units.scss\n// Emulate the sass function \"unit\"\nexport function getUnit(input) {\n  return String(input).match(/[\\d.\\-+]*\\s*(.*)/)[1] || '';\n}\n\n// Emulate the sass function \"unitless\"\nexport function toUnitless(length) {\n  return parseFloat(length);\n}\n\n// Convert any CSS <length> or <percentage> value to any another.\n// From https://github.com/KyleAMathews/convert-css-length\nexport function convertLength(baseFontSize) {\n  return (length, toUnit) => {\n    const fromUnit = getUnit(length);\n\n    // Optimize for cases where `from` and `to` units are accidentally the same.\n    if (fromUnit === toUnit) {\n      return length;\n    }\n\n    // Convert input length to pixels.\n    let pxLength = toUnitless(length);\n    if (fromUnit !== 'px') {\n      if (fromUnit === 'em') {\n        pxLength = toUnitless(length) * toUnitless(baseFontSize);\n      } else if (fromUnit === 'rem') {\n        pxLength = toUnitless(length) * toUnitless(baseFontSize);\n      }\n    }\n\n    // Convert length in pixels to the output unit\n    let outputLength = pxLength;\n    if (toUnit !== 'px') {\n      if (toUnit === 'em') {\n        outputLength = pxLength / toUnitless(baseFontSize);\n      } else if (toUnit === 'rem') {\n        outputLength = pxLength / toUnitless(baseFontSize);\n      } else {\n        return length;\n      }\n    }\n    return parseFloat(outputLength.toFixed(5)) + toUnit;\n  };\n}\nexport function alignProperty({\n  size,\n  grid\n}) {\n  const sizeBelow = size - size % grid;\n  const sizeAbove = sizeBelow + grid;\n  return size - sizeBelow < sizeAbove - size ? sizeBelow : sizeAbove;\n}\n\n// fontGrid finds a minimal grid (in rem) for the fontSize values so that the\n// lineHeight falls under a x pixels grid, 4px in the case of Material Design,\n// without changing the relative line height\nexport function fontGrid({\n  lineHeight,\n  pixels,\n  htmlFontSize\n}) {\n  return pixels / (lineHeight * htmlFontSize);\n}\n\n/**\n * generate a responsive version of a given CSS property\n * @example\n * responsiveProperty({\n *   cssProperty: 'fontSize',\n *   min: 15,\n *   max: 20,\n *   unit: 'px',\n *   breakpoints: [300, 600],\n * })\n *\n * // this returns\n *\n * {\n *   fontSize: '15px',\n *   '@media (min-width:300px)': {\n *     fontSize: '17.5px',\n *   },\n *   '@media (min-width:600px)': {\n *     fontSize: '20px',\n *   },\n * }\n * @param {Object} params\n * @param {string} params.cssProperty - The CSS property to be made responsive\n * @param {number} params.min - The smallest value of the CSS property\n * @param {number} params.max - The largest value of the CSS property\n * @param {string} [params.unit] - The unit to be used for the CSS property\n * @param {Array.number} [params.breakpoints]  - An array of breakpoints\n * @param {number} [params.alignStep] - Round scaled value to fall under this grid\n * @returns {Object} responsive styles for {params.cssProperty}\n */\nexport function responsiveProperty({\n  cssProperty,\n  min,\n  max,\n  unit = 'rem',\n  breakpoints = [600, 900, 1200],\n  transform = null\n}) {\n  const output = {\n    [cssProperty]: `${min}${unit}`\n  };\n  const factor = (max - min) / breakpoints[breakpoints.length - 1];\n  breakpoints.forEach(breakpoint => {\n    let value = min + factor * breakpoint;\n    if (transform !== null) {\n      value = transform(value);\n    }\n    output[`@media (min-width:${breakpoint}px)`] = {\n      [cssProperty]: `${Math.round(value * 10000) / 10000}${unit}`\n    };\n  });\n  return output;\n}"], "mappings": "AAAA,OAAO,SAASA,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOC,MAAM,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC,CAACG,MAAM,KAAKF,MAAM,CAACD,KAAK,CAAC,CAACG,MAAM;AAClE;;AAEA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAOJ,MAAM,CAACI,KAAK,CAAC,CAACC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;AACzD;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACJ,MAAM,EAAE;EACjC,OAAOD,UAAU,CAACC,MAAM,CAAC;AAC3B;;AAEA;AACA;AACA,OAAO,SAASK,aAAaA,CAACC,YAAY,EAAE;EAC1C,OAAO,CAACN,MAAM,EAAEO,MAAM,KAAK;IACzB,MAAMC,QAAQ,GAAGP,OAAO,CAACD,MAAM,CAAC;;IAEhC;IACA,IAAIQ,QAAQ,KAAKD,MAAM,EAAE;MACvB,OAAOP,MAAM;IACf;;IAEA;IACA,IAAIS,QAAQ,GAAGL,UAAU,CAACJ,MAAM,CAAC;IACjC,IAAIQ,QAAQ,KAAK,IAAI,EAAE;MACrB,IAAIA,QAAQ,KAAK,IAAI,EAAE;QACrBC,QAAQ,GAAGL,UAAU,CAACJ,MAAM,CAAC,GAAGI,UAAU,CAACE,YAAY,CAAC;MAC1D,CAAC,MAAM,IAAIE,QAAQ,KAAK,KAAK,EAAE;QAC7BC,QAAQ,GAAGL,UAAU,CAACJ,MAAM,CAAC,GAAGI,UAAU,CAACE,YAAY,CAAC;MAC1D;IACF;;IAEA;IACA,IAAII,YAAY,GAAGD,QAAQ;IAC3B,IAAIF,MAAM,KAAK,IAAI,EAAE;MACnB,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnBG,YAAY,GAAGD,QAAQ,GAAGL,UAAU,CAACE,YAAY,CAAC;MACpD,CAAC,MAAM,IAAIC,MAAM,KAAK,KAAK,EAAE;QAC3BG,YAAY,GAAGD,QAAQ,GAAGL,UAAU,CAACE,YAAY,CAAC;MACpD,CAAC,MAAM;QACL,OAAON,MAAM;MACf;IACF;IACA,OAAOD,UAAU,CAACW,YAAY,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGJ,MAAM;EACrD,CAAC;AACH;AACA,OAAO,SAASK,aAAaA,CAAAC,IAAA,EAG1B;EAAA,IAH2B;IAC5BC,IAAI;IACJC;EACF,CAAC,GAAAF,IAAA;EACC,MAAMG,SAAS,GAAGF,IAAI,GAAGA,IAAI,GAAGC,IAAI;EACpC,MAAME,SAAS,GAAGD,SAAS,GAAGD,IAAI;EAClC,OAAOD,IAAI,GAAGE,SAAS,GAAGC,SAAS,GAAGH,IAAI,GAAGE,SAAS,GAAGC,SAAS;AACpE;;AAEA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAAAC,KAAA,EAIrB;EAAA,IAJsB;IACvBC,UAAU;IACVC,MAAM;IACNC;EACF,CAAC,GAAAH,KAAA;EACC,OAAOE,MAAM,IAAID,UAAU,GAAGE,YAAY,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAAAC,KAAA,EAO/B;EAAA,IAPgC;IACjCC,WAAW;IACXC,GAAG;IACHC,GAAG;IACHC,IAAI,GAAG,KAAK;IACZC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;IAC9BC,SAAS,GAAG;EACd,CAAC,GAAAN,KAAA;EACC,MAAMO,MAAM,GAAG;IACb,CAACN,WAAW,GAAG,GAAGC,GAAG,GAAGE,IAAI;EAC9B,CAAC;EACD,MAAMI,MAAM,GAAG,CAACL,GAAG,GAAGD,GAAG,IAAIG,WAAW,CAACA,WAAW,CAAC7B,MAAM,GAAG,CAAC,CAAC;EAChE6B,WAAW,CAACI,OAAO,CAACC,UAAU,IAAI;IAChC,IAAIrC,KAAK,GAAG6B,GAAG,GAAGM,MAAM,GAAGE,UAAU;IACrC,IAAIJ,SAAS,KAAK,IAAI,EAAE;MACtBjC,KAAK,GAAGiC,SAAS,CAACjC,KAAK,CAAC;IAC1B;IACAkC,MAAM,CAAC,qBAAqBG,UAAU,KAAK,CAAC,GAAG;MAC7C,CAACT,WAAW,GAAG,GAAGU,IAAI,CAACC,KAAK,CAACvC,KAAK,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG+B,IAAI;IAC5D,CAAC;EACH,CAAC,CAAC;EACF,OAAOG,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}