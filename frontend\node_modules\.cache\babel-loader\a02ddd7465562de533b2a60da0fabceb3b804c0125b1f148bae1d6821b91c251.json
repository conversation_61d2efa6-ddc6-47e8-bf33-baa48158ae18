{"ast": null, "code": "var _excluded = [\"children\"],\n  _excluded2 = [\"open\"],\n  _excluded3 = [\"refKey\", \"role\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onClick\", \"onDragEnter\", \"onDragOver\", \"onDragLeave\", \"onDrop\"],\n  _excluded4 = [\"refKey\", \"onChange\", \"onClick\"];\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n\n/* eslint prefer-template: 0 */\nimport React, { forwardRef, Fragment, useCallback, useEffect, useImperativeHandle, useMemo, useReducer, useRef } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { fromEvent } from \"file-selector\";\nimport { acceptPropAsAcceptAttr, allFilesAccepted, composeEventHandlers, fileAccepted, fileMatchSize, canUseFileSystemAccessAPI, isAbort, isEvtWithFiles, isIeOrEdge, isPropagationStopped, isSecurityError, onDocumentDragOver, pickerOptionsFromAccept, TOO_MANY_FILES_REJECTION } from \"./utils/index.js\";\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */\n\nvar Dropzone = /*#__PURE__*/forwardRef(function (_ref, ref) {\n  var children = _ref.children,\n    params = _objectWithoutProperties(_ref, _excluded);\n  var _useDropzone = useDropzone(params),\n    open = _useDropzone.open,\n    props = _objectWithoutProperties(_useDropzone, _excluded2);\n  useImperativeHandle(ref, function () {\n    return {\n      open: open\n    };\n  }, [open]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n\n  return /*#__PURE__*/React.createElement(Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n    open: open\n  })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\n\nvar defaultProps = {\n  disabled: false,\n  getFilesFromEvent: fromEvent,\n  maxSize: Infinity,\n  minSize: 0,\n  multiple: true,\n  maxFiles: 0,\n  preventDropOnDocument: true,\n  noClick: false,\n  noKeyboard: false,\n  noDrag: false,\n  noDragEventsBubbling: false,\n  validator: null,\n  useFsAccessApi: false,\n  autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n  /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */\n  children: PropTypes.func,\n  /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */\n  accept: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),\n  /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */\n  multiple: PropTypes.bool,\n  /**\n   * If false, allow dropped items to take over the current browser window\n   */\n  preventDropOnDocument: PropTypes.bool,\n  /**\n   * If true, disables click to open the native file selection dialog\n   */\n  noClick: PropTypes.bool,\n  /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */\n  noKeyboard: PropTypes.bool,\n  /**\n   * If true, disables drag 'n' drop\n   */\n  noDrag: PropTypes.bool,\n  /**\n   * If true, stops drag event propagation to parents\n   */\n  noDragEventsBubbling: PropTypes.bool,\n  /**\n   * Minimum file size (in bytes)\n   */\n  minSize: PropTypes.number,\n  /**\n   * Maximum file size (in bytes)\n   */\n  maxSize: PropTypes.number,\n  /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */\n  maxFiles: PropTypes.number,\n  /**\n   * Enable/disable the dropzone\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  getFilesFromEvent: PropTypes.func,\n  /**\n   * Cb for when closing the file dialog with no selection\n   */\n  onFileDialogCancel: PropTypes.func,\n  /**\n   * Cb for when opening the file dialog\n   */\n  onFileDialogOpen: PropTypes.func,\n  /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */\n  useFsAccessApi: PropTypes.bool,\n  /**\n   * Set to true to focus the root element on render\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */\n  onDragEnter: PropTypes.func,\n  /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragLeave: PropTypes.func,\n  /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragOver: PropTypes.func,\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  onDrop: PropTypes.func,\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */\n  onDropAccepted: PropTypes.func,\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */\n  onDropRejected: PropTypes.func,\n  /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */\n  onError: PropTypes.func,\n  /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */\n  validator: PropTypes.func\n};\nexport default Dropzone;\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */\n\n/**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */\n\n/**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */\n\nvar initialState = {\n  isFocused: false,\n  isFileDialogActive: false,\n  isDragActive: false,\n  isDragAccept: false,\n  isDragReject: false,\n  acceptedFiles: [],\n  fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */\n\nexport function useDropzone() {\n  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n    accept = _defaultProps$props.accept,\n    disabled = _defaultProps$props.disabled,\n    getFilesFromEvent = _defaultProps$props.getFilesFromEvent,\n    maxSize = _defaultProps$props.maxSize,\n    minSize = _defaultProps$props.minSize,\n    multiple = _defaultProps$props.multiple,\n    maxFiles = _defaultProps$props.maxFiles,\n    onDragEnter = _defaultProps$props.onDragEnter,\n    onDragLeave = _defaultProps$props.onDragLeave,\n    onDragOver = _defaultProps$props.onDragOver,\n    onDrop = _defaultProps$props.onDrop,\n    onDropAccepted = _defaultProps$props.onDropAccepted,\n    onDropRejected = _defaultProps$props.onDropRejected,\n    onFileDialogCancel = _defaultProps$props.onFileDialogCancel,\n    onFileDialogOpen = _defaultProps$props.onFileDialogOpen,\n    useFsAccessApi = _defaultProps$props.useFsAccessApi,\n    autoFocus = _defaultProps$props.autoFocus,\n    preventDropOnDocument = _defaultProps$props.preventDropOnDocument,\n    noClick = _defaultProps$props.noClick,\n    noKeyboard = _defaultProps$props.noKeyboard,\n    noDrag = _defaultProps$props.noDrag,\n    noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling,\n    onError = _defaultProps$props.onError,\n    validator = _defaultProps$props.validator;\n  var acceptAttr = useMemo(function () {\n    return acceptPropAsAcceptAttr(accept);\n  }, [accept]);\n  var pickerTypes = useMemo(function () {\n    return pickerOptionsFromAccept(accept);\n  }, [accept]);\n  var onFileDialogOpenCb = useMemo(function () {\n    return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n  }, [onFileDialogOpen]);\n  var onFileDialogCancelCb = useMemo(function () {\n    return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n  }, [onFileDialogCancel]);\n  /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */\n\n  var rootRef = useRef(null);\n  var inputRef = useRef(null);\n  var _useReducer = useReducer(reducer, initialState),\n    _useReducer2 = _slicedToArray(_useReducer, 2),\n    state = _useReducer2[0],\n    dispatch = _useReducer2[1];\n  var isFocused = state.isFocused,\n    isFileDialogActive = state.isFileDialogActive;\n  var fsAccessApiWorksRef = useRef(typeof window !== \"undefined\" && window.isSecureContext && useFsAccessApi && canUseFileSystemAccessAPI()); // Update file dialog active state when the window is focused on\n\n  var onWindowFocus = function onWindowFocus() {\n    // Execute the timeout only if the file dialog is opened in the browser\n    if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n      setTimeout(function () {\n        if (inputRef.current) {\n          var files = inputRef.current.files;\n          if (!files.length) {\n            dispatch({\n              type: \"closeDialog\"\n            });\n            onFileDialogCancelCb();\n          }\n        }\n      }, 300);\n    }\n  };\n  useEffect(function () {\n    window.addEventListener(\"focus\", onWindowFocus, false);\n    return function () {\n      window.removeEventListener(\"focus\", onWindowFocus, false);\n    };\n  }, [inputRef, isFileDialogActive, onFileDialogCancelCb, fsAccessApiWorksRef]);\n  var dragTargetsRef = useRef([]);\n  var onDocumentDrop = function onDocumentDrop(event) {\n    if (rootRef.current && rootRef.current.contains(event.target)) {\n      // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n      return;\n    }\n    event.preventDefault();\n    dragTargetsRef.current = [];\n  };\n  useEffect(function () {\n    if (preventDropOnDocument) {\n      document.addEventListener(\"dragover\", onDocumentDragOver, false);\n      document.addEventListener(\"drop\", onDocumentDrop, false);\n    }\n    return function () {\n      if (preventDropOnDocument) {\n        document.removeEventListener(\"dragover\", onDocumentDragOver);\n        document.removeEventListener(\"drop\", onDocumentDrop);\n      }\n    };\n  }, [rootRef, preventDropOnDocument]); // Auto focus the root when autoFocus is true\n\n  useEffect(function () {\n    if (!disabled && autoFocus && rootRef.current) {\n      rootRef.current.focus();\n    }\n    return function () {};\n  }, [rootRef, autoFocus, disabled]);\n  var onErrCb = useCallback(function (e) {\n    if (onError) {\n      onError(e);\n    } else {\n      // Let the user know something's gone wrong if they haven't provided the onError cb.\n      console.error(e);\n    }\n  }, [onError]);\n  var onDragEnterCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [event.target]);\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n        var fileCount = files.length;\n        var isDragAccept = fileCount > 0 && allFilesAccepted({\n          files: files,\n          accept: acceptAttr,\n          minSize: minSize,\n          maxSize: maxSize,\n          multiple: multiple,\n          maxFiles: maxFiles,\n          validator: validator\n        });\n        var isDragReject = fileCount > 0 && !isDragAccept;\n        dispatch({\n          isDragAccept: isDragAccept,\n          isDragReject: isDragReject,\n          isDragActive: true,\n          type: \"setDraggedFiles\"\n        });\n        if (onDragEnter) {\n          onDragEnter(event);\n        }\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n  }, [getFilesFromEvent, onDragEnter, onErrCb, noDragEventsBubbling, acceptAttr, minSize, maxSize, multiple, maxFiles, validator]);\n  var onDragOverCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event);\n    var hasFiles = isEvtWithFiles(event);\n    if (hasFiles && event.dataTransfer) {\n      try {\n        event.dataTransfer.dropEffect = \"copy\";\n      } catch (_unused) {}\n      /* eslint-disable-line no-empty */\n    }\n    if (hasFiles && onDragOver) {\n      onDragOver(event);\n    }\n    return false;\n  }, [onDragOver, noDragEventsBubbling]);\n  var onDragLeaveCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n\n    var targets = dragTargetsRef.current.filter(function (target) {\n      return rootRef.current && rootRef.current.contains(target);\n    }); // Make sure to remove a target present multiple times only once\n    // (Firefox may fire dragenter/dragleave multiple times on the same element)\n\n    var targetIdx = targets.indexOf(event.target);\n    if (targetIdx !== -1) {\n      targets.splice(targetIdx, 1);\n    }\n    dragTargetsRef.current = targets;\n    if (targets.length > 0) {\n      return;\n    }\n    dispatch({\n      type: \"setDraggedFiles\",\n      isDragActive: false,\n      isDragAccept: false,\n      isDragReject: false\n    });\n    if (isEvtWithFiles(event) && onDragLeave) {\n      onDragLeave(event);\n    }\n  }, [rootRef, onDragLeave, noDragEventsBubbling]);\n  var setFiles = useCallback(function (files, event) {\n    var acceptedFiles = [];\n    var fileRejections = [];\n    files.forEach(function (file) {\n      var _fileAccepted = fileAccepted(file, acceptAttr),\n        _fileAccepted2 = _slicedToArray(_fileAccepted, 2),\n        accepted = _fileAccepted2[0],\n        acceptError = _fileAccepted2[1];\n      var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n        _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2),\n        sizeMatch = _fileMatchSize2[0],\n        sizeError = _fileMatchSize2[1];\n      var customErrors = validator ? validator(file) : null;\n      if (accepted && sizeMatch && !customErrors) {\n        acceptedFiles.push(file);\n      } else {\n        var errors = [acceptError, sizeError];\n        if (customErrors) {\n          errors = errors.concat(customErrors);\n        }\n        fileRejections.push({\n          file: file,\n          errors: errors.filter(function (e) {\n            return e;\n          })\n        });\n      }\n    });\n    if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n      // Reject everything and empty accepted files\n      acceptedFiles.forEach(function (file) {\n        fileRejections.push({\n          file: file,\n          errors: [TOO_MANY_FILES_REJECTION]\n        });\n      });\n      acceptedFiles.splice(0);\n    }\n    dispatch({\n      acceptedFiles: acceptedFiles,\n      fileRejections: fileRejections,\n      isDragReject: fileRejections.length > 0,\n      type: \"setFiles\"\n    });\n    if (onDrop) {\n      onDrop(acceptedFiles, fileRejections, event);\n    }\n    if (fileRejections.length > 0 && onDropRejected) {\n      onDropRejected(fileRejections, event);\n    }\n    if (acceptedFiles.length > 0 && onDropAccepted) {\n      onDropAccepted(acceptedFiles, event);\n    }\n  }, [dispatch, multiple, acceptAttr, minSize, maxSize, maxFiles, onDrop, onDropAccepted, onDropRejected, validator]);\n  var onDropCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [];\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n        setFiles(files, event);\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n    dispatch({\n      type: \"reset\"\n    });\n  }, [getFilesFromEvent, setFiles, onErrCb, noDragEventsBubbling]); // Fn for opening the file dialog programmatically\n\n  var openFileDialog = useCallback(function () {\n    // No point to use FS access APIs if context is not secure\n    // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n    if (fsAccessApiWorksRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n\n      var opts = {\n        multiple: multiple,\n        types: pickerTypes\n      };\n      window.showOpenFilePicker(opts).then(function (handles) {\n        return getFilesFromEvent(handles);\n      }).then(function (files) {\n        setFiles(files, null);\n        dispatch({\n          type: \"closeDialog\"\n        });\n      }).catch(function (e) {\n        // AbortError means the user canceled\n        if (isAbort(e)) {\n          onFileDialogCancelCb(e);\n          dispatch({\n            type: \"closeDialog\"\n          });\n        } else if (isSecurityError(e)) {\n          fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n          // Try using the input\n\n          if (inputRef.current) {\n            inputRef.current.value = null;\n            inputRef.current.click();\n          } else {\n            onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n          }\n        } else {\n          onErrCb(e);\n        }\n      });\n      return;\n    }\n    if (inputRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb();\n      inputRef.current.value = null;\n      inputRef.current.click();\n    }\n  }, [dispatch, onFileDialogOpenCb, onFileDialogCancelCb, useFsAccessApi, setFiles, onErrCb, pickerTypes, multiple]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n\n  var onKeyDownCb = useCallback(function (event) {\n    // Ignore keyboard events bubbling up the DOM tree\n    if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n      return;\n    }\n    if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n      event.preventDefault();\n      openFileDialog();\n    }\n  }, [rootRef, openFileDialog]); // Update focus state for the dropzone\n\n  var onFocusCb = useCallback(function () {\n    dispatch({\n      type: \"focus\"\n    });\n  }, []);\n  var onBlurCb = useCallback(function () {\n    dispatch({\n      type: \"blur\"\n    });\n  }, []); // Cb to open the file dialog when click occurs on the dropzone\n\n  var onClickCb = useCallback(function () {\n    if (noClick) {\n      return;\n    } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n    // to ensure React can handle state changes\n    // See: https://github.com/react-dropzone/react-dropzone/issues/450\n\n    if (isIeOrEdge()) {\n      setTimeout(openFileDialog, 0);\n    } else {\n      openFileDialog();\n    }\n  }, [noClick, openFileDialog]);\n  var composeHandler = function composeHandler(fn) {\n    return disabled ? null : fn;\n  };\n  var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n    return noKeyboard ? null : composeHandler(fn);\n  };\n  var composeDragHandler = function composeDragHandler(fn) {\n    return noDrag ? null : composeHandler(fn);\n  };\n  var stopPropagation = function stopPropagation(event) {\n    if (noDragEventsBubbling) {\n      event.stopPropagation();\n    }\n  };\n  var getRootProps = useMemo(function () {\n    return function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n        _ref2$refKey = _ref2.refKey,\n        refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey,\n        role = _ref2.role,\n        onKeyDown = _ref2.onKeyDown,\n        onFocus = _ref2.onFocus,\n        onBlur = _ref2.onBlur,\n        onClick = _ref2.onClick,\n        onDragEnter = _ref2.onDragEnter,\n        onDragOver = _ref2.onDragOver,\n        onDragLeave = _ref2.onDragLeave,\n        onDrop = _ref2.onDrop,\n        rest = _objectWithoutProperties(_ref2, _excluded3);\n      return _objectSpread(_objectSpread(_defineProperty({\n        onKeyDown: composeKeyboardHandler(composeEventHandlers(onKeyDown, onKeyDownCb)),\n        onFocus: composeKeyboardHandler(composeEventHandlers(onFocus, onFocusCb)),\n        onBlur: composeKeyboardHandler(composeEventHandlers(onBlur, onBlurCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onClickCb)),\n        onDragEnter: composeDragHandler(composeEventHandlers(onDragEnter, onDragEnterCb)),\n        onDragOver: composeDragHandler(composeEventHandlers(onDragOver, onDragOverCb)),\n        onDragLeave: composeDragHandler(composeEventHandlers(onDragLeave, onDragLeaveCb)),\n        onDrop: composeDragHandler(composeEventHandlers(onDrop, onDropCb)),\n        role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n      }, refKey, rootRef), !disabled && !noKeyboard ? {\n        tabIndex: 0\n      } : {}), rest);\n    };\n  }, [rootRef, onKeyDownCb, onFocusCb, onBlurCb, onClickCb, onDragEnterCb, onDragOverCb, onDragLeaveCb, onDropCb, noKeyboard, noDrag, disabled]);\n  var onInputElementClick = useCallback(function (event) {\n    event.stopPropagation();\n  }, []);\n  var getInputProps = useMemo(function () {\n    return function () {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n        _ref3$refKey = _ref3.refKey,\n        refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey,\n        onChange = _ref3.onChange,\n        onClick = _ref3.onClick,\n        rest = _objectWithoutProperties(_ref3, _excluded4);\n      var inputProps = _defineProperty({\n        accept: acceptAttr,\n        multiple: multiple,\n        type: \"file\",\n        style: {\n          border: 0,\n          clip: \"rect(0, 0, 0, 0)\",\n          clipPath: \"inset(50%)\",\n          height: \"1px\",\n          margin: \"0 -1px -1px 0\",\n          overflow: \"hidden\",\n          padding: 0,\n          position: \"absolute\",\n          width: \"1px\",\n          whiteSpace: \"nowrap\"\n        },\n        onChange: composeHandler(composeEventHandlers(onChange, onDropCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onInputElementClick)),\n        tabIndex: -1\n      }, refKey, inputRef);\n      return _objectSpread(_objectSpread({}, inputProps), rest);\n    };\n  }, [inputRef, accept, multiple, onDropCb, disabled]);\n  return _objectSpread(_objectSpread({}, state), {}, {\n    isFocused: isFocused && !disabled,\n    getRootProps: getRootProps,\n    getInputProps: getInputProps,\n    rootRef: rootRef,\n    inputRef: inputRef,\n    open: composeHandler(openFileDialog)\n  });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */\n\nfunction reducer(state, action) {\n  /* istanbul ignore next */\n  switch (action.type) {\n    case \"focus\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: true\n      });\n    case \"blur\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: false\n      });\n    case \"openDialog\":\n      return _objectSpread(_objectSpread({}, initialState), {}, {\n        isFileDialogActive: true\n      });\n    case \"closeDialog\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFileDialogActive: false\n      });\n    case \"setDraggedFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isDragActive: action.isDragActive,\n        isDragAccept: action.isDragAccept,\n        isDragReject: action.isDragReject\n      });\n    case \"setFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        acceptedFiles: action.acceptedFiles,\n        fileRejections: action.fileRejections,\n        isDragReject: action.isDragReject\n      });\n    case \"reset\":\n      return _objectSpread({}, initialState);\n    default:\n      return state;\n  }\n}\nfunction noop() {}\nexport { ErrorCode } from \"./utils/index.js\";", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_excluded3", "_excluded4", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "iter", "Symbol", "iterator", "Array", "from", "isArray", "_arrayLikeToArray", "_slicedToArray", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_nonIterableRest", "o", "minLen", "n", "Object", "prototype", "toString", "call", "slice", "constructor", "name", "test", "len", "length", "arr2", "_i", "_arr", "_n", "_d", "_s", "_e", "next", "done", "push", "value", "err", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "apply", "_objectSpread", "target", "arguments", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "configurable", "writable", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "sourceKeys", "React", "forwardRef", "Fragment", "useCallback", "useEffect", "useImperativeHandle", "useMemo", "useReducer", "useRef", "PropTypes", "fromEvent", "acceptPropAsAcceptAttr", "allFilesAccepted", "composeEventHandlers", "fileAccepted", "fileMatchSize", "canUseFileSystemAccessAPI", "isAbort", "isEvtWithFiles", "isIeOrEdge", "isPropagationStopped", "isSecurityError", "onDocumentDragOver", "pickerOptionsFromAccept", "TOO_MANY_FILES_REJECTION", "Dropzone", "_ref", "ref", "children", "params", "_useDropzone", "useDropzone", "open", "props", "createElement", "displayName", "defaultProps", "disabled", "getFilesFromEvent", "maxSize", "Infinity", "minSize", "multiple", "maxFiles", "preventDropOnDocument", "noClick", "noKeyboard", "noDrag", "noDragEventsBubbling", "validator", "useFsAccessApi", "autoFocus", "propTypes", "func", "accept", "objectOf", "arrayOf", "string", "bool", "number", "onFileDialogCancel", "onFileDialogOpen", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "onDropAccepted", "onDropRejected", "onError", "initialState", "isFocused", "isFileDialogActive", "isDragActive", "isDragAccept", "isDragReject", "acceptedFiles", "fileRejections", "undefined", "_defaultProps$props", "acceptAttr", "pickerTypes", "onFileDialogOpenCb", "noop", "onFileDialogCancelCb", "rootRef", "inputRef", "_useReducer", "reducer", "_useReducer2", "state", "dispatch", "fsAccessApiWorksRef", "window", "isSecureContext", "onWindowFocus", "current", "setTimeout", "files", "type", "addEventListener", "removeEventListener", "dragTargetsRef", "onDocumentDrop", "event", "contains", "preventDefault", "document", "focus", "onErrCb", "e", "console", "error", "onDragEnterCb", "persist", "stopPropagation", "concat", "Promise", "resolve", "then", "fileCount", "catch", "onDragOverCb", "hasFiles", "dataTransfer", "dropEffect", "_unused", "onDragLeaveCb", "targets", "targetIdx", "splice", "setFiles", "file", "_fileAccepted", "_fileAccepted2", "accepted", "acceptError", "_fileMatchSize", "_fileMatchSize2", "sizeMatch", "sizeError", "customErrors", "errors", "onDropCb", "openFileDialog", "opts", "types", "showOpenFilePicker", "handles", "click", "Error", "onKeyDownCb", "isEqualNode", "keyCode", "onFocusCb", "onBlurCb", "onClickCb", "<PERSON><PERSON><PERSON><PERSON>", "fn", "composeKeyboardHandler", "composeDragHandler", "getRootProps", "_ref2", "_ref2$refKey", "refKey", "role", "onKeyDown", "onFocus", "onBlur", "onClick", "rest", "tabIndex", "onInputElementClick", "getInputProps", "_ref3", "_ref3$refKey", "onChange", "inputProps", "style", "border", "clip", "clipPath", "height", "margin", "overflow", "padding", "position", "width", "whiteSpace", "action", "ErrorCode"], "sources": ["C:/laragon/www/frontend/node_modules/react-dropzone/dist/es/index.js"], "sourcesContent": ["var _excluded = [\"children\"],\n    _excluded2 = [\"open\"],\n    _excluded3 = [\"refKey\", \"role\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onClick\", \"onDragEnter\", \"onDragOver\", \"onDragLeave\", \"onDrop\"],\n    _excluded4 = [\"refKey\", \"onChange\", \"onClick\"];\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n/* eslint prefer-template: 0 */\nimport React, { forwardRef, Fragment, useCallback, useEffect, useImperativeHandle, useMemo, useReducer, useRef } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { fromEvent } from \"file-selector\";\nimport { acceptPropAsAcceptAttr, allFilesAccepted, composeEventHandlers, fileAccepted, fileMatchSize, canUseFileSystemAccessAPI, isAbort, isEvtWithFiles, isIeOrEdge, isPropagationStopped, isSecurityError, onDocumentDragOver, pickerOptionsFromAccept, TOO_MANY_FILES_REJECTION } from \"./utils/index.js\";\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */\n\nvar Dropzone = /*#__PURE__*/forwardRef(function (_ref, ref) {\n  var children = _ref.children,\n      params = _objectWithoutProperties(_ref, _excluded);\n\n  var _useDropzone = useDropzone(params),\n      open = _useDropzone.open,\n      props = _objectWithoutProperties(_useDropzone, _excluded2);\n\n  useImperativeHandle(ref, function () {\n    return {\n      open: open\n    };\n  }, [open]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n\n  return /*#__PURE__*/React.createElement(Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n    open: open\n  })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\n\nvar defaultProps = {\n  disabled: false,\n  getFilesFromEvent: fromEvent,\n  maxSize: Infinity,\n  minSize: 0,\n  multiple: true,\n  maxFiles: 0,\n  preventDropOnDocument: true,\n  noClick: false,\n  noKeyboard: false,\n  noDrag: false,\n  noDragEventsBubbling: false,\n  validator: null,\n  useFsAccessApi: false,\n  autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n  /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */\n  children: PropTypes.func,\n\n  /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */\n  accept: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),\n\n  /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */\n  multiple: PropTypes.bool,\n\n  /**\n   * If false, allow dropped items to take over the current browser window\n   */\n  preventDropOnDocument: PropTypes.bool,\n\n  /**\n   * If true, disables click to open the native file selection dialog\n   */\n  noClick: PropTypes.bool,\n\n  /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */\n  noKeyboard: PropTypes.bool,\n\n  /**\n   * If true, disables drag 'n' drop\n   */\n  noDrag: PropTypes.bool,\n\n  /**\n   * If true, stops drag event propagation to parents\n   */\n  noDragEventsBubbling: PropTypes.bool,\n\n  /**\n   * Minimum file size (in bytes)\n   */\n  minSize: PropTypes.number,\n\n  /**\n   * Maximum file size (in bytes)\n   */\n  maxSize: PropTypes.number,\n\n  /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */\n  maxFiles: PropTypes.number,\n\n  /**\n   * Enable/disable the dropzone\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  getFilesFromEvent: PropTypes.func,\n\n  /**\n   * Cb for when closing the file dialog with no selection\n   */\n  onFileDialogCancel: PropTypes.func,\n\n  /**\n   * Cb for when opening the file dialog\n   */\n  onFileDialogOpen: PropTypes.func,\n\n  /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */\n  useFsAccessApi: PropTypes.bool,\n\n  /**\n   * Set to true to focus the root element on render\n   */\n  autoFocus: PropTypes.bool,\n\n  /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */\n  onDragEnter: PropTypes.func,\n\n  /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragLeave: PropTypes.func,\n\n  /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragOver: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  onDrop: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */\n  onDropAccepted: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */\n  onDropRejected: PropTypes.func,\n\n  /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */\n  onError: PropTypes.func,\n\n  /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */\n  validator: PropTypes.func\n};\nexport default Dropzone;\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */\n\n/**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */\n\n/**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */\n\nvar initialState = {\n  isFocused: false,\n  isFileDialogActive: false,\n  isDragActive: false,\n  isDragAccept: false,\n  isDragReject: false,\n  acceptedFiles: [],\n  fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */\n\nexport function useDropzone() {\n  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n      accept = _defaultProps$props.accept,\n      disabled = _defaultProps$props.disabled,\n      getFilesFromEvent = _defaultProps$props.getFilesFromEvent,\n      maxSize = _defaultProps$props.maxSize,\n      minSize = _defaultProps$props.minSize,\n      multiple = _defaultProps$props.multiple,\n      maxFiles = _defaultProps$props.maxFiles,\n      onDragEnter = _defaultProps$props.onDragEnter,\n      onDragLeave = _defaultProps$props.onDragLeave,\n      onDragOver = _defaultProps$props.onDragOver,\n      onDrop = _defaultProps$props.onDrop,\n      onDropAccepted = _defaultProps$props.onDropAccepted,\n      onDropRejected = _defaultProps$props.onDropRejected,\n      onFileDialogCancel = _defaultProps$props.onFileDialogCancel,\n      onFileDialogOpen = _defaultProps$props.onFileDialogOpen,\n      useFsAccessApi = _defaultProps$props.useFsAccessApi,\n      autoFocus = _defaultProps$props.autoFocus,\n      preventDropOnDocument = _defaultProps$props.preventDropOnDocument,\n      noClick = _defaultProps$props.noClick,\n      noKeyboard = _defaultProps$props.noKeyboard,\n      noDrag = _defaultProps$props.noDrag,\n      noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling,\n      onError = _defaultProps$props.onError,\n      validator = _defaultProps$props.validator;\n\n  var acceptAttr = useMemo(function () {\n    return acceptPropAsAcceptAttr(accept);\n  }, [accept]);\n  var pickerTypes = useMemo(function () {\n    return pickerOptionsFromAccept(accept);\n  }, [accept]);\n  var onFileDialogOpenCb = useMemo(function () {\n    return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n  }, [onFileDialogOpen]);\n  var onFileDialogCancelCb = useMemo(function () {\n    return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n  }, [onFileDialogCancel]);\n  /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */\n\n  var rootRef = useRef(null);\n  var inputRef = useRef(null);\n\n  var _useReducer = useReducer(reducer, initialState),\n      _useReducer2 = _slicedToArray(_useReducer, 2),\n      state = _useReducer2[0],\n      dispatch = _useReducer2[1];\n\n  var isFocused = state.isFocused,\n      isFileDialogActive = state.isFileDialogActive;\n  var fsAccessApiWorksRef = useRef(typeof window !== \"undefined\" && window.isSecureContext && useFsAccessApi && canUseFileSystemAccessAPI()); // Update file dialog active state when the window is focused on\n\n  var onWindowFocus = function onWindowFocus() {\n    // Execute the timeout only if the file dialog is opened in the browser\n    if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n      setTimeout(function () {\n        if (inputRef.current) {\n          var files = inputRef.current.files;\n\n          if (!files.length) {\n            dispatch({\n              type: \"closeDialog\"\n            });\n            onFileDialogCancelCb();\n          }\n        }\n      }, 300);\n    }\n  };\n\n  useEffect(function () {\n    window.addEventListener(\"focus\", onWindowFocus, false);\n    return function () {\n      window.removeEventListener(\"focus\", onWindowFocus, false);\n    };\n  }, [inputRef, isFileDialogActive, onFileDialogCancelCb, fsAccessApiWorksRef]);\n  var dragTargetsRef = useRef([]);\n\n  var onDocumentDrop = function onDocumentDrop(event) {\n    if (rootRef.current && rootRef.current.contains(event.target)) {\n      // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n      return;\n    }\n\n    event.preventDefault();\n    dragTargetsRef.current = [];\n  };\n\n  useEffect(function () {\n    if (preventDropOnDocument) {\n      document.addEventListener(\"dragover\", onDocumentDragOver, false);\n      document.addEventListener(\"drop\", onDocumentDrop, false);\n    }\n\n    return function () {\n      if (preventDropOnDocument) {\n        document.removeEventListener(\"dragover\", onDocumentDragOver);\n        document.removeEventListener(\"drop\", onDocumentDrop);\n      }\n    };\n  }, [rootRef, preventDropOnDocument]); // Auto focus the root when autoFocus is true\n\n  useEffect(function () {\n    if (!disabled && autoFocus && rootRef.current) {\n      rootRef.current.focus();\n    }\n\n    return function () {};\n  }, [rootRef, autoFocus, disabled]);\n  var onErrCb = useCallback(function (e) {\n    if (onError) {\n      onError(e);\n    } else {\n      // Let the user know something's gone wrong if they haven't provided the onError cb.\n      console.error(e);\n    }\n  }, [onError]);\n  var onDragEnterCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [event.target]);\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        var fileCount = files.length;\n        var isDragAccept = fileCount > 0 && allFilesAccepted({\n          files: files,\n          accept: acceptAttr,\n          minSize: minSize,\n          maxSize: maxSize,\n          multiple: multiple,\n          maxFiles: maxFiles,\n          validator: validator\n        });\n        var isDragReject = fileCount > 0 && !isDragAccept;\n        dispatch({\n          isDragAccept: isDragAccept,\n          isDragReject: isDragReject,\n          isDragActive: true,\n          type: \"setDraggedFiles\"\n        });\n\n        if (onDragEnter) {\n          onDragEnter(event);\n        }\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n  }, [getFilesFromEvent, onDragEnter, onErrCb, noDragEventsBubbling, acceptAttr, minSize, maxSize, multiple, maxFiles, validator]);\n  var onDragOverCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event);\n    var hasFiles = isEvtWithFiles(event);\n\n    if (hasFiles && event.dataTransfer) {\n      try {\n        event.dataTransfer.dropEffect = \"copy\";\n      } catch (_unused) {}\n      /* eslint-disable-line no-empty */\n\n    }\n\n    if (hasFiles && onDragOver) {\n      onDragOver(event);\n    }\n\n    return false;\n  }, [onDragOver, noDragEventsBubbling]);\n  var onDragLeaveCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n\n    var targets = dragTargetsRef.current.filter(function (target) {\n      return rootRef.current && rootRef.current.contains(target);\n    }); // Make sure to remove a target present multiple times only once\n    // (Firefox may fire dragenter/dragleave multiple times on the same element)\n\n    var targetIdx = targets.indexOf(event.target);\n\n    if (targetIdx !== -1) {\n      targets.splice(targetIdx, 1);\n    }\n\n    dragTargetsRef.current = targets;\n\n    if (targets.length > 0) {\n      return;\n    }\n\n    dispatch({\n      type: \"setDraggedFiles\",\n      isDragActive: false,\n      isDragAccept: false,\n      isDragReject: false\n    });\n\n    if (isEvtWithFiles(event) && onDragLeave) {\n      onDragLeave(event);\n    }\n  }, [rootRef, onDragLeave, noDragEventsBubbling]);\n  var setFiles = useCallback(function (files, event) {\n    var acceptedFiles = [];\n    var fileRejections = [];\n    files.forEach(function (file) {\n      var _fileAccepted = fileAccepted(file, acceptAttr),\n          _fileAccepted2 = _slicedToArray(_fileAccepted, 2),\n          accepted = _fileAccepted2[0],\n          acceptError = _fileAccepted2[1];\n\n      var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n          _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2),\n          sizeMatch = _fileMatchSize2[0],\n          sizeError = _fileMatchSize2[1];\n\n      var customErrors = validator ? validator(file) : null;\n\n      if (accepted && sizeMatch && !customErrors) {\n        acceptedFiles.push(file);\n      } else {\n        var errors = [acceptError, sizeError];\n\n        if (customErrors) {\n          errors = errors.concat(customErrors);\n        }\n\n        fileRejections.push({\n          file: file,\n          errors: errors.filter(function (e) {\n            return e;\n          })\n        });\n      }\n    });\n\n    if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n      // Reject everything and empty accepted files\n      acceptedFiles.forEach(function (file) {\n        fileRejections.push({\n          file: file,\n          errors: [TOO_MANY_FILES_REJECTION]\n        });\n      });\n      acceptedFiles.splice(0);\n    }\n\n    dispatch({\n      acceptedFiles: acceptedFiles,\n      fileRejections: fileRejections,\n      isDragReject: fileRejections.length > 0,\n      type: \"setFiles\"\n    });\n\n    if (onDrop) {\n      onDrop(acceptedFiles, fileRejections, event);\n    }\n\n    if (fileRejections.length > 0 && onDropRejected) {\n      onDropRejected(fileRejections, event);\n    }\n\n    if (acceptedFiles.length > 0 && onDropAccepted) {\n      onDropAccepted(acceptedFiles, event);\n    }\n  }, [dispatch, multiple, acceptAttr, minSize, maxSize, maxFiles, onDrop, onDropAccepted, onDropRejected, validator]);\n  var onDropCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [];\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        setFiles(files, event);\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n\n    dispatch({\n      type: \"reset\"\n    });\n  }, [getFilesFromEvent, setFiles, onErrCb, noDragEventsBubbling]); // Fn for opening the file dialog programmatically\n\n  var openFileDialog = useCallback(function () {\n    // No point to use FS access APIs if context is not secure\n    // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n    if (fsAccessApiWorksRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n\n      var opts = {\n        multiple: multiple,\n        types: pickerTypes\n      };\n      window.showOpenFilePicker(opts).then(function (handles) {\n        return getFilesFromEvent(handles);\n      }).then(function (files) {\n        setFiles(files, null);\n        dispatch({\n          type: \"closeDialog\"\n        });\n      }).catch(function (e) {\n        // AbortError means the user canceled\n        if (isAbort(e)) {\n          onFileDialogCancelCb(e);\n          dispatch({\n            type: \"closeDialog\"\n          });\n        } else if (isSecurityError(e)) {\n          fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n          // Try using the input\n\n          if (inputRef.current) {\n            inputRef.current.value = null;\n            inputRef.current.click();\n          } else {\n            onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n          }\n        } else {\n          onErrCb(e);\n        }\n      });\n      return;\n    }\n\n    if (inputRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb();\n      inputRef.current.value = null;\n      inputRef.current.click();\n    }\n  }, [dispatch, onFileDialogOpenCb, onFileDialogCancelCb, useFsAccessApi, setFiles, onErrCb, pickerTypes, multiple]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n\n  var onKeyDownCb = useCallback(function (event) {\n    // Ignore keyboard events bubbling up the DOM tree\n    if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n      return;\n    }\n\n    if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n      event.preventDefault();\n      openFileDialog();\n    }\n  }, [rootRef, openFileDialog]); // Update focus state for the dropzone\n\n  var onFocusCb = useCallback(function () {\n    dispatch({\n      type: \"focus\"\n    });\n  }, []);\n  var onBlurCb = useCallback(function () {\n    dispatch({\n      type: \"blur\"\n    });\n  }, []); // Cb to open the file dialog when click occurs on the dropzone\n\n  var onClickCb = useCallback(function () {\n    if (noClick) {\n      return;\n    } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n    // to ensure React can handle state changes\n    // See: https://github.com/react-dropzone/react-dropzone/issues/450\n\n\n    if (isIeOrEdge()) {\n      setTimeout(openFileDialog, 0);\n    } else {\n      openFileDialog();\n    }\n  }, [noClick, openFileDialog]);\n\n  var composeHandler = function composeHandler(fn) {\n    return disabled ? null : fn;\n  };\n\n  var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n    return noKeyboard ? null : composeHandler(fn);\n  };\n\n  var composeDragHandler = function composeDragHandler(fn) {\n    return noDrag ? null : composeHandler(fn);\n  };\n\n  var stopPropagation = function stopPropagation(event) {\n    if (noDragEventsBubbling) {\n      event.stopPropagation();\n    }\n  };\n\n  var getRootProps = useMemo(function () {\n    return function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref2$refKey = _ref2.refKey,\n          refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey,\n          role = _ref2.role,\n          onKeyDown = _ref2.onKeyDown,\n          onFocus = _ref2.onFocus,\n          onBlur = _ref2.onBlur,\n          onClick = _ref2.onClick,\n          onDragEnter = _ref2.onDragEnter,\n          onDragOver = _ref2.onDragOver,\n          onDragLeave = _ref2.onDragLeave,\n          onDrop = _ref2.onDrop,\n          rest = _objectWithoutProperties(_ref2, _excluded3);\n\n      return _objectSpread(_objectSpread(_defineProperty({\n        onKeyDown: composeKeyboardHandler(composeEventHandlers(onKeyDown, onKeyDownCb)),\n        onFocus: composeKeyboardHandler(composeEventHandlers(onFocus, onFocusCb)),\n        onBlur: composeKeyboardHandler(composeEventHandlers(onBlur, onBlurCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onClickCb)),\n        onDragEnter: composeDragHandler(composeEventHandlers(onDragEnter, onDragEnterCb)),\n        onDragOver: composeDragHandler(composeEventHandlers(onDragOver, onDragOverCb)),\n        onDragLeave: composeDragHandler(composeEventHandlers(onDragLeave, onDragLeaveCb)),\n        onDrop: composeDragHandler(composeEventHandlers(onDrop, onDropCb)),\n        role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n      }, refKey, rootRef), !disabled && !noKeyboard ? {\n        tabIndex: 0\n      } : {}), rest);\n    };\n  }, [rootRef, onKeyDownCb, onFocusCb, onBlurCb, onClickCb, onDragEnterCb, onDragOverCb, onDragLeaveCb, onDropCb, noKeyboard, noDrag, disabled]);\n  var onInputElementClick = useCallback(function (event) {\n    event.stopPropagation();\n  }, []);\n  var getInputProps = useMemo(function () {\n    return function () {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref3$refKey = _ref3.refKey,\n          refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey,\n          onChange = _ref3.onChange,\n          onClick = _ref3.onClick,\n          rest = _objectWithoutProperties(_ref3, _excluded4);\n\n      var inputProps = _defineProperty({\n        accept: acceptAttr,\n        multiple: multiple,\n        type: \"file\",\n        style: {\n          border: 0,\n          clip: \"rect(0, 0, 0, 0)\",\n          clipPath: \"inset(50%)\",\n          height: \"1px\",\n          margin: \"0 -1px -1px 0\",\n          overflow: \"hidden\",\n          padding: 0,\n          position: \"absolute\",\n          width: \"1px\",\n          whiteSpace: \"nowrap\"\n        },\n        onChange: composeHandler(composeEventHandlers(onChange, onDropCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onInputElementClick)),\n        tabIndex: -1\n      }, refKey, inputRef);\n\n      return _objectSpread(_objectSpread({}, inputProps), rest);\n    };\n  }, [inputRef, accept, multiple, onDropCb, disabled]);\n  return _objectSpread(_objectSpread({}, state), {}, {\n    isFocused: isFocused && !disabled,\n    getRootProps: getRootProps,\n    getInputProps: getInputProps,\n    rootRef: rootRef,\n    inputRef: inputRef,\n    open: composeHandler(openFileDialog)\n  });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */\n\nfunction reducer(state, action) {\n  /* istanbul ignore next */\n  switch (action.type) {\n    case \"focus\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: true\n      });\n\n    case \"blur\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: false\n      });\n\n    case \"openDialog\":\n      return _objectSpread(_objectSpread({}, initialState), {}, {\n        isFileDialogActive: true\n      });\n\n    case \"closeDialog\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFileDialogActive: false\n      });\n\n    case \"setDraggedFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isDragActive: action.isDragActive,\n        isDragAccept: action.isDragAccept,\n        isDragReject: action.isDragReject\n      });\n\n    case \"setFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        acceptedFiles: action.acceptedFiles,\n        fileRejections: action.fileRejections,\n        isDragReject: action.isDragReject\n      });\n\n    case \"reset\":\n      return _objectSpread({}, initialState);\n\n    default:\n      return state;\n  }\n}\n\nfunction noop() {}\n\nexport { ErrorCode } from \"./utils/index.js\";"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,UAAU,CAAC;EACxBC,UAAU,GAAG,CAAC,MAAM,CAAC;EACrBC,UAAU,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,CAAC;EAClIC,UAAU,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;AAElD,SAASC,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AAExJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAE7L,SAASH,gBAAgBA,CAACI,IAAI,EAAE;EAAE,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAID,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIF,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOG,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC;AAAE;AAE7J,SAASL,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIS,KAAK,CAACE,OAAO,CAACX,GAAG,CAAC,EAAE,OAAOY,iBAAiB,CAACZ,GAAG,CAAC;AAAE;AAE1F,SAASa,cAAcA,CAACb,GAAG,EAAEc,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACf,GAAG,CAAC,IAAIgB,qBAAqB,CAAChB,GAAG,EAAEc,CAAC,CAAC,IAAIX,2BAA2B,CAACH,GAAG,EAAEc,CAAC,CAAC,IAAIG,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIZ,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAACe,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAON,iBAAiB,CAACM,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACN,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIL,CAAC,KAAK,QAAQ,IAAIF,CAAC,CAACQ,WAAW,EAAEN,CAAC,GAAGF,CAAC,CAACQ,WAAW,CAACC,IAAI;EAAE,IAAIP,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOX,KAAK,CAACC,IAAI,CAACQ,CAAC,CAAC;EAAE,IAAIE,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOR,iBAAiB,CAACM,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAASP,iBAAiBA,CAACZ,GAAG,EAAE6B,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAG7B,GAAG,CAAC8B,MAAM,EAAED,GAAG,GAAG7B,GAAG,CAAC8B,MAAM;EAAE,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAItB,KAAK,CAACoB,GAAG,CAAC,EAAEf,CAAC,GAAGe,GAAG,EAAEf,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGd,GAAG,CAACc,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;AAEtL,SAASf,qBAAqBA,CAAChB,GAAG,EAAEc,CAAC,EAAE;EAAE,IAAIkB,EAAE,GAAGhC,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOO,MAAM,KAAK,WAAW,IAAIP,GAAG,CAACO,MAAM,CAACC,QAAQ,CAAC,IAAIR,GAAG,CAAC,YAAY,CAAC;EAAE,IAAIgC,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIC,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKL,EAAE,GAAGA,EAAE,CAACR,IAAI,CAACxB,GAAG,CAAC,EAAE,EAAEkC,EAAE,GAAG,CAACE,EAAE,GAAGJ,EAAE,CAACM,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAACO,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAAE,IAAI3B,CAAC,IAAImB,IAAI,CAACH,MAAM,KAAKhB,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAO4B,GAAG,EAAE;IAAEP,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGK,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACR,EAAE,IAAIF,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIG,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASlB,eAAeA,CAACf,GAAG,EAAE;EAAE,IAAIS,KAAK,CAACE,OAAO,CAACX,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,SAAS2C,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGzB,MAAM,CAACyB,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIvB,MAAM,CAAC0B,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAG3B,MAAM,CAAC0B,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAO7B,MAAM,CAAC8B,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACN,IAAI,CAACa,KAAK,CAACP,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAEpV,SAASQ,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,SAAS,CAAC1B,MAAM,EAAEhB,CAAC,EAAE,EAAE;IAAE,IAAI2C,MAAM,GAAG,IAAI,IAAID,SAAS,CAAC1C,CAAC,CAAC,GAAG0C,SAAS,CAAC1C,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAG6B,OAAO,CAACtB,MAAM,CAACoC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACL,MAAM,EAAEI,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGtC,MAAM,CAACwC,yBAAyB,GAAGxC,MAAM,CAACyC,gBAAgB,CAACP,MAAM,EAAElC,MAAM,CAACwC,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGd,OAAO,CAACtB,MAAM,CAACoC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEtC,MAAM,CAAC0C,cAAc,CAACR,MAAM,EAAEI,GAAG,EAAEtC,MAAM,CAAC8B,wBAAwB,CAACM,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAEzf,SAASK,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAElB,KAAK,EAAE;EAAE,IAAIkB,GAAG,IAAIK,GAAG,EAAE;IAAE3C,MAAM,CAAC0C,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAElB,KAAK,EAAEA,KAAK;MAAEW,UAAU,EAAE,IAAI;MAAEa,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEF,GAAG,CAACL,GAAG,CAAC,GAAGlB,KAAK;EAAE;EAAE,OAAOuB,GAAG;AAAE;AAEhN,SAASG,wBAAwBA,CAACV,MAAM,EAAEW,QAAQ,EAAE;EAAE,IAAIX,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,MAAM,GAAGc,6BAA6B,CAACZ,MAAM,EAAEW,QAAQ,CAAC;EAAE,IAAIT,GAAG,EAAE7C,CAAC;EAAE,IAAIO,MAAM,CAAC0B,qBAAqB,EAAE;IAAE,IAAIuB,gBAAgB,GAAGjD,MAAM,CAAC0B,qBAAqB,CAACU,MAAM,CAAC;IAAE,KAAK3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,gBAAgB,CAACxC,MAAM,EAAEhB,CAAC,EAAE,EAAE;MAAE6C,GAAG,GAAGW,gBAAgB,CAACxD,CAAC,CAAC;MAAE,IAAIsD,QAAQ,CAACG,OAAO,CAACZ,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACtC,MAAM,CAACC,SAAS,CAACkD,oBAAoB,CAAChD,IAAI,CAACiC,MAAM,EAAEE,GAAG,CAAC,EAAE;MAAUJ,MAAM,CAACI,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAE3e,SAASc,6BAA6BA,CAACZ,MAAM,EAAEW,QAAQ,EAAE;EAAE,IAAIX,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIkB,UAAU,GAAGpD,MAAM,CAACyB,IAAI,CAACW,MAAM,CAAC;EAAE,IAAIE,GAAG,EAAE7C,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,UAAU,CAAC3C,MAAM,EAAEhB,CAAC,EAAE,EAAE;IAAE6C,GAAG,GAAGc,UAAU,CAAC3D,CAAC,CAAC;IAAE,IAAIsD,QAAQ,CAACG,OAAO,CAACZ,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUJ,MAAM,CAACI,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;;AAElT;AACA,OAAOmB,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,QAAQ,OAAO;AAC7H,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,sBAAsB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,yBAAyB,EAAEC,OAAO,EAAEC,cAAc,EAAEC,UAAU,EAAEC,oBAAoB,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,uBAAuB,EAAEC,wBAAwB,QAAQ,kBAAkB;AAC5S;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,QAAQ,GAAG,aAAaxB,UAAU,CAAC,UAAUyB,IAAI,EAAEC,GAAG,EAAE;EAC1D,IAAIC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,MAAM,GAAGpC,wBAAwB,CAACiC,IAAI,EAAEzG,SAAS,CAAC;EAEtD,IAAI6G,YAAY,GAAGC,WAAW,CAACF,MAAM,CAAC;IAClCG,IAAI,GAAGF,YAAY,CAACE,IAAI;IACxBC,KAAK,GAAGxC,wBAAwB,CAACqC,YAAY,EAAE5G,UAAU,CAAC;EAE9DmF,mBAAmB,CAACsB,GAAG,EAAE,YAAY;IACnC,OAAO;MACLK,IAAI,EAAEA;IACR,CAAC;EACH,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ,OAAO,aAAahC,KAAK,CAACkC,aAAa,CAAChC,QAAQ,EAAE,IAAI,EAAE0B,QAAQ,CAAChD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3GD,IAAI,EAAEA;EACR,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACFP,QAAQ,CAACU,WAAW,GAAG,UAAU,CAAC,CAAC;;AAEnC,IAAIC,YAAY,GAAG;EACjBC,QAAQ,EAAE,KAAK;EACfC,iBAAiB,EAAE5B,SAAS;EAC5B6B,OAAO,EAAEC,QAAQ;EACjBC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,CAAC;EACXC,qBAAqB,EAAE,IAAI;EAC3BC,OAAO,EAAE,KAAK;EACdC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,KAAK;EACbC,oBAAoB,EAAE,KAAK;EAC3BC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,KAAK;EACrBC,SAAS,EAAE;AACb,CAAC;AACD1B,QAAQ,CAACW,YAAY,GAAGA,YAAY;AACpCX,QAAQ,CAAC2B,SAAS,GAAG;EACnB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExB,QAAQ,EAAEnB,SAAS,CAAC4C,IAAI;EAExB;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,MAAM,EAAE7C,SAAS,CAAC8C,QAAQ,CAAC9C,SAAS,CAAC+C,OAAO,CAAC/C,SAAS,CAACgD,MAAM,CAAC,CAAC;EAE/D;AACF;AACA;EACEf,QAAQ,EAAEjC,SAAS,CAACiD,IAAI;EAExB;AACF;AACA;EACEd,qBAAqB,EAAEnC,SAAS,CAACiD,IAAI;EAErC;AACF;AACA;EACEb,OAAO,EAAEpC,SAAS,CAACiD,IAAI;EAEvB;AACF;AACA;AACA;EACEZ,UAAU,EAAErC,SAAS,CAACiD,IAAI;EAE1B;AACF;AACA;EACEX,MAAM,EAAEtC,SAAS,CAACiD,IAAI;EAEtB;AACF;AACA;EACEV,oBAAoB,EAAEvC,SAAS,CAACiD,IAAI;EAEpC;AACF;AACA;EACEjB,OAAO,EAAEhC,SAAS,CAACkD,MAAM;EAEzB;AACF;AACA;EACEpB,OAAO,EAAE9B,SAAS,CAACkD,MAAM;EAEzB;AACF;AACA;AACA;EACEhB,QAAQ,EAAElC,SAAS,CAACkD,MAAM;EAE1B;AACF;AACA;EACEtB,QAAQ,EAAE5B,SAAS,CAACiD,IAAI;EAExB;AACF;AACA;AACA;AACA;EACEpB,iBAAiB,EAAE7B,SAAS,CAAC4C,IAAI;EAEjC;AACF;AACA;EACEO,kBAAkB,EAAEnD,SAAS,CAAC4C,IAAI;EAElC;AACF;AACA;EACEQ,gBAAgB,EAAEpD,SAAS,CAAC4C,IAAI;EAEhC;AACF;AACA;AACA;EACEH,cAAc,EAAEzC,SAAS,CAACiD,IAAI;EAE9B;AACF;AACA;EACEP,SAAS,EAAE1C,SAAS,CAACiD,IAAI;EAEzB;AACF;AACA;AACA;AACA;EACEI,WAAW,EAAErD,SAAS,CAAC4C,IAAI;EAE3B;AACF;AACA;AACA;AACA;EACEU,WAAW,EAAEtD,SAAS,CAAC4C,IAAI;EAE3B;AACF;AACA;AACA;AACA;EACEW,UAAU,EAAEvD,SAAS,CAAC4C,IAAI;EAE1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEY,MAAM,EAAExD,SAAS,CAAC4C,IAAI;EAEtB;AACF;AACA;AACA;AACA;AACA;AACA;EACEa,cAAc,EAAEzD,SAAS,CAAC4C,IAAI;EAE9B;AACF;AACA;AACA;AACA;AACA;AACA;EACEc,cAAc,EAAE1D,SAAS,CAAC4C,IAAI;EAE9B;AACF;AACA;AACA;AACA;EACEe,OAAO,EAAE3D,SAAS,CAAC4C,IAAI;EAEvB;AACF;AACA;AACA;AACA;EACEJ,SAAS,EAAExC,SAAS,CAAC4C;AACvB,CAAC;AACD,eAAe5B,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAI4C,YAAY,GAAG;EACjBC,SAAS,EAAE,KAAK;EAChBC,kBAAkB,EAAE,KAAK;EACzBC,YAAY,EAAE,KAAK;EACnBC,YAAY,EAAE,KAAK;EACnBC,YAAY,EAAE,KAAK;EACnBC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAAS7C,WAAWA,CAAA,EAAG;EAC5B,IAAIE,KAAK,GAAGnD,SAAS,CAAC1B,MAAM,GAAG,CAAC,IAAI0B,SAAS,CAAC,CAAC,CAAC,KAAK+F,SAAS,GAAG/F,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAElF,IAAIgG,mBAAmB,GAAGlG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwD,YAAY,CAAC,EAAEH,KAAK,CAAC;IAC3EqB,MAAM,GAAGwB,mBAAmB,CAACxB,MAAM;IACnCjB,QAAQ,GAAGyC,mBAAmB,CAACzC,QAAQ;IACvCC,iBAAiB,GAAGwC,mBAAmB,CAACxC,iBAAiB;IACzDC,OAAO,GAAGuC,mBAAmB,CAACvC,OAAO;IACrCE,OAAO,GAAGqC,mBAAmB,CAACrC,OAAO;IACrCC,QAAQ,GAAGoC,mBAAmB,CAACpC,QAAQ;IACvCC,QAAQ,GAAGmC,mBAAmB,CAACnC,QAAQ;IACvCmB,WAAW,GAAGgB,mBAAmB,CAAChB,WAAW;IAC7CC,WAAW,GAAGe,mBAAmB,CAACf,WAAW;IAC7CC,UAAU,GAAGc,mBAAmB,CAACd,UAAU;IAC3CC,MAAM,GAAGa,mBAAmB,CAACb,MAAM;IACnCC,cAAc,GAAGY,mBAAmB,CAACZ,cAAc;IACnDC,cAAc,GAAGW,mBAAmB,CAACX,cAAc;IACnDP,kBAAkB,GAAGkB,mBAAmB,CAAClB,kBAAkB;IAC3DC,gBAAgB,GAAGiB,mBAAmB,CAACjB,gBAAgB;IACvDX,cAAc,GAAG4B,mBAAmB,CAAC5B,cAAc;IACnDC,SAAS,GAAG2B,mBAAmB,CAAC3B,SAAS;IACzCP,qBAAqB,GAAGkC,mBAAmB,CAAClC,qBAAqB;IACjEC,OAAO,GAAGiC,mBAAmB,CAACjC,OAAO;IACrCC,UAAU,GAAGgC,mBAAmB,CAAChC,UAAU;IAC3CC,MAAM,GAAG+B,mBAAmB,CAAC/B,MAAM;IACnCC,oBAAoB,GAAG8B,mBAAmB,CAAC9B,oBAAoB;IAC/DoB,OAAO,GAAGU,mBAAmB,CAACV,OAAO;IACrCnB,SAAS,GAAG6B,mBAAmB,CAAC7B,SAAS;EAE7C,IAAI8B,UAAU,GAAGzE,OAAO,CAAC,YAAY;IACnC,OAAOK,sBAAsB,CAAC2C,MAAM,CAAC;EACvC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,IAAI0B,WAAW,GAAG1E,OAAO,CAAC,YAAY;IACpC,OAAOiB,uBAAuB,CAAC+B,MAAM,CAAC;EACxC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,IAAI2B,kBAAkB,GAAG3E,OAAO,CAAC,YAAY;IAC3C,OAAO,OAAOuD,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,GAAGqB,IAAI;EACzE,CAAC,EAAE,CAACrB,gBAAgB,CAAC,CAAC;EACtB,IAAIsB,oBAAoB,GAAG7E,OAAO,CAAC,YAAY;IAC7C,OAAO,OAAOsD,kBAAkB,KAAK,UAAU,GAAGA,kBAAkB,GAAGsB,IAAI;EAC7E,CAAC,EAAE,CAACtB,kBAAkB,CAAC,CAAC;EACxB;AACF;AACA;AACA;;EAEE,IAAIwB,OAAO,GAAG5E,MAAM,CAAC,IAAI,CAAC;EAC1B,IAAI6E,QAAQ,GAAG7E,MAAM,CAAC,IAAI,CAAC;EAE3B,IAAI8E,WAAW,GAAG/E,UAAU,CAACgF,OAAO,EAAElB,YAAY,CAAC;IAC/CmB,YAAY,GAAGrJ,cAAc,CAACmJ,WAAW,EAAE,CAAC,CAAC;IAC7CG,KAAK,GAAGD,YAAY,CAAC,CAAC,CAAC;IACvBE,QAAQ,GAAGF,YAAY,CAAC,CAAC,CAAC;EAE9B,IAAIlB,SAAS,GAAGmB,KAAK,CAACnB,SAAS;IAC3BC,kBAAkB,GAAGkB,KAAK,CAAClB,kBAAkB;EACjD,IAAIoB,mBAAmB,GAAGnF,MAAM,CAAC,OAAOoF,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,eAAe,IAAI3C,cAAc,IAAIlC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5I,IAAI8E,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C;IACA,IAAI,CAACH,mBAAmB,CAACI,OAAO,IAAIxB,kBAAkB,EAAE;MACtDyB,UAAU,CAAC,YAAY;QACrB,IAAIX,QAAQ,CAACU,OAAO,EAAE;UACpB,IAAIE,KAAK,GAAGZ,QAAQ,CAACU,OAAO,CAACE,KAAK;UAElC,IAAI,CAACA,KAAK,CAAC7I,MAAM,EAAE;YACjBsI,QAAQ,CAAC;cACPQ,IAAI,EAAE;YACR,CAAC,CAAC;YACFf,oBAAoB,CAAC,CAAC;UACxB;QACF;MACF,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;EAED/E,SAAS,CAAC,YAAY;IACpBwF,MAAM,CAACO,gBAAgB,CAAC,OAAO,EAAEL,aAAa,EAAE,KAAK,CAAC;IACtD,OAAO,YAAY;MACjBF,MAAM,CAACQ,mBAAmB,CAAC,OAAO,EAAEN,aAAa,EAAE,KAAK,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,CAACT,QAAQ,EAAEd,kBAAkB,EAAEY,oBAAoB,EAAEQ,mBAAmB,CAAC,CAAC;EAC7E,IAAIU,cAAc,GAAG7F,MAAM,CAAC,EAAE,CAAC;EAE/B,IAAI8F,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;IAClD,IAAInB,OAAO,CAACW,OAAO,IAAIX,OAAO,CAACW,OAAO,CAACS,QAAQ,CAACD,KAAK,CAAC1H,MAAM,CAAC,EAAE;MAC7D;MACA;IACF;IAEA0H,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBJ,cAAc,CAACN,OAAO,GAAG,EAAE;EAC7B,CAAC;EAED3F,SAAS,CAAC,YAAY;IACpB,IAAIwC,qBAAqB,EAAE;MACzB8D,QAAQ,CAACP,gBAAgB,CAAC,UAAU,EAAE7E,kBAAkB,EAAE,KAAK,CAAC;MAChEoF,QAAQ,CAACP,gBAAgB,CAAC,MAAM,EAAEG,cAAc,EAAE,KAAK,CAAC;IAC1D;IAEA,OAAO,YAAY;MACjB,IAAI1D,qBAAqB,EAAE;QACzB8D,QAAQ,CAACN,mBAAmB,CAAC,UAAU,EAAE9E,kBAAkB,CAAC;QAC5DoF,QAAQ,CAACN,mBAAmB,CAAC,MAAM,EAAEE,cAAc,CAAC;MACtD;IACF,CAAC;EACH,CAAC,EAAE,CAAClB,OAAO,EAAExC,qBAAqB,CAAC,CAAC,CAAC,CAAC;;EAEtCxC,SAAS,CAAC,YAAY;IACpB,IAAI,CAACiC,QAAQ,IAAIc,SAAS,IAAIiC,OAAO,CAACW,OAAO,EAAE;MAC7CX,OAAO,CAACW,OAAO,CAACY,KAAK,CAAC,CAAC;IACzB;IAEA,OAAO,YAAY,CAAC,CAAC;EACvB,CAAC,EAAE,CAACvB,OAAO,EAAEjC,SAAS,EAAEd,QAAQ,CAAC,CAAC;EAClC,IAAIuE,OAAO,GAAGzG,WAAW,CAAC,UAAU0G,CAAC,EAAE;IACrC,IAAIzC,OAAO,EAAE;MACXA,OAAO,CAACyC,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACAC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACzC,OAAO,CAAC,CAAC;EACb,IAAI4C,aAAa,GAAG7G,WAAW,CAAC,UAAUoG,KAAK,EAAE;IAC/CA,KAAK,CAACE,cAAc,CAAC,CAAC,CAAC,CAAC;;IAExBF,KAAK,CAACU,OAAO,CAAC,CAAC;IACfC,eAAe,CAACX,KAAK,CAAC;IACtBF,cAAc,CAACN,OAAO,GAAG,EAAE,CAACoB,MAAM,CAAC9L,kBAAkB,CAACgL,cAAc,CAACN,OAAO,CAAC,EAAE,CAACQ,KAAK,CAAC1H,MAAM,CAAC,CAAC;IAE9F,IAAIqC,cAAc,CAACqF,KAAK,CAAC,EAAE;MACzBa,OAAO,CAACC,OAAO,CAAC/E,iBAAiB,CAACiE,KAAK,CAAC,CAAC,CAACe,IAAI,CAAC,UAAUrB,KAAK,EAAE;QAC9D,IAAI7E,oBAAoB,CAACmF,KAAK,CAAC,IAAI,CAACvD,oBAAoB,EAAE;UACxD;QACF;QAEA,IAAIuE,SAAS,GAAGtB,KAAK,CAAC7I,MAAM;QAC5B,IAAIqH,YAAY,GAAG8C,SAAS,GAAG,CAAC,IAAI3G,gBAAgB,CAAC;UACnDqF,KAAK,EAAEA,KAAK;UACZ3C,MAAM,EAAEyB,UAAU;UAClBtC,OAAO,EAAEA,OAAO;UAChBF,OAAO,EAAEA,OAAO;UAChBG,QAAQ,EAAEA,QAAQ;UAClBC,QAAQ,EAAEA,QAAQ;UAClBM,SAAS,EAAEA;QACb,CAAC,CAAC;QACF,IAAIyB,YAAY,GAAG6C,SAAS,GAAG,CAAC,IAAI,CAAC9C,YAAY;QACjDiB,QAAQ,CAAC;UACPjB,YAAY,EAAEA,YAAY;UAC1BC,YAAY,EAAEA,YAAY;UAC1BF,YAAY,EAAE,IAAI;UAClB0B,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAIpC,WAAW,EAAE;UACfA,WAAW,CAACyC,KAAK,CAAC;QACpB;MACF,CAAC,CAAC,CAACiB,KAAK,CAAC,UAAUX,CAAC,EAAE;QACpB,OAAOD,OAAO,CAACC,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACvE,iBAAiB,EAAEwB,WAAW,EAAE8C,OAAO,EAAE5D,oBAAoB,EAAE+B,UAAU,EAAEtC,OAAO,EAAEF,OAAO,EAAEG,QAAQ,EAAEC,QAAQ,EAAEM,SAAS,CAAC,CAAC;EAChI,IAAIwE,YAAY,GAAGtH,WAAW,CAAC,UAAUoG,KAAK,EAAE;IAC9CA,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACU,OAAO,CAAC,CAAC;IACfC,eAAe,CAACX,KAAK,CAAC;IACtB,IAAImB,QAAQ,GAAGxG,cAAc,CAACqF,KAAK,CAAC;IAEpC,IAAImB,QAAQ,IAAInB,KAAK,CAACoB,YAAY,EAAE;MAClC,IAAI;QACFpB,KAAK,CAACoB,YAAY,CAACC,UAAU,GAAG,MAAM;MACxC,CAAC,CAAC,OAAOC,OAAO,EAAE,CAAC;MACnB;IAEF;IAEA,IAAIH,QAAQ,IAAI1D,UAAU,EAAE;MAC1BA,UAAU,CAACuC,KAAK,CAAC;IACnB;IAEA,OAAO,KAAK;EACd,CAAC,EAAE,CAACvC,UAAU,EAAEhB,oBAAoB,CAAC,CAAC;EACtC,IAAI8E,aAAa,GAAG3H,WAAW,CAAC,UAAUoG,KAAK,EAAE;IAC/CA,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACU,OAAO,CAAC,CAAC;IACfC,eAAe,CAACX,KAAK,CAAC,CAAC,CAAC;;IAExB,IAAIwB,OAAO,GAAG1B,cAAc,CAACN,OAAO,CAACxH,MAAM,CAAC,UAAUM,MAAM,EAAE;MAC5D,OAAOuG,OAAO,CAACW,OAAO,IAAIX,OAAO,CAACW,OAAO,CAACS,QAAQ,CAAC3H,MAAM,CAAC;IAC5D,CAAC,CAAC,CAAC,CAAC;IACJ;;IAEA,IAAImJ,SAAS,GAAGD,OAAO,CAAClI,OAAO,CAAC0G,KAAK,CAAC1H,MAAM,CAAC;IAE7C,IAAImJ,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBD,OAAO,CAACE,MAAM,CAACD,SAAS,EAAE,CAAC,CAAC;IAC9B;IAEA3B,cAAc,CAACN,OAAO,GAAGgC,OAAO;IAEhC,IAAIA,OAAO,CAAC3K,MAAM,GAAG,CAAC,EAAE;MACtB;IACF;IAEAsI,QAAQ,CAAC;MACPQ,IAAI,EAAE,iBAAiB;MACvB1B,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE;IAChB,CAAC,CAAC;IAEF,IAAIxD,cAAc,CAACqF,KAAK,CAAC,IAAIxC,WAAW,EAAE;MACxCA,WAAW,CAACwC,KAAK,CAAC;IACpB;EACF,CAAC,EAAE,CAACnB,OAAO,EAAErB,WAAW,EAAEf,oBAAoB,CAAC,CAAC;EAChD,IAAIkF,QAAQ,GAAG/H,WAAW,CAAC,UAAU8F,KAAK,EAAEM,KAAK,EAAE;IACjD,IAAI5B,aAAa,GAAG,EAAE;IACtB,IAAIC,cAAc,GAAG,EAAE;IACvBqB,KAAK,CAACjH,OAAO,CAAC,UAAUmJ,IAAI,EAAE;MAC5B,IAAIC,aAAa,GAAGtH,YAAY,CAACqH,IAAI,EAAEpD,UAAU,CAAC;QAC9CsD,cAAc,GAAGlM,cAAc,CAACiM,aAAa,EAAE,CAAC,CAAC;QACjDE,QAAQ,GAAGD,cAAc,CAAC,CAAC,CAAC;QAC5BE,WAAW,GAAGF,cAAc,CAAC,CAAC,CAAC;MAEnC,IAAIG,cAAc,GAAGzH,aAAa,CAACoH,IAAI,EAAE1F,OAAO,EAAEF,OAAO,CAAC;QACtDkG,eAAe,GAAGtM,cAAc,CAACqM,cAAc,EAAE,CAAC,CAAC;QACnDE,SAAS,GAAGD,eAAe,CAAC,CAAC,CAAC;QAC9BE,SAAS,GAAGF,eAAe,CAAC,CAAC,CAAC;MAElC,IAAIG,YAAY,GAAG3F,SAAS,GAAGA,SAAS,CAACkF,IAAI,CAAC,GAAG,IAAI;MAErD,IAAIG,QAAQ,IAAII,SAAS,IAAI,CAACE,YAAY,EAAE;QAC1CjE,aAAa,CAAC7G,IAAI,CAACqK,IAAI,CAAC;MAC1B,CAAC,MAAM;QACL,IAAIU,MAAM,GAAG,CAACN,WAAW,EAAEI,SAAS,CAAC;QAErC,IAAIC,YAAY,EAAE;UAChBC,MAAM,GAAGA,MAAM,CAAC1B,MAAM,CAACyB,YAAY,CAAC;QACtC;QAEAhE,cAAc,CAAC9G,IAAI,CAAC;UAClBqK,IAAI,EAAEA,IAAI;UACVU,MAAM,EAAEA,MAAM,CAACtK,MAAM,CAAC,UAAUsI,CAAC,EAAE;YACjC,OAAOA,CAAC;UACV,CAAC;QACH,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACnE,QAAQ,IAAIiC,aAAa,CAACvH,MAAM,GAAG,CAAC,IAAIsF,QAAQ,IAAIC,QAAQ,IAAI,CAAC,IAAIgC,aAAa,CAACvH,MAAM,GAAGuF,QAAQ,EAAE;MACzG;MACAgC,aAAa,CAAC3F,OAAO,CAAC,UAAUmJ,IAAI,EAAE;QACpCvD,cAAc,CAAC9G,IAAI,CAAC;UAClBqK,IAAI,EAAEA,IAAI;UACVU,MAAM,EAAE,CAACrH,wBAAwB;QACnC,CAAC,CAAC;MACJ,CAAC,CAAC;MACFmD,aAAa,CAACsD,MAAM,CAAC,CAAC,CAAC;IACzB;IAEAvC,QAAQ,CAAC;MACPf,aAAa,EAAEA,aAAa;MAC5BC,cAAc,EAAEA,cAAc;MAC9BF,YAAY,EAAEE,cAAc,CAACxH,MAAM,GAAG,CAAC;MACvC8I,IAAI,EAAE;IACR,CAAC,CAAC;IAEF,IAAIjC,MAAM,EAAE;MACVA,MAAM,CAACU,aAAa,EAAEC,cAAc,EAAE2B,KAAK,CAAC;IAC9C;IAEA,IAAI3B,cAAc,CAACxH,MAAM,GAAG,CAAC,IAAI+G,cAAc,EAAE;MAC/CA,cAAc,CAACS,cAAc,EAAE2B,KAAK,CAAC;IACvC;IAEA,IAAI5B,aAAa,CAACvH,MAAM,GAAG,CAAC,IAAI8G,cAAc,EAAE;MAC9CA,cAAc,CAACS,aAAa,EAAE4B,KAAK,CAAC;IACtC;EACF,CAAC,EAAE,CAACb,QAAQ,EAAEhD,QAAQ,EAAEqC,UAAU,EAAEtC,OAAO,EAAEF,OAAO,EAAEI,QAAQ,EAAEsB,MAAM,EAAEC,cAAc,EAAEC,cAAc,EAAElB,SAAS,CAAC,CAAC;EACnH,IAAI6F,QAAQ,GAAG3I,WAAW,CAAC,UAAUoG,KAAK,EAAE;IAC1CA,KAAK,CAACE,cAAc,CAAC,CAAC,CAAC,CAAC;;IAExBF,KAAK,CAACU,OAAO,CAAC,CAAC;IACfC,eAAe,CAACX,KAAK,CAAC;IACtBF,cAAc,CAACN,OAAO,GAAG,EAAE;IAE3B,IAAI7E,cAAc,CAACqF,KAAK,CAAC,EAAE;MACzBa,OAAO,CAACC,OAAO,CAAC/E,iBAAiB,CAACiE,KAAK,CAAC,CAAC,CAACe,IAAI,CAAC,UAAUrB,KAAK,EAAE;QAC9D,IAAI7E,oBAAoB,CAACmF,KAAK,CAAC,IAAI,CAACvD,oBAAoB,EAAE;UACxD;QACF;QAEAkF,QAAQ,CAACjC,KAAK,EAAEM,KAAK,CAAC;MACxB,CAAC,CAAC,CAACiB,KAAK,CAAC,UAAUX,CAAC,EAAE;QACpB,OAAOD,OAAO,CAACC,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ;IAEAnB,QAAQ,CAAC;MACPQ,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC5D,iBAAiB,EAAE4F,QAAQ,EAAEtB,OAAO,EAAE5D,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAElE,IAAI+F,cAAc,GAAG5I,WAAW,CAAC,YAAY;IAC3C;IACA;IACA,IAAIwF,mBAAmB,CAACI,OAAO,EAAE;MAC/BL,QAAQ,CAAC;QACPQ,IAAI,EAAE;MACR,CAAC,CAAC;MACFjB,kBAAkB,CAAC,CAAC,CAAC,CAAC;;MAEtB,IAAI+D,IAAI,GAAG;QACTtG,QAAQ,EAAEA,QAAQ;QAClBuG,KAAK,EAAEjE;MACT,CAAC;MACDY,MAAM,CAACsD,kBAAkB,CAACF,IAAI,CAAC,CAAC1B,IAAI,CAAC,UAAU6B,OAAO,EAAE;QACtD,OAAO7G,iBAAiB,CAAC6G,OAAO,CAAC;MACnC,CAAC,CAAC,CAAC7B,IAAI,CAAC,UAAUrB,KAAK,EAAE;QACvBiC,QAAQ,CAACjC,KAAK,EAAE,IAAI,CAAC;QACrBP,QAAQ,CAAC;UACPQ,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC,CAACsB,KAAK,CAAC,UAAUX,CAAC,EAAE;QACpB;QACA,IAAI5F,OAAO,CAAC4F,CAAC,CAAC,EAAE;UACd1B,oBAAoB,CAAC0B,CAAC,CAAC;UACvBnB,QAAQ,CAAC;YACPQ,IAAI,EAAE;UACR,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI7E,eAAe,CAACwF,CAAC,CAAC,EAAE;UAC7BlB,mBAAmB,CAACI,OAAO,GAAG,KAAK,CAAC,CAAC;UACrC;;UAEA,IAAIV,QAAQ,CAACU,OAAO,EAAE;YACpBV,QAAQ,CAACU,OAAO,CAAChI,KAAK,GAAG,IAAI;YAC7BsH,QAAQ,CAACU,OAAO,CAACqD,KAAK,CAAC,CAAC;UAC1B,CAAC,MAAM;YACLxC,OAAO,CAAC,IAAIyC,KAAK,CAAC,+JAA+J,CAAC,CAAC;UACrL;QACF,CAAC,MAAM;UACLzC,OAAO,CAACC,CAAC,CAAC;QACZ;MACF,CAAC,CAAC;MACF;IACF;IAEA,IAAIxB,QAAQ,CAACU,OAAO,EAAE;MACpBL,QAAQ,CAAC;QACPQ,IAAI,EAAE;MACR,CAAC,CAAC;MACFjB,kBAAkB,CAAC,CAAC;MACpBI,QAAQ,CAACU,OAAO,CAAChI,KAAK,GAAG,IAAI;MAC7BsH,QAAQ,CAACU,OAAO,CAACqD,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAAC1D,QAAQ,EAAET,kBAAkB,EAAEE,oBAAoB,EAAEjC,cAAc,EAAEgF,QAAQ,EAAEtB,OAAO,EAAE5B,WAAW,EAAEtC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpH,IAAI4G,WAAW,GAAGnJ,WAAW,CAAC,UAAUoG,KAAK,EAAE;IAC7C;IACA,IAAI,CAACnB,OAAO,CAACW,OAAO,IAAI,CAACX,OAAO,CAACW,OAAO,CAACwD,WAAW,CAAChD,KAAK,CAAC1H,MAAM,CAAC,EAAE;MAClE;IACF;IAEA,IAAI0H,KAAK,CAACtH,GAAG,KAAK,GAAG,IAAIsH,KAAK,CAACtH,GAAG,KAAK,OAAO,IAAIsH,KAAK,CAACiD,OAAO,KAAK,EAAE,IAAIjD,KAAK,CAACiD,OAAO,KAAK,EAAE,EAAE;MAC9FjD,KAAK,CAACE,cAAc,CAAC,CAAC;MACtBsC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC3D,OAAO,EAAE2D,cAAc,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIU,SAAS,GAAGtJ,WAAW,CAAC,YAAY;IACtCuF,QAAQ,CAAC;MACPQ,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIwD,QAAQ,GAAGvJ,WAAW,CAAC,YAAY;IACrCuF,QAAQ,CAAC;MACPQ,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAIyD,SAAS,GAAGxJ,WAAW,CAAC,YAAY;IACtC,IAAI0C,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACF;IACA;;IAGA,IAAI1B,UAAU,CAAC,CAAC,EAAE;MAChB6E,UAAU,CAAC+C,cAAc,EAAE,CAAC,CAAC;IAC/B,CAAC,MAAM;MACLA,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAClG,OAAO,EAAEkG,cAAc,CAAC,CAAC;EAE7B,IAAIa,cAAc,GAAG,SAASA,cAAcA,CAACC,EAAE,EAAE;IAC/C,OAAOxH,QAAQ,GAAG,IAAI,GAAGwH,EAAE;EAC7B,CAAC;EAED,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACD,EAAE,EAAE;IAC/D,OAAO/G,UAAU,GAAG,IAAI,GAAG8G,cAAc,CAACC,EAAE,CAAC;EAC/C,CAAC;EAED,IAAIE,kBAAkB,GAAG,SAASA,kBAAkBA,CAACF,EAAE,EAAE;IACvD,OAAO9G,MAAM,GAAG,IAAI,GAAG6G,cAAc,CAACC,EAAE,CAAC;EAC3C,CAAC;EAED,IAAI3C,eAAe,GAAG,SAASA,eAAeA,CAACX,KAAK,EAAE;IACpD,IAAIvD,oBAAoB,EAAE;MACxBuD,KAAK,CAACW,eAAe,CAAC,CAAC;IACzB;EACF,CAAC;EAED,IAAI8C,YAAY,GAAG1J,OAAO,CAAC,YAAY;IACrC,OAAO,YAAY;MACjB,IAAI2J,KAAK,GAAGnL,SAAS,CAAC1B,MAAM,GAAG,CAAC,IAAI0B,SAAS,CAAC,CAAC,CAAC,KAAK+F,SAAS,GAAG/F,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9EoL,YAAY,GAAGD,KAAK,CAACE,MAAM;QAC3BA,MAAM,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;QACvDE,IAAI,GAAGH,KAAK,CAACG,IAAI;QACjBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;QAC3BC,OAAO,GAAGL,KAAK,CAACK,OAAO;QACvBC,MAAM,GAAGN,KAAK,CAACM,MAAM;QACrBC,OAAO,GAAGP,KAAK,CAACO,OAAO;QACvB1G,WAAW,GAAGmG,KAAK,CAACnG,WAAW;QAC/BE,UAAU,GAAGiG,KAAK,CAACjG,UAAU;QAC7BD,WAAW,GAAGkG,KAAK,CAAClG,WAAW;QAC/BE,MAAM,GAAGgG,KAAK,CAAChG,MAAM;QACrBwG,IAAI,GAAGhL,wBAAwB,CAACwK,KAAK,EAAE9O,UAAU,CAAC;MAEtD,OAAOyD,aAAa,CAACA,aAAa,CAACM,eAAe,CAAC;QACjDmL,SAAS,EAAEP,sBAAsB,CAACjJ,oBAAoB,CAACwJ,SAAS,EAAEf,WAAW,CAAC,CAAC;QAC/EgB,OAAO,EAAER,sBAAsB,CAACjJ,oBAAoB,CAACyJ,OAAO,EAAEb,SAAS,CAAC,CAAC;QACzEc,MAAM,EAAET,sBAAsB,CAACjJ,oBAAoB,CAAC0J,MAAM,EAAEb,QAAQ,CAAC,CAAC;QACtEc,OAAO,EAAEZ,cAAc,CAAC/I,oBAAoB,CAAC2J,OAAO,EAAEb,SAAS,CAAC,CAAC;QACjE7F,WAAW,EAAEiG,kBAAkB,CAAClJ,oBAAoB,CAACiD,WAAW,EAAEkD,aAAa,CAAC,CAAC;QACjFhD,UAAU,EAAE+F,kBAAkB,CAAClJ,oBAAoB,CAACmD,UAAU,EAAEyD,YAAY,CAAC,CAAC;QAC9E1D,WAAW,EAAEgG,kBAAkB,CAAClJ,oBAAoB,CAACkD,WAAW,EAAE+D,aAAa,CAAC,CAAC;QACjF7D,MAAM,EAAE8F,kBAAkB,CAAClJ,oBAAoB,CAACoD,MAAM,EAAE6E,QAAQ,CAAC,CAAC;QAClEsB,IAAI,EAAE,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,EAAE,GAAGA,IAAI,GAAG;MACzD,CAAC,EAAED,MAAM,EAAE/E,OAAO,CAAC,EAAE,CAAC/C,QAAQ,IAAI,CAACS,UAAU,GAAG;QAC9C4H,QAAQ,EAAE;MACZ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,CAACrF,OAAO,EAAEkE,WAAW,EAAEG,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAE3C,aAAa,EAAES,YAAY,EAAEK,aAAa,EAAEgB,QAAQ,EAAEhG,UAAU,EAAEC,MAAM,EAAEV,QAAQ,CAAC,CAAC;EAC9I,IAAIsI,mBAAmB,GAAGxK,WAAW,CAAC,UAAUoG,KAAK,EAAE;IACrDA,KAAK,CAACW,eAAe,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EACN,IAAI0D,aAAa,GAAGtK,OAAO,CAAC,YAAY;IACtC,OAAO,YAAY;MACjB,IAAIuK,KAAK,GAAG/L,SAAS,CAAC1B,MAAM,GAAG,CAAC,IAAI0B,SAAS,CAAC,CAAC,CAAC,KAAK+F,SAAS,GAAG/F,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9EgM,YAAY,GAAGD,KAAK,CAACV,MAAM;QAC3BA,MAAM,GAAGW,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;QACvDC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;QACzBP,OAAO,GAAGK,KAAK,CAACL,OAAO;QACvBC,IAAI,GAAGhL,wBAAwB,CAACoL,KAAK,EAAEzP,UAAU,CAAC;MAEtD,IAAI4P,UAAU,GAAG9L,eAAe,CAAC;QAC/BoE,MAAM,EAAEyB,UAAU;QAClBrC,QAAQ,EAAEA,QAAQ;QAClBwD,IAAI,EAAE,MAAM;QACZ+E,KAAK,EAAE;UACLC,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,kBAAkB;UACxBC,QAAQ,EAAE,YAAY;UACtBC,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE,eAAe;UACvBC,QAAQ,EAAE,QAAQ;UAClBC,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE;QACd,CAAC;QACDZ,QAAQ,EAAEnB,cAAc,CAAC/I,oBAAoB,CAACkK,QAAQ,EAAEjC,QAAQ,CAAC,CAAC;QAClE0B,OAAO,EAAEZ,cAAc,CAAC/I,oBAAoB,CAAC2J,OAAO,EAAEG,mBAAmB,CAAC,CAAC;QAC3ED,QAAQ,EAAE,CAAC;MACb,CAAC,EAAEP,MAAM,EAAE9E,QAAQ,CAAC;MAEpB,OAAOzG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoM,UAAU,CAAC,EAAEP,IAAI,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,CAACpF,QAAQ,EAAE/B,MAAM,EAAEZ,QAAQ,EAAEoG,QAAQ,EAAEzG,QAAQ,CAAC,CAAC;EACpD,OAAOzD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACjDnB,SAAS,EAAEA,SAAS,IAAI,CAACjC,QAAQ;IACjC2H,YAAY,EAAEA,YAAY;IAC1BY,aAAa,EAAEA,aAAa;IAC5BxF,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA,QAAQ;IAClBrD,IAAI,EAAE4H,cAAc,CAACb,cAAc;EACrC,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASxD,OAAOA,CAACE,KAAK,EAAEmG,MAAM,EAAE;EAC9B;EACA,QAAQA,MAAM,CAAC1F,IAAI;IACjB,KAAK,OAAO;MACV,OAAOtH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDnB,SAAS,EAAE;MACb,CAAC,CAAC;IAEJ,KAAK,MAAM;MACT,OAAO1F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDnB,SAAS,EAAE;MACb,CAAC,CAAC;IAEJ,KAAK,YAAY;MACf,OAAO1F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyF,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QACxDE,kBAAkB,EAAE;MACtB,CAAC,CAAC;IAEJ,KAAK,aAAa;MAChB,OAAO3F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDlB,kBAAkB,EAAE;MACtB,CAAC,CAAC;IAEJ,KAAK,iBAAiB;MACpB,OAAO3F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDjB,YAAY,EAAEoH,MAAM,CAACpH,YAAY;QACjCC,YAAY,EAAEmH,MAAM,CAACnH,YAAY;QACjCC,YAAY,EAAEkH,MAAM,CAAClH;MACvB,CAAC,CAAC;IAEJ,KAAK,UAAU;MACb,OAAO9F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDd,aAAa,EAAEiH,MAAM,CAACjH,aAAa;QACnCC,cAAc,EAAEgH,MAAM,CAAChH,cAAc;QACrCF,YAAY,EAAEkH,MAAM,CAAClH;MACvB,CAAC,CAAC;IAEJ,KAAK,OAAO;MACV,OAAO9F,aAAa,CAAC,CAAC,CAAC,EAAEyF,YAAY,CAAC;IAExC;MACE,OAAOoB,KAAK;EAChB;AACF;AAEA,SAASP,IAAIA,CAAA,EAAG,CAAC;AAEjB,SAAS2G,SAAS,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}