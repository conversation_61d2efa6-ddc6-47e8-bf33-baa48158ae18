{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport TimelineContext from \"./TimelineContext.js\";\nimport { getTimelineUtilityClass } from \"./timelineClasses.js\";\nimport convertTimelinePositionToClass from \"../internal/convertTimelinePositionToClass.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', position && convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineUtilityClass, classes);\n};\nconst TimelineRoot = styled('ul', {\n  name: 'MuiTimeline',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.position && styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  padding: '6px 16px',\n  flexGrow: 1\n});\n\n/**\n *\n * Demos:\n *\n * - [Timeline](https://mui.com/material-ui/react-timeline/)\n *\n * API:\n *\n * - [Timeline API](https://mui.com/material-ui/api/timeline/)\n */\nconst Timeline = /*#__PURE__*/React.forwardRef(function Timeline(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimeline'\n  });\n  const {\n    position = 'right',\n    className,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    position\n  };\n  const classes = useUtilityClasses(ownerState);\n  const contextValue = React.useMemo(() => ({\n    position\n  }), [position]);\n  return /*#__PURE__*/_jsx(TimelineContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(TimelineRoot, {\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ref: ref,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Timeline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * className applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * The position where the TimelineContent should appear relative to the time axis.\n   * @default 'right'\n   */\n  position: PropTypes.oneOf(['alternate-reverse', 'alternate', 'left', 'right']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\n\n/**\n *\n * Demos:\n *\n * - [Timeline](https://mui.com/components/timeline/)\n *\n * API:\n *\n * - [Timeline API](https://mui.com/api/timeline/)\n */\nexport default Timeline;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "useThemeProps", "TimelineContext", "getTimelineUtilityClass", "convertTimelinePositionToClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "position", "classes", "slots", "root", "TimelineRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "flexDirection", "padding", "flexGrow", "Timeline", "forwardRef", "inProps", "ref", "className", "other", "contextValue", "useMemo", "Provider", "value", "children", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "oneOf", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/Timeline/Timeline.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport TimelineContext from \"./TimelineContext.js\";\nimport { getTimelineUtilityClass } from \"./timelineClasses.js\";\nimport convertTimelinePositionToClass from \"../internal/convertTimelinePositionToClass.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', position && convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineUtilityClass, classes);\n};\nconst TimelineRoot = styled('ul', {\n  name: 'MuiTimeline',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.position && styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  padding: '6px 16px',\n  flexGrow: 1\n});\n\n/**\n *\n * Demos:\n *\n * - [Timeline](https://mui.com/material-ui/react-timeline/)\n *\n * API:\n *\n * - [Timeline API](https://mui.com/material-ui/api/timeline/)\n */\nconst Timeline = /*#__PURE__*/React.forwardRef(function Timeline(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimeline'\n  });\n  const {\n    position = 'right',\n    className,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    position\n  };\n  const classes = useUtilityClasses(ownerState);\n  const contextValue = React.useMemo(() => ({\n    position\n  }), [position]);\n  return /*#__PURE__*/_jsx(TimelineContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(TimelineRoot, {\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ref: ref,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Timeline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * className applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * The position where the TimelineContent should appear relative to the time axis.\n   * @default 'right'\n   */\n  position: PropTypes.oneOf(['alternate-reverse', 'alternate', 'left', 'right']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\n\n/**\n *\n * Demos:\n *\n * - [Timeline](https://mui.com/components/timeline/)\n *\n * API:\n *\n * - [Timeline API](https://mui.com/api/timeline/)\n */\nexport default Timeline;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,eAAe,MAAM,sBAAsB;AAClD,SAASC,uBAAuB,QAAQ,sBAAsB;AAC9D,OAAOC,8BAA8B,MAAM,+CAA+C;AAC1F,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,QAAQ,IAAIL,8BAA8B,CAACK,QAAQ,CAAC;EACrE,CAAC;EACD,OAAOV,cAAc,CAACY,KAAK,EAAER,uBAAuB,EAAEO,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,YAAY,GAAGb,MAAM,CAAC,IAAI,EAAE;EAChCc,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEJ,UAAU,CAACC,QAAQ,IAAIS,MAAM,CAACd,8BAA8B,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC1G;AACF,CAAC,CAAC,CAAC;EACDU,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvBC,OAAO,EAAE,UAAU;EACnBC,QAAQ,EAAE;AACZ,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAG,aAAa3B,KAAK,CAAC4B,UAAU,CAAC,SAASD,QAAQA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7E,MAAMT,KAAK,GAAGhB,aAAa,CAAC;IAC1BgB,KAAK,EAAEQ,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJL,QAAQ,GAAG,OAAO;IAClBkB,SAAS;IACT,GAAGC;EACL,CAAC,GAAGX,KAAK;EACT,MAAMT,UAAU,GAAG;IACjB,GAAGS,KAAK;IACRR;EACF,CAAC;EACD,MAAMC,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMqB,YAAY,GAAGjC,KAAK,CAACkC,OAAO,CAAC,OAAO;IACxCrB;EACF,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACf,OAAO,aAAaH,IAAI,CAACJ,eAAe,CAAC6B,QAAQ,EAAE;IACjDC,KAAK,EAAEH,YAAY;IACnBI,QAAQ,EAAE,aAAa3B,IAAI,CAACO,YAAY,EAAE;MACxCc,SAAS,EAAE7B,IAAI,CAACY,OAAO,CAACE,IAAI,EAAEe,SAAS,CAAC;MACxCnB,UAAU,EAAEA,UAAU;MACtBkB,GAAG,EAAEA,GAAG;MACR,GAAGE;IACL,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGb,QAAQ,CAACc,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACEJ,QAAQ,EAAEpC,SAAS,CAACyC,IAAI;EACxB;AACF;AACA;EACE5B,OAAO,EAAEb,SAAS,CAAC0C,MAAM;EACzB;AACF;AACA;EACEZ,SAAS,EAAE9B,SAAS,CAAC2C,MAAM;EAC3B;AACF;AACA;AACA;EACE/B,QAAQ,EAAEZ,SAAS,CAAC4C,KAAK,CAAC,CAAC,mBAAmB,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;EAC9E;AACF;AACA;EACEC,EAAE,EAAE7C,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAAC+C,OAAO,CAAC/C,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC0C,MAAM,EAAE1C,SAAS,CAACiD,IAAI,CAAC,CAAC,CAAC,EAAEjD,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC0C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAehB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}