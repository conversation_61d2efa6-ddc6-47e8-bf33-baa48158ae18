{"ast": null, "code": "import validate from './validate.js';\nfunction version(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n  return parseInt(uuid.slice(14, 15), 16);\n}\nexport default version;", "map": {"version": 3, "names": ["validate", "version", "uuid", "TypeError", "parseInt", "slice"], "sources": ["C:/laragon/www/frontend/node_modules/uuid/dist/esm-browser/version.js"], "sourcesContent": ["import validate from './validate.js';\n\nfunction version(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  return parseInt(uuid.slice(14, 15), 16);\n}\n\nexport default version;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,SAASC,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAI,CAACF,QAAQ,CAACE,IAAI,CAAC,EAAE;IACnB,MAAMC,SAAS,CAAC,cAAc,CAAC;EACjC;EAEA,OAAOC,QAAQ,CAACF,IAAI,CAACG,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;AACzC;AAEA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}