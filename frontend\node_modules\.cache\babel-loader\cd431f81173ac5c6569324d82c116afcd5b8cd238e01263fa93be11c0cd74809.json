{"ast": null, "code": "import { validateFieldsNatively as e, toNestErrors as t } from \"@hookform/resolvers\";\nimport { appendErrors as r } from \"react-hook-form\";\nfunction o(o, n, s) {\n  return void 0 === s && (s = {}), function (a, i, c) {\n    try {\n      return Promise.resolve(function (t, r) {\n        try {\n          var u = (null != n && n.context && \"development\" === process.env.NODE_ENV && console.warn(\"You should not used the yup options context. Please, use the 'useForm' context object instead\"), Promise.resolve(o[\"sync\" === s.mode ? \"validateSync\" : \"validate\"](a, Object.assign({\n            abortEarly: !1\n          }, n, {\n            context: i\n          }))).then(function (t) {\n            return c.shouldUseNativeValidation && e({}, c), {\n              values: s.raw ? Object.assign({}, a) : t,\n              errors: {}\n            };\n          }));\n        } catch (e) {\n          return r(e);\n        }\n        return u && u.then ? u.then(void 0, r) : u;\n      }(0, function (e) {\n        if (!e.inner) throw e;\n        return {\n          values: {},\n          errors: t((o = e, n = !c.shouldUseNativeValidation && \"all\" === c.criteriaMode, (o.inner || []).reduce(function (e, t) {\n            if (e[t.path] || (e[t.path] = {\n              message: t.message,\n              type: t.type\n            }), n) {\n              var o = e[t.path].types,\n                s = o && o[t.type];\n              e[t.path] = r(t.path, n, e, t.type, s ? [].concat(s, t.message) : t.message);\n            }\n            return e;\n          }, {})), c)\n        };\n        var o, n;\n      }));\n    } catch (e) {\n      return Promise.reject(e);\n    }\n  };\n}\nexport { o as yupResolver };", "map": {"version": 3, "names": ["o", "n", "s", "a", "i", "c", "Promise", "resolve", "t", "r", "u", "context", "process", "env", "NODE_ENV", "console", "warn", "mode", "Object", "assign", "abort<PERSON><PERSON><PERSON>", "then", "shouldUseNativeValidation", "e", "values", "raw", "errors", "inner", "criteriaMode", "reduce", "path", "message", "type", "types", "concat", "reject", "yupResolver"], "sources": ["C:\\laragon\\www\\frontend\\node_modules\\@hookform\\resolvers\\yup\\src\\yup.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  FieldError,\n  FieldValues,\n  Resolver,\n  appendErrors,\n} from 'react-hook-form';\nimport * as Yup from 'yup';\n\n/**\n * Why `path!` ? because it could be `undefined` in some case\n * https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n */\nfunction parseErrorSchema(\n  error: Yup.ValidationError,\n  validateAllFieldCriteria: boolean,\n) {\n  return (error.inner || []).reduce<Record<string, FieldError>>(\n    (previous, error) => {\n      if (!previous[error.path!]) {\n        previous[error.path!] = { message: error.message, type: error.type! };\n      }\n\n      if (validateAllFieldCriteria) {\n        const types = previous[error.path!].types;\n        const messages = types && types[error.type!];\n\n        previous[error.path!] = appendErrors(\n          error.path!,\n          validateAllFieldCriteria,\n          previous,\n          error.type!,\n          messages\n            ? ([] as string[]).concat(messages as string[], error.message)\n            : error.message,\n        ) as FieldError;\n      }\n\n      return previous;\n    },\n    {},\n  );\n}\n\nexport function yupResolver<Input extends FieldValues, Context, Output>(\n  schema:\n    | Yup.ObjectSchema<Input, any, Output, any>\n    | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<Input, any, Output, any>>>,\n  schemaOptions?: Parameters<(typeof schema)['validate']>[1],\n  resolverOptions?: {\n    mode?: 'async' | 'sync';\n    raw?: false;\n  },\n): Resolver<Input, Context, Yup.InferType<typeof schema>>;\n\nexport function yupResolver<Input extends FieldValues, Context, Output>(\n  schema:\n    | Yup.ObjectSchema<Input, any, Output, any>\n    | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<Input, any, Output, any>>>,\n  schemaOptions: Parameters<(typeof schema)['validate']>[1] | undefined,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using Yup schema validation\n * @param {Yup.ObjectSchema<TFieldValues> | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<TFieldValues>>>} schema - Yup validation schema\n * @param {Parameters<(typeof schema)['validate']>[1]} schemaOptions - Options to pass to Yup's validate/validateSync\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {('async' | 'sync')} [resolverOptions.mode] - Validation mode\n * @param {boolean} [resolverOptions.raw] - If true, returns raw values instead of validated results\n * @returns {Resolver<Yup.InferType<typeof schema> | Input>} A resolver function compatible with react-hook-form\n * @example\n * const schema = Yup.object({\n *   name: Yup.string().required(),\n *   age: Yup.number().required(),\n * });\n *\n * useForm({\n *   resolver: yupResolver(schema)\n * });\n */\nexport function yupResolver<Input extends FieldValues, Context, Output>(\n  schema:\n    | Yup.ObjectSchema<Input, any, Output, any>\n    | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<Input, any, Output, any>>>,\n  schemaOptions?: Parameters<(typeof schema)['validate']>[1],\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Yup.InferType<typeof schema> | Input> {\n  return async (values: Input, context, options) => {\n    try {\n      if (schemaOptions?.context && process.env.NODE_ENV === 'development') {\n        // eslint-disable-next-line no-console\n        console.warn(\n          \"You should not used the yup options context. Please, use the 'useForm' context object instead\",\n        );\n      }\n\n      const result = await schema[\n        resolverOptions.mode === 'sync' ? 'validateSync' : 'validate'\n      ](\n        values,\n        Object.assign({ abortEarly: false }, schemaOptions, { context }),\n      );\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        values: resolverOptions.raw ? Object.assign({}, values) : result,\n        errors: {},\n      };\n    } catch (e: any) {\n      if (!e.inner) {\n        throw e;\n      }\n\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrorSchema(\n            e,\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n  };\n}\n"], "mappings": ";;AAoFM,SAAUA,EACdA,CAAA,EAGAC,CAAA,EACAC,CAAA;EAKA,kBALAA,CAAA,KAAAA,CAAA,GAGI,KAEJ,UAAcC,CAAA,EAAeC,CAAA,EAASC,CAAA;IAAO;MAAA,OAAIC,OAAA,CAAAC,OAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA;UAAA,IAAAC,CAAA,IAEzC,QAAAT,CAAA,IAAAA,CAAA,CAAeU,OAAA,IAAoC,kBAAzBC,OAAA,CAAQC,GAAA,CAAIC,QAAA,IAExCC,OAAA,CAAQC,IAAA,CACN,kGAEHV,OAAA,CAAAC,OAAA,CAEoBP,CAAA,CACM,WAAzBE,CAAA,CAAgBe,IAAA,GAAkB,iBAAiB,YAEnDd,CAAA,EACAe,MAAA,CAAOC,MAAA,CAAO;YAAEC,UAAA,GAAY;UAAA,GAASnB,CAAA,EAAe;YAAEU,OAAA,EAAAP;UAAA,KACvDiB,IAAA,WALKb,CAAA;YASN,OAFAH,CAAA,CAAQiB,yBAAA,IAA6BC,CAAA,CAAuB,IAAIlB,CAAA,GAEzD;cACLmB,MAAA,EAAQtB,CAAA,CAAgBuB,GAAA,GAAMP,MAAA,CAAOC,MAAA,CAAO,CAAE,GAAEhB,CAAA,IAAUK,CAAA;cAC1DkB,MAAA,EAAQ;YAAA,CACR;UAAA;QAAA,SAAAH,CAAA;UAAA,OAAAd,CAAA,CAAAc,CAAA;QAAA;QAAA,OAAAb,CAAA,IAAAA,CAAA,CAAAW,IAAA,GAAAX,CAAA,CAAAW,IAAA,SAAAZ,CAAA,IAAAC,CAAA;MAAA,CArB2C,IAsB9C,UAAQa,CAAA;QACP,KAAKA,CAAA,CAAEI,KAAA,EACL,MAAMJ,CAAA;QAGR,OAAO;UACLC,MAAA,EAAQ;UACRE,MAAA,EAAQlB,CAAA,EA7GdR,CAAA,GA+GUuB,CAAA,EA9GVtB,CAAA,IA+GWI,CAAA,CAAQiB,yBAAA,IACkB,UAAzBjB,CAAA,CAAQuB,YAAA,GA9GZ5B,CAAA,CAAM2B,KAAA,IAAS,IAAIE,MAAA,CACzB,UAACN,CAAA,EAAUf,CAAA;YAKT,IAJKe,CAAA,CAASf,CAAA,CAAMsB,IAAA,MAClBP,CAAA,CAASf,CAAA,CAAMsB,IAAA,IAAS;cAAEC,OAAA,EAASvB,CAAA,CAAMuB,OAAA;cAASC,IAAA,EAAMxB,CAAA,CAAMwB;YAAA,IAG5D/B,CAAA,EAA0B;cAC5B,IAAMD,CAAA,GAAQuB,CAAA,CAASf,CAAA,CAAMsB,IAAA,EAAOG,KAAA;gBAC9B/B,CAAA,GAAWF,CAAA,IAASA,CAAA,CAAMQ,CAAA,CAAMwB,IAAA;cAEtCT,CAAA,CAASf,CAAA,CAAMsB,IAAA,IAASrB,CAAA,CACtBD,CAAA,CAAMsB,IAAA,EACN7B,CAAA,EACAsB,CAAA,EACAf,CAAA,CAAMwB,IAAA,EACN9B,CAAA,GACK,GAAgBgC,MAAA,CAAOhC,CAAA,EAAsBM,CAAA,CAAMuB,OAAA,IACpDvB,CAAA,CAAMuB,OAAA,CAEd;YAAA;YAEA,OAAOR,CACT;UAAA,GACA,MAyFMlB,CAAA;QAAA;QApHV,IACEL,CAAA,EACAC,CAqHE;MAAA,GACF;IAAA,CAAC,QAAAsB,CAAA;MAAA,OAAAjB,OAAA,CAAA6B,MAAA,CAAAZ,CAAA,CACH;IAAA;EAAA;AAAA;AAAA,SAAAvB,CAAA,IAAAoC,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}