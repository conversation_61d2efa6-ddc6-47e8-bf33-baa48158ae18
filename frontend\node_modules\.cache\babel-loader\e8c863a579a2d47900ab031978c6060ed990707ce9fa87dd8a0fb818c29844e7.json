{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Collapse from './Collapse';\nimport AccordionContext, { isAccordionItemSelected } from './AccordionContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * This component accepts all of [`Collapse`'s props](/docs/utilities/transitions#collapse-1).\n */\nconst AccordionCollapse = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    as: Component = 'div',\n    bsPrefix,\n    className,\n    children,\n    eventKey,\n    ...props\n  } = _ref;\n  const {\n    activeEventKey\n  } = useContext(AccordionContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-collapse');\n  return /*#__PURE__*/_jsx(Collapse, {\n    ref: ref,\n    in: isAccordionItemSelected(activeEventKey, eventKey),\n    ...props,\n    className: classNames(className, bsPrefix),\n    children: /*#__PURE__*/_jsx(Component, {\n      children: React.Children.only(children)\n    })\n  });\n});\nAccordionCollapse.displayName = 'AccordionCollapse';\nexport default AccordionCollapse;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useBootstrapPrefix", "Collapse", "AccordionContext", "isAccordionItemSelected", "jsx", "_jsx", "AccordionCollapse", "forwardRef", "_ref", "ref", "as", "Component", "bsPrefix", "className", "children", "eventKey", "props", "activeEventKey", "in", "Children", "only", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/AccordionCollapse.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Collapse from './Collapse';\nimport AccordionContext, { isAccordionItemSelected } from './AccordionContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * This component accepts all of [`Collapse`'s props](/docs/utilities/transitions#collapse-1).\n */\nconst AccordionCollapse = /*#__PURE__*/React.forwardRef(({\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  children,\n  eventKey,\n  ...props\n}, ref) => {\n  const {\n    activeEventKey\n  } = useContext(AccordionContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-collapse');\n  return /*#__PURE__*/_jsx(Collapse, {\n    ref: ref,\n    in: isAccordionItemSelected(activeEventKey, eventKey),\n    ...props,\n    className: classNames(className, bsPrefix),\n    children: /*#__PURE__*/_jsx(Component, {\n      children: React.Children.only(children)\n    })\n  });\n});\nAccordionCollapse.displayName = 'AccordionCollapse';\nexport default AccordionCollapse;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,gBAAgB,IAAIC,uBAAuB,QAAQ,oBAAoB;AAC9E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAOrDC,GAAG,KAAK;EAAA,IAP8C;IACvDC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrBC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACRC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAAR,IAAA;EACC,MAAM;IACJS;EACF,CAAC,GAAGlB,UAAU,CAACG,gBAAgB,CAAC;EAChCU,QAAQ,GAAGZ,kBAAkB,CAACY,QAAQ,EAAE,oBAAoB,CAAC;EAC7D,OAAO,aAAaP,IAAI,CAACJ,QAAQ,EAAE;IACjCQ,GAAG,EAAEA,GAAG;IACRS,EAAE,EAAEf,uBAAuB,CAACc,cAAc,EAAEF,QAAQ,CAAC;IACrD,GAAGC,KAAK;IACRH,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAED,QAAQ,CAAC;IAC1CE,QAAQ,EAAE,aAAaT,IAAI,CAACM,SAAS,EAAE;MACrCG,QAAQ,EAAEhB,KAAK,CAACqB,QAAQ,CAACC,IAAI,CAACN,QAAQ;IACxC,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,iBAAiB,CAACe,WAAW,GAAG,mBAAmB;AACnD,eAAef,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}