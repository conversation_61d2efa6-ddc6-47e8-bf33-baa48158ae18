{"ast": null, "code": "export { default } from \"./ScopedCssBaseline.js\";\nexport { default as scopedCssBaselineClasses } from \"./scopedCssBaselineClasses.js\";\nexport * from \"./scopedCssBaselineClasses.js\";", "map": {"version": 3, "names": ["default", "scopedCssBaselineClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/ScopedCssBaseline/index.js"], "sourcesContent": ["export { default } from \"./ScopedCssBaseline.js\";\nexport { default as scopedCssBaselineClasses } from \"./scopedCssBaselineClasses.js\";\nexport * from \"./scopedCssBaselineClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASA,OAAO,IAAIC,wBAAwB,QAAQ,+BAA+B;AACnF,cAAc,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}