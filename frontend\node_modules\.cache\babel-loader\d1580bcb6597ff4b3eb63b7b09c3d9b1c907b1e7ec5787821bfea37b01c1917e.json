{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useButtonProps } from '@restart/ui/Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Button = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    as,\n    bsPrefix,\n    variant = 'primary',\n    size,\n    active = false,\n    disabled = false,\n    className,\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn');\n  const [buttonProps, {\n    tagName\n  }] = useButtonProps({\n    tagName: as,\n    disabled,\n    ...props\n  });\n  const Component = tagName;\n  return /*#__PURE__*/_jsx(Component, {\n    ...buttonProps,\n    ...props,\n    ref: ref,\n    disabled: disabled,\n    className: classNames(className, prefix, active && 'active', variant && `${prefix}-${variant}`, size && `${prefix}-${size}`, props.href && disabled && 'disabled')\n  });\n});\nButton.displayName = 'Button';\nexport default Button;", "map": {"version": 3, "names": ["classNames", "React", "useButtonProps", "useBootstrapPrefix", "jsx", "_jsx", "<PERSON><PERSON>", "forwardRef", "_ref", "ref", "as", "bsPrefix", "variant", "size", "active", "disabled", "className", "props", "prefix", "buttonProps", "tagName", "Component", "href", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Button.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useButtonProps } from '@restart/ui/Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Button = /*#__PURE__*/React.forwardRef(({\n  as,\n  bsPrefix,\n  variant = 'primary',\n  size,\n  active = false,\n  disabled = false,\n  className,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn');\n  const [buttonProps, {\n    tagName\n  }] = useButtonProps({\n    tagName: as,\n    disabled,\n    ...props\n  });\n  const Component = tagName;\n  return /*#__PURE__*/_jsx(Component, {\n    ...buttonProps,\n    ...props,\n    ref: ref,\n    disabled: disabled,\n    className: classNames(className, prefix, active && 'active', variant && `${prefix}-${variant}`, size && `${prefix}-${size}`, props.href && disabled && 'disabled')\n  });\n});\nButton.displayName = 'Button';\nexport default Button;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,MAAM,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAAC,IAAA,EAS1CC,GAAG,KAAK;EAAA,IATmC;IAC5CC,EAAE;IACFC,QAAQ;IACRC,OAAO,GAAG,SAAS;IACnBC,IAAI;IACJC,MAAM,GAAG,KAAK;IACdC,QAAQ,GAAG,KAAK;IAChBC,SAAS;IACT,GAAGC;EACL,CAAC,GAAAT,IAAA;EACC,MAAMU,MAAM,GAAGf,kBAAkB,CAACQ,QAAQ,EAAE,KAAK,CAAC;EAClD,MAAM,CAACQ,WAAW,EAAE;IAClBC;EACF,CAAC,CAAC,GAAGlB,cAAc,CAAC;IAClBkB,OAAO,EAAEV,EAAE;IACXK,QAAQ;IACR,GAAGE;EACL,CAAC,CAAC;EACF,MAAMI,SAAS,GAAGD,OAAO;EACzB,OAAO,aAAaf,IAAI,CAACgB,SAAS,EAAE;IAClC,GAAGF,WAAW;IACd,GAAGF,KAAK;IACRR,GAAG,EAAEA,GAAG;IACRM,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAEE,MAAM,EAAEJ,MAAM,IAAI,QAAQ,EAAEF,OAAO,IAAI,GAAGM,MAAM,IAAIN,OAAO,EAAE,EAAEC,IAAI,IAAI,GAAGK,MAAM,IAAIL,IAAI,EAAE,EAAEI,KAAK,CAACK,IAAI,IAAIP,QAAQ,IAAI,UAAU;EACnK,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,MAAM,CAACiB,WAAW,GAAG,QAAQ;AAC7B,eAAejB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}