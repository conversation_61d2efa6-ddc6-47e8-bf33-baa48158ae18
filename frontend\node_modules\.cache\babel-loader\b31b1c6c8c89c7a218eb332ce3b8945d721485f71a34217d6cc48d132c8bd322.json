{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "map": {"version": 3, "names": ["React", "classNames", "useBootstrapPrefix", "divWithClassName", "jsx", "_jsx", "DivStyledAsH4", "displayName", "AlertHeading", "forwardRef", "className", "bsPrefix", "as", "Component", "props", "ref"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/AlertHeading.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGH,gBAAgB,CAAC,IAAI,CAAC;AAC5CG,aAAa,CAACC,WAAW,GAAG,eAAe;AAC3C,MAAMC,YAAY,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAC;EAClDC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAGP,aAAa;EAC7B,GAAGQ;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,eAAe,CAAC;EACxD,OAAO,aAAaN,IAAI,CAACQ,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAET,UAAU,CAACS,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,YAAY,CAACD,WAAW,GAAG,cAAc;AACzC,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}