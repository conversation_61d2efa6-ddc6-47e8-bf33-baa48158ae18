{"ast": null, "code": "/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar b = \"function\" === typeof Symbol && Symbol.for,\n  c = b ? Symbol.for(\"react.element\") : 60103,\n  d = b ? Symbol.for(\"react.portal\") : 60106,\n  e = b ? Symbol.for(\"react.fragment\") : 60107,\n  f = b ? Symbol.for(\"react.strict_mode\") : 60108,\n  g = b ? Symbol.for(\"react.profiler\") : 60114,\n  h = b ? Symbol.for(\"react.provider\") : 60109,\n  k = b ? Symbol.for(\"react.context\") : 60110,\n  l = b ? Symbol.for(\"react.async_mode\") : 60111,\n  m = b ? Symbol.for(\"react.concurrent_mode\") : 60111,\n  n = b ? Symbol.for(\"react.forward_ref\") : 60112,\n  p = b ? Symbol.for(\"react.suspense\") : 60113,\n  q = b ? Symbol.for(\"react.suspense_list\") : 60120,\n  r = b ? Symbol.for(\"react.memo\") : 60115,\n  t = b ? Symbol.for(\"react.lazy\") : 60116,\n  v = b ? Symbol.for(\"react.block\") : 60121,\n  w = b ? Symbol.for(\"react.fundamental\") : 60117,\n  x = b ? Symbol.for(\"react.responder\") : 60118,\n  y = b ? Symbol.for(\"react.scope\") : 60119;\nfunction z(a) {\n  if (\"object\" === typeof a && null !== a) {\n    var u = a.$$typeof;\n    switch (u) {\n      case c:\n        switch (a = a.type, a) {\n          case l:\n          case m:\n          case e:\n          case g:\n          case f:\n          case p:\n            return a;\n          default:\n            switch (a = a && a.$$typeof, a) {\n              case k:\n              case n:\n              case t:\n              case r:\n              case h:\n                return a;\n              default:\n                return u;\n            }\n        }\n      case d:\n        return u;\n    }\n  }\n}\nfunction A(a) {\n  return z(a) === m;\n}\nexports.AsyncMode = l;\nexports.ConcurrentMode = m;\nexports.ContextConsumer = k;\nexports.ContextProvider = h;\nexports.Element = c;\nexports.ForwardRef = n;\nexports.Fragment = e;\nexports.Lazy = t;\nexports.Memo = r;\nexports.Portal = d;\nexports.Profiler = g;\nexports.StrictMode = f;\nexports.Suspense = p;\nexports.isAsyncMode = function (a) {\n  return A(a) || z(a) === l;\n};\nexports.isConcurrentMode = A;\nexports.isContextConsumer = function (a) {\n  return z(a) === k;\n};\nexports.isContextProvider = function (a) {\n  return z(a) === h;\n};\nexports.isElement = function (a) {\n  return \"object\" === typeof a && null !== a && a.$$typeof === c;\n};\nexports.isForwardRef = function (a) {\n  return z(a) === n;\n};\nexports.isFragment = function (a) {\n  return z(a) === e;\n};\nexports.isLazy = function (a) {\n  return z(a) === t;\n};\nexports.isMemo = function (a) {\n  return z(a) === r;\n};\nexports.isPortal = function (a) {\n  return z(a) === d;\n};\nexports.isProfiler = function (a) {\n  return z(a) === g;\n};\nexports.isStrictMode = function (a) {\n  return z(a) === f;\n};\nexports.isSuspense = function (a) {\n  return z(a) === p;\n};\nexports.isValidElementType = function (a) {\n  return \"string\" === typeof a || \"function\" === typeof a || a === e || a === m || a === g || a === f || a === p || a === q || \"object\" === typeof a && null !== a && (a.$$typeof === t || a.$$typeof === r || a.$$typeof === h || a.$$typeof === k || a.$$typeof === n || a.$$typeof === w || a.$$typeof === x || a.$$typeof === y || a.$$typeof === v);\n};\nexports.typeOf = z;", "map": {"version": 3, "names": ["b", "Symbol", "for", "c", "d", "e", "f", "g", "h", "k", "l", "m", "n", "p", "q", "r", "t", "v", "w", "x", "y", "z", "a", "u", "$$typeof", "type", "A", "exports", "AsyncMode", "ConcurrentMode", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Fragment", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "isAsyncMode", "isConcurrentMode", "isContextConsumer", "isContextProvider", "isElement", "isForwardRef", "isFragment", "isLazy", "isMemo", "isPortal", "isProfiler", "isStrictMode", "isSuspense", "isValidElementType", "typeOf"], "sources": ["C:/laragon/www/frontend/node_modules/react-is/cjs/react-is.production.min.js"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';var b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?\nSymbol.for(\"react.suspense_list\"):60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.block\"):60121,w=b?Symbol.for(\"react.fundamental\"):60117,x=b?Symbol.for(\"react.responder\"):60118,y=b?Symbol.for(\"react.scope\"):60119;\nfunction z(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}exports.AsyncMode=l;exports.ConcurrentMode=m;exports.ContextConsumer=k;exports.ContextProvider=h;exports.Element=c;exports.ForwardRef=n;exports.Fragment=e;exports.Lazy=t;exports.Memo=r;exports.Portal=d;\nexports.Profiler=g;exports.StrictMode=f;exports.Suspense=p;exports.isAsyncMode=function(a){return A(a)||z(a)===l};exports.isConcurrentMode=A;exports.isContextConsumer=function(a){return z(a)===k};exports.isContextProvider=function(a){return z(a)===h};exports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===c};exports.isForwardRef=function(a){return z(a)===n};exports.isFragment=function(a){return z(a)===e};exports.isLazy=function(a){return z(a)===t};\nexports.isMemo=function(a){return z(a)===r};exports.isPortal=function(a){return z(a)===d};exports.isProfiler=function(a){return z(a)===g};exports.isStrictMode=function(a){return z(a)===f};exports.isSuspense=function(a){return z(a)===p};\nexports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};exports.typeOf=z;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAAC,IAAIA,CAAC,GAAC,UAAU,KAAG,OAAOC,MAAM,IAAEA,MAAM,CAACC,GAAG;EAACC,CAAC,GAACH,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC,GAAC,KAAK;EAACE,CAAC,GAACJ,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,cAAc,CAAC,GAAC,KAAK;EAACG,CAAC,GAACL,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC,GAAC,KAAK;EAACI,CAAC,GAACN,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC,GAAC,KAAK;EAACK,CAAC,GAACP,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC,GAAC,KAAK;EAACM,CAAC,GAACR,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC,GAAC,KAAK;EAACO,CAAC,GAACT,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC,GAAC,KAAK;EAACQ,CAAC,GAACV,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,kBAAkB,CAAC,GAAC,KAAK;EAACS,CAAC,GAACX,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,uBAAuB,CAAC,GAAC,KAAK;EAACU,CAAC,GAACZ,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC,GAAC,KAAK;EAACW,CAAC,GAACb,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC,GAAC,KAAK;EAACY,CAAC,GAACd,CAAC,GACrfC,MAAM,CAACC,GAAG,CAAC,qBAAqB,CAAC,GAAC,KAAK;EAACa,CAAC,GAACf,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,YAAY,CAAC,GAAC,KAAK;EAACc,CAAC,GAAChB,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,YAAY,CAAC,GAAC,KAAK;EAACe,CAAC,GAACjB,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,aAAa,CAAC,GAAC,KAAK;EAACgB,CAAC,GAAClB,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC,GAAC,KAAK;EAACiB,CAAC,GAACnB,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,iBAAiB,CAAC,GAAC,KAAK;EAACkB,CAAC,GAACpB,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,aAAa,CAAC,GAAC,KAAK;AACvQ,SAASmB,CAACA,CAACC,CAAC,EAAC;EAAC,IAAG,QAAQ,KAAG,OAAOA,CAAC,IAAE,IAAI,KAAGA,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,QAAQ;IAAC,QAAOD,CAAC;MAAE,KAAKpB,CAAC;QAAC,QAAOmB,CAAC,GAACA,CAAC,CAACG,IAAI,EAACH,CAAC;UAAE,KAAKZ,CAAC;UAAC,KAAKC,CAAC;UAAC,KAAKN,CAAC;UAAC,KAAKE,CAAC;UAAC,KAAKD,CAAC;UAAC,KAAKO,CAAC;YAAC,OAAOS,CAAC;UAAC;YAAQ,QAAOA,CAAC,GAACA,CAAC,IAAEA,CAAC,CAACE,QAAQ,EAACF,CAAC;cAAE,KAAKb,CAAC;cAAC,KAAKG,CAAC;cAAC,KAAKI,CAAC;cAAC,KAAKD,CAAC;cAAC,KAAKP,CAAC;gBAAC,OAAOc,CAAC;cAAC;gBAAQ,OAAOC,CAAC;YAAA;QAAC;MAAC,KAAKnB,CAAC;QAAC,OAAOmB,CAAC;IAAA;EAAC;AAAC;AAAC,SAASG,CAACA,CAACJ,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGX,CAAC;AAAA;AAACgB,OAAO,CAACC,SAAS,GAAClB,CAAC;AAACiB,OAAO,CAACE,cAAc,GAAClB,CAAC;AAACgB,OAAO,CAACG,eAAe,GAACrB,CAAC;AAACkB,OAAO,CAACI,eAAe,GAACvB,CAAC;AAACmB,OAAO,CAACK,OAAO,GAAC7B,CAAC;AAACwB,OAAO,CAACM,UAAU,GAACrB,CAAC;AAACe,OAAO,CAACO,QAAQ,GAAC7B,CAAC;AAACsB,OAAO,CAACQ,IAAI,GAACnB,CAAC;AAACW,OAAO,CAACS,IAAI,GAACrB,CAAC;AAACY,OAAO,CAACU,MAAM,GAACjC,CAAC;AACjfuB,OAAO,CAACW,QAAQ,GAAC/B,CAAC;AAACoB,OAAO,CAACY,UAAU,GAACjC,CAAC;AAACqB,OAAO,CAACa,QAAQ,GAAC3B,CAAC;AAACc,OAAO,CAACc,WAAW,GAAC,UAASnB,CAAC,EAAC;EAAC,OAAOI,CAAC,CAACJ,CAAC,CAAC,IAAED,CAAC,CAACC,CAAC,CAAC,KAAGZ,CAAC;AAAA,CAAC;AAACiB,OAAO,CAACe,gBAAgB,GAAChB,CAAC;AAACC,OAAO,CAACgB,iBAAiB,GAAC,UAASrB,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGb,CAAC;AAAA,CAAC;AAACkB,OAAO,CAACiB,iBAAiB,GAAC,UAAStB,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGd,CAAC;AAAA,CAAC;AAACmB,OAAO,CAACkB,SAAS,GAAC,UAASvB,CAAC,EAAC;EAAC,OAAM,QAAQ,KAAG,OAAOA,CAAC,IAAE,IAAI,KAAGA,CAAC,IAAEA,CAAC,CAACE,QAAQ,KAAGrB,CAAC;AAAA,CAAC;AAACwB,OAAO,CAACmB,YAAY,GAAC,UAASxB,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGV,CAAC;AAAA,CAAC;AAACe,OAAO,CAACoB,UAAU,GAAC,UAASzB,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGjB,CAAC;AAAA,CAAC;AAACsB,OAAO,CAACqB,MAAM,GAAC,UAAS1B,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGN,CAAC;AAAA,CAAC;AAC3dW,OAAO,CAACsB,MAAM,GAAC,UAAS3B,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGP,CAAC;AAAA,CAAC;AAACY,OAAO,CAACuB,QAAQ,GAAC,UAAS5B,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGlB,CAAC;AAAA,CAAC;AAACuB,OAAO,CAACwB,UAAU,GAAC,UAAS7B,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGf,CAAC;AAAA,CAAC;AAACoB,OAAO,CAACyB,YAAY,GAAC,UAAS9B,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGhB,CAAC;AAAA,CAAC;AAACqB,OAAO,CAAC0B,UAAU,GAAC,UAAS/B,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGT,CAAC;AAAA,CAAC;AAC3Oc,OAAO,CAAC2B,kBAAkB,GAAC,UAAShC,CAAC,EAAC;EAAC,OAAM,QAAQ,KAAG,OAAOA,CAAC,IAAE,UAAU,KAAG,OAAOA,CAAC,IAAEA,CAAC,KAAGjB,CAAC,IAAEiB,CAAC,KAAGX,CAAC,IAAEW,CAAC,KAAGf,CAAC,IAAEe,CAAC,KAAGhB,CAAC,IAAEgB,CAAC,KAAGT,CAAC,IAAES,CAAC,KAAGR,CAAC,IAAE,QAAQ,KAAG,OAAOQ,CAAC,IAAE,IAAI,KAAGA,CAAC,KAAGA,CAAC,CAACE,QAAQ,KAAGR,CAAC,IAAEM,CAAC,CAACE,QAAQ,KAAGT,CAAC,IAAEO,CAAC,CAACE,QAAQ,KAAGhB,CAAC,IAAEc,CAAC,CAACE,QAAQ,KAAGf,CAAC,IAAEa,CAAC,CAACE,QAAQ,KAAGZ,CAAC,IAAEU,CAAC,CAACE,QAAQ,KAAGN,CAAC,IAAEI,CAAC,CAACE,QAAQ,KAAGL,CAAC,IAAEG,CAAC,CAACE,QAAQ,KAAGJ,CAAC,IAAEE,CAAC,CAACE,QAAQ,KAAGP,CAAC,CAAC;AAAA,CAAC;AAACU,OAAO,CAAC4B,MAAM,GAAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}