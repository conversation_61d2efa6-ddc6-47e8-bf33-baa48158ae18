{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 7h2v6h-2zm0 8h2v2h-2z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 15h2v2h-2zm0-8h2v6h-2z\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M17 1.01 7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99M17 21H7v-1h10zm0-3H7V6h10zm0-14H7V3h10z\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M7 21h10v-1H7zM7 3v1h10V3z\",\n  opacity: \".3\"\n}, \"3\")], 'SystemSecurityUpdateWarningTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/icons-material/esm/SystemSecurityUpdateWarningTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 7h2v6h-2zm0 8h2v2h-2z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M11 15h2v2h-2zm0-8h2v6h-2z\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M17 1.01 7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99M17 21H7v-1h10zm0-3H7V6h10zm0-14H7V3h10z\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M7 21h10v-1H7zM7 3v1h10V3z\",\n  opacity: \".3\"\n}, \"3\")], 'SystemSecurityUpdateWarningTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,2BAA2B;EAC9BC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE,4BAA4B;EAC/BC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,oCAAoC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}