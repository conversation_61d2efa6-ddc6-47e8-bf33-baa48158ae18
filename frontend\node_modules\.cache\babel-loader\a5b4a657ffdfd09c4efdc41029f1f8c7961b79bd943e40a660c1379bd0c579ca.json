{"ast": null, "code": "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols) for (var prop of __getOwnPropSymbols(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\nvar __esm = (fn, res) => function __init() {\n  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;\n};\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {\n    exports: {}\n  }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from)) if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n      get: () => from[key],\n      enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n    });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n// If the importer is in node compatibility mode or this is not an ESM\n// file that has been converted to a CommonJS file using a Babel-\n// compatible transform (i.e. \"__esModule\" has not been set), then set\n// \"default\" to the CommonJS \"module.exports\" for node compatibility.\nisNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", {\n  value: mod,\n  enumerable: true\n}) : target, mod));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = value => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = value => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = x => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// ../tsup-config/react-import.js\nimport React from \"react\";\nvar init_react_import = __esm({\n  \"../tsup-config/react-import.js\"() {\n    \"use strict\";\n  }\n});\n\n// rsc.tsx\ninit_react_import();\n\n// components/ServerRender/index.tsx\ninit_react_import();\n\n// lib/root-droppable-id.ts\ninit_react_import();\nvar rootAreaId = \"root\";\nvar rootZone = \"default-zone\";\nvar rootDroppableId = `${rootAreaId}:${rootZone}`;\n\n// lib/data/setup-zone.ts\ninit_react_import();\nvar setupZone = (data, zoneKey) => {\n  if (zoneKey === rootDroppableId) {\n    return data;\n  }\n  const newData = __spreadProps(__spreadValues({}, data), {\n    zones: data.zones ? __spreadValues({}, data.zones) : {}\n  });\n  newData.zones[zoneKey] = newData.zones[zoneKey] || [];\n  return newData;\n};\n\n// lib/use-slots.tsx\ninit_react_import();\nimport { useMemo } from \"react\";\n\n// lib/data/map-slots.ts\ninit_react_import();\n\n// lib/data/default-slots.ts\ninit_react_import();\nvar defaultSlots = (value, fields) => Object.keys(fields).reduce((acc, fieldName) => fields[fieldName].type === \"slot\" ? __spreadValues({\n  [fieldName]: []\n}, acc) : acc, value);\n\n// lib/data/map-slots.ts\nvar isPromise = v => !!v && typeof v.then === \"function\";\nvar flatten = values => values.reduce((acc, item) => __spreadValues(__spreadValues({}, acc), item), {});\nvar containsPromise = arr => arr.some(isPromise);\nvar walkField = _ref => {\n  let {\n    value,\n    fields,\n    map,\n    propKey = \"\",\n    propPath = \"\",\n    id = \"\",\n    config,\n    recurseSlots = false\n  } = _ref;\n  var _a, _b, _c;\n  if (((_a = fields[propKey]) == null ? void 0 : _a.type) === \"slot\") {\n    const content = value || [];\n    const mappedContent = recurseSlots ? content.map(el => {\n      var _a2;\n      const componentConfig = config.components[el.type];\n      if (!componentConfig) {\n        throw new Error(`Could not find component config for ${el.type}`);\n      }\n      const fields2 = (_a2 = componentConfig.fields) != null ? _a2 : {};\n      return walkField({\n        value: __spreadProps(__spreadValues({}, el), {\n          props: defaultSlots(el.props, fields2)\n        }),\n        fields: fields2,\n        map,\n        id: el.props.id,\n        config,\n        recurseSlots\n      });\n    }) : content;\n    if (containsPromise(mappedContent)) {\n      return Promise.all(mappedContent);\n    }\n    return map(mappedContent, id, propPath, fields[propKey], propPath);\n  }\n  if (value && typeof value === \"object\") {\n    if (Array.isArray(value)) {\n      const arrayFields = ((_b = fields[propKey]) == null ? void 0 : _b.type) === \"array\" ? fields[propKey].arrayFields : null;\n      if (!arrayFields) return value;\n      const newValue = value.map((el, idx) => walkField({\n        value: el,\n        fields: arrayFields,\n        map,\n        propKey,\n        propPath: `${propPath}[${idx}]`,\n        id,\n        config,\n        recurseSlots\n      }));\n      if (containsPromise(newValue)) {\n        return Promise.all(newValue);\n      }\n      return newValue;\n    } else if (\"$$typeof\" in value) {\n      return value;\n    } else {\n      const objectFields = ((_c = fields[propKey]) == null ? void 0 : _c.type) === \"object\" ? fields[propKey].objectFields : fields;\n      return walkObject({\n        value,\n        fields: objectFields,\n        map,\n        id,\n        getPropPath: k => `${propPath}.${k}`,\n        config,\n        recurseSlots\n      });\n    }\n  }\n  return value;\n};\nvar walkObject = _ref2 => {\n  let {\n    value,\n    fields,\n    map,\n    id,\n    getPropPath,\n    config,\n    recurseSlots\n  } = _ref2;\n  const newProps = Object.entries(value).map(_ref3 => {\n    let [k, v] = _ref3;\n    const opts = {\n      value: v,\n      fields,\n      map,\n      propKey: k,\n      propPath: getPropPath(k),\n      id,\n      config,\n      recurseSlots\n    };\n    const newValue = walkField(opts);\n    if (isPromise(newValue)) {\n      return newValue.then(resolvedValue => ({\n        [k]: resolvedValue\n      }));\n    }\n    return {\n      [k]: newValue\n    };\n  }, {});\n  if (containsPromise(newProps)) {\n    return Promise.all(newProps).then(flatten);\n  }\n  return flatten(newProps);\n};\nfunction mapSlots(item, map, config) {\n  let recurseSlots = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var _a, _b, _c, _d, _e;\n  const itemType = \"type\" in item ? item.type : \"root\";\n  const componentConfig = itemType === \"root\" ? config.root : (_a = config.components) == null ? void 0 : _a[itemType];\n  const newProps = walkObject({\n    value: defaultSlots((_b = item.props) != null ? _b : {}, (_c = componentConfig == null ? void 0 : componentConfig.fields) != null ? _c : {}),\n    fields: (_d = componentConfig == null ? void 0 : componentConfig.fields) != null ? _d : {},\n    map,\n    id: item.props ? (_e = item.props.id) != null ? _e : \"root\" : \"root\",\n    getPropPath: k => k,\n    config,\n    recurseSlots\n  });\n  if (isPromise(newProps)) {\n    return newProps.then(resolvedProps => __spreadProps(__spreadValues({}, item), {\n      props: resolvedProps\n    }));\n  }\n  return __spreadProps(__spreadValues({}, item), {\n    props: newProps\n  });\n}\n\n// lib/use-slots.tsx\nfunction useSlots(config, item, renderSlotEdit) {\n  let renderSlotRender = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : renderSlotEdit;\n  let readOnly = arguments.length > 4 ? arguments[4] : undefined;\n  let forceReadOnly = arguments.length > 5 ? arguments[5] : undefined;\n  const slotProps = useMemo(() => {\n    const mapped = mapSlots(item, (content, _parentId, propName, field, propPath) => {\n      const wildcardPath = propPath.replace(/\\[\\d+\\]/g, \"[*]\");\n      const isReadOnly = (readOnly == null ? void 0 : readOnly[propPath]) || (readOnly == null ? void 0 : readOnly[wildcardPath]) || forceReadOnly;\n      const render = isReadOnly ? renderSlotRender : renderSlotEdit;\n      const Slot = dzProps => render(__spreadProps(__spreadValues({\n        allow: (field == null ? void 0 : field.type) === \"slot\" ? field.allow : [],\n        disallow: (field == null ? void 0 : field.type) === \"slot\" ? field.disallow : []\n      }, dzProps), {\n        zone: propName,\n        content\n      }));\n      return Slot;\n    }, config).props;\n    return mapped;\n  }, [config, item, readOnly, forceReadOnly]);\n  const mergedProps = useMemo(() => __spreadValues(__spreadValues({}, item.props), slotProps), [item.props, slotProps]);\n  return mergedProps;\n}\n\n// components/SlotRender/server.tsx\ninit_react_import();\nimport { forwardRef } from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar SlotRenderPure = props => /* @__PURE__ */jsx(SlotRender, __spreadValues({}, props));\nvar Item = _ref4 => {\n  let {\n    config,\n    item,\n    metadata\n  } = _ref4;\n  const Component = config.components[item.type];\n  const props = useSlots(config, item, slotProps => /* @__PURE__ */jsx(SlotRenderPure, __spreadProps(__spreadValues({}, slotProps), {\n    config,\n    metadata\n  })));\n  return /* @__PURE__ */jsx(Component.render, __spreadProps(__spreadValues({}, props), {\n    puck: __spreadProps(__spreadValues({}, props.puck), {\n      renderDropZone: DropZoneRender,\n      metadata: metadata || {}\n    })\n  }));\n};\nvar SlotRender = forwardRef(function SlotRenderInternal(_ref5, ref) {\n  let {\n    className,\n    style,\n    content,\n    config,\n    metadata\n  } = _ref5;\n  return /* @__PURE__ */jsx(\"div\", {\n    className,\n    style,\n    ref,\n    children: content.map(item => {\n      if (!config.components[item.type]) {\n        return null;\n      }\n      return /* @__PURE__ */jsx(Item, {\n        config,\n        item,\n        metadata\n      }, item.props.id);\n    })\n  });\n});\n\n// components/ServerRender/index.tsx\nimport { Fragment, jsx as jsx2 } from \"react/jsx-runtime\";\nfunction DropZoneRender(_ref6) {\n  let {\n    zone,\n    data,\n    areaId = \"root\",\n    config,\n    metadata = {}\n  } = _ref6;\n  let zoneCompound = rootDroppableId;\n  let content = (data == null ? void 0 : data.content) || [];\n  if (!data || !config) {\n    return null;\n  }\n  if (areaId !== rootAreaId && zone !== rootZone) {\n    zoneCompound = `${areaId}:${zone}`;\n    content = setupZone(data, zoneCompound).zones[zoneCompound];\n  }\n  return /* @__PURE__ */jsx2(Fragment, {\n    children: content.map(item => {\n      const Component = config.components[item.type];\n      const props = __spreadProps(__spreadValues({}, item.props), {\n        puck: {\n          renderDropZone: _ref7 => {\n            let {\n              zone: zone2\n            } = _ref7;\n            return /* @__PURE__ */jsx2(DropZoneRender, {\n              zone: zone2,\n              data,\n              areaId: item.props.id,\n              config,\n              metadata\n            });\n          },\n          metadata,\n          dragRef: null,\n          isEditing: false\n        }\n      });\n      const renderItem = __spreadProps(__spreadValues({}, item), {\n        props\n      });\n      const propsWithSlots = useSlots(config, renderItem, props2 => /* @__PURE__ */jsx2(SlotRenderPure, __spreadProps(__spreadValues({}, props2), {\n        config,\n        metadata\n      })));\n      if (Component) {\n        return /* @__PURE__ */jsx2(Component.render, __spreadValues({}, propsWithSlots), renderItem.props.id);\n      }\n      return null;\n    })\n  });\n}\nfunction Render(_ref8) {\n  let {\n    config,\n    data,\n    metadata = {}\n  } = _ref8;\n  var _a;\n  const rootProps = \"props\" in data.root ? data.root.props : data.root;\n  const title = rootProps.title || \"\";\n  const props = __spreadProps(__spreadValues({}, rootProps), {\n    puck: {\n      renderDropZone: _ref9 => {\n        let {\n          zone\n        } = _ref9;\n        return /* @__PURE__ */jsx2(DropZoneRender, {\n          zone,\n          data,\n          config,\n          metadata\n        });\n      },\n      isEditing: false,\n      dragRef: null,\n      metadata\n    },\n    title,\n    editMode: false,\n    id: \"puck-root\"\n  });\n  const propsWithSlots = useSlots(config, {\n    type: \"root\",\n    props\n  }, props2 => /* @__PURE__ */jsx2(SlotRenderPure, __spreadProps(__spreadValues({}, props2), {\n    config,\n    metadata\n  })));\n  if ((_a = config.root) == null ? void 0 : _a.render) {\n    return /* @__PURE__ */jsx2(config.root.render, __spreadProps(__spreadValues({}, propsWithSlots), {\n      children: /* @__PURE__ */jsx2(DropZoneRender, {\n        config,\n        data,\n        zone: rootZone,\n        metadata\n      })\n    }));\n  }\n  return /* @__PURE__ */jsx2(DropZoneRender, {\n    config,\n    data,\n    zone: rootZone,\n    metadata\n  });\n}\n\n// lib/resolve-all-data.ts\ninit_react_import();\n\n// lib/resolve-component-data.ts\ninit_react_import();\n\n// lib/get-changed.ts\ninit_react_import();\nimport fdeq from \"fast-deep-equal\";\nvar getChanged = (newItem, oldItem) => {\n  return newItem ? Object.keys(newItem.props || {}).reduce((acc, item) => {\n    const newItemProps = (newItem == null ? void 0 : newItem.props) || {};\n    const oldItemProps = (oldItem == null ? void 0 : oldItem.props) || {};\n    return __spreadProps(__spreadValues({}, acc), {\n      [item]: !fdeq(oldItemProps[item], newItemProps[item])\n    });\n  }, {}) : {};\n};\n\n// lib/resolve-component-data.ts\nimport fdeq2 from \"fast-deep-equal\";\nvar cache = {\n  lastChange: {}\n};\nvar resolveComponentData = function (_0, _1) {\n  for (var _len = arguments.length, _2 = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    _2[_key - 2] = arguments[_key];\n  }\n  return __async(void 0, [_0, _1, ..._2], function (item, config) {\n    let metadata = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let onResolveStart = arguments.length > 3 ? arguments[3] : undefined;\n    let onResolveEnd = arguments.length > 4 ? arguments[4] : undefined;\n    let trigger = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : \"replace\";\n    return function* () {\n      const configForItem = \"type\" in item && item.type !== \"root\" ? config.components[item.type] : config.root;\n      const resolvedItem = __spreadValues({}, item);\n      const shouldRunResolver = (configForItem == null ? void 0 : configForItem.resolveData) && item.props;\n      const id = \"id\" in item.props ? item.props.id : \"root\";\n      if (shouldRunResolver) {\n        const {\n          item: oldItem = null,\n          resolved = {}\n        } = cache.lastChange[id] || {};\n        if (item && fdeq2(item, oldItem)) {\n          return {\n            node: resolved,\n            didChange: false\n          };\n        }\n        const changed = getChanged(item, oldItem);\n        if (onResolveStart) {\n          onResolveStart(item);\n        }\n        const {\n          props: resolvedProps,\n          readOnly = {}\n        } = yield configForItem.resolveData(item, {\n          changed,\n          lastData: oldItem,\n          metadata: __spreadValues(__spreadValues({}, metadata), configForItem.metadata),\n          trigger\n        });\n        resolvedItem.props = __spreadValues(__spreadValues({}, item.props), resolvedProps);\n        if (Object.keys(readOnly).length) {\n          resolvedItem.readOnly = readOnly;\n        }\n      }\n      let itemWithResolvedChildren = yield mapSlots(resolvedItem, content => __async(void 0, null, function* () {\n        return yield Promise.all(content.map(childItem => __async(void 0, null, function* () {\n          return (yield resolveComponentData(childItem, config, metadata, onResolveStart, onResolveEnd, trigger)).node;\n        })));\n      }), config);\n      if (shouldRunResolver && onResolveEnd) {\n        onResolveEnd(resolvedItem);\n      }\n      cache.lastChange[id] = {\n        item,\n        resolved: itemWithResolvedChildren\n      };\n      return {\n        node: itemWithResolvedChildren,\n        didChange: !fdeq2(item, itemWithResolvedChildren)\n      };\n    }();\n  });\n};\n\n// lib/data/default-data.ts\ninit_react_import();\nvar defaultData = data => __spreadProps(__spreadValues({}, data), {\n  root: data.root || {},\n  content: data.content || []\n});\n\n// lib/data/to-component.ts\ninit_react_import();\nvar toComponent = item => {\n  return \"type\" in item ? item : __spreadProps(__spreadValues({}, item), {\n    props: __spreadProps(__spreadValues({}, item.props), {\n      id: \"root\"\n    }),\n    type: \"root\"\n  });\n};\n\n// lib/resolve-all-data.ts\nfunction resolveAllData(_0, _1) {\n  return __async(this, arguments, function (data, config) {\n    var _this = this;\n    let metadata = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let onResolveStart = arguments.length > 3 ? arguments[3] : undefined;\n    let onResolveEnd = arguments.length > 4 ? arguments[4] : undefined;\n    return function* () {\n      var _a;\n      const defaultedData = defaultData(data);\n      const resolveNode = _node => __async(_this, null, function* () {\n        const node = toComponent(_node);\n        onResolveStart == null ? void 0 : onResolveStart(node);\n        const resolved = (yield resolveComponentData(node, config, metadata, () => {}, () => {}, \"force\")).node;\n        const resolvedDeep = yield mapSlots(resolved, processContent, config);\n        onResolveEnd == null ? void 0 : onResolveEnd(toComponent(resolvedDeep));\n        return resolvedDeep;\n      });\n      const processContent = content => __async(_this, null, function* () {\n        return Promise.all(content.map(resolveNode));\n      });\n      const processZones = () => __async(_this, null, function* () {\n        var _a2;\n        const zones = (_a2 = data.zones) != null ? _a2 : {};\n        Object.entries(zones).forEach(_02 => __async(this, [_02], function (_ref0) {\n          let [zoneKey, content] = _ref0;\n          return function* () {\n            zones[zoneKey] = yield Promise.all(content.map(resolveNode));\n          }();\n        }));\n        return zones;\n      });\n      const dynamic = {\n        root: yield resolveNode(defaultedData.root),\n        content: yield processContent(defaultedData.content),\n        zones: yield processZones()\n      };\n      Object.keys((_a = defaultedData.zones) != null ? _a : {}).forEach(zoneKey => __async(_this, null, function* () {\n        const content = defaultedData.zones[zoneKey];\n        dynamic.zones[zoneKey] = yield processContent(content);\n      }), {});\n      return dynamic;\n    }();\n  });\n}\n\n// lib/transform-props.ts\ninit_react_import();\n\n// lib/data/walk-tree.ts\ninit_react_import();\nfunction walkTree(data, config, callbackFn) {\n  var _a, _b;\n  const walkItem = item => {\n    return mapSlots(item, (content, parentId, propName) => {\n      var _a2;\n      return (_a2 = callbackFn(content, {\n        parentId,\n        propName\n      })) != null ? _a2 : content;\n    }, config, true);\n  };\n  if (\"props\" in data) {\n    return walkItem(data);\n  }\n  const _data = data;\n  const zones = (_a = _data.zones) != null ? _a : {};\n  const mappedContent = _data.content.map(walkItem);\n  return {\n    root: walkItem(_data.root),\n    content: (_b = callbackFn(mappedContent, {\n      parentId: \"root\",\n      propName: \"default-zone\"\n    })) != null ? _b : mappedContent,\n    zones: Object.keys(zones).reduce((acc, zoneCompound) => __spreadProps(__spreadValues({}, acc), {\n      [zoneCompound]: zones[zoneCompound].map(walkItem)\n    }), {})\n  };\n}\n\n// lib/transform-props.ts\nfunction transformProps(data, propTransforms) {\n  let config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    components: {}\n  };\n  const mapItem = item => {\n    if (propTransforms[item.type]) {\n      return __spreadProps(__spreadValues({}, item), {\n        props: __spreadValues({\n          id: item.props.id\n        }, propTransforms[item.type](item.props))\n      });\n    }\n    return item;\n  };\n  const defaultedData = defaultData(data);\n  const rootProps = defaultedData.root.props || defaultedData.root;\n  let newRoot = __spreadValues({}, defaultedData.root);\n  if (propTransforms[\"root\"]) {\n    newRoot.props = propTransforms[\"root\"](rootProps);\n  }\n  const dataWithUpdatedRoot = __spreadProps(__spreadValues({}, defaultedData), {\n    root: newRoot\n  });\n  const updatedData = walkTree(dataWithUpdatedRoot, config, content => content.map(mapItem));\n  if (!defaultedData.root.props) {\n    updatedData.root = updatedData.root.props;\n  }\n  return updatedData;\n}\n\n// lib/migrate.ts\ninit_react_import();\n\n// store/default-app-state.ts\ninit_react_import();\n\n// components/ViewportControls/default-viewports.ts\ninit_react_import();\nvar defaultViewports = [{\n  width: 360,\n  height: \"auto\",\n  icon: \"Smartphone\",\n  label: \"Small\"\n}, {\n  width: 768,\n  height: \"auto\",\n  icon: \"Tablet\",\n  label: \"Medium\"\n}, {\n  width: 1280,\n  height: \"auto\",\n  icon: \"Monitor\",\n  label: \"Large\"\n}];\n\n// store/default-app-state.ts\nvar defaultAppState = {\n  data: {\n    content: [],\n    root: {},\n    zones: {}\n  },\n  ui: {\n    leftSideBarVisible: true,\n    rightSideBarVisible: true,\n    arrayState: {},\n    itemSelector: null,\n    componentList: {},\n    isDragging: false,\n    previewMode: \"edit\",\n    viewports: {\n      current: {\n        width: defaultViewports[0].width,\n        height: defaultViewports[0].height || \"auto\"\n      },\n      options: [],\n      controlsVisible: true\n    },\n    field: {\n      focus: null\n    }\n  },\n  indexes: {\n    nodes: {},\n    zones: {}\n  }\n};\n\n// lib/data/walk-app-state.ts\ninit_react_import();\n\n// lib/data/for-related-zones.ts\ninit_react_import();\n\n// lib/get-zone-id.ts\ninit_react_import();\nvar getZoneId = zoneCompound => {\n  if (!zoneCompound) {\n    return [];\n  }\n  if (zoneCompound && zoneCompound.indexOf(\":\") > -1) {\n    return zoneCompound.split(\":\");\n  }\n  return [rootDroppableId, zoneCompound];\n};\n\n// lib/data/for-related-zones.ts\nfunction forRelatedZones(item, data, cb) {\n  let path = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : [];\n  Object.entries(data.zones || {}).forEach(_ref1 => {\n    let [zoneCompound, content] = _ref1;\n    const [parentId] = getZoneId(zoneCompound);\n    if (parentId === item.props.id) {\n      cb(path, zoneCompound, content);\n    }\n  });\n}\n\n// lib/data/flatten-node.ts\ninit_react_import();\nimport flat from \"flat\";\n\n// lib/data/strip-slots.ts\ninit_react_import();\nvar stripSlots = (data, config) => {\n  return mapSlots(data, () => null, config);\n};\n\n// lib/data/flatten-node.ts\nvar {\n  flatten: flatten2,\n  unflatten\n} = flat;\nvar flattenNode = (node, config) => {\n  return __spreadProps(__spreadValues({}, node), {\n    props: flatten2(stripSlots(node, config).props)\n  });\n};\nvar expandNode = node => {\n  const props = unflatten(node.props);\n  return __spreadProps(__spreadValues({}, node), {\n    props\n  });\n};\n\n// lib/data/walk-app-state.ts\nfunction walkAppState(state, config) {\n  let mapContent = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : content => content;\n  let mapNodeOrSkip = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : item => item;\n  var _a;\n  let newZones = {};\n  const newZoneIndex = {};\n  const newNodeIndex = {};\n  const processContent = (path, zoneCompound, content, zoneType, newId) => {\n    var _a2;\n    const [parentId] = zoneCompound.split(\":\");\n    const mappedContent = ((_a2 = mapContent(content, zoneCompound, zoneType)) != null ? _a2 : content) || [];\n    const [_2, zone] = zoneCompound.split(\":\");\n    const newZoneCompound = `${newId || parentId}:${zone}`;\n    const newContent2 = mappedContent.map((zoneChild, index) => processItem(zoneChild, [...path, newZoneCompound], index));\n    newZoneIndex[newZoneCompound] = {\n      contentIds: newContent2.map(item => item.props.id),\n      type: zoneType\n    };\n    return [newZoneCompound, newContent2];\n  };\n  const processRelatedZones = (item, newId, initialPath) => {\n    forRelatedZones(item, state.data, (relatedPath, relatedZoneCompound, relatedContent) => {\n      const [zoneCompound, newContent2] = processContent(relatedPath, relatedZoneCompound, relatedContent, \"dropzone\", newId);\n      newZones[zoneCompound] = newContent2;\n    }, initialPath);\n  };\n  const processItem = (item, path, index) => {\n    const mappedItem = mapNodeOrSkip(item, path, index);\n    if (!mappedItem) return item;\n    const id = mappedItem.props.id;\n    const newProps = __spreadProps(__spreadValues({}, mapSlots(mappedItem, (content, parentId2, slotId) => {\n      const zoneCompound = `${parentId2}:${slotId}`;\n      const [_2, newContent2] = processContent(path, zoneCompound, content, \"slot\", parentId2);\n      return newContent2;\n    }, config).props), {\n      id\n    });\n    processRelatedZones(item, id, path);\n    const newItem = __spreadProps(__spreadValues({}, item), {\n      props: newProps\n    });\n    const thisZoneCompound = path[path.length - 1];\n    const [parentId, zone] = thisZoneCompound ? thisZoneCompound.split(\":\") : [null, \"\"];\n    newNodeIndex[id] = {\n      data: newItem,\n      flatData: flattenNode(newItem, config),\n      path,\n      parentId,\n      zone\n    };\n    const finalData = __spreadProps(__spreadValues({}, newItem), {\n      props: __spreadValues({}, newItem.props)\n    });\n    if (newProps.id === \"root\") {\n      delete finalData[\"type\"];\n      delete finalData.props[\"id\"];\n    }\n    return finalData;\n  };\n  const zones = state.data.zones || {};\n  const [_, newContent] = processContent([], rootDroppableId, state.data.content, \"root\");\n  const processedContent = newContent;\n  const zonesAlreadyProcessed = Object.keys(newZones);\n  Object.keys(zones || {}).forEach(zoneCompound => {\n    const [parentId] = zoneCompound.split(\":\");\n    if (zonesAlreadyProcessed.includes(zoneCompound)) {\n      return;\n    }\n    const [_2, newContent2] = processContent([rootDroppableId], zoneCompound, zones[zoneCompound], \"dropzone\", parentId);\n    newZones[zoneCompound] = newContent2;\n  }, newZones);\n  const processedRoot = processItem({\n    type: \"root\",\n    props: __spreadProps(__spreadValues({}, (_a = state.data.root.props) != null ? _a : state.data.root), {\n      id: \"root\"\n    })\n  }, [], -1);\n  const root = __spreadProps(__spreadValues({}, state.data.root), {\n    props: processedRoot.props\n  });\n  return __spreadProps(__spreadValues({}, state), {\n    data: {\n      root,\n      content: processedContent,\n      zones: __spreadValues(__spreadValues({}, state.data.zones), newZones)\n    },\n    indexes: {\n      nodes: __spreadValues(__spreadValues({}, state.indexes.nodes), newNodeIndex),\n      zones: __spreadValues(__spreadValues({}, state.indexes.zones), newZoneIndex)\n    }\n  });\n}\n\n// lib/migrate.ts\nvar migrations = [\n// Migrate root to root.props\ndata => {\n  const rootProps = data.root.props || data.root;\n  if (Object.keys(data.root).length > 0 && !data.root.props) {\n    console.warn(\"Migration applied: Root props moved from `root` to `root.props`.\");\n    return __spreadProps(__spreadValues({}, data), {\n      root: {\n        props: __spreadValues({}, rootProps)\n      }\n    });\n  }\n  return data;\n},\n// Migrate zones to slots\n(data, config) => {\n  var _a;\n  if (!config) return data;\n  console.log(\"Migrating DropZones to slots...\");\n  const updatedItems = {};\n  const appState = __spreadProps(__spreadValues({}, defaultAppState), {\n    data\n  });\n  const {\n    indexes\n  } = walkAppState(appState, config);\n  const deletedCompounds = [];\n  walkAppState(appState, config, (content, zoneCompound, zoneType) => {\n    var _a2, _b, _c;\n    if (zoneType === \"dropzone\") {\n      const [id, slotName] = zoneCompound.split(\":\");\n      const nodeData = indexes.nodes[id].data;\n      const componentType = nodeData.type;\n      const configForComponent = id === \"root\" ? config.root : config.components[componentType];\n      if (((_b = (_a2 = configForComponent == null ? void 0 : configForComponent.fields) == null ? void 0 : _a2[slotName]) == null ? void 0 : _b.type) === \"slot\") {\n        updatedItems[id] = __spreadProps(__spreadValues({}, nodeData), {\n          props: __spreadProps(__spreadValues(__spreadValues({}, nodeData.props), (_c = updatedItems[id]) == null ? void 0 : _c.props), {\n            [slotName]: content\n          })\n        });\n        deletedCompounds.push(zoneCompound);\n      }\n      return content;\n    }\n    return content;\n  });\n  const updated = walkAppState(appState, config, content => content, item => {\n    var _a2;\n    return (_a2 = updatedItems[item.props.id]) != null ? _a2 : item;\n  });\n  deletedCompounds.forEach(zoneCompound => {\n    var _a2;\n    const [_, propName] = zoneCompound.split(\":\");\n    console.log(`\\u2713 Success: Migrated \"${zoneCompound}\" from DropZone to slot field \"${propName}\"`);\n    (_a2 = updated.data.zones) == null ? true : delete _a2[zoneCompound];\n  });\n  Object.keys((_a = updated.data.zones) != null ? _a : {}).forEach(zoneCompound => {\n    const [_, propName] = zoneCompound.split(\":\");\n    throw new Error(`Could not migrate DropZone \"${zoneCompound}\" to slot field. No slot exists with the name \"${propName}\".`);\n  });\n  delete updated.data.zones;\n  return updated.data;\n}];\nfunction migrate(data, config) {\n  return migrations == null ? void 0 : migrations.reduce((acc, migration) => migration(acc, config), data);\n}\nexport { __spreadValues, __spreadProps, __objRest, __commonJS, __toESM, __async, init_react_import, rootAreaId, rootZone, rootDroppableId, defaultSlots, walkField, expandNode, walkAppState, walkTree, setupZone, useSlots, SlotRenderPure, SlotRender, Render, getChanged, resolveComponentData, resolveAllData, transformProps, defaultViewports, defaultAppState, migrate };", "map": {"version": 3, "names": ["__create", "Object", "create", "__defProp", "defineProperty", "__defProps", "defineProperties", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropDescs", "getOwnPropertyDescriptors", "__getOwnPropNames", "getOwnPropertyNames", "__getOwnPropSymbols", "getOwnPropertySymbols", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__propIsEnum", "propertyIsEnumerable", "__defNormalProp", "obj", "key", "value", "enumerable", "configurable", "writable", "__spreadValues", "a", "b", "prop", "call", "__spreadProps", "__objRest", "source", "exclude", "target", "indexOf", "__esm", "fn", "res", "__init", "__commonJS", "cb", "mod", "__require", "exports", "__copyProps", "to", "from", "except", "desc", "get", "__toESM", "isNodeMode", "__esModule", "__async", "__this", "__arguments", "generator", "Promise", "resolve", "reject", "fulfilled", "step", "next", "e", "rejected", "throw", "x", "done", "then", "apply", "React", "init_react_import", "../tsup-config/react-import.js", "rootAreaId", "rootZone", "rootDroppableId", "setupZone", "data", "zoneKey", "newData", "zones", "useMemo", "defaultSlots", "fields", "keys", "reduce", "acc", "fieldName", "type", "isPromise", "v", "flatten", "values", "item", "containsPromise", "arr", "some", "walkField", "_ref", "map", "<PERSON><PERSON><PERSON>", "prop<PERSON>ath", "id", "config", "recurseSlots", "_a", "_b", "_c", "content", "mappedContent", "el", "_a2", "componentConfig", "components", "Error", "fields2", "props", "all", "Array", "isArray", "arrayFields", "newValue", "idx", "objectFields", "walkObject", "getPropPath", "k", "_ref2", "newProps", "entries", "_ref3", "opts", "resolvedValue", "mapSlots", "arguments", "length", "undefined", "_d", "_e", "itemType", "root", "resolvedProps", "useSlots", "renderSlotEdit", "renderSlotRender", "readOnly", "forceReadOnly", "slotProps", "mapped", "_parentId", "propName", "field", "wildcardPath", "replace", "isReadOnly", "render", "Slot", "dzProps", "allow", "disallow", "zone", "mergedProps", "forwardRef", "jsx", "SlotRenderPure", "SlotRender", "<PERSON><PERSON>", "_ref4", "metadata", "Component", "puck", "renderDropZone", "DropZoneRender", "SlotRenderInternal", "_ref5", "ref", "className", "style", "children", "Fragment", "jsx2", "_ref6", "areaId", "zoneCompound", "_ref7", "zone2", "dragRef", "isEditing", "renderItem", "propsWithSlots", "props2", "Render", "_ref8", "rootProps", "title", "_ref9", "editMode", "fdeq", "getChanged", "newItem", "oldItem", "newItemProps", "oldItemProps", "fdeq2", "cache", "lastChange", "resolveComponentData", "_0", "_1", "_len", "_2", "_key", "onResolveStart", "onResolveEnd", "trigger", "configForItem", "resolvedItem", "shouldRunResolver", "resolveData", "resolved", "node", "<PERSON><PERSON><PERSON><PERSON>", "changed", "lastData", "itemWithResolvedChildren", "childItem", "defaultData", "toComponent", "resolveAllData", "_this", "defaultedData", "resolveNode", "_node", "resolved<PERSON>eep", "processContent", "processZones", "for<PERSON>ach", "_02", "_ref0", "dynamic", "walkTree", "callbackFn", "walkItem", "parentId", "_data", "transformProps", "propTransforms", "mapItem", "newRoot", "dataWithUpdatedRoot", "updatedData", "defaultViewports", "width", "height", "icon", "label", "defaultAppState", "ui", "leftSideBarVisible", "rightSideBarVisible", "arrayState", "itemSelector", "componentList", "isDragging", "previewMode", "viewports", "current", "options", "controlsVisible", "focus", "indexes", "nodes", "getZoneId", "split", "forRelatedZones", "path", "_ref1", "flat", "stripSlots", "flatten2", "unflatten", "flattenNode", "expandNode", "walkAppState", "state", "mapContent", "mapNodeOrSkip", "newZones", "newZoneIndex", "newNodeIndex", "zoneType", "newId", "newZoneCompound", "newContent2", "zoneChild", "index", "processItem", "contentIds", "processRelatedZones", "initialPath", "relatedPath", "relatedZoneCompound", "relatedContent", "mappedItem", "parentId2", "slotId", "thisZoneCompound", "flatData", "finalData", "_", "newContent", "processedContent", "zonesAlreadyProcessed", "includes", "processedRoot", "migrations", "console", "warn", "log", "updatedItems", "appState", "deletedCompounds", "slotName", "nodeData", "componentType", "configForComponent", "push", "updated", "migrate", "migration"], "sources": ["C:/laragon/www/frontend/node_modules/@measured/puck/dist/chunk-IM42S4YL.mjs"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nvar __esm = (fn, res) => function __init() {\n  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;\n};\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// ../tsup-config/react-import.js\nimport React from \"react\";\nvar init_react_import = __esm({\n  \"../tsup-config/react-import.js\"() {\n    \"use strict\";\n  }\n});\n\n// rsc.tsx\ninit_react_import();\n\n// components/ServerRender/index.tsx\ninit_react_import();\n\n// lib/root-droppable-id.ts\ninit_react_import();\nvar rootAreaId = \"root\";\nvar rootZone = \"default-zone\";\nvar rootDroppableId = `${rootAreaId}:${rootZone}`;\n\n// lib/data/setup-zone.ts\ninit_react_import();\nvar setupZone = (data, zoneKey) => {\n  if (zoneKey === rootDroppableId) {\n    return data;\n  }\n  const newData = __spreadProps(__spreadValues({}, data), {\n    zones: data.zones ? __spreadValues({}, data.zones) : {}\n  });\n  newData.zones[zoneKey] = newData.zones[zoneKey] || [];\n  return newData;\n};\n\n// lib/use-slots.tsx\ninit_react_import();\nimport { useMemo } from \"react\";\n\n// lib/data/map-slots.ts\ninit_react_import();\n\n// lib/data/default-slots.ts\ninit_react_import();\nvar defaultSlots = (value, fields) => Object.keys(fields).reduce(\n  (acc, fieldName) => fields[fieldName].type === \"slot\" ? __spreadValues({ [fieldName]: [] }, acc) : acc,\n  value\n);\n\n// lib/data/map-slots.ts\nvar isPromise = (v) => !!v && typeof v.then === \"function\";\nvar flatten = (values) => values.reduce((acc, item) => __spreadValues(__spreadValues({}, acc), item), {});\nvar containsPromise = (arr) => arr.some(isPromise);\nvar walkField = ({\n  value,\n  fields,\n  map,\n  propKey = \"\",\n  propPath = \"\",\n  id = \"\",\n  config,\n  recurseSlots = false\n}) => {\n  var _a, _b, _c;\n  if (((_a = fields[propKey]) == null ? void 0 : _a.type) === \"slot\") {\n    const content = value || [];\n    const mappedContent = recurseSlots ? content.map((el) => {\n      var _a2;\n      const componentConfig = config.components[el.type];\n      if (!componentConfig) {\n        throw new Error(`Could not find component config for ${el.type}`);\n      }\n      const fields2 = (_a2 = componentConfig.fields) != null ? _a2 : {};\n      return walkField({\n        value: __spreadProps(__spreadValues({}, el), { props: defaultSlots(el.props, fields2) }),\n        fields: fields2,\n        map,\n        id: el.props.id,\n        config,\n        recurseSlots\n      });\n    }) : content;\n    if (containsPromise(mappedContent)) {\n      return Promise.all(mappedContent);\n    }\n    return map(mappedContent, id, propPath, fields[propKey], propPath);\n  }\n  if (value && typeof value === \"object\") {\n    if (Array.isArray(value)) {\n      const arrayFields = ((_b = fields[propKey]) == null ? void 0 : _b.type) === \"array\" ? fields[propKey].arrayFields : null;\n      if (!arrayFields) return value;\n      const newValue = value.map(\n        (el, idx) => walkField({\n          value: el,\n          fields: arrayFields,\n          map,\n          propKey,\n          propPath: `${propPath}[${idx}]`,\n          id,\n          config,\n          recurseSlots\n        })\n      );\n      if (containsPromise(newValue)) {\n        return Promise.all(newValue);\n      }\n      return newValue;\n    } else if (\"$$typeof\" in value) {\n      return value;\n    } else {\n      const objectFields = ((_c = fields[propKey]) == null ? void 0 : _c.type) === \"object\" ? fields[propKey].objectFields : fields;\n      return walkObject({\n        value,\n        fields: objectFields,\n        map,\n        id,\n        getPropPath: (k) => `${propPath}.${k}`,\n        config,\n        recurseSlots\n      });\n    }\n  }\n  return value;\n};\nvar walkObject = ({\n  value,\n  fields,\n  map,\n  id,\n  getPropPath,\n  config,\n  recurseSlots\n}) => {\n  const newProps = Object.entries(value).map(([k, v]) => {\n    const opts = {\n      value: v,\n      fields,\n      map,\n      propKey: k,\n      propPath: getPropPath(k),\n      id,\n      config,\n      recurseSlots\n    };\n    const newValue = walkField(opts);\n    if (isPromise(newValue)) {\n      return newValue.then((resolvedValue) => ({\n        [k]: resolvedValue\n      }));\n    }\n    return {\n      [k]: newValue\n    };\n  }, {});\n  if (containsPromise(newProps)) {\n    return Promise.all(newProps).then(flatten);\n  }\n  return flatten(newProps);\n};\nfunction mapSlots(item, map, config, recurseSlots = false) {\n  var _a, _b, _c, _d, _e;\n  const itemType = \"type\" in item ? item.type : \"root\";\n  const componentConfig = itemType === \"root\" ? config.root : (_a = config.components) == null ? void 0 : _a[itemType];\n  const newProps = walkObject({\n    value: defaultSlots((_b = item.props) != null ? _b : {}, (_c = componentConfig == null ? void 0 : componentConfig.fields) != null ? _c : {}),\n    fields: (_d = componentConfig == null ? void 0 : componentConfig.fields) != null ? _d : {},\n    map,\n    id: item.props ? (_e = item.props.id) != null ? _e : \"root\" : \"root\",\n    getPropPath: (k) => k,\n    config,\n    recurseSlots\n  });\n  if (isPromise(newProps)) {\n    return newProps.then((resolvedProps) => __spreadProps(__spreadValues({}, item), {\n      props: resolvedProps\n    }));\n  }\n  return __spreadProps(__spreadValues({}, item), {\n    props: newProps\n  });\n}\n\n// lib/use-slots.tsx\nfunction useSlots(config, item, renderSlotEdit, renderSlotRender = renderSlotEdit, readOnly, forceReadOnly) {\n  const slotProps = useMemo(() => {\n    const mapped = mapSlots(\n      item,\n      (content, _parentId, propName, field, propPath) => {\n        const wildcardPath = propPath.replace(/\\[\\d+\\]/g, \"[*]\");\n        const isReadOnly = (readOnly == null ? void 0 : readOnly[propPath]) || (readOnly == null ? void 0 : readOnly[wildcardPath]) || forceReadOnly;\n        const render = isReadOnly ? renderSlotRender : renderSlotEdit;\n        const Slot = (dzProps) => render(__spreadProps(__spreadValues({\n          allow: (field == null ? void 0 : field.type) === \"slot\" ? field.allow : [],\n          disallow: (field == null ? void 0 : field.type) === \"slot\" ? field.disallow : []\n        }, dzProps), {\n          zone: propName,\n          content\n        }));\n        return Slot;\n      },\n      config\n    ).props;\n    return mapped;\n  }, [config, item, readOnly, forceReadOnly]);\n  const mergedProps = useMemo(\n    () => __spreadValues(__spreadValues({}, item.props), slotProps),\n    [item.props, slotProps]\n  );\n  return mergedProps;\n}\n\n// components/SlotRender/server.tsx\ninit_react_import();\nimport { forwardRef } from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar SlotRenderPure = (props) => /* @__PURE__ */ jsx(SlotRender, __spreadValues({}, props));\nvar Item = ({\n  config,\n  item,\n  metadata\n}) => {\n  const Component = config.components[item.type];\n  const props = useSlots(config, item, (slotProps) => /* @__PURE__ */ jsx(SlotRenderPure, __spreadProps(__spreadValues({}, slotProps), { config, metadata })));\n  return /* @__PURE__ */ jsx(\n    Component.render,\n    __spreadProps(__spreadValues({}, props), {\n      puck: __spreadProps(__spreadValues({}, props.puck), {\n        renderDropZone: DropZoneRender,\n        metadata: metadata || {}\n      })\n    })\n  );\n};\nvar SlotRender = forwardRef(\n  function SlotRenderInternal({ className, style, content, config, metadata }, ref) {\n    return /* @__PURE__ */ jsx(\"div\", { className, style, ref, children: content.map((item) => {\n      if (!config.components[item.type]) {\n        return null;\n      }\n      return /* @__PURE__ */ jsx(\n        Item,\n        {\n          config,\n          item,\n          metadata\n        },\n        item.props.id\n      );\n    }) });\n  }\n);\n\n// components/ServerRender/index.tsx\nimport { Fragment, jsx as jsx2 } from \"react/jsx-runtime\";\nfunction DropZoneRender({\n  zone,\n  data,\n  areaId = \"root\",\n  config,\n  metadata = {}\n}) {\n  let zoneCompound = rootDroppableId;\n  let content = (data == null ? void 0 : data.content) || [];\n  if (!data || !config) {\n    return null;\n  }\n  if (areaId !== rootAreaId && zone !== rootZone) {\n    zoneCompound = `${areaId}:${zone}`;\n    content = setupZone(data, zoneCompound).zones[zoneCompound];\n  }\n  return /* @__PURE__ */ jsx2(Fragment, { children: content.map((item) => {\n    const Component = config.components[item.type];\n    const props = __spreadProps(__spreadValues({}, item.props), {\n      puck: {\n        renderDropZone: ({ zone: zone2 }) => /* @__PURE__ */ jsx2(\n          DropZoneRender,\n          {\n            zone: zone2,\n            data,\n            areaId: item.props.id,\n            config,\n            metadata\n          }\n        ),\n        metadata,\n        dragRef: null,\n        isEditing: false\n      }\n    });\n    const renderItem = __spreadProps(__spreadValues({}, item), { props });\n    const propsWithSlots = useSlots(config, renderItem, (props2) => /* @__PURE__ */ jsx2(SlotRenderPure, __spreadProps(__spreadValues({}, props2), { config, metadata })));\n    if (Component) {\n      return /* @__PURE__ */ jsx2(Component.render, __spreadValues({}, propsWithSlots), renderItem.props.id);\n    }\n    return null;\n  }) });\n}\nfunction Render({\n  config,\n  data,\n  metadata = {}\n}) {\n  var _a;\n  const rootProps = \"props\" in data.root ? data.root.props : data.root;\n  const title = rootProps.title || \"\";\n  const props = __spreadProps(__spreadValues({}, rootProps), {\n    puck: {\n      renderDropZone: ({ zone }) => /* @__PURE__ */ jsx2(\n        DropZoneRender,\n        {\n          zone,\n          data,\n          config,\n          metadata\n        }\n      ),\n      isEditing: false,\n      dragRef: null,\n      metadata\n    },\n    title,\n    editMode: false,\n    id: \"puck-root\"\n  });\n  const propsWithSlots = useSlots(config, { type: \"root\", props }, (props2) => /* @__PURE__ */ jsx2(SlotRenderPure, __spreadProps(__spreadValues({}, props2), { config, metadata })));\n  if ((_a = config.root) == null ? void 0 : _a.render) {\n    return /* @__PURE__ */ jsx2(config.root.render, __spreadProps(__spreadValues({}, propsWithSlots), { children: /* @__PURE__ */ jsx2(\n      DropZoneRender,\n      {\n        config,\n        data,\n        zone: rootZone,\n        metadata\n      }\n    ) }));\n  }\n  return /* @__PURE__ */ jsx2(\n    DropZoneRender,\n    {\n      config,\n      data,\n      zone: rootZone,\n      metadata\n    }\n  );\n}\n\n// lib/resolve-all-data.ts\ninit_react_import();\n\n// lib/resolve-component-data.ts\ninit_react_import();\n\n// lib/get-changed.ts\ninit_react_import();\nimport fdeq from \"fast-deep-equal\";\nvar getChanged = (newItem, oldItem) => {\n  return newItem ? Object.keys(newItem.props || {}).reduce((acc, item) => {\n    const newItemProps = (newItem == null ? void 0 : newItem.props) || {};\n    const oldItemProps = (oldItem == null ? void 0 : oldItem.props) || {};\n    return __spreadProps(__spreadValues({}, acc), {\n      [item]: !fdeq(oldItemProps[item], newItemProps[item])\n    });\n  }, {}) : {};\n};\n\n// lib/resolve-component-data.ts\nimport fdeq2 from \"fast-deep-equal\";\nvar cache = { lastChange: {} };\nvar resolveComponentData = (_0, _1, ..._2) => __async(void 0, [_0, _1, ..._2], function* (item, config, metadata = {}, onResolveStart, onResolveEnd, trigger = \"replace\") {\n  const configForItem = \"type\" in item && item.type !== \"root\" ? config.components[item.type] : config.root;\n  const resolvedItem = __spreadValues({}, item);\n  const shouldRunResolver = (configForItem == null ? void 0 : configForItem.resolveData) && item.props;\n  const id = \"id\" in item.props ? item.props.id : \"root\";\n  if (shouldRunResolver) {\n    const { item: oldItem = null, resolved = {} } = cache.lastChange[id] || {};\n    if (item && fdeq2(item, oldItem)) {\n      return { node: resolved, didChange: false };\n    }\n    const changed = getChanged(item, oldItem);\n    if (onResolveStart) {\n      onResolveStart(item);\n    }\n    const { props: resolvedProps, readOnly = {} } = yield configForItem.resolveData(item, {\n      changed,\n      lastData: oldItem,\n      metadata: __spreadValues(__spreadValues({}, metadata), configForItem.metadata),\n      trigger\n    });\n    resolvedItem.props = __spreadValues(__spreadValues({}, item.props), resolvedProps);\n    if (Object.keys(readOnly).length) {\n      resolvedItem.readOnly = readOnly;\n    }\n  }\n  let itemWithResolvedChildren = yield mapSlots(\n    resolvedItem,\n    (content) => __async(void 0, null, function* () {\n      return yield Promise.all(\n        content.map(\n          (childItem) => __async(void 0, null, function* () {\n            return (yield resolveComponentData(\n              childItem,\n              config,\n              metadata,\n              onResolveStart,\n              onResolveEnd,\n              trigger\n            )).node;\n          })\n        )\n      );\n    }),\n    config\n  );\n  if (shouldRunResolver && onResolveEnd) {\n    onResolveEnd(resolvedItem);\n  }\n  cache.lastChange[id] = {\n    item,\n    resolved: itemWithResolvedChildren\n  };\n  return {\n    node: itemWithResolvedChildren,\n    didChange: !fdeq2(item, itemWithResolvedChildren)\n  };\n});\n\n// lib/data/default-data.ts\ninit_react_import();\nvar defaultData = (data) => __spreadProps(__spreadValues({}, data), {\n  root: data.root || {},\n  content: data.content || []\n});\n\n// lib/data/to-component.ts\ninit_react_import();\nvar toComponent = (item) => {\n  return \"type\" in item ? item : __spreadProps(__spreadValues({}, item), {\n    props: __spreadProps(__spreadValues({}, item.props), { id: \"root\" }),\n    type: \"root\"\n  });\n};\n\n// lib/resolve-all-data.ts\nfunction resolveAllData(_0, _1) {\n  return __async(this, arguments, function* (data, config, metadata = {}, onResolveStart, onResolveEnd) {\n    var _a;\n    const defaultedData = defaultData(data);\n    const resolveNode = (_node) => __async(this, null, function* () {\n      const node = toComponent(_node);\n      onResolveStart == null ? void 0 : onResolveStart(node);\n      const resolved = (yield resolveComponentData(\n        node,\n        config,\n        metadata,\n        () => {\n        },\n        () => {\n        },\n        \"force\"\n      )).node;\n      const resolvedDeep = yield mapSlots(\n        resolved,\n        processContent,\n        config\n      );\n      onResolveEnd == null ? void 0 : onResolveEnd(toComponent(resolvedDeep));\n      return resolvedDeep;\n    });\n    const processContent = (content) => __async(this, null, function* () {\n      return Promise.all(content.map(resolveNode));\n    });\n    const processZones = () => __async(this, null, function* () {\n      var _a2;\n      const zones = (_a2 = data.zones) != null ? _a2 : {};\n      Object.entries(zones).forEach((_02) => __async(this, [_02], function* ([zoneKey, content]) {\n        zones[zoneKey] = yield Promise.all(content.map(resolveNode));\n      }));\n      return zones;\n    });\n    const dynamic = {\n      root: yield resolveNode(defaultedData.root),\n      content: yield processContent(defaultedData.content),\n      zones: yield processZones()\n    };\n    Object.keys((_a = defaultedData.zones) != null ? _a : {}).forEach((zoneKey) => __async(this, null, function* () {\n      const content = defaultedData.zones[zoneKey];\n      dynamic.zones[zoneKey] = yield processContent(content);\n    }), {});\n    return dynamic;\n  });\n}\n\n// lib/transform-props.ts\ninit_react_import();\n\n// lib/data/walk-tree.ts\ninit_react_import();\nfunction walkTree(data, config, callbackFn) {\n  var _a, _b;\n  const walkItem = (item) => {\n    return mapSlots(\n      item,\n      (content, parentId, propName) => {\n        var _a2;\n        return (_a2 = callbackFn(content, { parentId, propName })) != null ? _a2 : content;\n      },\n      config,\n      true\n    );\n  };\n  if (\"props\" in data) {\n    return walkItem(data);\n  }\n  const _data = data;\n  const zones = (_a = _data.zones) != null ? _a : {};\n  const mappedContent = _data.content.map(walkItem);\n  return {\n    root: walkItem(_data.root),\n    content: (_b = callbackFn(mappedContent, {\n      parentId: \"root\",\n      propName: \"default-zone\"\n    })) != null ? _b : mappedContent,\n    zones: Object.keys(zones).reduce(\n      (acc, zoneCompound) => __spreadProps(__spreadValues({}, acc), {\n        [zoneCompound]: zones[zoneCompound].map(walkItem)\n      }),\n      {}\n    )\n  };\n}\n\n// lib/transform-props.ts\nfunction transformProps(data, propTransforms, config = { components: {} }) {\n  const mapItem = (item) => {\n    if (propTransforms[item.type]) {\n      return __spreadProps(__spreadValues({}, item), {\n        props: __spreadValues({\n          id: item.props.id\n        }, propTransforms[item.type](item.props))\n      });\n    }\n    return item;\n  };\n  const defaultedData = defaultData(data);\n  const rootProps = defaultedData.root.props || defaultedData.root;\n  let newRoot = __spreadValues({}, defaultedData.root);\n  if (propTransforms[\"root\"]) {\n    newRoot.props = propTransforms[\"root\"](rootProps);\n  }\n  const dataWithUpdatedRoot = __spreadProps(__spreadValues({}, defaultedData), { root: newRoot });\n  const updatedData = walkTree(\n    dataWithUpdatedRoot,\n    config,\n    (content) => content.map(mapItem)\n  );\n  if (!defaultedData.root.props) {\n    updatedData.root = updatedData.root.props;\n  }\n  return updatedData;\n}\n\n// lib/migrate.ts\ninit_react_import();\n\n// store/default-app-state.ts\ninit_react_import();\n\n// components/ViewportControls/default-viewports.ts\ninit_react_import();\nvar defaultViewports = [\n  { width: 360, height: \"auto\", icon: \"Smartphone\", label: \"Small\" },\n  { width: 768, height: \"auto\", icon: \"Tablet\", label: \"Medium\" },\n  { width: 1280, height: \"auto\", icon: \"Monitor\", label: \"Large\" }\n];\n\n// store/default-app-state.ts\nvar defaultAppState = {\n  data: { content: [], root: {}, zones: {} },\n  ui: {\n    leftSideBarVisible: true,\n    rightSideBarVisible: true,\n    arrayState: {},\n    itemSelector: null,\n    componentList: {},\n    isDragging: false,\n    previewMode: \"edit\",\n    viewports: {\n      current: {\n        width: defaultViewports[0].width,\n        height: defaultViewports[0].height || \"auto\"\n      },\n      options: [],\n      controlsVisible: true\n    },\n    field: { focus: null }\n  },\n  indexes: {\n    nodes: {},\n    zones: {}\n  }\n};\n\n// lib/data/walk-app-state.ts\ninit_react_import();\n\n// lib/data/for-related-zones.ts\ninit_react_import();\n\n// lib/get-zone-id.ts\ninit_react_import();\nvar getZoneId = (zoneCompound) => {\n  if (!zoneCompound) {\n    return [];\n  }\n  if (zoneCompound && zoneCompound.indexOf(\":\") > -1) {\n    return zoneCompound.split(\":\");\n  }\n  return [rootDroppableId, zoneCompound];\n};\n\n// lib/data/for-related-zones.ts\nfunction forRelatedZones(item, data, cb, path = []) {\n  Object.entries(data.zones || {}).forEach(([zoneCompound, content]) => {\n    const [parentId] = getZoneId(zoneCompound);\n    if (parentId === item.props.id) {\n      cb(path, zoneCompound, content);\n    }\n  });\n}\n\n// lib/data/flatten-node.ts\ninit_react_import();\nimport flat from \"flat\";\n\n// lib/data/strip-slots.ts\ninit_react_import();\nvar stripSlots = (data, config) => {\n  return mapSlots(data, () => null, config);\n};\n\n// lib/data/flatten-node.ts\nvar { flatten: flatten2, unflatten } = flat;\nvar flattenNode = (node, config) => {\n  return __spreadProps(__spreadValues({}, node), {\n    props: flatten2(stripSlots(node, config).props)\n  });\n};\nvar expandNode = (node) => {\n  const props = unflatten(node.props);\n  return __spreadProps(__spreadValues({}, node), {\n    props\n  });\n};\n\n// lib/data/walk-app-state.ts\nfunction walkAppState(state, config, mapContent = (content) => content, mapNodeOrSkip = (item) => item) {\n  var _a;\n  let newZones = {};\n  const newZoneIndex = {};\n  const newNodeIndex = {};\n  const processContent = (path, zoneCompound, content, zoneType, newId) => {\n    var _a2;\n    const [parentId] = zoneCompound.split(\":\");\n    const mappedContent = ((_a2 = mapContent(content, zoneCompound, zoneType)) != null ? _a2 : content) || [];\n    const [_2, zone] = zoneCompound.split(\":\");\n    const newZoneCompound = `${newId || parentId}:${zone}`;\n    const newContent2 = mappedContent.map(\n      (zoneChild, index) => processItem(zoneChild, [...path, newZoneCompound], index)\n    );\n    newZoneIndex[newZoneCompound] = {\n      contentIds: newContent2.map((item) => item.props.id),\n      type: zoneType\n    };\n    return [newZoneCompound, newContent2];\n  };\n  const processRelatedZones = (item, newId, initialPath) => {\n    forRelatedZones(\n      item,\n      state.data,\n      (relatedPath, relatedZoneCompound, relatedContent) => {\n        const [zoneCompound, newContent2] = processContent(\n          relatedPath,\n          relatedZoneCompound,\n          relatedContent,\n          \"dropzone\",\n          newId\n        );\n        newZones[zoneCompound] = newContent2;\n      },\n      initialPath\n    );\n  };\n  const processItem = (item, path, index) => {\n    const mappedItem = mapNodeOrSkip(item, path, index);\n    if (!mappedItem) return item;\n    const id = mappedItem.props.id;\n    const newProps = __spreadProps(__spreadValues({}, mapSlots(\n      mappedItem,\n      (content, parentId2, slotId) => {\n        const zoneCompound = `${parentId2}:${slotId}`;\n        const [_2, newContent2] = processContent(\n          path,\n          zoneCompound,\n          content,\n          \"slot\",\n          parentId2\n        );\n        return newContent2;\n      },\n      config\n    ).props), {\n      id\n    });\n    processRelatedZones(item, id, path);\n    const newItem = __spreadProps(__spreadValues({}, item), { props: newProps });\n    const thisZoneCompound = path[path.length - 1];\n    const [parentId, zone] = thisZoneCompound ? thisZoneCompound.split(\":\") : [null, \"\"];\n    newNodeIndex[id] = {\n      data: newItem,\n      flatData: flattenNode(newItem, config),\n      path,\n      parentId,\n      zone\n    };\n    const finalData = __spreadProps(__spreadValues({}, newItem), { props: __spreadValues({}, newItem.props) });\n    if (newProps.id === \"root\") {\n      delete finalData[\"type\"];\n      delete finalData.props[\"id\"];\n    }\n    return finalData;\n  };\n  const zones = state.data.zones || {};\n  const [_, newContent] = processContent(\n    [],\n    rootDroppableId,\n    state.data.content,\n    \"root\"\n  );\n  const processedContent = newContent;\n  const zonesAlreadyProcessed = Object.keys(newZones);\n  Object.keys(zones || {}).forEach((zoneCompound) => {\n    const [parentId] = zoneCompound.split(\":\");\n    if (zonesAlreadyProcessed.includes(zoneCompound)) {\n      return;\n    }\n    const [_2, newContent2] = processContent(\n      [rootDroppableId],\n      zoneCompound,\n      zones[zoneCompound],\n      \"dropzone\",\n      parentId\n    );\n    newZones[zoneCompound] = newContent2;\n  }, newZones);\n  const processedRoot = processItem(\n    {\n      type: \"root\",\n      props: __spreadProps(__spreadValues({}, (_a = state.data.root.props) != null ? _a : state.data.root), { id: \"root\" })\n    },\n    [],\n    -1\n  );\n  const root = __spreadProps(__spreadValues({}, state.data.root), {\n    props: processedRoot.props\n  });\n  return __spreadProps(__spreadValues({}, state), {\n    data: {\n      root,\n      content: processedContent,\n      zones: __spreadValues(__spreadValues({}, state.data.zones), newZones)\n    },\n    indexes: {\n      nodes: __spreadValues(__spreadValues({}, state.indexes.nodes), newNodeIndex),\n      zones: __spreadValues(__spreadValues({}, state.indexes.zones), newZoneIndex)\n    }\n  });\n}\n\n// lib/migrate.ts\nvar migrations = [\n  // Migrate root to root.props\n  (data) => {\n    const rootProps = data.root.props || data.root;\n    if (Object.keys(data.root).length > 0 && !data.root.props) {\n      console.warn(\n        \"Migration applied: Root props moved from `root` to `root.props`.\"\n      );\n      return __spreadProps(__spreadValues({}, data), {\n        root: {\n          props: __spreadValues({}, rootProps)\n        }\n      });\n    }\n    return data;\n  },\n  // Migrate zones to slots\n  (data, config) => {\n    var _a;\n    if (!config) return data;\n    console.log(\"Migrating DropZones to slots...\");\n    const updatedItems = {};\n    const appState = __spreadProps(__spreadValues({}, defaultAppState), { data });\n    const { indexes } = walkAppState(appState, config);\n    const deletedCompounds = [];\n    walkAppState(appState, config, (content, zoneCompound, zoneType) => {\n      var _a2, _b, _c;\n      if (zoneType === \"dropzone\") {\n        const [id, slotName] = zoneCompound.split(\":\");\n        const nodeData = indexes.nodes[id].data;\n        const componentType = nodeData.type;\n        const configForComponent = id === \"root\" ? config.root : config.components[componentType];\n        if (((_b = (_a2 = configForComponent == null ? void 0 : configForComponent.fields) == null ? void 0 : _a2[slotName]) == null ? void 0 : _b.type) === \"slot\") {\n          updatedItems[id] = __spreadProps(__spreadValues({}, nodeData), {\n            props: __spreadProps(__spreadValues(__spreadValues({}, nodeData.props), (_c = updatedItems[id]) == null ? void 0 : _c.props), {\n              [slotName]: content\n            })\n          });\n          deletedCompounds.push(zoneCompound);\n        }\n        return content;\n      }\n      return content;\n    });\n    const updated = walkAppState(\n      appState,\n      config,\n      (content) => content,\n      (item) => {\n        var _a2;\n        return (_a2 = updatedItems[item.props.id]) != null ? _a2 : item;\n      }\n    );\n    deletedCompounds.forEach((zoneCompound) => {\n      var _a2;\n      const [_, propName] = zoneCompound.split(\":\");\n      console.log(\n        `\\u2713 Success: Migrated \"${zoneCompound}\" from DropZone to slot field \"${propName}\"`\n      );\n      (_a2 = updated.data.zones) == null ? true : delete _a2[zoneCompound];\n    });\n    Object.keys((_a = updated.data.zones) != null ? _a : {}).forEach((zoneCompound) => {\n      const [_, propName] = zoneCompound.split(\":\");\n      throw new Error(\n        `Could not migrate DropZone \"${zoneCompound}\" to slot field. No slot exists with the name \"${propName}\".`\n      );\n    });\n    delete updated.data.zones;\n    return updated.data;\n  }\n];\nfunction migrate(data, config) {\n  return migrations == null ? void 0 : migrations.reduce(\n    (acc, migration) => migration(acc, config),\n    data\n  );\n}\n\nexport {\n  __spreadValues,\n  __spreadProps,\n  __objRest,\n  __commonJS,\n  __toESM,\n  __async,\n  init_react_import,\n  rootAreaId,\n  rootZone,\n  rootDroppableId,\n  defaultSlots,\n  walkField,\n  expandNode,\n  walkAppState,\n  walkTree,\n  setupZone,\n  useSlots,\n  SlotRenderPure,\n  SlotRender,\n  Render,\n  getChanged,\n  resolveComponentData,\n  resolveAllData,\n  transformProps,\n  defaultViewports,\n  defaultAppState,\n  migrate\n};\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM;AAC5B,IAAIC,SAAS,GAAGF,MAAM,CAACG,cAAc;AACrC,IAAIC,UAAU,GAAGJ,MAAM,CAACK,gBAAgB;AACxC,IAAIC,gBAAgB,GAAGN,MAAM,CAACO,wBAAwB;AACtD,IAAIC,iBAAiB,GAAGR,MAAM,CAACS,yBAAyB;AACxD,IAAIC,iBAAiB,GAAGV,MAAM,CAACW,mBAAmB;AAClD,IAAIC,mBAAmB,GAAGZ,MAAM,CAACa,qBAAqB;AACtD,IAAIC,YAAY,GAAGd,MAAM,CAACe,cAAc;AACxC,IAAIC,YAAY,GAAGhB,MAAM,CAACiB,SAAS,CAACC,cAAc;AAClD,IAAIC,YAAY,GAAGnB,MAAM,CAACiB,SAAS,CAACG,oBAAoB;AACxD,IAAIC,eAAe,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAID,GAAG,GAAGpB,SAAS,CAACoB,GAAG,EAAEC,GAAG,EAAE;EAAEE,UAAU,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEH;AAAM,CAAC,CAAC,GAAGF,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;AAC/J,IAAII,cAAc,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC7B,KAAK,IAAIC,IAAI,IAAID,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC,EAC5B,IAAId,YAAY,CAACgB,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC,IAAInB,mBAAmB,EACrB,KAAK,IAAImB,IAAI,IAAInB,mBAAmB,CAACkB,CAAC,CAAC,EAAE;IACvC,IAAIX,YAAY,CAACa,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC;EACF,OAAOF,CAAC;AACV,CAAC;AACD,IAAII,aAAa,GAAGA,CAACJ,CAAC,EAAEC,CAAC,KAAK1B,UAAU,CAACyB,CAAC,EAAErB,iBAAiB,CAACsB,CAAC,CAAC,CAAC;AACjE,IAAII,SAAS,GAAGA,CAACC,MAAM,EAAEC,OAAO,KAAK;EACnC,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAIN,IAAI,IAAII,MAAM,EACrB,IAAInB,YAAY,CAACgB,IAAI,CAACG,MAAM,EAAEJ,IAAI,CAAC,IAAIK,OAAO,CAACE,OAAO,CAACP,IAAI,CAAC,GAAG,CAAC,EAC9DM,MAAM,CAACN,IAAI,CAAC,GAAGI,MAAM,CAACJ,IAAI,CAAC;EAC/B,IAAII,MAAM,IAAI,IAAI,IAAIvB,mBAAmB,EACvC,KAAK,IAAImB,IAAI,IAAInB,mBAAmB,CAACuB,MAAM,CAAC,EAAE;IAC5C,IAAIC,OAAO,CAACE,OAAO,CAACP,IAAI,CAAC,GAAG,CAAC,IAAIZ,YAAY,CAACa,IAAI,CAACG,MAAM,EAAEJ,IAAI,CAAC,EAC9DM,MAAM,CAACN,IAAI,CAAC,GAAGI,MAAM,CAACJ,IAAI,CAAC;EAC/B;EACF,OAAOM,MAAM;AACf,CAAC;AACD,IAAIE,KAAK,GAAGA,CAACC,EAAE,EAAEC,GAAG,KAAK,SAASC,MAAMA,CAAA,EAAG;EACzC,OAAOF,EAAE,KAAKC,GAAG,GAAG,CAAC,CAAC,EAAED,EAAE,CAAC9B,iBAAiB,CAAC8B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,EAAE,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG;AACrE,CAAC;AACD,IAAIE,UAAU,GAAGA,CAACC,EAAE,EAAEC,GAAG,KAAK,SAASC,SAASA,CAAA,EAAG;EACjD,OAAOD,GAAG,IAAI,CAAC,CAAC,EAAED,EAAE,CAAClC,iBAAiB,CAACkC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACC,GAAG,GAAG;IAAEE,OAAO,EAAE,CAAC;EAAE,CAAC,EAAEA,OAAO,EAAEF,GAAG,CAAC,EAAEA,GAAG,CAACE,OAAO;AACpG,CAAC;AACD,IAAIC,WAAW,GAAGA,CAACC,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,KAAK;EAC5C,IAAIF,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAClE,KAAK,IAAI3B,GAAG,IAAIb,iBAAiB,CAACwC,IAAI,CAAC,EACrC,IAAI,CAAClC,YAAY,CAACgB,IAAI,CAACiB,EAAE,EAAE1B,GAAG,CAAC,IAAIA,GAAG,KAAK4B,MAAM,EAC/CjD,SAAS,CAAC+C,EAAE,EAAE1B,GAAG,EAAE;MAAE8B,GAAG,EAAEA,CAAA,KAAMH,IAAI,CAAC3B,GAAG,CAAC;MAAEE,UAAU,EAAE,EAAE2B,IAAI,GAAG9C,gBAAgB,CAAC4C,IAAI,EAAE3B,GAAG,CAAC,CAAC,IAAI6B,IAAI,CAAC3B;IAAW,CAAC,CAAC;EACxH;EACA,OAAOwB,EAAE;AACX,CAAC;AACD,IAAIK,OAAO,GAAGA,CAACT,GAAG,EAAEU,UAAU,EAAElB,MAAM,MAAMA,MAAM,GAAGQ,GAAG,IAAI,IAAI,GAAG9C,QAAQ,CAACe,YAAY,CAAC+B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEG,WAAW;AAC9G;AACA;AACA;AACA;AACAO,UAAU,IAAI,CAACV,GAAG,IAAI,CAACA,GAAG,CAACW,UAAU,GAAGtD,SAAS,CAACmC,MAAM,EAAE,SAAS,EAAE;EAAEb,KAAK,EAAEqB,GAAG;EAAEpB,UAAU,EAAE;AAAK,CAAC,CAAC,GAAGY,MAAM,EAC/GQ,GACF,CAAC,CAAC;AACF,IAAIY,OAAO,GAAGA,CAACC,MAAM,EAAEC,WAAW,EAAEC,SAAS,KAAK;EAChD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAIC,SAAS,GAAIxC,KAAK,IAAK;MACzB,IAAI;QACFyC,IAAI,CAACL,SAAS,CAACM,IAAI,CAAC1C,KAAK,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAO2C,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF,CAAC;IACD,IAAIC,QAAQ,GAAI5C,KAAK,IAAK;MACxB,IAAI;QACFyC,IAAI,CAACL,SAAS,CAACS,KAAK,CAAC7C,KAAK,CAAC,CAAC;MAC9B,CAAC,CAAC,OAAO2C,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF,CAAC;IACD,IAAIF,IAAI,GAAIK,CAAC,IAAKA,CAAC,CAACC,IAAI,GAAGT,OAAO,CAACQ,CAAC,CAAC9C,KAAK,CAAC,GAAGqC,OAAO,CAACC,OAAO,CAACQ,CAAC,CAAC9C,KAAK,CAAC,CAACgD,IAAI,CAACR,SAAS,EAAEI,QAAQ,CAAC;IAChGH,IAAI,CAAC,CAACL,SAAS,GAAGA,SAAS,CAACa,KAAK,CAACf,MAAM,EAAEC,WAAW,CAAC,EAAEO,IAAI,CAAC,CAAC,CAAC;EACjE,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAOQ,KAAK,MAAM,OAAO;AACzB,IAAIC,iBAAiB,GAAGpC,KAAK,CAAC;EAC5B,gCAAgCqC,CAAA,EAAG;IACjC,YAAY;EACd;AACF,CAAC,CAAC;;AAEF;AACAD,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;AACnB,IAAIE,UAAU,GAAG,MAAM;AACvB,IAAIC,QAAQ,GAAG,cAAc;AAC7B,IAAIC,eAAe,GAAG,GAAGF,UAAU,IAAIC,QAAQ,EAAE;;AAEjD;AACAH,iBAAiB,CAAC,CAAC;AACnB,IAAIK,SAAS,GAAGA,CAACC,IAAI,EAAEC,OAAO,KAAK;EACjC,IAAIA,OAAO,KAAKH,eAAe,EAAE;IAC/B,OAAOE,IAAI;EACb;EACA,MAAME,OAAO,GAAGlD,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqD,IAAI,CAAC,EAAE;IACtDG,KAAK,EAAEH,IAAI,CAACG,KAAK,GAAGxD,cAAc,CAAC,CAAC,CAAC,EAAEqD,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;EACxD,CAAC,CAAC;EACFD,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC,GAAGC,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC,IAAI,EAAE;EACrD,OAAOC,OAAO;AAChB,CAAC;;AAED;AACAR,iBAAiB,CAAC,CAAC;AACnB,SAASU,OAAO,QAAQ,OAAO;;AAE/B;AACAV,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;AACnB,IAAIW,YAAY,GAAGA,CAAC9D,KAAK,EAAE+D,MAAM,KAAKvF,MAAM,CAACwF,IAAI,CAACD,MAAM,CAAC,CAACE,MAAM,CAC9D,CAACC,GAAG,EAAEC,SAAS,KAAKJ,MAAM,CAACI,SAAS,CAAC,CAACC,IAAI,KAAK,MAAM,GAAGhE,cAAc,CAAC;EAAE,CAAC+D,SAAS,GAAG;AAAG,CAAC,EAAED,GAAG,CAAC,GAAGA,GAAG,EACtGlE,KACF,CAAC;;AAED;AACA,IAAIqE,SAAS,GAAIC,CAAC,IAAK,CAAC,CAACA,CAAC,IAAI,OAAOA,CAAC,CAACtB,IAAI,KAAK,UAAU;AAC1D,IAAIuB,OAAO,GAAIC,MAAM,IAAKA,MAAM,CAACP,MAAM,CAAC,CAACC,GAAG,EAAEO,IAAI,KAAKrE,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE8D,GAAG,CAAC,EAAEO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACzG,IAAIC,eAAe,GAAIC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAACP,SAAS,CAAC;AAClD,IAAIQ,SAAS,GAAGC,IAAA,IASV;EAAA,IATW;IACf9E,KAAK;IACL+D,MAAM;IACNgB,GAAG;IACHC,OAAO,GAAG,EAAE;IACZC,QAAQ,GAAG,EAAE;IACbC,EAAE,GAAG,EAAE;IACPC,MAAM;IACNC,YAAY,GAAG;EACjB,CAAC,GAAAN,IAAA;EACC,IAAIO,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,IAAI,CAAC,CAACF,EAAE,GAAGtB,MAAM,CAACiB,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGK,EAAE,CAACjB,IAAI,MAAM,MAAM,EAAE;IAClE,MAAMoB,OAAO,GAAGxF,KAAK,IAAI,EAAE;IAC3B,MAAMyF,aAAa,GAAGL,YAAY,GAAGI,OAAO,CAACT,GAAG,CAAEW,EAAE,IAAK;MACvD,IAAIC,GAAG;MACP,MAAMC,eAAe,GAAGT,MAAM,CAACU,UAAU,CAACH,EAAE,CAACtB,IAAI,CAAC;MAClD,IAAI,CAACwB,eAAe,EAAE;QACpB,MAAM,IAAIE,KAAK,CAAC,uCAAuCJ,EAAE,CAACtB,IAAI,EAAE,CAAC;MACnE;MACA,MAAM2B,OAAO,GAAG,CAACJ,GAAG,GAAGC,eAAe,CAAC7B,MAAM,KAAK,IAAI,GAAG4B,GAAG,GAAG,CAAC,CAAC;MACjE,OAAOd,SAAS,CAAC;QACf7E,KAAK,EAAES,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEsF,EAAE,CAAC,EAAE;UAAEM,KAAK,EAAElC,YAAY,CAAC4B,EAAE,CAACM,KAAK,EAAED,OAAO;QAAE,CAAC,CAAC;QACxFhC,MAAM,EAAEgC,OAAO;QACfhB,GAAG;QACHG,EAAE,EAAEQ,EAAE,CAACM,KAAK,CAACd,EAAE;QACfC,MAAM;QACNC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,GAAGI,OAAO;IACZ,IAAId,eAAe,CAACe,aAAa,CAAC,EAAE;MAClC,OAAOpD,OAAO,CAAC4D,GAAG,CAACR,aAAa,CAAC;IACnC;IACA,OAAOV,GAAG,CAACU,aAAa,EAAEP,EAAE,EAAED,QAAQ,EAAElB,MAAM,CAACiB,OAAO,CAAC,EAAEC,QAAQ,CAAC;EACpE;EACA,IAAIjF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACtC,IAAIkG,KAAK,CAACC,OAAO,CAACnG,KAAK,CAAC,EAAE;MACxB,MAAMoG,WAAW,GAAG,CAAC,CAACd,EAAE,GAAGvB,MAAM,CAACiB,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGM,EAAE,CAAClB,IAAI,MAAM,OAAO,GAAGL,MAAM,CAACiB,OAAO,CAAC,CAACoB,WAAW,GAAG,IAAI;MACxH,IAAI,CAACA,WAAW,EAAE,OAAOpG,KAAK;MAC9B,MAAMqG,QAAQ,GAAGrG,KAAK,CAAC+E,GAAG,CACxB,CAACW,EAAE,EAAEY,GAAG,KAAKzB,SAAS,CAAC;QACrB7E,KAAK,EAAE0F,EAAE;QACT3B,MAAM,EAAEqC,WAAW;QACnBrB,GAAG;QACHC,OAAO;QACPC,QAAQ,EAAE,GAAGA,QAAQ,IAAIqB,GAAG,GAAG;QAC/BpB,EAAE;QACFC,MAAM;QACNC;MACF,CAAC,CACH,CAAC;MACD,IAAIV,eAAe,CAAC2B,QAAQ,CAAC,EAAE;QAC7B,OAAOhE,OAAO,CAAC4D,GAAG,CAACI,QAAQ,CAAC;MAC9B;MACA,OAAOA,QAAQ;IACjB,CAAC,MAAM,IAAI,UAAU,IAAIrG,KAAK,EAAE;MAC9B,OAAOA,KAAK;IACd,CAAC,MAAM;MACL,MAAMuG,YAAY,GAAG,CAAC,CAAChB,EAAE,GAAGxB,MAAM,CAACiB,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGO,EAAE,CAACnB,IAAI,MAAM,QAAQ,GAAGL,MAAM,CAACiB,OAAO,CAAC,CAACuB,YAAY,GAAGxC,MAAM;MAC7H,OAAOyC,UAAU,CAAC;QAChBxG,KAAK;QACL+D,MAAM,EAAEwC,YAAY;QACpBxB,GAAG;QACHG,EAAE;QACFuB,WAAW,EAAGC,CAAC,IAAK,GAAGzB,QAAQ,IAAIyB,CAAC,EAAE;QACtCvB,MAAM;QACNC;MACF,CAAC,CAAC;IACJ;EACF;EACA,OAAOpF,KAAK;AACd,CAAC;AACD,IAAIwG,UAAU,GAAGG,KAAA,IAQX;EAAA,IARY;IAChB3G,KAAK;IACL+D,MAAM;IACNgB,GAAG;IACHG,EAAE;IACFuB,WAAW;IACXtB,MAAM;IACNC;EACF,CAAC,GAAAuB,KAAA;EACC,MAAMC,QAAQ,GAAGpI,MAAM,CAACqI,OAAO,CAAC7G,KAAK,CAAC,CAAC+E,GAAG,CAAC+B,KAAA,IAAY;IAAA,IAAX,CAACJ,CAAC,EAAEpC,CAAC,CAAC,GAAAwC,KAAA;IAChD,MAAMC,IAAI,GAAG;MACX/G,KAAK,EAAEsE,CAAC;MACRP,MAAM;MACNgB,GAAG;MACHC,OAAO,EAAE0B,CAAC;MACVzB,QAAQ,EAAEwB,WAAW,CAACC,CAAC,CAAC;MACxBxB,EAAE;MACFC,MAAM;MACNC;IACF,CAAC;IACD,MAAMiB,QAAQ,GAAGxB,SAAS,CAACkC,IAAI,CAAC;IAChC,IAAI1C,SAAS,CAACgC,QAAQ,CAAC,EAAE;MACvB,OAAOA,QAAQ,CAACrD,IAAI,CAAEgE,aAAa,KAAM;QACvC,CAACN,CAAC,GAAGM;MACP,CAAC,CAAC,CAAC;IACL;IACA,OAAO;MACL,CAACN,CAAC,GAAGL;IACP,CAAC;EACH,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,IAAI3B,eAAe,CAACkC,QAAQ,CAAC,EAAE;IAC7B,OAAOvE,OAAO,CAAC4D,GAAG,CAACW,QAAQ,CAAC,CAAC5D,IAAI,CAACuB,OAAO,CAAC;EAC5C;EACA,OAAOA,OAAO,CAACqC,QAAQ,CAAC;AAC1B,CAAC;AACD,SAASK,QAAQA,CAACxC,IAAI,EAAEM,GAAG,EAAEI,MAAM,EAAwB;EAAA,IAAtBC,YAAY,GAAA8B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACvD,IAAI7B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE8B,EAAE,EAAEC,EAAE;EACtB,MAAMC,QAAQ,GAAG,MAAM,IAAI9C,IAAI,GAAGA,IAAI,CAACL,IAAI,GAAG,MAAM;EACpD,MAAMwB,eAAe,GAAG2B,QAAQ,KAAK,MAAM,GAAGpC,MAAM,CAACqC,IAAI,GAAG,CAACnC,EAAE,GAAGF,MAAM,CAACU,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,EAAE,CAACkC,QAAQ,CAAC;EACpH,MAAMX,QAAQ,GAAGJ,UAAU,CAAC;IAC1BxG,KAAK,EAAE8D,YAAY,CAAC,CAACwB,EAAE,GAAGb,IAAI,CAACuB,KAAK,KAAK,IAAI,GAAGV,EAAE,GAAG,CAAC,CAAC,EAAE,CAACC,EAAE,GAAGK,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC7B,MAAM,KAAK,IAAI,GAAGwB,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5IxB,MAAM,EAAE,CAACsD,EAAE,GAAGzB,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC7B,MAAM,KAAK,IAAI,GAAGsD,EAAE,GAAG,CAAC,CAAC;IAC1FtC,GAAG;IACHG,EAAE,EAAET,IAAI,CAACuB,KAAK,GAAG,CAACsB,EAAE,GAAG7C,IAAI,CAACuB,KAAK,CAACd,EAAE,KAAK,IAAI,GAAGoC,EAAE,GAAG,MAAM,GAAG,MAAM;IACpEb,WAAW,EAAGC,CAAC,IAAKA,CAAC;IACrBvB,MAAM;IACNC;EACF,CAAC,CAAC;EACF,IAAIf,SAAS,CAACuC,QAAQ,CAAC,EAAE;IACvB,OAAOA,QAAQ,CAAC5D,IAAI,CAAEyE,aAAa,IAAKhH,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAAC,EAAE;MAC9EuB,KAAK,EAAEyB;IACT,CAAC,CAAC,CAAC;EACL;EACA,OAAOhH,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAAC,EAAE;IAC7CuB,KAAK,EAAEY;EACT,CAAC,CAAC;AACJ;;AAEA;AACA,SAASc,QAAQA,CAACvC,MAAM,EAAEV,IAAI,EAAEkD,cAAc,EAA8D;EAAA,IAA5DC,gBAAgB,GAAAV,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGS,cAAc;EAAA,IAAEE,QAAQ,GAAAX,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEU,aAAa,GAAAZ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACxG,MAAMW,SAAS,GAAGlE,OAAO,CAAC,MAAM;IAC9B,MAAMmE,MAAM,GAAGf,QAAQ,CACrBxC,IAAI,EACJ,CAACe,OAAO,EAAEyC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAElD,QAAQ,KAAK;MACjD,MAAMmD,YAAY,GAAGnD,QAAQ,CAACoD,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC;MACxD,MAAMC,UAAU,GAAG,CAACT,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC5C,QAAQ,CAAC,MAAM4C,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACO,YAAY,CAAC,CAAC,IAAIN,aAAa;MAC5I,MAAMS,MAAM,GAAGD,UAAU,GAAGV,gBAAgB,GAAGD,cAAc;MAC7D,MAAMa,IAAI,GAAIC,OAAO,IAAKF,MAAM,CAAC9H,aAAa,CAACL,cAAc,CAAC;QAC5DsI,KAAK,EAAE,CAACP,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC/D,IAAI,MAAM,MAAM,GAAG+D,KAAK,CAACO,KAAK,GAAG,EAAE;QAC1EC,QAAQ,EAAE,CAACR,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC/D,IAAI,MAAM,MAAM,GAAG+D,KAAK,CAACQ,QAAQ,GAAG;MAChF,CAAC,EAAEF,OAAO,CAAC,EAAE;QACXG,IAAI,EAAEV,QAAQ;QACd1C;MACF,CAAC,CAAC,CAAC;MACH,OAAOgD,IAAI;IACb,CAAC,EACDrD,MACF,CAAC,CAACa,KAAK;IACP,OAAOgC,MAAM;EACf,CAAC,EAAE,CAAC7C,MAAM,EAAEV,IAAI,EAAEoD,QAAQ,EAAEC,aAAa,CAAC,CAAC;EAC3C,MAAMe,WAAW,GAAGhF,OAAO,CACzB,MAAMzD,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAACuB,KAAK,CAAC,EAAE+B,SAAS,CAAC,EAC/D,CAACtD,IAAI,CAACuB,KAAK,EAAE+B,SAAS,CACxB,CAAC;EACD,OAAOc,WAAW;AACpB;;AAEA;AACA1F,iBAAiB,CAAC,CAAC;AACnB,SAAS2F,UAAU,QAAQ,OAAO;AAClC,SAASC,GAAG,QAAQ,mBAAmB;AACvC,IAAIC,cAAc,GAAIhD,KAAK,IAAK,eAAgB+C,GAAG,CAACE,UAAU,EAAE7I,cAAc,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,CAAC;AAC1F,IAAIkD,IAAI,GAAGC,KAAA,IAIL;EAAA,IAJM;IACVhE,MAAM;IACNV,IAAI;IACJ2E;EACF,CAAC,GAAAD,KAAA;EACC,MAAME,SAAS,GAAGlE,MAAM,CAACU,UAAU,CAACpB,IAAI,CAACL,IAAI,CAAC;EAC9C,MAAM4B,KAAK,GAAG0B,QAAQ,CAACvC,MAAM,EAAEV,IAAI,EAAGsD,SAAS,IAAK,eAAgBgB,GAAG,CAACC,cAAc,EAAEvI,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE2H,SAAS,CAAC,EAAE;IAAE5C,MAAM;IAAEiE;EAAS,CAAC,CAAC,CAAC,CAAC;EAC5J,OAAO,eAAgBL,GAAG,CACxBM,SAAS,CAACd,MAAM,EAChB9H,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,EAAE;IACvCsD,IAAI,EAAE7I,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAACsD,IAAI,CAAC,EAAE;MAClDC,cAAc,EAAEC,cAAc;MAC9BJ,QAAQ,EAAEA,QAAQ,IAAI,CAAC;IACzB,CAAC;EACH,CAAC,CACH,CAAC;AACH,CAAC;AACD,IAAIH,UAAU,GAAGH,UAAU,CACzB,SAASW,kBAAkBA,CAAAC,KAAA,EAAkDC,GAAG,EAAE;EAAA,IAAtD;IAAEC,SAAS;IAAEC,KAAK;IAAErE,OAAO;IAAEL,MAAM;IAAEiE;EAAS,CAAC,GAAAM,KAAA;EACzE,OAAO,eAAgBX,GAAG,CAAC,KAAK,EAAE;IAAEa,SAAS;IAAEC,KAAK;IAAEF,GAAG;IAAEG,QAAQ,EAAEtE,OAAO,CAACT,GAAG,CAAEN,IAAI,IAAK;MACzF,IAAI,CAACU,MAAM,CAACU,UAAU,CAACpB,IAAI,CAACL,IAAI,CAAC,EAAE;QACjC,OAAO,IAAI;MACb;MACA,OAAO,eAAgB2E,GAAG,CACxBG,IAAI,EACJ;QACE/D,MAAM;QACNV,IAAI;QACJ2E;MACF,CAAC,EACD3E,IAAI,CAACuB,KAAK,CAACd,EACb,CAAC;IACH,CAAC;EAAE,CAAC,CAAC;AACP,CACF,CAAC;;AAED;AACA,SAAS6E,QAAQ,EAAEhB,GAAG,IAAIiB,IAAI,QAAQ,mBAAmB;AACzD,SAASR,cAAcA,CAAAS,KAAA,EAMpB;EAAA,IANqB;IACtBrB,IAAI;IACJnF,IAAI;IACJyG,MAAM,GAAG,MAAM;IACf/E,MAAM;IACNiE,QAAQ,GAAG,CAAC;EACd,CAAC,GAAAa,KAAA;EACC,IAAIE,YAAY,GAAG5G,eAAe;EAClC,IAAIiC,OAAO,GAAG,CAAC/B,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC+B,OAAO,KAAK,EAAE;EAC1D,IAAI,CAAC/B,IAAI,IAAI,CAAC0B,MAAM,EAAE;IACpB,OAAO,IAAI;EACb;EACA,IAAI+E,MAAM,KAAK7G,UAAU,IAAIuF,IAAI,KAAKtF,QAAQ,EAAE;IAC9C6G,YAAY,GAAG,GAAGD,MAAM,IAAItB,IAAI,EAAE;IAClCpD,OAAO,GAAGhC,SAAS,CAACC,IAAI,EAAE0G,YAAY,CAAC,CAACvG,KAAK,CAACuG,YAAY,CAAC;EAC7D;EACA,OAAO,eAAgBH,IAAI,CAACD,QAAQ,EAAE;IAAED,QAAQ,EAAEtE,OAAO,CAACT,GAAG,CAAEN,IAAI,IAAK;MACtE,MAAM4E,SAAS,GAAGlE,MAAM,CAACU,UAAU,CAACpB,IAAI,CAACL,IAAI,CAAC;MAC9C,MAAM4B,KAAK,GAAGvF,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAACuB,KAAK,CAAC,EAAE;QAC1DsD,IAAI,EAAE;UACJC,cAAc,EAAEa,KAAA;YAAA,IAAC;cAAExB,IAAI,EAAEyB;YAAM,CAAC,GAAAD,KAAA;YAAA,OAAK,eAAgBJ,IAAI,CACvDR,cAAc,EACd;cACEZ,IAAI,EAAEyB,KAAK;cACX5G,IAAI;cACJyG,MAAM,EAAEzF,IAAI,CAACuB,KAAK,CAACd,EAAE;cACrBC,MAAM;cACNiE;YACF,CACF,CAAC;UAAA;UACDA,QAAQ;UACRkB,OAAO,EAAE,IAAI;UACbC,SAAS,EAAE;QACb;MACF,CAAC,CAAC;MACF,MAAMC,UAAU,GAAG/J,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAAC,EAAE;QAAEuB;MAAM,CAAC,CAAC;MACrE,MAAMyE,cAAc,GAAG/C,QAAQ,CAACvC,MAAM,EAAEqF,UAAU,EAAGE,MAAM,IAAK,eAAgBV,IAAI,CAAChB,cAAc,EAAEvI,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEsK,MAAM,CAAC,EAAE;QAAEvF,MAAM;QAAEiE;MAAS,CAAC,CAAC,CAAC,CAAC;MACtK,IAAIC,SAAS,EAAE;QACb,OAAO,eAAgBW,IAAI,CAACX,SAAS,CAACd,MAAM,EAAEnI,cAAc,CAAC,CAAC,CAAC,EAAEqK,cAAc,CAAC,EAAED,UAAU,CAACxE,KAAK,CAACd,EAAE,CAAC;MACxG;MACA,OAAO,IAAI;IACb,CAAC;EAAE,CAAC,CAAC;AACP;AACA,SAASyF,MAAMA,CAAAC,KAAA,EAIZ;EAAA,IAJa;IACdzF,MAAM;IACN1B,IAAI;IACJ2F,QAAQ,GAAG,CAAC;EACd,CAAC,GAAAwB,KAAA;EACC,IAAIvF,EAAE;EACN,MAAMwF,SAAS,GAAG,OAAO,IAAIpH,IAAI,CAAC+D,IAAI,GAAG/D,IAAI,CAAC+D,IAAI,CAACxB,KAAK,GAAGvC,IAAI,CAAC+D,IAAI;EACpE,MAAMsD,KAAK,GAAGD,SAAS,CAACC,KAAK,IAAI,EAAE;EACnC,MAAM9E,KAAK,GAAGvF,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEyK,SAAS,CAAC,EAAE;IACzDvB,IAAI,EAAE;MACJC,cAAc,EAAEwB,KAAA;QAAA,IAAC;UAAEnC;QAAK,CAAC,GAAAmC,KAAA;QAAA,OAAK,eAAgBf,IAAI,CAChDR,cAAc,EACd;UACEZ,IAAI;UACJnF,IAAI;UACJ0B,MAAM;UACNiE;QACF,CACF,CAAC;MAAA;MACDmB,SAAS,EAAE,KAAK;MAChBD,OAAO,EAAE,IAAI;MACblB;IACF,CAAC;IACD0B,KAAK;IACLE,QAAQ,EAAE,KAAK;IACf9F,EAAE,EAAE;EACN,CAAC,CAAC;EACF,MAAMuF,cAAc,GAAG/C,QAAQ,CAACvC,MAAM,EAAE;IAAEf,IAAI,EAAE,MAAM;IAAE4B;EAAM,CAAC,EAAG0E,MAAM,IAAK,eAAgBV,IAAI,CAAChB,cAAc,EAAEvI,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEsK,MAAM,CAAC,EAAE;IAAEvF,MAAM;IAAEiE;EAAS,CAAC,CAAC,CAAC,CAAC;EACnL,IAAI,CAAC/D,EAAE,GAAGF,MAAM,CAACqC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGnC,EAAE,CAACkD,MAAM,EAAE;IACnD,OAAO,eAAgByB,IAAI,CAAC7E,MAAM,CAACqC,IAAI,CAACe,MAAM,EAAE9H,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqK,cAAc,CAAC,EAAE;MAAEX,QAAQ,EAAE,eAAgBE,IAAI,CAChIR,cAAc,EACd;QACErE,MAAM;QACN1B,IAAI;QACJmF,IAAI,EAAEtF,QAAQ;QACd8F;MACF,CACF;IAAE,CAAC,CAAC,CAAC;EACP;EACA,OAAO,eAAgBY,IAAI,CACzBR,cAAc,EACd;IACErE,MAAM;IACN1B,IAAI;IACJmF,IAAI,EAAEtF,QAAQ;IACd8F;EACF,CACF,CAAC;AACH;;AAEA;AACAjG,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;AACnB,OAAO8H,IAAI,MAAM,iBAAiB;AAClC,IAAIC,UAAU,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;EACrC,OAAOD,OAAO,GAAG3M,MAAM,CAACwF,IAAI,CAACmH,OAAO,CAACnF,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC/B,MAAM,CAAC,CAACC,GAAG,EAAEO,IAAI,KAAK;IACtE,MAAM4G,YAAY,GAAG,CAACF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACnF,KAAK,KAAK,CAAC,CAAC;IACrE,MAAMsF,YAAY,GAAG,CAACF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACpF,KAAK,KAAK,CAAC,CAAC;IACrE,OAAOvF,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE8D,GAAG,CAAC,EAAE;MAC5C,CAACO,IAAI,GAAG,CAACwG,IAAI,CAACK,YAAY,CAAC7G,IAAI,CAAC,EAAE4G,YAAY,CAAC5G,IAAI,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,CAAC;;AAED;AACA,OAAO8G,KAAK,MAAM,iBAAiB;AACnC,IAAIC,KAAK,GAAG;EAAEC,UAAU,EAAE,CAAC;AAAE,CAAC;AAC9B,IAAIC,oBAAoB,GAAG,SAAAA,CAACC,EAAE,EAAEC,EAAE;EAAA,SAAAC,IAAA,GAAA3E,SAAA,CAAAC,MAAA,EAAK2E,EAAE,OAAA5F,KAAA,CAAA2F,IAAA,OAAAA,IAAA,WAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;IAAFD,EAAE,CAAAC,IAAA,QAAA7E,SAAA,CAAA6E,IAAA;EAAA;EAAA,OAAK9J,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC0J,EAAE,EAAEC,EAAE,EAAE,GAAGE,EAAE,CAAC,EAAE,UAAWrH,IAAI,EAAEU,MAAM;IAAA,IAAEiE,QAAQ,GAAAlC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,IAAE8E,cAAc,GAAA9E,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAAA,IAAE6E,YAAY,GAAA/E,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAAA,IAAE8E,OAAO,GAAAhF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;IAAA,oBAAE;MACxK,MAAMiF,aAAa,GAAG,MAAM,IAAI1H,IAAI,IAAIA,IAAI,CAACL,IAAI,KAAK,MAAM,GAAGe,MAAM,CAACU,UAAU,CAACpB,IAAI,CAACL,IAAI,CAAC,GAAGe,MAAM,CAACqC,IAAI;MACzG,MAAM4E,YAAY,GAAGhM,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAAC;MAC7C,MAAM4H,iBAAiB,GAAG,CAACF,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACG,WAAW,KAAK7H,IAAI,CAACuB,KAAK;MACpG,MAAMd,EAAE,GAAG,IAAI,IAAIT,IAAI,CAACuB,KAAK,GAAGvB,IAAI,CAACuB,KAAK,CAACd,EAAE,GAAG,MAAM;MACtD,IAAImH,iBAAiB,EAAE;QACrB,MAAM;UAAE5H,IAAI,EAAE2G,OAAO,GAAG,IAAI;UAAEmB,QAAQ,GAAG,CAAC;QAAE,CAAC,GAAGf,KAAK,CAACC,UAAU,CAACvG,EAAE,CAAC,IAAI,CAAC,CAAC;QAC1E,IAAIT,IAAI,IAAI8G,KAAK,CAAC9G,IAAI,EAAE2G,OAAO,CAAC,EAAE;UAChC,OAAO;YAAEoB,IAAI,EAAED,QAAQ;YAAEE,SAAS,EAAE;UAAM,CAAC;QAC7C;QACA,MAAMC,OAAO,GAAGxB,UAAU,CAACzG,IAAI,EAAE2G,OAAO,CAAC;QACzC,IAAIY,cAAc,EAAE;UAClBA,cAAc,CAACvH,IAAI,CAAC;QACtB;QACA,MAAM;UAAEuB,KAAK,EAAEyB,aAAa;UAAEI,QAAQ,GAAG,CAAC;QAAE,CAAC,GAAG,MAAMsE,aAAa,CAACG,WAAW,CAAC7H,IAAI,EAAE;UACpFiI,OAAO;UACPC,QAAQ,EAAEvB,OAAO;UACjBhC,QAAQ,EAAEhJ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgJ,QAAQ,CAAC,EAAE+C,aAAa,CAAC/C,QAAQ,CAAC;UAC9E8C;QACF,CAAC,CAAC;QACFE,YAAY,CAACpG,KAAK,GAAG5F,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAACuB,KAAK,CAAC,EAAEyB,aAAa,CAAC;QAClF,IAAIjJ,MAAM,CAACwF,IAAI,CAAC6D,QAAQ,CAAC,CAACV,MAAM,EAAE;UAChCiF,YAAY,CAACvE,QAAQ,GAAGA,QAAQ;QAClC;MACF;MACA,IAAI+E,wBAAwB,GAAG,MAAM3F,QAAQ,CAC3CmF,YAAY,EACX5G,OAAO,IAAKvD,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,aAAa;QAC9C,OAAO,MAAMI,OAAO,CAAC4D,GAAG,CACtBT,OAAO,CAACT,GAAG,CACR8H,SAAS,IAAK5K,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,aAAa;UAChD,OAAO,CAAC,MAAMyJ,oBAAoB,CAChCmB,SAAS,EACT1H,MAAM,EACNiE,QAAQ,EACR4C,cAAc,EACdC,YAAY,EACZC,OACF,CAAC,EAAEM,IAAI;QACT,CAAC,CACH,CACF,CAAC;MACH,CAAC,CAAC,EACFrH,MACF,CAAC;MACD,IAAIkH,iBAAiB,IAAIJ,YAAY,EAAE;QACrCA,YAAY,CAACG,YAAY,CAAC;MAC5B;MACAZ,KAAK,CAACC,UAAU,CAACvG,EAAE,CAAC,GAAG;QACrBT,IAAI;QACJ8H,QAAQ,EAAEK;MACZ,CAAC;MACD,OAAO;QACLJ,IAAI,EAAEI,wBAAwB;QAC9BH,SAAS,EAAE,CAAClB,KAAK,CAAC9G,IAAI,EAAEmI,wBAAwB;MAClD,CAAC;IACH,CAAC;EAAA,EAAC;AAAA;;AAEF;AACAzJ,iBAAiB,CAAC,CAAC;AACnB,IAAI2J,WAAW,GAAIrJ,IAAI,IAAKhD,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqD,IAAI,CAAC,EAAE;EAClE+D,IAAI,EAAE/D,IAAI,CAAC+D,IAAI,IAAI,CAAC,CAAC;EACrBhC,OAAO,EAAE/B,IAAI,CAAC+B,OAAO,IAAI;AAC3B,CAAC,CAAC;;AAEF;AACArC,iBAAiB,CAAC,CAAC;AACnB,IAAI4J,WAAW,GAAItI,IAAI,IAAK;EAC1B,OAAO,MAAM,IAAIA,IAAI,GAAGA,IAAI,GAAGhE,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAAC,EAAE;IACrEuB,KAAK,EAAEvF,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAACuB,KAAK,CAAC,EAAE;MAAEd,EAAE,EAAE;IAAO,CAAC,CAAC;IACpEd,IAAI,EAAE;EACR,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,SAAS4I,cAAcA,CAACrB,EAAE,EAAEC,EAAE,EAAE;EAC9B,OAAO3J,OAAO,CAAC,IAAI,EAAEiF,SAAS,EAAE,UAAWzD,IAAI,EAAE0B,MAAM;IAAA,IAAA8H,KAAA;IAAA,IAAE7D,QAAQ,GAAAlC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,IAAE8E,cAAc,GAAA9E,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAAA,IAAE6E,YAAY,GAAA/E,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAAA,oBAAE;MACpG,IAAI/B,EAAE;MACN,MAAM6H,aAAa,GAAGJ,WAAW,CAACrJ,IAAI,CAAC;MACvC,MAAM0J,WAAW,GAAIC,KAAK,IAAKnL,OAAO,CAACgL,KAAI,EAAE,IAAI,EAAE,aAAa;QAC9D,MAAMT,IAAI,GAAGO,WAAW,CAACK,KAAK,CAAC;QAC/BpB,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACQ,IAAI,CAAC;QACtD,MAAMD,QAAQ,GAAG,CAAC,MAAMb,oBAAoB,CAC1Cc,IAAI,EACJrH,MAAM,EACNiE,QAAQ,EACR,MAAM,CACN,CAAC,EACD,MAAM,CACN,CAAC,EACD,OACF,CAAC,EAAEoD,IAAI;QACP,MAAMa,YAAY,GAAG,MAAMpG,QAAQ,CACjCsF,QAAQ,EACRe,cAAc,EACdnI,MACF,CAAC;QACD8G,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACc,WAAW,CAACM,YAAY,CAAC,CAAC;QACvE,OAAOA,YAAY;MACrB,CAAC,CAAC;MACF,MAAMC,cAAc,GAAI9H,OAAO,IAAKvD,OAAO,CAACgL,KAAI,EAAE,IAAI,EAAE,aAAa;QACnE,OAAO5K,OAAO,CAAC4D,GAAG,CAACT,OAAO,CAACT,GAAG,CAACoI,WAAW,CAAC,CAAC;MAC9C,CAAC,CAAC;MACF,MAAMI,YAAY,GAAGA,CAAA,KAAMtL,OAAO,CAACgL,KAAI,EAAE,IAAI,EAAE,aAAa;QAC1D,IAAItH,GAAG;QACP,MAAM/B,KAAK,GAAG,CAAC+B,GAAG,GAAGlC,IAAI,CAACG,KAAK,KAAK,IAAI,GAAG+B,GAAG,GAAG,CAAC,CAAC;QACnDnH,MAAM,CAACqI,OAAO,CAACjD,KAAK,CAAC,CAAC4J,OAAO,CAAEC,GAAG,IAAKxL,OAAO,CAAC,IAAI,EAAE,CAACwL,GAAG,CAAC,EAAE,UAAAC,KAAA;UAAA,IAAW,CAAChK,OAAO,EAAE8B,OAAO,CAAC,GAAAkI,KAAA;UAAA,oBAAE;YACzF9J,KAAK,CAACF,OAAO,CAAC,GAAG,MAAMrB,OAAO,CAAC4D,GAAG,CAACT,OAAO,CAACT,GAAG,CAACoI,WAAW,CAAC,CAAC;UAC9D,CAAC;QAAA,EAAC,CAAC;QACH,OAAOvJ,KAAK;MACd,CAAC,CAAC;MACF,MAAM+J,OAAO,GAAG;QACdnG,IAAI,EAAE,MAAM2F,WAAW,CAACD,aAAa,CAAC1F,IAAI,CAAC;QAC3ChC,OAAO,EAAE,MAAM8H,cAAc,CAACJ,aAAa,CAAC1H,OAAO,CAAC;QACpD5B,KAAK,EAAE,MAAM2J,YAAY,CAAC;MAC5B,CAAC;MACD/O,MAAM,CAACwF,IAAI,CAAC,CAACqB,EAAE,GAAG6H,aAAa,CAACtJ,KAAK,KAAK,IAAI,GAAGyB,EAAE,GAAG,CAAC,CAAC,CAAC,CAACmI,OAAO,CAAE9J,OAAO,IAAKzB,OAAO,CAACgL,KAAI,EAAE,IAAI,EAAE,aAAa;QAC9G,MAAMzH,OAAO,GAAG0H,aAAa,CAACtJ,KAAK,CAACF,OAAO,CAAC;QAC5CiK,OAAO,CAAC/J,KAAK,CAACF,OAAO,CAAC,GAAG,MAAM4J,cAAc,CAAC9H,OAAO,CAAC;MACxD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACP,OAAOmI,OAAO;IAChB,CAAC;EAAA,EAAC;AACJ;;AAEA;AACAxK,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;AACnB,SAASyK,QAAQA,CAACnK,IAAI,EAAE0B,MAAM,EAAE0I,UAAU,EAAE;EAC1C,IAAIxI,EAAE,EAAEC,EAAE;EACV,MAAMwI,QAAQ,GAAIrJ,IAAI,IAAK;IACzB,OAAOwC,QAAQ,CACbxC,IAAI,EACJ,CAACe,OAAO,EAAEuI,QAAQ,EAAE7F,QAAQ,KAAK;MAC/B,IAAIvC,GAAG;MACP,OAAO,CAACA,GAAG,GAAGkI,UAAU,CAACrI,OAAO,EAAE;QAAEuI,QAAQ;QAAE7F;MAAS,CAAC,CAAC,KAAK,IAAI,GAAGvC,GAAG,GAAGH,OAAO;IACpF,CAAC,EACDL,MAAM,EACN,IACF,CAAC;EACH,CAAC;EACD,IAAI,OAAO,IAAI1B,IAAI,EAAE;IACnB,OAAOqK,QAAQ,CAACrK,IAAI,CAAC;EACvB;EACA,MAAMuK,KAAK,GAAGvK,IAAI;EAClB,MAAMG,KAAK,GAAG,CAACyB,EAAE,GAAG2I,KAAK,CAACpK,KAAK,KAAK,IAAI,GAAGyB,EAAE,GAAG,CAAC,CAAC;EAClD,MAAMI,aAAa,GAAGuI,KAAK,CAACxI,OAAO,CAACT,GAAG,CAAC+I,QAAQ,CAAC;EACjD,OAAO;IACLtG,IAAI,EAAEsG,QAAQ,CAACE,KAAK,CAACxG,IAAI,CAAC;IAC1BhC,OAAO,EAAE,CAACF,EAAE,GAAGuI,UAAU,CAACpI,aAAa,EAAE;MACvCsI,QAAQ,EAAE,MAAM;MAChB7F,QAAQ,EAAE;IACZ,CAAC,CAAC,KAAK,IAAI,GAAG5C,EAAE,GAAGG,aAAa;IAChC7B,KAAK,EAAEpF,MAAM,CAACwF,IAAI,CAACJ,KAAK,CAAC,CAACK,MAAM,CAC9B,CAACC,GAAG,EAAEiG,YAAY,KAAK1J,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE8D,GAAG,CAAC,EAAE;MAC5D,CAACiG,YAAY,GAAGvG,KAAK,CAACuG,YAAY,CAAC,CAACpF,GAAG,CAAC+I,QAAQ;IAClD,CAAC,CAAC,EACF,CAAC,CACH;EACF,CAAC;AACH;;AAEA;AACA,SAASG,cAAcA,CAACxK,IAAI,EAAEyK,cAAc,EAA+B;EAAA,IAA7B/I,MAAM,GAAA+B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;IAAErB,UAAU,EAAE,CAAC;EAAE,CAAC;EACvE,MAAMsI,OAAO,GAAI1J,IAAI,IAAK;IACxB,IAAIyJ,cAAc,CAACzJ,IAAI,CAACL,IAAI,CAAC,EAAE;MAC7B,OAAO3D,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAAC,EAAE;QAC7CuB,KAAK,EAAE5F,cAAc,CAAC;UACpB8E,EAAE,EAAET,IAAI,CAACuB,KAAK,CAACd;QACjB,CAAC,EAAEgJ,cAAc,CAACzJ,IAAI,CAACL,IAAI,CAAC,CAACK,IAAI,CAACuB,KAAK,CAAC;MAC1C,CAAC,CAAC;IACJ;IACA,OAAOvB,IAAI;EACb,CAAC;EACD,MAAMyI,aAAa,GAAGJ,WAAW,CAACrJ,IAAI,CAAC;EACvC,MAAMoH,SAAS,GAAGqC,aAAa,CAAC1F,IAAI,CAACxB,KAAK,IAAIkH,aAAa,CAAC1F,IAAI;EAChE,IAAI4G,OAAO,GAAGhO,cAAc,CAAC,CAAC,CAAC,EAAE8M,aAAa,CAAC1F,IAAI,CAAC;EACpD,IAAI0G,cAAc,CAAC,MAAM,CAAC,EAAE;IAC1BE,OAAO,CAACpI,KAAK,GAAGkI,cAAc,CAAC,MAAM,CAAC,CAACrD,SAAS,CAAC;EACnD;EACA,MAAMwD,mBAAmB,GAAG5N,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE8M,aAAa,CAAC,EAAE;IAAE1F,IAAI,EAAE4G;EAAQ,CAAC,CAAC;EAC/F,MAAME,WAAW,GAAGV,QAAQ,CAC1BS,mBAAmB,EACnBlJ,MAAM,EACLK,OAAO,IAAKA,OAAO,CAACT,GAAG,CAACoJ,OAAO,CAClC,CAAC;EACD,IAAI,CAACjB,aAAa,CAAC1F,IAAI,CAACxB,KAAK,EAAE;IAC7BsI,WAAW,CAAC9G,IAAI,GAAG8G,WAAW,CAAC9G,IAAI,CAACxB,KAAK;EAC3C;EACA,OAAOsI,WAAW;AACpB;;AAEA;AACAnL,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;AACnB,IAAIoL,gBAAgB,GAAG,CACrB;EAAEC,KAAK,EAAE,GAAG;EAAEC,MAAM,EAAE,MAAM;EAAEC,IAAI,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAClE;EAAEH,KAAK,EAAE,GAAG;EAAEC,MAAM,EAAE,MAAM;EAAEC,IAAI,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EAC/D;EAAEH,KAAK,EAAE,IAAI;EAAEC,MAAM,EAAE,MAAM;EAAEC,IAAI,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAQ,CAAC,CACjE;;AAED;AACA,IAAIC,eAAe,GAAG;EACpBnL,IAAI,EAAE;IAAE+B,OAAO,EAAE,EAAE;IAAEgC,IAAI,EAAE,CAAC,CAAC;IAAE5D,KAAK,EAAE,CAAC;EAAE,CAAC;EAC1CiL,EAAE,EAAE;IACFC,kBAAkB,EAAE,IAAI;IACxBC,mBAAmB,EAAE,IAAI;IACzBC,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,CAAC,CAAC;IACjBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,MAAM;IACnBC,SAAS,EAAE;MACTC,OAAO,EAAE;QACPd,KAAK,EAAED,gBAAgB,CAAC,CAAC,CAAC,CAACC,KAAK;QAChCC,MAAM,EAAEF,gBAAgB,CAAC,CAAC,CAAC,CAACE,MAAM,IAAI;MACxC,CAAC;MACDc,OAAO,EAAE,EAAE;MACXC,eAAe,EAAE;IACnB,CAAC;IACDrH,KAAK,EAAE;MAAEsH,KAAK,EAAE;IAAK;EACvB,CAAC;EACDC,OAAO,EAAE;IACPC,KAAK,EAAE,CAAC,CAAC;IACT/L,KAAK,EAAE,CAAC;EACV;AACF,CAAC;;AAED;AACAT,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;;AAEnB;AACAA,iBAAiB,CAAC,CAAC;AACnB,IAAIyM,SAAS,GAAIzF,YAAY,IAAK;EAChC,IAAI,CAACA,YAAY,EAAE;IACjB,OAAO,EAAE;EACX;EACA,IAAIA,YAAY,IAAIA,YAAY,CAACrJ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IAClD,OAAOqJ,YAAY,CAAC0F,KAAK,CAAC,GAAG,CAAC;EAChC;EACA,OAAO,CAACtM,eAAe,EAAE4G,YAAY,CAAC;AACxC,CAAC;;AAED;AACA,SAAS2F,eAAeA,CAACrL,IAAI,EAAEhB,IAAI,EAAErC,EAAE,EAAa;EAAA,IAAX2O,IAAI,GAAA7I,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAChD1I,MAAM,CAACqI,OAAO,CAACpD,IAAI,CAACG,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC4J,OAAO,CAACwC,KAAA,IAA6B;IAAA,IAA5B,CAAC7F,YAAY,EAAE3E,OAAO,CAAC,GAAAwK,KAAA;IAC/D,MAAM,CAACjC,QAAQ,CAAC,GAAG6B,SAAS,CAACzF,YAAY,CAAC;IAC1C,IAAI4D,QAAQ,KAAKtJ,IAAI,CAACuB,KAAK,CAACd,EAAE,EAAE;MAC9B9D,EAAE,CAAC2O,IAAI,EAAE5F,YAAY,EAAE3E,OAAO,CAAC;IACjC;EACF,CAAC,CAAC;AACJ;;AAEA;AACArC,iBAAiB,CAAC,CAAC;AACnB,OAAO8M,IAAI,MAAM,MAAM;;AAEvB;AACA9M,iBAAiB,CAAC,CAAC;AACnB,IAAI+M,UAAU,GAAGA,CAACzM,IAAI,EAAE0B,MAAM,KAAK;EACjC,OAAO8B,QAAQ,CAACxD,IAAI,EAAE,MAAM,IAAI,EAAE0B,MAAM,CAAC;AAC3C,CAAC;;AAED;AACA,IAAI;EAAEZ,OAAO,EAAE4L,QAAQ;EAAEC;AAAU,CAAC,GAAGH,IAAI;AAC3C,IAAII,WAAW,GAAGA,CAAC7D,IAAI,EAAErH,MAAM,KAAK;EAClC,OAAO1E,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEoM,IAAI,CAAC,EAAE;IAC7CxG,KAAK,EAAEmK,QAAQ,CAACD,UAAU,CAAC1D,IAAI,EAAErH,MAAM,CAAC,CAACa,KAAK;EAChD,CAAC,CAAC;AACJ,CAAC;AACD,IAAIsK,UAAU,GAAI9D,IAAI,IAAK;EACzB,MAAMxG,KAAK,GAAGoK,SAAS,CAAC5D,IAAI,CAACxG,KAAK,CAAC;EACnC,OAAOvF,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEoM,IAAI,CAAC,EAAE;IAC7CxG;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,SAASuK,YAAYA,CAACC,KAAK,EAAErL,MAAM,EAAqE;EAAA,IAAnEsL,UAAU,GAAAvJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAI1B,OAAO,IAAKA,OAAO;EAAA,IAAEkL,aAAa,GAAAxJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAIzC,IAAI,IAAKA,IAAI;EACpG,IAAIY,EAAE;EACN,IAAIsL,QAAQ,GAAG,CAAC,CAAC;EACjB,MAAMC,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMvD,cAAc,GAAGA,CAACyC,IAAI,EAAE5F,YAAY,EAAE3E,OAAO,EAAEsL,QAAQ,EAAEC,KAAK,KAAK;IACvE,IAAIpL,GAAG;IACP,MAAM,CAACoI,QAAQ,CAAC,GAAG5D,YAAY,CAAC0F,KAAK,CAAC,GAAG,CAAC;IAC1C,MAAMpK,aAAa,GAAG,CAAC,CAACE,GAAG,GAAG8K,UAAU,CAACjL,OAAO,EAAE2E,YAAY,EAAE2G,QAAQ,CAAC,KAAK,IAAI,GAAGnL,GAAG,GAAGH,OAAO,KAAK,EAAE;IACzG,MAAM,CAACsG,EAAE,EAAElD,IAAI,CAAC,GAAGuB,YAAY,CAAC0F,KAAK,CAAC,GAAG,CAAC;IAC1C,MAAMmB,eAAe,GAAG,GAAGD,KAAK,IAAIhD,QAAQ,IAAInF,IAAI,EAAE;IACtD,MAAMqI,WAAW,GAAGxL,aAAa,CAACV,GAAG,CACnC,CAACmM,SAAS,EAAEC,KAAK,KAAKC,WAAW,CAACF,SAAS,EAAE,CAAC,GAAGnB,IAAI,EAAEiB,eAAe,CAAC,EAAEG,KAAK,CAChF,CAAC;IACDP,YAAY,CAACI,eAAe,CAAC,GAAG;MAC9BK,UAAU,EAAEJ,WAAW,CAAClM,GAAG,CAAEN,IAAI,IAAKA,IAAI,CAACuB,KAAK,CAACd,EAAE,CAAC;MACpDd,IAAI,EAAE0M;IACR,CAAC;IACD,OAAO,CAACE,eAAe,EAAEC,WAAW,CAAC;EACvC,CAAC;EACD,MAAMK,mBAAmB,GAAGA,CAAC7M,IAAI,EAAEsM,KAAK,EAAEQ,WAAW,KAAK;IACxDzB,eAAe,CACbrL,IAAI,EACJ+L,KAAK,CAAC/M,IAAI,EACV,CAAC+N,WAAW,EAAEC,mBAAmB,EAAEC,cAAc,KAAK;MACpD,MAAM,CAACvH,YAAY,EAAE8G,WAAW,CAAC,GAAG3D,cAAc,CAChDkE,WAAW,EACXC,mBAAmB,EACnBC,cAAc,EACd,UAAU,EACVX,KACF,CAAC;MACDJ,QAAQ,CAACxG,YAAY,CAAC,GAAG8G,WAAW;IACtC,CAAC,EACDM,WACF,CAAC;EACH,CAAC;EACD,MAAMH,WAAW,GAAGA,CAAC3M,IAAI,EAAEsL,IAAI,EAAEoB,KAAK,KAAK;IACzC,MAAMQ,UAAU,GAAGjB,aAAa,CAACjM,IAAI,EAAEsL,IAAI,EAAEoB,KAAK,CAAC;IACnD,IAAI,CAACQ,UAAU,EAAE,OAAOlN,IAAI;IAC5B,MAAMS,EAAE,GAAGyM,UAAU,CAAC3L,KAAK,CAACd,EAAE;IAC9B,MAAM0B,QAAQ,GAAGnG,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE6G,QAAQ,CACxD0K,UAAU,EACV,CAACnM,OAAO,EAAEoM,SAAS,EAAEC,MAAM,KAAK;MAC9B,MAAM1H,YAAY,GAAG,GAAGyH,SAAS,IAAIC,MAAM,EAAE;MAC7C,MAAM,CAAC/F,EAAE,EAAEmF,WAAW,CAAC,GAAG3D,cAAc,CACtCyC,IAAI,EACJ5F,YAAY,EACZ3E,OAAO,EACP,MAAM,EACNoM,SACF,CAAC;MACD,OAAOX,WAAW;IACpB,CAAC,EACD9L,MACF,CAAC,CAACa,KAAK,CAAC,EAAE;MACRd;IACF,CAAC,CAAC;IACFoM,mBAAmB,CAAC7M,IAAI,EAAES,EAAE,EAAE6K,IAAI,CAAC;IACnC,MAAM5E,OAAO,GAAG1K,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqE,IAAI,CAAC,EAAE;MAAEuB,KAAK,EAAEY;IAAS,CAAC,CAAC;IAC5E,MAAMkL,gBAAgB,GAAG/B,IAAI,CAACA,IAAI,CAAC5I,MAAM,GAAG,CAAC,CAAC;IAC9C,MAAM,CAAC4G,QAAQ,EAAEnF,IAAI,CAAC,GAAGkJ,gBAAgB,GAAGA,gBAAgB,CAACjC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;IACpFgB,YAAY,CAAC3L,EAAE,CAAC,GAAG;MACjBzB,IAAI,EAAE0H,OAAO;MACb4G,QAAQ,EAAE1B,WAAW,CAAClF,OAAO,EAAEhG,MAAM,CAAC;MACtC4K,IAAI;MACJhC,QAAQ;MACRnF;IACF,CAAC;IACD,MAAMoJ,SAAS,GAAGvR,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE+K,OAAO,CAAC,EAAE;MAAEnF,KAAK,EAAE5F,cAAc,CAAC,CAAC,CAAC,EAAE+K,OAAO,CAACnF,KAAK;IAAE,CAAC,CAAC;IAC1G,IAAIY,QAAQ,CAAC1B,EAAE,KAAK,MAAM,EAAE;MAC1B,OAAO8M,SAAS,CAAC,MAAM,CAAC;MACxB,OAAOA,SAAS,CAAChM,KAAK,CAAC,IAAI,CAAC;IAC9B;IACA,OAAOgM,SAAS;EAClB,CAAC;EACD,MAAMpO,KAAK,GAAG4M,KAAK,CAAC/M,IAAI,CAACG,KAAK,IAAI,CAAC,CAAC;EACpC,MAAM,CAACqO,CAAC,EAAEC,UAAU,CAAC,GAAG5E,cAAc,CACpC,EAAE,EACF/J,eAAe,EACfiN,KAAK,CAAC/M,IAAI,CAAC+B,OAAO,EAClB,MACF,CAAC;EACD,MAAM2M,gBAAgB,GAAGD,UAAU;EACnC,MAAME,qBAAqB,GAAG5T,MAAM,CAACwF,IAAI,CAAC2M,QAAQ,CAAC;EACnDnS,MAAM,CAACwF,IAAI,CAACJ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC4J,OAAO,CAAErD,YAAY,IAAK;IACjD,MAAM,CAAC4D,QAAQ,CAAC,GAAG5D,YAAY,CAAC0F,KAAK,CAAC,GAAG,CAAC;IAC1C,IAAIuC,qBAAqB,CAACC,QAAQ,CAAClI,YAAY,CAAC,EAAE;MAChD;IACF;IACA,MAAM,CAAC2B,EAAE,EAAEmF,WAAW,CAAC,GAAG3D,cAAc,CACtC,CAAC/J,eAAe,CAAC,EACjB4G,YAAY,EACZvG,KAAK,CAACuG,YAAY,CAAC,EACnB,UAAU,EACV4D,QACF,CAAC;IACD4C,QAAQ,CAACxG,YAAY,CAAC,GAAG8G,WAAW;EACtC,CAAC,EAAEN,QAAQ,CAAC;EACZ,MAAM2B,aAAa,GAAGlB,WAAW,CAC/B;IACEhN,IAAI,EAAE,MAAM;IACZ4B,KAAK,EAAEvF,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE,CAACiF,EAAE,GAAGmL,KAAK,CAAC/M,IAAI,CAAC+D,IAAI,CAACxB,KAAK,KAAK,IAAI,GAAGX,EAAE,GAAGmL,KAAK,CAAC/M,IAAI,CAAC+D,IAAI,CAAC,EAAE;MAAEtC,EAAE,EAAE;IAAO,CAAC;EACtH,CAAC,EACD,EAAE,EACF,CAAC,CACH,CAAC;EACD,MAAMsC,IAAI,GAAG/G,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEoQ,KAAK,CAAC/M,IAAI,CAAC+D,IAAI,CAAC,EAAE;IAC9DxB,KAAK,EAAEsM,aAAa,CAACtM;EACvB,CAAC,CAAC;EACF,OAAOvF,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEoQ,KAAK,CAAC,EAAE;IAC9C/M,IAAI,EAAE;MACJ+D,IAAI;MACJhC,OAAO,EAAE2M,gBAAgB;MACzBvO,KAAK,EAAExD,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEoQ,KAAK,CAAC/M,IAAI,CAACG,KAAK,CAAC,EAAE+M,QAAQ;IACtE,CAAC;IACDjB,OAAO,EAAE;MACPC,KAAK,EAAEvP,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEoQ,KAAK,CAACd,OAAO,CAACC,KAAK,CAAC,EAAEkB,YAAY,CAAC;MAC5EjN,KAAK,EAAExD,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEoQ,KAAK,CAACd,OAAO,CAAC9L,KAAK,CAAC,EAAEgN,YAAY;IAC7E;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,IAAI2B,UAAU,GAAG;AACf;AACC9O,IAAI,IAAK;EACR,MAAMoH,SAAS,GAAGpH,IAAI,CAAC+D,IAAI,CAACxB,KAAK,IAAIvC,IAAI,CAAC+D,IAAI;EAC9C,IAAIhJ,MAAM,CAACwF,IAAI,CAACP,IAAI,CAAC+D,IAAI,CAAC,CAACL,MAAM,GAAG,CAAC,IAAI,CAAC1D,IAAI,CAAC+D,IAAI,CAACxB,KAAK,EAAE;IACzDwM,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;IACD,OAAOhS,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEqD,IAAI,CAAC,EAAE;MAC7C+D,IAAI,EAAE;QACJxB,KAAK,EAAE5F,cAAc,CAAC,CAAC,CAAC,EAAEyK,SAAS;MACrC;IACF,CAAC,CAAC;EACJ;EACA,OAAOpH,IAAI;AACb,CAAC;AACD;AACA,CAACA,IAAI,EAAE0B,MAAM,KAAK;EAChB,IAAIE,EAAE;EACN,IAAI,CAACF,MAAM,EAAE,OAAO1B,IAAI;EACxB+O,OAAO,CAACE,GAAG,CAAC,iCAAiC,CAAC;EAC9C,MAAMC,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,QAAQ,GAAGnS,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEwO,eAAe,CAAC,EAAE;IAAEnL;EAAK,CAAC,CAAC;EAC7E,MAAM;IAAEiM;EAAQ,CAAC,GAAGa,YAAY,CAACqC,QAAQ,EAAEzN,MAAM,CAAC;EAClD,MAAM0N,gBAAgB,GAAG,EAAE;EAC3BtC,YAAY,CAACqC,QAAQ,EAAEzN,MAAM,EAAE,CAACK,OAAO,EAAE2E,YAAY,EAAE2G,QAAQ,KAAK;IAClE,IAAInL,GAAG,EAAEL,EAAE,EAAEC,EAAE;IACf,IAAIuL,QAAQ,KAAK,UAAU,EAAE;MAC3B,MAAM,CAAC5L,EAAE,EAAE4N,QAAQ,CAAC,GAAG3I,YAAY,CAAC0F,KAAK,CAAC,GAAG,CAAC;MAC9C,MAAMkD,QAAQ,GAAGrD,OAAO,CAACC,KAAK,CAACzK,EAAE,CAAC,CAACzB,IAAI;MACvC,MAAMuP,aAAa,GAAGD,QAAQ,CAAC3O,IAAI;MACnC,MAAM6O,kBAAkB,GAAG/N,EAAE,KAAK,MAAM,GAAGC,MAAM,CAACqC,IAAI,GAAGrC,MAAM,CAACU,UAAU,CAACmN,aAAa,CAAC;MACzF,IAAI,CAAC,CAAC1N,EAAE,GAAG,CAACK,GAAG,GAAGsN,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAAClP,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4B,GAAG,CAACmN,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGxN,EAAE,CAAClB,IAAI,MAAM,MAAM,EAAE;QAC3JuO,YAAY,CAACzN,EAAE,CAAC,GAAGzE,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE2S,QAAQ,CAAC,EAAE;UAC7D/M,KAAK,EAAEvF,aAAa,CAACL,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE2S,QAAQ,CAAC/M,KAAK,CAAC,EAAE,CAACT,EAAE,GAAGoN,YAAY,CAACzN,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGK,EAAE,CAACS,KAAK,CAAC,EAAE;YAC5H,CAAC8M,QAAQ,GAAGtN;UACd,CAAC;QACH,CAAC,CAAC;QACFqN,gBAAgB,CAACK,IAAI,CAAC/I,YAAY,CAAC;MACrC;MACA,OAAO3E,OAAO;IAChB;IACA,OAAOA,OAAO;EAChB,CAAC,CAAC;EACF,MAAM2N,OAAO,GAAG5C,YAAY,CAC1BqC,QAAQ,EACRzN,MAAM,EACLK,OAAO,IAAKA,OAAO,EACnBf,IAAI,IAAK;IACR,IAAIkB,GAAG;IACP,OAAO,CAACA,GAAG,GAAGgN,YAAY,CAAClO,IAAI,CAACuB,KAAK,CAACd,EAAE,CAAC,KAAK,IAAI,GAAGS,GAAG,GAAGlB,IAAI;EACjE,CACF,CAAC;EACDoO,gBAAgB,CAACrF,OAAO,CAAErD,YAAY,IAAK;IACzC,IAAIxE,GAAG;IACP,MAAM,CAACsM,CAAC,EAAE/J,QAAQ,CAAC,GAAGiC,YAAY,CAAC0F,KAAK,CAAC,GAAG,CAAC;IAC7C2C,OAAO,CAACE,GAAG,CACT,6BAA6BvI,YAAY,kCAAkCjC,QAAQ,GACrF,CAAC;IACD,CAACvC,GAAG,GAAGwN,OAAO,CAAC1P,IAAI,CAACG,KAAK,KAAK,IAAI,GAAG,IAAI,GAAG,OAAO+B,GAAG,CAACwE,YAAY,CAAC;EACtE,CAAC,CAAC;EACF3L,MAAM,CAACwF,IAAI,CAAC,CAACqB,EAAE,GAAG8N,OAAO,CAAC1P,IAAI,CAACG,KAAK,KAAK,IAAI,GAAGyB,EAAE,GAAG,CAAC,CAAC,CAAC,CAACmI,OAAO,CAAErD,YAAY,IAAK;IACjF,MAAM,CAAC8H,CAAC,EAAE/J,QAAQ,CAAC,GAAGiC,YAAY,CAAC0F,KAAK,CAAC,GAAG,CAAC;IAC7C,MAAM,IAAI/J,KAAK,CACb,+BAA+BqE,YAAY,kDAAkDjC,QAAQ,IACvG,CAAC;EACH,CAAC,CAAC;EACF,OAAOiL,OAAO,CAAC1P,IAAI,CAACG,KAAK;EACzB,OAAOuP,OAAO,CAAC1P,IAAI;AACrB,CAAC,CACF;AACD,SAAS2P,OAAOA,CAAC3P,IAAI,EAAE0B,MAAM,EAAE;EAC7B,OAAOoN,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACtO,MAAM,CACpD,CAACC,GAAG,EAAEmP,SAAS,KAAKA,SAAS,CAACnP,GAAG,EAAEiB,MAAM,CAAC,EAC1C1B,IACF,CAAC;AACH;AAEA,SACErD,cAAc,EACdK,aAAa,EACbC,SAAS,EACTS,UAAU,EACVW,OAAO,EACPG,OAAO,EACPkB,iBAAiB,EACjBE,UAAU,EACVC,QAAQ,EACRC,eAAe,EACfO,YAAY,EACZe,SAAS,EACTyL,UAAU,EACVC,YAAY,EACZ3C,QAAQ,EACRpK,SAAS,EACTkE,QAAQ,EACRsB,cAAc,EACdC,UAAU,EACV0B,MAAM,EACNO,UAAU,EACVQ,oBAAoB,EACpBsB,cAAc,EACdiB,cAAc,EACdM,gBAAgB,EAChBK,eAAe,EACfwE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}