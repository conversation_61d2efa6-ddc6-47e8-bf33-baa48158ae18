{"ast": null, "code": "import React from'react';import{Navbar as Bootstra<PERSON><PERSON><PERSON><PERSON>,Nav,NavDropdown,Container,But<PERSON>}from'react-bootstrap';import{Link,useNavigate}from'react-router-dom';import{useAuth}from'../../contexts/AuthContext';import{useSiteName,useSiteLogo}from'../../contexts/SettingsContext';import authService from'../../services/authService';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Navbar=()=>{const{user,isAuthenticated,logout}=useAuth();const siteName=useSiteName();const siteLogo=useSiteLogo();const navigate=useNavigate();const handleLogout=async()=>{try{await logout();}catch(error){console.error('Logout error:',error);}};const handleAdminPanel=async()=>{try{await authService.navigateToAdminPanel();}catch(error){var _process$env$REACT_AP;console.error('Failed to access admin panel:',error);// Fallback to direct navigation (user will need to login manually)\nconst baseUrl=((_process$env$REACT_AP=process.env.REACT_APP_API_URL)===null||_process$env$REACT_AP===void 0?void 0:_process$env$REACT_AP.replace('/api',''))||'http://localhost:8000';const adminUrl=`${baseUrl}/admin`;window.open(adminUrl,'_blank');}};return/*#__PURE__*/_jsx(BootstrapNavbar,{bg:\"dark\",variant:\"dark\",expand:\"lg\",sticky:\"top\",children:/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsxs(BootstrapNavbar.Brand,{as:Link,to:\"/\",className:\"text-decoration-none d-flex align-items-center\",children:[siteLogo&&/*#__PURE__*/_jsx(\"img\",{src:siteLogo,alt:siteName,height:\"30\",className:\"me-2\",style:{maxWidth:'120px',objectFit:'contain'}}),siteName]}),/*#__PURE__*/_jsx(BootstrapNavbar.Toggle,{\"aria-controls\":\"basic-navbar-nav\"}),/*#__PURE__*/_jsxs(BootstrapNavbar.Collapse,{id:\"basic-navbar-nav\",children:[/*#__PURE__*/_jsx(Nav,{className:\"me-auto\",children:/*#__PURE__*/_jsx(Nav.Link,{as:Link,to:\"/\",className:\"text-decoration-none\",children:\"Home\"})}),/*#__PURE__*/_jsx(Nav,{className:\"ms-auto\",children:isAuthenticated?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Nav.Link,{as:Link,to:\"/dashboard\",className:\"text-decoration-none\",children:\"Dashboard\"}),/*#__PURE__*/_jsxs(NavDropdown,{title:(user===null||user===void 0?void 0:user.name)||'User',id:\"user-dropdown\",align:\"end\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>navigate('/profile'),children:\"Profile\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>navigate('/profile/edit'),children:\"Edit Profile\"}),(user===null||user===void 0?void 0:user.role)==='admin'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(NavDropdown.Divider,{}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:handleAdminPanel,children:\"Admin Panel\"})]}),/*#__PURE__*/_jsx(NavDropdown.Divider,{}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:handleLogout,children:\"Logout\"})]}),!(user!==null&&user!==void 0&&user.email_verified_at)&&/*#__PURE__*/_jsx(Button,{variant:\"outline-warning\",size:\"sm\",className:\"ms-2\",onClick:()=>navigate('/email-verification'),children:\"Verify Email\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Nav.Link,{as:Link,to:\"/login\",className:\"text-decoration-none\",children:\"Login\"}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",className:\"ms-2\",onClick:()=>navigate('/register'),children:\"Register\"})]})})]})]})});};export default Navbar;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "BootstrapNavbar", "Nav", "NavDropdown", "Container", "<PERSON><PERSON>", "Link", "useNavigate", "useAuth", "useSiteName", "useSiteLogo", "authService", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "user", "isAuthenticated", "logout", "siteName", "siteLogo", "navigate", "handleLogout", "error", "console", "handleAdminPanel", "navigateToAdminPanel", "_process$env$REACT_AP", "baseUrl", "process", "env", "REACT_APP_API_URL", "replace", "adminUrl", "window", "open", "bg", "variant", "expand", "sticky", "children", "Brand", "as", "to", "className", "src", "alt", "height", "style", "max<PERSON><PERSON><PERSON>", "objectFit", "Toggle", "Collapse", "id", "title", "name", "align", "<PERSON><PERSON>", "onClick", "role", "Divider", "email_verified_at", "size"], "sources": ["C:/laragon/www/frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navbar as BootstrapN<PERSON><PERSON>, Nav, NavDropdown, Container, But<PERSON> } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useSiteName, useSiteLogo } from '../../contexts/SettingsContext';\nimport authService from '../../services/authService';\n\nconst Navbar: React.FC = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const siteName = useSiteName();\n  const siteLogo = useSiteLogo();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const handleAdminPanel = async () => {\n    try {\n      await authService.navigateToAdminPanel();\n    } catch (error: any) {\n      console.error('Failed to access admin panel:', error);\n      // Fallback to direct navigation (user will need to login manually)\n      const baseUrl = process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:8000';\n      const adminUrl = `${baseUrl}/admin`;\n      window.open(adminUrl, '_blank');\n    }\n  };\n\n  return (\n    <BootstrapNavbar bg=\"dark\" variant=\"dark\" expand=\"lg\" sticky=\"top\">\n      <Container>\n        <BootstrapNavbar.Brand as={Link} to=\"/\" className=\"text-decoration-none d-flex align-items-center\">\n          {siteLogo && (\n            <img\n              src={siteLogo}\n              alt={siteName}\n              height=\"30\"\n              className=\"me-2\"\n              style={{ maxWidth: '120px', objectFit: 'contain' }}\n            />\n          )}\n          {siteName}\n        </BootstrapNavbar.Brand>\n\n        <BootstrapNavbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <BootstrapNavbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/\" className=\"text-decoration-none\">\n              Home\n            </Nav.Link>\n          </Nav>\n\n          <Nav className=\"ms-auto\">\n            {isAuthenticated ? (\n              <>\n                <Nav.Link as={Link} to=\"/dashboard\" className=\"text-decoration-none\">\n                  Dashboard\n                </Nav.Link>\n                <NavDropdown\n                  title={user?.name || 'User'}\n                  id=\"user-dropdown\"\n                  align=\"end\"\n                >\n                  <NavDropdown.Item onClick={() => navigate('/profile')}>\n                    Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => navigate('/profile/edit')}>\n                    Edit Profile\n                  </NavDropdown.Item>\n                  {user?.role === 'admin' && (\n                    <>\n                      <NavDropdown.Divider />\n                      <NavDropdown.Item onClick={handleAdminPanel}>\n                        Admin Panel\n                      </NavDropdown.Item>\n                    </>\n                  )}\n                  <NavDropdown.Divider />\n                  <NavDropdown.Item onClick={handleLogout}>\n                    Logout\n                  </NavDropdown.Item>\n                </NavDropdown>\n\n                {!user?.email_verified_at && (\n                  <Button\n                    variant=\"outline-warning\"\n                    size=\"sm\"\n                    className=\"ms-2\"\n                    onClick={() => navigate('/email-verification')}\n                  >\n                    Verify Email\n                  </Button>\n                )}\n              </>\n            ) : (\n              <>\n                <Nav.Link as={Link} to=\"/login\" className=\"text-decoration-none\">\n                  Login\n                </Nav.Link>\n                <Button\n                  variant=\"primary\"\n                  className=\"ms-2\"\n                  onClick={() => navigate('/register')}\n                >\n                  Register\n                </Button>\n              </>\n            )}\n          </Nav>\n        </BootstrapNavbar.Collapse>\n      </Container>\n    </BootstrapNavbar>\n  );\n};\n\nexport default Navbar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,GAAI,CAAAC,eAAe,CAAEC,GAAG,CAAEC,WAAW,CAAEC,SAAS,CAAEC,MAAM,KAAQ,iBAAiB,CAChG,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,WAAW,CAAEC,WAAW,KAAQ,gCAAgC,CACzE,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,KAAM,CAAAjB,MAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAEkB,IAAI,CAAEC,eAAe,CAAEC,MAAO,CAAC,CAAGZ,OAAO,CAAC,CAAC,CACnD,KAAM,CAAAa,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAa,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAa,QAAQ,CAAGhB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAiB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAJ,MAAM,CAAC,CAAC,CAChB,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CACF,CAAC,CAED,KAAM,CAAAE,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAhB,WAAW,CAACiB,oBAAoB,CAAC,CAAC,CAC1C,CAAE,MAAOH,KAAU,CAAE,KAAAI,qBAAA,CACnBH,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD;AACA,KAAM,CAAAK,OAAO,CAAG,EAAAD,qBAAA,CAAAE,OAAO,CAACC,GAAG,CAACC,iBAAiB,UAAAJ,qBAAA,iBAA7BA,qBAAA,CAA+BK,OAAO,CAAC,MAAM,CAAE,EAAE,CAAC,GAAI,uBAAuB,CAC7F,KAAM,CAAAC,QAAQ,CAAG,GAAGL,OAAO,QAAQ,CACnCM,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAE,QAAQ,CAAC,CACjC,CACF,CAAC,CAED,mBACEtB,IAAA,CAACZ,eAAe,EAACqC,EAAE,CAAC,MAAM,CAACC,OAAO,CAAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACC,MAAM,CAAC,KAAK,CAAAC,QAAA,cAChE3B,KAAA,CAACX,SAAS,EAAAsC,QAAA,eACR3B,KAAA,CAACd,eAAe,CAAC0C,KAAK,EAACC,EAAE,CAAEtC,IAAK,CAACuC,EAAE,CAAC,GAAG,CAACC,SAAS,CAAC,gDAAgD,CAAAJ,QAAA,EAC/FpB,QAAQ,eACPT,IAAA,QACEkC,GAAG,CAAEzB,QAAS,CACd0B,GAAG,CAAE3B,QAAS,CACd4B,MAAM,CAAC,IAAI,CACXH,SAAS,CAAC,MAAM,CAChBI,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAO,CAAEC,SAAS,CAAE,SAAU,CAAE,CACpD,CACF,CACA/B,QAAQ,EACY,CAAC,cAExBR,IAAA,CAACZ,eAAe,CAACoD,MAAM,EAAC,gBAAc,kBAAkB,CAAE,CAAC,cAC3DtC,KAAA,CAACd,eAAe,CAACqD,QAAQ,EAACC,EAAE,CAAC,kBAAkB,CAAAb,QAAA,eAC7C7B,IAAA,CAACX,GAAG,EAAC4C,SAAS,CAAC,SAAS,CAAAJ,QAAA,cACtB7B,IAAA,CAACX,GAAG,CAACI,IAAI,EAACsC,EAAE,CAAEtC,IAAK,CAACuC,EAAE,CAAC,GAAG,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,MAE5D,CAAU,CAAC,CACR,CAAC,cAEN7B,IAAA,CAACX,GAAG,EAAC4C,SAAS,CAAC,SAAS,CAAAJ,QAAA,CACrBvB,eAAe,cACdJ,KAAA,CAAAE,SAAA,EAAAyB,QAAA,eACE7B,IAAA,CAACX,GAAG,CAACI,IAAI,EAACsC,EAAE,CAAEtC,IAAK,CAACuC,EAAE,CAAC,YAAY,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,WAErE,CAAU,CAAC,cACX3B,KAAA,CAACZ,WAAW,EACVqD,KAAK,CAAE,CAAAtC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEuC,IAAI,GAAI,MAAO,CAC5BF,EAAE,CAAC,eAAe,CAClBG,KAAK,CAAC,KAAK,CAAAhB,QAAA,eAEX7B,IAAA,CAACV,WAAW,CAACwD,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAMrC,QAAQ,CAAC,UAAU,CAAE,CAAAmB,QAAA,CAAC,SAEvD,CAAkB,CAAC,cACnB7B,IAAA,CAACV,WAAW,CAACwD,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAMrC,QAAQ,CAAC,eAAe,CAAE,CAAAmB,QAAA,CAAC,cAE5D,CAAkB,CAAC,CAClB,CAAAxB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2C,IAAI,IAAK,OAAO,eACrB9C,KAAA,CAAAE,SAAA,EAAAyB,QAAA,eACE7B,IAAA,CAACV,WAAW,CAAC2D,OAAO,GAAE,CAAC,cACvBjD,IAAA,CAACV,WAAW,CAACwD,IAAI,EAACC,OAAO,CAAEjC,gBAAiB,CAAAe,QAAA,CAAC,aAE7C,CAAkB,CAAC,EACnB,CACH,cACD7B,IAAA,CAACV,WAAW,CAAC2D,OAAO,GAAE,CAAC,cACvBjD,IAAA,CAACV,WAAW,CAACwD,IAAI,EAACC,OAAO,CAAEpC,YAAa,CAAAkB,QAAA,CAAC,QAEzC,CAAkB,CAAC,EACR,CAAC,CAEb,EAACxB,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAE6C,iBAAiB,gBACvBlD,IAAA,CAACR,MAAM,EACLkC,OAAO,CAAC,iBAAiB,CACzByB,IAAI,CAAC,IAAI,CACTlB,SAAS,CAAC,MAAM,CAChBc,OAAO,CAAEA,CAAA,GAAMrC,QAAQ,CAAC,qBAAqB,CAAE,CAAAmB,QAAA,CAChD,cAED,CAAQ,CACT,EACD,CAAC,cAEH3B,KAAA,CAAAE,SAAA,EAAAyB,QAAA,eACE7B,IAAA,CAACX,GAAG,CAACI,IAAI,EAACsC,EAAE,CAAEtC,IAAK,CAACuC,EAAE,CAAC,QAAQ,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,OAEjE,CAAU,CAAC,cACX7B,IAAA,CAACR,MAAM,EACLkC,OAAO,CAAC,SAAS,CACjBO,SAAS,CAAC,MAAM,CAChBc,OAAO,CAAEA,CAAA,GAAMrC,QAAQ,CAAC,WAAW,CAAE,CAAAmB,QAAA,CACtC,UAED,CAAQ,CAAC,EACT,CACH,CACE,CAAC,EACkB,CAAC,EAClB,CAAC,CACG,CAAC,CAEtB,CAAC,CAED,cAAe,CAAA1C,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}