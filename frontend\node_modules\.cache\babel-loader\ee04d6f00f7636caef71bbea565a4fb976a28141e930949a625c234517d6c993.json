{"ast": null, "code": "/**\n * A universal utility to style components with multiple color modes. Always use it from the theme object.\n * It works with:\n *  - [Basic theme](https://mui.com/material-ui/customization/dark-mode/)\n *  - [CSS theme variables](https://mui.com/material-ui/customization/css-theme-variables/overview/)\n *  - Zero-runtime engine\n *\n * Tips: Use an array over object spread and place `theme.applyStyles()` last.\n *\n * With the styled function:\n * ✅ [{ background: '#e5e5e5' }, theme.applyStyles('dark', { background: '#1c1c1c' })]\n * 🚫 { background: '#e5e5e5', ...theme.applyStyles('dark', { background: '#1c1c1c' })}\n *\n * With the sx prop:\n * ✅ [{ background: '#e5e5e5' }, theme => theme.applyStyles('dark', { background: '#1c1c1c' })]\n * 🚫 { background: '#e5e5e5', ...theme => theme.applyStyles('dark', { background: '#1c1c1c' })}\n *\n * @example\n * 1. using with `styled`:\n * ```jsx\n *   const Component = styled('div')(({ theme }) => [\n *     { background: '#e5e5e5' },\n *     theme.applyStyles('dark', {\n *       background: '#1c1c1c',\n *       color: '#fff',\n *     }),\n *   ]);\n * ```\n *\n * @example\n * 2. using with `sx` prop:\n * ```jsx\n *   <Box sx={[\n *     { background: '#e5e5e5' },\n *     theme => theme.applyStyles('dark', {\n *        background: '#1c1c1c',\n *        color: '#fff',\n *      }),\n *     ]}\n *   />\n * ```\n *\n * @example\n * 3. theming a component:\n * ```jsx\n *   extendTheme({\n *     components: {\n *       MuiButton: {\n *         styleOverrides: {\n *           root: ({ theme }) => [\n *             { background: '#e5e5e5' },\n *             theme.applyStyles('dark', {\n *               background: '#1c1c1c',\n *               color: '#fff',\n *             }),\n *           ],\n *         },\n *       }\n *     }\n *   })\n *```\n */\nexport default function applyStyles(key, styles) {\n  // @ts-expect-error this is 'any' type\n  const theme = this;\n  if (theme.vars) {\n    if (!theme.colorSchemes?.[key] || typeof theme.getColorSchemeSelector !== 'function') {\n      return {};\n    }\n    // If CssVarsProvider is used as a provider, returns '*:where({selector}) &'\n    let selector = theme.getColorSchemeSelector(key);\n    if (selector === '&') {\n      return styles;\n    }\n    if (selector.includes('data-') || selector.includes('.')) {\n      // '*' is required as a workaround for Emotion issue (https://github.com/emotion-js/emotion/issues/2836)\n      selector = `*:where(${selector.replace(/\\s*&$/, '')}) &`;\n    }\n    return {\n      [selector]: styles\n    };\n  }\n  if (theme.palette.mode === key) {\n    return styles;\n  }\n  return {};\n}", "map": {"version": 3, "names": ["applyStyles", "key", "styles", "theme", "vars", "colorSchemes", "getColorSchemeSelector", "selector", "includes", "replace", "palette", "mode"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/system/esm/createTheme/applyStyles.js"], "sourcesContent": ["/**\n * A universal utility to style components with multiple color modes. Always use it from the theme object.\n * It works with:\n *  - [Basic theme](https://mui.com/material-ui/customization/dark-mode/)\n *  - [CSS theme variables](https://mui.com/material-ui/customization/css-theme-variables/overview/)\n *  - Zero-runtime engine\n *\n * Tips: Use an array over object spread and place `theme.applyStyles()` last.\n *\n * With the styled function:\n * ✅ [{ background: '#e5e5e5' }, theme.applyStyles('dark', { background: '#1c1c1c' })]\n * 🚫 { background: '#e5e5e5', ...theme.applyStyles('dark', { background: '#1c1c1c' })}\n *\n * With the sx prop:\n * ✅ [{ background: '#e5e5e5' }, theme => theme.applyStyles('dark', { background: '#1c1c1c' })]\n * 🚫 { background: '#e5e5e5', ...theme => theme.applyStyles('dark', { background: '#1c1c1c' })}\n *\n * @example\n * 1. using with `styled`:\n * ```jsx\n *   const Component = styled('div')(({ theme }) => [\n *     { background: '#e5e5e5' },\n *     theme.applyStyles('dark', {\n *       background: '#1c1c1c',\n *       color: '#fff',\n *     }),\n *   ]);\n * ```\n *\n * @example\n * 2. using with `sx` prop:\n * ```jsx\n *   <Box sx={[\n *     { background: '#e5e5e5' },\n *     theme => theme.applyStyles('dark', {\n *        background: '#1c1c1c',\n *        color: '#fff',\n *      }),\n *     ]}\n *   />\n * ```\n *\n * @example\n * 3. theming a component:\n * ```jsx\n *   extendTheme({\n *     components: {\n *       MuiButton: {\n *         styleOverrides: {\n *           root: ({ theme }) => [\n *             { background: '#e5e5e5' },\n *             theme.applyStyles('dark', {\n *               background: '#1c1c1c',\n *               color: '#fff',\n *             }),\n *           ],\n *         },\n *       }\n *     }\n *   })\n *```\n */\nexport default function applyStyles(key, styles) {\n  // @ts-expect-error this is 'any' type\n  const theme = this;\n  if (theme.vars) {\n    if (!theme.colorSchemes?.[key] || typeof theme.getColorSchemeSelector !== 'function') {\n      return {};\n    }\n    // If CssVarsProvider is used as a provider, returns '*:where({selector}) &'\n    let selector = theme.getColorSchemeSelector(key);\n    if (selector === '&') {\n      return styles;\n    }\n    if (selector.includes('data-') || selector.includes('.')) {\n      // '*' is required as a workaround for Emotion issue (https://github.com/emotion-js/emotion/issues/2836)\n      selector = `*:where(${selector.replace(/\\s*&$/, '')}) &`;\n    }\n    return {\n      [selector]: styles\n    };\n  }\n  if (theme.palette.mode === key) {\n    return styles;\n  }\n  return {};\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,WAAWA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC/C;EACA,MAAMC,KAAK,GAAG,IAAI;EAClB,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,IAAI,CAACD,KAAK,CAACE,YAAY,GAAGJ,GAAG,CAAC,IAAI,OAAOE,KAAK,CAACG,sBAAsB,KAAK,UAAU,EAAE;MACpF,OAAO,CAAC,CAAC;IACX;IACA;IACA,IAAIC,QAAQ,GAAGJ,KAAK,CAACG,sBAAsB,CAACL,GAAG,CAAC;IAChD,IAAIM,QAAQ,KAAK,GAAG,EAAE;MACpB,OAAOL,MAAM;IACf;IACA,IAAIK,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MACxD;MACAD,QAAQ,GAAG,WAAWA,QAAQ,CAACE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK;IAC1D;IACA,OAAO;MACL,CAACF,QAAQ,GAAGL;IACd,CAAC;EACH;EACA,IAAIC,KAAK,CAACO,OAAO,CAACC,IAAI,KAAKV,GAAG,EAAE;IAC9B,OAAOC,MAAM;EACf;EACA,OAAO,CAAC,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}