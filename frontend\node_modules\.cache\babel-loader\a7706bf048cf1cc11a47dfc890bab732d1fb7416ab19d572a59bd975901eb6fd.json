{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getMasonryUtilityClass(slot) {\n  return generateUtilityClass('MuiMasonry', slot);\n}\nconst masonryClasses = generateUtilityClasses('MuiMasonry', ['root']);\nexport default masonryClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMasonryUtilityClass", "slot", "masonryClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/Masonry/masonryClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getMasonryUtilityClass(slot) {\n  return generateUtilityClass('MuiMasonry', slot);\n}\nconst masonryClasses = generateUtilityClasses('MuiMasonry', ['root']);\nexport default masonryClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOH,oBAAoB,CAAC,YAAY,EAAEG,IAAI,CAAC;AACjD;AACA,MAAMC,cAAc,GAAGH,sBAAsB,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC;AACrE,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}