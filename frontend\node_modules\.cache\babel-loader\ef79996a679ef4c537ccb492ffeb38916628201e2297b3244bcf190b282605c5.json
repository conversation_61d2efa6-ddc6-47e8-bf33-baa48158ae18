{"ast": null, "code": "/**\n * Workaround for React.StrictMode double execution issues\n * This helps prevent navigation interference caused by double effect execution\n */let isStrictModeDetected=false;let effectExecutionCount=0;/**\n * Detect if we're running in React.StrictMode\n */export const detectStrictMode=()=>{effectExecutionCount++;// In StrictMode, effects run twice in development\nif(effectExecutionCount>1&&process.env.NODE_ENV==='development'){isStrictModeDetected=true;console.log('🔍 React.StrictMode detected - implementing workarounds');}return isStrictModeDetected;};/**\n * Execute a function only once, even in StrictMode\n */export const executeOnce=(fn,key)=>{const executionKey=`__executed_${key}`;if(!window[executionKey]){window[executionKey]=true;fn();// Clean up the flag after a delay to allow for re-execution if needed\nsetTimeout(()=>{delete window[executionKey];},1000);}};/**\n * Debounced execution that handles StrictMode double calls\n */export const debouncedExecuteOnce=function(fn,key){let delay=arguments.length>2&&arguments[2]!==undefined?arguments[2]:100;const timeoutKey=`__timeout_${key}`;// Clear any existing timeout\nif(window[timeoutKey]){clearTimeout(window[timeoutKey]);}// Set new timeout\nwindow[timeoutKey]=setTimeout(()=>{executeOnce(fn,key);delete window[timeoutKey];},delay);};/**\n * Safe navigation that handles StrictMode issues\n */export const strictModeSafeNavigation=(navigateFn,path)=>{const navigationKey=`nav_${path}_${Date.now()}`;if(isStrictModeDetected){// In StrictMode, use debounced execution\ndebouncedExecuteOnce(()=>{console.log('🧭 StrictMode safe navigation to:',path);navigateFn(path);},navigationKey,50);}else{// Normal execution\nnavigateFn(path);}};/**\n * Clean up any StrictMode workaround artifacts\n */export const cleanupStrictModeWorkarounds=()=>{// Clean up any execution flags\nObject.keys(window).forEach(key=>{if(key.startsWith('__executed_')||key.startsWith('__timeout_')){delete window[key];}});console.log('🧹 StrictMode workaround artifacts cleaned up');};export default{detectStrictMode,executeOnce,debouncedExecuteOnce,strictModeSafeNavigation,cleanupStrictModeWorkarounds};", "map": {"version": 3, "names": ["isStrictModeDetected", "effectExecutionCount", "detectStrictMode", "process", "env", "NODE_ENV", "console", "log", "executeOnce", "fn", "key", "<PERSON><PERSON><PERSON>", "window", "setTimeout", "debouncedExecuteOnce", "delay", "arguments", "length", "undefined", "timeout<PERSON><PERSON>", "clearTimeout", "strictModeSafeNavigation", "navigateFn", "path", "navigationKey", "Date", "now", "cleanupStrictModeWorkarounds", "Object", "keys", "for<PERSON>ach", "startsWith"], "sources": ["C:/laragon/www/frontend/src/utils/strictModeWorkaround.ts"], "sourcesContent": ["/**\n * Workaround for React.StrictMode double execution issues\n * This helps prevent navigation interference caused by double effect execution\n */\n\nlet isStrictModeDetected = false;\nlet effectExecutionCount = 0;\n\n/**\n * Detect if we're running in React.StrictMode\n */\nexport const detectStrictMode = () => {\n  effectExecutionCount++;\n  \n  // In StrictMode, effects run twice in development\n  if (effectExecutionCount > 1 && process.env.NODE_ENV === 'development') {\n    isStrictModeDetected = true;\n    console.log('🔍 React.StrictMode detected - implementing workarounds');\n  }\n  \n  return isStrictModeDetected;\n};\n\n/**\n * Execute a function only once, even in StrictMode\n */\nexport const executeOnce = (fn: () => void, key: string) => {\n  const executionKey = `__executed_${key}`;\n  \n  if (!(window as any)[executionKey]) {\n    (window as any)[executionKey] = true;\n    fn();\n    \n    // Clean up the flag after a delay to allow for re-execution if needed\n    setTimeout(() => {\n      delete (window as any)[executionKey];\n    }, 1000);\n  }\n};\n\n/**\n * Debounced execution that handles StrictMode double calls\n */\nexport const debouncedExecuteOnce = (fn: () => void, key: string, delay: number = 100) => {\n  const timeoutKey = `__timeout_${key}`;\n  \n  // Clear any existing timeout\n  if ((window as any)[timeoutKey]) {\n    clearTimeout((window as any)[timeoutKey]);\n  }\n  \n  // Set new timeout\n  (window as any)[timeoutKey] = setTimeout(() => {\n    executeOnce(fn, key);\n    delete (window as any)[timeoutKey];\n  }, delay);\n};\n\n/**\n * Safe navigation that handles StrictMode issues\n */\nexport const strictModeSafeNavigation = (navigateFn: (path: string) => void, path: string) => {\n  const navigationKey = `nav_${path}_${Date.now()}`;\n  \n  if (isStrictModeDetected) {\n    // In StrictMode, use debounced execution\n    debouncedExecuteOnce(() => {\n      console.log('🧭 StrictMode safe navigation to:', path);\n      navigateFn(path);\n    }, navigationKey, 50);\n  } else {\n    // Normal execution\n    navigateFn(path);\n  }\n};\n\n/**\n * Clean up any StrictMode workaround artifacts\n */\nexport const cleanupStrictModeWorkarounds = () => {\n  // Clean up any execution flags\n  Object.keys(window).forEach(key => {\n    if (key.startsWith('__executed_') || key.startsWith('__timeout_')) {\n      delete (window as any)[key];\n    }\n  });\n  \n  console.log('🧹 StrictMode workaround artifacts cleaned up');\n};\n\nexport default {\n  detectStrictMode,\n  executeOnce,\n  debouncedExecuteOnce,\n  strictModeSafeNavigation,\n  cleanupStrictModeWorkarounds,\n};\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA,GAAI,CAAAA,oBAAoB,CAAG,KAAK,CAChC,GAAI,CAAAC,oBAAoB,CAAG,CAAC,CAE5B;AACA;AACA,GACA,MAAO,MAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CACpCD,oBAAoB,EAAE,CAEtB;AACA,GAAIA,oBAAoB,CAAG,CAAC,EAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CACtEL,oBAAoB,CAAG,IAAI,CAC3BM,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CACxE,CAEA,MAAO,CAAAP,oBAAoB,CAC7B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAQ,WAAW,CAAGA,CAACC,EAAc,CAAEC,GAAW,GAAK,CAC1D,KAAM,CAAAC,YAAY,CAAG,cAAcD,GAAG,EAAE,CAExC,GAAI,CAAEE,MAAM,CAASD,YAAY,CAAC,CAAE,CACjCC,MAAM,CAASD,YAAY,CAAC,CAAG,IAAI,CACpCF,EAAE,CAAC,CAAC,CAEJ;AACAI,UAAU,CAAC,IAAM,CACf,MAAQ,CAAAD,MAAM,CAASD,YAAY,CAAC,CACtC,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAG,oBAAoB,CAAG,QAAAA,CAACL,EAAc,CAAEC,GAAW,CAA0B,IAAxB,CAAAK,KAAa,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,GAAG,CACnF,KAAM,CAAAG,UAAU,CAAG,aAAaT,GAAG,EAAE,CAErC;AACA,GAAKE,MAAM,CAASO,UAAU,CAAC,CAAE,CAC/BC,YAAY,CAAER,MAAM,CAASO,UAAU,CAAC,CAAC,CAC3C,CAEA;AACCP,MAAM,CAASO,UAAU,CAAC,CAAGN,UAAU,CAAC,IAAM,CAC7CL,WAAW,CAACC,EAAE,CAAEC,GAAG,CAAC,CACpB,MAAQ,CAAAE,MAAM,CAASO,UAAU,CAAC,CACpC,CAAC,CAAEJ,KAAK,CAAC,CACX,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAM,wBAAwB,CAAGA,CAACC,UAAkC,CAAEC,IAAY,GAAK,CAC5F,KAAM,CAAAC,aAAa,CAAG,OAAOD,IAAI,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAEjD,GAAI1B,oBAAoB,CAAE,CACxB;AACAc,oBAAoB,CAAC,IAAM,CACzBR,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAEgB,IAAI,CAAC,CACtDD,UAAU,CAACC,IAAI,CAAC,CAClB,CAAC,CAAEC,aAAa,CAAE,EAAE,CAAC,CACvB,CAAC,IAAM,CACL;AACAF,UAAU,CAACC,IAAI,CAAC,CAClB,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAI,4BAA4B,CAAGA,CAAA,GAAM,CAChD;AACAC,MAAM,CAACC,IAAI,CAACjB,MAAM,CAAC,CAACkB,OAAO,CAACpB,GAAG,EAAI,CACjC,GAAIA,GAAG,CAACqB,UAAU,CAAC,aAAa,CAAC,EAAIrB,GAAG,CAACqB,UAAU,CAAC,YAAY,CAAC,CAAE,CACjE,MAAQ,CAAAnB,MAAM,CAASF,GAAG,CAAC,CAC7B,CACF,CAAC,CAAC,CAEFJ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAC9D,CAAC,CAED,cAAe,CACbL,gBAAgB,CAChBM,WAAW,CACXM,oBAAoB,CACpBO,wBAAwB,CACxBM,4BACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}