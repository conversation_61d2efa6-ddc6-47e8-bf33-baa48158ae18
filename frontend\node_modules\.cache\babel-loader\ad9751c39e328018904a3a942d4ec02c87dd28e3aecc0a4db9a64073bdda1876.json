{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\cms\\\\EditPageButton.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON>, Tooltip, OverlayTrigger } from 'react-bootstrap';\nimport { Edit } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../../services/authService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EditPageButton = ({\n  pageId,\n  className = '',\n  variant = 'floating',\n  size = 'sm'\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [navigating, setNavigating] = useState(false);\n  useEffect(() => {\n    checkAdminStatus();\n  }, []);\n  const checkAdminStatus = async () => {\n    try {\n      setLoading(true);\n      const adminStatus = await authService.checkAdminStatus();\n      setIsAdmin(adminStatus.is_admin);\n    } catch (error) {\n      console.error('Failed to check admin status:', error);\n      setIsAdmin(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEditClick = async () => {\n    try {\n      setNavigating(true);\n      // Navigate to the frontend Puck editor\n      navigate(`/edit/${pageId}`);\n    } catch (error) {\n      console.error('Failed to navigate to editor:', error);\n    } finally {\n      setNavigating(false);\n    }\n  };\n\n  // Don't render if loading or not admin\n  if (loading || !isAdmin) {\n    return null;\n  }\n  const buttonContent = /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [navigating ? /*#__PURE__*/_jsxDEV(Spinner, {\n      as: \"span\",\n      animation: \"border\",\n      size: \"sm\",\n      role: \"status\",\n      \"aria-hidden\": \"true\",\n      className: \"me-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Edit, {\n      className: \"me-2\",\n      style: {\n        fontSize: '16px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }, this), navigating ? 'Opening...' : 'Edit Page']\n  }, void 0, true);\n  const button = /*#__PURE__*/_jsxDEV(Button, {\n    variant: \"primary\",\n    size: size,\n    onClick: handleEditClick,\n    disabled: navigating,\n    className: `edit-page-button ${variant === 'floating' ? 'edit-page-floating' : ''} ${className}`,\n    style: variant === 'floating' ? floatingButtonStyle : {},\n    children: buttonContent\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n  const tooltip = /*#__PURE__*/_jsxDEV(Tooltip, {\n    id: `edit-page-tooltip-${pageId}`,\n    children: \"Edit this page with the visual editor\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n    placement: \"left\",\n    delay: {\n      show: 250,\n      hide: 400\n    },\n    overlay: tooltip,\n    children: button\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n\n// Floating button styles\n_s(EditPageButton, \"Rjc1qrgKA7lxf9hxMN8BggXLTMs=\", false, function () {\n  return [useNavigate];\n});\n_c = EditPageButton;\nconst floatingButtonStyle = {\n  position: 'fixed',\n  bottom: '20px',\n  right: '20px',\n  zIndex: 1000,\n  borderRadius: '50px',\n  padding: '12px 20px',\n  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n  border: 'none',\n  fontWeight: 600,\n  fontSize: '14px',\n  transition: 'all 0.3s ease',\n  background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)'\n};\n\n// CSS for additional styling\nconst editPageButtonStyles = `\n  .edit-page-floating:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;\n  }\n\n  .edit-page-floating:active {\n    transform: translateY(0);\n  }\n\n  .edit-page-button {\n    font-weight: 600;\n    letter-spacing: 0.5px;\n  }\n\n  .edit-page-button:disabled {\n    opacity: 0.7;\n    cursor: not-allowed;\n  }\n\n  @media (max-width: 768px) {\n    .edit-page-floating {\n      bottom: 15px !important;\n      right: 15px !important;\n      padding: 10px 16px !important;\n      font-size: 13px !important;\n    }\n  }\n\n  @media (max-width: 480px) {\n    .edit-page-floating {\n      bottom: 10px !important;\n      right: 10px !important;\n      padding: 8px 12px !important;\n      font-size: 12px !important;\n    }\n    \n    .edit-page-floating .me-2 {\n      margin-right: 0.25rem !important;\n    }\n  }\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleId = 'edit-page-button-styles';\n  if (!document.getElementById(styleId)) {\n    const style = document.createElement('style');\n    style.id = styleId;\n    style.textContent = editPageButtonStyles;\n    document.head.appendChild(style);\n  }\n}\nexport default EditPageButton;\nvar _c;\n$RefreshReg$(_c, \"EditPageButton\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Spinner", "<PERSON><PERSON><PERSON>", "OverlayTrigger", "Edit", "useNavigate", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditPageButton", "pageId", "className", "variant", "size", "_s", "navigate", "isAdmin", "setIsAdmin", "loading", "setLoading", "navigating", "setNavigating", "checkAdminStatus", "adminStatus", "is_admin", "error", "console", "handleEditClick", "buttonContent", "children", "as", "animation", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "button", "onClick", "disabled", "floatingButtonStyle", "tooltip", "id", "placement", "delay", "show", "hide", "overlay", "_c", "position", "bottom", "right", "zIndex", "borderRadius", "padding", "boxShadow", "border", "fontWeight", "transition", "background", "editPageButtonStyles", "document", "styleId", "getElementById", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/cms/EditPageButton.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, OverlayTrigger } from 'react-bootstrap';\nimport { Edit } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../../services/authService';\n\ninterface EditPageButtonProps {\n  pageId: number;\n  className?: string;\n  variant?: 'floating' | 'inline';\n  size?: 'sm' | 'lg';\n}\n\nconst EditPageButton: React.FC<EditPageButtonProps> = ({\n  pageId,\n  className = '',\n  variant = 'floating',\n  size = 'sm'\n}) => {\n  const navigate = useNavigate();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [navigating, setNavigating] = useState(false);\n\n  useEffect(() => {\n    checkAdminStatus();\n  }, []);\n\n  const checkAdminStatus = async () => {\n    try {\n      setLoading(true);\n      const adminStatus = await authService.checkAdminStatus();\n      setIsAdmin(adminStatus.is_admin);\n    } catch (error) {\n      console.error('Failed to check admin status:', error);\n      setIsAdmin(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEditClick = async () => {\n    try {\n      setNavigating(true);\n      // Navigate to the frontend Puck editor\n      navigate(`/edit/${pageId}`);\n    } catch (error) {\n      console.error('Failed to navigate to editor:', error);\n    } finally {\n      setNavigating(false);\n    }\n  };\n\n  // Don't render if loading or not admin\n  if (loading || !isAdmin) {\n    return null;\n  }\n\n  const buttonContent = (\n    <>\n      {navigating ? (\n        <Spinner\n          as=\"span\"\n          animation=\"border\"\n          size=\"sm\"\n          role=\"status\"\n          aria-hidden=\"true\"\n          className=\"me-2\"\n        />\n      ) : (\n        <Edit className=\"me-2\" style={{ fontSize: '16px' }} />\n      )}\n      {navigating ? 'Opening...' : 'Edit Page'}\n    </>\n  );\n\n  const button = (\n    <Button\n      variant=\"primary\"\n      size={size}\n      onClick={handleEditClick}\n      disabled={navigating}\n      className={`edit-page-button ${variant === 'floating' ? 'edit-page-floating' : ''} ${className}`}\n      style={variant === 'floating' ? floatingButtonStyle : {}}\n    >\n      {buttonContent}\n    </Button>\n  );\n\n  const tooltip = (\n    <Tooltip id={`edit-page-tooltip-${pageId}`}>\n      Edit this page with the visual editor\n    </Tooltip>\n  );\n\n  return (\n    <OverlayTrigger\n      placement=\"left\"\n      delay={{ show: 250, hide: 400 }}\n      overlay={tooltip}\n    >\n      {button}\n    </OverlayTrigger>\n  );\n};\n\n// Floating button styles\nconst floatingButtonStyle: React.CSSProperties = {\n  position: 'fixed',\n  bottom: '20px',\n  right: '20px',\n  zIndex: 1000,\n  borderRadius: '50px',\n  padding: '12px 20px',\n  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n  border: 'none',\n  fontWeight: 600,\n  fontSize: '14px',\n  transition: 'all 0.3s ease',\n  background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',\n};\n\n// CSS for additional styling\nconst editPageButtonStyles = `\n  .edit-page-floating:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;\n  }\n\n  .edit-page-floating:active {\n    transform: translateY(0);\n  }\n\n  .edit-page-button {\n    font-weight: 600;\n    letter-spacing: 0.5px;\n  }\n\n  .edit-page-button:disabled {\n    opacity: 0.7;\n    cursor: not-allowed;\n  }\n\n  @media (max-width: 768px) {\n    .edit-page-floating {\n      bottom: 15px !important;\n      right: 15px !important;\n      padding: 10px 16px !important;\n      font-size: 13px !important;\n    }\n  }\n\n  @media (max-width: 480px) {\n    .edit-page-floating {\n      bottom: 10px !important;\n      right: 10px !important;\n      padding: 8px 12px !important;\n      font-size: 12px !important;\n    }\n    \n    .edit-page-floating .me-2 {\n      margin-right: 0.25rem !important;\n    }\n  }\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleId = 'edit-page-button-styles';\n  if (!document.getElementById(styleId)) {\n    const style = document.createElement('style');\n    style.id = styleId;\n    style.textContent = editPageButtonStyles;\n    document.head.appendChild(style);\n  }\n}\n\nexport default EditPageButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,QAAQ,iBAAiB;AAC1E,SAASC,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASrD,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,MAAM;EACNC,SAAS,GAAG,EAAE;EACdC,OAAO,GAAG,UAAU;EACpBC,IAAI,GAAG;AACT,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdyB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,WAAW,GAAG,MAAMnB,WAAW,CAACkB,gBAAgB,CAAC,CAAC;MACxDL,UAAU,CAACM,WAAW,CAACC,QAAQ,CAAC;IAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDR,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFN,aAAa,CAAC,IAAI,CAAC;MACnB;MACAN,QAAQ,CAAC,SAASL,MAAM,EAAE,CAAC;IAC7B,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRJ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,IAAIH,OAAO,IAAI,CAACF,OAAO,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,MAAMY,aAAa,gBACjBtB,OAAA,CAAAE,SAAA;IAAAqB,QAAA,GACGT,UAAU,gBACTd,OAAA,CAACP,OAAO;MACN+B,EAAE,EAAC,MAAM;MACTC,SAAS,EAAC,QAAQ;MAClBlB,IAAI,EAAC,IAAI;MACTmB,IAAI,EAAC,QAAQ;MACb,eAAY,MAAM;MAClBrB,SAAS,EAAC;IAAM;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAEF9B,OAAA,CAACJ,IAAI;MAACS,SAAS,EAAC,MAAM;MAAC0B,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACtD,EACAhB,UAAU,GAAG,YAAY,GAAG,WAAW;EAAA,eACxC,CACH;EAED,MAAMmB,MAAM,gBACVjC,OAAA,CAACR,MAAM;IACLc,OAAO,EAAC,SAAS;IACjBC,IAAI,EAAEA,IAAK;IACX2B,OAAO,EAAEb,eAAgB;IACzBc,QAAQ,EAAErB,UAAW;IACrBT,SAAS,EAAE,oBAAoBC,OAAO,KAAK,UAAU,GAAG,oBAAoB,GAAG,EAAE,IAAID,SAAS,EAAG;IACjG0B,KAAK,EAAEzB,OAAO,KAAK,UAAU,GAAG8B,mBAAmB,GAAG,CAAC,CAAE;IAAAb,QAAA,EAExDD;EAAa;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACT;EAED,MAAMO,OAAO,gBACXrC,OAAA,CAACN,OAAO;IAAC4C,EAAE,EAAE,qBAAqBlC,MAAM,EAAG;IAAAmB,QAAA,EAAC;EAE5C;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAS,CACV;EAED,oBACE9B,OAAA,CAACL,cAAc;IACb4C,SAAS,EAAC,MAAM;IAChBC,KAAK,EAAE;MAAEC,IAAI,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAI,CAAE;IAChCC,OAAO,EAAEN,OAAQ;IAAAd,QAAA,EAEhBU;EAAM;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAErB,CAAC;;AAED;AAAAtB,EAAA,CA7FML,cAA6C;EAAA,QAMhCN,WAAW;AAAA;AAAA+C,EAAA,GANxBzC,cAA6C;AA8FnD,MAAMiC,mBAAwC,GAAG;EAC/CS,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,IAAI;EACZC,YAAY,EAAE,MAAM;EACpBC,OAAO,EAAE,WAAW;EACpBC,SAAS,EAAE,gCAAgC;EAC3CC,MAAM,EAAE,MAAM;EACdC,UAAU,EAAE,GAAG;EACfrB,QAAQ,EAAE,MAAM;EAChBsB,UAAU,EAAE,eAAe;EAC3BC,UAAU,EAAE;AACd,CAAC;;AAED;AACA,MAAMC,oBAAoB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,OAAO,GAAG,yBAAyB;EACzC,IAAI,CAACD,QAAQ,CAACE,cAAc,CAACD,OAAO,CAAC,EAAE;IACrC,MAAM3B,KAAK,GAAG0B,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;IAC7C7B,KAAK,CAACO,EAAE,GAAGoB,OAAO;IAClB3B,KAAK,CAAC8B,WAAW,GAAGL,oBAAoB;IACxCC,QAAQ,CAACK,IAAI,CAACC,WAAW,CAAChC,KAAK,CAAC;EAClC;AACF;AAEA,eAAe5B,cAAc;AAAC,IAAAyC,EAAA;AAAAoB,YAAA,CAAApB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}