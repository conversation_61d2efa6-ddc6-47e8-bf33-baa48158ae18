{"ast": null, "code": "import listen from 'dom-helpers/listen';\nimport ownerDocument from 'dom-helpers/ownerDocument';\nimport { useEffect } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useClickOutside, { getRefTarget } from './useClickOutside';\nimport { isEscKey } from './utils';\nconst noop = () => {};\n/**\n * The `useRootClose` hook registers your callback on the document\n * when rendered. Powers the `<Overlay/>` component. This is used achieve modal\n * style behavior where your callback is triggered when the user tries to\n * interact with the rest of the document or hits the `esc` key.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onRootClose\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useRootClose(ref, onRootClose, {\n  disabled,\n  clickTrigger\n} = {}) {\n  const onClose = onRootClose || noop;\n  useClickOutside(ref, onClose, {\n    disabled,\n    clickTrigger\n  });\n  const handleKeyUp = useEventCallback(e => {\n    if (isEscKey(e)) {\n      onClose(e);\n    }\n  });\n  useEffect(() => {\n    if (disabled || ref == null) return undefined;\n    const doc = ownerDocument(getRefTarget(ref));\n\n    // Store the current event to avoid triggering handlers immediately\n    // https://github.com/facebook/react/issues/20074\n    let currentEvent = (doc.defaultView || window).event;\n    const removeKeyupListener = listen(doc, 'keyup', e => {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n      handleKeyUp(e);\n    });\n    return () => {\n      removeKeyupListener();\n    };\n  }, [ref, disabled, handleKeyUp]);\n}\nexport default useRootClose;", "map": {"version": 3, "names": ["listen", "ownerDocument", "useEffect", "useEventCallback", "useClickOutside", "getRefTarget", "isEscKey", "noop", "useRootClose", "ref", "onRootClose", "disabled", "clickTrigger", "onClose", "handleKeyUp", "e", "undefined", "doc", "currentEvent", "defaultView", "window", "event", "removeKeyupListener"], "sources": ["C:/laragon/www/frontend/node_modules/@restart/ui/esm/useRootClose.js"], "sourcesContent": ["import listen from 'dom-helpers/listen';\nimport ownerDocument from 'dom-helpers/ownerDocument';\nimport { useEffect } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useClickOutside, { getRefTarget } from './useClickOutside';\nimport { isEscKey } from './utils';\nconst noop = () => {};\n/**\n * The `useRootClose` hook registers your callback on the document\n * when rendered. Powers the `<Overlay/>` component. This is used achieve modal\n * style behavior where your callback is triggered when the user tries to\n * interact with the rest of the document or hits the `esc` key.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onRootClose\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useRootClose(ref, onRootClose, {\n  disabled,\n  clickTrigger\n} = {}) {\n  const onClose = onRootClose || noop;\n  useClickOutside(ref, onClose, {\n    disabled,\n    clickTrigger\n  });\n  const handleKeyUp = useEventCallback(e => {\n    if (isEscKey(e)) {\n      onClose(e);\n    }\n  });\n  useEffect(() => {\n    if (disabled || ref == null) return undefined;\n    const doc = ownerDocument(getRefTarget(ref));\n\n    // Store the current event to avoid triggering handlers immediately\n    // https://github.com/facebook/react/issues/20074\n    let currentEvent = (doc.defaultView || window).event;\n    const removeKeyupListener = listen(doc, 'keyup', e => {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n      handleKeyUp(e);\n    });\n    return () => {\n      removeKeyupListener();\n    };\n  }, [ref, disabled, handleKeyUp]);\n}\nexport default useRootClose;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,IAAIC,YAAY,QAAQ,mBAAmB;AACjE,SAASC,QAAQ,QAAQ,SAAS;AAClC,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,GAAG,EAAEC,WAAW,EAAE;EACtCC,QAAQ;EACRC;AACF,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,MAAMC,OAAO,GAAGH,WAAW,IAAIH,IAAI;EACnCH,eAAe,CAACK,GAAG,EAAEI,OAAO,EAAE;IAC5BF,QAAQ;IACRC;EACF,CAAC,CAAC;EACF,MAAME,WAAW,GAAGX,gBAAgB,CAACY,CAAC,IAAI;IACxC,IAAIT,QAAQ,CAACS,CAAC,CAAC,EAAE;MACfF,OAAO,CAACE,CAAC,CAAC;IACZ;EACF,CAAC,CAAC;EACFb,SAAS,CAAC,MAAM;IACd,IAAIS,QAAQ,IAAIF,GAAG,IAAI,IAAI,EAAE,OAAOO,SAAS;IAC7C,MAAMC,GAAG,GAAGhB,aAAa,CAACI,YAAY,CAACI,GAAG,CAAC,CAAC;;IAE5C;IACA;IACA,IAAIS,YAAY,GAAG,CAACD,GAAG,CAACE,WAAW,IAAIC,MAAM,EAAEC,KAAK;IACpD,MAAMC,mBAAmB,GAAGtB,MAAM,CAACiB,GAAG,EAAE,OAAO,EAAEF,CAAC,IAAI;MACpD;MACA,IAAIA,CAAC,KAAKG,YAAY,EAAE;QACtBA,YAAY,GAAGF,SAAS;QACxB;MACF;MACAF,WAAW,CAACC,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,OAAO,MAAM;MACXO,mBAAmB,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACb,GAAG,EAAEE,QAAQ,EAAEG,WAAW,CAAC,CAAC;AAClC;AACA,eAAeN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}