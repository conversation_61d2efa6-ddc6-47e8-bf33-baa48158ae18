{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\seo\\\\SEOHead.tsx\";\nimport React from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SEOHead = ({\n  title,\n  description = '',\n  keywords,\n  robots = 'index, follow',\n  canonicalUrl,\n  ogTitle,\n  ogDescription,\n  ogImage,\n  ogType = 'website',\n  twitterCard = 'summary_large_image',\n  author,\n  publishedTime,\n  modifiedTime,\n  siteName = 'Your Site Name',\n  locale = 'en_US',\n  additionalMeta = [],\n  structuredData\n}) => {\n  const baseUrl = process.env.REACT_APP_FRONTEND_URL || 'http://localhost:3000';\n  const currentUrl = canonicalUrl || `${baseUrl}${window.location.pathname}`;\n\n  // Use provided values or fallback to title/description\n  const effectiveOgTitle = ogTitle || title;\n  const effectiveOgDescription = ogDescription || description;\n  return /*#__PURE__*/_jsxDEV(Helmet, {\n    children: [/*#__PURE__*/_jsxDEV(\"title\", {\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), description && /*#__PURE__*/_jsxDEV(\"meta\", {\n      name: \"description\",\n      content: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 23\n    }, this), keywords && /*#__PURE__*/_jsxDEV(\"meta\", {\n      name: \"keywords\",\n      content: keywords\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 20\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      name: \"robots\",\n      content: robots\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), author && /*#__PURE__*/_jsxDEV(\"meta\", {\n      name: \"author\",\n      content: author\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 18\n    }, this), canonicalUrl && /*#__PURE__*/_jsxDEV(\"link\", {\n      rel: \"canonical\",\n      href: canonicalUrl\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 24\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"og:title\",\n      content: effectiveOgTitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"og:description\",\n      content: effectiveOgDescription\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"og:url\",\n      content: currentUrl\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"og:type\",\n      content: ogType\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"og:site_name\",\n      content: siteName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"og:locale\",\n      content: locale\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), ogImage && /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"og:image\",\n      content: ogImage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 19\n    }, this), ogImage && /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"og:image:alt\",\n      content: effectiveOgTitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      name: \"twitter:card\",\n      content: twitterCard\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      name: \"twitter:title\",\n      content: effectiveOgTitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      name: \"twitter:description\",\n      content: effectiveOgDescription\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), ogImage && /*#__PURE__*/_jsxDEV(\"meta\", {\n      name: \"twitter:image\",\n      content: ogImage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 19\n    }, this), publishedTime && /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"article:published_time\",\n      content: publishedTime\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 25\n    }, this), modifiedTime && /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"article:modified_time\",\n      content: modifiedTime\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 24\n    }, this), author && /*#__PURE__*/_jsxDEV(\"meta\", {\n      property: \"article:author\",\n      content: author\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 18\n    }, this), additionalMeta.map((meta, index) => {\n      if (meta.name) {\n        return /*#__PURE__*/_jsxDEV(\"meta\", {\n          name: meta.name,\n          content: meta.content\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 18\n        }, this);\n      } else if (meta.property) {\n        return /*#__PURE__*/_jsxDEV(\"meta\", {\n          property: meta.property,\n          content: meta.content\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 18\n        }, this);\n      }\n      return null;\n    }), structuredData && /*#__PURE__*/_jsxDEV(\"script\", {\n      type: \"application/ld+json\",\n      children: JSON.stringify(structuredData)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_c = SEOHead;\nexport default SEOHead;\nvar _c;\n$RefreshReg$(_c, \"SEOHead\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SEOHead", "title", "description", "keywords", "robots", "canonicalUrl", "ogTitle", "ogDescription", "ogImage", "ogType", "twitterCard", "author", "publishedTime", "modifiedTime", "siteName", "locale", "additionalMeta", "structuredData", "baseUrl", "process", "env", "REACT_APP_FRONTEND_URL", "currentUrl", "window", "location", "pathname", "effectiveOgTitle", "effectiveOgDescription", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "rel", "href", "property", "map", "meta", "index", "type", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/seo/SEOHead.tsx"], "sourcesContent": ["import React from 'react';\nimport { Helmet } from 'react-helmet-async';\n\ninterface SEOHeadProps {\n  title: string;\n  description?: string;\n  keywords?: string;\n  robots?: string;\n  canonicalUrl?: string;\n  ogTitle?: string;\n  ogDescription?: string;\n  ogImage?: string;\n  ogType?: string;\n  twitterCard?: string;\n  author?: string;\n  publishedTime?: string;\n  modifiedTime?: string;\n  siteName?: string;\n  locale?: string;\n  additionalMeta?: Array<{\n    name?: string;\n    property?: string;\n    content: string;\n  }>;\n  structuredData?: object;\n}\n\nconst SEOHead: React.FC<SEOHeadProps> = ({\n  title,\n  description = '',\n  keywords,\n  robots = 'index, follow',\n  canonicalUrl,\n  ogTitle,\n  ogDescription,\n  ogImage,\n  ogType = 'website',\n  twitterCard = 'summary_large_image',\n  author,\n  publishedTime,\n  modifiedTime,\n  siteName = 'Your Site Name',\n  locale = 'en_US',\n  additionalMeta = [],\n  structuredData,\n}) => {\n  const baseUrl = process.env.REACT_APP_FRONTEND_URL || 'http://localhost:3000';\n  const currentUrl = canonicalUrl || `${baseUrl}${window.location.pathname}`;\n  \n  // Use provided values or fallback to title/description\n  const effectiveOgTitle = ogTitle || title;\n  const effectiveOgDescription = ogDescription || description;\n\n  return (\n    <Helmet>\n      {/* Basic Meta Tags */}\n      <title>{title}</title>\n      {description && <meta name=\"description\" content={description} />}\n      {keywords && <meta name=\"keywords\" content={keywords} />}\n      <meta name=\"robots\" content={robots} />\n      {author && <meta name=\"author\" content={author} />}\n      \n      {/* Canonical URL */}\n      {canonicalUrl && <link rel=\"canonical\" href={canonicalUrl} />}\n      \n      {/* Open Graph Tags */}\n      <meta property=\"og:title\" content={effectiveOgTitle} />\n      <meta property=\"og:description\" content={effectiveOgDescription} />\n      <meta property=\"og:url\" content={currentUrl} />\n      <meta property=\"og:type\" content={ogType} />\n      <meta property=\"og:site_name\" content={siteName} />\n      <meta property=\"og:locale\" content={locale} />\n      {ogImage && <meta property=\"og:image\" content={ogImage} />}\n      {ogImage && <meta property=\"og:image:alt\" content={effectiveOgTitle} />}\n      \n      {/* Twitter Card Tags */}\n      <meta name=\"twitter:card\" content={twitterCard} />\n      <meta name=\"twitter:title\" content={effectiveOgTitle} />\n      <meta name=\"twitter:description\" content={effectiveOgDescription} />\n      {ogImage && <meta name=\"twitter:image\" content={ogImage} />}\n      \n      {/* Article specific tags */}\n      {publishedTime && <meta property=\"article:published_time\" content={publishedTime} />}\n      {modifiedTime && <meta property=\"article:modified_time\" content={modifiedTime} />}\n      {author && <meta property=\"article:author\" content={author} />}\n      \n      {/* Additional Meta Tags */}\n      {additionalMeta.map((meta, index) => {\n        if (meta.name) {\n          return <meta key={index} name={meta.name} content={meta.content} />;\n        } else if (meta.property) {\n          return <meta key={index} property={meta.property} content={meta.content} />;\n        }\n        return null;\n      })}\n      \n      {/* Structured Data */}\n      {structuredData && (\n        <script type=\"application/ld+json\">\n          {JSON.stringify(structuredData)}\n        </script>\n      )}\n    </Helmet>\n  );\n};\n\nexport default SEOHead;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0B5C,MAAMC,OAA+B,GAAGA,CAAC;EACvCC,KAAK;EACLC,WAAW,GAAG,EAAE;EAChBC,QAAQ;EACRC,MAAM,GAAG,eAAe;EACxBC,YAAY;EACZC,OAAO;EACPC,aAAa;EACbC,OAAO;EACPC,MAAM,GAAG,SAAS;EAClBC,WAAW,GAAG,qBAAqB;EACnCC,MAAM;EACNC,aAAa;EACbC,YAAY;EACZC,QAAQ,GAAG,gBAAgB;EAC3BC,MAAM,GAAG,OAAO;EAChBC,cAAc,GAAG,EAAE;EACnBC;AACF,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;EAC7E,MAAMC,UAAU,GAAGjB,YAAY,IAAI,GAAGa,OAAO,GAAGK,MAAM,CAACC,QAAQ,CAACC,QAAQ,EAAE;;EAE1E;EACA,MAAMC,gBAAgB,GAAGpB,OAAO,IAAIL,KAAK;EACzC,MAAM0B,sBAAsB,GAAGpB,aAAa,IAAIL,WAAW;EAE3D,oBACEH,OAAA,CAACF,MAAM;IAAA+B,QAAA,gBAEL7B,OAAA;MAAA6B,QAAA,EAAQ3B;IAAK;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EACrB9B,WAAW,iBAAIH,OAAA;MAAMkC,IAAI,EAAC,aAAa;MAACC,OAAO,EAAEhC;IAAY;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAChE7B,QAAQ,iBAAIJ,OAAA;MAAMkC,IAAI,EAAC,UAAU;MAACC,OAAO,EAAE/B;IAAS;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxDjC,OAAA;MAAMkC,IAAI,EAAC,QAAQ;MAACC,OAAO,EAAE9B;IAAO;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACtCrB,MAAM,iBAAIZ,OAAA;MAAMkC,IAAI,EAAC,QAAQ;MAACC,OAAO,EAAEvB;IAAO;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGjD3B,YAAY,iBAAIN,OAAA;MAAMoC,GAAG,EAAC,WAAW;MAACC,IAAI,EAAE/B;IAAa;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG7DjC,OAAA;MAAMsC,QAAQ,EAAC,UAAU;MAACH,OAAO,EAAER;IAAiB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvDjC,OAAA;MAAMsC,QAAQ,EAAC,gBAAgB;MAACH,OAAO,EAAEP;IAAuB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnEjC,OAAA;MAAMsC,QAAQ,EAAC,QAAQ;MAACH,OAAO,EAAEZ;IAAW;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/CjC,OAAA;MAAMsC,QAAQ,EAAC,SAAS;MAACH,OAAO,EAAEzB;IAAO;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5CjC,OAAA;MAAMsC,QAAQ,EAAC,cAAc;MAACH,OAAO,EAAEpB;IAAS;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnDjC,OAAA;MAAMsC,QAAQ,EAAC,WAAW;MAACH,OAAO,EAAEnB;IAAO;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC7CxB,OAAO,iBAAIT,OAAA;MAAMsC,QAAQ,EAAC,UAAU;MAACH,OAAO,EAAE1B;IAAQ;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACzDxB,OAAO,iBAAIT,OAAA;MAAMsC,QAAQ,EAAC,cAAc;MAACH,OAAO,EAAER;IAAiB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvEjC,OAAA;MAAMkC,IAAI,EAAC,cAAc;MAACC,OAAO,EAAExB;IAAY;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClDjC,OAAA;MAAMkC,IAAI,EAAC,eAAe;MAACC,OAAO,EAAER;IAAiB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxDjC,OAAA;MAAMkC,IAAI,EAAC,qBAAqB;MAACC,OAAO,EAAEP;IAAuB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACnExB,OAAO,iBAAIT,OAAA;MAAMkC,IAAI,EAAC,eAAe;MAACC,OAAO,EAAE1B;IAAQ;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAG1DpB,aAAa,iBAAIb,OAAA;MAAMsC,QAAQ,EAAC,wBAAwB;MAACH,OAAO,EAAEtB;IAAc;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACnFnB,YAAY,iBAAId,OAAA;MAAMsC,QAAQ,EAAC,uBAAuB;MAACH,OAAO,EAAErB;IAAa;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAChFrB,MAAM,iBAAIZ,OAAA;MAAMsC,QAAQ,EAAC,gBAAgB;MAACH,OAAO,EAAEvB;IAAO;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAG7DhB,cAAc,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MACnC,IAAID,IAAI,CAACN,IAAI,EAAE;QACb,oBAAOlC,OAAA;UAAkBkC,IAAI,EAAEM,IAAI,CAACN,IAAK;UAACC,OAAO,EAAEK,IAAI,CAACL;QAAQ,GAA9CM,KAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA2C,CAAC;MACrE,CAAC,MAAM,IAAIO,IAAI,CAACF,QAAQ,EAAE;QACxB,oBAAOtC,OAAA;UAAkBsC,QAAQ,EAAEE,IAAI,CAACF,QAAS;UAACH,OAAO,EAAEK,IAAI,CAACL;QAAQ,GAAtDM,KAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmD,CAAC;MAC7E;MACA,OAAO,IAAI;IACb,CAAC,CAAC,EAGDf,cAAc,iBACblB,OAAA;MAAQ0C,IAAI,EAAC,qBAAqB;MAAAb,QAAA,EAC/Bc,IAAI,CAACC,SAAS,CAAC1B,cAAc;IAAC;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACY,EAAA,GA7EI5C,OAA+B;AA+ErC,eAAeA,OAAO;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}