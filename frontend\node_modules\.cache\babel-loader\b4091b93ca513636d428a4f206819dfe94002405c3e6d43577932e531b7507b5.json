{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormFloating = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    className,\n    bsPrefix,\n    as: Component = 'div',\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-floating');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nFormFloating.displayName = 'FormFloating';\nexport default FormFloating;", "map": {"version": 3, "names": ["React", "classNames", "useBootstrapPrefix", "jsx", "_jsx", "FormFloating", "forwardRef", "_ref", "ref", "className", "bsPrefix", "as", "Component", "props", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FormFloating.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormFloating = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-floating');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nFormFloating.displayName = 'FormFloating';\nexport default FormFloating;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAAC,IAAA,EAKhDC,GAAG,KAAK;EAAA,IALyC;IAClDC,SAAS;IACTC,QAAQ;IACRC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrB,GAAGC;EACL,CAAC,GAAAN,IAAA;EACCG,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,eAAe,CAAC;EACxD,OAAO,aAAaN,IAAI,CAACQ,SAAS,EAAE;IAClCJ,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,YAAY,CAACS,WAAW,GAAG,cAAc;AACzC,eAAeT,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}