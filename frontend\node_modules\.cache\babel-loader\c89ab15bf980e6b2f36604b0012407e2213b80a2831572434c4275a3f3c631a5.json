{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport exactProp from '@mui/utils/exactProp';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\n\n// TODO: return `EventHandlerName extends `on${infer EventName}` ? Lowercase<EventName> : never` once generatePropTypes runs with TS 4.1\nfunction mapEventPropToEvent(eventProp) {\n  return eventProp.substring(2).toLowerCase();\n}\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n/**\n * Listen for click events that occur somewhere in the document, outside of the element itself.\n * For instance, if you need to hide a menu when people click anywhere else on your page.\n *\n * Demos:\n *\n * - [Click-Away Listener](https://mui.com/material-ui/react-click-away-listener/)\n * - [Menu](https://mui.com/material-ui/react-menu/)\n *\n * API:\n *\n * - [ClickAwayListener API](https://mui.com/material-ui/api/click-away-listener/)\n */\nfunction ClickAwayListener(props) {\n  const {\n    children,\n    disableReactTree = false,\n    mouseEvent = 'onClick',\n    onClickAway,\n    touchEvent = 'onTouchEnd'\n  } = props;\n  const movedRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  React.useEffect(() => {\n    // Ensure that this component is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n    setTimeout(() => {\n      activatedRef.current = true;\n    }, 0);\n    return () => {\n      activatedRef.current = false;\n    };\n  }, []);\n  const handleRef = useForkRef(getReactElementRef(children), nodeRef);\n\n  // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n  const handleClickAway = useEventCallback(event => {\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current);\n\n    // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n    if (!activatedRef.current || !nodeRef.current || 'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    }\n\n    // Do not act if user performed touchmove\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n    let insideDOM;\n\n    // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n    if (event.composedPath) {\n      insideDOM = event.composedPath().includes(nodeRef.current);\n    } else {\n      insideDOM = !doc.documentElement.contains(\n      // @ts-expect-error returns `false` as intended when not dispatched from a Node\n      event.target) || nodeRef.current.contains(\n      // @ts-expect-error returns `false` as intended when not dispatched from a Node\n      event.target);\n    }\n    if (!insideDOM && (disableReactTree || !insideReactTree)) {\n      onClickAway(event);\n    }\n  });\n\n  // Keep track of mouse/touch events that bubbled up through the portal.\n  const createHandleSynthetic = handlerName => event => {\n    syntheticEventRef.current = true;\n    const childrenPropsHandler = children.props[handlerName];\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n  const childrenProps = {\n    ref: handleRef\n  };\n  if (touchEvent !== false) {\n    childrenProps[touchEvent] = createHandleSynthetic(touchEvent);\n  }\n  React.useEffect(() => {\n    if (touchEvent !== false) {\n      const mappedTouchEvent = mapEventPropToEvent(touchEvent);\n      const doc = ownerDocument(nodeRef.current);\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n      doc.addEventListener(mappedTouchEvent, handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener(mappedTouchEvent, handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n    return undefined;\n  }, [handleClickAway, touchEvent]);\n  if (mouseEvent !== false) {\n    childrenProps[mouseEvent] = createHandleSynthetic(mouseEvent);\n  }\n  React.useEffect(() => {\n    if (mouseEvent !== false) {\n      const mappedMouseEvent = mapEventPropToEvent(mouseEvent);\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener(mappedMouseEvent, handleClickAway);\n      return () => {\n        doc.removeEventListener(mappedMouseEvent, handleClickAway);\n      };\n    }\n    return undefined;\n  }, [handleClickAway, mouseEvent]);\n  return /*#__PURE__*/React.cloneElement(children, childrenProps);\n}\nprocess.env.NODE_ENV !== \"production\" ? ClickAwayListener.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The wrapped element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * If `true`, the React tree is ignored and only the DOM tree is considered.\n   * This prop changes how portaled elements are handled.\n   * @default false\n   */\n  disableReactTree: PropTypes.bool,\n  /**\n   * The mouse event to listen to. You can disable the listener by providing `false`.\n   * @default 'onClick'\n   */\n  mouseEvent: PropTypes.oneOf(['onClick', 'onMouseDown', 'onMouseUp', 'onPointerDown', 'onPointerUp', false]),\n  /**\n   * Callback fired when a \"click away\" event is detected.\n   */\n  onClickAway: PropTypes.func.isRequired,\n  /**\n   * The touch event to listen to. You can disable the listener by providing `false`.\n   * @default 'onTouchEnd'\n   */\n  touchEvent: PropTypes.oneOf(['onTouchEnd', 'onTouchStart', false])\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  ClickAwayListener['propTypes' + ''] = exactProp(ClickAwayListener.propTypes);\n}\nexport { ClickAwayListener };", "map": {"version": 3, "names": ["React", "PropTypes", "ownerDocument", "useForkRef", "useEventCallback", "elementAcceptingRef", "exactProp", "getReactElementRef", "mapEventPropToEvent", "eventProp", "substring", "toLowerCase", "clickedRootScrollbar", "event", "doc", "documentElement", "clientWidth", "clientX", "clientHeight", "clientY", "ClickAwayListener", "props", "children", "disableReactTree", "mouseEvent", "onClickAway", "touchEvent", "movedRef", "useRef", "nodeRef", "activatedRef", "syntheticEventRef", "useEffect", "setTimeout", "current", "handleRef", "handleClickAway", "insideReactTree", "insideDOM", "<PERSON><PERSON><PERSON>", "includes", "contains", "target", "createHandleSynthetic", "handler<PERSON>ame", "childrenPropsHandler", "childrenProps", "ref", "mappedTouchEvent", "handleTouchMove", "addEventListener", "removeEventListener", "undefined", "mappedMouseEvent", "cloneElement", "process", "env", "NODE_ENV", "propTypes", "isRequired", "bool", "oneOf", "func"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/ClickAwayListener/ClickAwayListener.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport exactProp from '@mui/utils/exactProp';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\n\n// TODO: return `EventHandlerName extends `on${infer EventName}` ? Lowercase<EventName> : never` once generatePropTypes runs with TS 4.1\nfunction mapEventPropToEvent(eventProp) {\n  return eventProp.substring(2).toLowerCase();\n}\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n/**\n * Listen for click events that occur somewhere in the document, outside of the element itself.\n * For instance, if you need to hide a menu when people click anywhere else on your page.\n *\n * Demos:\n *\n * - [Click-Away Listener](https://mui.com/material-ui/react-click-away-listener/)\n * - [Menu](https://mui.com/material-ui/react-menu/)\n *\n * API:\n *\n * - [ClickAwayListener API](https://mui.com/material-ui/api/click-away-listener/)\n */\nfunction ClickAwayListener(props) {\n  const {\n    children,\n    disableReactTree = false,\n    mouseEvent = 'onClick',\n    onClickAway,\n    touchEvent = 'onTouchEnd'\n  } = props;\n  const movedRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  React.useEffect(() => {\n    // Ensure that this component is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n    setTimeout(() => {\n      activatedRef.current = true;\n    }, 0);\n    return () => {\n      activatedRef.current = false;\n    };\n  }, []);\n  const handleRef = useForkRef(getReactElementRef(children), nodeRef);\n\n  // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n  const handleClickAway = useEventCallback(event => {\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current);\n\n    // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n    if (!activatedRef.current || !nodeRef.current || 'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    }\n\n    // Do not act if user performed touchmove\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n    let insideDOM;\n\n    // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n    if (event.composedPath) {\n      insideDOM = event.composedPath().includes(nodeRef.current);\n    } else {\n      insideDOM = !doc.documentElement.contains(\n      // @ts-expect-error returns `false` as intended when not dispatched from a Node\n      event.target) || nodeRef.current.contains(\n      // @ts-expect-error returns `false` as intended when not dispatched from a Node\n      event.target);\n    }\n    if (!insideDOM && (disableReactTree || !insideReactTree)) {\n      onClickAway(event);\n    }\n  });\n\n  // Keep track of mouse/touch events that bubbled up through the portal.\n  const createHandleSynthetic = handlerName => event => {\n    syntheticEventRef.current = true;\n    const childrenPropsHandler = children.props[handlerName];\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n  const childrenProps = {\n    ref: handleRef\n  };\n  if (touchEvent !== false) {\n    childrenProps[touchEvent] = createHandleSynthetic(touchEvent);\n  }\n  React.useEffect(() => {\n    if (touchEvent !== false) {\n      const mappedTouchEvent = mapEventPropToEvent(touchEvent);\n      const doc = ownerDocument(nodeRef.current);\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n      doc.addEventListener(mappedTouchEvent, handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener(mappedTouchEvent, handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n    return undefined;\n  }, [handleClickAway, touchEvent]);\n  if (mouseEvent !== false) {\n    childrenProps[mouseEvent] = createHandleSynthetic(mouseEvent);\n  }\n  React.useEffect(() => {\n    if (mouseEvent !== false) {\n      const mappedMouseEvent = mapEventPropToEvent(mouseEvent);\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener(mappedMouseEvent, handleClickAway);\n      return () => {\n        doc.removeEventListener(mappedMouseEvent, handleClickAway);\n      };\n    }\n    return undefined;\n  }, [handleClickAway, mouseEvent]);\n  return /*#__PURE__*/React.cloneElement(children, childrenProps);\n}\nprocess.env.NODE_ENV !== \"production\" ? ClickAwayListener.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The wrapped element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * If `true`, the React tree is ignored and only the DOM tree is considered.\n   * This prop changes how portaled elements are handled.\n   * @default false\n   */\n  disableReactTree: PropTypes.bool,\n  /**\n   * The mouse event to listen to. You can disable the listener by providing `false`.\n   * @default 'onClick'\n   */\n  mouseEvent: PropTypes.oneOf(['onClick', 'onMouseDown', 'onMouseUp', 'onPointerDown', 'onPointerUp', false]),\n  /**\n   * Callback fired when a \"click away\" event is detected.\n   */\n  onClickAway: PropTypes.func.isRequired,\n  /**\n   * The touch event to listen to. You can disable the listener by providing `false`.\n   * @default 'onTouchEnd'\n   */\n  touchEvent: PropTypes.oneOf(['onTouchEnd', 'onTouchStart', false])\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  ClickAwayListener['propTypes' + ''] = exactProp(ClickAwayListener.propTypes);\n}\nexport { ClickAwayListener };"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,kBAAkB,MAAM,+BAA+B;;AAE9D;AACA,SAASC,mBAAmBA,CAACC,SAAS,EAAE;EACtC,OAAOA,SAAS,CAACC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AAC7C;AACA,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxC,OAAOA,GAAG,CAACC,eAAe,CAACC,WAAW,GAAGH,KAAK,CAACI,OAAO,IAAIH,GAAG,CAACC,eAAe,CAACG,YAAY,GAAGL,KAAK,CAACM,OAAO;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAChC,MAAM;IACJC,QAAQ;IACRC,gBAAgB,GAAG,KAAK;IACxBC,UAAU,GAAG,SAAS;IACtBC,WAAW;IACXC,UAAU,GAAG;EACf,CAAC,GAAGL,KAAK;EACT,MAAMM,QAAQ,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMC,OAAO,GAAG7B,KAAK,CAAC4B,MAAM,CAAC,IAAI,CAAC;EAClC,MAAME,YAAY,GAAG9B,KAAK,CAAC4B,MAAM,CAAC,KAAK,CAAC;EACxC,MAAMG,iBAAiB,GAAG/B,KAAK,CAAC4B,MAAM,CAAC,KAAK,CAAC;EAC7C5B,KAAK,CAACgC,SAAS,CAAC,MAAM;IACpB;IACA;IACAC,UAAU,CAAC,MAAM;MACfH,YAAY,CAACI,OAAO,GAAG,IAAI;IAC7B,CAAC,EAAE,CAAC,CAAC;IACL,OAAO,MAAM;MACXJ,YAAY,CAACI,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,SAAS,GAAGhC,UAAU,CAACI,kBAAkB,CAACe,QAAQ,CAAC,EAAEO,OAAO,CAAC;;EAEnE;EACA;EACA;EACA;EACA;EACA;EACA,MAAMO,eAAe,GAAGhC,gBAAgB,CAACS,KAAK,IAAI;IAChD;IACA;IACA,MAAMwB,eAAe,GAAGN,iBAAiB,CAACG,OAAO;IACjDH,iBAAiB,CAACG,OAAO,GAAG,KAAK;IACjC,MAAMpB,GAAG,GAAGZ,aAAa,CAAC2B,OAAO,CAACK,OAAO,CAAC;;IAE1C;IACA;IACA;IACA,IAAI,CAACJ,YAAY,CAACI,OAAO,IAAI,CAACL,OAAO,CAACK,OAAO,IAAI,SAAS,IAAIrB,KAAK,IAAID,oBAAoB,CAACC,KAAK,EAAEC,GAAG,CAAC,EAAE;MACvG;IACF;;IAEA;IACA,IAAIa,QAAQ,CAACO,OAAO,EAAE;MACpBP,QAAQ,CAACO,OAAO,GAAG,KAAK;MACxB;IACF;IACA,IAAII,SAAS;;IAEb;IACA,IAAIzB,KAAK,CAAC0B,YAAY,EAAE;MACtBD,SAAS,GAAGzB,KAAK,CAAC0B,YAAY,CAAC,CAAC,CAACC,QAAQ,CAACX,OAAO,CAACK,OAAO,CAAC;IAC5D,CAAC,MAAM;MACLI,SAAS,GAAG,CAACxB,GAAG,CAACC,eAAe,CAAC0B,QAAQ;MACzC;MACA5B,KAAK,CAAC6B,MAAM,CAAC,IAAIb,OAAO,CAACK,OAAO,CAACO,QAAQ;MACzC;MACA5B,KAAK,CAAC6B,MAAM,CAAC;IACf;IACA,IAAI,CAACJ,SAAS,KAAKf,gBAAgB,IAAI,CAACc,eAAe,CAAC,EAAE;MACxDZ,WAAW,CAACZ,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;;EAEF;EACA,MAAM8B,qBAAqB,GAAGC,WAAW,IAAI/B,KAAK,IAAI;IACpDkB,iBAAiB,CAACG,OAAO,GAAG,IAAI;IAChC,MAAMW,oBAAoB,GAAGvB,QAAQ,CAACD,KAAK,CAACuB,WAAW,CAAC;IACxD,IAAIC,oBAAoB,EAAE;MACxBA,oBAAoB,CAAChC,KAAK,CAAC;IAC7B;EACF,CAAC;EACD,MAAMiC,aAAa,GAAG;IACpBC,GAAG,EAAEZ;EACP,CAAC;EACD,IAAIT,UAAU,KAAK,KAAK,EAAE;IACxBoB,aAAa,CAACpB,UAAU,CAAC,GAAGiB,qBAAqB,CAACjB,UAAU,CAAC;EAC/D;EACA1B,KAAK,CAACgC,SAAS,CAAC,MAAM;IACpB,IAAIN,UAAU,KAAK,KAAK,EAAE;MACxB,MAAMsB,gBAAgB,GAAGxC,mBAAmB,CAACkB,UAAU,CAAC;MACxD,MAAMZ,GAAG,GAAGZ,aAAa,CAAC2B,OAAO,CAACK,OAAO,CAAC;MAC1C,MAAMe,eAAe,GAAGA,CAAA,KAAM;QAC5BtB,QAAQ,CAACO,OAAO,GAAG,IAAI;MACzB,CAAC;MACDpB,GAAG,CAACoC,gBAAgB,CAACF,gBAAgB,EAAEZ,eAAe,CAAC;MACvDtB,GAAG,CAACoC,gBAAgB,CAAC,WAAW,EAAED,eAAe,CAAC;MAClD,OAAO,MAAM;QACXnC,GAAG,CAACqC,mBAAmB,CAACH,gBAAgB,EAAEZ,eAAe,CAAC;QAC1DtB,GAAG,CAACqC,mBAAmB,CAAC,WAAW,EAAEF,eAAe,CAAC;MACvD,CAAC;IACH;IACA,OAAOG,SAAS;EAClB,CAAC,EAAE,CAAChB,eAAe,EAAEV,UAAU,CAAC,CAAC;EACjC,IAAIF,UAAU,KAAK,KAAK,EAAE;IACxBsB,aAAa,CAACtB,UAAU,CAAC,GAAGmB,qBAAqB,CAACnB,UAAU,CAAC;EAC/D;EACAxB,KAAK,CAACgC,SAAS,CAAC,MAAM;IACpB,IAAIR,UAAU,KAAK,KAAK,EAAE;MACxB,MAAM6B,gBAAgB,GAAG7C,mBAAmB,CAACgB,UAAU,CAAC;MACxD,MAAMV,GAAG,GAAGZ,aAAa,CAAC2B,OAAO,CAACK,OAAO,CAAC;MAC1CpB,GAAG,CAACoC,gBAAgB,CAACG,gBAAgB,EAAEjB,eAAe,CAAC;MACvD,OAAO,MAAM;QACXtB,GAAG,CAACqC,mBAAmB,CAACE,gBAAgB,EAAEjB,eAAe,CAAC;MAC5D,CAAC;IACH;IACA,OAAOgB,SAAS;EAClB,CAAC,EAAE,CAAChB,eAAe,EAAEZ,UAAU,CAAC,CAAC;EACjC,OAAO,aAAaxB,KAAK,CAACsD,YAAY,CAAChC,QAAQ,EAAEwB,aAAa,CAAC;AACjE;AACAS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrC,iBAAiB,CAACsC,SAAS,CAAC,yBAAyB;EAC3F;EACA;EACA;EACA;EACA;AACF;AACA;EACEpC,QAAQ,EAAEjB,mBAAmB,CAACsD,UAAU;EACxC;AACF;AACA;AACA;AACA;EACEpC,gBAAgB,EAAEtB,SAAS,CAAC2D,IAAI;EAChC;AACF;AACA;AACA;EACEpC,UAAU,EAAEvB,SAAS,CAAC4D,KAAK,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;EAC3G;AACF;AACA;EACEpC,WAAW,EAAExB,SAAS,CAAC6D,IAAI,CAACH,UAAU;EACtC;AACF;AACA;AACA;EACEjC,UAAU,EAAEzB,SAAS,CAAC4D,KAAK,CAAC,CAAC,YAAY,EAAE,cAAc,EAAE,KAAK,CAAC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACArC,iBAAiB,CAAC,WAAW,GAAG,EAAE,CAAC,GAAGd,SAAS,CAACc,iBAAiB,CAACsC,SAAS,CAAC;AAC9E;AACA,SAAStC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}