{"ast": null, "code": "export { default } from \"./FormControl.js\";\nexport { default as useFormControl } from \"./useFormControl.js\";\nexport { default as formControlClasses } from \"./formControlClasses.js\";\nexport * from \"./formControlClasses.js\";", "map": {"version": 3, "names": ["default", "useFormControl", "formControlClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/FormControl/index.js"], "sourcesContent": ["export { default } from \"./FormControl.js\";\nexport { default as useFormControl } from \"./useFormControl.js\";\nexport { default as formControlClasses } from \"./formControlClasses.js\";\nexport * from \"./formControlClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,cAAc,QAAQ,qBAAqB;AAC/D,SAASD,OAAO,IAAIE,kBAAkB,QAAQ,yBAAyB;AACvE,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}