{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The ClockPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { ClockPicker } from '@mui/x-date-pickers'`\", \"or `import { ClockPicker } from '@mui/x-date-pickers/ClockPicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The ClockPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst ClockPicker = /*#__PURE__*/React.forwardRef(function DeprecatedClockPicker() {\n  warn();\n  return null;\n});\nexport default ClockPicker;\nexport const clockPickerClasses = {};", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "ClockPicker", "forwardRef", "DeprecatedClockPicker", "clockPickerClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/ClockPicker/ClockPicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The ClockPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { ClockPicker } from '@mui/x-date-pickers'`\", \"or `import { ClockPicker } from '@mui/x-date-pickers/ClockPicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The ClockPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst ClockPicker = /*#__PURE__*/React.forwardRef(function DeprecatedClockPicker() {\n  warn();\n  return null;\n});\nexport default ClockPicker;\nexport const clockPickerClasses = {};"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,oFAAoF,EAAE,EAAE,EAAE,oEAAoE,EAAE,oEAAoE,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1WH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,WAAW,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,qBAAqBA,CAAA,EAAG;EACjFL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,WAAW;AAC1B,OAAO,MAAMG,kBAAkB,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}