{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\ChartExample.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Card, CardContent, Typography, Box, useTheme } from '@mui/material';\n\n// This is a placeholder component showing how to integrate charts\n// To use real charts, install a charting library like:\n// npm install react-chartjs-2 chart.js\n// or\n// npm install recharts\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChartExample = ({\n  title,\n  data,\n  type = 'line',\n  height = 300\n}) => {\n  _s();\n  const theme = useTheme();\n\n  // Mock chart visualization - replace with actual chart library\n  const renderMockChart = () => {\n    const maxValue = Math.max(...data.datasets[0].data);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        height,\n        display: 'flex',\n        alignItems: 'end',\n        gap: 1,\n        p: 2\n      },\n      children: data.labels.map((label, index) => {\n        const value = data.datasets[0].data[index];\n        const barHeight = value / maxValue * (height - 60);\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: '100%',\n              maxWidth: 40,\n              height: barHeight,\n              backgroundColor: theme.palette.primary.main,\n              borderRadius: 1,\n              mb: 1,\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                backgroundColor: theme.palette.primary.dark,\n                transform: 'scale(1.05)'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"textSecondary\",\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"bold\",\n            children: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this)]\n        }, label, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        sx: {\n          mb: 2\n        },\n        children: \"This is a mock chart. Replace with actual chart library implementation.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), renderMockChart()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n\n// Example usage component\n_s(ChartExample, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = ChartExample;\nexport const DashboardCharts = () => {\n  const sampleData = {\n    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n    datasets: [{\n      label: 'Monthly Sales',\n      data: [65, 59, 80, 81, 56, 55],\n      backgroundColor: '#1976d2',\n      borderColor: '#1976d2',\n      borderWidth: 2\n    }]\n  };\n  const userGrowthData = {\n    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n    datasets: [{\n      label: 'New Users',\n      data: [12, 19, 15, 25],\n      backgroundColor: '#4caf50',\n      borderColor: '#4caf50',\n      borderWidth: 2\n    }]\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'grid',\n      gridTemplateColumns: {\n        xs: '1fr',\n        md: '1fr 1fr'\n      },\n      gap: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(ChartExample, {\n      title: \"Monthly Sales Overview\",\n      data: sampleData,\n      type: \"bar\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChartExample, {\n      title: \"User Growth\",\n      data: userGrowthData,\n      type: \"line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n\n// Instructions for integrating real charts:\n/*\n1. Install a charting library:\n   npm install react-chartjs-2 chart.js\n\n2. Import the chart components:\n   import {\n     Chart as ChartJS,\n     CategoryScale,\n     LinearScale,\n     BarElement,\n     LineElement,\n     PointElement,\n     Title,\n     Tooltip,\n     Legend,\n   } from 'chart.js';\n   import { Bar, Line } from 'react-chartjs-2';\n\n3. Register the components:\n   ChartJS.register(\n     CategoryScale,\n     LinearScale,\n     BarElement,\n     LineElement,\n     PointElement,\n     Title,\n     Tooltip,\n     Legend\n   );\n\n4. Replace the mock chart with real chart:\n   <Bar data={data} options={options} />\n\n5. For Recharts alternative:\n   npm install recharts\n   \n   import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';\n   \n   <BarChart width={400} height={300} data={data}>\n     <CartesianGrid strokeDasharray=\"3 3\" />\n     <XAxis dataKey=\"name\" />\n     <YAxis />\n     <Tooltip />\n     <Legend />\n     <Bar dataKey=\"value\" fill=\"#1976d2\" />\n   </BarChart>\n*/\n_c2 = DashboardCharts;\nexport default ChartExample;\nvar _c, _c2;\n$RefreshReg$(_c, \"ChartExample\");\n$RefreshReg$(_c2, \"DashboardCharts\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "useTheme", "jsxDEV", "_jsxDEV", "Chart<PERSON>xample", "title", "data", "type", "height", "_s", "theme", "renderMockChart", "maxValue", "Math", "max", "datasets", "sx", "display", "alignItems", "gap", "p", "children", "labels", "map", "label", "index", "value", "barHeight", "flexDirection", "flex", "width", "max<PERSON><PERSON><PERSON>", "backgroundColor", "palette", "primary", "main", "borderRadius", "mb", "transition", "dark", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "fontWeight", "gutterBottom", "_c", "DashboardCharts", "sampleData", "borderColor", "borderWidth", "userGrowthData", "gridTemplateColumns", "xs", "md", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/ChartExample.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Card,\n  CardContent,\n  Typography,\n  Box,\n  useTheme,\n} from '@mui/material';\n\n// This is a placeholder component showing how to integrate charts\n// To use real charts, install a charting library like:\n// npm install react-chartjs-2 chart.js\n// or\n// npm install recharts\n\ninterface ChartData {\n  labels: string[];\n  datasets: {\n    label: string;\n    data: number[];\n    backgroundColor?: string;\n    borderColor?: string;\n    borderWidth?: number;\n  }[];\n}\n\ninterface ChartExampleProps {\n  title: string;\n  data: ChartData;\n  type?: 'line' | 'bar' | 'pie' | 'doughnut';\n  height?: number;\n}\n\nconst ChartExample: React.FC<ChartExampleProps> = ({\n  title,\n  data,\n  type = 'line',\n  height = 300,\n}) => {\n  const theme = useTheme();\n\n  // Mock chart visualization - replace with actual chart library\n  const renderMockChart = () => {\n    const maxValue = Math.max(...data.datasets[0].data);\n    \n    return (\n      <Box sx={{ height, display: 'flex', alignItems: 'end', gap: 1, p: 2 }}>\n        {data.labels.map((label, index) => {\n          const value = data.datasets[0].data[index];\n          const barHeight = (value / maxValue) * (height - 60);\n          \n          return (\n            <Box\n              key={label}\n              sx={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                flex: 1,\n              }}\n            >\n              <Box\n                sx={{\n                  width: '100%',\n                  maxWidth: 40,\n                  height: barHeight,\n                  backgroundColor: theme.palette.primary.main,\n                  borderRadius: 1,\n                  mb: 1,\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    backgroundColor: theme.palette.primary.dark,\n                    transform: 'scale(1.05)',\n                  },\n                }}\n              />\n              <Typography variant=\"caption\" color=\"textSecondary\">\n                {label}\n              </Typography>\n              <Typography variant=\"body2\" fontWeight=\"bold\">\n                {value}\n              </Typography>\n            </Box>\n          );\n        })}\n      </Box>\n    );\n  };\n\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          {title}\n        </Typography>\n        <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mb: 2 }}>\n          This is a mock chart. Replace with actual chart library implementation.\n        </Typography>\n        {renderMockChart()}\n      </CardContent>\n    </Card>\n  );\n};\n\n// Example usage component\nexport const DashboardCharts: React.FC = () => {\n  const sampleData: ChartData = {\n    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n    datasets: [\n      {\n        label: 'Monthly Sales',\n        data: [65, 59, 80, 81, 56, 55],\n        backgroundColor: '#1976d2',\n        borderColor: '#1976d2',\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  const userGrowthData: ChartData = {\n    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n    datasets: [\n      {\n        label: 'New Users',\n        data: [12, 19, 15, 25],\n        backgroundColor: '#4caf50',\n        borderColor: '#4caf50',\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  return (\n    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>\n      <ChartExample\n        title=\"Monthly Sales Overview\"\n        data={sampleData}\n        type=\"bar\"\n      />\n      <ChartExample\n        title=\"User Growth\"\n        data={userGrowthData}\n        type=\"line\"\n      />\n    </Box>\n  );\n};\n\n// Instructions for integrating real charts:\n/*\n1. Install a charting library:\n   npm install react-chartjs-2 chart.js\n\n2. Import the chart components:\n   import {\n     Chart as ChartJS,\n     CategoryScale,\n     LinearScale,\n     BarElement,\n     LineElement,\n     PointElement,\n     Title,\n     Tooltip,\n     Legend,\n   } from 'chart.js';\n   import { Bar, Line } from 'react-chartjs-2';\n\n3. Register the components:\n   ChartJS.register(\n     CategoryScale,\n     LinearScale,\n     BarElement,\n     LineElement,\n     PointElement,\n     Title,\n     Tooltip,\n     Legend\n   );\n\n4. Replace the mock chart with real chart:\n   <Bar data={data} options={options} />\n\n5. For Recharts alternative:\n   npm install recharts\n   \n   import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';\n   \n   <BarChart width={400} height={300} data={data}>\n     <CartesianGrid strokeDasharray=\"3 3\" />\n     <XAxis dataKey=\"name\" />\n     <YAxis />\n     <Tooltip />\n     <Legend />\n     <Bar dataKey=\"value\" fill=\"#1976d2\" />\n   </BarChart>\n*/\n\nexport default ChartExample;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,QAAQ,QACH,eAAe;;AAEtB;AACA;AACA;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAoBA,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,KAAK;EACLC,IAAI;EACJC,IAAI,GAAG,MAAM;EACbC,MAAM,GAAG;AACX,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGR,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC;IAEnD,oBACEH,OAAA,CAACH,GAAG;MAACgB,EAAE,EAAE;QAAER,MAAM;QAAES,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,KAAK;QAAEC,GAAG,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,EACnEf,IAAI,CAACgB,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QACjC,MAAMC,KAAK,GAAGpB,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACT,IAAI,CAACmB,KAAK,CAAC;QAC1C,MAAME,SAAS,GAAID,KAAK,GAAGd,QAAQ,IAAKJ,MAAM,GAAG,EAAE,CAAC;QAEpD,oBACEL,OAAA,CAACH,GAAG;UAEFgB,EAAE,EAAE;YACFC,OAAO,EAAE,MAAM;YACfW,aAAa,EAAE,QAAQ;YACvBV,UAAU,EAAE,QAAQ;YACpBW,IAAI,EAAE;UACR,CAAE;UAAAR,QAAA,gBAEFlB,OAAA,CAACH,GAAG;YACFgB,EAAE,EAAE;cACFc,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,EAAE;cACZvB,MAAM,EAAEmB,SAAS;cACjBK,eAAe,EAAEtB,KAAK,CAACuB,OAAO,CAACC,OAAO,CAACC,IAAI;cAC3CC,YAAY,EAAE,CAAC;cACfC,EAAE,EAAE,CAAC;cACLC,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTN,eAAe,EAAEtB,KAAK,CAACuB,OAAO,CAACC,OAAO,CAACK,IAAI;gBAC3CC,SAAS,EAAE;cACb;YACF;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFzC,OAAA,CAACJ,UAAU;YAAC8C,OAAO,EAAC,SAAS;YAACC,KAAK,EAAC,eAAe;YAAAzB,QAAA,EAChDG;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACbzC,OAAA,CAACJ,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACE,UAAU,EAAC,MAAM;YAAA1B,QAAA,EAC1CK;UAAK;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA,GA5BRpB,KAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BP,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,oBACEzC,OAAA,CAACN,IAAI;IAAAwB,QAAA,eACHlB,OAAA,CAACL,WAAW;MAAAuB,QAAA,gBACVlB,OAAA,CAACJ,UAAU;QAAC8C,OAAO,EAAC,IAAI;QAACG,YAAY;QAAA3B,QAAA,EAClChB;MAAK;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACbzC,OAAA,CAACJ,UAAU;QAAC8C,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,eAAe;QAAC9B,EAAE,EAAE;UAAEqB,EAAE,EAAE;QAAE,CAAE;QAAAhB,QAAA,EAAC;MAEjE;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZjC,eAAe,CAAC,CAAC;IAAA;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;;AAED;AAAAnC,EAAA,CAvEML,YAAyC;EAAA,QAM/BH,QAAQ;AAAA;AAAAgD,EAAA,GANlB7C,YAAyC;AAwE/C,OAAO,MAAM8C,eAAyB,GAAGA,CAAA,KAAM;EAC7C,MAAMC,UAAqB,GAAG;IAC5B7B,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAClDP,QAAQ,EAAE,CACR;MACES,KAAK,EAAE,eAAe;MACtBlB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC9B0B,eAAe,EAAE,SAAS;MAC1BoB,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMC,cAAyB,GAAG;IAChChC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAChDP,QAAQ,EAAE,CACR;MACES,KAAK,EAAE,WAAW;MAClBlB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACtB0B,eAAe,EAAE,SAAS;MAC1BoB,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,oBACElD,OAAA,CAACH,GAAG;IAACgB,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEsC,mBAAmB,EAAE;QAAEC,EAAE,EAAE,KAAK;QAAEC,EAAE,EAAE;MAAU,CAAC;MAAEtC,GAAG,EAAE;IAAE,CAAE;IAAAE,QAAA,gBACtFlB,OAAA,CAACC,YAAY;MACXC,KAAK,EAAC,wBAAwB;MAC9BC,IAAI,EAAE6C,UAAW;MACjB5C,IAAI,EAAC;IAAK;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACFzC,OAAA,CAACC,YAAY;MACXC,KAAK,EAAC,aAAa;MACnBC,IAAI,EAAEgD,cAAe;MACrB/C,IAAI,EAAC;IAAM;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9CAc,GAAA,GA5CaR,eAAyB;AA4FtC,eAAe9C,YAAY;AAAC,IAAA6C,EAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}