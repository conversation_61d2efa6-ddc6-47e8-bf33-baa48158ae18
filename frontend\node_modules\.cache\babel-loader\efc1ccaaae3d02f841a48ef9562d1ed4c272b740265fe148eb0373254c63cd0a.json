{"ast": null, "code": "import { useEffect } from 'react';\nimport useEventCallback from './useEventCallback';\n/**\n * Attaches an event handler outside directly to specified DOM element\n * bypassing the react synthetic event system.\n *\n * @param element The target to listen for events on\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useEventListener(eventTarget, event, listener) {\n  let capture = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  const handler = useEventCallback(listener);\n  useEffect(() => {\n    const target = typeof eventTarget === 'function' ? eventTarget() : eventTarget;\n    target.addEventListener(event, handler, capture);\n    return () => target.removeEventListener(event, handler, capture);\n  }, [eventTarget]);\n}", "map": {"version": 3, "names": ["useEffect", "useEventCallback", "useEventListener", "eventTarget", "event", "listener", "capture", "arguments", "length", "undefined", "handler", "target", "addEventListener", "removeEventListener"], "sources": ["C:/laragon/www/frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useEventListener.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport useEventCallback from './useEventCallback';\n/**\n * Attaches an event handler outside directly to specified DOM element\n * bypassing the react synthetic event system.\n *\n * @param element The target to listen for events on\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useEventListener(eventTarget, event, listener, capture = false) {\n  const handler = useEventCallback(listener);\n  useEffect(() => {\n    const target = typeof eventTarget === 'function' ? eventTarget() : eventTarget;\n    target.addEventListener(event, handler, capture);\n    return () => target.removeEventListener(event, handler, capture);\n  }, [eventTarget]);\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAmB;EAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACpF,MAAMG,OAAO,GAAGT,gBAAgB,CAACI,QAAQ,CAAC;EAC1CL,SAAS,CAAC,MAAM;IACd,MAAMW,MAAM,GAAG,OAAOR,WAAW,KAAK,UAAU,GAAGA,WAAW,CAAC,CAAC,GAAGA,WAAW;IAC9EQ,MAAM,CAACC,gBAAgB,CAACR,KAAK,EAAEM,OAAO,EAAEJ,OAAO,CAAC;IAChD,OAAO,MAAMK,MAAM,CAACE,mBAAmB,CAACT,KAAK,EAAEM,OAAO,EAAEJ,OAAO,CAAC;EAClE,CAAC,EAAE,CAACH,WAAW,CAAC,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}