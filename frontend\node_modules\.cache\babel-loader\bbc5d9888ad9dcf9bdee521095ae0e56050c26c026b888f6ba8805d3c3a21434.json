{"ast": null, "code": "export { default } from \"./Stack.js\";\nexport { default as createStack } from \"./createStack.js\";\nexport * from \"./StackProps.js\";\nexport { default as stackClasses } from \"./stackClasses.js\";\nexport * from \"./stackClasses.js\";", "map": {"version": 3, "names": ["default", "createStack", "stackClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/system/esm/Stack/index.js"], "sourcesContent": ["export { default } from \"./Stack.js\";\nexport { default as createStack } from \"./createStack.js\";\nexport * from \"./StackProps.js\";\nexport { default as stackClasses } from \"./stackClasses.js\";\nexport * from \"./stackClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,WAAW,QAAQ,kBAAkB;AACzD,cAAc,iBAAiB;AAC/B,SAASD,OAAO,IAAIE,YAAY,QAAQ,mBAAmB;AAC3D,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}