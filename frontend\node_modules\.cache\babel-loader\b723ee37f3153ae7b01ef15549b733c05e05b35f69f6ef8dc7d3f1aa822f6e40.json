{"ast": null, "code": "/**\n * react-router v7.7.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { ENABLE_DEV_WARNINGS, ErrorResponseImpl, FrameworkContext, NO_BODY_STATUS_CODES, Outlet, RSCRouterContext, RemixErrorBoundary, RouterProvider, SINGLE_FETCH_REDIRECT_STATUS, SingleFetchRedirectSymbol, StaticRouterProvider, StreamTransfer, convertRoutesToDataRoutes, createBrowserHistory, createMemoryRouter, createRequestInit, createRouter, createServerRoutes, createStaticHandler, createStaticRouter, decodeViaTurboStream, encode, getManifestPath, getSingleFetchDataStrategyImpl, getStaticContextFromError, invariant, isDataWithResponseInit, isMutationMethod, isRedirectResponse, isRedirectStatusCode, isResponse, isRouteErrorResponse, matchRoutes, noActionDefinedError, redirect, redirectDocument, replace, shouldHydrateRouteLoader, singleFetchUrl, stripBasename, stripIndexParam, unstable_RouterContextProvider, unstable_createContext, useRouteError, warnOnce, withComponentProps, withErrorBoundaryProps, withHydrateFallbackProps } from \"./chunk-EF7DTUVF.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter(_ref) {\n  let {\n    context,\n    url,\n    nonce\n  } = _ref;\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let {\n    manifest,\n    routeModules,\n    criticalCss,\n    serverHandoffString\n  } = context;\n  let routes = createServerRoutes(manifest.routes, routeModules, context.future, context.isSpaMode);\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(routeId, route.clientLoader, manifestRoute.hasLoader, context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, /* @__PURE__ */React.createElement(FrameworkContext.Provider, {\n    value: {\n      manifest,\n      routeModules,\n      criticalCss,\n      serverHandoffString,\n      future: context.future,\n      ssr: context.ssr,\n      isSpaMode: context.isSpaMode,\n      routeDiscovery: context.routeDiscovery,\n      serializeError: context.serializeError,\n      renderMeta: context.renderMeta\n    }\n  }, /* @__PURE__ */React.createElement(RemixErrorBoundary, {\n    location: router.state.location\n  }, /* @__PURE__ */React.createElement(StaticRouterProvider, {\n    router,\n    context: context.staticHandlerContext,\n    hydrate: false\n  }))), context.serverHandoffStream ? /* @__PURE__ */React.createElement(React.Suspense, null, /* @__PURE__ */React.createElement(StreamTransfer, {\n    context,\n    identifier: 0,\n    reader: context.serverHandoffStream.getReader(),\n    textDecoder: new TextDecoder(),\n    nonce\n  })) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub(_ref2) {\n    let {\n      initialEntries,\n      initialIndex,\n      hydrationData,\n      future\n    } = _ref2;\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          unstable_middleware: future?.unstable_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: {\n            imports: [],\n            module: \"\"\n          },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: {\n          mode: \"lazy\",\n          manifestPath: \"/__manifest\"\n        }\n      };\n      let patched = processRoutes(\n      // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n      // types compared to `AgnosticRouteObject`\n      convertRoutesToDataRoutes(routes, r => r), _context !== void 0 ? _context : future?.unstable_middleware ? new unstable_RouterContextProvider() : {}, frameworkContextRef.current.manifest, frameworkContextRef.current.routeModules);\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: frameworkContextRef.current\n    }, /* @__PURE__ */React2.createElement(RouterProvider, {\n      router: routerRef.current\n    }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map(route => {\n    if (!route.id) {\n      throw new Error(\"Expected a route.id in react-router processRoutes() function\");\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? args => route.action({\n        ...args,\n        context\n      }) : void 0,\n      loader: route.loader ? args => route.loader({\n        ...args,\n        context\n      }) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(route.children, context, manifest, routeModules, newRoute.id);\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(/=+$/, \"\");\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\"raw\", encoder.encode(secret), {\n  name: \"HMAC\",\n  hash: \"SHA-256\"\n}, false, usages);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = function (name) {\n  let cookieOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let {\n    secrets = [],\n    ...options\n  } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, {\n        ...options,\n        ...parseOptions\n      });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(name, value === \"\" ? \"\" : await encodeCookieValue(value, secrets), {\n        ...options,\n        ...serializeOptions\n      });\n    }\n  };\n};\nvar isCookie = object => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(!expires, `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`);\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */(ServerMode2 => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, _ref3) => {\n    let [routeId, error] = _ref3;\n    return Object.assign(acc, {\n      [routeId]: sanitizeError(error, serverMode)\n    });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = {\n        ...val,\n        __type: \"RouteErrorResponse\"\n      };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...(sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {})\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(routes, pathname, basename);\n  if (!matches) return null;\n  return matches.map(match => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\");\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {}\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach(route => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest) {\n  let parentId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n  let routesByParentId = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : groupRoutesByParentId(manifest);\n  return (routesByParentId[parentId] || []).map(route => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future) {\n  let parentId = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"\";\n  let routesByParentId = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : groupRoutesByParentId(manifest);\n  return (routesByParentId[parentId] || []).map(route => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      unstable_middleware: route.module.unstable_middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async args => {\n        let preRenderedData = getBuildTimeHeader(args.request, \"X-React-Router-Prerender-Data\");\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = {\n              status: result.status\n            };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(data2 && route.id in data2, \"Unable to decode prerendered data\");\n            let result = data2[route.id];\n            invariant2(\"data\" in result, \"Unable to process prerendered data\");\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? args => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(manifest, future, route.id, routesByParentId),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/markup.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, match => ESCAPE_LOOKUP[match]);\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, m => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn) {\n  let boundaryIdx = context.errors ? context.matches.findIndex(m => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let {\n      actionHeaders,\n      actionData,\n      loaderHeaders,\n      loaderData\n    } = context;\n    context.matches.slice(boundaryIdx).some(match => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  return matches.reduce((parentHeaders, match, idx) => {\n    let {\n      id\n    } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(typeof headersFn === \"function\" ? headersFn({\n      loaderHeaders,\n      parentHeaders,\n      actionHeaders,\n      errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n    }) : headersFn);\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers());\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach(cookie => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */new Set([...NO_BODY_STATUS_CODES, 304]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function (context) {\n      let headers = getDocumentHeaders(context, build);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: getSingleFetchRedirect(context.statusCode, headers, build.basename),\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach(err => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let singleFetchResult;\n      if (context.errors) {\n        singleFetchResult = {\n          error: Object.values(context.errors)[0]\n        };\n      } else {\n        singleFetchResult = {\n          data: Object.values(context.actionData || {})[0]\n        };\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: singleFetchResult,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...(request.body ? {\n        duplex: \"half\"\n      } : void 0)\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: getSingleFetchRedirect(result.status, result.headers, build.basename),\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function (context) {\n      let headers = getDocumentHeaders(context, build);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: {\n            [SingleFetchRedirectSymbol]: getSingleFetchRedirect(context.statusCode, headers, build.basename)\n          },\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach(err => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let results = {};\n      let loadedMatches = new Set(context.matches.filter(m => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null).map(m => m.route.id));\n      if (context.errors) {\n        for (let [id, error] of Object.entries(context.errors)) {\n          results[id] = {\n            error\n          };\n        }\n      }\n      for (let [id, data2] of Object.entries(context.loaderData)) {\n        if (!(id in results) && loadedMatches.has(id)) {\n          results[id] = {\n            data: data2\n          };\n        }\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: results,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n    let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: m => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: {\n          [SingleFetchRedirectSymbol]: getSingleFetchRedirect(result.status, result.headers, build.basename)\n        },\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        root: {\n          error\n        }\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, _ref4) {\n  let {\n    result,\n    headers,\n    status\n  } = _ref4;\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, {\n      status,\n      headers: resultHeaders\n    });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(encodeViaTurboStream(result, request.signal, build.entry.module.streamTimeout, serverMode), {\n    status: status || 200,\n    headers: resultHeaders\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate:\n    // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n    // detail of ?_data requests as our way to tell the front end to revalidate when\n    // we didn't have a response body to include that information in.\n    // With single fetch, we tell the front end via this revalidate boolean field.\n    // However, we're respecting it for now because it may be something folks have\n    // used in their own responses\n    // TODO(v3): Consider removing or making this official public API\n    headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\"),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(() => controller.abort(new Error(\"Server Timeout\")), typeof streamTimeout === \"number\" ? streamTimeout : 4950);\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [value => {\n      if (value instanceof Error) {\n        let {\n          name,\n          message,\n          stack\n        } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n        return [\"SanitizedError\", name, message, stack];\n      }\n      if (value instanceof ErrorResponseImpl) {\n        let {\n          data: data3,\n          status,\n          statusText\n        } = value;\n        return [\"ErrorResponse\", data3, status, statusText];\n      }\n      if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n        return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n      }\n    }],\n    postPlugins: [value => {\n      if (!value) return;\n      if (typeof value !== \"object\") return;\n      return [\"SingleFetchClassInstance\", Object.fromEntries(Object.entries(value))];\n    }, () => [\"SingleFetchFallback\"]]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, _ref5) => {\n    let {\n      request\n    } = _ref5;\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      isRouteErrorResponse(error) && error.error ? error.error : error);\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = error => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.unstable_middleware) {\n      if (initialContext == null) {\n        loadContext = new unstable_RouterContextProvider();\n      } else {\n        try {\n          loadContext = new unstable_RouterContextProvider(initialContext);\n        } catch (e) {\n          let error = new Error(`Unable to create initial \\`unstable_RouterContextProvider\\` instance. Please confirm you are returning an instance of \\`Map<unstable_routerContext, unknown>\\` from your \\`getLoadContext\\` function.\n\nError: ${e instanceof Error ? e.toString() : e}`);\n          handleError(error);\n          return returnLastResortErrorResponse(error, serverMode);\n        }\n      }\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(_build.routeDiscovery.manifestPath, normalizedBasename);\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", {\n          status: 500\n        });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(routes, handlerUrl.pathname, _build.basename);\n      response = await handleSingleFetchRequest(serverMode, _build, staticHandler, request, handlerUrl, loadContext, handleError);\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          let result = getSingleFetchRedirect(response.status, response.headers, _build.basename);\n          if (request.method === \"GET\") {\n            result = {\n              [SingleFetchRedirectSymbol]: result\n            };\n          }\n          let headers = new Headers(response.headers);\n          headers.set(\"Content-Type\", \"text/x-script\");\n          return new Response(encodeViaTurboStream(result, request.signal, _build.entry.module.streamTimeout, serverMode), {\n            status: SINGLE_FETCH_REDIRECT_STATUS,\n            headers\n          });\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(serverMode, _build, staticHandler, matches.slice(-1)[0].route.id, request, loadContext, handleError);\n    } else {\n      let {\n        pathname\n      } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({\n          pathname\n        });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(serverMode, _build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss);\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */new Set();\n    url.searchParams.getAll(\"p\").forEach(path => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", {\n    status: 400\n  });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) : await singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError);\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let response = await staticHandler.query(request, {\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? ctx => renderHtml(ctx, isSpaMode) : void 0\n    });\n    return isResponse(response) ? response : renderHtml(response, isSpaMode);\n  } catch (error) {\n    handleError(error);\n    return new Response(null, {\n      status: 500\n    });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    if (isResponse(context)) {\n      return context;\n    }\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(state, request.signal, build.entry.module.streamTimeout, serverMode),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: err => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(error.status, error.statusText, data2);\n        } catch (e) {}\n      }\n      context = getStaticContextFromError(staticHandler.dataRoutes, context, errorForSecondRender);\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(state2, request.signal, build.entry.module.streamTimeout, serverMode),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let response = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? ctx => ctx : void 0\n    });\n    if (isResponse(response)) {\n      return response;\n    }\n    if (typeof response === \"string\") {\n      return new Response(response);\n    }\n    return Response.json(response);\n  } catch (error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      if (error) {\n        handleError(error);\n      }\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\"Expected a Response to be returned from resource route handler\");\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(serializeError(\n  // @ts-expect-error This is \"private\" from users but intended for internal use\n  errorResponse.error || new Error(\"Unexpected Server Error\"), serverMode), {\n    status: errorResponse.status,\n    statusText: errorResponse.statusText,\n    headers: {\n      \"X-Remix-Error\": \"yes\"\n    }\n  });\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = function () {\n  let initialData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let id = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = object => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage(_ref6) {\n  let {\n    cookie: cookieArg,\n    createData,\n    readData,\n    updateData,\n    deleteData\n  } = _ref6;\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && (await cookie.parse(cookieHeader, options));\n      let data2 = id && (await readData(id));\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let {\n        id,\n        data: data2\n      } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(cookie.isSigned, `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`);\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage() {\n  let {\n    cookie: cookieArg\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(cookieHeader && (await cookie.parse(cookieHeader, options)) || {});\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length);\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage() {\n  let {\n    cookie\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let map = /* @__PURE__ */new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, {\n        data: data2,\n        expires\n      });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let {\n          data: data2,\n          expires\n        } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, {\n        data: data2,\n        expires\n      });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path) {\n  let params = arguments.length <= 1 ? undefined : arguments[1];\n  return path.split(\"/\").map(segment => {\n    if (segment === \"*\") {\n      return params ? params[\"*\"] : void 0;\n    }\n    const match = segment.match(/^:([\\w-]+)(\\?)?/);\n    if (!match) return segment;\n    const param = match[1];\n    const value = params ? params[param] : void 0;\n    const isRequired = match[2] === void 0;\n    if (isRequired && value === void 0) {\n      throw Error(`Path '${path}' requires param '${param}' but it was not provided`);\n    }\n    return value;\n  }).filter(segment => segment !== void 0).join(\"/\");\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData(state, routes, getRouteInfo, location2, basename, isSpaMode) {\n  let hydrationData = {\n    ...state,\n    loaderData: {\n      ...state.loaderData\n    }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(routeId, routeInfo.clientLoader, routeInfo.hasLoader, isSpaMode) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null,\n      location: props.location\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return {\n        error: null,\n        location: props.location\n      };\n    }\n    return {\n      error: state.error,\n      location: state.location\n    };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n        error: this.state.error,\n        renderAppShell: true\n      });\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper(_ref7) {\n  let {\n    renderAppShell,\n    title,\n    children\n  } = _ref7;\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */React3.createElement(\"html\", {\n    lang: \"en\"\n  }, /* @__PURE__ */React3.createElement(\"head\", null, /* @__PURE__ */React3.createElement(\"meta\", {\n    charSet: \"utf-8\"\n  }), /* @__PURE__ */React3.createElement(\"meta\", {\n    name: \"viewport\",\n    content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n  }), /* @__PURE__ */React3.createElement(\"title\", null, title)), /* @__PURE__ */React3.createElement(\"body\", null, /* @__PURE__ */React3.createElement(\"main\", {\n    style: {\n      fontFamily: \"system-ui, sans-serif\",\n      padding: \"2rem\"\n    }\n  }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl(_ref8) {\n  let {\n    error,\n    renderAppShell\n  } = _ref8;\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */React3.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n    }\n  });\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n      renderAppShell,\n      title: \"Unhandled Thrown Response!\"\n    }, /* @__PURE__ */React3.createElement(\"h1\", {\n      style: {\n        fontSize: \"24px\"\n      }\n    }, error.status, \" \", error.statusText), ENABLE_DEV_WARNINGS ? heyDeveloper : null);\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n    renderAppShell,\n    title: \"Application Error!\"\n  }, /* @__PURE__ */React3.createElement(\"h1\", {\n    style: {\n      fontSize: \"24px\"\n    }\n  }, \"Application Error\"), /* @__PURE__ */React3.createElement(\"pre\", {\n    style: {\n      padding: \"2rem\",\n      background: \"hsla(10, 50%, 50%, 0.1)\",\n      color: \"red\",\n      overflow: \"auto\"\n    }\n  }, errorInstance.stack), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary(_ref9) {\n  let {\n    hasRootLayout\n  } = _ref9;\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n    renderAppShell: !hasRootLayout,\n    error\n  });\n}\n\n// lib/rsc/browser.tsx\nfunction createCallServer(_ref0) {\n  let {\n    createFromReadableStream,\n    createTemporaryReferenceSet,\n    encodeReply,\n    fetch: fetchImplementation = fetch\n  } = _ref0;\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(new Request(location.href, {\n      body: await encodeReply(args, {\n        temporaryReferences\n      }),\n      method: \"POST\",\n      headers: {\n        Accept: \"text/x-component\",\n        \"rsc-action-id\": id\n      }\n    }));\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__router.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n      // @ts-expect-error - We have old react types that don't know this can be async\n      async () => {\n        const rerender = await payload.rerender;\n        if (!rerender) return;\n        if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n          landedActionId = actionId;\n          if (rerender.type === \"redirect\") {\n            if (rerender.reload) {\n              window.location.href = rerender.location;\n              return;\n            }\n            globalVar.__router.navigate(rerender.location, {\n              replace: rerender.replace\n            });\n            return;\n          }\n          let lastMatch;\n          for (const match of rerender.matches) {\n            globalVar.__router.patchRoutes(lastMatch?.id ?? null, [createRouteFromServerManifest(match)], true);\n            lastMatch = match;\n          }\n          window.__router._internalSetStateDoNotUseOrYouWillBreakYourApp({});\n          React4.startTransition(() => {\n            window.__router._internalSetStateDoNotUseOrYouWillBreakYourApp({\n              loaderData: Object.assign({}, globalVar.__router.state.loaderData, rerender.loaderData),\n              errors: rerender.errors ? Object.assign({}, globalVar.__router.state.errors, rerender.errors) : null\n            });\n          });\n        }\n      });\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload(_ref1) {\n  let {\n    fetchImplementation,\n    createFromReadableStream,\n    unstable_getContext,\n    payload\n  } = _ref1;\n  const globalVar = window;\n  if (globalVar.__router) return globalVar.__router;\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let patches = /* @__PURE__ */new Map();\n  payload.patches?.forEach(patch => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(match, payload);\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(...childrenToPatch.map(r => createRouteFromServerManifest(r)));\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__router = createRouter({\n    routes,\n    unstable_getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData({\n      loaderData: payload.loaderData,\n      actionData: payload.actionData,\n      errors: payload.errors\n    }, routes, routeId => {\n      let match = payload.matches.find(m => m.id === routeId);\n      invariant(match, \"Route not found in payload\");\n      return {\n        clientLoader: match.clientLoader,\n        hasLoader: match.hasLoader,\n        hasHydrateFallback: match.hydrateFallbackElement != null\n      };\n    }, payload.location, void 0, false),\n    async patchRoutesOnNavigation(_ref10) {\n      let {\n        path,\n        signal\n      } = _ref10;\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches([path], createFromReadableStream, fetchImplementation, signal);\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(() => globalVar.__router, true, payload.basename, createFromReadableStream, fetchImplementation)\n  });\n  if (globalVar.__router.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__router.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__router.subscribe(_ref11 => {\n    let {\n      loaderData,\n      actionData\n    } = _ref11;\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  return globalVar.__router;\n}\nvar renderedRoutesContext = unstable_createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(getRouter, match => {\n    let M = match;\n    return {\n      hasLoader: M.route.hasLoader,\n      hasClientLoader: M.route.hasClientLoader,\n      hasComponent: M.route.hasComponent,\n      hasAction: M.route.hasAction,\n      hasClientAction: M.route.hasClientAction,\n      hasShouldRevalidate: M.route.hasShouldRevalidate\n    };\n  },\n  // pass map into fetchAndDecode so it can add payloads\n  getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation), ssr, basename,\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match => {\n    let M = match;\n    return M.route.hasComponent && !M.route.element;\n  });\n  return async args => args.unstable_runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__router.patchRoutes(rendered.parentId ?? null, [createRouteFromServerManifest(rendered)], true);\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let {\n      request,\n      context\n    } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(new Request(url, await createRequestInit(request)));\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = {\n        routes: {}\n      };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = {\n          data: data2\n        };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = {\n            error\n          };\n        }\n      }\n      return {\n        status: res.status,\n        data: results\n      };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter(_ref12) {\n  let {\n    createFromReadableStream,\n    fetch: fetchImplementation = fetch,\n    payload,\n    routeDiscovery = \"eager\",\n    unstable_getContext\n  } = _ref12;\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let router = React4.useMemo(() => createRouterFromPayload({\n    payload,\n    fetchImplementation,\n    unstable_getContext,\n    createFromReadableStream\n  }), [createFromReadableStream, payload, fetchImplementation, unstable_getContext]);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__router.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(() => router.subscribe(newState => {\n    if (newState.location !== location2) {\n      setLocation(newState.location);\n    }\n  }), [router, location2]);\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" ||\n    // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter(path => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation);\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: true,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules: {}\n  };\n  return /* @__PURE__ */React4.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React4.createElement(RSCRouterGlobalErrorBoundary, {\n    location: location2\n  }, /* @__PURE__ */React4.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React4.createElement(RouterProvider, {\n    router,\n    flushSync: ReactDOM.flushSync\n  }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader ||\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\"loader\", match.id, match.hasLoader);\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } :\n    // We always make the call in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    (_, singleFetch) => callSingleFetch(singleFetch),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\"action\", match.id, match.hasLoader);\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false);\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__router.basename ?? \"\").replace(/^\\/|\\/$/g, \"\");\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  paths.sort().forEach(path => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, {\n    signal\n  }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach(p => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach(p => {\n    window.__router.patchRoutes(p.parentId ?? null, [createRouteFromServerManifest(p)]);\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(resolve => resolveFlightDataPromise = resolve);\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, {\n        stream: true\n      });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch(err => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", {\n    fatal: true\n  });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(JSON.stringify(decoder.decode(chunk, {\n          stream: true\n        })), controller);\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(`Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`, controller);\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(encoder2.encode(`<script>${escapeScript(`(self.__FLIGHT_DATA||=[]).push(${chunk})`)}</script>`));\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nasync function routeRSCServerRequest(_ref13) {\n  let {\n    request,\n    fetchServer,\n    createFromReadableStream,\n    renderHTML,\n    hydrate = true\n  } = _ref13;\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let payloadPromise;\n  const getPayload = async () => {\n    if (payloadPromise) return payloadPromise;\n    payloadPromise = createFromReadableStream(body);\n    return payloadPromise;\n  };\n  try {\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter(_ref14) {\n  let {\n    getPayload\n  } = _ref14;\n  const payload = React5.use(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = {\n    ...payload.loaderData\n  };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map(match => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(payload.matches.reduceRight((previous, match) => {\n    const route = {\n      id: match.id,\n      action: match.hasAction || !!match.clientAction,\n      element: match.element,\n      errorElement: match.errorElement,\n      handle: match.handle,\n      hasErrorBoundary: !!match.errorElement,\n      hydrateFallbackElement: match.hydrateFallbackElement,\n      index: match.index,\n      loader: match.hasLoader || !!match.clientLoader,\n      path: match.path,\n      shouldRevalidate: match.shouldRevalidate\n    };\n    if (previous.length > 0) {\n      route.children = previous;\n    }\n    return [route];\n  }, []), context);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules: {}\n  };\n  return /* @__PURE__ */React5.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React5.createElement(RSCRouterGlobalErrorBoundary, {\n    location: payload.location\n  }, /* @__PURE__ */React5.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React5.createElement(StaticRouterProvider, {\n    context,\n    router,\n    hydrate: false,\n    nonce: payload.nonce\n  }))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = chunk => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = chunk => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {}\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nexport { ServerRouter, createRoutesStub, createCookie, isCookie, ServerMode, setDevServerHooks, createRequestHandler, createSession, isSession, createSessionStorage, createCookieSessionStorage, createMemorySessionStorage, href, getHydrationData, RSCDefaultRootErrorBoundary, createCallServer, RSCHydratedRouter, routeRSCServerRequest, RSCStaticRouter, getRSCStream, deserializeErrors };", "map": {"version": 3, "names": ["ENABLE_DEV_WARNINGS", "ErrorResponseImpl", "FrameworkContext", "NO_BODY_STATUS_CODES", "Outlet", "RSCRouterContext", "RemixErrorBoundary", "RouterProvider", "SINGLE_FETCH_REDIRECT_STATUS", "SingleFetchRedirectSymbol", "StaticRouterProvider", "StreamTransfer", "convertRoutesToDataRoutes", "createBrowserHistory", "createMemoryRouter", "createRequestInit", "createRouter", "createServerRoutes", "createStaticHandler", "createStaticRouter", "decodeViaTurboStream", "encode", "getManifestPath", "getSingleFetchDataStrategyImpl", "getStaticContextFromError", "invariant", "isDataWithResponseInit", "isMutationMethod", "isRedirectResponse", "isRedirectStatusCode", "isResponse", "isRouteErrorResponse", "matchRoutes", "noActionDefinedError", "redirect", "redirectDocument", "replace", "shouldHydrateRouteLoader", "singleFetchUrl", "stripBasename", "stripIndexParam", "unstable_RouterContextProvider", "unstable_createContext", "useRouteError", "warnOnce", "withComponentProps", "withErrorBoundaryProps", "withHydrateFallbackProps", "React", "ServerRouter", "_ref", "context", "url", "nonce", "URL", "manifest", "routeModules", "criticalCss", "serverHandoffString", "routes", "future", "isSpaMode", "staticHandlerContext", "loaderData", "match", "matches", "routeId", "route", "id", "manifestRoute", "clientLoader", "<PERSON><PERSON><PERSON><PERSON>", "HydrateFallback", "router", "createElement", "Fragment", "Provider", "value", "ssr", "routeDiscovery", "serializeError", "renderMeta", "location", "state", "hydrate", "serverHandoffStream", "Suspense", "identifier", "reader", "<PERSON><PERSON><PERSON><PERSON>", "textDecoder", "TextDecoder", "React2", "createRoutesStub", "_context", "RoutesTestStub", "_ref2", "initialEntries", "initialIndex", "hydrationData", "routerRef", "useRef", "frameworkContextRef", "current", "unstable_subResourceIntegrity", "unstable_middleware", "entry", "imports", "module", "version", "mode", "manifestPath", "patched", "processRoutes", "r", "parentId", "map", "Error", "newRoute", "path", "index", "Component", "Error<PERSON>ou<PERSON><PERSON>", "action", "args", "loader", "handle", "shouldRevalidate", "entryRoute", "hasAction", "hasClientAction", "hasClientLoader", "hasClientMiddleware", "hasErrorBou<PERSON>ry", "clientActionModule", "clientLoaderModule", "clientMiddlewareModule", "hydrateFallbackModule", "default", "links", "meta", "children", "parse", "serialize", "encoder", "TextEncoder", "sign", "secret", "data2", "key", "create<PERSON><PERSON>", "signature", "crypto", "subtle", "hash", "btoa", "String", "fromCharCode", "Uint8Array", "unsign", "cookie", "lastIndexOf", "slice", "byteStringToUint8Array", "atob", "valid", "verify", "error", "usages", "importKey", "name", "byteString", "array", "length", "i", "charCodeAt", "createCookie", "cookieOptions", "arguments", "undefined", "secrets", "options", "sameSite", "warnOnceAboutExpiresCookie", "expires", "isSigned", "maxAge", "Date", "now", "cookieHeader", "parseOptions", "cookies", "decoded", "decodeCookieValue", "serializeOptions", "encodeCookieValue", "<PERSON><PERSON><PERSON><PERSON>", "object", "encoded", "encodeData", "unsignedValue", "decodeData", "myUnescape", "encodeURIComponent", "JSON", "stringify", "decodeURIComponent", "myEscape", "str", "toString", "result", "chr", "code", "char<PERSON>t", "exec", "hex", "toUpperCase", "part", "parseInt", "createEntryRouteModules", "Object", "keys", "reduce", "memo", "ServerMode", "ServerMode2", "isServerMode", "sanitizeError", "serverMode", "sanitized", "stack", "sanitizeErrors", "errors", "entries", "acc", "_ref3", "assign", "message", "serializeErrors", "serialized", "val", "__type", "__subType", "matchServerRoutes", "pathname", "basename", "params", "callRouteHandler", "handler", "request", "stripRoutesParam", "stripIndexParam2", "init", "status", "Response", "indexValues", "searchParams", "getAll", "delete", "indexValuesToKeep", "indexValue", "push", "<PERSON><PERSON><PERSON>", "append", "method", "body", "headers", "signal", "duplex", "Request", "href", "invariant2", "console", "globalDevServerHooksKey", "setDevServerHooks", "devServerHooks", "globalThis", "getDevServerHooks", "getBuildTimeHeader", "headerName", "process", "env", "IS_RR_BUILD_REQUEST", "get", "e", "groupRoutesByParentId", "values", "for<PERSON>ach", "createRoutes", "routesByParentId", "createStaticHandlerDataRoutes", "commonRoute", "preRenderedData", "decodeURI", "uint8array", "stream", "ReadableStream", "start", "controller", "enqueue", "close", "global", "reload", "data", "caseSensitive", "ESCAPE_LOOKUP", "ESCAPE_REGEX", "escapeHtml", "html", "createServerHandoffString", "serverHandoff", "splitCookiesString", "getDocumentHeaders", "build", "getDocumentHeadersImpl", "m", "getRouteHeadersFn", "boundaryIdx", "findIndex", "errorHeaders", "actionHeaders", "actionData", "loaderHeaders", "some", "hasOwnProperty", "parentHeaders", "idx", "Headers", "includeErrorHeaders", "includeErrorCookies", "headersFn", "headers2", "prependCookies", "childHeaders", "parentSetCookieString", "childCookies", "Set", "getSetCookie", "has", "SERVER_NO_BODY_STATUS_CODES", "singleFetchAction", "static<PERSON><PERSON><PERSON>", "handlerUrl", "loadContext", "handleError", "respond2", "statusCode", "generateSingleFetchResponse", "getSingleFetchRedirect", "err", "singleFetchResult", "respond", "handlerRequest", "query", "requestContext", "skipL<PERSON>derError<PERSON><PERSON>bling", "skipRevalidation", "unstable_respond", "singleFetchLoaders", "results", "loadedMatches", "filter", "loadRouteIds", "routesParam", "split", "filterMatchesToLoad", "root", "_ref4", "resultHeaders", "set", "encodeViaTurboStream", "streamTimeout", "redirect2", "revalidate", "requestSignal", "AbortController", "timeoutId", "setTimeout", "abort", "addEventListener", "clearTimeout", "plugins", "data3", "statusText", "postPlugins", "fromEntries", "derive", "dataRoutes", "<PERSON><PERSON><PERSON><PERSON>", "_ref5", "aborted", "createRequestHandler", "_build", "requestHandler", "initialContext", "derived", "processRequestError", "returnLastResortErrorResponse", "normalizedBasename", "normalizedPath", "endsWith", "decodedPath", "prerender", "includes", "manifestUrl", "res", "handleManifestRequest", "response", "singleFetchMatches", "handleSingleFetchRequest", "handleDataRequest", "handleResourceRequest", "unstable_getCriticalCss", "getCriticalCss", "handleDocumentRequest", "assets", "patches", "paths", "startsWith", "segments", "_", "partialPath", "join", "add", "json", "ctx", "renderHtml", "isSpaMode2", "baseServerHandoff", "entryContext", "handleDocumentRequestFunction", "errorForSecondRender", "unwrapResponse", "state2", "error2", "queryRoute", "errorResponseToJson", "newError", "errorResponse", "contentType", "test", "text", "flash", "createSession", "initialData", "Map", "flashName", "unset", "isSession", "createSessionStorage", "_ref6", "cookieArg", "createData", "readData", "updateData", "deleteData", "warnOnceAboutSigningSessionCookie", "getSession", "commitSession", "session", "destroySession", "createCookieSessionStorage", "serializedCookie", "_session", "createMemorySessionStorage", "Math", "random", "substring", "segment", "param", "isRequired", "React4", "ReactDOM", "getHydrationData", "getRouteInfo", "location2", "initialMatches", "routeInfo", "hasHydrateFallback", "React3", "RSCRouterGlobalErrorBoundary", "constructor", "props", "getDerivedStateFromError", "getDerivedStateFromProps", "render", "RSCDefaultRootErrorBoundaryImpl", "renderAppShell", "ErrorWrapper", "_ref7", "title", "lang", "charSet", "content", "style", "fontFamily", "padding", "_ref8", "heyDeveloper", "dangerouslySetInnerHTML", "__html", "fontSize", "errorInstance", "errorString", "background", "color", "overflow", "RSCDefaultRootErrorBoundary", "_ref9", "hasRootLayout", "createCallServer", "_ref0", "createFromReadableStream", "createTemporaryReferenceSet", "encodeReply", "fetch", "fetchImplementation", "globalVar", "window", "landedActionId", "actionId", "__routerActionID", "temporaryReferences", "Accept", "payload", "type", "__router", "navigate", "actionResult", "rerender", "startTransition", "lastMatch", "patchRoutes", "createRouteFromServerManifest", "_internalSetStateDoNotUseOrYouWillBreakYourApp", "createRouterFromPayload", "_ref1", "unstable_getContext", "patch", "reduceRight", "previous", "childrenToPatch", "history", "find", "hydrateFallbackElement", "patchRoutesOnNavigation", "_ref10", "discoveredPaths", "fetchAndApplyManifestPatches", "dataStrategy", "getRSCSingleFetchDataStrategy", "initialized", "__routerInitialized", "initialize", "lastLoaderData", "subscribe", "_ref11", "renderedRoutesContext", "getRouter", "M", "hasComponent", "hasShouldRevalidate", "getFetchAndDecodeViaRSC", "element", "unstable_runClientMiddleware", "renderedRoutesById", "renderedRoutes", "rendered", "targetRoutes", "dataKey", "RSCHydratedRouter", "_ref12", "useMemo", "useLayoutEffect", "setLocation", "useState", "newState", "useEffect", "navigator", "connection", "saveData", "registerElement", "el", "tagName", "getAttribute", "origin", "nextPaths", "fetchPatches", "document", "querySelectorAll", "Array", "from", "debouncedFetchPatches", "debounce", "observer", "MutationObserver", "observe", "documentElement", "subtree", "childList", "attributes", "attributeFilter", "frameworkContext", "flushSync", "hasInitialData", "hasInitialError", "initialError", "isHydrationRequest", "dataRoute", "errorElement", "singleFetch", "serverLoader", "preventInvalidServerHandlerCall", "callSingleFetch", "clientAction", "serverAction", "<PERSON><PERSON><PERSON><PERSON>", "fn", "msg", "discoveredPathsMaxSize", "URL_LIMIT", "getManifestUrl", "sort", "clear", "p", "addToFifoQueue", "queue", "size", "first", "next", "callback", "wait", "_len", "_key", "React5", "encoder2", "trailer", "injectRSCPayload", "rscStream", "decoder", "resolveFlightDataPromise", "flightDataPromise", "Promise", "resolve", "startedRSC", "buffered", "timeout", "flushBufferedChunks", "chunk", "buf", "decode", "TransformStream", "transform", "writeRSCStream", "catch", "then", "flush", "fatal", "read", "done", "writeChunk", "base64", "fromCodePoint", "releaseLock", "remaining", "escapeScript", "script", "routeRSCServerRequest", "_ref13", "fetchServer", "renderHTML", "isDataRequest", "isReactServerRequest", "respondWithRSCPayload", "isManifestRequest", "serverResponse", "serverResponseB", "clone", "payloadPromise", "getPayload", "body2", "pipeThrough", "reason", "RSCStatic<PERSON><PERSON><PERSON>", "_ref14", "use", "Location", "patchedLoaderData", "pathnameBase", "getRSCStream", "encoder3", "streamController", "handleChunk", "__FLIGHT_DATA", "readyState", "deserializeErrors", "internal", "ErrorConstructor"], "sources": ["C:/laragon/www/frontend/node_modules/react-router/dist/development/chunk-T3VM44WY.mjs"], "sourcesContent": ["/**\n * react-router v7.7.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport {\n  ENABLE_DEV_WARNINGS,\n  ErrorResponseImpl,\n  FrameworkContext,\n  NO_BODY_STATUS_CODES,\n  Outlet,\n  RSCRouterContext,\n  RemixErrorBoundary,\n  RouterProvider,\n  SINGLE_FETCH_REDIRECT_STATUS,\n  SingleFetchRedirectSymbol,\n  StaticRouterProvider,\n  StreamTransfer,\n  convertRoutesToDataRoutes,\n  createBrowserHistory,\n  createMemoryRouter,\n  createRequestInit,\n  createRouter,\n  createServerRoutes,\n  createStaticHandler,\n  createStaticRouter,\n  decodeViaTurboStream,\n  encode,\n  getManifestPath,\n  getSingleFetchDataStrategyImpl,\n  getStaticContextFromError,\n  invariant,\n  isDataWithResponseInit,\n  isMutationMethod,\n  isRedirectResponse,\n  isRedirectStatusCode,\n  isResponse,\n  isRouteErrorResponse,\n  matchRoutes,\n  noActionDefinedError,\n  redirect,\n  redirectDocument,\n  replace,\n  shouldHydrateRouteLoader,\n  singleFetchUrl,\n  stripBasename,\n  stripIndexParam,\n  unstable_RouterContextProvider,\n  unstable_createContext,\n  useRouteError,\n  warnOnce,\n  withComponentProps,\n  withErrorBoundaryProps,\n  withHydrateFallbackProps\n} from \"./chunk-EF7DTUVF.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let { manifest, routeModules, criticalCss, serverHandoffString } = context;\n  let routes = createServerRoutes(\n    manifest.routes,\n    routeModules,\n    context.future,\n    context.isSpaMode\n  );\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(\n      routeId,\n      route.clientLoader,\n      manifestRoute.hasLoader,\n      context.isSpaMode\n    ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\n    FrameworkContext.Provider,\n    {\n      value: {\n        manifest,\n        routeModules,\n        criticalCss,\n        serverHandoffString,\n        future: context.future,\n        ssr: context.ssr,\n        isSpaMode: context.isSpaMode,\n        routeDiscovery: context.routeDiscovery,\n        serializeError: context.serializeError,\n        renderMeta: context.renderMeta\n      }\n    },\n    /* @__PURE__ */ React.createElement(RemixErrorBoundary, { location: router.state.location }, /* @__PURE__ */ React.createElement(\n      StaticRouterProvider,\n      {\n        router,\n        context: context.staticHandlerContext,\n        hydrate: false\n      }\n    ))\n  ), context.serverHandoffStream ? /* @__PURE__ */ React.createElement(React.Suspense, null, /* @__PURE__ */ React.createElement(\n    StreamTransfer,\n    {\n      context,\n      identifier: 0,\n      reader: context.serverHandoffStream.getReader(),\n      textDecoder: new TextDecoder(),\n      nonce\n    }\n  )) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          unstable_middleware: future?.unstable_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: { imports: [], module: \"\" },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" }\n      };\n      let patched = processRoutes(\n        // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n        // types compared to `AgnosticRouteObject`\n        convertRoutesToDataRoutes(routes, (r) => r),\n        _context !== void 0 ? _context : future?.unstable_middleware ? new unstable_RouterContextProvider() : {},\n        frameworkContextRef.current.manifest,\n        frameworkContextRef.current.routeModules\n      );\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */ React2.createElement(FrameworkContext.Provider, { value: frameworkContextRef.current }, /* @__PURE__ */ React2.createElement(RouterProvider, { router: routerRef.current }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map((route) => {\n    if (!route.id) {\n      throw new Error(\n        \"Expected a route.id in react-router processRoutes() function\"\n      );\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? (args) => route.action({ ...args, context }) : void 0,\n      loader: route.loader ? (args) => route.loader({ ...args, context }) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(\n        route.children,\n        context,\n        manifest,\n        routeModules,\n        newRoute.id\n      );\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */ new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(\n    /=+$/,\n    \"\"\n  );\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\n  \"raw\",\n  encoder.encode(secret),\n  { name: \"HMAC\", hash: \"SHA-256\" },\n  false,\n  usages\n);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let { secrets = [], ...options } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, { ...options, ...parseOptions });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(\n        name,\n        value === \"\" ? \"\" : await encodeCookieValue(value, secrets),\n        {\n          ...options,\n          ...serializeOptions\n        }\n      );\n    }\n  };\n};\nvar isCookie = (object) => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(\n    !expires,\n    `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`\n  );\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */ ((ServerMode2) => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, { [routeId]: sanitizeError(error, serverMode) });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = { ...val, __type: \"RouteErrorResponse\" };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {}\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(\n    routes,\n    pathname,\n    basename\n  );\n  if (!matches) return null;\n  return matches.map((match) => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\n      \"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\"\n    );\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {\n    }\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach((route) => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      unstable_middleware: route.module.unstable_middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async (args) => {\n        let preRenderedData = getBuildTimeHeader(\n          args.request,\n          \"X-React-Router-Prerender-Data\"\n        );\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = { status: result.status };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(\n              data2 && route.id in data2,\n              \"Unable to decode prerendered data\"\n            );\n            let result = data2[route.id];\n            invariant2(\n              \"data\" in result,\n              \"Unable to process prerendered data\"\n            );\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? (args) => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(\n        manifest,\n        future,\n        route.id,\n        routesByParentId\n      ),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/markup.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, (match) => ESCAPE_LOOKUP[match]);\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, (m) => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn) {\n  let boundaryIdx = context.errors ? context.matches.findIndex((m) => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let { actionHeaders, actionData, loaderHeaders, loaderData } = context;\n    context.matches.slice(boundaryIdx).some((match) => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  return matches.reduce((parentHeaders, match, idx) => {\n    let { id } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(\n      typeof headersFn === \"function\" ? headersFn({\n        loaderHeaders,\n        parentHeaders,\n        actionHeaders,\n        errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n      }) : headersFn\n    );\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers());\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach((cookie) => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */ new Set([\n  ...NO_BODY_STATUS_CODES,\n  304\n]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function(context) {\n      let headers = getDocumentHeaders(context, build);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: getSingleFetchRedirect(\n            context.statusCode,\n            headers,\n            build.basename\n          ),\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach((err) => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let singleFetchResult;\n      if (context.errors) {\n        singleFetchResult = { error: Object.values(context.errors)[0] };\n      } else {\n        singleFetchResult = {\n          data: Object.values(context.actionData || {})[0]\n        };\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: singleFetchResult,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...request.body ? { duplex: \"half\" } : void 0\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: getSingleFetchRedirect(\n          result.status,\n          result.headers,\n          build.basename\n        ),\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function(context) {\n      let headers = getDocumentHeaders(context, build);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: {\n            [SingleFetchRedirectSymbol]: getSingleFetchRedirect(\n              context.statusCode,\n              headers,\n              build.basename\n            )\n          },\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach((err) => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let results = {};\n      let loadedMatches = new Set(\n        context.matches.filter(\n          (m) => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null\n        ).map((m) => m.route.id)\n      );\n      if (context.errors) {\n        for (let [id, error] of Object.entries(context.errors)) {\n          results[id] = { error };\n        }\n      }\n      for (let [id, data2] of Object.entries(context.loaderData)) {\n        if (!(id in results) && loadedMatches.has(id)) {\n          results[id] = { data: data2 };\n        }\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: results,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n    let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: (m) => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: {\n          [SingleFetchRedirectSymbol]: getSingleFetchRedirect(\n            result.status,\n            result.headers,\n            build.basename\n          )\n        },\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { root: { error } },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, { status, headers: resultHeaders });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(\n    encodeViaTurboStream(\n      result,\n      request.signal,\n      build.entry.module.streamTimeout,\n      serverMode\n    ),\n    {\n      status: status || 200,\n      headers: resultHeaders\n    }\n  );\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate: (\n      // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n      // detail of ?_data requests as our way to tell the front end to revalidate when\n      // we didn't have a response body to include that information in.\n      // With single fetch, we tell the front end via this revalidate boolean field.\n      // However, we're respecting it for now because it may be something folks have\n      // used in their own responses\n      // TODO(v3): Consider removing or making this official public API\n      headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\")\n    ),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(\n    () => controller.abort(new Error(\"Server Timeout\")),\n    typeof streamTimeout === \"number\" ? streamTimeout : 4950\n  );\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [\n      (value) => {\n        if (value instanceof Error) {\n          let { name, message, stack } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n          return [\"SanitizedError\", name, message, stack];\n        }\n        if (value instanceof ErrorResponseImpl) {\n          let { data: data3, status, statusText } = value;\n          return [\"ErrorResponse\", data3, status, statusText];\n        }\n        if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n          return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n        }\n      }\n    ],\n    postPlugins: [\n      (value) => {\n        if (!value) return;\n        if (typeof value !== \"object\") return;\n        return [\n          \"SingleFetchClassInstance\",\n          Object.fromEntries(Object.entries(value))\n        ];\n      },\n      () => [\"SingleFetchFallback\"]\n    ]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, { request }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        isRouteErrorResponse(error) && error.error ? error.error : error\n      );\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = (error) => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.unstable_middleware) {\n      if (initialContext == null) {\n        loadContext = new unstable_RouterContextProvider();\n      } else {\n        try {\n          loadContext = new unstable_RouterContextProvider(\n            initialContext\n          );\n        } catch (e) {\n          let error = new Error(\n            `Unable to create initial \\`unstable_RouterContextProvider\\` instance. Please confirm you are returning an instance of \\`Map<unstable_routerContext, unknown>\\` from your \\`getLoadContext\\` function.\n\nError: ${e instanceof Error ? e.toString() : e}`\n          );\n          handleError(error);\n          return returnLastResortErrorResponse(error, serverMode);\n        }\n      }\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(\n      _build.routeDiscovery.manifestPath,\n      normalizedBasename\n    );\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", { status: 500 });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(\n        routes,\n        handlerUrl.pathname,\n        _build.basename\n      );\n      response = await handleSingleFetchRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        handlerUrl,\n        loadContext,\n        handleError\n      );\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          let result = getSingleFetchRedirect(\n            response.status,\n            response.headers,\n            _build.basename\n          );\n          if (request.method === \"GET\") {\n            result = {\n              [SingleFetchRedirectSymbol]: result\n            };\n          }\n          let headers = new Headers(response.headers);\n          headers.set(\"Content-Type\", \"text/x-script\");\n          return new Response(\n            encodeViaTurboStream(\n              result,\n              request.signal,\n              _build.entry.module.streamTimeout,\n              serverMode\n            ),\n            {\n              status: SINGLE_FETCH_REDIRECT_STATUS,\n              headers\n            }\n          );\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        matches.slice(-1)[0].route.id,\n        request,\n        loadContext,\n        handleError\n      );\n    } else {\n      let { pathname } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({ pathname });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        loadContext,\n        handleError,\n        isSpaMode,\n        criticalCss\n      );\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */ new Set();\n    url.searchParams.getAll(\"p\").forEach((path) => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", { status: 400 });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  ) : await singleFetchLoaders(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  );\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let response = await staticHandler.query(request, {\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? (ctx) => renderHtml(ctx, isSpaMode) : void 0\n    });\n    return isResponse(response) ? response : renderHtml(response, isSpaMode);\n  } catch (error) {\n    handleError(error);\n    return new Response(null, { status: 500 });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    if (isResponse(context)) {\n      return context;\n    }\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(\n        state,\n        request.signal,\n        build.entry.module.streamTimeout,\n        serverMode\n      ),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: (err) => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(\n        request,\n        context.statusCode,\n        headers,\n        entryContext,\n        loadContext\n      );\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(\n            error.status,\n            error.statusText,\n            data2\n          );\n        } catch (e) {\n        }\n      }\n      context = getStaticContextFromError(\n        staticHandler.dataRoutes,\n        context,\n        errorForSecondRender\n      );\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(\n          state2,\n          request.signal,\n          build.entry.module.streamTimeout,\n          serverMode\n        ),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(\n          request,\n          context.statusCode,\n          headers,\n          entryContext,\n          loadContext\n        );\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let response = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? (ctx) => ctx : void 0\n    });\n    if (isResponse(response)) {\n      return response;\n    }\n    if (typeof response === \"string\") {\n      return new Response(response);\n    }\n    return Response.json(response);\n  } catch (error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      if (error) {\n        handleError(error);\n      }\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\n        \"Expected a Response to be returned from resource route handler\"\n      );\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(\n    serializeError(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      errorResponse.error || new Error(\"Unexpected Server Error\"),\n      serverMode\n    ),\n    {\n      status: errorResponse.status,\n      statusText: errorResponse.statusText,\n      headers: {\n        \"X-Remix-Error\": \"yes\"\n      }\n    }\n  );\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = (object) => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && await cookie.parse(cookieHeader, options);\n      let data2 = id && await readData(id);\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let { id, data: data2 } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(\n    cookie.isSigned,\n    `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`\n  );\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({ cookie: cookieArg } = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(\n        cookieHeader && await cookie.parse(cookieHeader, options) || {}\n      );\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\n          \"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length\n        );\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({ cookie } = {}) {\n  let map = /* @__PURE__ */ new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, { data: data2, expires });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let { data: data2, expires } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */ new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, { data: data2, expires });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  return path.split(\"/\").map((segment) => {\n    if (segment === \"*\") {\n      return params ? params[\"*\"] : void 0;\n    }\n    const match = segment.match(/^:([\\w-]+)(\\?)?/);\n    if (!match) return segment;\n    const param = match[1];\n    const value = params ? params[param] : void 0;\n    const isRequired = match[2] === void 0;\n    if (isRequired && value === void 0) {\n      throw Error(\n        `Path '${path}' requires param '${param}' but it was not provided`\n      );\n    }\n    return value;\n  }).filter((segment) => segment !== void 0).join(\"/\");\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData(state, routes, getRouteInfo, location2, basename, isSpaMode) {\n  let hydrationData = {\n    ...state,\n    loaderData: { ...state.loaderData }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(\n        routeId,\n        routeInfo.clientLoader,\n        routeInfo.hasLoader,\n        isSpaMode\n      ) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = { error: null, location: props.location };\n  }\n  static getDerivedStateFromError(error) {\n    return { error };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return { error: null, location: props.location };\n    }\n    return { error: state.error, location: state.location };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */ React3.createElement(\n        RSCDefaultRootErrorBoundaryImpl,\n        {\n          error: this.state.error,\n          renderAppShell: true\n        }\n      );\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */ React3.createElement(\"html\", { lang: \"en\" }, /* @__PURE__ */ React3.createElement(\"head\", null, /* @__PURE__ */ React3.createElement(\"meta\", { charSet: \"utf-8\" }), /* @__PURE__ */ React3.createElement(\n    \"meta\",\n    {\n      name: \"viewport\",\n      content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n    }\n  ), /* @__PURE__ */ React3.createElement(\"title\", null, title)), /* @__PURE__ */ React3.createElement(\"body\", null, /* @__PURE__ */ React3.createElement(\"main\", { style: { fontFamily: \"system-ui, sans-serif\", padding: \"2rem\" } }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */ React3.createElement(\n    \"script\",\n    {\n      dangerouslySetInnerHTML: {\n        __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n      }\n    }\n  );\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */ React3.createElement(\n      ErrorWrapper,\n      {\n        renderAppShell,\n        title: \"Unhandled Thrown Response!\"\n      },\n      /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, error.status, \" \", error.statusText),\n      ENABLE_DEV_WARNINGS ? heyDeveloper : null\n    );\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */ React3.createElement(ErrorWrapper, { renderAppShell, title: \"Application Error!\" }, /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, \"Application Error\"), /* @__PURE__ */ React3.createElement(\n    \"pre\",\n    {\n      style: {\n        padding: \"2rem\",\n        background: \"hsla(10, 50%, 50%, 0.1)\",\n        color: \"red\",\n        overflow: \"auto\"\n      }\n    },\n    errorInstance.stack\n  ), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */ React3.createElement(\n    RSCDefaultRootErrorBoundaryImpl,\n    {\n      renderAppShell: !hasRootLayout,\n      error\n    }\n  );\n}\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(\n      new Request(location.href, {\n        body: await encodeReply(args, { temporaryReferences }),\n        method: \"POST\",\n        headers: {\n          Accept: \"text/x-component\",\n          \"rsc-action-id\": id\n        }\n      })\n    );\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__router.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n        // @ts-expect-error - We have old react types that don't know this can be async\n        async () => {\n          const rerender = await payload.rerender;\n          if (!rerender) return;\n          if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n            landedActionId = actionId;\n            if (rerender.type === \"redirect\") {\n              if (rerender.reload) {\n                window.location.href = rerender.location;\n                return;\n              }\n              globalVar.__router.navigate(rerender.location, {\n                replace: rerender.replace\n              });\n              return;\n            }\n            let lastMatch;\n            for (const match of rerender.matches) {\n              globalVar.__router.patchRoutes(\n                lastMatch?.id ?? null,\n                [createRouteFromServerManifest(match)],\n                true\n              );\n              lastMatch = match;\n            }\n            window.__router._internalSetStateDoNotUseOrYouWillBreakYourApp({});\n            React4.startTransition(() => {\n              window.__router._internalSetStateDoNotUseOrYouWillBreakYourApp({\n                loaderData: Object.assign(\n                  {},\n                  globalVar.__router.state.loaderData,\n                  rerender.loaderData\n                ),\n                errors: rerender.errors ? Object.assign(\n                  {},\n                  globalVar.__router.state.errors,\n                  rerender.errors\n                ) : null\n              });\n            });\n          }\n        }\n      );\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  unstable_getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__router) return globalVar.__router;\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let patches = /* @__PURE__ */ new Map();\n  payload.patches?.forEach((patch) => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(\n      match,\n      payload\n    );\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(\n          ...childrenToPatch.map((r) => createRouteFromServerManifest(r))\n        );\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__router = createRouter({\n    routes,\n    unstable_getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData(\n      {\n        loaderData: payload.loaderData,\n        actionData: payload.actionData,\n        errors: payload.errors\n      },\n      routes,\n      (routeId) => {\n        let match = payload.matches.find((m) => m.id === routeId);\n        invariant(match, \"Route not found in payload\");\n        return {\n          clientLoader: match.clientLoader,\n          hasLoader: match.hasLoader,\n          hasHydrateFallback: match.hydrateFallbackElement != null\n        };\n      },\n      payload.location,\n      void 0,\n      false\n    ),\n    async patchRoutesOnNavigation({ path, signal }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches(\n        [path],\n        createFromReadableStream,\n        fetchImplementation,\n        signal\n      );\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(\n      () => globalVar.__router,\n      true,\n      payload.basename,\n      createFromReadableStream,\n      fetchImplementation\n    )\n  });\n  if (globalVar.__router.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__router.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__router.subscribe(({ loaderData, actionData }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  return globalVar.__router;\n}\nvar renderedRoutesContext = unstable_createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(\n    getRouter,\n    (match) => {\n      let M = match;\n      return {\n        hasLoader: M.route.hasLoader,\n        hasClientLoader: M.route.hasClientLoader,\n        hasComponent: M.route.hasComponent,\n        hasAction: M.route.hasAction,\n        hasClientAction: M.route.hasClientAction,\n        hasShouldRevalidate: M.route.hasShouldRevalidate\n      };\n    },\n    // pass map into fetchAndDecode so it can add payloads\n    getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation),\n    ssr,\n    basename,\n    // If the route has a component but we don't have an element, we need to hit\n    // the server loader flow regardless of whether the client loader calls\n    // `serverLoader` or not, otherwise we'll have nothing to render.\n    (match) => {\n      let M = match;\n      return M.route.hasComponent && !M.route.element;\n    }\n  );\n  return async (args) => args.unstable_runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */ new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__router.patchRoutes(\n            rendered.parentId ?? null,\n            [createRouteFromServerManifest(rendered)],\n            true\n          );\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let { request, context } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(\n      new Request(url, await createRequestInit(request))\n    );\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = { routes: {} };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = { data: data2 };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = { error };\n        }\n      }\n      return { status: res.status, data: results };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  unstable_getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let router = React4.useMemo(\n    () => createRouterFromPayload({\n      payload,\n      fetchImplementation,\n      unstable_getContext,\n      createFromReadableStream\n    }),\n    [\n      createFromReadableStream,\n      payload,\n      fetchImplementation,\n      unstable_getContext\n    ]\n  );\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__router.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(\n    () => router.subscribe((newState) => {\n      if (newState.location !== location2) {\n        setLocation(newState.location);\n      }\n    }),\n    [router, location2]\n  );\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" || // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter((path) => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(\n          paths,\n          createFromReadableStream,\n          fetchImplementation\n        );\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: true,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules: {}\n  };\n  return /* @__PURE__ */ React4.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React4.createElement(RSCRouterGlobalErrorBoundary, { location: location2 }, /* @__PURE__ */ React4.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React4.createElement(RouterProvider, { router, flushSync: ReactDOM.flushSync }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader || // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\n              \"loader\",\n              match.id,\n              match.hasLoader\n            );\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } : (\n      // We always make the call in this RSC world since even if we don't\n      // have a `loader` we may need to get the `element` implementation\n      (_, singleFetch) => callSingleFetch(singleFetch)\n    ),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\n          \"action\",\n          match.id,\n          match.hasLoader\n        );\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    );\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */ new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */ new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__router.basename ?? \"\").replace(/^\\/|\\/$/g, \"\");\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  paths.sort().forEach((path) => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, { signal }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach((p) => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach((p) => {\n    window.__router.patchRoutes(\n      p.parentId ?? null,\n      [createRouteFromServerManifest(p)]\n    );\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(\n    (resolve) => resolveFlightDataPromise = resolve\n  );\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, { stream: true });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch((err) => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", { fatal: true });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(\n          JSON.stringify(decoder.decode(chunk, { stream: true })),\n          controller\n        );\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(\n          `Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`,\n          controller\n        );\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(\n    encoder2.encode(\n      `<script>${escapeScript(\n        `(self.__FLIGHT_DATA||=[]).push(${chunk})`\n      )}</script>`\n    )\n  );\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let payloadPromise;\n  const getPayload = async () => {\n    if (payloadPromise) return payloadPromise;\n    payloadPromise = createFromReadableStream(body);\n    return payloadPromise;\n  };\n  try {\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({\n  getPayload\n}) {\n  const payload = React5.use(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = { ...payload.loaderData };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    ) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map((match) => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(\n    payload.matches.reduceRight((previous, match) => {\n      const route = {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        element: match.element,\n        errorElement: match.errorElement,\n        handle: match.handle,\n        hasErrorBoundary: !!match.errorElement,\n        hydrateFallbackElement: match.hydrateFallbackElement,\n        index: match.index,\n        loader: match.hasLoader || !!match.clientLoader,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      };\n      if (previous.length > 0) {\n        route.children = previous;\n      }\n      return [route];\n    }, []),\n    context\n  );\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules: {}\n  };\n  return /* @__PURE__ */ React5.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React5.createElement(RSCRouterGlobalErrorBoundary, { location: payload.location }, /* @__PURE__ */ React5.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React5.createElement(\n    StaticRouterProvider,\n    {\n      context,\n      router,\n      hydrate: false,\n      nonce: payload.nonce\n    }\n  ))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = (chunk) => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = (chunk) => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\nexport {\n  ServerRouter,\n  createRoutesStub,\n  createCookie,\n  isCookie,\n  ServerMode,\n  setDevServerHooks,\n  createRequestHandler,\n  createSession,\n  isSession,\n  createSessionStorage,\n  createCookieSessionStorage,\n  createMemorySessionStorage,\n  href,\n  getHydrationData,\n  RSCDefaultRootErrorBoundary,\n  createCallServer,\n  RSCHydratedRouter,\n  routeRSCServerRequest,\n  RSCStaticRouter,\n  getRSCStream,\n  deserializeErrors\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACEA,mBAAmB,EACnBC,iBAAiB,EACjBC,gBAAgB,EAChBC,oBAAoB,EACpBC,MAAM,EACNC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,4BAA4B,EAC5BC,yBAAyB,EACzBC,oBAAoB,EACpBC,cAAc,EACdC,yBAAyB,EACzBC,oBAAoB,EACpBC,kBAAkB,EAClBC,iBAAiB,EACjBC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,kBAAkB,EAClBC,oBAAoB,EACpBC,MAAM,EACNC,eAAe,EACfC,8BAA8B,EAC9BC,yBAAyB,EACzBC,SAAS,EACTC,sBAAsB,EACtBC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,UAAU,EACVC,oBAAoB,EACpBC,WAAW,EACXC,oBAAoB,EACpBC,QAAQ,EACRC,gBAAgB,EAChBC,OAAO,EACPC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,8BAA8B,EAC9BC,sBAAsB,EACtBC,aAAa,EACbC,QAAQ,EACRC,kBAAkB,EAClBC,sBAAsB,EACtBC,wBAAwB,QACnB,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAYA,CAAAC,IAAA,EAIlB;EAAA,IAJmB;IACpBC,OAAO;IACPC,GAAG;IACHC;EACF,CAAC,GAAAH,IAAA;EACC,IAAI,OAAOE,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAG,IAAIE,GAAG,CAACF,GAAG,CAAC;EACpB;EACA,IAAI;IAAEG,QAAQ;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAoB,CAAC,GAAGP,OAAO;EAC1E,IAAIQ,MAAM,GAAG1C,kBAAkB,CAC7BsC,QAAQ,CAACI,MAAM,EACfH,YAAY,EACZL,OAAO,CAACS,MAAM,EACdT,OAAO,CAACU,SACV,CAAC;EACDV,OAAO,CAACW,oBAAoB,CAACC,UAAU,GAAG;IACxC,GAAGZ,OAAO,CAACW,oBAAoB,CAACC;EAClC,CAAC;EACD,KAAK,IAAIC,KAAK,IAAIb,OAAO,CAACW,oBAAoB,CAACG,OAAO,EAAE;IACtD,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;IAC5B,IAAID,KAAK,GAAGX,YAAY,CAACU,OAAO,CAAC;IACjC,IAAIG,aAAa,GAAGlB,OAAO,CAACI,QAAQ,CAACI,MAAM,CAACO,OAAO,CAAC;IACpD,IAAIC,KAAK,IAAIE,aAAa,IAAIhC,wBAAwB,CACpD6B,OAAO,EACPC,KAAK,CAACG,YAAY,EAClBD,aAAa,CAACE,SAAS,EACvBpB,OAAO,CAACU,SACV,CAAC,KAAKM,KAAK,CAACK,eAAe,IAAI,CAACH,aAAa,CAACE,SAAS,CAAC,EAAE;MACxD,OAAOpB,OAAO,CAACW,oBAAoB,CAACC,UAAU,CAACG,OAAO,CAAC;IACzD;EACF;EACA,IAAIO,MAAM,GAAGtD,kBAAkB,CAACwC,MAAM,EAAER,OAAO,CAACW,oBAAoB,CAAC;EACrE,OAAO,eAAgBd,KAAK,CAAC0B,aAAa,CAAC1B,KAAK,CAAC2B,QAAQ,EAAE,IAAI,EAAE,eAAgB3B,KAAK,CAAC0B,aAAa,CAClGxE,gBAAgB,CAAC0E,QAAQ,EACzB;IACEC,KAAK,EAAE;MACLtB,QAAQ;MACRC,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBE,MAAM,EAAET,OAAO,CAACS,MAAM;MACtBkB,GAAG,EAAE3B,OAAO,CAAC2B,GAAG;MAChBjB,SAAS,EAAEV,OAAO,CAACU,SAAS;MAC5BkB,cAAc,EAAE5B,OAAO,CAAC4B,cAAc;MACtCC,cAAc,EAAE7B,OAAO,CAAC6B,cAAc;MACtCC,UAAU,EAAE9B,OAAO,CAAC8B;IACtB;EACF,CAAC,EACD,eAAgBjC,KAAK,CAAC0B,aAAa,CAACpE,kBAAkB,EAAE;IAAE4E,QAAQ,EAAET,MAAM,CAACU,KAAK,CAACD;EAAS,CAAC,EAAE,eAAgBlC,KAAK,CAAC0B,aAAa,CAC9HhE,oBAAoB,EACpB;IACE+D,MAAM;IACNtB,OAAO,EAAEA,OAAO,CAACW,oBAAoB;IACrCsB,OAAO,EAAE;EACX,CACF,CAAC,CACH,CAAC,EAAEjC,OAAO,CAACkC,mBAAmB,GAAG,eAAgBrC,KAAK,CAAC0B,aAAa,CAAC1B,KAAK,CAACsC,QAAQ,EAAE,IAAI,EAAE,eAAgBtC,KAAK,CAAC0B,aAAa,CAC5H/D,cAAc,EACd;IACEwC,OAAO;IACPoC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAErC,OAAO,CAACkC,mBAAmB,CAACI,SAAS,CAAC,CAAC;IAC/CC,WAAW,EAAE,IAAIC,WAAW,CAAC,CAAC;IAC9BtC;EACF,CACF,CAAC,CAAC,GAAG,IAAI,CAAC;AACZ;;AAEA;AACA,OAAO,KAAKuC,MAAM,MAAM,OAAO;AAC/B,SAASC,gBAAgBA,CAAClC,MAAM,EAAEmC,QAAQ,EAAE;EAC1C,OAAO,SAASC,cAAcA,CAAAC,KAAA,EAK3B;IAAA,IAL4B;MAC7BC,cAAc;MACdC,YAAY;MACZC,aAAa;MACbvC;IACF,CAAC,GAAAoC,KAAA;IACC,IAAII,SAAS,GAAGR,MAAM,CAACS,MAAM,CAAC,CAAC;IAC/B,IAAIC,mBAAmB,GAAGV,MAAM,CAACS,MAAM,CAAC,CAAC;IACzC,IAAID,SAAS,CAACG,OAAO,IAAI,IAAI,EAAE;MAC7BD,mBAAmB,CAACC,OAAO,GAAG;QAC5B3C,MAAM,EAAE;UACN4C,6BAA6B,EAAE5C,MAAM,EAAE4C,6BAA6B,KAAK,IAAI;UAC7EC,mBAAmB,EAAE7C,MAAM,EAAE6C,mBAAmB,KAAK;QACvD,CAAC;QACDlD,QAAQ,EAAE;UACRI,MAAM,EAAE,CAAC,CAAC;UACV+C,KAAK,EAAE;YAAEC,OAAO,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;UAClCxD,GAAG,EAAE,EAAE;UACPyD,OAAO,EAAE;QACX,CAAC;QACDrD,YAAY,EAAE,CAAC,CAAC;QAChBsB,GAAG,EAAE,KAAK;QACVjB,SAAS,EAAE,KAAK;QAChBkB,cAAc,EAAE;UAAE+B,IAAI,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAc;MAC9D,CAAC;MACD,IAAIC,OAAO,GAAGC,aAAa;MACzB;MACA;MACArG,yBAAyB,CAAC+C,MAAM,EAAGuD,CAAC,IAAKA,CAAC,CAAC,EAC3CpB,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGlC,MAAM,EAAE6C,mBAAmB,GAAG,IAAIhE,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,EACxG6D,mBAAmB,CAACC,OAAO,CAAChD,QAAQ,EACpC+C,mBAAmB,CAACC,OAAO,CAAC/C,YAC9B,CAAC;MACD4C,SAAS,CAACG,OAAO,GAAGzF,kBAAkB,CAACkG,OAAO,EAAE;QAC9Cf,cAAc;QACdC,YAAY;QACZC;MACF,CAAC,CAAC;IACJ;IACA,OAAO,eAAgBP,MAAM,CAAClB,aAAa,CAACxE,gBAAgB,CAAC0E,QAAQ,EAAE;MAAEC,KAAK,EAAEyB,mBAAmB,CAACC;IAAQ,CAAC,EAAE,eAAgBX,MAAM,CAAClB,aAAa,CAACnE,cAAc,EAAE;MAAEkE,MAAM,EAAE2B,SAAS,CAACG;IAAQ,CAAC,CAAC,CAAC;EACrM,CAAC;AACH;AACA,SAASU,aAAaA,CAACtD,MAAM,EAAER,OAAO,EAAEI,QAAQ,EAAEC,YAAY,EAAE2D,QAAQ,EAAE;EACxE,OAAOxD,MAAM,CAACyD,GAAG,CAAEjD,KAAK,IAAK;IAC3B,IAAI,CAACA,KAAK,CAACC,EAAE,EAAE;MACb,MAAM,IAAIiD,KAAK,CACb,8DACF,CAAC;IACH;IACA,IAAIC,QAAQ,GAAG;MACblD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZmD,IAAI,EAAEpD,KAAK,CAACoD,IAAI;MAChBC,KAAK,EAAErD,KAAK,CAACqD,KAAK;MAClBC,SAAS,EAAEtD,KAAK,CAACsD,SAAS,GAAG5E,kBAAkB,CAACsB,KAAK,CAACsD,SAAS,CAAC,GAAG,KAAK,CAAC;MACzEjD,eAAe,EAAEL,KAAK,CAACK,eAAe,GAAGzB,wBAAwB,CAACoB,KAAK,CAACK,eAAe,CAAC,GAAG,KAAK,CAAC;MACjGkD,aAAa,EAAEvD,KAAK,CAACuD,aAAa,GAAG5E,sBAAsB,CAACqB,KAAK,CAACuD,aAAa,CAAC,GAAG,KAAK,CAAC;MACzFC,MAAM,EAAExD,KAAK,CAACwD,MAAM,GAAIC,IAAI,IAAKzD,KAAK,CAACwD,MAAM,CAAC;QAAE,GAAGC,IAAI;QAAEzE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5E0E,MAAM,EAAE1D,KAAK,CAAC0D,MAAM,GAAID,IAAI,IAAKzD,KAAK,CAAC0D,MAAM,CAAC;QAAE,GAAGD,IAAI;QAAEzE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5E2E,MAAM,EAAE3D,KAAK,CAAC2D,MAAM;MACpBC,gBAAgB,EAAE5D,KAAK,CAAC4D;IAC1B,CAAC;IACD,IAAIC,UAAU,GAAG;MACf5D,EAAE,EAAED,KAAK,CAACC,EAAE;MACZmD,IAAI,EAAEpD,KAAK,CAACoD,IAAI;MAChBC,KAAK,EAAErD,KAAK,CAACqD,KAAK;MAClBL,QAAQ;MACRc,SAAS,EAAE9D,KAAK,CAACwD,MAAM,IAAI,IAAI;MAC/BpD,SAAS,EAAEJ,KAAK,CAAC0D,MAAM,IAAI,IAAI;MAC/B;MACA;MACA;MACAK,eAAe,EAAE,KAAK;MACtBC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,gBAAgB,EAAElE,KAAK,CAACuD,aAAa,IAAI,IAAI;MAC7C;MACAd,MAAM,EAAE,8BAA8B;MACtC0B,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,sBAAsB,EAAE,KAAK,CAAC;MAC9BC,qBAAqB,EAAE,KAAK;IAC9B,CAAC;IACDlF,QAAQ,CAACI,MAAM,CAAC2D,QAAQ,CAAClD,EAAE,CAAC,GAAG4D,UAAU;IACzCxE,YAAY,CAACW,KAAK,CAACC,EAAE,CAAC,GAAG;MACvBsE,OAAO,EAAEpB,QAAQ,CAACG,SAAS,IAAIrH,MAAM;MACrCsH,aAAa,EAAEJ,QAAQ,CAACI,aAAa,IAAI,KAAK,CAAC;MAC/CI,MAAM,EAAE3D,KAAK,CAAC2D,MAAM;MACpBa,KAAK,EAAExE,KAAK,CAACwE,KAAK;MAClBC,IAAI,EAAEzE,KAAK,CAACyE,IAAI;MAChBb,gBAAgB,EAAE5D,KAAK,CAAC4D;IAC1B,CAAC;IACD,IAAI5D,KAAK,CAAC0E,QAAQ,EAAE;MAClBvB,QAAQ,CAACuB,QAAQ,GAAG5B,aAAa,CAC/B9C,KAAK,CAAC0E,QAAQ,EACd1F,OAAO,EACPI,QAAQ,EACRC,YAAY,EACZ8D,QAAQ,CAAClD,EACX,CAAC;IACH;IACA,OAAOkD,QAAQ;EACjB,CAAC,CAAC;AACJ;;AAEA;AACA,SAASwB,KAAK,EAAEC,SAAS,QAAQ,QAAQ;;AAEzC;AACA,IAAIC,OAAO,GAAG,eAAgB,IAAIC,WAAW,CAAC,CAAC;AAC/C,IAAIC,IAAI,GAAG,MAAAA,CAAOrE,KAAK,EAAEsE,MAAM,KAAK;EAClC,IAAIC,KAAK,GAAGJ,OAAO,CAAC3H,MAAM,CAACwD,KAAK,CAAC;EACjC,IAAIwE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;EAC3C,IAAII,SAAS,GAAG,MAAMC,MAAM,CAACC,MAAM,CAACP,IAAI,CAAC,MAAM,EAAEG,GAAG,EAAED,KAAK,CAAC;EAC5D,IAAIM,IAAI,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAG,IAAIC,UAAU,CAACP,SAAS,CAAC,CAAC,CAAC,CAACnH,OAAO,CACxE,KAAK,EACL,EACF,CAAC;EACD,OAAOyC,KAAK,GAAG,GAAG,GAAG6E,IAAI;AAC3B,CAAC;AACD,IAAIK,MAAM,GAAG,MAAAA,CAAOC,MAAM,EAAEb,MAAM,KAAK;EACrC,IAAI3B,KAAK,GAAGwC,MAAM,CAACC,WAAW,CAAC,GAAG,CAAC;EACnC,IAAIpF,KAAK,GAAGmF,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE1C,KAAK,CAAC;EAClC,IAAIkC,IAAI,GAAGM,MAAM,CAACE,KAAK,CAAC1C,KAAK,GAAG,CAAC,CAAC;EAClC,IAAI4B,KAAK,GAAGJ,OAAO,CAAC3H,MAAM,CAACwD,KAAK,CAAC;EACjC,IAAIwE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC;EAC7C,IAAI;IACF,IAAII,SAAS,GAAGY,sBAAsB,CAACC,IAAI,CAACV,IAAI,CAAC,CAAC;IAClD,IAAIW,KAAK,GAAG,MAAMb,MAAM,CAACC,MAAM,CAACa,MAAM,CAAC,MAAM,EAAEjB,GAAG,EAAEE,SAAS,EAAEH,KAAK,CAAC;IACrE,OAAOiB,KAAK,GAAGxF,KAAK,GAAG,KAAK;EAC9B,CAAC,CAAC,OAAO0F,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC;AACD,IAAIjB,SAAS,GAAG,MAAAA,CAAOH,MAAM,EAAEqB,MAAM,KAAKhB,MAAM,CAACC,MAAM,CAACgB,SAAS,CAC/D,KAAK,EACLzB,OAAO,CAAC3H,MAAM,CAAC8H,MAAM,CAAC,EACtB;EAAEuB,IAAI,EAAE,MAAM;EAAEhB,IAAI,EAAE;AAAU,CAAC,EACjC,KAAK,EACLc,MACF,CAAC;AACD,SAASL,sBAAsBA,CAACQ,UAAU,EAAE;EAC1C,IAAIC,KAAK,GAAG,IAAId,UAAU,CAACa,UAAU,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC1CF,KAAK,CAACE,CAAC,CAAC,GAAGH,UAAU,CAACI,UAAU,CAACD,CAAC,CAAC;EACrC;EACA,OAAOF,KAAK;AACd;;AAEA;AACA,IAAII,YAAY,GAAG,SAAAA,CAACN,IAAI,EAAyB;EAAA,IAAvBO,aAAa,GAAAC,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC1C,IAAI;IAAEE,OAAO,GAAG,EAAE;IAAE,GAAGC;EAAQ,CAAC,GAAG;IACjC9D,IAAI,EAAE,GAAG;IACT+D,QAAQ,EAAE,KAAK;IACf,GAAGL;EACL,CAAC;EACDM,0BAA0B,CAACb,IAAI,EAAEW,OAAO,CAACG,OAAO,CAAC;EACjD,OAAO;IACL,IAAId,IAAIA,CAAA,EAAG;MACT,OAAOA,IAAI;IACb,CAAC;IACD,IAAIe,QAAQA,CAAA,EAAG;MACb,OAAOL,OAAO,CAACP,MAAM,GAAG,CAAC;IAC3B,CAAC;IACD,IAAIW,OAAOA,CAAA,EAAG;MACZ,OAAO,OAAOH,OAAO,CAACK,MAAM,KAAK,WAAW,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,CAACG,OAAO;IAC9G,CAAC;IACD,MAAM1C,KAAKA,CAAC+C,YAAY,EAAEC,YAAY,EAAE;MACtC,IAAI,CAACD,YAAY,EAAE,OAAO,IAAI;MAC9B,IAAIE,OAAO,GAAGjD,KAAK,CAAC+C,YAAY,EAAE;QAAE,GAAGR,OAAO;QAAE,GAAGS;MAAa,CAAC,CAAC;MAClE,IAAIpB,IAAI,IAAIqB,OAAO,EAAE;QACnB,IAAIlH,KAAK,GAAGkH,OAAO,CAACrB,IAAI,CAAC;QACzB,IAAI,OAAO7F,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE,EAAE;UAC7C,IAAImH,OAAO,GAAG,MAAMC,iBAAiB,CAACpH,KAAK,EAAEuG,OAAO,CAAC;UACrD,OAAOY,OAAO;QAChB,CAAC,MAAM;UACL,OAAO,EAAE;QACX;MACF,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;IACD,MAAMjD,SAASA,CAAClE,KAAK,EAAEqH,gBAAgB,EAAE;MACvC,OAAOnD,SAAS,CACd2B,IAAI,EACJ7F,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG,MAAMsH,iBAAiB,CAACtH,KAAK,EAAEuG,OAAO,CAAC,EAC3D;QACE,GAAGC,OAAO;QACV,GAAGa;MACL,CACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,IAAIE,QAAQ,GAAIC,MAAM,IAAK;EACzB,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAAC3B,IAAI,KAAK,QAAQ,IAAI,OAAO2B,MAAM,CAACZ,QAAQ,KAAK,SAAS,IAAI,OAAOY,MAAM,CAACvD,KAAK,KAAK,UAAU,IAAI,OAAOuD,MAAM,CAACtD,SAAS,KAAK,UAAU;AAClL,CAAC;AACD,eAAeoD,iBAAiBA,CAACtH,KAAK,EAAEuG,OAAO,EAAE;EAC/C,IAAIkB,OAAO,GAAGC,UAAU,CAAC1H,KAAK,CAAC;EAC/B,IAAIuG,OAAO,CAACP,MAAM,GAAG,CAAC,EAAE;IACtByB,OAAO,GAAG,MAAMpD,IAAI,CAACoD,OAAO,EAAElB,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOkB,OAAO;AAChB;AACA,eAAeL,iBAAiBA,CAACpH,KAAK,EAAEuG,OAAO,EAAE;EAC/C,IAAIA,OAAO,CAACP,MAAM,GAAG,CAAC,EAAE;IACtB,KAAK,IAAI1B,MAAM,IAAIiC,OAAO,EAAE;MAC1B,IAAIoB,aAAa,GAAG,MAAMzC,MAAM,CAAClF,KAAK,EAAEsE,MAAM,CAAC;MAC/C,IAAIqD,aAAa,KAAK,KAAK,EAAE;QAC3B,OAAOC,UAAU,CAACD,aAAa,CAAC;MAClC;IACF;IACA,OAAO,IAAI;EACb;EACA,OAAOC,UAAU,CAAC5H,KAAK,CAAC;AAC1B;AACA,SAAS0H,UAAUA,CAAC1H,KAAK,EAAE;EACzB,OAAO8E,IAAI,CAAC+C,UAAU,CAACC,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAAChI,KAAK,CAAC,CAAC,CAAC,CAAC;AACpE;AACA,SAAS4H,UAAUA,CAAC5H,KAAK,EAAE;EACzB,IAAI;IACF,OAAO+H,IAAI,CAAC9D,KAAK,CAACgE,kBAAkB,CAACC,QAAQ,CAAC3C,IAAI,CAACvF,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,OAAO0F,KAAK,EAAE;IACd,OAAO,CAAC,CAAC;EACX;AACF;AACA,SAASwC,QAAQA,CAAClI,KAAK,EAAE;EACvB,IAAImI,GAAG,GAAGnI,KAAK,CAACoI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI1F,KAAK,GAAG,CAAC;EACb,IAAI2F,GAAG,EAAEC,IAAI;EACb,OAAO5F,KAAK,GAAGwF,GAAG,CAACnC,MAAM,EAAE;IACzBsC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC7F,KAAK,EAAE,CAAC;IACzB,IAAI,aAAa,CAAC8F,IAAI,CAACH,GAAG,CAAC,EAAE;MAC3BD,MAAM,IAAIC,GAAG;IACf,CAAC,MAAM;MACLC,IAAI,GAAGD,GAAG,CAACpC,UAAU,CAAC,CAAC,CAAC;MACxB,IAAIqC,IAAI,GAAG,GAAG,EAAE;QACdF,MAAM,IAAI,GAAG,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLF,MAAM,IAAI,IAAI,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;MAC7C;IACF;EACF;EACA,OAAON,MAAM;AACf;AACA,SAASK,GAAGA,CAACH,IAAI,EAAEvC,MAAM,EAAE;EACzB,IAAIqC,MAAM,GAAGE,IAAI,CAACH,QAAQ,CAAC,EAAE,CAAC;EAC9B,OAAOC,MAAM,CAACrC,MAAM,GAAGA,MAAM,EAAEqC,MAAM,GAAG,GAAG,GAAGA,MAAM;EACpD,OAAOA,MAAM;AACf;AACA,SAASR,UAAUA,CAAC7H,KAAK,EAAE;EACzB,IAAImI,GAAG,GAAGnI,KAAK,CAACoI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI1F,KAAK,GAAG,CAAC;EACb,IAAI2F,GAAG,EAAEM,IAAI;EACb,OAAOjG,KAAK,GAAGwF,GAAG,CAACnC,MAAM,EAAE;IACzBsC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC7F,KAAK,EAAE,CAAC;IACzB,IAAI2F,GAAG,KAAK,GAAG,EAAE;MACf,IAAIH,GAAG,CAACK,MAAM,CAAC7F,KAAK,CAAC,KAAK,GAAG,EAAE;QAC7BiG,IAAI,GAAGT,GAAG,CAAC9C,KAAK,CAAC1C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,CAAC;QACtC,IAAI,eAAe,CAAC8F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAItD,MAAM,CAACC,YAAY,CAAC6D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjDjG,KAAK,IAAI,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACLiG,IAAI,GAAGT,GAAG,CAAC9C,KAAK,CAAC1C,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;QAClC,IAAI,eAAe,CAAC8F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAItD,MAAM,CAACC,YAAY,CAAC6D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjDjG,KAAK,IAAI,CAAC;UACV;QACF;MACF;IACF;IACA0F,MAAM,IAAIC,GAAG;EACf;EACA,OAAOD,MAAM;AACf;AACA,SAAS3B,0BAA0BA,CAACb,IAAI,EAAEc,OAAO,EAAE;EACjD5I,QAAQ,CACN,CAAC4I,OAAO,EACR,QAAQd,IAAI,6WACd,CAAC;AACH;;AAEA;AACA,SAASiD,uBAAuBA,CAACpK,QAAQ,EAAE;EACzC,OAAOqK,MAAM,CAACC,IAAI,CAACtK,QAAQ,CAAC,CAACuK,MAAM,CAAC,CAACC,IAAI,EAAE7J,OAAO,KAAK;IACrD,IAAIC,KAAK,GAAGZ,QAAQ,CAACW,OAAO,CAAC;IAC7B,IAAIC,KAAK,EAAE;MACT4J,IAAI,CAAC7J,OAAO,CAAC,GAAGC,KAAK,CAACyC,MAAM;IAC9B;IACA,OAAOmH,IAAI;EACb,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA,IAAIC,UAAU,GAAG,eAAgB,CAAEC,WAAW,IAAK;EACjDA,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa;EAC1CA,WAAW,CAAC,YAAY,CAAC,GAAG,YAAY;EACxCA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5B,OAAOA,WAAW;AACpB,CAAC,EAAED,UAAU,IAAI,CAAC,CAAC,CAAC;AACpB,SAASE,YAAYA,CAACrJ,KAAK,EAAE;EAC3B,OAAOA,KAAK,KAAK,aAAa,CAAC,qBAAqBA,KAAK,KAAK,YAAY,CAAC,oBAAoBA,KAAK,KAAK,MAAM,CAAC;AAClH;;AAEA;AACA,SAASsJ,aAAaA,CAAC5D,KAAK,EAAE6D,UAAU,EAAE;EACxC,IAAI7D,KAAK,YAAYlD,KAAK,IAAI+G,UAAU,KAAK,aAAa,CAAC,mBAAmB;IAC5E,IAAIC,SAAS,GAAG,IAAIhH,KAAK,CAAC,yBAAyB,CAAC;IACpDgH,SAAS,CAACC,KAAK,GAAG,KAAK,CAAC;IACxB,OAAOD,SAAS;EAClB;EACA,OAAO9D,KAAK;AACd;AACA,SAASgE,cAAcA,CAACC,MAAM,EAAEJ,UAAU,EAAE;EAC1C,OAAOR,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC,CAACV,MAAM,CAAC,CAACY,GAAG,EAAAC,KAAA,KAAuB;IAAA,IAArB,CAACzK,OAAO,EAAEqG,KAAK,CAAC,GAAAoE,KAAA;IACzD,OAAOf,MAAM,CAACgB,MAAM,CAACF,GAAG,EAAE;MAAE,CAACxK,OAAO,GAAGiK,aAAa,CAAC5D,KAAK,EAAE6D,UAAU;IAAE,CAAC,CAAC;EAC5E,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASpJ,cAAcA,CAACuF,KAAK,EAAE6D,UAAU,EAAE;EACzC,IAAIC,SAAS,GAAGF,aAAa,CAAC5D,KAAK,EAAE6D,UAAU,CAAC;EAChD,OAAO;IACLS,OAAO,EAAER,SAAS,CAACQ,OAAO;IAC1BP,KAAK,EAAED,SAAS,CAACC;EACnB,CAAC;AACH;AACA,SAASQ,eAAeA,CAACN,MAAM,EAAEJ,UAAU,EAAE;EAC3C,IAAI,CAACI,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIO,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAAC1F,GAAG,EAAE2F,GAAG,CAAC,IAAIP,OAAO,EAAE;IAC9B,IAAI1M,oBAAoB,CAACiN,GAAG,CAAC,EAAE;MAC7BD,UAAU,CAAC1F,GAAG,CAAC,GAAG;QAAE,GAAG2F,GAAG;QAAEC,MAAM,EAAE;MAAqB,CAAC;IAC5D,CAAC,MAAM,IAAID,GAAG,YAAY3H,KAAK,EAAE;MAC/B,IAAIgH,SAAS,GAAGF,aAAa,CAACa,GAAG,EAAEZ,UAAU,CAAC;MAC9CW,UAAU,CAAC1F,GAAG,CAAC,GAAG;QAChBwF,OAAO,EAAER,SAAS,CAACQ,OAAO;QAC1BP,KAAK,EAAED,SAAS,CAACC,KAAK;QACtBW,MAAM,EAAE,OAAO;QACf;QACA;QACA;QACA;QACA,IAAGZ,SAAS,CAAC3D,IAAI,KAAK,OAAO,GAAG;UAC9BwE,SAAS,EAAEb,SAAS,CAAC3D;QACvB,CAAC,GAAG,CAAC,CAAC;MACR,CAAC;IACH,CAAC,MAAM;MACLqE,UAAU,CAAC1F,GAAG,CAAC,GAAG2F,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;;AAEA;AACA,SAASI,iBAAiBA,CAACxL,MAAM,EAAEyL,QAAQ,EAAEC,QAAQ,EAAE;EACrD,IAAIpL,OAAO,GAAGjC,WAAW,CACvB2B,MAAM,EACNyL,QAAQ,EACRC,QACF,CAAC;EACD,IAAI,CAACpL,OAAO,EAAE,OAAO,IAAI;EACzB,OAAOA,OAAO,CAACmD,GAAG,CAAEpD,KAAK,KAAM;IAC7BsL,MAAM,EAAEtL,KAAK,CAACsL,MAAM;IACpBF,QAAQ,EAAEpL,KAAK,CAACoL,QAAQ;IACxBjL,KAAK,EAAEH,KAAK,CAACG;EACf,CAAC,CAAC,CAAC;AACL;;AAEA;AACA,eAAeoL,gBAAgBA,CAACC,OAAO,EAAE5H,IAAI,EAAE;EAC7C,IAAIsF,MAAM,GAAG,MAAMsC,OAAO,CAAC;IACzBC,OAAO,EAAEC,gBAAgB,CAACC,gBAAgB,CAAC/H,IAAI,CAAC6H,OAAO,CAAC,CAAC;IACzDH,MAAM,EAAE1H,IAAI,CAAC0H,MAAM;IACnBnM,OAAO,EAAEyE,IAAI,CAACzE;EAChB,CAAC,CAAC;EACF,IAAIzB,sBAAsB,CAACwL,MAAM,CAAC,IAAIA,MAAM,CAAC0C,IAAI,IAAI1C,MAAM,CAAC0C,IAAI,CAACC,MAAM,IAAIhO,oBAAoB,CAACqL,MAAM,CAAC0C,IAAI,CAACC,MAAM,CAAC,EAAE;IACnH,MAAM,IAAIC,QAAQ,CAAC,IAAI,EAAE5C,MAAM,CAAC0C,IAAI,CAAC;EACvC;EACA,OAAO1C,MAAM;AACf;AACA,SAASyC,gBAAgBA,CAACF,OAAO,EAAE;EACjC,IAAIrM,GAAG,GAAG,IAAIE,GAAG,CAACmM,OAAO,CAACrM,GAAG,CAAC;EAC9B,IAAI2M,WAAW,GAAG3M,GAAG,CAAC4M,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;EAClD7M,GAAG,CAAC4M,YAAY,CAACE,MAAM,CAAC,OAAO,CAAC;EAChC,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAIC,UAAU,IAAIL,WAAW,EAAE;IAClC,IAAIK,UAAU,EAAE;MACdD,iBAAiB,CAACE,IAAI,CAACD,UAAU,CAAC;IACpC;EACF;EACA,KAAK,IAAIE,MAAM,IAAIH,iBAAiB,EAAE;IACpC/M,GAAG,CAAC4M,YAAY,CAACO,MAAM,CAAC,OAAO,EAAED,MAAM,CAAC;EAC1C;EACA,IAAIV,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACzN,GAAG,CAAC0N,IAAI,EAAElB,IAAI,CAAC;AACpC;AACA,SAASF,gBAAgBA,CAACD,OAAO,EAAE;EACjC,IAAIrM,GAAG,GAAG,IAAIE,GAAG,CAACmM,OAAO,CAACrM,GAAG,CAAC;EAC9BA,GAAG,CAAC4M,YAAY,CAACE,MAAM,CAAC,SAAS,CAAC;EAClC,IAAIN,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACzN,GAAG,CAAC0N,IAAI,EAAElB,IAAI,CAAC;AACpC;;AAEA;AACA,SAASmB,UAAUA,CAAClM,KAAK,EAAEgK,OAAO,EAAE;EAClC,IAAIhK,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IACrEmM,OAAO,CAACzG,KAAK,CACX,iIACF,CAAC;IACD,MAAM,IAAIlD,KAAK,CAACwH,OAAO,CAAC;EAC1B;AACF;;AAEA;AACA,IAAIoC,uBAAuB,GAAG,6BAA6B;AAC3D,SAASC,iBAAiBA,CAACC,cAAc,EAAE;EACzCC,UAAU,CAACH,uBAAuB,CAAC,GAAGE,cAAc;AACtD;AACA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,OAAOD,UAAU,CAACH,uBAAuB,CAAC;AAC5C;AACA,SAASK,kBAAkBA,CAAC7B,OAAO,EAAE8B,UAAU,EAAE;EAC/C,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;IAClC,IAAI;MACF,IAAIA,OAAO,CAACC,GAAG,EAAEC,mBAAmB,KAAK,KAAK,EAAE;QAC9C,OAAOjC,OAAO,CAACiB,OAAO,CAACiB,GAAG,CAACJ,UAAU,CAAC;MACxC;IACF,CAAC,CAAC,OAAOK,CAAC,EAAE,CACZ;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASC,qBAAqBA,CAACtO,QAAQ,EAAE;EACvC,IAAII,MAAM,GAAG,CAAC,CAAC;EACfiK,MAAM,CAACkE,MAAM,CAACvO,QAAQ,CAAC,CAACwO,OAAO,CAAE5N,KAAK,IAAK;IACzC,IAAIA,KAAK,EAAE;MACT,IAAIgD,QAAQ,GAAGhD,KAAK,CAACgD,QAAQ,IAAI,EAAE;MACnC,IAAI,CAACxD,MAAM,CAACwD,QAAQ,CAAC,EAAE;QACrBxD,MAAM,CAACwD,QAAQ,CAAC,GAAG,EAAE;MACvB;MACAxD,MAAM,CAACwD,QAAQ,CAAC,CAACkJ,IAAI,CAAClM,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,OAAOR,MAAM;AACf;AACA,SAASqO,YAAYA,CAACzO,QAAQ,EAAqE;EAAA,IAAnE4D,QAAQ,GAAA+D,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAAA,IAAE+G,gBAAgB,GAAA/G,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG2G,qBAAqB,CAACtO,QAAQ,CAAC;EAC/F,OAAO,CAAC0O,gBAAgB,CAAC9K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEjD,KAAK,KAAM;IACxD,GAAGA,KAAK;IACR0E,QAAQ,EAAEmJ,YAAY,CAACzO,QAAQ,EAAEY,KAAK,CAACC,EAAE,EAAE6N,gBAAgB;EAC7D,CAAC,CAAC,CAAC;AACL;AACA,SAASC,6BAA6BA,CAAC3O,QAAQ,EAAEK,MAAM,EAAqE;EAAA,IAAnEuD,QAAQ,GAAA+D,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAAA,IAAE+G,gBAAgB,GAAA/G,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG2G,qBAAqB,CAACtO,QAAQ,CAAC;EACxH,OAAO,CAAC0O,gBAAgB,CAAC9K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEjD,KAAK,IAAK;IACvD,IAAIgO,WAAW,GAAG;MAChB;MACA9J,gBAAgB,EAAElE,KAAK,CAACC,EAAE,KAAK,MAAM,IAAID,KAAK,CAACyC,MAAM,CAACc,aAAa,IAAI,IAAI;MAC3EtD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZmD,IAAI,EAAEpD,KAAK,CAACoD,IAAI;MAChBd,mBAAmB,EAAEtC,KAAK,CAACyC,MAAM,CAACH,mBAAmB;MACrD;MACA;MACAoB,MAAM,EAAE1D,KAAK,CAACyC,MAAM,CAACiB,MAAM,GAAG,MAAOD,IAAI,IAAK;QAC5C,IAAIwK,eAAe,GAAGd,kBAAkB,CACtC1J,IAAI,CAAC6H,OAAO,EACZ,+BACF,CAAC;QACD,IAAI2C,eAAe,IAAI,IAAI,EAAE;UAC3B,IAAI9F,OAAO,GAAG8F,eAAe,GAAGC,SAAS,CAACD,eAAe,CAAC,GAAGA,eAAe;UAC5ErB,UAAU,CAACzE,OAAO,EAAE,oCAAoC,CAAC;UACzD,IAAIgG,UAAU,GAAG,IAAIrJ,WAAW,CAAC,CAAC,CAAC5H,MAAM,CAACiL,OAAO,CAAC;UAClD,IAAIiG,MAAM,GAAG,IAAIC,cAAc,CAAC;YAC9BC,KAAKA,CAACC,UAAU,EAAE;cAChBA,UAAU,CAACC,OAAO,CAACL,UAAU,CAAC;cAC9BI,UAAU,CAACE,KAAK,CAAC,CAAC;YACpB;UACF,CAAC,CAAC;UACF,IAAI5G,OAAO,GAAG,MAAM5K,oBAAoB,CAACmR,MAAM,EAAEM,MAAM,CAAC;UACxD,IAAIzJ,KAAK,GAAG4C,OAAO,CAACnH,KAAK;UACzB,IAAIuE,KAAK,IAAI3I,yBAAyB,IAAI2I,KAAK,EAAE;YAC/C,IAAI8D,MAAM,GAAG9D,KAAK,CAAC3I,yBAAyB,CAAC;YAC7C,IAAImP,IAAI,GAAG;cAAEC,MAAM,EAAE3C,MAAM,CAAC2C;YAAO,CAAC;YACpC,IAAI3C,MAAM,CAAC4F,MAAM,EAAE;cACjB,MAAM3Q,gBAAgB,CAAC+K,MAAM,CAAChL,QAAQ,EAAE0N,IAAI,CAAC;YAC/C,CAAC,MAAM,IAAI1C,MAAM,CAAC9K,OAAO,EAAE;cACzB,MAAMA,OAAO,CAAC8K,MAAM,CAAChL,QAAQ,EAAE0N,IAAI,CAAC;YACtC,CAAC,MAAM;cACL,MAAM1N,QAAQ,CAACgL,MAAM,CAAChL,QAAQ,EAAE0N,IAAI,CAAC;YACvC;UACF,CAAC,MAAM;YACLmB,UAAU,CACR3H,KAAK,IAAIjF,KAAK,CAACC,EAAE,IAAIgF,KAAK,EAC1B,mCACF,CAAC;YACD,IAAI8D,MAAM,GAAG9D,KAAK,CAACjF,KAAK,CAACC,EAAE,CAAC;YAC5B2M,UAAU,CACR,MAAM,IAAI7D,MAAM,EAChB,oCACF,CAAC;YACD,OAAOA,MAAM,CAAC6F,IAAI;UACpB;QACF;QACA,IAAI/D,GAAG,GAAG,MAAMO,gBAAgB,CAACpL,KAAK,CAACyC,MAAM,CAACiB,MAAM,EAAED,IAAI,CAAC;QAC3D,OAAOoH,GAAG;MACZ,CAAC,GAAG,KAAK,CAAC;MACVrH,MAAM,EAAExD,KAAK,CAACyC,MAAM,CAACe,MAAM,GAAIC,IAAI,IAAK2H,gBAAgB,CAACpL,KAAK,CAACyC,MAAM,CAACe,MAAM,EAAEC,IAAI,CAAC,GAAG,KAAK,CAAC;MAC5FE,MAAM,EAAE3D,KAAK,CAACyC,MAAM,CAACkB;IACvB,CAAC;IACD,OAAO3D,KAAK,CAACqD,KAAK,GAAG;MACnBA,KAAK,EAAE,IAAI;MACX,GAAG2K;IACL,CAAC,GAAG;MACFa,aAAa,EAAE7O,KAAK,CAAC6O,aAAa;MAClCnK,QAAQ,EAAEqJ,6BAA6B,CACrC3O,QAAQ,EACRK,MAAM,EACNO,KAAK,CAACC,EAAE,EACR6N,gBACF,CAAC;MACD,GAAGE;IACL,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA,IAAIc,aAAa,GAAG;EAClB,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,QAAQ,EAAE,SAAS;EACnB,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,YAAY,GAAG,oBAAoB;AACvC,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAOA,IAAI,CAAChR,OAAO,CAAC8Q,YAAY,EAAGlP,KAAK,IAAKiP,aAAa,CAACjP,KAAK,CAAC,CAAC;AACpE;;AAEA;AACA,SAASqP,yBAAyBA,CAACC,aAAa,EAAE;EAChD,OAAOH,UAAU,CAACvG,IAAI,CAACC,SAAS,CAACyG,aAAa,CAAC,CAAC;AAClD;;AAEA;AACA,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,SAASC,kBAAkBA,CAACrQ,OAAO,EAAEsQ,KAAK,EAAE;EAC1C,OAAOC,sBAAsB,CAACvQ,OAAO,EAAGwQ,CAAC,IAAK;IAC5C,IAAIxP,KAAK,GAAGsP,KAAK,CAAC9P,MAAM,CAACgQ,CAAC,CAACxP,KAAK,CAACC,EAAE,CAAC;IACpC2M,UAAU,CAAC5M,KAAK,EAAE,kBAAkBwP,CAAC,CAACxP,KAAK,CAACC,EAAE,sBAAsB,CAAC;IACrE,OAAOD,KAAK,CAACyC,MAAM,CAAC8J,OAAO;EAC7B,CAAC,CAAC;AACJ;AACA,SAASgD,sBAAsBA,CAACvQ,OAAO,EAAEyQ,iBAAiB,EAAE;EAC1D,IAAIC,WAAW,GAAG1Q,OAAO,CAACqL,MAAM,GAAGrL,OAAO,CAACc,OAAO,CAAC6P,SAAS,CAAEH,CAAC,IAAKxQ,OAAO,CAACqL,MAAM,CAACmF,CAAC,CAACxP,KAAK,CAACC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;EACpG,IAAIH,OAAO,GAAG4P,WAAW,IAAI,CAAC,GAAG1Q,OAAO,CAACc,OAAO,CAACiG,KAAK,CAAC,CAAC,EAAE2J,WAAW,GAAG,CAAC,CAAC,GAAG1Q,OAAO,CAACc,OAAO;EAC5F,IAAI8P,YAAY;EAChB,IAAIF,WAAW,IAAI,CAAC,EAAE;IACpB,IAAI;MAAEG,aAAa;MAAEC,UAAU;MAAEC,aAAa;MAAEnQ;IAAW,CAAC,GAAGZ,OAAO;IACtEA,OAAO,CAACc,OAAO,CAACiG,KAAK,CAAC2J,WAAW,CAAC,CAACM,IAAI,CAAEnQ,KAAK,IAAK;MACjD,IAAII,EAAE,GAAGJ,KAAK,CAACG,KAAK,CAACC,EAAE;MACvB,IAAI4P,aAAa,CAAC5P,EAAE,CAAC,KAAK,CAAC6P,UAAU,IAAI,CAACA,UAAU,CAACG,cAAc,CAAChQ,EAAE,CAAC,CAAC,EAAE;QACxE2P,YAAY,GAAGC,aAAa,CAAC5P,EAAE,CAAC;MAClC,CAAC,MAAM,IAAI8P,aAAa,CAAC9P,EAAE,CAAC,IAAI,CAACL,UAAU,CAACqQ,cAAc,CAAChQ,EAAE,CAAC,EAAE;QAC9D2P,YAAY,GAAGG,aAAa,CAAC9P,EAAE,CAAC;MAClC;MACA,OAAO2P,YAAY,IAAI,IAAI;IAC7B,CAAC,CAAC;EACJ;EACA,OAAO9P,OAAO,CAAC6J,MAAM,CAAC,CAACuG,aAAa,EAAErQ,KAAK,EAAEsQ,GAAG,KAAK;IACnD,IAAI;MAAElQ;IAAG,CAAC,GAAGJ,KAAK,CAACG,KAAK;IACxB,IAAI+P,aAAa,GAAG/Q,OAAO,CAAC+Q,aAAa,CAAC9P,EAAE,CAAC,IAAI,IAAImQ,OAAO,CAAC,CAAC;IAC9D,IAAIP,aAAa,GAAG7Q,OAAO,CAAC6Q,aAAa,CAAC5P,EAAE,CAAC,IAAI,IAAImQ,OAAO,CAAC,CAAC;IAC9D,IAAIC,mBAAmB,GAAGT,YAAY,IAAI,IAAI,IAAIO,GAAG,KAAKrQ,OAAO,CAAC4G,MAAM,GAAG,CAAC;IAC5E,IAAI4J,mBAAmB,GAAGD,mBAAmB,IAAIT,YAAY,KAAKG,aAAa,IAAIH,YAAY,KAAKC,aAAa;IACjH,IAAIU,SAAS,GAAGd,iBAAiB,CAAC5P,KAAK,CAAC;IACxC,IAAI0Q,SAAS,IAAI,IAAI,EAAE;MACrB,IAAIC,QAAQ,GAAG,IAAIJ,OAAO,CAACF,aAAa,CAAC;MACzC,IAAII,mBAAmB,EAAE;QACvBG,cAAc,CAACb,YAAY,EAAEY,QAAQ,CAAC;MACxC;MACAC,cAAc,CAACZ,aAAa,EAAEW,QAAQ,CAAC;MACvCC,cAAc,CAACV,aAAa,EAAES,QAAQ,CAAC;MACvC,OAAOA,QAAQ;IACjB;IACA,IAAIjE,OAAO,GAAG,IAAI6D,OAAO,CACvB,OAAOG,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC;MAC1CR,aAAa;MACbG,aAAa;MACbL,aAAa;MACbD,YAAY,EAAES,mBAAmB,GAAGT,YAAY,GAAG,KAAK;IAC1D,CAAC,CAAC,GAAGW,SACP,CAAC;IACD,IAAID,mBAAmB,EAAE;MACvBG,cAAc,CAACb,YAAY,EAAErD,OAAO,CAAC;IACvC;IACAkE,cAAc,CAACZ,aAAa,EAAEtD,OAAO,CAAC;IACtCkE,cAAc,CAACV,aAAa,EAAExD,OAAO,CAAC;IACtCkE,cAAc,CAACP,aAAa,EAAE3D,OAAO,CAAC;IACtC,OAAOA,OAAO;EAChB,CAAC,EAAE,IAAI6D,OAAO,CAAC,CAAC,CAAC;AACnB;AACA,SAASK,cAAcA,CAACP,aAAa,EAAEQ,YAAY,EAAE;EACnD,IAAIC,qBAAqB,GAAGT,aAAa,CAAC1C,GAAG,CAAC,YAAY,CAAC;EAC3D,IAAImD,qBAAqB,EAAE;IACzB,IAAI/I,OAAO,GAAGwH,kBAAkB,CAACuB,qBAAqB,CAAC;IACvD,IAAIC,YAAY,GAAG,IAAIC,GAAG,CAACH,YAAY,CAACI,YAAY,CAAC,CAAC,CAAC;IACvDlJ,OAAO,CAACgG,OAAO,CAAE/H,MAAM,IAAK;MAC1B,IAAI,CAAC+K,YAAY,CAACG,GAAG,CAAClL,MAAM,CAAC,EAAE;QAC7B6K,YAAY,CAACtE,MAAM,CAAC,YAAY,EAAEvG,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,IAAImL,2BAA2B,GAAG,eAAgB,IAAIH,GAAG,CAAC,CACxD,GAAG7U,oBAAoB,EACvB,GAAG,CACJ,CAAC;AACF,eAAeiV,iBAAiBA,CAAC3B,KAAK,EAAErF,UAAU,EAAEiH,aAAa,EAAE5F,OAAO,EAAE6F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EAChH,IAAI;IACF,IAAIC,QAAQ,GAAG,SAAAA,CAAStS,OAAO,EAAE;MAC/B,IAAIuN,OAAO,GAAG8C,kBAAkB,CAACrQ,OAAO,EAAEsQ,KAAK,CAAC;MAChD,IAAI5R,oBAAoB,CAACsB,OAAO,CAACuS,UAAU,CAAC,IAAIhF,OAAO,CAACwE,GAAG,CAAC,UAAU,CAAC,EAAE;QACvE,OAAOS,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAErF,UAAU,EAAE;UAC7DlB,MAAM,EAAE0I,sBAAsB,CAC5BzS,OAAO,CAACuS,UAAU,EAClBhF,OAAO,EACP+C,KAAK,CAACpE,QACR,CAAC;UACDqB,OAAO;UACPb,MAAM,EAAErP;QACV,CAAC,CAAC;MACJ;MACA,IAAI2C,OAAO,CAACqL,MAAM,EAAE;QAClBZ,MAAM,CAACkE,MAAM,CAAC3O,OAAO,CAACqL,MAAM,CAAC,CAACuD,OAAO,CAAE8D,GAAG,IAAK;UAC7C,IAAI,CAAC9T,oBAAoB,CAAC8T,GAAG,CAAC,IAAIA,GAAG,CAACtL,KAAK,EAAE;YAC3CiL,WAAW,CAACK,GAAG,CAAC;UAClB;QACF,CAAC,CAAC;QACF1S,OAAO,CAACqL,MAAM,GAAGD,cAAc,CAACpL,OAAO,CAACqL,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAI0H,iBAAiB;MACrB,IAAI3S,OAAO,CAACqL,MAAM,EAAE;QAClBsH,iBAAiB,GAAG;UAAEvL,KAAK,EAAEqD,MAAM,CAACkE,MAAM,CAAC3O,OAAO,CAACqL,MAAM,CAAC,CAAC,CAAC;QAAE,CAAC;MACjE,CAAC,MAAM;QACLsH,iBAAiB,GAAG;UAClB/C,IAAI,EAAEnF,MAAM,CAACkE,MAAM,CAAC3O,OAAO,CAAC8Q,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;MACH;MACA,OAAO0B,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAErF,UAAU,EAAE;QAC7DlB,MAAM,EAAE4I,iBAAiB;QACzBpF,OAAO;QACPb,MAAM,EAAE1M,OAAO,CAACuS;MAClB,CAAC,CAAC;IACJ,CAAC;IACD,IAAIK,OAAO,GAAGN,QAAQ;IACtB,IAAIO,cAAc,GAAG,IAAInF,OAAO,CAACyE,UAAU,EAAE;MAC3C9E,MAAM,EAAEf,OAAO,CAACe,MAAM;MACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;MAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB,MAAM;MACtB,IAAGlB,OAAO,CAACgB,IAAI,GAAG;QAAEG,MAAM,EAAE;MAAO,CAAC,GAAG,KAAK,CAAC;IAC/C,CAAC,CAAC;IACF,IAAI1D,MAAM,GAAG,MAAMmI,aAAa,CAACY,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEX,WAAW;MAC3BY,uBAAuB,EAAE,IAAI;MAC7BC,gBAAgB,EAAE,IAAI;MACtBC,gBAAgB,EAAEZ;IACpB,CAAC,CAAC;IACF,IAAI,CAAC3T,UAAU,CAACoL,MAAM,CAAC,EAAE;MACvBA,MAAM,GAAGuI,QAAQ,CAACvI,MAAM,CAAC;IAC3B;IACA,IAAItL,kBAAkB,CAACsL,MAAM,CAAC,EAAE;MAC9B,OAAOyI,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAErF,UAAU,EAAE;QAC7DlB,MAAM,EAAE0I,sBAAsB,CAC5B1I,MAAM,CAAC2C,MAAM,EACb3C,MAAM,CAACwD,OAAO,EACd+C,KAAK,CAACpE,QACR,CAAC;QACDqB,OAAO,EAAExD,MAAM,CAACwD,OAAO;QACvBb,MAAM,EAAErP;MACV,CAAC,CAAC;IACJ;IACA,OAAO0M,MAAM;EACf,CAAC,CAAC,OAAO3C,KAAK,EAAE;IACdiL,WAAW,CAACjL,KAAK,CAAC;IAClB,OAAOoL,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAErF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAE3C;MAAM,CAAC;MACjBmG,OAAO,EAAE,IAAI6D,OAAO,CAAC,CAAC;MACtB1E,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF;AACA,eAAeyG,kBAAkBA,CAAC7C,KAAK,EAAErF,UAAU,EAAEiH,aAAa,EAAE5F,OAAO,EAAE6F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAI;IACF,IAAIC,QAAQ,GAAG,SAAAA,CAAStS,OAAO,EAAE;MAC/B,IAAIuN,OAAO,GAAG8C,kBAAkB,CAACrQ,OAAO,EAAEsQ,KAAK,CAAC;MAChD,IAAI5R,oBAAoB,CAACsB,OAAO,CAACuS,UAAU,CAAC,IAAIhF,OAAO,CAACwE,GAAG,CAAC,UAAU,CAAC,EAAE;QACvE,OAAOS,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAErF,UAAU,EAAE;UAC7DlB,MAAM,EAAE;YACN,CAACzM,yBAAyB,GAAGmV,sBAAsB,CACjDzS,OAAO,CAACuS,UAAU,EAClBhF,OAAO,EACP+C,KAAK,CAACpE,QACR;UACF,CAAC;UACDqB,OAAO;UACPb,MAAM,EAAErP;QACV,CAAC,CAAC;MACJ;MACA,IAAI2C,OAAO,CAACqL,MAAM,EAAE;QAClBZ,MAAM,CAACkE,MAAM,CAAC3O,OAAO,CAACqL,MAAM,CAAC,CAACuD,OAAO,CAAE8D,GAAG,IAAK;UAC7C,IAAI,CAAC9T,oBAAoB,CAAC8T,GAAG,CAAC,IAAIA,GAAG,CAACtL,KAAK,EAAE;YAC3CiL,WAAW,CAACK,GAAG,CAAC;UAClB;QACF,CAAC,CAAC;QACF1S,OAAO,CAACqL,MAAM,GAAGD,cAAc,CAACpL,OAAO,CAACqL,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAImI,OAAO,GAAG,CAAC,CAAC;MAChB,IAAIC,aAAa,GAAG,IAAIxB,GAAG,CACzB7R,OAAO,CAACc,OAAO,CAACwS,MAAM,CACnB9C,CAAC,IAAK+C,YAAY,GAAGA,YAAY,CAACxB,GAAG,CAACvB,CAAC,CAACxP,KAAK,CAACC,EAAE,CAAC,GAAGuP,CAAC,CAACxP,KAAK,CAAC0D,MAAM,IAAI,IACzE,CAAC,CAACT,GAAG,CAAEuM,CAAC,IAAKA,CAAC,CAACxP,KAAK,CAACC,EAAE,CACzB,CAAC;MACD,IAAIjB,OAAO,CAACqL,MAAM,EAAE;QAClB,KAAK,IAAI,CAACpK,EAAE,EAAEmG,KAAK,CAAC,IAAIqD,MAAM,CAACa,OAAO,CAACtL,OAAO,CAACqL,MAAM,CAAC,EAAE;UACtD+H,OAAO,CAACnS,EAAE,CAAC,GAAG;YAAEmG;UAAM,CAAC;QACzB;MACF;MACA,KAAK,IAAI,CAACnG,EAAE,EAAEgF,KAAK,CAAC,IAAIwE,MAAM,CAACa,OAAO,CAACtL,OAAO,CAACY,UAAU,CAAC,EAAE;QAC1D,IAAI,EAAEK,EAAE,IAAImS,OAAO,CAAC,IAAIC,aAAa,CAACtB,GAAG,CAAC9Q,EAAE,CAAC,EAAE;UAC7CmS,OAAO,CAACnS,EAAE,CAAC,GAAG;YAAE2O,IAAI,EAAE3J;UAAM,CAAC;QAC/B;MACF;MACA,OAAOuM,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAErF,UAAU,EAAE;QAC7DlB,MAAM,EAAEqJ,OAAO;QACf7F,OAAO;QACPb,MAAM,EAAE1M,OAAO,CAACuS;MAClB,CAAC,CAAC;IACJ,CAAC;IACD,IAAIK,OAAO,GAAGN,QAAQ;IACtB,IAAIO,cAAc,GAAG,IAAInF,OAAO,CAACyE,UAAU,EAAE;MAC3C5E,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB;IAClB,CAAC,CAAC;IACF,IAAIgG,WAAW,GAAG,IAAIrT,GAAG,CAACmM,OAAO,CAACrM,GAAG,CAAC,CAAC4M,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC;IAClE,IAAI+E,YAAY,GAAGC,WAAW,GAAG,IAAI3B,GAAG,CAAC2B,WAAW,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IACvE,IAAI1J,MAAM,GAAG,MAAMmI,aAAa,CAACY,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEX,WAAW;MAC3BsB,mBAAmB,EAAGlD,CAAC,IAAK,CAAC+C,YAAY,IAAIA,YAAY,CAACxB,GAAG,CAACvB,CAAC,CAACxP,KAAK,CAACC,EAAE,CAAC;MACzE+R,uBAAuB,EAAE,IAAI;MAC7BE,gBAAgB,EAAEZ;IACpB,CAAC,CAAC;IACF,IAAI,CAAC3T,UAAU,CAACoL,MAAM,CAAC,EAAE;MACvBA,MAAM,GAAGuI,QAAQ,CAACvI,MAAM,CAAC;IAC3B;IACA,IAAItL,kBAAkB,CAACsL,MAAM,CAAC,EAAE;MAC9B,OAAOyI,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAErF,UAAU,EAAE;QAC7DlB,MAAM,EAAE;UACN,CAACzM,yBAAyB,GAAGmV,sBAAsB,CACjD1I,MAAM,CAAC2C,MAAM,EACb3C,MAAM,CAACwD,OAAO,EACd+C,KAAK,CAACpE,QACR;QACF,CAAC;QACDqB,OAAO,EAAExD,MAAM,CAACwD,OAAO;QACvBb,MAAM,EAAErP;MACV,CAAC,CAAC;IACJ;IACA,OAAO0M,MAAM;EACf,CAAC,CAAC,OAAO3C,KAAK,EAAE;IACdiL,WAAW,CAACjL,KAAK,CAAC;IAClB,OAAOoL,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAErF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAE4J,IAAI,EAAE;UAAEvM;QAAM;MAAE,CAAC;MAC3BmG,OAAO,EAAE,IAAI6D,OAAO,CAAC,CAAC;MACtB1E,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF;AACA,SAAS8F,2BAA2BA,CAAClG,OAAO,EAAEgE,KAAK,EAAErF,UAAU,EAAA2I,KAAA,EAI5D;EAAA,IAJ8D;IAC/D7J,MAAM;IACNwD,OAAO;IACPb;EACF,CAAC,GAAAkH,KAAA;EACC,IAAIC,aAAa,GAAG,IAAIzC,OAAO,CAAC7D,OAAO,CAAC;EACxCsG,aAAa,CAACC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC;EAC5C,IAAI9B,2BAA2B,CAACD,GAAG,CAACrF,MAAM,CAAC,EAAE;IAC3C,OAAO,IAAIC,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM;MAAEa,OAAO,EAAEsG;IAAc,CAAC,CAAC;EAC/D;EACAA,aAAa,CAACC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAClDD,aAAa,CAAC9G,MAAM,CAAC,gBAAgB,CAAC;EACtC,OAAO,IAAIJ,QAAQ,CACjBoH,oBAAoB,CAClBhK,MAAM,EACNuC,OAAO,CAACkB,MAAM,EACd8C,KAAK,CAAC/M,KAAK,CAACE,MAAM,CAACuQ,aAAa,EAChC/I,UACF,CAAC,EACD;IACEyB,MAAM,EAAEA,MAAM,IAAI,GAAG;IACrBa,OAAO,EAAEsG;EACX,CACF,CAAC;AACH;AACA,SAASpB,sBAAsBA,CAAC/F,MAAM,EAAEa,OAAO,EAAErB,QAAQ,EAAE;EACzD,IAAI+H,SAAS,GAAG1G,OAAO,CAACiB,GAAG,CAAC,UAAU,CAAC;EACvC,IAAItC,QAAQ,EAAE;IACZ+H,SAAS,GAAG7U,aAAa,CAAC6U,SAAS,EAAE/H,QAAQ,CAAC,IAAI+H,SAAS;EAC7D;EACA,OAAO;IACLlV,QAAQ,EAAEkV,SAAS;IACnBvH,MAAM;IACNwH,UAAU;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA3G,OAAO,CAACwE,GAAG,CAAC,oBAAoB,CAAC,IAAIxE,OAAO,CAACwE,GAAG,CAAC,YAAY,CAC9D;IACDpC,MAAM,EAAEpC,OAAO,CAACwE,GAAG,CAAC,yBAAyB,CAAC;IAC9C9S,OAAO,EAAEsO,OAAO,CAACwE,GAAG,CAAC,iBAAiB;EACxC,CAAC;AACH;AACA,SAASgC,oBAAoBA,CAAC9N,KAAK,EAAEkO,aAAa,EAAEH,aAAa,EAAE/I,UAAU,EAAE;EAC7E,IAAIsE,UAAU,GAAG,IAAI6E,eAAe,CAAC,CAAC;EACtC,IAAIC,SAAS,GAAGC,UAAU,CACxB,MAAM/E,UAAU,CAACgF,KAAK,CAAC,IAAIrQ,KAAK,CAAC,gBAAgB,CAAC,CAAC,EACnD,OAAO8P,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG,IACtD,CAAC;EACDG,aAAa,CAACK,gBAAgB,CAAC,OAAO,EAAE,MAAMC,YAAY,CAACJ,SAAS,CAAC,CAAC;EACtE,OAAOnW,MAAM,CAAC+H,KAAK,EAAE;IACnBuH,MAAM,EAAE+B,UAAU,CAAC/B,MAAM;IACzBkH,OAAO,EAAE,CACNhT,KAAK,IAAK;MACT,IAAIA,KAAK,YAAYwC,KAAK,EAAE;QAC1B,IAAI;UAAEqD,IAAI;UAAEmE,OAAO;UAAEP;QAAM,CAAC,GAAGF,UAAU,KAAK,YAAY,CAAC,mBAAmBD,aAAa,CAACtJ,KAAK,EAAEuJ,UAAU,CAAC,GAAGvJ,KAAK;QACtH,OAAO,CAAC,gBAAgB,EAAE6F,IAAI,EAAEmE,OAAO,EAAEP,KAAK,CAAC;MACjD;MACA,IAAIzJ,KAAK,YAAY5E,iBAAiB,EAAE;QACtC,IAAI;UAAE8S,IAAI,EAAE+E,KAAK;UAAEjI,MAAM;UAAEkI;QAAW,CAAC,GAAGlT,KAAK;QAC/C,OAAO,CAAC,eAAe,EAAEiT,KAAK,EAAEjI,MAAM,EAAEkI,UAAU,CAAC;MACrD;MACA,IAAIlT,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIpE,yBAAyB,IAAIoE,KAAK,EAAE;QAC5E,OAAO,CAAC,qBAAqB,EAAEA,KAAK,CAACpE,yBAAyB,CAAC,CAAC;MAClE;IACF,CAAC,CACF;IACDuX,WAAW,EAAE,CACVnT,KAAK,IAAK;MACT,IAAI,CAACA,KAAK,EAAE;MACZ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC/B,OAAO,CACL,0BAA0B,EAC1B+I,MAAM,CAACqK,WAAW,CAACrK,MAAM,CAACa,OAAO,CAAC5J,KAAK,CAAC,CAAC,CAC1C;IACH,CAAC,EACD,MAAM,CAAC,qBAAqB,CAAC;EAEjC,CAAC,CAAC;AACJ;;AAEA;AACA,SAASqT,MAAMA,CAACzE,KAAK,EAAE3M,IAAI,EAAE;EAC3B,IAAInD,MAAM,GAAGqO,YAAY,CAACyB,KAAK,CAAC9P,MAAM,CAAC;EACvC,IAAIwU,UAAU,GAAGjG,6BAA6B,CAACuB,KAAK,CAAC9P,MAAM,EAAE8P,KAAK,CAAC7P,MAAM,CAAC;EAC1E,IAAIwK,UAAU,GAAGF,YAAY,CAACpH,IAAI,CAAC,GAAGA,IAAI,GAAG,YAAY,CAAC;EAC1D,IAAIuO,aAAa,GAAGnU,mBAAmB,CAACiX,UAAU,EAAE;IAClD9I,QAAQ,EAAEoE,KAAK,CAACpE;EAClB,CAAC,CAAC;EACF,IAAI+I,YAAY,GAAG3E,KAAK,CAAC/M,KAAK,CAACE,MAAM,CAAC4O,WAAW,KAAK,CAACjL,KAAK,EAAA8N,KAAA,KAAkB;IAAA,IAAhB;MAAE5I;IAAQ,CAAC,GAAA4I,KAAA;IACvE,IAAIjK,UAAU,KAAK,MAAM,CAAC,cAAc,CAACqB,OAAO,CAACkB,MAAM,CAAC2H,OAAO,EAAE;MAC/DtH,OAAO,CAACzG,KAAK;MACX;MACAxI,oBAAoB,CAACwI,KAAK,CAAC,IAAIA,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,GAAGA,KAC7D,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAO;IACL5G,MAAM;IACNwU,UAAU;IACV/J,UAAU;IACViH,aAAa;IACb+C;EACF,CAAC;AACH;AACA,IAAIG,oBAAoB,GAAGA,CAAC9E,KAAK,EAAE3M,IAAI,KAAK;EAC1C,IAAI0R,MAAM;EACV,IAAI7U,MAAM;EACV,IAAIyK,UAAU;EACd,IAAIiH,aAAa;EACjB,IAAI+C,YAAY;EAChB,OAAO,eAAeK,cAAcA,CAAChJ,OAAO,EAAEiJ,cAAc,EAAE;IAC5DF,MAAM,GAAG,OAAO/E,KAAK,KAAK,UAAU,GAAG,MAAMA,KAAK,CAAC,CAAC,GAAGA,KAAK;IAC5D,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAC/B,IAAIkF,OAAO,GAAGT,MAAM,CAACM,MAAM,EAAE1R,IAAI,CAAC;MAClCnD,MAAM,GAAGgV,OAAO,CAAChV,MAAM;MACvByK,UAAU,GAAGuK,OAAO,CAACvK,UAAU;MAC/BiH,aAAa,GAAGsD,OAAO,CAACtD,aAAa;MACrC+C,YAAY,GAAGO,OAAO,CAACP,YAAY;IACrC,CAAC,MAAM,IAAI,CAACzU,MAAM,IAAI,CAACyK,UAAU,IAAI,CAACiH,aAAa,IAAI,CAAC+C,YAAY,EAAE;MACpE,IAAIO,OAAO,GAAGT,MAAM,CAACM,MAAM,EAAE1R,IAAI,CAAC;MAClCnD,MAAM,GAAGgV,OAAO,CAAChV,MAAM;MACvByK,UAAU,GAAGuK,OAAO,CAACvK,UAAU;MAC/BiH,aAAa,GAAGsD,OAAO,CAACtD,aAAa;MACrC+C,YAAY,GAAGO,OAAO,CAACP,YAAY;IACrC;IACA,IAAI9I,MAAM,GAAG,CAAC,CAAC;IACf,IAAIiG,WAAW;IACf,IAAIC,WAAW,GAAIjL,KAAK,IAAK;MAC3B,IAAIzD,IAAI,KAAK,aAAa,CAAC,mBAAmB;QAC5CuK,iBAAiB,CAAC,CAAC,EAAEuH,mBAAmB,GAAGrO,KAAK,CAAC;MACnD;MACA6N,YAAY,CAAC7N,KAAK,EAAE;QAClBpH,OAAO,EAAEoS,WAAW;QACpBjG,MAAM;QACNG;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAI+I,MAAM,CAAC5U,MAAM,CAAC6C,mBAAmB,EAAE;MACrC,IAAIiS,cAAc,IAAI,IAAI,EAAE;QAC1BnD,WAAW,GAAG,IAAI9S,8BAA8B,CAAC,CAAC;MACpD,CAAC,MAAM;QACL,IAAI;UACF8S,WAAW,GAAG,IAAI9S,8BAA8B,CAC9CiW,cACF,CAAC;QACH,CAAC,CAAC,OAAO9G,CAAC,EAAE;UACV,IAAIrH,KAAK,GAAG,IAAIlD,KAAK,CACnB;AACZ;AACA,SAASuK,CAAC,YAAYvK,KAAK,GAAGuK,CAAC,CAAC3E,QAAQ,CAAC,CAAC,GAAG2E,CAAC,EACpC,CAAC;UACD4D,WAAW,CAACjL,KAAK,CAAC;UAClB,OAAOsO,6BAA6B,CAACtO,KAAK,EAAE6D,UAAU,CAAC;QACzD;MACF;IACF,CAAC,MAAM;MACLmH,WAAW,GAAGmD,cAAc,IAAI,CAAC,CAAC;IACpC;IACA,IAAItV,GAAG,GAAG,IAAIE,GAAG,CAACmM,OAAO,CAACrM,GAAG,CAAC;IAC9B,IAAI0V,kBAAkB,GAAGN,MAAM,CAACnJ,QAAQ,IAAI,GAAG;IAC/C,IAAI0J,cAAc,GAAG3V,GAAG,CAACgM,QAAQ;IACjC,IAAI7M,aAAa,CAACwW,cAAc,EAAED,kBAAkB,CAAC,KAAK,aAAa,EAAE;MACvEC,cAAc,GAAGD,kBAAkB;IACrC,CAAC,MAAM,IAAIC,cAAc,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC3CD,cAAc,GAAGA,cAAc,CAAC3W,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IACxD;IACA,IAAIG,aAAa,CAACwW,cAAc,EAAED,kBAAkB,CAAC,KAAK,GAAG,IAAIC,cAAc,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7FD,cAAc,GAAGA,cAAc,CAAC7O,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C;IACA,IAAIrG,SAAS,GAAGyN,kBAAkB,CAAC7B,OAAO,EAAE,yBAAyB,CAAC,KAAK,KAAK;IAChF,IAAI,CAAC+I,MAAM,CAAC1T,GAAG,EAAE;MACf,IAAImU,WAAW,GAAG5G,SAAS,CAAC0G,cAAc,CAAC;MAC3C,IAAIP,MAAM,CAACU,SAAS,CAACrO,MAAM,KAAK,CAAC,EAAE;QACjChH,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAI,CAAC2U,MAAM,CAACU,SAAS,CAACC,QAAQ,CAACF,WAAW,CAAC,IAAI,CAACT,MAAM,CAACU,SAAS,CAACC,QAAQ,CAACF,WAAW,GAAG,GAAG,CAAC,EAAE;QACnG,IAAI7V,GAAG,CAACgM,QAAQ,CAAC4J,QAAQ,CAAC,OAAO,CAAC,EAAE;UAClCZ,YAAY,CACV,IAAInY,iBAAiB,CACnB,GAAG,EACH,WAAW,EACX,8BAA8BgZ,WAAW,oIAC3C,CAAC,EACD;YACE9V,OAAO,EAAEoS,WAAW;YACpBjG,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXkI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ,CAAC,MAAM;UACLlU,SAAS,GAAG,IAAI;QAClB;MACF;IACF;IACA,IAAIuV,WAAW,GAAG9X,eAAe,CAC/BkX,MAAM,CAACzT,cAAc,CAACgC,YAAY,EAClC+R,kBACF,CAAC;IACD,IAAI1V,GAAG,CAACgM,QAAQ,KAAKgK,WAAW,EAAE;MAChC,IAAI;QACF,IAAIC,GAAG,GAAG,MAAMC,qBAAqB,CAACd,MAAM,EAAE7U,MAAM,EAAEP,GAAG,CAAC;QAC1D,OAAOiW,GAAG;MACZ,CAAC,CAAC,OAAOzH,CAAC,EAAE;QACV4D,WAAW,CAAC5D,CAAC,CAAC;QACd,OAAO,IAAI9B,QAAQ,CAAC,sBAAsB,EAAE;UAAED,MAAM,EAAE;QAAI,CAAC,CAAC;MAC9D;IACF;IACA,IAAI5L,OAAO,GAAGkL,iBAAiB,CAACxL,MAAM,EAAEoV,cAAc,EAAEP,MAAM,CAACnJ,QAAQ,CAAC;IACxE,IAAIpL,OAAO,IAAIA,OAAO,CAAC4G,MAAM,GAAG,CAAC,EAAE;MACjC+C,MAAM,CAACgB,MAAM,CAACU,MAAM,EAAErL,OAAO,CAAC,CAAC,CAAC,CAACqL,MAAM,CAAC;IAC1C;IACA,IAAIiK,QAAQ;IACZ,IAAInW,GAAG,CAACgM,QAAQ,CAAC4J,QAAQ,CAAC,OAAO,CAAC,EAAE;MAClC,IAAI1D,UAAU,GAAG,IAAIhS,GAAG,CAACmM,OAAO,CAACrM,GAAG,CAAC;MACrCkS,UAAU,CAAClG,QAAQ,GAAG2J,cAAc;MACpC,IAAIS,kBAAkB,GAAGrK,iBAAiB,CACxCxL,MAAM,EACN2R,UAAU,CAAClG,QAAQ,EACnBoJ,MAAM,CAACnJ,QACT,CAAC;MACDkK,QAAQ,GAAG,MAAME,wBAAwB,CACvCrL,UAAU,EACVoK,MAAM,EACNnD,aAAa,EACb5F,OAAO,EACP6F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;MACD,IAAIgD,MAAM,CAAC9R,KAAK,CAACE,MAAM,CAAC8S,iBAAiB,EAAE;QACzCH,QAAQ,GAAG,MAAMf,MAAM,CAAC9R,KAAK,CAACE,MAAM,CAAC8S,iBAAiB,CAACH,QAAQ,EAAE;UAC/DpW,OAAO,EAAEoS,WAAW;UACpBjG,MAAM,EAAEkK,kBAAkB,GAAGA,kBAAkB,CAAC,CAAC,CAAC,CAAClK,MAAM,GAAG,CAAC,CAAC;UAC9DG;QACF,CAAC,CAAC;QACF,IAAI7N,kBAAkB,CAAC2X,QAAQ,CAAC,EAAE;UAChC,IAAIrM,MAAM,GAAG0I,sBAAsB,CACjC2D,QAAQ,CAAC1J,MAAM,EACf0J,QAAQ,CAAC7I,OAAO,EAChB8H,MAAM,CAACnJ,QACT,CAAC;UACD,IAAII,OAAO,CAACe,MAAM,KAAK,KAAK,EAAE;YAC5BtD,MAAM,GAAG;cACP,CAACzM,yBAAyB,GAAGyM;YAC/B,CAAC;UACH;UACA,IAAIwD,OAAO,GAAG,IAAI6D,OAAO,CAACgF,QAAQ,CAAC7I,OAAO,CAAC;UAC3CA,OAAO,CAACuG,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;UAC5C,OAAO,IAAInH,QAAQ,CACjBoH,oBAAoB,CAClBhK,MAAM,EACNuC,OAAO,CAACkB,MAAM,EACd6H,MAAM,CAAC9R,KAAK,CAACE,MAAM,CAACuQ,aAAa,EACjC/I,UACF,CAAC,EACD;YACEyB,MAAM,EAAErP,4BAA4B;YACpCkQ;UACF,CACF,CAAC;QACH;MACF;IACF,CAAC,MAAM,IAAI,CAAC7M,SAAS,IAAII,OAAO,IAAIA,OAAO,CAACA,OAAO,CAAC4G,MAAM,GAAG,CAAC,CAAC,CAAC1G,KAAK,CAACyC,MAAM,CAAC8B,OAAO,IAAI,IAAI,IAAIzE,OAAO,CAACA,OAAO,CAAC4G,MAAM,GAAG,CAAC,CAAC,CAAC1G,KAAK,CAACyC,MAAM,CAACc,aAAa,IAAI,IAAI,EAAE;MAC9J6R,QAAQ,GAAG,MAAMI,qBAAqB,CACpCvL,UAAU,EACVoK,MAAM,EACNnD,aAAa,EACbpR,OAAO,CAACiG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC/F,KAAK,CAACC,EAAE,EAC7BqL,OAAO,EACP8F,WAAW,EACXC,WACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI;QAAEpG;MAAS,CAAC,GAAGhM,GAAG;MACtB,IAAIK,WAAW,GAAG,KAAK,CAAC;MACxB,IAAI+U,MAAM,CAACoB,uBAAuB,EAAE;QAClCnW,WAAW,GAAG,MAAM+U,MAAM,CAACoB,uBAAuB,CAAC;UAAExK;QAAS,CAAC,CAAC;MAClE,CAAC,MAAM,IAAItI,IAAI,KAAK,aAAa,CAAC,qBAAqBuK,iBAAiB,CAAC,CAAC,EAAEwI,cAAc,EAAE;QAC1FpW,WAAW,GAAG,MAAM4N,iBAAiB,CAAC,CAAC,EAAEwI,cAAc,GAAGzK,QAAQ,CAAC;MACrE;MACAmK,QAAQ,GAAG,MAAMO,qBAAqB,CACpC1L,UAAU,EACVoK,MAAM,EACNnD,aAAa,EACb5F,OAAO,EACP8F,WAAW,EACXC,WAAW,EACX3R,SAAS,EACTJ,WACF,CAAC;IACH;IACA,IAAIgM,OAAO,CAACe,MAAM,KAAK,MAAM,EAAE;MAC7B,OAAO,IAAIV,QAAQ,CAAC,IAAI,EAAE;QACxBY,OAAO,EAAE6I,QAAQ,CAAC7I,OAAO;QACzBb,MAAM,EAAE0J,QAAQ,CAAC1J,MAAM;QACvBkI,UAAU,EAAEwB,QAAQ,CAACxB;MACvB,CAAC,CAAC;IACJ;IACA,OAAOwB,QAAQ;EACjB,CAAC;AACH,CAAC;AACD,eAAeD,qBAAqBA,CAAC7F,KAAK,EAAE9P,MAAM,EAAEP,GAAG,EAAE;EACvD,IAAIqQ,KAAK,CAACsG,MAAM,CAAClT,OAAO,KAAKzD,GAAG,CAAC4M,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC,EAAE;IAC5D,OAAO,IAAI7B,QAAQ,CAAC,IAAI,EAAE;MACxBD,MAAM,EAAE,GAAG;MACXa,OAAO,EAAE;QACP,yBAAyB,EAAE;MAC7B;IACF,CAAC,CAAC;EACJ;EACA,IAAIsJ,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI5W,GAAG,CAAC4M,YAAY,CAACkF,GAAG,CAAC,GAAG,CAAC,EAAE;IAC7B,IAAI+E,KAAK,GAAG,eAAgB,IAAIjF,GAAG,CAAC,CAAC;IACrC5R,GAAG,CAAC4M,YAAY,CAACC,MAAM,CAAC,GAAG,CAAC,CAAC8B,OAAO,CAAExK,IAAI,IAAK;MAC7C,IAAI,CAACA,IAAI,CAAC2S,UAAU,CAAC,GAAG,CAAC,EAAE;QACzB3S,IAAI,GAAG,IAAIA,IAAI,EAAE;MACnB;MACA,IAAI4S,QAAQ,GAAG5S,IAAI,CAACqP,KAAK,CAAC,GAAG,CAAC,CAAC1M,KAAK,CAAC,CAAC,CAAC;MACvCiQ,QAAQ,CAACpI,OAAO,CAAC,CAACqI,CAAC,EAAEtP,CAAC,KAAK;QACzB,IAAIuP,WAAW,GAAGF,QAAQ,CAACjQ,KAAK,CAAC,CAAC,EAAEY,CAAC,GAAG,CAAC,CAAC,CAACwP,IAAI,CAAC,GAAG,CAAC;QACpDL,KAAK,CAACM,GAAG,CAAC,IAAIF,WAAW,EAAE,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,KAAK,IAAI9S,IAAI,IAAI0S,KAAK,EAAE;MACtB,IAAIhW,OAAO,GAAGkL,iBAAiB,CAACxL,MAAM,EAAE4D,IAAI,EAAEkM,KAAK,CAACpE,QAAQ,CAAC;MAC7D,IAAIpL,OAAO,EAAE;QACX,KAAK,IAAID,KAAK,IAAIC,OAAO,EAAE;UACzB,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;UAC5B,IAAID,KAAK,GAAGsP,KAAK,CAACsG,MAAM,CAACpW,MAAM,CAACO,OAAO,CAAC;UACxC,IAAIC,KAAK,EAAE;YACT6V,OAAO,CAAC9V,OAAO,CAAC,GAAGC,KAAK;UAC1B;QACF;MACF;IACF;IACA,OAAO2L,QAAQ,CAAC0K,IAAI,CAACR,OAAO,EAAE;MAC5BtJ,OAAO,EAAE;QACP,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAIZ,QAAQ,CAAC,iBAAiB,EAAE;IAAED,MAAM,EAAE;EAAI,CAAC,CAAC;AACzD;AACA,eAAe4J,wBAAwBA,CAACrL,UAAU,EAAEqF,KAAK,EAAE4B,aAAa,EAAE5F,OAAO,EAAE6F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACvH,IAAI+D,QAAQ,GAAG9J,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG,MAAM4E,iBAAiB,CAC/D3B,KAAK,EACLrF,UAAU,EACViH,aAAa,EACb5F,OAAO,EACP6F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC,GAAG,MAAMc,kBAAkB,CAC1B7C,KAAK,EACLrF,UAAU,EACViH,aAAa,EACb5F,OAAO,EACP6F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;EACD,OAAO+D,QAAQ;AACjB;AACA,eAAeO,qBAAqBA,CAAC1L,UAAU,EAAEqF,KAAK,EAAE4B,aAAa,EAAE5F,OAAO,EAAE8F,WAAW,EAAEC,WAAW,EAAE3R,SAAS,EAAEJ,WAAW,EAAE;EAChI,IAAI;IACF,IAAI8V,QAAQ,GAAG,MAAMlE,aAAa,CAACY,KAAK,CAACxG,OAAO,EAAE;MAChDyG,cAAc,EAAEX,WAAW;MAC3Bc,gBAAgB,EAAE5C,KAAK,CAAC7P,MAAM,CAAC6C,mBAAmB,GAAIgU,GAAG,IAAKC,UAAU,CAACD,GAAG,EAAE5W,SAAS,CAAC,GAAG,KAAK;IAClG,CAAC,CAAC;IACF,OAAO/B,UAAU,CAACyX,QAAQ,CAAC,GAAGA,QAAQ,GAAGmB,UAAU,CAACnB,QAAQ,EAAE1V,SAAS,CAAC;EAC1E,CAAC,CAAC,OAAO0G,KAAK,EAAE;IACdiL,WAAW,CAACjL,KAAK,CAAC;IAClB,OAAO,IAAIuF,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM,EAAE;IAAI,CAAC,CAAC;EAC5C;EACA,eAAe6K,UAAUA,CAACvX,OAAO,EAAEwX,UAAU,EAAE;IAC7C,IAAI7Y,UAAU,CAACqB,OAAO,CAAC,EAAE;MACvB,OAAOA,OAAO;IAChB;IACA,IAAIuN,OAAO,GAAG8C,kBAAkB,CAACrQ,OAAO,EAAEsQ,KAAK,CAAC;IAChD,IAAI0B,2BAA2B,CAACD,GAAG,CAAC/R,OAAO,CAACuS,UAAU,CAAC,EAAE;MACvD,OAAO,IAAI5F,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAE1M,OAAO,CAACuS,UAAU;QAAEhF;MAAQ,CAAC,CAAC;IACpE;IACA,IAAIvN,OAAO,CAACqL,MAAM,EAAE;MAClBZ,MAAM,CAACkE,MAAM,CAAC3O,OAAO,CAACqL,MAAM,CAAC,CAACuD,OAAO,CAAE8D,GAAG,IAAK;QAC7C,IAAI,CAAC9T,oBAAoB,CAAC8T,GAAG,CAAC,IAAIA,GAAG,CAACtL,KAAK,EAAE;UAC3CiL,WAAW,CAACK,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF1S,OAAO,CAACqL,MAAM,GAAGD,cAAc,CAACpL,OAAO,CAACqL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAIjJ,KAAK,GAAG;MACVpB,UAAU,EAAEZ,OAAO,CAACY,UAAU;MAC9BkQ,UAAU,EAAE9Q,OAAO,CAAC8Q,UAAU;MAC9BzF,MAAM,EAAEM,eAAe,CAAC3L,OAAO,CAACqL,MAAM,EAAEJ,UAAU;IACpD,CAAC;IACD,IAAIwM,iBAAiB,GAAG;MACtBvL,QAAQ,EAAEoE,KAAK,CAACpE,QAAQ;MACxBzL,MAAM,EAAE6P,KAAK,CAAC7P,MAAM;MACpBmB,cAAc,EAAE0O,KAAK,CAAC1O,cAAc;MACpCD,GAAG,EAAE2O,KAAK,CAAC3O,GAAG;MACdjB,SAAS,EAAE8W;IACb,CAAC;IACD,IAAIE,YAAY,GAAG;MACjBtX,QAAQ,EAAEkQ,KAAK,CAACsG,MAAM;MACtBvW,YAAY,EAAEmK,uBAAuB,CAAC8F,KAAK,CAAC9P,MAAM,CAAC;MACnDG,oBAAoB,EAAEX,OAAO;MAC7BM,WAAW;MACXC,mBAAmB,EAAE2P,yBAAyB,CAAC;QAC7C,GAAGuH,iBAAiB;QACpBnX;MACF,CAAC,CAAC;MACF4B,mBAAmB,EAAE6R,oBAAoB,CACvC/R,KAAK,EACLsK,OAAO,CAACkB,MAAM,EACd8C,KAAK,CAAC/M,KAAK,CAACE,MAAM,CAACuQ,aAAa,EAChC/I,UACF,CAAC;MACDnJ,UAAU,EAAE,CAAC,CAAC;MACdrB,MAAM,EAAE6P,KAAK,CAAC7P,MAAM;MACpBkB,GAAG,EAAE2O,KAAK,CAAC3O,GAAG;MACdC,cAAc,EAAE0O,KAAK,CAAC1O,cAAc;MACpClB,SAAS,EAAE8W,UAAU;MACrB3V,cAAc,EAAG6Q,GAAG,IAAK7Q,cAAc,CAAC6Q,GAAG,EAAEzH,UAAU;IACzD,CAAC;IACD,IAAI0M,6BAA6B,GAAGrH,KAAK,CAAC/M,KAAK,CAACE,MAAM,CAAC8B,OAAO;IAC9D,IAAI;MACF,OAAO,MAAMoS,6BAA6B,CACxCrL,OAAO,EACPtM,OAAO,CAACuS,UAAU,EAClBhF,OAAO,EACPmK,YAAY,EACZtF,WACF,CAAC;IACH,CAAC,CAAC,OAAOhL,KAAK,EAAE;MACdiL,WAAW,CAACjL,KAAK,CAAC;MAClB,IAAIwQ,oBAAoB,GAAGxQ,KAAK;MAChC,IAAIzI,UAAU,CAACyI,KAAK,CAAC,EAAE;QACrB,IAAI;UACF,IAAInB,KAAK,GAAG,MAAM4R,cAAc,CAACzQ,KAAK,CAAC;UACvCwQ,oBAAoB,GAAG,IAAI9a,iBAAiB,CAC1CsK,KAAK,CAACsF,MAAM,EACZtF,KAAK,CAACwN,UAAU,EAChB3O,KACF,CAAC;QACH,CAAC,CAAC,OAAOwI,CAAC,EAAE,CACZ;MACF;MACAzO,OAAO,GAAG3B,yBAAyB,CACjC6T,aAAa,CAAC8C,UAAU,EACxBhV,OAAO,EACP4X,oBACF,CAAC;MACD,IAAI5X,OAAO,CAACqL,MAAM,EAAE;QAClBrL,OAAO,CAACqL,MAAM,GAAGD,cAAc,CAACpL,OAAO,CAACqL,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAI6M,MAAM,GAAG;QACXlX,UAAU,EAAEZ,OAAO,CAACY,UAAU;QAC9BkQ,UAAU,EAAE9Q,OAAO,CAAC8Q,UAAU;QAC9BzF,MAAM,EAAEM,eAAe,CAAC3L,OAAO,CAACqL,MAAM,EAAEJ,UAAU;MACpD,CAAC;MACDyM,YAAY,GAAG;QACb,GAAGA,YAAY;QACf/W,oBAAoB,EAAEX,OAAO;QAC7BO,mBAAmB,EAAE2P,yBAAyB,CAACuH,iBAAiB,CAAC;QACjEvV,mBAAmB,EAAE6R,oBAAoB,CACvC+D,MAAM,EACNxL,OAAO,CAACkB,MAAM,EACd8C,KAAK,CAAC/M,KAAK,CAACE,MAAM,CAACuQ,aAAa,EAChC/I,UACF,CAAC;QACDnJ,UAAU,EAAE,CAAC;MACf,CAAC;MACD,IAAI;QACF,OAAO,MAAM6V,6BAA6B,CACxCrL,OAAO,EACPtM,OAAO,CAACuS,UAAU,EAClBhF,OAAO,EACPmK,YAAY,EACZtF,WACF,CAAC;MACH,CAAC,CAAC,OAAO2F,MAAM,EAAE;QACf1F,WAAW,CAAC0F,MAAM,CAAC;QACnB,OAAOrC,6BAA6B,CAACqC,MAAM,EAAE9M,UAAU,CAAC;MAC1D;IACF;EACF;AACF;AACA,eAAeuL,qBAAqBA,CAACvL,UAAU,EAAEqF,KAAK,EAAE4B,aAAa,EAAEnR,OAAO,EAAEuL,OAAO,EAAE8F,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAI;IACF,IAAI+D,QAAQ,GAAG,MAAMlE,aAAa,CAAC8F,UAAU,CAAC1L,OAAO,EAAE;MACrDvL,OAAO;MACPgS,cAAc,EAAEX,WAAW;MAC3Bc,gBAAgB,EAAE5C,KAAK,CAAC7P,MAAM,CAAC6C,mBAAmB,GAAIgU,GAAG,IAAKA,GAAG,GAAG,KAAK;IAC3E,CAAC,CAAC;IACF,IAAI3Y,UAAU,CAACyX,QAAQ,CAAC,EAAE;MACxB,OAAOA,QAAQ;IACjB;IACA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAO,IAAIzJ,QAAQ,CAACyJ,QAAQ,CAAC;IAC/B;IACA,OAAOzJ,QAAQ,CAAC0K,IAAI,CAACjB,QAAQ,CAAC;EAChC,CAAC,CAAC,OAAOhP,KAAK,EAAE;IACd,IAAIzI,UAAU,CAACyI,KAAK,CAAC,EAAE;MACrBA,KAAK,CAACmG,OAAO,CAACuG,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;MACzC,OAAO1M,KAAK;IACd;IACA,IAAIxI,oBAAoB,CAACwI,KAAK,CAAC,EAAE;MAC/B,IAAIA,KAAK,EAAE;QACTiL,WAAW,CAACjL,KAAK,CAAC;MACpB;MACA,OAAO6Q,mBAAmB,CAAC7Q,KAAK,EAAE6D,UAAU,CAAC;IAC/C;IACA,IAAI7D,KAAK,YAAYlD,KAAK,IAAIkD,KAAK,CAACsE,OAAO,KAAK,qCAAqC,EAAE;MACrF,IAAIwM,QAAQ,GAAG,IAAIhU,KAAK,CACtB,gEACF,CAAC;MACDmO,WAAW,CAAC6F,QAAQ,CAAC;MACrB,OAAOxC,6BAA6B,CAACwC,QAAQ,EAAEjN,UAAU,CAAC;IAC5D;IACAoH,WAAW,CAACjL,KAAK,CAAC;IAClB,OAAOsO,6BAA6B,CAACtO,KAAK,EAAE6D,UAAU,CAAC;EACzD;AACF;AACA,SAASgN,mBAAmBA,CAACE,aAAa,EAAElN,UAAU,EAAE;EACtD,OAAO0B,QAAQ,CAAC0K,IAAI,CAClBxV,cAAc;EACZ;EACAsW,aAAa,CAAC/Q,KAAK,IAAI,IAAIlD,KAAK,CAAC,yBAAyB,CAAC,EAC3D+G,UACF,CAAC,EACD;IACEyB,MAAM,EAAEyL,aAAa,CAACzL,MAAM;IAC5BkI,UAAU,EAAEuD,aAAa,CAACvD,UAAU;IACpCrH,OAAO,EAAE;MACP,eAAe,EAAE;IACnB;EACF,CACF,CAAC;AACH;AACA,SAASmI,6BAA6BA,CAACtO,KAAK,EAAE6D,UAAU,EAAE;EACxD,IAAIS,OAAO,GAAG,yBAAyB;EACvC,IAAIT,UAAU,KAAK,YAAY,CAAC,kBAAkB;IAChDS,OAAO,IAAI;AACf;AACA,EAAEjF,MAAM,CAACW,KAAK,CAAC,EAAE;EACf;EACA,OAAO,IAAIuF,QAAQ,CAACjB,OAAO,EAAE;IAC3BgB,MAAM,EAAE,GAAG;IACXa,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ;AACA,SAASsK,cAAcA,CAACzB,QAAQ,EAAE;EAChC,IAAIgC,WAAW,GAAGhC,QAAQ,CAAC7I,OAAO,CAACiB,GAAG,CAAC,cAAc,CAAC;EACtD,OAAO4J,WAAW,IAAI,uBAAuB,CAACC,IAAI,CAACD,WAAW,CAAC,GAAGhC,QAAQ,CAAC9I,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG8I,QAAQ,CAACiB,IAAI,CAAC,CAAC,GAAGjB,QAAQ,CAACkC,IAAI,CAAC,CAAC;AACpI;;AAEA;AACA,SAASC,KAAKA,CAAChR,IAAI,EAAE;EACnB,OAAO,WAAWA,IAAI,IAAI;AAC5B;AACA,IAAIiR,aAAa,GAAG,SAAAA,CAAA,EAA+B;EAAA,IAA9BC,WAAW,GAAA1Q,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAAA,IAAE9G,EAAE,GAAA8G,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAC5C,IAAI9D,GAAG,GAAG,IAAIyU,GAAG,CAACjO,MAAM,CAACa,OAAO,CAACmN,WAAW,CAAC,CAAC;EAC9C,OAAO;IACL,IAAIxX,EAAEA,CAAA,EAAG;MACP,OAAOA,EAAE;IACX,CAAC;IACD,IAAI2O,IAAIA,CAAA,EAAG;MACT,OAAOnF,MAAM,CAACqK,WAAW,CAAC7Q,GAAG,CAAC;IAChC,CAAC;IACD8N,GAAGA,CAACxK,IAAI,EAAE;MACR,OAAOtD,GAAG,CAAC8N,GAAG,CAACxK,IAAI,CAAC,IAAItD,GAAG,CAAC8N,GAAG,CAACwG,KAAK,CAAChR,IAAI,CAAC,CAAC;IAC9C,CAAC;IACDiH,GAAGA,CAACjH,IAAI,EAAE;MACR,IAAItD,GAAG,CAAC8N,GAAG,CAACxK,IAAI,CAAC,EAAE,OAAOtD,GAAG,CAACuK,GAAG,CAACjH,IAAI,CAAC;MACvC,IAAIoR,SAAS,GAAGJ,KAAK,CAAChR,IAAI,CAAC;MAC3B,IAAItD,GAAG,CAAC8N,GAAG,CAAC4G,SAAS,CAAC,EAAE;QACtB,IAAIjX,KAAK,GAAGuC,GAAG,CAACuK,GAAG,CAACmK,SAAS,CAAC;QAC9B1U,GAAG,CAAC8I,MAAM,CAAC4L,SAAS,CAAC;QACrB,OAAOjX,KAAK;MACd;MACA,OAAO,KAAK,CAAC;IACf,CAAC;IACDoS,GAAGA,CAACvM,IAAI,EAAE7F,KAAK,EAAE;MACfuC,GAAG,CAAC6P,GAAG,CAACvM,IAAI,EAAE7F,KAAK,CAAC;IACtB,CAAC;IACD6W,KAAKA,CAAChR,IAAI,EAAE7F,KAAK,EAAE;MACjBuC,GAAG,CAAC6P,GAAG,CAACyE,KAAK,CAAChR,IAAI,CAAC,EAAE7F,KAAK,CAAC;IAC7B,CAAC;IACDkX,KAAKA,CAACrR,IAAI,EAAE;MACVtD,GAAG,CAAC8I,MAAM,CAACxF,IAAI,CAAC;IAClB;EACF,CAAC;AACH,CAAC;AACD,IAAIsR,SAAS,GAAI3P,MAAM,IAAK;EAC1B,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACjI,EAAE,KAAK,QAAQ,IAAI,OAAOiI,MAAM,CAAC0G,IAAI,KAAK,WAAW,IAAI,OAAO1G,MAAM,CAAC6I,GAAG,KAAK,UAAU,IAAI,OAAO7I,MAAM,CAACsF,GAAG,KAAK,UAAU,IAAI,OAAOtF,MAAM,CAAC4K,GAAG,KAAK,UAAU,IAAI,OAAO5K,MAAM,CAACqP,KAAK,KAAK,UAAU,IAAI,OAAOrP,MAAM,CAAC0P,KAAK,KAAK,UAAU;AACtR,CAAC;AACD,SAASE,oBAAoBA,CAAAC,KAAA,EAM1B;EAAA,IAN2B;IAC5BlS,MAAM,EAAEmS,SAAS;IACjBC,UAAU;IACVC,QAAQ;IACRC,UAAU;IACVC;EACF,CAAC,GAAAL,KAAA;EACC,IAAIlS,MAAM,GAAGoC,QAAQ,CAAC+P,SAAS,CAAC,GAAGA,SAAS,GAAGnR,YAAY,CAACmR,SAAS,EAAEzR,IAAI,IAAI,WAAW,EAAEyR,SAAS,CAAC;EACtGK,iCAAiC,CAACxS,MAAM,CAAC;EACzC,OAAO;IACL,MAAMyS,UAAUA,CAAC5Q,YAAY,EAAER,OAAO,EAAE;MACtC,IAAIjH,EAAE,GAAGyH,YAAY,KAAI,MAAM7B,MAAM,CAAClB,KAAK,CAAC+C,YAAY,EAAER,OAAO,CAAC;MAClE,IAAIjC,KAAK,GAAGhF,EAAE,KAAI,MAAMiY,QAAQ,CAACjY,EAAE,CAAC;MACpC,OAAOuX,aAAa,CAACvS,KAAK,IAAI,CAAC,CAAC,EAAEhF,EAAE,IAAI,EAAE,CAAC;IAC7C,CAAC;IACD,MAAMsY,aAAaA,CAACC,OAAO,EAAEtR,OAAO,EAAE;MACpC,IAAI;QAAEjH,EAAE;QAAE2O,IAAI,EAAE3J;MAAM,CAAC,GAAGuT,OAAO;MACjC,IAAInR,OAAO,GAAGH,OAAO,EAAEK,MAAM,IAAI,IAAI,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,EAAEG,OAAO,IAAI,IAAI,GAAGH,OAAO,CAACG,OAAO,GAAGxB,MAAM,CAACwB,OAAO;MACjJ,IAAIpH,EAAE,EAAE;QACN,MAAMkY,UAAU,CAAClY,EAAE,EAAEgF,KAAK,EAAEoC,OAAO,CAAC;MACtC,CAAC,MAAM;QACLpH,EAAE,GAAG,MAAMgY,UAAU,CAAChT,KAAK,EAAEoC,OAAO,CAAC;MACvC;MACA,OAAOxB,MAAM,CAACjB,SAAS,CAAC3E,EAAE,EAAEiH,OAAO,CAAC;IACtC,CAAC;IACD,MAAMuR,cAAcA,CAACD,OAAO,EAAEtR,OAAO,EAAE;MACrC,MAAMkR,UAAU,CAACI,OAAO,CAACvY,EAAE,CAAC;MAC5B,OAAO4F,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGsC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AACA,SAAS6Q,iCAAiCA,CAACxS,MAAM,EAAE;EACjDpH,QAAQ,CACNoH,MAAM,CAACyB,QAAQ,EACf,QAAQzB,MAAM,CAACU,IAAI,6OACrB,CAAC;AACH;;AAEA;AACA,SAASmS,0BAA0BA,CAAA,EAA6B;EAAA,IAA5B;IAAE7S,MAAM,EAAEmS;EAAU,CAAC,GAAAjR,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC5D,IAAIlB,MAAM,GAAGoC,QAAQ,CAAC+P,SAAS,CAAC,GAAGA,SAAS,GAAGnR,YAAY,CAACmR,SAAS,EAAEzR,IAAI,IAAI,WAAW,EAAEyR,SAAS,CAAC;EACtGK,iCAAiC,CAACxS,MAAM,CAAC;EACzC,OAAO;IACL,MAAMyS,UAAUA,CAAC5Q,YAAY,EAAER,OAAO,EAAE;MACtC,OAAOsQ,aAAa,CAClB9P,YAAY,KAAI,MAAM7B,MAAM,CAAClB,KAAK,CAAC+C,YAAY,EAAER,OAAO,CAAC,KAAI,CAAC,CAChE,CAAC;IACH,CAAC;IACD,MAAMqR,aAAaA,CAACC,OAAO,EAAEtR,OAAO,EAAE;MACpC,IAAIyR,gBAAgB,GAAG,MAAM9S,MAAM,CAACjB,SAAS,CAAC4T,OAAO,CAAC5J,IAAI,EAAE1H,OAAO,CAAC;MACpE,IAAIyR,gBAAgB,CAACjS,MAAM,GAAG,IAAI,EAAE;QAClC,MAAM,IAAIxD,KAAK,CACb,qDAAqD,GAAGyV,gBAAgB,CAACjS,MAC3E,CAAC;MACH;MACA,OAAOiS,gBAAgB;IACzB,CAAC;IACD,MAAMF,cAAcA,CAACG,QAAQ,EAAE1R,OAAO,EAAE;MACtC,OAAOrB,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGsC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA,SAASqR,0BAA0BA,CAAA,EAAkB;EAAA,IAAjB;IAAEhT;EAAO,CAAC,GAAAkB,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EACjD,IAAI9D,GAAG,GAAG,eAAgB,IAAIyU,GAAG,CAAC,CAAC;EACnC,OAAOI,oBAAoB,CAAC;IAC1BjS,MAAM;IACN,MAAMoS,UAAUA,CAAChT,KAAK,EAAEoC,OAAO,EAAE;MAC/B,IAAIpH,EAAE,GAAG6Y,IAAI,CAACC,MAAM,CAAC,CAAC,CAACjQ,QAAQ,CAAC,EAAE,CAAC,CAACkQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MACpD/V,GAAG,CAAC6P,GAAG,CAAC7S,EAAE,EAAE;QAAE2O,IAAI,EAAE3J,KAAK;QAAEoC;MAAQ,CAAC,CAAC;MACrC,OAAOpH,EAAE;IACX,CAAC;IACD,MAAMiY,QAAQA,CAACjY,EAAE,EAAE;MACjB,IAAIgD,GAAG,CAAC8N,GAAG,CAAC9Q,EAAE,CAAC,EAAE;QACf,IAAI;UAAE2O,IAAI,EAAE3J,KAAK;UAAEoC;QAAQ,CAAC,GAAGpE,GAAG,CAACuK,GAAG,CAACvN,EAAE,CAAC;QAC1C,IAAI,CAACoH,OAAO,IAAIA,OAAO,GAAG,eAAgB,IAAIG,IAAI,CAAC,CAAC,EAAE;UACpD,OAAOvC,KAAK;QACd;QACA,IAAIoC,OAAO,EAAEpE,GAAG,CAAC8I,MAAM,CAAC9L,EAAE,CAAC;MAC7B;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAMkY,UAAUA,CAAClY,EAAE,EAAEgF,KAAK,EAAEoC,OAAO,EAAE;MACnCpE,GAAG,CAAC6P,GAAG,CAAC7S,EAAE,EAAE;QAAE2O,IAAI,EAAE3J,KAAK;QAAEoC;MAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAM+Q,UAAUA,CAACnY,EAAE,EAAE;MACnBgD,GAAG,CAAC8I,MAAM,CAAC9L,EAAE,CAAC;IAChB;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,SAAS0M,IAAIA,CAACvJ,IAAI,EAAW;EAC3B,IAAI+H,MAAM,GAAApE,SAAA,CAAAL,MAAA,QAAAM,SAAA,GAAAD,SAAA,GAAU;EACpB,OAAO3D,IAAI,CAACqP,KAAK,CAAC,GAAG,CAAC,CAACxP,GAAG,CAAEgW,OAAO,IAAK;IACtC,IAAIA,OAAO,KAAK,GAAG,EAAE;MACnB,OAAO9N,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtC;IACA,MAAMtL,KAAK,GAAGoZ,OAAO,CAACpZ,KAAK,CAAC,iBAAiB,CAAC;IAC9C,IAAI,CAACA,KAAK,EAAE,OAAOoZ,OAAO;IAC1B,MAAMC,KAAK,GAAGrZ,KAAK,CAAC,CAAC,CAAC;IACtB,MAAMa,KAAK,GAAGyK,MAAM,GAAGA,MAAM,CAAC+N,KAAK,CAAC,GAAG,KAAK,CAAC;IAC7C,MAAMC,UAAU,GAAGtZ,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC;IACtC,IAAIsZ,UAAU,IAAIzY,KAAK,KAAK,KAAK,CAAC,EAAE;MAClC,MAAMwC,KAAK,CACT,SAASE,IAAI,qBAAqB8V,KAAK,2BACzC,CAAC;IACH;IACA,OAAOxY,KAAK;EACd,CAAC,CAAC,CAAC4R,MAAM,CAAE2G,OAAO,IAAKA,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC9C,IAAI,CAAC,GAAG,CAAC;AACtD;;AAEA;AACA,OAAO,KAAKiD,MAAM,MAAM,OAAO;AAC/B,OAAO,KAAKC,QAAQ,MAAM,WAAW;;AAErC;AACA,SAASC,gBAAgBA,CAACtY,KAAK,EAAExB,MAAM,EAAE+Z,YAAY,EAAEC,SAAS,EAAEtO,QAAQ,EAAExL,SAAS,EAAE;EACrF,IAAIsC,aAAa,GAAG;IAClB,GAAGhB,KAAK;IACRpB,UAAU,EAAE;MAAE,GAAGoB,KAAK,CAACpB;IAAW;EACpC,CAAC;EACD,IAAI6Z,cAAc,GAAG5b,WAAW,CAAC2B,MAAM,EAAEga,SAAS,EAAEtO,QAAQ,CAAC;EAC7D,IAAIuO,cAAc,EAAE;IAClB,KAAK,IAAI5Z,KAAK,IAAI4Z,cAAc,EAAE;MAChC,IAAI1Z,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;MAC5B,IAAIyZ,SAAS,GAAGH,YAAY,CAACxZ,OAAO,CAAC;MACrC,IAAI7B,wBAAwB,CAC1B6B,OAAO,EACP2Z,SAAS,CAACvZ,YAAY,EACtBuZ,SAAS,CAACtZ,SAAS,EACnBV,SACF,CAAC,KAAKga,SAAS,CAACC,kBAAkB,IAAI,CAACD,SAAS,CAACtZ,SAAS,CAAC,EAAE;QAC3D,OAAO4B,aAAa,CAACpC,UAAU,CAACG,OAAO,CAAC;MAC1C,CAAC,MAAM,IAAI,CAAC2Z,SAAS,CAACtZ,SAAS,EAAE;QAC/B4B,aAAa,CAACpC,UAAU,CAACG,OAAO,CAAC,GAAG,IAAI;MAC1C;IACF;EACF;EACA,OAAOiC,aAAa;AACtB;;AAEA;AACA,OAAO4X,MAAM,MAAM,OAAO;AAC1B,IAAIC,4BAA4B,GAAG,cAAcD,MAAM,CAACtW,SAAS,CAAC;EAChEwW,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC/Y,KAAK,GAAG;MAAEoF,KAAK,EAAE,IAAI;MAAErF,QAAQ,EAAEgZ,KAAK,CAAChZ;IAAS,CAAC;EACxD;EACA,OAAOiZ,wBAAwBA,CAAC5T,KAAK,EAAE;IACrC,OAAO;MAAEA;IAAM,CAAC;EAClB;EACA,OAAO6T,wBAAwBA,CAACF,KAAK,EAAE/Y,KAAK,EAAE;IAC5C,IAAIA,KAAK,CAACD,QAAQ,KAAKgZ,KAAK,CAAChZ,QAAQ,EAAE;MACrC,OAAO;QAAEqF,KAAK,EAAE,IAAI;QAAErF,QAAQ,EAAEgZ,KAAK,CAAChZ;MAAS,CAAC;IAClD;IACA,OAAO;MAAEqF,KAAK,EAAEpF,KAAK,CAACoF,KAAK;MAAErF,QAAQ,EAAEC,KAAK,CAACD;IAAS,CAAC;EACzD;EACAmZ,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAClZ,KAAK,CAACoF,KAAK,EAAE;MACpB,OAAO,eAAgBwT,MAAM,CAACrZ,aAAa,CACzC4Z,+BAA+B,EAC/B;QACE/T,KAAK,EAAE,IAAI,CAACpF,KAAK,CAACoF,KAAK;QACvBgU,cAAc,EAAE;MAClB,CACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO,IAAI,CAACL,KAAK,CAACrV,QAAQ;IAC5B;EACF;AACF,CAAC;AACD,SAAS2V,YAAYA,CAAAC,KAAA,EAIlB;EAAA,IAJmB;IACpBF,cAAc;IACdG,KAAK;IACL7V;EACF,CAAC,GAAA4V,KAAA;EACC,IAAI,CAACF,cAAc,EAAE;IACnB,OAAO1V,QAAQ;EACjB;EACA,OAAO,eAAgBkV,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE;IAAEia,IAAI,EAAE;EAAK,CAAC,EAAE,eAAgBZ,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBqZ,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE;IAAEka,OAAO,EAAE;EAAQ,CAAC,CAAC,EAAE,eAAgBb,MAAM,CAACrZ,aAAa,CAC7N,MAAM,EACN;IACEgG,IAAI,EAAE,UAAU;IAChBmU,OAAO,EAAE;EACX,CACF,CAAC,EAAE,eAAgBd,MAAM,CAACrZ,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEga,KAAK,CAAC,CAAC,EAAE,eAAgBX,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBqZ,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE;IAAEoa,KAAK,EAAE;MAAEC,UAAU,EAAE,uBAAuB;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAAEnW,QAAQ,CAAC,CAAC,CAAC;AAClP;AACA,SAASyV,+BAA+BA,CAAAW,KAAA,EAGrC;EAAA,IAHsC;IACvC1U,KAAK;IACLgU;EACF,CAAC,GAAAU,KAAA;EACCjO,OAAO,CAACzG,KAAK,CAACA,KAAK,CAAC;EACpB,IAAI2U,YAAY,GAAG,eAAgBnB,MAAM,CAACrZ,aAAa,CACrD,QAAQ,EACR;IACEya,uBAAuB,EAAE;MACvBC,MAAM,EAAE;AAChB;AACA;AACA;AACA;IACM;EACF,CACF,CAAC;EACD,IAAIrd,oBAAoB,CAACwI,KAAK,CAAC,EAAE;IAC/B,OAAO,eAAgBwT,MAAM,CAACrZ,aAAa,CACzC8Z,YAAY,EACZ;MACED,cAAc;MACdG,KAAK,EAAE;IACT,CAAC,EACD,eAAgBX,MAAM,CAACrZ,aAAa,CAAC,IAAI,EAAE;MAAEoa,KAAK,EAAE;QAAEO,QAAQ,EAAE;MAAO;IAAE,CAAC,EAAE9U,KAAK,CAACsF,MAAM,EAAE,GAAG,EAAEtF,KAAK,CAACwN,UAAU,CAAC,EAChH/X,mBAAmB,GAAGkf,YAAY,GAAG,IACvC,CAAC;EACH;EACA,IAAII,aAAa;EACjB,IAAI/U,KAAK,YAAYlD,KAAK,EAAE;IAC1BiY,aAAa,GAAG/U,KAAK;EACvB,CAAC,MAAM;IACL,IAAIgV,WAAW,GAAGhV,KAAK,IAAI,IAAI,GAAG,eAAe,GAAG,OAAOA,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAIA,KAAK,GAAGA,KAAK,CAAC0C,QAAQ,CAAC,CAAC,GAAGL,IAAI,CAACC,SAAS,CAACtC,KAAK,CAAC;IAC/I+U,aAAa,GAAG,IAAIjY,KAAK,CAACkY,WAAW,CAAC;EACxC;EACA,OAAO,eAAgBxB,MAAM,CAACrZ,aAAa,CAAC8Z,YAAY,EAAE;IAAED,cAAc;IAAEG,KAAK,EAAE;EAAqB,CAAC,EAAE,eAAgBX,MAAM,CAACrZ,aAAa,CAAC,IAAI,EAAE;IAAEoa,KAAK,EAAE;MAAEO,QAAQ,EAAE;IAAO;EAAE,CAAC,EAAE,mBAAmB,CAAC,EAAE,eAAgBtB,MAAM,CAACrZ,aAAa,CAC/O,KAAK,EACL;IACEoa,KAAK,EAAE;MACLE,OAAO,EAAE,MAAM;MACfQ,UAAU,EAAE,yBAAyB;MACrCC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC,EACDJ,aAAa,CAAChR,KAChB,CAAC,EAAE4Q,YAAY,CAAC;AAClB;AACA,SAASS,2BAA2BA,CAAAC,KAAA,EAEjC;EAAA,IAFkC;IACnCC;EACF,CAAC,GAAAD,KAAA;EACC,IAAIrV,KAAK,GAAG5H,aAAa,CAAC,CAAC;EAC3B,IAAIkd,aAAa,KAAK,KAAK,CAAC,EAAE;IAC5B,MAAM,IAAIxY,KAAK,CAAC,8BAA8B,CAAC;EACjD;EACA,OAAO,eAAgB0W,MAAM,CAACrZ,aAAa,CACzC4Z,+BAA+B,EAC/B;IACEC,cAAc,EAAE,CAACsB,aAAa;IAC9BtV;EACF,CACF,CAAC;AACH;;AAEA;AACA,SAASuV,gBAAgBA,CAAAC,KAAA,EAKtB;EAAA,IALuB;IACxBC,wBAAwB;IACxBC,2BAA2B;IAC3BC,WAAW;IACXC,KAAK,EAAEC,mBAAmB,GAAGD;EAC/B,CAAC,GAAAJ,KAAA;EACC,MAAMM,SAAS,GAAGC,MAAM;EACxB,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAO,OAAOnc,EAAE,EAAEwD,IAAI,KAAK;IACzB,IAAI4Y,QAAQ,GAAGH,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IAChH,MAAMC,mBAAmB,GAAGT,2BAA2B,CAAC,CAAC;IACzD,MAAM1G,QAAQ,GAAG,MAAM6G,mBAAmB,CACxC,IAAIvP,OAAO,CAAC3L,QAAQ,CAAC4L,IAAI,EAAE;MACzBL,IAAI,EAAE,MAAMyP,WAAW,CAACtY,IAAI,EAAE;QAAE8Y;MAAoB,CAAC,CAAC;MACtDlQ,MAAM,EAAE,MAAM;MACdE,OAAO,EAAE;QACPiQ,MAAM,EAAE,kBAAkB;QAC1B,eAAe,EAAEvc;MACnB;IACF,CAAC,CACH,CAAC;IACD,IAAI,CAACmV,QAAQ,CAAC9I,IAAI,EAAE;MAClB,MAAM,IAAIpJ,KAAK,CAAC,kBAAkB,CAAC;IACrC;IACA,MAAMuZ,OAAO,GAAG,MAAMZ,wBAAwB,CAACzG,QAAQ,CAAC9I,IAAI,EAAE;MAC5DiQ;IACF,CAAC,CAAC;IACF,IAAIE,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;MAC/B,IAAID,OAAO,CAAC9N,MAAM,EAAE;QAClBwN,MAAM,CAACpb,QAAQ,CAAC4L,IAAI,GAAG8P,OAAO,CAAC1b,QAAQ;QACvC;MACF;MACAmb,SAAS,CAACS,QAAQ,CAACC,QAAQ,CAACH,OAAO,CAAC1b,QAAQ,EAAE;QAC5C9C,OAAO,EAAEwe,OAAO,CAACxe;MACnB,CAAC,CAAC;MACF,OAAOwe,OAAO,CAACI,YAAY;IAC7B;IACA,IAAIJ,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIxZ,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACA,IAAIuZ,OAAO,CAACK,QAAQ,EAAE;MACpB1D,MAAM,CAAC2D,eAAe;MACpB;MACA,YAAY;QACV,MAAMD,QAAQ,GAAG,MAAML,OAAO,CAACK,QAAQ;QACvC,IAAI,CAACA,QAAQ,EAAE;QACf,IAAIV,cAAc,GAAGC,QAAQ,IAAIH,SAAS,CAACI,gBAAgB,IAAID,QAAQ,EAAE;UACvED,cAAc,GAAGC,QAAQ;UACzB,IAAIS,QAAQ,CAACJ,IAAI,KAAK,UAAU,EAAE;YAChC,IAAII,QAAQ,CAACnO,MAAM,EAAE;cACnBwN,MAAM,CAACpb,QAAQ,CAAC4L,IAAI,GAAGmQ,QAAQ,CAAC/b,QAAQ;cACxC;YACF;YACAmb,SAAS,CAACS,QAAQ,CAACC,QAAQ,CAACE,QAAQ,CAAC/b,QAAQ,EAAE;cAC7C9C,OAAO,EAAE6e,QAAQ,CAAC7e;YACpB,CAAC,CAAC;YACF;UACF;UACA,IAAI+e,SAAS;UACb,KAAK,MAAMnd,KAAK,IAAIid,QAAQ,CAAChd,OAAO,EAAE;YACpCoc,SAAS,CAACS,QAAQ,CAACM,WAAW,CAC5BD,SAAS,EAAE/c,EAAE,IAAI,IAAI,EACrB,CAACid,6BAA6B,CAACrd,KAAK,CAAC,CAAC,EACtC,IACF,CAAC;YACDmd,SAAS,GAAGnd,KAAK;UACnB;UACAsc,MAAM,CAACQ,QAAQ,CAACQ,8CAA8C,CAAC,CAAC,CAAC,CAAC;UAClE/D,MAAM,CAAC2D,eAAe,CAAC,MAAM;YAC3BZ,MAAM,CAACQ,QAAQ,CAACQ,8CAA8C,CAAC;cAC7Dvd,UAAU,EAAE6J,MAAM,CAACgB,MAAM,CACvB,CAAC,CAAC,EACFyR,SAAS,CAACS,QAAQ,CAAC3b,KAAK,CAACpB,UAAU,EACnCkd,QAAQ,CAACld,UACX,CAAC;cACDyK,MAAM,EAAEyS,QAAQ,CAACzS,MAAM,GAAGZ,MAAM,CAACgB,MAAM,CACrC,CAAC,CAAC,EACFyR,SAAS,CAACS,QAAQ,CAAC3b,KAAK,CAACqJ,MAAM,EAC/ByS,QAAQ,CAACzS,MACX,CAAC,GAAG;YACN,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF,CACF,CAAC;IACH;IACA,OAAOoS,OAAO,CAACI,YAAY;EAC7B,CAAC;AACH;AACA,SAASO,uBAAuBA,CAAAC,KAAA,EAK7B;EAAA,IAL8B;IAC/BpB,mBAAmB;IACnBJ,wBAAwB;IACxByB,mBAAmB;IACnBb;EACF,CAAC,GAAAY,KAAA;EACC,MAAMnB,SAAS,GAAGC,MAAM;EACxB,IAAID,SAAS,CAACS,QAAQ,EAAE,OAAOT,SAAS,CAACS,QAAQ;EACjD,IAAIF,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAIxZ,KAAK,CAAC,sBAAsB,CAAC;EACtE,IAAI2S,OAAO,GAAG,eAAgB,IAAI6B,GAAG,CAAC,CAAC;EACvC+E,OAAO,CAAC5G,OAAO,EAAEjI,OAAO,CAAE2P,KAAK,IAAK;IAClCjgB,SAAS,CAACigB,KAAK,CAACva,QAAQ,EAAE,wBAAwB,CAAC;IACnD,IAAI,CAAC6S,OAAO,CAAC9E,GAAG,CAACwM,KAAK,CAACva,QAAQ,CAAC,EAAE;MAChC6S,OAAO,CAAC/C,GAAG,CAACyK,KAAK,CAACva,QAAQ,EAAE,EAAE,CAAC;IACjC;IACA6S,OAAO,CAACrI,GAAG,CAAC+P,KAAK,CAACva,QAAQ,CAAC,EAAEkJ,IAAI,CAACqR,KAAK,CAAC;EAC1C,CAAC,CAAC;EACF,IAAI/d,MAAM,GAAGid,OAAO,CAAC3c,OAAO,CAAC0d,WAAW,CAAC,CAACC,QAAQ,EAAE5d,KAAK,KAAK;IAC5D,MAAMG,KAAK,GAAGkd,6BAA6B,CACzCrd,KAAK,EACL4c,OACF,CAAC;IACD,IAAIgB,QAAQ,CAAC/W,MAAM,GAAG,CAAC,EAAE;MACvB1G,KAAK,CAAC0E,QAAQ,GAAG+Y,QAAQ;MACzB,IAAIC,eAAe,GAAG7H,OAAO,CAACrI,GAAG,CAAC3N,KAAK,CAACI,EAAE,CAAC;MAC3C,IAAIyd,eAAe,EAAE;QACnB1d,KAAK,CAAC0E,QAAQ,CAACwH,IAAI,CACjB,GAAGwR,eAAe,CAACza,GAAG,CAAEF,CAAC,IAAKma,6BAA6B,CAACna,CAAC,CAAC,CAChE,CAAC;MACH;IACF;IACA,OAAO,CAAC/C,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACNkc,SAAS,CAACS,QAAQ,GAAG9f,YAAY,CAAC;IAChC2C,MAAM;IACN8d,mBAAmB;IACnBpS,QAAQ,EAAEuR,OAAO,CAACvR,QAAQ;IAC1ByS,OAAO,EAAEjhB,oBAAoB,CAAC,CAAC;IAC/BsF,aAAa,EAAEsX,gBAAgB,CAC7B;MACE1Z,UAAU,EAAE6c,OAAO,CAAC7c,UAAU;MAC9BkQ,UAAU,EAAE2M,OAAO,CAAC3M,UAAU;MAC9BzF,MAAM,EAAEoS,OAAO,CAACpS;IAClB,CAAC,EACD7K,MAAM,EACLO,OAAO,IAAK;MACX,IAAIF,KAAK,GAAG4c,OAAO,CAAC3c,OAAO,CAAC8d,IAAI,CAAEpO,CAAC,IAAKA,CAAC,CAACvP,EAAE,KAAKF,OAAO,CAAC;MACzDzC,SAAS,CAACuC,KAAK,EAAE,4BAA4B,CAAC;MAC9C,OAAO;QACLM,YAAY,EAAEN,KAAK,CAACM,YAAY;QAChCC,SAAS,EAAEP,KAAK,CAACO,SAAS;QAC1BuZ,kBAAkB,EAAE9Z,KAAK,CAACge,sBAAsB,IAAI;MACtD,CAAC;IACH,CAAC,EACDpB,OAAO,CAAC1b,QAAQ,EAChB,KAAK,CAAC,EACN,KACF,CAAC;IACD,MAAM+c,uBAAuBA,CAAAC,MAAA,EAAmB;MAAA,IAAlB;QAAE3a,IAAI;QAAEoJ;MAAO,CAAC,GAAAuR,MAAA;MAC5C,IAAIC,eAAe,CAACjN,GAAG,CAAC3N,IAAI,CAAC,EAAE;QAC7B;MACF;MACA,MAAM6a,4BAA4B,CAChC,CAAC7a,IAAI,CAAC,EACNyY,wBAAwB,EACxBI,mBAAmB,EACnBzP,MACF,CAAC;IACH,CAAC;IACD;IACA0R,YAAY,EAAEC,6BAA6B,CACzC,MAAMjC,SAAS,CAACS,QAAQ,EACxB,IAAI,EACJF,OAAO,CAACvR,QAAQ,EAChB2Q,wBAAwB,EACxBI,mBACF;EACF,CAAC,CAAC;EACF,IAAIC,SAAS,CAACS,QAAQ,CAAC3b,KAAK,CAACod,WAAW,EAAE;IACxClC,SAAS,CAACmC,mBAAmB,GAAG,IAAI;IACpCnC,SAAS,CAACS,QAAQ,CAAC2B,UAAU,CAAC,CAAC;EACjC,CAAC,MAAM;IACLpC,SAAS,CAACmC,mBAAmB,GAAG,KAAK;EACvC;EACA,IAAIE,cAAc,GAAG,KAAK,CAAC;EAC3BrC,SAAS,CAACS,QAAQ,CAAC6B,SAAS,CAACC,MAAA,IAAgC;IAAA,IAA/B;MAAE7e,UAAU;MAAEkQ;IAAW,CAAC,GAAA2O,MAAA;IACtD,IAAIF,cAAc,KAAK3e,UAAU,EAAE;MACjCsc,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IACnG;EACF,CAAC,CAAC;EACF,OAAOJ,SAAS,CAACS,QAAQ;AAC3B;AACA,IAAI+B,qBAAqB,GAAGngB,sBAAsB,CAAC,CAAC;AACpD,SAAS4f,6BAA6BA,CAACQ,SAAS,EAAEhe,GAAG,EAAEuK,QAAQ,EAAE2Q,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9G,IAAIiC,YAAY,GAAG9gB,8BAA8B,CAC/CuhB,SAAS,EACR9e,KAAK,IAAK;IACT,IAAI+e,CAAC,GAAG/e,KAAK;IACb,OAAO;MACLO,SAAS,EAAEwe,CAAC,CAAC5e,KAAK,CAACI,SAAS;MAC5B4D,eAAe,EAAE4a,CAAC,CAAC5e,KAAK,CAACgE,eAAe;MACxC6a,YAAY,EAAED,CAAC,CAAC5e,KAAK,CAAC6e,YAAY;MAClC/a,SAAS,EAAE8a,CAAC,CAAC5e,KAAK,CAAC8D,SAAS;MAC5BC,eAAe,EAAE6a,CAAC,CAAC5e,KAAK,CAAC+D,eAAe;MACxC+a,mBAAmB,EAAEF,CAAC,CAAC5e,KAAK,CAAC8e;IAC/B,CAAC;EACH,CAAC;EACD;EACAC,uBAAuB,CAAClD,wBAAwB,EAAEI,mBAAmB,CAAC,EACtEtb,GAAG,EACHuK,QAAQ;EACR;EACA;EACA;EACCrL,KAAK,IAAK;IACT,IAAI+e,CAAC,GAAG/e,KAAK;IACb,OAAO+e,CAAC,CAAC5e,KAAK,CAAC6e,YAAY,IAAI,CAACD,CAAC,CAAC5e,KAAK,CAACgf,OAAO;EACjD,CACF,CAAC;EACD,OAAO,MAAOvb,IAAI,IAAKA,IAAI,CAACwb,4BAA4B,CAAC,YAAY;IACnE,IAAIjgB,OAAO,GAAGyE,IAAI,CAACzE,OAAO;IAC1BA,OAAO,CAAC8T,GAAG,CAAC4L,qBAAqB,EAAE,EAAE,CAAC;IACtC,IAAItM,OAAO,GAAG,MAAM8L,YAAY,CAACza,IAAI,CAAC;IACtC,MAAMyb,kBAAkB,GAAG,eAAgB,IAAIxH,GAAG,CAAC,CAAC;IACpD,KAAK,MAAM1X,KAAK,IAAIhB,OAAO,CAACwO,GAAG,CAACkR,qBAAqB,CAAC,EAAE;MACtD,IAAI,CAACQ,kBAAkB,CAACnO,GAAG,CAAC/Q,KAAK,CAACC,EAAE,CAAC,EAAE;QACrCif,kBAAkB,CAACpM,GAAG,CAAC9S,KAAK,CAACC,EAAE,EAAE,EAAE,CAAC;MACtC;MACAif,kBAAkB,CAAC1R,GAAG,CAACxN,KAAK,CAACC,EAAE,CAAC,CAACiM,IAAI,CAAClM,KAAK,CAAC;IAC9C;IACA,KAAK,MAAMH,KAAK,IAAI4D,IAAI,CAAC3D,OAAO,EAAE;MAChC,MAAMqf,cAAc,GAAGD,kBAAkB,CAAC1R,GAAG,CAAC3N,KAAK,CAACG,KAAK,CAACC,EAAE,CAAC;MAC7D,IAAIkf,cAAc,EAAE;QAClB,KAAK,MAAMC,QAAQ,IAAID,cAAc,EAAE;UACrChD,MAAM,CAACQ,QAAQ,CAACM,WAAW,CACzBmC,QAAQ,CAACpc,QAAQ,IAAI,IAAI,EACzB,CAACka,6BAA6B,CAACkC,QAAQ,CAAC,CAAC,EACzC,IACF,CAAC;QACH;MACF;IACF;IACA,OAAOhN,OAAO;EAChB,CAAC,CAAC;AACJ;AACA,SAAS2M,uBAAuBA,CAAClD,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9E,OAAO,OAAOxY,IAAI,EAAEyH,QAAQ,EAAEmU,YAAY,KAAK;IAC7C,IAAI;MAAE/T,OAAO;MAAEtM;IAAQ,CAAC,GAAGyE,IAAI;IAC/B,IAAIxE,GAAG,GAAGd,cAAc,CAACmN,OAAO,CAACrM,GAAG,EAAEiM,QAAQ,EAAE,KAAK,CAAC;IACtD,IAAII,OAAO,CAACe,MAAM,KAAK,KAAK,EAAE;MAC5BpN,GAAG,GAAGZ,eAAe,CAACY,GAAG,CAAC;MAC1B,IAAIogB,YAAY,EAAE;QAChBpgB,GAAG,CAAC4M,YAAY,CAACiH,GAAG,CAAC,SAAS,EAAEuM,YAAY,CAAClJ,IAAI,CAAC,GAAG,CAAC,CAAC;MACzD;IACF;IACA,IAAIjB,GAAG,GAAG,MAAM+G,mBAAmB,CACjC,IAAIvP,OAAO,CAACzN,GAAG,EAAE,MAAMrC,iBAAiB,CAAC0O,OAAO,CAAC,CACnD,CAAC;IACD,IAAI4J,GAAG,CAACxJ,MAAM,KAAK,GAAG,IAAI,CAACwJ,GAAG,CAAC3I,OAAO,CAACwE,GAAG,CAAC,kBAAkB,CAAC,EAAE;MAC9D,MAAM,IAAIjV,iBAAiB,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC;IACrD;IACAwB,SAAS,CAAC4X,GAAG,CAAC5I,IAAI,EAAE,4BAA4B,CAAC;IACjD,IAAI;MACF,MAAMmQ,OAAO,GAAG,MAAMZ,wBAAwB,CAAC3G,GAAG,CAAC5I,IAAI,EAAE;QACvDiQ,mBAAmB,EAAE,KAAK;MAC5B,CAAC,CAAC;MACF,IAAIE,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;QAC/B,OAAO;UACLhR,MAAM,EAAEwJ,GAAG,CAACxJ,MAAM;UAClBkD,IAAI,EAAE;YACJ7Q,QAAQ,EAAE;cACRA,QAAQ,EAAE0e,OAAO,CAAC1b,QAAQ;cAC1B4N,MAAM,EAAE8N,OAAO,CAAC9N,MAAM;cACtB1Q,OAAO,EAAEwe,OAAO,CAACxe,OAAO;cACxBiV,UAAU,EAAE,KAAK;cACjBxH,MAAM,EAAE+Q,OAAO,CAAC/Q;YAClB;UACF;QACF,CAAC;MACH;MACA,IAAI+Q,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIxZ,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACAlE,OAAO,CAACwO,GAAG,CAACkR,qBAAqB,CAAC,CAACxS,IAAI,CAAC,GAAGuQ,OAAO,CAAC3c,OAAO,CAAC;MAC3D,IAAIsS,OAAO,GAAG;QAAE5S,MAAM,EAAE,CAAC;MAAE,CAAC;MAC5B,MAAM8f,OAAO,GAAG9hB,gBAAgB,CAAC8N,OAAO,CAACe,MAAM,CAAC,GAAG,YAAY,GAAG,YAAY;MAC9E,KAAK,IAAI,CAACtM,OAAO,EAAEkF,KAAK,CAAC,IAAIwE,MAAM,CAACa,OAAO,CAACmS,OAAO,CAAC6C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QACnElN,OAAO,CAAC5S,MAAM,CAACO,OAAO,CAAC,GAAG;UAAE6O,IAAI,EAAE3J;QAAM,CAAC;MAC3C;MACA,IAAIwX,OAAO,CAACpS,MAAM,EAAE;QAClB,KAAK,IAAI,CAACtK,OAAO,EAAEqG,KAAK,CAAC,IAAIqD,MAAM,CAACa,OAAO,CAACmS,OAAO,CAACpS,MAAM,CAAC,EAAE;UAC3D+H,OAAO,CAAC5S,MAAM,CAACO,OAAO,CAAC,GAAG;YAAEqG;UAAM,CAAC;QACrC;MACF;MACA,OAAO;QAAEsF,MAAM,EAAEwJ,GAAG,CAACxJ,MAAM;QAAEkD,IAAI,EAAEwD;MAAQ,CAAC;IAC9C,CAAC,CAAC,OAAO3E,CAAC,EAAE;MACV,MAAM,IAAIvK,KAAK,CAAC,+BAA+B,CAAC;IAClD;EACF,CAAC;AACH;AACA,SAASqc,iBAAiBA,CAAAC,MAAA,EAMvB;EAAA,IANwB;IACzB3D,wBAAwB;IACxBG,KAAK,EAAEC,mBAAmB,GAAGD,KAAK;IAClCS,OAAO;IACP7b,cAAc,GAAG,OAAO;IACxB0c;EACF,CAAC,GAAAkC,MAAA;EACC,IAAI/C,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAIxZ,KAAK,CAAC,sBAAsB,CAAC;EACtE,IAAI5C,MAAM,GAAG8Y,MAAM,CAACqG,OAAO,CACzB,MAAMrC,uBAAuB,CAAC;IAC5BX,OAAO;IACPR,mBAAmB;IACnBqB,mBAAmB;IACnBzB;EACF,CAAC,CAAC,EACF,CACEA,wBAAwB,EACxBY,OAAO,EACPR,mBAAmB,EACnBqB,mBAAmB,CAEvB,CAAC;EACDlE,MAAM,CAACsG,eAAe,CAAC,MAAM;IAC3B,MAAMxD,SAAS,GAAGC,MAAM;IACxB,IAAI,CAACD,SAAS,CAACmC,mBAAmB,EAAE;MAClCnC,SAAS,CAACmC,mBAAmB,GAAG,IAAI;MACpCnC,SAAS,CAACS,QAAQ,CAAC2B,UAAU,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI,CAAC9E,SAAS,EAAEmG,WAAW,CAAC,GAAGvG,MAAM,CAACwG,QAAQ,CAACtf,MAAM,CAACU,KAAK,CAACD,QAAQ,CAAC;EACrEqY,MAAM,CAACsG,eAAe,CACpB,MAAMpf,MAAM,CAACke,SAAS,CAAEqB,QAAQ,IAAK;IACnC,IAAIA,QAAQ,CAAC9e,QAAQ,KAAKyY,SAAS,EAAE;MACnCmG,WAAW,CAACE,QAAQ,CAAC9e,QAAQ,CAAC;IAChC;EACF,CAAC,CAAC,EACF,CAACT,MAAM,EAAEkZ,SAAS,CACpB,CAAC;EACDJ,MAAM,CAAC0G,SAAS,CAAC,MAAM;IACrB,IAAIlf,cAAc,KAAK,MAAM;IAAI;IACjCub,MAAM,CAAC4D,SAAS,EAAEC,UAAU,EAAEC,QAAQ,KAAK,IAAI,EAAE;MAC/C;IACF;IACA,SAASC,eAAeA,CAACC,EAAE,EAAE;MAC3B,IAAI/c,IAAI,GAAG+c,EAAE,CAACC,OAAO,KAAK,MAAM,GAAGD,EAAE,CAACE,YAAY,CAAC,QAAQ,CAAC,GAAGF,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC;MACtF,IAAI,CAACjd,IAAI,EAAE;QACT;MACF;MACA,IAAI6H,QAAQ,GAAGkV,EAAE,CAACC,OAAO,KAAK,GAAG,GAAGD,EAAE,CAAClV,QAAQ,GAAG,IAAI9L,GAAG,CAACiE,IAAI,EAAE+Y,MAAM,CAACpb,QAAQ,CAACuf,MAAM,CAAC,CAACrV,QAAQ;MAChG,IAAI,CAAC+S,eAAe,CAACjN,GAAG,CAAC9F,QAAQ,CAAC,EAAE;QAClCsV,SAAS,CAACnK,GAAG,CAACnL,QAAQ,CAAC;MACzB;IACF;IACA,eAAeuV,YAAYA,CAAA,EAAG;MAC5BC,QAAQ,CAACC,gBAAgB,CAAC,uCAAuC,CAAC,CAAC9S,OAAO,CAACsS,eAAe,CAAC;MAC3F,IAAIpK,KAAK,GAAG6K,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC7W,IAAI,CAAC,CAAC,CAAC,CAAC4I,MAAM,CAAElP,IAAI,IAAK;QACxD,IAAI4a,eAAe,CAACjN,GAAG,CAAC3N,IAAI,CAAC,EAAE;UAC7Bmd,SAAS,CAACxU,MAAM,CAAC3I,IAAI,CAAC;UACtB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI0S,KAAK,CAACpP,MAAM,KAAK,CAAC,EAAE;QACtB;MACF;MACA,IAAI;QACF,MAAMuX,4BAA4B,CAChCnI,KAAK,EACL+F,wBAAwB,EACxBI,mBACF,CAAC;MACH,CAAC,CAAC,OAAOxO,CAAC,EAAE;QACVZ,OAAO,CAACzG,KAAK,CAAC,kCAAkC,EAAEqH,CAAC,CAAC;MACtD;IACF;IACA,IAAIoT,qBAAqB,GAAGC,QAAQ,CAACN,YAAY,EAAE,GAAG,CAAC;IACvDA,YAAY,CAAC,CAAC;IACd,IAAIO,QAAQ,GAAG,IAAIC,gBAAgB,CAAC,MAAMH,qBAAqB,CAAC,CAAC,CAAC;IAClEE,QAAQ,CAACE,OAAO,CAACR,QAAQ,CAACS,eAAe,EAAE;MACzCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,QAAQ;IACrD,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1gB,cAAc,EAAEib,wBAAwB,EAAEI,mBAAmB,CAAC,CAAC;EACnE,MAAMsF,gBAAgB,GAAG;IACvB9hB,MAAM,EAAE;MACN;MACA;MACA6C,mBAAmB,EAAE,KAAK;MAC1BD,6BAA6B,EAAE;IACjC,CAAC;IACD3C,SAAS,EAAE,IAAI;IACfiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACVkD,OAAO,EAAE,GAAG;MACZzD,GAAG,EAAE,EAAE;MACPsD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD5B,cAAc,EAAE;MAAE+B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DvD,YAAY,EAAE,CAAC;EACjB,CAAC;EACD,OAAO,eAAgB+Z,MAAM,CAAC7Y,aAAa,CAACrE,gBAAgB,CAACuE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgB0Y,MAAM,CAAC7Y,aAAa,CAACsZ,4BAA4B,EAAE;IAAE9Y,QAAQ,EAAEyY;EAAU,CAAC,EAAE,eAAgBJ,MAAM,CAAC7Y,aAAa,CAACxE,gBAAgB,CAAC0E,QAAQ,EAAE;IAAEC,KAAK,EAAE6gB;EAAiB,CAAC,EAAE,eAAgBnI,MAAM,CAAC7Y,aAAa,CAACnE,cAAc,EAAE;IAAEkE,MAAM;IAAEkhB,SAAS,EAAEnI,QAAQ,CAACmI;EAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACrX;AACA,SAAStE,6BAA6BA,CAACrd,KAAK,EAAE4c,OAAO,EAAE;EACrD,IAAIgF,cAAc,GAAGhF,OAAO,IAAI5c,KAAK,CAACI,EAAE,IAAIwc,OAAO,CAAC7c,UAAU;EAC9D,IAAI6X,WAAW,GAAGgF,OAAO,EAAE7c,UAAU,CAACC,KAAK,CAACI,EAAE,CAAC;EAC/C,IAAIyhB,eAAe,GAAGjF,OAAO,EAAEpS,MAAM,IAAIxK,KAAK,CAACI,EAAE,IAAIwc,OAAO,CAACpS,MAAM;EACnE,IAAIsX,YAAY,GAAGlF,OAAO,EAAEpS,MAAM,GAAGxK,KAAK,CAACI,EAAE,CAAC;EAC9C,IAAI2hB,kBAAkB,GAAG/hB,KAAK,CAACM,YAAY,EAAEc,OAAO,KAAK,IAAI,IAAI,CAACpB,KAAK,CAACO,SAAS;EAAI;EACrF;EACA;EACAP,KAAK,CAACgf,YAAY,IAAI,CAAChf,KAAK,CAACmf,OAAO;EACpC,IAAI6C,SAAS,GAAG;IACd5hB,EAAE,EAAEJ,KAAK,CAACI,EAAE;IACZ+e,OAAO,EAAEnf,KAAK,CAACmf,OAAO;IACtB8C,YAAY,EAAEjiB,KAAK,CAACiiB,YAAY;IAChCne,MAAM,EAAE9D,KAAK,CAAC8D,MAAM;IACpBO,gBAAgB,EAAErE,KAAK,CAACqE,gBAAgB;IACxC2Z,sBAAsB,EAAEhe,KAAK,CAACge,sBAAsB;IACpDxa,KAAK,EAAExD,KAAK,CAACwD,KAAK;IAClBK,MAAM,EAAE7D,KAAK,CAACM,YAAY,GAAG,OAAOsD,IAAI,EAAEse,WAAW,KAAK;MACxD,IAAI;QACF,IAAIhZ,MAAM,GAAG,MAAMlJ,KAAK,CAACM,YAAY,CAAC;UACpC,GAAGsD,IAAI;UACPue,YAAY,EAAEA,CAAA,KAAM;YAClBC,+BAA+B,CAC7B,QAAQ,EACRpiB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;YACD,IAAIwhB,kBAAkB,EAAE;cACtB,IAAIH,cAAc,EAAE;gBAClB,OAAOhK,WAAW;cACpB;cACA,IAAIiK,eAAe,EAAE;gBACnB,MAAMC,YAAY;cACpB;YACF;YACA,OAAOO,eAAe,CAACH,WAAW,CAAC;UACrC;QACF,CAAC,CAAC;QACF,OAAOhZ,MAAM;MACf,CAAC,SAAS;QACR6Y,kBAAkB,GAAG,KAAK;MAC5B;IACF,CAAC;IACC;IACA;IACA,CAAC3L,CAAC,EAAE8L,WAAW,KAAKG,eAAe,CAACH,WAAW,CAChD;IACDve,MAAM,EAAE3D,KAAK,CAACsiB,YAAY,GAAG,CAAC1e,IAAI,EAAEse,WAAW,KAAKliB,KAAK,CAACsiB,YAAY,CAAC;MACrE,GAAG1e,IAAI;MACP2e,YAAY,EAAE,MAAAA,CAAA,KAAY;QACxBH,+BAA+B,CAC7B,QAAQ,EACRpiB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;QACD,OAAO,MAAM8hB,eAAe,CAACH,WAAW,CAAC;MAC3C;IACF,CAAC,CAAC,GAAGliB,KAAK,CAACiE,SAAS,GAAG,CAACmS,CAAC,EAAE8L,WAAW,KAAKG,eAAe,CAACH,WAAW,CAAC,GAAG,MAAM;MAC9E,MAAMjkB,oBAAoB,CAAC,QAAQ,EAAE+B,KAAK,CAACI,EAAE,CAAC;IAChD,CAAC;IACDmD,IAAI,EAAEvD,KAAK,CAACuD,IAAI;IAChBQ,gBAAgB,EAAE/D,KAAK,CAAC+D,gBAAgB;IACxC;IACA;IACAxD,SAAS,EAAE,IAAI;IACf4D,eAAe,EAAEnE,KAAK,CAACM,YAAY,IAAI,IAAI;IAC3C2D,SAAS,EAAEjE,KAAK,CAACiE,SAAS;IAC1BC,eAAe,EAAElE,KAAK,CAACsiB,YAAY,IAAI,IAAI;IAC3CrD,mBAAmB,EAAEjf,KAAK,CAAC+D,gBAAgB,IAAI;EACjD,CAAC;EACD,IAAI,OAAOie,SAAS,CAACne,MAAM,KAAK,UAAU,EAAE;IAC1Cme,SAAS,CAACne,MAAM,CAACzC,OAAO,GAAG/C,wBAAwB,CACjD2B,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC;EACH;EACA,OAAOyhB,SAAS;AAClB;AACA,SAASK,eAAeA,CAACH,WAAW,EAAE;EACpCzkB,SAAS,CAAC,OAAOykB,WAAW,KAAK,UAAU,EAAE,+BAA+B,CAAC;EAC7E,OAAOA,WAAW,CAAC,CAAC;AACtB;AACA,SAASE,+BAA+BA,CAACvF,IAAI,EAAE3c,OAAO,EAAEsiB,UAAU,EAAE;EAClE,IAAI,CAACA,UAAU,EAAE;IACf,IAAIC,EAAE,GAAG5F,IAAI,KAAK,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB;IAChE,IAAI6F,GAAG,GAAG,0BAA0BD,EAAE,2CAA2C5F,IAAI,eAAe3c,OAAO,IAAI;IAC/G8M,OAAO,CAACzG,KAAK,CAACmc,GAAG,CAAC;IAClB,MAAM,IAAIzmB,iBAAiB,CAAC,GAAG,EAAE,aAAa,EAAE,IAAIoH,KAAK,CAACqf,GAAG,CAAC,EAAE,IAAI,CAAC;EACvE;AACF;AACA,IAAIhC,SAAS,GAAG,eAAgB,IAAI1P,GAAG,CAAC,CAAC;AACzC,IAAI2R,sBAAsB,GAAG,GAAG;AAChC,IAAIxE,eAAe,GAAG,eAAgB,IAAInN,GAAG,CAAC,CAAC;AAC/C,IAAI4R,SAAS,GAAG,IAAI;AACpB,SAASC,cAAcA,CAAC5M,KAAK,EAAE;EAC7B,IAAIA,KAAK,CAACpP,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EACA,IAAIoP,KAAK,CAACpP,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAIvH,GAAG,CAAC,GAAG2W,KAAK,CAAC,CAAC,CAAC,WAAW,EAAEqG,MAAM,CAACpb,QAAQ,CAACuf,MAAM,CAAC;EAChE;EACA,MAAMpE,SAAS,GAAGC,MAAM;EACxB,IAAIjR,QAAQ,GAAG,CAACgR,SAAS,CAACS,QAAQ,CAACzR,QAAQ,IAAI,EAAE,EAAEjN,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;EAC1E,IAAIgB,GAAG,GAAG,IAAIE,GAAG,CAAC,GAAG+L,QAAQ,YAAY,EAAEiR,MAAM,CAACpb,QAAQ,CAACuf,MAAM,CAAC;EAClExK,KAAK,CAAC6M,IAAI,CAAC,CAAC,CAAC/U,OAAO,CAAExK,IAAI,IAAKnE,GAAG,CAAC4M,YAAY,CAACO,MAAM,CAAC,GAAG,EAAEhJ,IAAI,CAAC,CAAC;EAClE,OAAOnE,GAAG;AACZ;AACA,eAAegf,4BAA4BA,CAACnI,KAAK,EAAE+F,wBAAwB,EAAEI,mBAAmB,EAAEzP,MAAM,EAAE;EACxG,IAAIvN,GAAG,GAAGyjB,cAAc,CAAC5M,KAAK,CAAC;EAC/B,IAAI7W,GAAG,IAAI,IAAI,EAAE;IACf;EACF;EACA,IAAIA,GAAG,CAAC6J,QAAQ,CAAC,CAAC,CAACpC,MAAM,GAAG+b,SAAS,EAAE;IACrClC,SAAS,CAACqC,KAAK,CAAC,CAAC;IACjB;EACF;EACA,IAAIxN,QAAQ,GAAG,MAAM6G,mBAAmB,CAAC,IAAIvP,OAAO,CAACzN,GAAG,EAAE;IAAEuN;EAAO,CAAC,CAAC,CAAC;EACtE,IAAI,CAAC4I,QAAQ,CAAC9I,IAAI,IAAI8I,QAAQ,CAAC1J,MAAM,GAAG,GAAG,IAAI0J,QAAQ,CAAC1J,MAAM,IAAI,GAAG,EAAE;IACrE,MAAM,IAAIxI,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,IAAIuZ,OAAO,GAAG,MAAMZ,wBAAwB,CAACzG,QAAQ,CAAC9I,IAAI,EAAE;IAC1DiQ,mBAAmB,EAAE,KAAK;EAC5B,CAAC,CAAC;EACF,IAAIE,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIxZ,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EACA4S,KAAK,CAAClI,OAAO,CAAEiV,CAAC,IAAKC,cAAc,CAACD,CAAC,EAAE7E,eAAe,CAAC,CAAC;EACxDvB,OAAO,CAAC5G,OAAO,CAACjI,OAAO,CAAEiV,CAAC,IAAK;IAC7B1G,MAAM,CAACQ,QAAQ,CAACM,WAAW,CACzB4F,CAAC,CAAC7f,QAAQ,IAAI,IAAI,EAClB,CAACka,6BAA6B,CAAC2F,CAAC,CAAC,CACnC,CAAC;EACH,CAAC,CAAC;AACJ;AACA,SAASC,cAAcA,CAAC1f,IAAI,EAAE2f,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACC,IAAI,IAAIR,sBAAsB,EAAE;IACxC,IAAIS,KAAK,GAAGF,KAAK,CAACpV,MAAM,CAAC,CAAC,CAACuV,IAAI,CAAC,CAAC,CAACxiB,KAAK;IACvCqiB,KAAK,CAAChX,MAAM,CAACkX,KAAK,CAAC;EACrB;EACAF,KAAK,CAAC3M,GAAG,CAAChT,IAAI,CAAC;AACjB;AACA,SAAS0d,QAAQA,CAACqC,QAAQ,EAAEC,IAAI,EAAE;EAChC,IAAI/P,SAAS;EACb,OAAO,YAAa;IAAA,SAAAgQ,IAAA,GAAAtc,SAAA,CAAAL,MAAA,EAATjD,IAAI,OAAAkd,KAAA,CAAA0C,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAJ7f,IAAI,CAAA6f,IAAA,IAAAvc,SAAA,CAAAuc,IAAA;IAAA;IACbnH,MAAM,CAAC1I,YAAY,CAACJ,SAAS,CAAC;IAC9BA,SAAS,GAAG8I,MAAM,CAAC7I,UAAU,CAAC,MAAM6P,QAAQ,CAAC,GAAG1f,IAAI,CAAC,EAAE2f,IAAI,CAAC;EAC9D,CAAC;AACH;;AAEA;AACA,OAAO,KAAKG,MAAM,MAAM,OAAO;;AAE/B;AACA,IAAIC,QAAQ,GAAG,IAAI1e,WAAW,CAAC,CAAC;AAChC,IAAI2e,OAAO,GAAG,gBAAgB;AAC9B,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EACnC,IAAIC,OAAO,GAAG,IAAIpiB,WAAW,CAAC,CAAC;EAC/B,IAAIqiB,wBAAwB;EAC5B,IAAIC,iBAAiB,GAAG,IAAIC,OAAO,CAChCC,OAAO,IAAKH,wBAAwB,GAAGG,OAC1C,CAAC;EACD,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,IAAI;EAClB,SAASC,mBAAmBA,CAAC7V,UAAU,EAAE;IACvC,KAAK,IAAI8V,KAAK,IAAIH,QAAQ,EAAE;MAC1B,IAAII,GAAG,GAAGV,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;QAAEjW,MAAM,EAAE;MAAK,CAAC,CAAC;MACjD,IAAIkW,GAAG,CAACzP,QAAQ,CAAC4O,OAAO,CAAC,EAAE;QACzBa,GAAG,GAAGA,GAAG,CAACve,KAAK,CAAC,CAAC,EAAE,CAAC0d,OAAO,CAAC/c,MAAM,CAAC;MACrC;MACA6H,UAAU,CAACC,OAAO,CAACgV,QAAQ,CAACtmB,MAAM,CAAConB,GAAG,CAAC,CAAC;IAC1C;IACAJ,QAAQ,CAACxd,MAAM,GAAG,CAAC;IACnByd,OAAO,GAAG,IAAI;EAChB;EACA,OAAO,IAAIK,eAAe,CAAC;IACzBC,SAASA,CAACJ,KAAK,EAAE9V,UAAU,EAAE;MAC3B2V,QAAQ,CAAChY,IAAI,CAACmY,KAAK,CAAC;MACpB,IAAIF,OAAO,EAAE;QACX;MACF;MACAA,OAAO,GAAG7Q,UAAU,CAAC,YAAY;QAC/B8Q,mBAAmB,CAAC7V,UAAU,CAAC;QAC/B,IAAI,CAAC0V,UAAU,EAAE;UACfA,UAAU,GAAG,IAAI;UACjBS,cAAc,CAACf,SAAS,EAAEpV,UAAU,CAAC,CAACoW,KAAK,CAAEjT,GAAG,IAAKnD,UAAU,CAACnI,KAAK,CAACsL,GAAG,CAAC,CAAC,CAACkT,IAAI,CAACf,wBAAwB,CAAC;QAC5G;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IACD,MAAMgB,KAAKA,CAACtW,UAAU,EAAE;MACtB,MAAMuV,iBAAiB;MACvB,IAAIK,OAAO,EAAE;QACX1Q,YAAY,CAAC0Q,OAAO,CAAC;QACrBC,mBAAmB,CAAC7V,UAAU,CAAC;MACjC;MACAA,UAAU,CAACC,OAAO,CAACgV,QAAQ,CAACtmB,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACvD;EACF,CAAC,CAAC;AACJ;AACA,eAAewnB,cAAcA,CAACf,SAAS,EAAEpV,UAAU,EAAE;EACnD,IAAIqV,OAAO,GAAG,IAAIpiB,WAAW,CAAC,OAAO,EAAE;IAAEsjB,KAAK,EAAE;EAAK,CAAC,CAAC;EACvD,MAAMzjB,MAAM,GAAGsiB,SAAS,CAACriB,SAAS,CAAC,CAAC;EACpC,IAAI;IACF,IAAIyjB,IAAI;IACR,OAAO,CAACA,IAAI,GAAG,MAAM1jB,MAAM,CAAC0jB,IAAI,CAAC,CAAC,KAAK,CAACA,IAAI,CAACC,IAAI,EAAE;MACjD,MAAMX,KAAK,GAAGU,IAAI,CAACrkB,KAAK;MACxB,IAAI;QACFukB,UAAU,CACRxc,IAAI,CAACC,SAAS,CAACkb,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;UAAEjW,MAAM,EAAE;QAAK,CAAC,CAAC,CAAC,EACvDG,UACF,CAAC;MACH,CAAC,CAAC,OAAOmD,GAAG,EAAE;QACZ,IAAIwT,MAAM,GAAGzc,IAAI,CAACC,SAAS,CAAClD,IAAI,CAACC,MAAM,CAAC0f,aAAa,CAAC,GAAGd,KAAK,CAAC,CAAC,CAAC;QACjEY,UAAU,CACR,wBAAwBC,MAAM,2BAA2B,EACzD3W,UACF,CAAC;MACH;IACF;EACF,CAAC,SAAS;IACRlN,MAAM,CAAC+jB,WAAW,CAAC,CAAC;EACtB;EACA,IAAIC,SAAS,GAAGzB,OAAO,CAACW,MAAM,CAAC,CAAC;EAChC,IAAIc,SAAS,CAAC3e,MAAM,EAAE;IACpBue,UAAU,CAACxc,IAAI,CAACC,SAAS,CAAC2c,SAAS,CAAC,EAAE9W,UAAU,CAAC;EACnD;AACF;AACA,SAAS0W,UAAUA,CAACZ,KAAK,EAAE9V,UAAU,EAAE;EACrCA,UAAU,CAACC,OAAO,CAChBgV,QAAQ,CAACtmB,MAAM,CACb,WAAWooB,YAAY,CACrB,kCAAkCjB,KAAK,GACzC,CAAC,WACH,CACF,CAAC;AACH;AACA,SAASiB,YAAYA,CAACC,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAACtnB,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC;AAC7E;;AAEA;AACA,eAAeunB,qBAAqBA,CAAAC,MAAA,EAMjC;EAAA,IANkC;IACnCna,OAAO;IACPoa,WAAW;IACX7J,wBAAwB;IACxB8J,UAAU;IACV1kB,OAAO,GAAG;EACZ,CAAC,GAAAwkB,MAAA;EACC,MAAMxmB,GAAG,GAAG,IAAIE,GAAG,CAACmM,OAAO,CAACrM,GAAG,CAAC;EAChC,MAAM2mB,aAAa,GAAGC,oBAAoB,CAAC5mB,GAAG,CAAC;EAC/C,MAAM6mB,qBAAqB,GAAGF,aAAa,IAAIG,iBAAiB,CAAC9mB,GAAG,CAAC,IAAIqM,OAAO,CAACiB,OAAO,CAACwE,GAAG,CAAC,eAAe,CAAC;EAC7G,MAAMiV,cAAc,GAAG,MAAMN,WAAW,CAACpa,OAAO,CAAC;EACjD,IAAIwa,qBAAqB,IAAIE,cAAc,CAACzZ,OAAO,CAACiB,GAAG,CAAC,uBAAuB,CAAC,KAAK,MAAM,EAAE;IAC3F,OAAOwY,cAAc;EACvB;EACA,IAAI,CAACA,cAAc,CAAC1Z,IAAI,EAAE;IACxB,MAAM,IAAIpJ,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACA,IAAI+iB,eAAe,GAAG,IAAI;EAC1B,IAAIhlB,OAAO,EAAE;IACXglB,eAAe,GAAGD,cAAc,CAACE,KAAK,CAAC,CAAC;EAC1C;EACA,MAAM5Z,IAAI,GAAG0Z,cAAc,CAAC1Z,IAAI;EAChC,IAAI6Z,cAAc;EAClB,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAID,cAAc,EAAE,OAAOA,cAAc;IACzCA,cAAc,GAAGtK,wBAAwB,CAACvP,IAAI,CAAC;IAC/C,OAAO6Z,cAAc;EACvB,CAAC;EACD,IAAI;IACF,MAAMlX,IAAI,GAAG,MAAM0W,UAAU,CAACS,UAAU,CAAC;IACzC,MAAM7Z,OAAO,GAAG,IAAI6D,OAAO,CAAC4V,cAAc,CAACzZ,OAAO,CAAC;IACnDA,OAAO,CAACuG,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;IACxC,IAAI,CAAC7R,OAAO,EAAE;MACZ,OAAO,IAAI0K,QAAQ,CAACsD,IAAI,EAAE;QACxBvD,MAAM,EAAEsa,cAAc,CAACta,MAAM;QAC7Ba;MACF,CAAC,CAAC;IACJ;IACA,IAAI,CAAC0Z,eAAe,EAAE3Z,IAAI,EAAE;MAC1B,MAAM,IAAIpJ,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,MAAMmjB,KAAK,GAAGpX,IAAI,CAACqX,WAAW,CAAC5C,gBAAgB,CAACuC,eAAe,CAAC3Z,IAAI,CAAC,CAAC;IACtE,OAAO,IAAIX,QAAQ,CAAC0a,KAAK,EAAE;MACzB3a,MAAM,EAAEsa,cAAc,CAACta,MAAM;MAC7Ba;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOga,MAAM,EAAE;IACf,IAAIA,MAAM,YAAY5a,QAAQ,EAAE;MAC9B,OAAO4a,MAAM;IACf;IACA,MAAMA,MAAM;EACd;AACF;AACA,SAASC,eAAeA,CAAAC,MAAA,EAErB;EAAA,IAFsB;IACvBL;EACF,CAAC,GAAAK,MAAA;EACC,MAAMhK,OAAO,GAAG8G,MAAM,CAACmD,GAAG,CAACN,UAAU,CAAC,CAAC,CAAC;EACxC,IAAI3J,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAI/Q,QAAQ,CAAC,IAAI,EAAE;MACvBD,MAAM,EAAE+Q,OAAO,CAAC/Q,MAAM;MACtBa,OAAO,EAAE;QACPoa,QAAQ,EAAElK,OAAO,CAAC1b;MACpB;IACF,CAAC,CAAC;EACJ;EACA,IAAI0b,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE,OAAO,IAAI;EAC1C,IAAIkK,iBAAiB,GAAG;IAAE,GAAGnK,OAAO,CAAC7c;EAAW,CAAC;EACjD,KAAK,MAAMC,KAAK,IAAI4c,OAAO,CAAC3c,OAAO,EAAE;IACnC,IAAI5B,wBAAwB,CAC1B2B,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC,KAAKP,KAAK,CAACge,sBAAsB,IAAI,CAAChe,KAAK,CAACO,SAAS,CAAC,EAAE;MACvD,OAAOwmB,iBAAiB,CAAC/mB,KAAK,CAACI,EAAE,CAAC;IACpC;EACF;EACA,MAAMjB,OAAO,GAAG;IACd8Q,UAAU,EAAE2M,OAAO,CAAC3M,UAAU;IAC9BD,aAAa,EAAE,CAAC,CAAC;IACjB3E,QAAQ,EAAEuR,OAAO,CAACvR,QAAQ;IAC1Bb,MAAM,EAAEoS,OAAO,CAACpS,MAAM;IACtBzK,UAAU,EAAEgnB,iBAAiB;IAC7B7W,aAAa,EAAE,CAAC,CAAC;IACjBhP,QAAQ,EAAE0b,OAAO,CAAC1b,QAAQ;IAC1BwQ,UAAU,EAAE,GAAG;IACfzR,OAAO,EAAE2c,OAAO,CAAC3c,OAAO,CAACmD,GAAG,CAAEpD,KAAK,KAAM;MACvCsL,MAAM,EAAEtL,KAAK,CAACsL,MAAM;MACpBF,QAAQ,EAAEpL,KAAK,CAACoL,QAAQ;MACxB4b,YAAY,EAAEhnB,KAAK,CAACgnB,YAAY;MAChC7mB,KAAK,EAAE;QACLC,EAAE,EAAEJ,KAAK,CAACI,EAAE;QACZuD,MAAM,EAAE3D,KAAK,CAACiE,SAAS,IAAI,CAAC,CAACjE,KAAK,CAACsiB,YAAY;QAC/Cxe,MAAM,EAAE9D,KAAK,CAAC8D,MAAM;QACpBO,gBAAgB,EAAErE,KAAK,CAACqE,gBAAgB;QACxCR,MAAM,EAAE7D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;QAC/CkD,KAAK,EAAExD,KAAK,CAACwD,KAAK;QAClBD,IAAI,EAAEvD,KAAK,CAACuD,IAAI;QAChBQ,gBAAgB,EAAE/D,KAAK,CAAC+D;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMtD,MAAM,GAAGtD,kBAAkB,CAC/Byf,OAAO,CAAC3c,OAAO,CAAC0d,WAAW,CAAC,CAACC,QAAQ,EAAE5d,KAAK,KAAK;IAC/C,MAAMG,KAAK,GAAG;MACZC,EAAE,EAAEJ,KAAK,CAACI,EAAE;MACZuD,MAAM,EAAE3D,KAAK,CAACiE,SAAS,IAAI,CAAC,CAACjE,KAAK,CAACsiB,YAAY;MAC/CnD,OAAO,EAAEnf,KAAK,CAACmf,OAAO;MACtB8C,YAAY,EAAEjiB,KAAK,CAACiiB,YAAY;MAChCne,MAAM,EAAE9D,KAAK,CAAC8D,MAAM;MACpBO,gBAAgB,EAAE,CAAC,CAACrE,KAAK,CAACiiB,YAAY;MACtCjE,sBAAsB,EAAEhe,KAAK,CAACge,sBAAsB;MACpDxa,KAAK,EAAExD,KAAK,CAACwD,KAAK;MAClBK,MAAM,EAAE7D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;MAC/CiD,IAAI,EAAEvD,KAAK,CAACuD,IAAI;MAChBQ,gBAAgB,EAAE/D,KAAK,CAAC+D;IAC1B,CAAC;IACD,IAAI6Z,QAAQ,CAAC/W,MAAM,GAAG,CAAC,EAAE;MACvB1G,KAAK,CAAC0E,QAAQ,GAAG+Y,QAAQ;IAC3B;IACA,OAAO,CAACzd,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC,EACNhB,OACF,CAAC;EACD,MAAMuiB,gBAAgB,GAAG;IACvB9hB,MAAM,EAAE;MACN;MACA;MACA6C,mBAAmB,EAAE,KAAK;MAC1BD,6BAA6B,EAAE;IACjC,CAAC;IACD3C,SAAS,EAAE,KAAK;IAChBiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACVkD,OAAO,EAAE,GAAG;MACZzD,GAAG,EAAE,EAAE;MACPsD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD5B,cAAc,EAAE;MAAE+B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DvD,YAAY,EAAE,CAAC;EACjB,CAAC;EACD,OAAO,eAAgBkkB,MAAM,CAAChjB,aAAa,CAACrE,gBAAgB,CAACuE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgB6iB,MAAM,CAAChjB,aAAa,CAACsZ,4BAA4B,EAAE;IAAE9Y,QAAQ,EAAE0b,OAAO,CAAC1b;EAAS,CAAC,EAAE,eAAgBwiB,MAAM,CAAChjB,aAAa,CAACxE,gBAAgB,CAAC0E,QAAQ,EAAE;IAAEC,KAAK,EAAE6gB;EAAiB,CAAC,EAAE,eAAgBgC,MAAM,CAAChjB,aAAa,CAC1ThE,oBAAoB,EACpB;IACEyC,OAAO;IACPsB,MAAM;IACNW,OAAO,EAAE,KAAK;IACd/B,KAAK,EAAEud,OAAO,CAACvd;EACjB,CACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAAS2mB,oBAAoBA,CAAC5mB,GAAG,EAAE;EACjC,OAAOA,GAAG,CAACgM,QAAQ,CAAC4J,QAAQ,CAAC,MAAM,CAAC;AACtC;AACA,SAASkR,iBAAiBA,CAAC9mB,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAACgM,QAAQ,CAAC4J,QAAQ,CAAC,WAAW,CAAC;AAC3C;;AAEA;AACA,SAASiS,YAAYA,CAAA,EAAG;EACtB,IAAIC,QAAQ,GAAG,IAAIjiB,WAAW,CAAC,CAAC;EAChC,IAAIkiB,gBAAgB,GAAG,IAAI;EAC3B,IAAIrD,SAAS,GAAG,IAAItV,cAAc,CAAC;IACjCC,KAAKA,CAACC,UAAU,EAAE;MAChB,IAAI,OAAO4N,MAAM,KAAK,WAAW,EAAE;QACjC;MACF;MACA,IAAI8K,WAAW,GAAI5C,KAAK,IAAK;QAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7B9V,UAAU,CAACC,OAAO,CAACuY,QAAQ,CAAC7pB,MAAM,CAACmnB,KAAK,CAAC,CAAC;QAC5C,CAAC,MAAM;UACL9V,UAAU,CAACC,OAAO,CAAC6V,KAAK,CAAC;QAC3B;MACF,CAAC;MACDlI,MAAM,CAAC+K,aAAa,KAAK/K,MAAM,CAAC+K,aAAa,GAAG,EAAE,CAAC;MACnD/K,MAAM,CAAC+K,aAAa,CAACtZ,OAAO,CAACqZ,WAAW,CAAC;MACzC9K,MAAM,CAAC+K,aAAa,CAAChb,IAAI,GAAImY,KAAK,IAAK;QACrC4C,WAAW,CAAC5C,KAAK,CAAC;QAClB,OAAO,CAAC;MACV,CAAC;MACD2C,gBAAgB,GAAGzY,UAAU;IAC/B;EACF,CAAC,CAAC;EACF,IAAI,OAAOkS,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAAC0G,UAAU,KAAK,SAAS,EAAE;IACxE1G,QAAQ,CAACjN,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;MAClDwT,gBAAgB,EAAEvY,KAAK,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,MAAM;IACLuY,gBAAgB,EAAEvY,KAAK,CAAC,CAAC;EAC3B;EACA,OAAOkV,SAAS;AAClB;;AAEA;AACA,SAASyD,iBAAiBA,CAAC/c,MAAM,EAAE;EACjC,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIO,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAAC1F,GAAG,EAAE2F,GAAG,CAAC,IAAIP,OAAO,EAAE;IAC9B,IAAIO,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAAC1F,GAAG,CAAC,GAAG,IAAIpJ,iBAAiB,CACrC+O,GAAG,CAACa,MAAM,EACVb,GAAG,CAAC+I,UAAU,EACd/I,GAAG,CAAC+D,IAAI,EACR/D,GAAG,CAACwc,QAAQ,KAAK,IACnB,CAAC;IACH,CAAC,MAAM,IAAIxc,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;MACxC,IAAID,GAAG,CAACE,SAAS,EAAE;QACjB,IAAIuc,gBAAgB,GAAGnL,MAAM,CAACtR,GAAG,CAACE,SAAS,CAAC;QAC5C,IAAI,OAAOuc,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;YACF,IAAIlhB,KAAK,GAAG,IAAIkhB,gBAAgB,CAACzc,GAAG,CAACH,OAAO,CAAC;YAC7CtE,KAAK,CAAC+D,KAAK,GAAGU,GAAG,CAACV,KAAK;YACvBS,UAAU,CAAC1F,GAAG,CAAC,GAAGkB,KAAK;UACzB,CAAC,CAAC,OAAOqH,CAAC,EAAE,CACZ;QACF;MACF;MACA,IAAI7C,UAAU,CAAC1F,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAIkB,KAAK,GAAG,IAAIlD,KAAK,CAAC2H,GAAG,CAACH,OAAO,CAAC;QAClCtE,KAAK,CAAC+D,KAAK,GAAGU,GAAG,CAACV,KAAK;QACvBS,UAAU,CAAC1F,GAAG,CAAC,GAAGkB,KAAK;MACzB;IACF,CAAC,MAAM;MACLwE,UAAU,CAAC1F,GAAG,CAAC,GAAG2F,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;AAEA,SACE9L,YAAY,EACZ4C,gBAAgB,EAChBmF,YAAY,EACZoB,QAAQ,EACR4B,UAAU,EACVkD,iBAAiB,EACjBqH,oBAAoB,EACpBoD,aAAa,EACbK,SAAS,EACTC,oBAAoB,EACpBY,0BAA0B,EAC1BG,0BAA0B,EAC1BlM,IAAI,EACJ2M,gBAAgB,EAChBkC,2BAA2B,EAC3BG,gBAAgB,EAChB4D,iBAAiB,EACjBiG,qBAAqB,EACrBgB,eAAe,EACfM,YAAY,EACZM,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}