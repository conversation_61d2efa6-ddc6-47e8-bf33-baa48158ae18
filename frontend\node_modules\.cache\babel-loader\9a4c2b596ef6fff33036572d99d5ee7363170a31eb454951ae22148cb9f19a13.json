{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18.5 4.5c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2m-2.72 16.4.76.27c.62.21 1.27.33 1.96.33.68 0 1.34-.12 1.95-.33.27-.09.57-.02.78.18.39.4.23 1.06-.3 1.24-.76.27-1.58.41-2.43.41-.86 0-1.68-.14-2.45-.41L2.7 17.72c-.39-.14-.59-.57-.45-.95.14-.39.57-.6.96-.45l6.19 2.25 1.72-4.44-3.57-3.73c-.9-.94-.68-2.47.45-3.12l3.48-2.01c1.1-.64 2.52-.1 2.91 1.11l.33 1.08c.44 1.42 1.48 2.57 2.83 3.14l.29-.89c.13-.39.55-.61.94-.48.4.13.61.55.48.95l-.6 1.85c-.17.52-.72.82-1.24.65-2.02-.63-3.64-2.15-4.42-4.1l-2.53 1.45 2.23 2.55c.49.56.63 1.34.36 2.04l-1.78 4.63 3.09 1.12 2.1-6.44c.46.18.94.31 1.44.41z\"\n}), 'DownhillSkiingRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/icons-material/esm/DownhillSkiingRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18.5 4.5c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2m-2.72 16.4.76.27c.62.21 1.27.33 1.96.33.68 0 1.34-.12 1.95-.33.27-.09.57-.02.78.18.39.4.23 1.06-.3 1.24-.76.27-1.58.41-2.43.41-.86 0-1.68-.14-2.45-.41L2.7 17.72c-.39-.14-.59-.57-.45-.95.14-.39.57-.6.96-.45l6.19 2.25 1.72-4.44-3.57-3.73c-.9-.94-.68-2.47.45-3.12l3.48-2.01c1.1-.64 2.52-.1 2.91 1.11l.33 1.08c.44 1.42 1.48 2.57 2.83 3.14l.29-.89c.13-.39.55-.61.94-.48.4.13.61.55.48.95l-.6 1.85c-.17.52-.72.82-1.24.65-2.02-.63-3.64-2.15-4.42-4.1l-2.53 1.45 2.23 2.55c.49.56.63 1.34.36 2.04l-1.78 4.63 3.09 1.12 2.1-6.44c.46.18.94.31 1.44.41z\"\n}), 'DownhillSkiingRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,uBAAuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}