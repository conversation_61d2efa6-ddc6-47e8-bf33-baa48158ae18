{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineContentUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineContent', slot);\n}\nconst timelineContentClasses = generateUtilityClasses('MuiTimelineContent', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);\nexport default timelineContentClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTimelineContentUtilityClass", "slot", "timelineContentClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineContent/timelineContentClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineContentUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineContent', slot);\n}\nconst timelineContentClasses = generateUtilityClasses('MuiTimelineContent', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);\nexport default timelineContentClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,8BAA8BA,CAACC,IAAI,EAAE;EACnD,OAAOH,oBAAoB,CAAC,oBAAoB,EAAEG,IAAI,CAAC;AACzD;AACA,MAAMC,sBAAsB,GAAGH,sBAAsB,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,mBAAmB,EAAE,0BAA0B,CAAC,CAAC;AACvK,eAAeG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}