{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport Image, { propTypes as imagePropTypes } from './Image';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FigureImage = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    className,\n    fluid = true,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/_jsx(Image, {\n    ref: ref,\n    ...props,\n    fluid: fluid,\n    className: classNames(className, 'figure-img')\n  });\n});\nFigureImage.displayName = 'FigureImage';\nFigureImage.propTypes = imagePropTypes;\nexport default FigureImage;", "map": {"version": 3, "names": ["classNames", "React", "Image", "propTypes", "imagePropTypes", "jsx", "_jsx", "FigureImage", "forwardRef", "_ref", "ref", "className", "fluid", "props", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FigureImage.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport Image, { propTypes as imagePropTypes } from './Image';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FigureImage = /*#__PURE__*/React.forwardRef(({\n  className,\n  fluid = true,\n  ...props\n}, ref) => /*#__PURE__*/_jsx(Image, {\n  ref: ref,\n  ...props,\n  fluid: fluid,\n  className: classNames(className, 'figure-img')\n}));\nFigureImage.displayName = 'FigureImage';\nFigureImage.propTypes = imagePropTypes;\nexport default FigureImage;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,IAAIC,SAAS,IAAIC,cAAc,QAAQ,SAAS;AAC5D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAI/CC,GAAG;EAAA,IAJ6C;IACjDC,SAAS;IACTC,KAAK,GAAG,IAAI;IACZ,GAAGC;EACL,CAAC,GAAAJ,IAAA;EAAA,OAAU,aAAaH,IAAI,CAACJ,KAAK,EAAE;IAClCQ,GAAG,EAAEA,GAAG;IACR,GAAGG,KAAK;IACRD,KAAK,EAAEA,KAAK;IACZD,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAE,YAAY;EAC/C,CAAC,CAAC;AAAA,EAAC;AACHJ,WAAW,CAACO,WAAW,GAAG,aAAa;AACvCP,WAAW,CAACJ,SAAS,GAAGC,cAAc;AACtC,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}