{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams,Navigate}from'react-router-dom';import{Container,Row,Col,<PERSON><PERSON>,Spinner}from'react-bootstrap';import cmsService from'../../services/cmsService';import SEOHead from'../seo/SEOHead';import CmsPageTemplate from'./templates/CmsPageTemplate';import LandingPageTemplate from'./templates/LandingPageTemplate';import BlogPostTemplate from'./templates/BlogPostTemplate';import MinimalTemplate from'./templates/MinimalTemplate';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const DynamicCmsPage=_ref=>{let{slug,className}=_ref;const{slug:routeSlug}=useParams();const pageSlug=slug||routeSlug;const[page,setPage]=useState(null);const[content,setContent]=useState('');const[seo,setSeo]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState('');useEffect(()=>{if(pageSlug){loadPage(pageSlug);}},[pageSlug]);const loadPage=async slug=>{try{setLoading(true);setError('');const pageData=await cmsService.getPageWithSEO(slug);const pageInfo=pageData.data;// Check if page is published\nif(!cmsService.isPagePublished(pageInfo)){setError('Page not found or not published');return;}setPage(pageInfo);setContent(cmsService.getPageContent(pageInfo));setSeo(pageData.data.seo||cmsService.getSEOData(pageInfo));}catch(err){var _err$response;console.error('Error loading page:',err);if(((_err$response=err.response)===null||_err$response===void 0?void 0:_err$response.status)===404){setError('Page not found');}else{setError('Failed to load page. Please try again later.');}}finally{setLoading(false);}};const renderSEOTags=()=>{var _page$creator;if(!seo||!page)return null;const baseUrl=process.env.REACT_APP_FRONTEND_URL||'http://localhost:3000';const pageUrl=`${baseUrl}/pages/${page.slug}`;// Generate structured data for the page\nconst structuredData={\"@context\":\"https://schema.org\",\"@type\":page.template==='blog'?'Article':'WebPage',\"headline\":seo.meta_title,\"description\":seo.meta_description,\"url\":pageUrl,\"datePublished\":page.published_at,\"dateModified\":page.updated_at,...(page.creator&&{\"author\":{\"@type\":\"Person\",\"name\":page.creator.name}}),...(seo.og_image&&{\"image\":{\"@type\":\"ImageObject\",\"url\":seo.og_image,\"alt\":seo.meta_title}}),\"publisher\":{\"@type\":\"Organization\",\"name\":\"Your Site Name\"}};return/*#__PURE__*/_jsx(SEOHead,{title:seo.meta_title,description:seo.meta_description,keywords:seo.meta_keywords,robots:seo.meta_robots,canonicalUrl:seo.canonical_url||pageUrl,ogTitle:seo.og_title,ogDescription:seo.og_description,ogImage:seo.og_image,ogType:page.template==='blog'?'article':'website',author:(_page$creator=page.creator)===null||_page$creator===void 0?void 0:_page$creator.name,publishedTime:page.published_at,modifiedTime:page.updated_at,structuredData:structuredData});};const renderTemplate=()=>{if(!page||!content)return null;const templateProps={page,content,className:className||''};switch(page.template){case'landing':return/*#__PURE__*/_jsx(LandingPageTemplate,{...templateProps});case'blog':return/*#__PURE__*/_jsx(BlogPostTemplate,{...templateProps});case'minimal':return/*#__PURE__*/_jsx(MinimalTemplate,{...templateProps});case'default':default:return/*#__PURE__*/_jsx(CmsPageTemplate,{...templateProps});}};if(!pageSlug){return/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true});}if(loading){return/*#__PURE__*/_jsx(Container,{className:\"py-5\",children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{xs:\"auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading page...\"})]})})})});}if(error){return/*#__PURE__*/_jsx(Container,{className:\"py-5\",children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{lg:8,children:/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",children:[/*#__PURE__*/_jsx(Alert.Heading,{children:error==='Page not found'?'Page Not Found':'Error Loading Page'}),/*#__PURE__*/_jsx(\"p\",{children:error}),error==='Page not found'&&/*#__PURE__*/_jsx(\"p\",{className:\"mb-0\",children:\"The page you're looking for doesn't exist or has been removed.\"})]})})})});}if(!page){return/*#__PURE__*/_jsx(Container,{className:\"py-5\",children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{lg:8,children:/*#__PURE__*/_jsxs(Alert,{variant:\"warning\",children:[/*#__PURE__*/_jsx(Alert.Heading,{children:\"No Content Available\"}),/*#__PURE__*/_jsx(\"p\",{children:\"This page exists but has no content to display.\"})]})})})});}return/*#__PURE__*/_jsxs(_Fragment,{children:[renderSEOTags(),/*#__PURE__*/_jsx(\"div\",{className:`dynamic-cms-page template-${page.template} ${className||''}`,children:renderTemplate()})]});};export default DynamicCmsPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Navigate", "Container", "Row", "Col", "<PERSON><PERSON>", "Spinner", "cmsService", "SEOHead", "CmsPageTemplate", "LandingPageTemplate", "BlogPostTemplate", "MinimalTemplate", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "DynamicCmsPage", "_ref", "slug", "className", "routeSlug", "pageSlug", "page", "setPage", "content", "<PERSON><PERSON><PERSON><PERSON>", "seo", "setSeo", "loading", "setLoading", "error", "setError", "loadPage", "pageData", "getPageWithSEO", "pageInfo", "data", "isPagePublished", "getPageContent", "getSEOData", "err", "_err$response", "console", "response", "status", "renderSEOTags", "_page$creator", "baseUrl", "process", "env", "REACT_APP_FRONTEND_URL", "pageUrl", "structuredData", "template", "meta_title", "meta_description", "published_at", "updated_at", "creator", "name", "og_image", "title", "description", "keywords", "meta_keywords", "robots", "meta_robots", "canonicalUrl", "canonical_url", "ogTitle", "og_title", "ogDescription", "og_description", "ogImage", "ogType", "author", "publishedTime", "modifiedTime", "renderTemplate", "templateProps", "to", "replace", "children", "xs", "animation", "size", "lg", "variant", "Heading"], "sources": ["C:/laragon/www/frontend/src/components/cms/DynamicCmsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, Navigate } from 'react-router-dom';\nimport { Container, Row, Col, Alert, Spinner } from 'react-bootstrap';\nimport cmsService, { CmsPage, CmsPageSEO } from '../../services/cmsService';\nimport SEOHead from '../seo/SEOHead';\nimport PuckRenderer from '../puck/PuckRenderer';\nimport CmsPageTemplate from './templates/CmsPageTemplate';\nimport LandingPageTemplate from './templates/LandingPageTemplate';\nimport BlogPostTemplate from './templates/BlogPostTemplate';\nimport MinimalTemplate from './templates/MinimalTemplate';\n\ninterface DynamicCmsPageProps {\n  slug?: string;\n  className?: string;\n}\n\nconst DynamicCmsPage: React.FC<DynamicCmsPageProps> = ({ slug, className }) => {\n  const { slug: routeSlug } = useParams<{ slug: string }>();\n  const pageSlug = slug || routeSlug;\n\n  const [page, setPage] = useState<CmsPage | null>(null);\n  const [content, setContent] = useState<string>('');\n  const [seo, setSeo] = useState<CmsPageSEO | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    if (pageSlug) {\n      loadPage(pageSlug);\n    }\n  }, [pageSlug]);\n\n  const loadPage = async (slug: string) => {\n    try {\n      setLoading(true);\n      setError('');\n\n      const pageData = await cmsService.getPageWithSEO(slug);\n      const pageInfo = pageData.data;\n\n      // Check if page is published\n      if (!cmsService.isPagePublished(pageInfo)) {\n        setError('Page not found or not published');\n        return;\n      }\n\n      setPage(pageInfo);\n      setContent(cmsService.getPageContent(pageInfo));\n      setSeo(pageData.data.seo || cmsService.getSEOData(pageInfo));\n\n    } catch (err: any) {\n      console.error('Error loading page:', err);\n      if (err.response?.status === 404) {\n        setError('Page not found');\n      } else {\n        setError('Failed to load page. Please try again later.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderSEOTags = () => {\n    if (!seo || !page) return null;\n\n    const baseUrl = process.env.REACT_APP_FRONTEND_URL || 'http://localhost:3000';\n    const pageUrl = `${baseUrl}/pages/${page.slug}`;\n\n    // Generate structured data for the page\n    const structuredData = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": page.template === 'blog' ? 'Article' : 'WebPage',\n      \"headline\": seo.meta_title,\n      \"description\": seo.meta_description,\n      \"url\": pageUrl,\n      \"datePublished\": page.published_at,\n      \"dateModified\": page.updated_at,\n      ...(page.creator && {\n        \"author\": {\n          \"@type\": \"Person\",\n          \"name\": page.creator.name\n        }\n      }),\n      ...(seo.og_image && {\n        \"image\": {\n          \"@type\": \"ImageObject\",\n          \"url\": seo.og_image,\n          \"alt\": seo.meta_title\n        }\n      }),\n      \"publisher\": {\n        \"@type\": \"Organization\",\n        \"name\": \"Your Site Name\"\n      }\n    };\n\n    return (\n      <SEOHead\n        title={seo.meta_title}\n        description={seo.meta_description}\n        keywords={seo.meta_keywords}\n        robots={seo.meta_robots}\n        canonicalUrl={seo.canonical_url || pageUrl}\n        ogTitle={seo.og_title}\n        ogDescription={seo.og_description}\n        ogImage={seo.og_image}\n        ogType={page.template === 'blog' ? 'article' : 'website'}\n        author={page.creator?.name}\n        publishedTime={page.published_at}\n        modifiedTime={page.updated_at}\n        structuredData={structuredData}\n      />\n    );\n  };\n\n  const renderTemplate = () => {\n    if (!page || !content) return null;\n\n    const templateProps = {\n      page,\n      content,\n      className: className || '',\n    };\n\n    switch (page.template) {\n      case 'landing':\n        return <LandingPageTemplate {...templateProps} />;\n      case 'blog':\n        return <BlogPostTemplate {...templateProps} />;\n      case 'minimal':\n        return <MinimalTemplate {...templateProps} />;\n      case 'default':\n      default:\n        return <CmsPageTemplate {...templateProps} />;\n    }\n  };\n\n  if (!pageSlug) {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  if (loading) {\n    return (\n      <Container className=\"py-5\">\n        <Row className=\"justify-content-center\">\n          <Col xs=\"auto\">\n            <div className=\"d-flex align-items-center\">\n              <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n              <span>Loading page...</span>\n            </div>\n          </Col>\n        </Row>\n      </Container>\n    );\n  }\n\n  if (error) {\n    return (\n      <Container className=\"py-5\">\n        <Row className=\"justify-content-center\">\n          <Col lg={8}>\n            <Alert variant=\"danger\">\n              <Alert.Heading>\n                {error === 'Page not found' ? 'Page Not Found' : 'Error Loading Page'}\n              </Alert.Heading>\n              <p>{error}</p>\n              {error === 'Page not found' && (\n                <p className=\"mb-0\">\n                  The page you're looking for doesn't exist or has been removed.\n                </p>\n              )}\n            </Alert>\n          </Col>\n        </Row>\n      </Container>\n    );\n  }\n\n  if (!page) {\n    return (\n      <Container className=\"py-5\">\n        <Row className=\"justify-content-center\">\n          <Col lg={8}>\n            <Alert variant=\"warning\">\n              <Alert.Heading>No Content Available</Alert.Heading>\n              <p>This page exists but has no content to display.</p>\n            </Alert>\n          </Col>\n        </Row>\n      </Container>\n    );\n  }\n\n  return (\n    <>\n      {renderSEOTags()}\n      <div className={`dynamic-cms-page template-${page.template} ${className || ''}`}>\n        {renderTemplate()}\n      </div>\n    </>\n  );\n};\n\nexport default DynamicCmsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,QAAQ,KAAQ,kBAAkB,CACtD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,KAAK,CAAEC,OAAO,KAAQ,iBAAiB,CACrE,MAAO,CAAAC,UAAU,KAA+B,2BAA2B,CAC3E,MAAO,CAAAC,OAAO,KAAM,gBAAgB,CAEpC,MAAO,CAAAC,eAAe,KAAM,6BAA6B,CACzD,MAAO,CAAAC,mBAAmB,KAAM,iCAAiC,CACjE,MAAO,CAAAC,gBAAgB,KAAM,8BAA8B,CAC3D,MAAO,CAAAC,eAAe,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAO1D,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAyB,IAAxB,CAAEC,IAAI,CAAEC,SAAU,CAAC,CAAAF,IAAA,CACxE,KAAM,CAAEC,IAAI,CAAEE,SAAU,CAAC,CAAGvB,SAAS,CAAmB,CAAC,CACzD,KAAM,CAAAwB,QAAQ,CAAGH,IAAI,EAAIE,SAAS,CAElC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAG5B,QAAQ,CAAiB,IAAI,CAAC,CACtD,KAAM,CAAC6B,OAAO,CAAEC,UAAU,CAAC,CAAG9B,QAAQ,CAAS,EAAE,CAAC,CAClD,KAAM,CAAC+B,GAAG,CAAEC,MAAM,CAAC,CAAGhC,QAAQ,CAAoB,IAAI,CAAC,CACvD,KAAM,CAACiC,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACmC,KAAK,CAAEC,QAAQ,CAAC,CAAGpC,QAAQ,CAAS,EAAE,CAAC,CAE9CC,SAAS,CAAC,IAAM,CACd,GAAIyB,QAAQ,CAAE,CACZW,QAAQ,CAACX,QAAQ,CAAC,CACpB,CACF,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAW,QAAQ,CAAG,KAAO,CAAAd,IAAY,EAAK,CACvC,GAAI,CACFW,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAA7B,UAAU,CAAC8B,cAAc,CAAChB,IAAI,CAAC,CACtD,KAAM,CAAAiB,QAAQ,CAAGF,QAAQ,CAACG,IAAI,CAE9B;AACA,GAAI,CAAChC,UAAU,CAACiC,eAAe,CAACF,QAAQ,CAAC,CAAE,CACzCJ,QAAQ,CAAC,iCAAiC,CAAC,CAC3C,OACF,CAEAR,OAAO,CAACY,QAAQ,CAAC,CACjBV,UAAU,CAACrB,UAAU,CAACkC,cAAc,CAACH,QAAQ,CAAC,CAAC,CAC/CR,MAAM,CAACM,QAAQ,CAACG,IAAI,CAACV,GAAG,EAAItB,UAAU,CAACmC,UAAU,CAACJ,QAAQ,CAAC,CAAC,CAE9D,CAAE,MAAOK,GAAQ,CAAE,KAAAC,aAAA,CACjBC,OAAO,CAACZ,KAAK,CAAC,qBAAqB,CAAEU,GAAG,CAAC,CACzC,GAAI,EAAAC,aAAA,CAAAD,GAAG,CAACG,QAAQ,UAAAF,aAAA,iBAAZA,aAAA,CAAcG,MAAM,IAAK,GAAG,CAAE,CAChCb,QAAQ,CAAC,gBAAgB,CAAC,CAC5B,CAAC,IAAM,CACLA,QAAQ,CAAC,8CAA8C,CAAC,CAC1D,CACF,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAgB,aAAa,CAAGA,CAAA,GAAM,KAAAC,aAAA,CAC1B,GAAI,CAACpB,GAAG,EAAI,CAACJ,IAAI,CAAE,MAAO,KAAI,CAE9B,KAAM,CAAAyB,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAI,uBAAuB,CAC7E,KAAM,CAAAC,OAAO,CAAG,GAAGJ,OAAO,UAAUzB,IAAI,CAACJ,IAAI,EAAE,CAE/C;AACA,KAAM,CAAAkC,cAAc,CAAG,CACrB,UAAU,CAAE,oBAAoB,CAChC,OAAO,CAAE9B,IAAI,CAAC+B,QAAQ,GAAK,MAAM,CAAG,SAAS,CAAG,SAAS,CACzD,UAAU,CAAE3B,GAAG,CAAC4B,UAAU,CAC1B,aAAa,CAAE5B,GAAG,CAAC6B,gBAAgB,CACnC,KAAK,CAAEJ,OAAO,CACd,eAAe,CAAE7B,IAAI,CAACkC,YAAY,CAClC,cAAc,CAAElC,IAAI,CAACmC,UAAU,CAC/B,IAAInC,IAAI,CAACoC,OAAO,EAAI,CAClB,QAAQ,CAAE,CACR,OAAO,CAAE,QAAQ,CACjB,MAAM,CAAEpC,IAAI,CAACoC,OAAO,CAACC,IACvB,CACF,CAAC,CAAC,CACF,IAAIjC,GAAG,CAACkC,QAAQ,EAAI,CAClB,OAAO,CAAE,CACP,OAAO,CAAE,aAAa,CACtB,KAAK,CAAElC,GAAG,CAACkC,QAAQ,CACnB,KAAK,CAAElC,GAAG,CAAC4B,UACb,CACF,CAAC,CAAC,CACF,WAAW,CAAE,CACX,OAAO,CAAE,cAAc,CACvB,MAAM,CAAE,gBACV,CACF,CAAC,CAED,mBACE3C,IAAA,CAACN,OAAO,EACNwD,KAAK,CAAEnC,GAAG,CAAC4B,UAAW,CACtBQ,WAAW,CAAEpC,GAAG,CAAC6B,gBAAiB,CAClCQ,QAAQ,CAAErC,GAAG,CAACsC,aAAc,CAC5BC,MAAM,CAAEvC,GAAG,CAACwC,WAAY,CACxBC,YAAY,CAAEzC,GAAG,CAAC0C,aAAa,EAAIjB,OAAQ,CAC3CkB,OAAO,CAAE3C,GAAG,CAAC4C,QAAS,CACtBC,aAAa,CAAE7C,GAAG,CAAC8C,cAAe,CAClCC,OAAO,CAAE/C,GAAG,CAACkC,QAAS,CACtBc,MAAM,CAAEpD,IAAI,CAAC+B,QAAQ,GAAK,MAAM,CAAG,SAAS,CAAG,SAAU,CACzDsB,MAAM,EAAA7B,aAAA,CAAExB,IAAI,CAACoC,OAAO,UAAAZ,aAAA,iBAAZA,aAAA,CAAca,IAAK,CAC3BiB,aAAa,CAAEtD,IAAI,CAACkC,YAAa,CACjCqB,YAAY,CAAEvD,IAAI,CAACmC,UAAW,CAC9BL,cAAc,CAAEA,cAAe,CAChC,CAAC,CAEN,CAAC,CAED,KAAM,CAAA0B,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI,CAACxD,IAAI,EAAI,CAACE,OAAO,CAAE,MAAO,KAAI,CAElC,KAAM,CAAAuD,aAAa,CAAG,CACpBzD,IAAI,CACJE,OAAO,CACPL,SAAS,CAAEA,SAAS,EAAI,EAC1B,CAAC,CAED,OAAQG,IAAI,CAAC+B,QAAQ,EACnB,IAAK,SAAS,CACZ,mBAAO1C,IAAA,CAACJ,mBAAmB,KAAKwE,aAAa,CAAG,CAAC,CACnD,IAAK,MAAM,CACT,mBAAOpE,IAAA,CAACH,gBAAgB,KAAKuE,aAAa,CAAG,CAAC,CAChD,IAAK,SAAS,CACZ,mBAAOpE,IAAA,CAACF,eAAe,KAAKsE,aAAa,CAAG,CAAC,CAC/C,IAAK,SAAS,CACd,QACE,mBAAOpE,IAAA,CAACL,eAAe,KAAKyE,aAAa,CAAG,CAAC,CACjD,CACF,CAAC,CAED,GAAI,CAAC1D,QAAQ,CAAE,CACb,mBAAOV,IAAA,CAACb,QAAQ,EAACkF,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,CACpC,CAEA,GAAIrD,OAAO,CAAE,CACX,mBACEjB,IAAA,CAACZ,SAAS,EAACoB,SAAS,CAAC,MAAM,CAAA+D,QAAA,cACzBvE,IAAA,CAACX,GAAG,EAACmB,SAAS,CAAC,wBAAwB,CAAA+D,QAAA,cACrCvE,IAAA,CAACV,GAAG,EAACkF,EAAE,CAAC,MAAM,CAAAD,QAAA,cACZrE,KAAA,QAAKM,SAAS,CAAC,2BAA2B,CAAA+D,QAAA,eACxCvE,IAAA,CAACR,OAAO,EAACiF,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAClE,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDR,IAAA,SAAAuE,QAAA,CAAM,iBAAe,CAAM,CAAC,EACzB,CAAC,CACH,CAAC,CACH,CAAC,CACG,CAAC,CAEhB,CAEA,GAAIpD,KAAK,CAAE,CACT,mBACEnB,IAAA,CAACZ,SAAS,EAACoB,SAAS,CAAC,MAAM,CAAA+D,QAAA,cACzBvE,IAAA,CAACX,GAAG,EAACmB,SAAS,CAAC,wBAAwB,CAAA+D,QAAA,cACrCvE,IAAA,CAACV,GAAG,EAACqF,EAAE,CAAE,CAAE,CAAAJ,QAAA,cACTrE,KAAA,CAACX,KAAK,EAACqF,OAAO,CAAC,QAAQ,CAAAL,QAAA,eACrBvE,IAAA,CAACT,KAAK,CAACsF,OAAO,EAAAN,QAAA,CACXpD,KAAK,GAAK,gBAAgB,CAAG,gBAAgB,CAAG,oBAAoB,CACxD,CAAC,cAChBnB,IAAA,MAAAuE,QAAA,CAAIpD,KAAK,CAAI,CAAC,CACbA,KAAK,GAAK,gBAAgB,eACzBnB,IAAA,MAAGQ,SAAS,CAAC,MAAM,CAAA+D,QAAA,CAAC,gEAEpB,CAAG,CACJ,EACI,CAAC,CACL,CAAC,CACH,CAAC,CACG,CAAC,CAEhB,CAEA,GAAI,CAAC5D,IAAI,CAAE,CACT,mBACEX,IAAA,CAACZ,SAAS,EAACoB,SAAS,CAAC,MAAM,CAAA+D,QAAA,cACzBvE,IAAA,CAACX,GAAG,EAACmB,SAAS,CAAC,wBAAwB,CAAA+D,QAAA,cACrCvE,IAAA,CAACV,GAAG,EAACqF,EAAE,CAAE,CAAE,CAAAJ,QAAA,cACTrE,KAAA,CAACX,KAAK,EAACqF,OAAO,CAAC,SAAS,CAAAL,QAAA,eACtBvE,IAAA,CAACT,KAAK,CAACsF,OAAO,EAAAN,QAAA,CAAC,sBAAoB,CAAe,CAAC,cACnDvE,IAAA,MAAAuE,QAAA,CAAG,iDAA+C,CAAG,CAAC,EACjD,CAAC,CACL,CAAC,CACH,CAAC,CACG,CAAC,CAEhB,CAEA,mBACErE,KAAA,CAAAE,SAAA,EAAAmE,QAAA,EACGrC,aAAa,CAAC,CAAC,cAChBlC,IAAA,QAAKQ,SAAS,CAAE,6BAA6BG,IAAI,CAAC+B,QAAQ,IAAIlC,SAAS,EAAI,EAAE,EAAG,CAAA+D,QAAA,CAC7EJ,cAAc,CAAC,CAAC,CACd,CAAC,EACN,CAAC,CAEP,CAAC,CAED,cAAe,CAAA9D,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}