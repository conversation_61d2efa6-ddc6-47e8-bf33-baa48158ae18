{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\pages\\\\StaticPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Contain<PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';\nimport { Helmet } from 'react-helmet-async';\nimport { Render } from '@measured/puck';\nimport { useSiteName } from '../../contexts/SettingsContext';\nimport { puckConfig } from '../puck/puckConfig';\nimport '../puck/PuckRenderer.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StaticPage = ({\n  slug: propSlug\n}) => {\n  _s();\n  const {\n    slug: paramSlug\n  } = useParams();\n  const slug = propSlug || paramSlug;\n  const siteName = useSiteName();\n  const [pageData, setPageData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    if (!slug) {\n      setError('Page slug is required');\n      setLoading(false);\n      return;\n    }\n    const fetchPageData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(`${process.env.REACT_APP_API_URL}/pages/${slug}`);\n        if (!response.ok) {\n          if (response.status === 404) {\n            setError('Page not found');\n          } else {\n            setError('Failed to load page');\n          }\n          return;\n        }\n        const result = await response.json();\n        if (result.success) {\n          setPageData(result.data);\n        } else {\n          setError(result.message || 'Failed to load page');\n        }\n      } catch (err) {\n        console.error('Error fetching page:', err);\n        setError('Failed to load page');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPageData();\n  }, [slug]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-3\",\n        children: \"Loading page...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !pageData) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error || 'Page not found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: [pageData.meta_title || pageData.title, \" - \", siteName]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), pageData.meta_description && /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: pageData.meta_description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"puck-renderer\",\n      children: pageData.content && pageData.content.content ? /*#__PURE__*/_jsxDEV(Render, {\n        config: puckConfig,\n        data: pageData.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Container, {\n        className: \"py-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: pageData.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"This page is currently being set up. Please check back later.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(StaticPage, \"GkN8D7HUSdwfGX5n/QggloW41oQ=\", false, function () {\n  return [useParams, useSiteName];\n});\n_c = StaticPage;\nexport default StaticPage;\nvar _c;\n$RefreshReg$(_c, \"StaticPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Container", "<PERSON><PERSON>", "Spinner", "<PERSON><PERSON><PERSON>", "Render", "useSiteName", "puckConfig", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StaticPage", "slug", "propSlug", "_s", "paramSlug", "siteName", "pageData", "setPageData", "loading", "setLoading", "error", "setError", "fetchPageData", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "status", "result", "json", "success", "data", "message", "err", "console", "className", "children", "animation", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "Heading", "meta_title", "title", "meta_description", "name", "content", "config", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/pages/StaticPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';\nimport { Helmet } from 'react-helmet-async';\nimport { Render } from '@measured/puck';\nimport { useSiteName } from '../../contexts/SettingsContext';\nimport { puckConfig } from '../puck/puckConfig';\nimport '../puck/PuckRenderer.css';\n\ninterface PageData {\n  id: number;\n  title: string;\n  slug: string;\n  content: any;\n  meta_title?: string;\n  meta_description?: string;\n  status: string;\n  published_at: string;\n}\n\ninterface StaticPageProps {\n  slug?: string;\n}\n\nconst StaticPage: React.FC<StaticPageProps> = ({ slug: propSlug }) => {\n  const { slug: paramSlug } = useParams<{ slug: string }>();\n  const slug = propSlug || paramSlug;\n  const siteName = useSiteName();\n  \n  const [pageData, setPageData] = useState<PageData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    if (!slug) {\n      setError('Page slug is required');\n      setLoading(false);\n      return;\n    }\n\n    const fetchPageData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(`${process.env.REACT_APP_API_URL}/pages/${slug}`);\n        \n        if (!response.ok) {\n          if (response.status === 404) {\n            setError('Page not found');\n          } else {\n            setError('Failed to load page');\n          }\n          return;\n        }\n\n        const result = await response.json();\n        if (result.success) {\n          setPageData(result.data);\n        } else {\n          setError(result.message || 'Failed to load page');\n        }\n      } catch (err) {\n        console.error('Error fetching page:', err);\n        setError('Failed to load page');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchPageData();\n  }, [slug]);\n\n  if (loading) {\n    return (\n      <Container className=\"py-5 text-center\">\n        <Spinner animation=\"border\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </Spinner>\n        <p className=\"mt-3\">Loading page...</p>\n      </Container>\n    );\n  }\n\n  if (error || !pageData) {\n    return (\n      <Container className=\"py-5\">\n        <Alert variant=\"danger\">\n          <Alert.Heading>Error</Alert.Heading>\n          <p>{error || 'Page not found'}</p>\n        </Alert>\n      </Container>\n    );\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>{pageData.meta_title || pageData.title} - {siteName}</title>\n        {pageData.meta_description && (\n          <meta name=\"description\" content={pageData.meta_description} />\n        )}\n      </Helmet>\n\n      <div className=\"puck-renderer\">\n        {pageData.content && pageData.content.content ? (\n          <Render config={puckConfig} data={pageData.content} />\n        ) : (\n          <Container className=\"py-5\">\n            <h1>{pageData.title}</h1>\n            <p>This page is currently being set up. Please check back later.</p>\n          </Container>\n        )}\n      </div>\n    </>\n  );\n};\n\nexport default StaticPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AAC3D,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAiBlC,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,IAAI,EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM;IAAEF,IAAI,EAAEG;EAAU,CAAC,GAAGhB,SAAS,CAAmB,CAAC;EACzD,MAAMa,IAAI,GAAGC,QAAQ,IAAIE,SAAS;EAClC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAkB,IAAI,CAAC;EAC/D,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAS,EAAE,CAAC;EAE9CC,SAAS,CAAC,MAAM;IACd,IAAI,CAACc,IAAI,EAAE;MACTU,QAAQ,CAAC,uBAAuB,CAAC;MACjCF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,UAAUhB,IAAI,EAAE,CAAC;QAE9E,IAAI,CAACY,QAAQ,CAACK,EAAE,EAAE;UAChB,IAAIL,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;YAC3BR,QAAQ,CAAC,gBAAgB,CAAC;UAC5B,CAAC,MAAM;YACLA,QAAQ,CAAC,qBAAqB,CAAC;UACjC;UACA;QACF;QAEA,MAAMS,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACpC,IAAID,MAAM,CAACE,OAAO,EAAE;UAClBf,WAAW,CAACa,MAAM,CAACG,IAAI,CAAC;QAC1B,CAAC,MAAM;UACLZ,QAAQ,CAACS,MAAM,CAACI,OAAO,IAAI,qBAAqB,CAAC;QACnD;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAAChB,KAAK,CAAC,sBAAsB,EAAEe,GAAG,CAAC;QAC1Cd,QAAQ,CAAC,qBAAqB,CAAC;MACjC,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACX,IAAI,CAAC,CAAC;EAEV,IAAIO,OAAO,EAAE;IACX,oBACEX,OAAA,CAACR,SAAS;MAACsC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBACrC/B,OAAA,CAACN,OAAO;QAACsC,SAAS,EAAC,QAAQ;QAACC,IAAI,EAAC,QAAQ;QAAAF,QAAA,eACvC/B,OAAA;UAAM8B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACVrC,OAAA;QAAG8B,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEhB;EAEA,IAAIxB,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACET,OAAA,CAACR,SAAS;MAACsC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACzB/B,OAAA,CAACP,KAAK;QAAC6C,OAAO,EAAC,QAAQ;QAAAP,QAAA,gBACrB/B,OAAA,CAACP,KAAK,CAAC8C,OAAO;UAAAR,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eACpCrC,OAAA;UAAA+B,QAAA,EAAIlB,KAAK,IAAI;QAAgB;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACErC,OAAA,CAAAE,SAAA;IAAA6B,QAAA,gBACE/B,OAAA,CAACL,MAAM;MAAAoC,QAAA,gBACL/B,OAAA;QAAA+B,QAAA,GAAQtB,QAAQ,CAAC+B,UAAU,IAAI/B,QAAQ,CAACgC,KAAK,EAAC,KAAG,EAACjC,QAAQ;MAAA;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAClE5B,QAAQ,CAACiC,gBAAgB,iBACxB1C,OAAA;QAAM2C,IAAI,EAAC,aAAa;QAACC,OAAO,EAAEnC,QAAQ,CAACiC;MAAiB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC/D;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAETrC,OAAA;MAAK8B,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BtB,QAAQ,CAACmC,OAAO,IAAInC,QAAQ,CAACmC,OAAO,CAACA,OAAO,gBAC3C5C,OAAA,CAACJ,MAAM;QAACiD,MAAM,EAAE/C,UAAW;QAAC4B,IAAI,EAAEjB,QAAQ,CAACmC;MAAQ;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEtDrC,OAAA,CAACR,SAAS;QAACsC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACzB/B,OAAA;UAAA+B,QAAA,EAAKtB,QAAQ,CAACgC;QAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzBrC,OAAA;UAAA+B,QAAA,EAAG;QAA6D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IACZ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC/B,EAAA,CA1FIH,UAAqC;EAAA,QACbZ,SAAS,EAEpBM,WAAW;AAAA;AAAAiD,EAAA,GAHxB3C,UAAqC;AA4F3C,eAAeA,UAAU;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}