{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    bg,\n    text,\n    border,\n    body = false,\n    children,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CardImg", "CardImgOverlay", "CardLink", "CardSubtitle", "CardText", "CardTitle", "jsx", "_jsx", "Card", "forwardRef", "_ref", "ref", "bsPrefix", "className", "bg", "text", "border", "body", "children", "as", "Component", "props", "prefix", "displayName", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,IAAI,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,CAAAC,IAAA,EAWxCC,GAAG,KAAK;EAAA,IAXiC;IAC1CC,QAAQ;IACRC,SAAS;IACTC,EAAE;IACFC,IAAI;IACJC,MAAM;IACNC,IAAI,GAAG,KAAK;IACZC,QAAQ;IACR;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrB,GAAGC;EACL,CAAC,GAAAX,IAAA;EACC,MAAMY,MAAM,GAAG1B,kBAAkB,CAACgB,QAAQ,EAAE,MAAM,CAAC;EACnD,OAAO,aAAaL,IAAI,CAACa,SAAS,EAAE;IAClCT,GAAG,EAAEA,GAAG;IACR,GAAGU,KAAK;IACRR,SAAS,EAAEnB,UAAU,CAACmB,SAAS,EAAES,MAAM,EAAER,EAAE,IAAI,MAAMA,EAAE,EAAE,EAAEC,IAAI,IAAI,QAAQA,IAAI,EAAE,EAAEC,MAAM,IAAI,UAAUA,MAAM,EAAE,CAAC;IAChHE,QAAQ,EAAED,IAAI,GAAG,aAAaV,IAAI,CAACV,QAAQ,EAAE;MAC3CqB,QAAQ,EAAEA;IACZ,CAAC,CAAC,GAAGA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC;AACFV,IAAI,CAACe,WAAW,GAAG,MAAM;AACzB,eAAeC,MAAM,CAACC,MAAM,CAACjB,IAAI,EAAE;EACjCkB,GAAG,EAAE1B,OAAO;EACZ2B,KAAK,EAAEtB,SAAS;EAChBuB,QAAQ,EAAEzB,YAAY;EACtB0B,IAAI,EAAEhC,QAAQ;EACdiC,IAAI,EAAE5B,QAAQ;EACd6B,IAAI,EAAE3B,QAAQ;EACd4B,MAAM,EAAEjC,UAAU;EAClBkC,MAAM,EAAEnC,UAAU;EAClBoC,UAAU,EAAEjC;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}