{"ast": null, "code": "export { default } from \"./AccordionSummary.js\";\nexport { default as accordionSummaryClasses } from \"./accordionSummaryClasses.js\";\nexport * from \"./accordionSummaryClasses.js\";", "map": {"version": 3, "names": ["default", "accordionSummaryClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/AccordionSummary/index.js"], "sourcesContent": ["export { default } from \"./AccordionSummary.js\";\nexport { default as accordionSummaryClasses } from \"./accordionSummaryClasses.js\";\nexport * from \"./accordionSummaryClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASA,OAAO,IAAIC,uBAAuB,QAAQ,8BAA8B;AACjF,cAAc,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}