{"ast": null, "code": "export const reflow = node => node.scrollTop;\nexport function getTransitionProps(props, options) {\n  const {\n    timeout,\n    easing,\n    style = {}\n  } = props;\n  return {\n    duration: style.transitionDuration ?? (typeof timeout === 'number' ? timeout : timeout[options.mode] || 0),\n    easing: style.transitionTimingFunction ?? (typeof easing === 'object' ? easing[options.mode] : easing),\n    delay: style.transitionDelay\n  };\n}", "map": {"version": 3, "names": ["reflow", "node", "scrollTop", "getTransitionProps", "props", "options", "timeout", "easing", "style", "duration", "transitionDuration", "mode", "transitionTimingFunction", "delay", "transitionDelay"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/transitions/utils.js"], "sourcesContent": ["export const reflow = node => node.scrollTop;\nexport function getTransitionProps(props, options) {\n  const {\n    timeout,\n    easing,\n    style = {}\n  } = props;\n  return {\n    duration: style.transitionDuration ?? (typeof timeout === 'number' ? timeout : timeout[options.mode] || 0),\n    easing: style.transitionTimingFunction ?? (typeof easing === 'object' ? easing[options.mode] : easing),\n    delay: style.transitionDelay\n  };\n}"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAGC,IAAI,IAAIA,IAAI,CAACC,SAAS;AAC5C,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACjD,MAAM;IACJC,OAAO;IACPC,MAAM;IACNC,KAAK,GAAG,CAAC;EACX,CAAC,GAAGJ,KAAK;EACT,OAAO;IACLK,QAAQ,EAAED,KAAK,CAACE,kBAAkB,KAAK,OAAOJ,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGA,OAAO,CAACD,OAAO,CAACM,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1GJ,MAAM,EAAEC,KAAK,CAACI,wBAAwB,KAAK,OAAOL,MAAM,KAAK,QAAQ,GAAGA,MAAM,CAACF,OAAO,CAACM,IAAI,CAAC,GAAGJ,MAAM,CAAC;IACtGM,KAAK,EAAEL,KAAK,CAACM;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}