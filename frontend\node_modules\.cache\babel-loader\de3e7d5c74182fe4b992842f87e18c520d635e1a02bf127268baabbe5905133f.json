{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport Feedback from './Feedback';\nimport FormCheckInput from './FormCheckInput';\nimport Form<PERSON>heckLabel from './FormCheckLabel';\nimport Form<PERSON>ontext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { hasChildOfType } from './ElementChildren';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst FormCheck = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    id,\n    bsPrefix,\n    bsSwitchPrefix,\n    inline = false,\n    reverse = false,\n    disabled = false,\n    isValid = false,\n    isInvalid = false,\n    feedbackTooltip = false,\n    feedback,\n    feedbackType,\n    className,\n    style,\n    title = '',\n    type = 'checkbox',\n    label,\n    children,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as = 'input',\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check');\n  bsSwitchPrefix = useBootstrapPrefix(bsSwitchPrefix, 'form-switch');\n  const {\n    controlId\n  } = useContext(FormContext);\n  const innerFormContext = useMemo(() => ({\n    controlId: id || controlId\n  }), [controlId, id]);\n  const hasLabel = !children && label != null && label !== false || hasChildOfType(children, FormCheckLabel);\n  const input = /*#__PURE__*/_jsx(FormCheckInput, {\n    ...props,\n    type: type === 'switch' ? 'checkbox' : type,\n    ref: ref,\n    isValid: isValid,\n    isInvalid: isInvalid,\n    disabled: disabled,\n    as: as\n  });\n  return /*#__PURE__*/_jsx(FormContext.Provider, {\n    value: innerFormContext,\n    children: /*#__PURE__*/_jsx(\"div\", {\n      style: style,\n      className: classNames(className, hasLabel && bsPrefix, inline && `${bsPrefix}-inline`, reverse && `${bsPrefix}-reverse`, type === 'switch' && bsSwitchPrefix),\n      children: children || /*#__PURE__*/_jsxs(_Fragment, {\n        children: [input, hasLabel && /*#__PURE__*/_jsx(FormCheckLabel, {\n          title: title,\n          children: label\n        }), feedback && /*#__PURE__*/_jsx(Feedback, {\n          type: feedbackType,\n          tooltip: feedbackTooltip,\n          children: feedback\n        })]\n      })\n    })\n  });\n});\nFormCheck.displayName = 'FormCheck';\nexport default Object.assign(FormCheck, {\n  Input: FormCheckInput,\n  Label: FormCheckLabel\n});", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useMemo", "<PERSON><PERSON><PERSON>", "FormCheckInput", "FormCheckLabel", "FormContext", "useBootstrapPrefix", "hasChildOfType", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "FormCheck", "forwardRef", "_ref", "ref", "id", "bsPrefix", "bsSwitchPrefix", "inline", "reverse", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "isInvalid", "feedbackTooltip", "feedback", "feedbackType", "className", "style", "title", "type", "label", "children", "as", "props", "controlId", "innerFormContext", "<PERSON><PERSON><PERSON><PERSON>", "input", "Provider", "value", "tooltip", "displayName", "Object", "assign", "Input", "Label"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FormCheck.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport Feedback from './Feedback';\nimport FormCheckInput from './FormCheckInput';\nimport Form<PERSON>heckLabel from './FormCheckLabel';\nimport Form<PERSON>ontext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { hasChildOfType } from './ElementChildren';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst FormCheck = /*#__PURE__*/React.forwardRef(({\n  id,\n  bsPrefix,\n  bsSwitchPrefix,\n  inline = false,\n  reverse = false,\n  disabled = false,\n  isValid = false,\n  isInvalid = false,\n  feedbackTooltip = false,\n  feedback,\n  feedbackType,\n  className,\n  style,\n  title = '',\n  type = 'checkbox',\n  label,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as = 'input',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check');\n  bsSwitchPrefix = useBootstrapPrefix(bsSwitchPrefix, 'form-switch');\n  const {\n    controlId\n  } = useContext(FormContext);\n  const innerFormContext = useMemo(() => ({\n    controlId: id || controlId\n  }), [controlId, id]);\n  const hasLabel = !children && label != null && label !== false || hasChildOfType(children, FormCheckLabel);\n  const input = /*#__PURE__*/_jsx(FormCheckInput, {\n    ...props,\n    type: type === 'switch' ? 'checkbox' : type,\n    ref: ref,\n    isValid: isValid,\n    isInvalid: isInvalid,\n    disabled: disabled,\n    as: as\n  });\n  return /*#__PURE__*/_jsx(FormContext.Provider, {\n    value: innerFormContext,\n    children: /*#__PURE__*/_jsx(\"div\", {\n      style: style,\n      className: classNames(className, hasLabel && bsPrefix, inline && `${bsPrefix}-inline`, reverse && `${bsPrefix}-reverse`, type === 'switch' && bsSwitchPrefix),\n      children: children || /*#__PURE__*/_jsxs(_Fragment, {\n        children: [input, hasLabel && /*#__PURE__*/_jsx(FormCheckLabel, {\n          title: title,\n          children: label\n        }), feedback && /*#__PURE__*/_jsx(Feedback, {\n          type: feedbackType,\n          tooltip: feedbackTooltip,\n          children: feedback\n        })]\n      })\n    })\n  });\n});\nFormCheck.displayName = 'FormCheck';\nexport default Object.assign(FormCheck, {\n  Input: FormCheckInput,\n  Label: FormCheckLabel\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,SAAS,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,CAAAC,IAAA,EAqB7CC,GAAG,KAAK;EAAA,IArBsC;IAC/CC,EAAE;IACFC,QAAQ;IACRC,cAAc;IACdC,MAAM,GAAG,KAAK;IACdC,OAAO,GAAG,KAAK;IACfC,QAAQ,GAAG,KAAK;IAChBC,OAAO,GAAG,KAAK;IACfC,SAAS,GAAG,KAAK;IACjBC,eAAe,GAAG,KAAK;IACvBC,QAAQ;IACRC,YAAY;IACZC,SAAS;IACTC,KAAK;IACLC,KAAK,GAAG,EAAE;IACVC,IAAI,GAAG,UAAU;IACjBC,KAAK;IACLC,QAAQ;IACR;IACAC,EAAE,GAAG,OAAO;IACZ,GAAGC;EACL,CAAC,GAAApB,IAAA;EACCG,QAAQ,GAAGb,kBAAkB,CAACa,QAAQ,EAAE,YAAY,CAAC;EACrDC,cAAc,GAAGd,kBAAkB,CAACc,cAAc,EAAE,aAAa,CAAC;EAClE,MAAM;IACJiB;EACF,CAAC,GAAGrC,UAAU,CAACK,WAAW,CAAC;EAC3B,MAAMiC,gBAAgB,GAAGrC,OAAO,CAAC,OAAO;IACtCoC,SAAS,EAAEnB,EAAE,IAAImB;EACnB,CAAC,CAAC,EAAE,CAACA,SAAS,EAAEnB,EAAE,CAAC,CAAC;EACpB,MAAMqB,QAAQ,GAAG,CAACL,QAAQ,IAAID,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,KAAK,IAAI1B,cAAc,CAAC2B,QAAQ,EAAE9B,cAAc,CAAC;EAC1G,MAAMoC,KAAK,GAAG,aAAa/B,IAAI,CAACN,cAAc,EAAE;IAC9C,GAAGiC,KAAK;IACRJ,IAAI,EAAEA,IAAI,KAAK,QAAQ,GAAG,UAAU,GAAGA,IAAI;IAC3Cf,GAAG,EAAEA,GAAG;IACRO,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBF,QAAQ,EAAEA,QAAQ;IAClBY,EAAE,EAAEA;EACN,CAAC,CAAC;EACF,OAAO,aAAa1B,IAAI,CAACJ,WAAW,CAACoC,QAAQ,EAAE;IAC7CC,KAAK,EAAEJ,gBAAgB;IACvBJ,QAAQ,EAAE,aAAazB,IAAI,CAAC,KAAK,EAAE;MACjCqB,KAAK,EAAEA,KAAK;MACZD,SAAS,EAAE/B,UAAU,CAAC+B,SAAS,EAAEU,QAAQ,IAAIpB,QAAQ,EAAEE,MAAM,IAAI,GAAGF,QAAQ,SAAS,EAAEG,OAAO,IAAI,GAAGH,QAAQ,UAAU,EAAEa,IAAI,KAAK,QAAQ,IAAIZ,cAAc,CAAC;MAC7Jc,QAAQ,EAAEA,QAAQ,IAAI,aAAarB,KAAK,CAACF,SAAS,EAAE;QAClDuB,QAAQ,EAAE,CAACM,KAAK,EAAED,QAAQ,IAAI,aAAa9B,IAAI,CAACL,cAAc,EAAE;UAC9D2B,KAAK,EAAEA,KAAK;UACZG,QAAQ,EAAED;QACZ,CAAC,CAAC,EAAEN,QAAQ,IAAI,aAAalB,IAAI,CAACP,QAAQ,EAAE;UAC1C8B,IAAI,EAAEJ,YAAY;UAClBe,OAAO,EAAEjB,eAAe;UACxBQ,QAAQ,EAAEP;QACZ,CAAC,CAAC;MACJ,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFb,SAAS,CAAC8B,WAAW,GAAG,WAAW;AACnC,eAAeC,MAAM,CAACC,MAAM,CAAChC,SAAS,EAAE;EACtCiC,KAAK,EAAE5C,cAAc;EACrB6C,KAAK,EAAE5C;AACT,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}