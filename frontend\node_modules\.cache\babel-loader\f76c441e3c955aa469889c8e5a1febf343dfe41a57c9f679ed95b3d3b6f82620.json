{"ast": null, "code": "import api from './api';\nclass CreditService {\n  // Get user's credit balance\n  async getBalance() {\n    const response = await api.get('/credit/balance');\n    return response.data;\n  }\n\n  // Get available credit packages\n  async getPackages() {\n    const response = await api.get('/credit/packages');\n    return response.data.packages;\n  }\n\n  // Get user's credit transaction history\n  async getTransactions(page = 1) {\n    const response = await api.get(`/credit/transactions?page=${page}`);\n    return response.data;\n  }\n\n  // Get credit statistics\n  async getStatistics() {\n    const response = await api.get('/credit/statistics');\n    return response.data;\n  }\n\n  // Create payment for credit package\n  async createPayment(packageId, redirectUrl) {\n    const response = await api.post('/payment/create', {\n      package_id: packageId,\n      redirect_url: redirectUrl\n    });\n    return response.data;\n  }\n\n  // Check payment status\n  async checkPaymentStatus(transactionId) {\n    const response = await api.get('/payment/status', {\n      params: {\n        transaction_id: transactionId\n      }\n    });\n    return response.data;\n  }\n\n  // Get payment configuration\n  async getPaymentConfig() {\n    const response = await api.get('/payment/config');\n    return response.data;\n  }\n\n  // Format wallet balance for display (Malaysian Ringgit)\n  formatWalletBalance(amount) {\n    try {\n      // Handle falsy values\n      if (!amount && amount !== 0) {\n        return 'RM 0.00';\n      }\n\n      // Ensure we have a valid number\n      let numericAmount;\n      if (typeof amount === 'number') {\n        numericAmount = amount;\n      } else {\n        numericAmount = parseFloat(String(amount));\n      }\n\n      // Validate the number\n      if (isNaN(numericAmount) || !isFinite(numericAmount)) {\n        return 'RM 0.00';\n      }\n\n      // Ensure non-negative for wallet balance\n      const safeAmount = Math.max(0, numericAmount);\n      return `RM ${safeAmount.toFixed(2)}`;\n    } catch (error) {\n      console.error('formatWalletBalance error:', error, 'amount:', amount);\n      return 'RM 0.00';\n    }\n  }\n\n  // Legacy method for backward compatibility\n  // @deprecated Use formatWalletBalance() instead\n  formatCredits(amount) {\n    return this.formatWalletBalance(amount);\n  }\n\n  // Format currency for display\n  formatCurrency(amount) {\n    try {\n      // Handle falsy values\n      if (!amount && amount !== 0) {\n        return 'RM 0.00';\n      }\n\n      // Ensure we have a valid number\n      let numericAmount;\n      if (typeof amount === 'number') {\n        numericAmount = amount;\n      } else {\n        numericAmount = parseFloat(String(amount));\n      }\n\n      // Validate the number\n      if (isNaN(numericAmount) || !isFinite(numericAmount)) {\n        return 'RM 0.00';\n      }\n\n      // Ensure non-negative for currency\n      const safeAmount = Math.max(0, numericAmount);\n      return `RM ${safeAmount.toFixed(2)}`;\n    } catch (error) {\n      console.error('formatCurrency error:', error, 'amount:', amount);\n      return 'RM 0.00';\n    }\n  }\n\n  // Get transaction type color\n  getTransactionTypeColor(type) {\n    switch (type) {\n      case 'purchase':\n        return 'success';\n      case 'usage':\n        return 'warning';\n      case 'refund':\n        return 'error';\n      case 'bonus':\n        return 'info';\n      default:\n        return 'default';\n    }\n  }\n\n  // Get payment status color\n  getPaymentStatusColor(status) {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'failed':\n        return 'error';\n      default:\n        return 'default';\n    }\n  }\n}\nexport default new CreditService();", "map": {"version": 3, "names": ["api", "CreditService", "getBalance", "response", "get", "data", "getPackages", "packages", "getTransactions", "page", "getStatistics", "createPayment", "packageId", "redirectUrl", "post", "package_id", "redirect_url", "checkPaymentStatus", "transactionId", "params", "transaction_id", "getPaymentConfig", "formatWalletBalance", "amount", "numericAmount", "parseFloat", "String", "isNaN", "isFinite", "safeAmount", "Math", "max", "toFixed", "error", "console", "formatCredits", "formatCurrency", "getTransactionTypeColor", "type", "getPaymentStatusColor", "status"], "sources": ["C:/laragon/www/frontend/src/services/creditService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface CreditPackage {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  formatted_price: string;\n  credit_amount: number;\n  price_per_credit: number;\n  features: string[];\n}\n\nexport interface CreditTransaction {\n  id: number;\n  type: string;\n  credit_amount: number;\n  amount_paid: number | null;\n  formatted_amount_paid: string | null;\n  payment_method: string | null;\n  payment_status: string;\n  description: string;\n  package_name: string | null;\n  is_credit: boolean;\n  is_debit: boolean;\n  processed_at: string | null;\n  created_at: string;\n}\n\nexport interface CreditBalance {\n  credit_balance: number;\n  user_id: number;\n}\n\nexport interface CreditStatistics {\n  current_balance: number;\n  total_purchased: number;\n  total_used: number;\n  total_spent: number;\n  recent_transactions: Array<{\n    id: number;\n    type: string;\n    credit_amount: number;\n    description: string;\n    created_at: string;\n  }>;\n}\n\nexport interface PaymentResponse {\n  success: boolean;\n  payment_url?: string;\n  bill_id?: string;\n  transaction_id?: number;\n  error?: string;\n}\n\nexport interface PaymentStatus {\n  transaction_id: number;\n  payment_status: string;\n  credit_amount: number;\n  amount_paid: number;\n  processed_at: string | null;\n}\n\nexport interface PaymentConfig {\n  billplz_enabled: boolean;\n  billplz_configured: boolean;\n}\n\nclass CreditService {\n  // Get user's credit balance\n  async getBalance(): Promise<CreditBalance> {\n    const response = await api.get('/credit/balance');\n    return response.data;\n  }\n\n  // Get available credit packages\n  async getPackages(): Promise<CreditPackage[]> {\n    const response = await api.get('/credit/packages');\n    return response.data.packages;\n  }\n\n  // Get user's credit transaction history\n  async getTransactions(page: number = 1): Promise<{\n    transactions: {\n      data: CreditTransaction[];\n      current_page: number;\n      last_page: number;\n      per_page: number;\n      total: number;\n    };\n  }> {\n    const response = await api.get(`/credit/transactions?page=${page}`);\n    return response.data;\n  }\n\n  // Get credit statistics\n  async getStatistics(): Promise<CreditStatistics> {\n    const response = await api.get('/credit/statistics');\n    return response.data;\n  }\n\n  // Create payment for credit package\n  async createPayment(packageId: number, redirectUrl?: string): Promise<PaymentResponse> {\n    const response = await api.post('/payment/create', {\n      package_id: packageId,\n      redirect_url: redirectUrl,\n    });\n    return response.data;\n  }\n\n  // Check payment status\n  async checkPaymentStatus(transactionId: number): Promise<PaymentStatus> {\n    const response = await api.get('/payment/status', {\n      params: { transaction_id: transactionId },\n    });\n    return response.data;\n  }\n\n  // Get payment configuration\n  async getPaymentConfig(): Promise<PaymentConfig> {\n    const response = await api.get('/payment/config');\n    return response.data;\n  }\n\n  // Format wallet balance for display (Malaysian Ringgit)\n  formatWalletBalance(amount: any): string {\n    try {\n      // Handle falsy values\n      if (!amount && amount !== 0) {\n        return 'RM 0.00';\n      }\n\n      // Ensure we have a valid number\n      let numericAmount: number;\n      if (typeof amount === 'number') {\n        numericAmount = amount;\n      } else {\n        numericAmount = parseFloat(String(amount));\n      }\n\n      // Validate the number\n      if (isNaN(numericAmount) || !isFinite(numericAmount)) {\n        return 'RM 0.00';\n      }\n\n      // Ensure non-negative for wallet balance\n      const safeAmount = Math.max(0, numericAmount);\n      return `RM ${safeAmount.toFixed(2)}`;\n    } catch (error) {\n      console.error('formatWalletBalance error:', error, 'amount:', amount);\n      return 'RM 0.00';\n    }\n  }\n\n  // Legacy method for backward compatibility\n  // @deprecated Use formatWalletBalance() instead\n  formatCredits(amount: any): string {\n    return this.formatWalletBalance(amount);\n  }\n\n  // Format currency for display\n  formatCurrency(amount: any): string {\n    try {\n      // Handle falsy values\n      if (!amount && amount !== 0) {\n        return 'RM 0.00';\n      }\n\n      // Ensure we have a valid number\n      let numericAmount: number;\n      if (typeof amount === 'number') {\n        numericAmount = amount;\n      } else {\n        numericAmount = parseFloat(String(amount));\n      }\n\n      // Validate the number\n      if (isNaN(numericAmount) || !isFinite(numericAmount)) {\n        return 'RM 0.00';\n      }\n\n      // Ensure non-negative for currency\n      const safeAmount = Math.max(0, numericAmount);\n      return `RM ${safeAmount.toFixed(2)}`;\n    } catch (error) {\n      console.error('formatCurrency error:', error, 'amount:', amount);\n      return 'RM 0.00';\n    }\n  }\n\n  // Get transaction type color\n  getTransactionTypeColor(type: string): 'success' | 'warning' | 'error' | 'info' | 'default' {\n    switch (type) {\n      case 'purchase':\n        return 'success';\n      case 'usage':\n        return 'warning';\n      case 'refund':\n        return 'error';\n      case 'bonus':\n        return 'info';\n      default:\n        return 'default';\n    }\n  }\n\n  // Get payment status color\n  getPaymentStatusColor(status: string): 'success' | 'warning' | 'error' | 'default' {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'failed':\n        return 'error';\n      default:\n        return 'default';\n    }\n  }\n}\n\nexport default new CreditService();\n"], "mappings": "AAAA,OAAOA,GAAG,MAAqB,OAAO;AAqEtC,MAAMC,aAAa,CAAC;EAClB;EACA,MAAMC,UAAUA,CAAA,EAA2B;IACzC,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,iBAAiB,CAAC;IACjD,OAAOD,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMC,WAAWA,CAAA,EAA6B;IAC5C,MAAMH,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,kBAAkB,CAAC;IAClD,OAAOD,QAAQ,CAACE,IAAI,CAACE,QAAQ;EAC/B;;EAEA;EACA,MAAMC,eAAeA,CAACC,IAAY,GAAG,CAAC,EAQnC;IACD,MAAMN,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,6BAA6BK,IAAI,EAAE,CAAC;IACnE,OAAON,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMK,aAAaA,CAAA,EAA8B;IAC/C,MAAMP,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,oBAAoB,CAAC;IACpD,OAAOD,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMM,aAAaA,CAACC,SAAiB,EAAEC,WAAoB,EAA4B;IACrF,MAAMV,QAAQ,GAAG,MAAMH,GAAG,CAACc,IAAI,CAAC,iBAAiB,EAAE;MACjDC,UAAU,EAAEH,SAAS;MACrBI,YAAY,EAAEH;IAChB,CAAC,CAAC;IACF,OAAOV,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMY,kBAAkBA,CAACC,aAAqB,EAA0B;IACtE,MAAMf,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,iBAAiB,EAAE;MAChDe,MAAM,EAAE;QAAEC,cAAc,EAAEF;MAAc;IAC1C,CAAC,CAAC;IACF,OAAOf,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMgB,gBAAgBA,CAAA,EAA2B;IAC/C,MAAMlB,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,iBAAiB,CAAC;IACjD,OAAOD,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACAiB,mBAAmBA,CAACC,MAAW,EAAU;IACvC,IAAI;MACF;MACA,IAAI,CAACA,MAAM,IAAIA,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO,SAAS;MAClB;;MAEA;MACA,IAAIC,aAAqB;MACzB,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BC,aAAa,GAAGD,MAAM;MACxB,CAAC,MAAM;QACLC,aAAa,GAAGC,UAAU,CAACC,MAAM,CAACH,MAAM,CAAC,CAAC;MAC5C;;MAEA;MACA,IAAII,KAAK,CAACH,aAAa,CAAC,IAAI,CAACI,QAAQ,CAACJ,aAAa,CAAC,EAAE;QACpD,OAAO,SAAS;MAClB;;MAEA;MACA,MAAMK,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,aAAa,CAAC;MAC7C,OAAO,MAAMK,UAAU,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;IACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,EAAE,SAAS,EAAEV,MAAM,CAAC;MACrE,OAAO,SAAS;IAClB;EACF;;EAEA;EACA;EACAY,aAAaA,CAACZ,MAAW,EAAU;IACjC,OAAO,IAAI,CAACD,mBAAmB,CAACC,MAAM,CAAC;EACzC;;EAEA;EACAa,cAAcA,CAACb,MAAW,EAAU;IAClC,IAAI;MACF;MACA,IAAI,CAACA,MAAM,IAAIA,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO,SAAS;MAClB;;MAEA;MACA,IAAIC,aAAqB;MACzB,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BC,aAAa,GAAGD,MAAM;MACxB,CAAC,MAAM;QACLC,aAAa,GAAGC,UAAU,CAACC,MAAM,CAACH,MAAM,CAAC,CAAC;MAC5C;;MAEA;MACA,IAAII,KAAK,CAACH,aAAa,CAAC,IAAI,CAACI,QAAQ,CAACJ,aAAa,CAAC,EAAE;QACpD,OAAO,SAAS;MAClB;;MAEA;MACA,MAAMK,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,aAAa,CAAC;MAC7C,OAAO,MAAMK,UAAU,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;IACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,EAAE,SAAS,EAAEV,MAAM,CAAC;MAChE,OAAO,SAAS;IAClB;EACF;;EAEA;EACAc,uBAAuBA,CAACC,IAAY,EAAwD;IAC1F,QAAQA,IAAI;MACV,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,OAAO;QACV,OAAO,MAAM;MACf;QACE,OAAO,SAAS;IACpB;EACF;;EAEA;EACAC,qBAAqBA,CAACC,MAAc,EAA+C;IACjF,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF;AACF;AAEA,eAAe,IAAIvC,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}