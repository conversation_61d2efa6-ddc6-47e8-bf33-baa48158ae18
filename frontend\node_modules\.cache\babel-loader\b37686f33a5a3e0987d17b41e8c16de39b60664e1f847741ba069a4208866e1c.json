{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DateRangePickerDay component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { DateRangePickerDay } from '@mui/x-date-pickers-pro'`\", \"or `import { DateRangePickerDay } from '@mui/x-date-pickers-pro/DateRangePickerDay'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DateRangePickerDay component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DateRangePickerDay = /*#__PURE__*/React.forwardRef(function DeprecatedDateRangePickerDay() {\n  warn();\n  return null;\n});\nexport default DateRangePickerDay;\nexport const getDateRangePickerDayUtilityClass = slot => {\n  warn();\n  return '';\n};", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "DateRangePickerDay", "forwardRef", "DeprecatedDateRangePickerDay", "getDateRangePickerDayUtilityClass", "slot"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/DateRangePickerDay/DateRangePickerDay.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DateRangePickerDay component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { DateRangePickerDay } from '@mui/x-date-pickers-pro'`\", \"or `import { DateRangePickerDay } from '@mui/x-date-pickers-pro/DateRangePickerDay'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DateRangePickerDay component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DateRangePickerDay = /*#__PURE__*/React.forwardRef(function DeprecatedDateRangePickerDay() {\n  warn();\n  return null;\n});\nexport default DateRangePickerDay;\nexport const getDateRangePickerDayUtilityClass = slot => {\n  warn();\n  return '';\n};"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,8FAA8F,EAAE,EAAE,EAAE,+EAA+E,EAAE,sFAAsF,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACjZH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,kBAAkB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,4BAA4BA,CAAA,EAAG;EAC/FL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,kBAAkB;AACjC,OAAO,MAAMG,iCAAiC,GAAGC,IAAI,IAAI;EACvDP,IAAI,CAAC,CAAC;EACN,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}