{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DattaAbleBreadcrumbs.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Breadcrumb } from 'react-bootstrap';\nimport { useLocation, Link } from 'react-router-dom';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DattaAbleBreadcrumbs = () => {\n  _s();\n  const location = useLocation();\n\n  // Define breadcrumb mappings\n  const breadcrumbMap = {\n    '/dashboard': 'Dashboard',\n    '/dashboard/wallet': 'Wallet',\n    '/dashboard/order': 'Order',\n    '/dashboard/orders': 'My Orders'\n  };\n\n  // Generate breadcrumb items\n  const generateBreadcrumbs = () => {\n    const pathSegments = location.pathname.split('/').filter(segment => segment);\n    const breadcrumbs = [];\n\n    // Always start with Dashboard\n    breadcrumbs.push({\n      path: '/dashboard',\n      label: 'Dashboard',\n      isActive: location.pathname === '/dashboard'\n    });\n\n    // Add current page if not dashboard\n    if (location.pathname !== '/dashboard') {\n      const currentLabel = breadcrumbMap[location.pathname] || 'Page';\n      breadcrumbs.push({\n        path: location.pathname,\n        label: currentLabel,\n        isActive: true\n      });\n    }\n    return breadcrumbs;\n  };\n  const breadcrumbs = generateBreadcrumbs();\n  const breadcrumbStyles = {\n    backgroundColor: 'transparent',\n    padding: `0 0 ${dattaAbleTheme.spacing[4]}`,\n    margin: 0,\n    fontSize: dattaAbleTheme.typography.fontSize.sm\n  };\n  const breadcrumbItemStyles = {\n    color: dattaAbleTheme.colors.text.secondary\n  };\n  const activeBreadcrumbStyles = {\n    color: dattaAbleTheme.colors.text.primary,\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium\n  };\n  const linkStyles = {\n    color: dattaAbleTheme.colors.primary.main,\n    textDecoration: 'none'\n  };\n\n  // Don't show breadcrumbs if only one item (Dashboard)\n  if (breadcrumbs.length <= 1) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Breadcrumb, {\n    style: breadcrumbStyles,\n    children: breadcrumbs.map((crumb, index) => crumb.isActive ? /*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n      active: true,\n      style: activeBreadcrumbStyles,\n      children: crumb.label\n    }, crumb.path, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n      as: Link,\n      to: crumb.path,\n      style: linkStyles,\n      onMouseEnter: e => {\n        e.target.style.textDecoration = 'underline';\n      },\n      onMouseLeave: e => {\n        e.target.style.textDecoration = 'none';\n      },\n      children: crumb.label\n    }, crumb.path, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(DattaAbleBreadcrumbs, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = DattaAbleBreadcrumbs;\nexport default DattaAbleBreadcrumbs;\nvar _c;\n$RefreshReg$(_c, \"DattaAbleBreadcrumbs\");", "map": {"version": 3, "names": ["React", "Breadcrumb", "useLocation", "Link", "dattaAbleTheme", "jsxDEV", "_jsxDEV", "DattaAbleBreadcrumbs", "_s", "location", "breadcrumbMap", "generateBreadcrumbs", "pathSegments", "pathname", "split", "filter", "segment", "breadcrumbs", "push", "path", "label", "isActive", "current<PERSON><PERSON><PERSON>", "breadcrumbStyles", "backgroundColor", "padding", "spacing", "margin", "fontSize", "typography", "sm", "breadcrumbItemStyles", "color", "colors", "text", "secondary", "activeBreadcrumbStyles", "primary", "fontWeight", "medium", "linkStyles", "main", "textDecoration", "length", "style", "children", "map", "crumb", "index", "<PERSON><PERSON>", "active", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "as", "to", "onMouseEnter", "e", "target", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DattaAbleBreadcrumbs.tsx"], "sourcesContent": ["import React from 'react';\nimport { Breadcrumb } from 'react-bootstrap';\nimport { useLocation, Link } from 'react-router-dom';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\n\nconst DattaAbleBreadcrumbs = () => {\n  const location = useLocation();\n  \n  // Define breadcrumb mappings\n  const breadcrumbMap: { [key: string]: string } = {\n    '/dashboard': 'Dashboard',\n    '/dashboard/wallet': 'Wallet',\n    '/dashboard/order': 'Order',\n    '/dashboard/orders': 'My Orders',\n  };\n\n  // Generate breadcrumb items\n  const generateBreadcrumbs = () => {\n    const pathSegments = location.pathname.split('/').filter(segment => segment);\n    const breadcrumbs = [];\n    \n    // Always start with Dashboard\n    breadcrumbs.push({\n      path: '/dashboard',\n      label: 'Dashboard',\n      isActive: location.pathname === '/dashboard'\n    });\n\n    // Add current page if not dashboard\n    if (location.pathname !== '/dashboard') {\n      const currentLabel = breadcrumbMap[location.pathname] || 'Page';\n      breadcrumbs.push({\n        path: location.pathname,\n        label: currentLabel,\n        isActive: true\n      });\n    }\n\n    return breadcrumbs;\n  };\n\n  const breadcrumbs = generateBreadcrumbs();\n\n  const breadcrumbStyles = {\n    backgroundColor: 'transparent',\n    padding: `0 0 ${dattaAbleTheme.spacing[4]}`,\n    margin: 0,\n    fontSize: dattaAbleTheme.typography.fontSize.sm,\n  };\n\n  const breadcrumbItemStyles = {\n    color: dattaAbleTheme.colors.text.secondary,\n  };\n\n  const activeBreadcrumbStyles = {\n    color: dattaAbleTheme.colors.text.primary,\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n  };\n\n  const linkStyles = {\n    color: dattaAbleTheme.colors.primary.main,\n    textDecoration: 'none',\n  };\n\n  // Don't show breadcrumbs if only one item (Dashboard)\n  if (breadcrumbs.length <= 1) {\n    return null;\n  }\n\n  return (\n    <Breadcrumb style={breadcrumbStyles}>\n      {breadcrumbs.map((crumb, index) =>\n        crumb.isActive ? (\n          <Breadcrumb.Item\n            key={crumb.path}\n            active={true}\n            style={activeBreadcrumbStyles}\n          >\n            {crumb.label}\n          </Breadcrumb.Item>\n        ) : (\n          <Breadcrumb.Item\n            key={crumb.path}\n            as={Link}\n            to={crumb.path}\n            style={linkStyles}\n            onMouseEnter={(e) => {\n              (e.target as HTMLElement).style.textDecoration = 'underline';\n            }}\n            onMouseLeave={(e) => {\n              (e.target as HTMLElement).style.textDecoration = 'none';\n            }}\n          >\n            {crumb.label}\n          </Breadcrumb.Item>\n        )\n      )}\n\n\n    </Breadcrumb>\n  );\n};\n\nexport default DattaAbleBreadcrumbs;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMQ,aAAwC,GAAG;IAC/C,YAAY,EAAE,WAAW;IACzB,mBAAmB,EAAE,QAAQ;IAC7B,kBAAkB,EAAE,OAAO;IAC3B,mBAAmB,EAAE;EACvB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,YAAY,GAAGH,QAAQ,CAACI,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAAC;IAC5E,MAAMC,WAAW,GAAG,EAAE;;IAEtB;IACAA,WAAW,CAACC,IAAI,CAAC;MACfC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,WAAW;MAClBC,QAAQ,EAAEZ,QAAQ,CAACI,QAAQ,KAAK;IAClC,CAAC,CAAC;;IAEF;IACA,IAAIJ,QAAQ,CAACI,QAAQ,KAAK,YAAY,EAAE;MACtC,MAAMS,YAAY,GAAGZ,aAAa,CAACD,QAAQ,CAACI,QAAQ,CAAC,IAAI,MAAM;MAC/DI,WAAW,CAACC,IAAI,CAAC;QACfC,IAAI,EAAEV,QAAQ,CAACI,QAAQ;QACvBO,KAAK,EAAEE,YAAY;QACnBD,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEA,OAAOJ,WAAW;EACpB,CAAC;EAED,MAAMA,WAAW,GAAGN,mBAAmB,CAAC,CAAC;EAEzC,MAAMY,gBAAgB,GAAG;IACvBC,eAAe,EAAE,aAAa;IAC9BC,OAAO,EAAE,OAAOrB,cAAc,CAACsB,OAAO,CAAC,CAAC,CAAC,EAAE;IAC3CC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAExB,cAAc,CAACyB,UAAU,CAACD,QAAQ,CAACE;EAC/C,CAAC;EAED,MAAMC,oBAAoB,GAAG;IAC3BC,KAAK,EAAE5B,cAAc,CAAC6B,MAAM,CAACC,IAAI,CAACC;EACpC,CAAC;EAED,MAAMC,sBAAsB,GAAG;IAC7BJ,KAAK,EAAE5B,cAAc,CAAC6B,MAAM,CAACC,IAAI,CAACG,OAAO;IACzCC,UAAU,EAAElC,cAAc,CAACyB,UAAU,CAACS,UAAU,CAACC;EACnD,CAAC;EAED,MAAMC,UAAU,GAAG;IACjBR,KAAK,EAAE5B,cAAc,CAAC6B,MAAM,CAACI,OAAO,CAACI,IAAI;IACzCC,cAAc,EAAE;EAClB,CAAC;;EAED;EACA,IAAIzB,WAAW,CAAC0B,MAAM,IAAI,CAAC,EAAE;IAC3B,OAAO,IAAI;EACb;EAEA,oBACErC,OAAA,CAACL,UAAU;IAAC2C,KAAK,EAAErB,gBAAiB;IAAAsB,QAAA,EACjC5B,WAAW,CAAC6B,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAC5BD,KAAK,CAAC1B,QAAQ,gBACZf,OAAA,CAACL,UAAU,CAACgD,IAAI;MAEdC,MAAM,EAAE,IAAK;MACbN,KAAK,EAAER,sBAAuB;MAAAS,QAAA,EAE7BE,KAAK,CAAC3B;IAAK,GAJP2B,KAAK,CAAC5B,IAAI;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKA,CAAC,gBAElBhD,OAAA,CAACL,UAAU,CAACgD,IAAI;MAEdM,EAAE,EAAEpD,IAAK;MACTqD,EAAE,EAAET,KAAK,CAAC5B,IAAK;MACfyB,KAAK,EAAEJ,UAAW;MAClBiB,YAAY,EAAGC,CAAC,IAAK;QAClBA,CAAC,CAACC,MAAM,CAAiBf,KAAK,CAACF,cAAc,GAAG,WAAW;MAC9D,CAAE;MACFkB,YAAY,EAAGF,CAAC,IAAK;QAClBA,CAAC,CAACC,MAAM,CAAiBf,KAAK,CAACF,cAAc,GAAG,MAAM;MACzD,CAAE;MAAAG,QAAA,EAEDE,KAAK,CAAC3B;IAAK,GAXP2B,KAAK,CAAC5B,IAAI;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAYA,CAErB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGS,CAAC;AAEjB,CAAC;AAAC9C,EAAA,CAhGID,oBAAoB;EAAA,QACPL,WAAW;AAAA;AAAA2D,EAAA,GADxBtD,oBAAoB;AAkG1B,eAAeA,oBAAoB;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}