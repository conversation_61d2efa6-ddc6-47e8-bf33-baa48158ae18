{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardRoute.tsx\";\nimport React from 'react';\nimport DattaAbleLayout from './DattaAbleLayout';\nimport ProtectedRoute from '../auth/ProtectedRoute';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardRoute = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    children: /*#__PURE__*/_jsxDEV(DattaAbleLayout, {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = DashboardRoute;\nexport default DashboardRoute;\nvar _c;\n$RefreshReg$(_c, \"DashboardRoute\");", "map": {"version": 3, "names": ["React", "DattaAbleLayout", "ProtectedRoute", "jsxDEV", "_jsxDEV", "DashboardRoute", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DashboardRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport DattaAbleLayout from './DattaAbleLayout';\nimport ProtectedRoute from '../auth/ProtectedRoute';\n\ninterface DashboardRouteProps {\n  children: React.ReactNode;\n}\n\nconst DashboardRoute: React.FC<DashboardRouteProps> = ({ children }) => {\n  return (\n    <ProtectedRoute>\n      <DattaAbleLayout>\n        {children}\n      </DattaAbleLayout>\n    </ProtectedRoute>\n  );\n};\n\nexport default DashboardRoute;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMpD,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACtE,oBACEF,OAAA,CAACF,cAAc;IAAAI,QAAA,eACbF,OAAA,CAACH,eAAe;MAAAK,QAAA,EACbA;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAErB,CAAC;AAACC,EAAA,GARIN,cAA6C;AAUnD,eAAeA,cAAc;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}