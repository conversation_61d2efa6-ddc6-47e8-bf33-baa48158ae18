{"ast": null, "code": "export { default } from \"./Skeleton.js\";\nexport * from \"./skeletonClasses.js\";\nexport { default as skeletonClasses } from \"./skeletonClasses.js\";", "map": {"version": 3, "names": ["default", "skeletonClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Skeleton/index.js"], "sourcesContent": ["export { default } from \"./Skeleton.js\";\nexport * from \"./skeletonClasses.js\";\nexport { default as skeletonClasses } from \"./skeletonClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC,cAAc,sBAAsB;AACpC,SAASA,OAAO,IAAIC,eAAe,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}