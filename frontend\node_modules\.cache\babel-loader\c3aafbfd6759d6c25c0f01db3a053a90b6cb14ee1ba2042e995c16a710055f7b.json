{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { getTimelineSeparatorUtilityClass } from \"./timelineSeparatorClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTimelineSeparatorUtilityClass, classes);\n};\nconst TimelineSeparatorRoot = styled('div', {\n  name: 'MuiTimelineSeparator',\n  slot: 'Root'\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  flex: 0,\n  alignItems: 'center'\n});\nconst TimelineSeparator = /*#__PURE__*/React.forwardRef(function TimelineSeparator(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineSeparator'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineSeparatorRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineSeparator.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineSeparator;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "useThemeProps", "getTimelineSeparatorUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "TimelineSeparatorRoot", "name", "slot", "display", "flexDirection", "flex", "alignItems", "TimelineSeparator", "forwardRef", "inProps", "ref", "props", "className", "other", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineSeparator/TimelineSeparator.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { getTimelineSeparatorUtilityClass } from \"./timelineSeparatorClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTimelineSeparatorUtilityClass, classes);\n};\nconst TimelineSeparatorRoot = styled('div', {\n  name: 'MuiTimelineSeparator',\n  slot: 'Root'\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  flex: 0,\n  alignItems: 'center'\n});\nconst TimelineSeparator = /*#__PURE__*/React.forwardRef(function TimelineSeparator(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineSeparator'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineSeparatorRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineSeparator.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineSeparator;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,gCAAgC,QAAQ,+BAA+B;AAChF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOV,cAAc,CAACS,KAAK,EAAEN,gCAAgC,EAAEK,OAAO,CAAC;AACzE,CAAC;AACD,MAAMG,qBAAqB,GAAGV,MAAM,CAAC,KAAK,EAAE;EAC1CW,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvBC,IAAI,EAAE,CAAC;EACPC,UAAU,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,iBAAiB,GAAG,aAAarB,KAAK,CAACsB,UAAU,CAAC,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/F,MAAMC,KAAK,GAAGpB,aAAa,CAAC;IAC1BoB,KAAK,EAAEF,OAAO;IACdR,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJW,SAAS;IACT,GAAGC;EACL,CAAC,GAAGF,KAAK;EACT,MAAMf,UAAU,GAAGe,KAAK;EACxB,MAAMd,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACM,qBAAqB,EAAE;IAC9CY,SAAS,EAAExB,IAAI,CAACS,OAAO,CAACE,IAAI,EAAEa,SAAS,CAAC;IACxChB,UAAU,EAAEA,UAAU;IACtBc,GAAG,EAAEA,GAAG;IACR,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGT,iBAAiB,CAACU,SAAS,CAAC,yBAAyB;EAC3F;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAE/B,SAAS,CAACgC,IAAI;EACxB;AACF;AACA;EACEtB,OAAO,EAAEV,SAAS,CAACiC,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAEzB,SAAS,CAACkC,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAEnC,SAAS,CAACoC,SAAS,CAAC,CAACpC,SAAS,CAACqC,OAAO,CAACrC,SAAS,CAACoC,SAAS,CAAC,CAACpC,SAAS,CAACsC,IAAI,EAAEtC,SAAS,CAACiC,MAAM,EAAEjC,SAAS,CAACuC,IAAI,CAAC,CAAC,CAAC,EAAEvC,SAAS,CAACsC,IAAI,EAAEtC,SAAS,CAACiC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeb,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}