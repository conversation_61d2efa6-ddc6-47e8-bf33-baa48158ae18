{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The PickersDay component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { PickersDay } from '@mui/x-date-pickers'`\", \"or `import { PickersDay } from '@mui/x-date-pickers/PickersDay'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The PickersDay component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst PickersDay = /*#__PURE__*/React.forwardRef(function DeprecatedPickersDay() {\n  warn();\n  return null;\n});\nexport default PickersDay;\nexport const pickersDayClasses = {};\nexport const getPickersDayUtilityClass = slot => {\n  warn();\n  return '';\n};", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "PickersDay", "forwardRef", "DeprecatedPickersDay", "pickersDayClasses", "getPickersDayUtilityClass", "slot"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/PickersDay/PickersDay.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The PickersDay component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { PickersDay } from '@mui/x-date-pickers'`\", \"or `import { PickersDay } from '@mui/x-date-pickers/PickersDay'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The PickersDay component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst PickersDay = /*#__PURE__*/React.forwardRef(function DeprecatedPickersDay() {\n  warn();\n  return null;\n});\nexport default PickersDay;\nexport const pickersDayClasses = {};\nexport const getPickersDayUtilityClass = slot => {\n  warn();\n  return '';\n};"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,mFAAmF,EAAE,EAAE,EAAE,mEAAmE,EAAE,kEAAkE,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACtWH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,UAAU,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,oBAAoBA,CAAA,EAAG;EAC/EL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,UAAU;AACzB,OAAO,MAAMG,iBAAiB,GAAG,CAAC,CAAC;AACnC,OAAO,MAAMC,yBAAyB,GAAGC,IAAI,IAAI;EAC/CR,IAAI,CAAC,CAAC;EACN,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}