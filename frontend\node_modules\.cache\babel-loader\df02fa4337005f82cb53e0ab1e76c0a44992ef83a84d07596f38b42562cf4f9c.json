{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Row,Col,<PERSON>,<PERSON>,<PERSON>,Spin<PERSON>}from'react-bootstrap';import creditService from'../../services/creditService';import dattaAbleTheme from'../../theme/dattaAbleTheme';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WalletBalance=_ref=>{let{refreshTrigger,onTopUpClick,onHistoryClick}=_ref;const[statistics,setStatistics]=useState(null);const[loading,setLoading]=useState(true);const[refreshing,setRefreshing]=useState(false);const fetchStatistics=async function(){let showRefreshing=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{if(showRefreshing)setRefreshing(true);const data=await creditService.getStatistics();setStatistics(data);}catch(error){console.error('Failed to fetch wallet statistics:',error);}finally{setLoading(false);if(showRefreshing)setRefreshing(false);}};useEffect(()=>{fetchStatistics();},[refreshTrigger]);const handleRefresh=()=>{fetchStatistics(true);};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center align-items-center\",style:{minHeight:'200px'},children:/*#__PURE__*/_jsx(Spinner,{animation:\"border\",variant:\"primary\"})});}if(!statistics){return/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"mb-3\",children:\"Failed to load wallet information. Please try refreshing the page.\"});}const currentBalance=statistics.current_balance||0;const totalSpent=statistics.total_spent||0;const totalPurchased=statistics.total_purchased||0;// Determine wallet status\nconst getWalletStatus=()=>{if(currentBalance<=0)return{label:'Empty',color:'warning',icon:'fas fa-exclamation-triangle'};if(currentBalance<10)return{label:'Low Balance',color:'warning',icon:'fas fa-exclamation-triangle'};return{label:'Active',color:'success',icon:'fas fa-check-circle'};};const walletStatus=getWalletStatus();return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Card,{className:\"text-white mb-4 position-relative overflow-hidden\",style:{background:`linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%)`,borderRadius:dattaAbleTheme.borderRadius['2xl'],border:'none',boxShadow:dattaAbleTheme.shadows.lg},children:/*#__PURE__*/_jsxs(Card.Body,{className:\"p-4 position-relative\",style:{zIndex:1},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-start mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"d-flex align-items-center justify-content-center\",style:{width:'60px',height:'60px',borderRadius:dattaAbleTheme.borderRadius.lg,backgroundColor:'rgba(255, 255, 255, 0.15)',backdropFilter:'blur(10px)'},children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-wallet\",style:{fontSize:'2rem'}})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-1 fw-semibold\",children:\"My Wallet\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center gap-2\",children:[/*#__PURE__*/_jsx(\"i\",{className:walletStatus.icon,style:{fontSize:'1rem'}}),/*#__PURE__*/_jsx(\"small\",{style:{opacity:0.9},children:walletStatus.label})]})]})]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-light\",size:\"sm\",onClick:handleRefresh,disabled:refreshing,className:\"d-flex align-items-center gap-2\",style:{backgroundColor:'rgba(255, 255, 255, 0.1)',borderColor:'rgba(255, 255, 255, 0.3)'},children:[refreshing?/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\"}):/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sync-alt\"}),\"Refresh\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-4\",children:[/*#__PURE__*/_jsx(\"small\",{className:\"d-block mb-2\",style:{opacity:0.8},children:\"Available Balance\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"display-3 fw-bold mb-3\",style:{textShadow:'0 2px 4px rgba(0,0,0,0.1)',fontSize:'clamp(2.5rem, 5vw, 4rem)'},children:creditService.formatWalletBalance(currentBalance)}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-3 justify-content-center flex-column flex-sm-row\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"light\",size:\"lg\",onClick:onTopUpClick,className:\"d-flex align-items-center justify-content-center gap-2 fw-semibold\",style:{backgroundColor:'rgba(255, 255, 255, 0.15)',backdropFilter:'blur(10px)',border:'1px solid rgba(255, 255, 255, 0.2)',borderRadius:dattaAbleTheme.borderRadius.lg,padding:`${dattaAbleTheme.spacing[3]} ${dattaAbleTheme.spacing[5]}`},children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-plus\"}),\"Top Up Wallet\"]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-light\",size:\"lg\",onClick:onHistoryClick,className:\"d-flex align-items-center justify-content-center gap-2 fw-semibold\",style:{borderColor:'rgba(255, 255, 255, 0.3)',borderRadius:dattaAbleTheme.borderRadius.lg,padding:`${dattaAbleTheme.spacing[3]} ${dattaAbleTheme.spacing[5]}`},children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-history\"}),\"View History\"]})]})]})]})}),/*#__PURE__*/_jsxs(Row,{className:\"g-3 mb-3\",children:[/*#__PURE__*/_jsx(Col,{xs:12,sm:6,md:4,children:/*#__PURE__*/_jsx(Card,{className:\"h-100 border-0 shadow-sm\",style:{borderRadius:dattaAbleTheme.borderRadius.lg,transition:'all 0.3s ease'},children:/*#__PURE__*/_jsxs(Card.Body,{className:\"p-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center gap-3 mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"d-flex align-items-center justify-content-center\",style:{width:'48px',height:'48px',borderRadius:dattaAbleTheme.borderRadius.lg,backgroundColor:`${dattaAbleTheme.colors.success.main}20`,color:dattaAbleTheme.colors.success.main},children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-up\"})}),/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0 fw-semibold\",children:\"Total Purchased\"})]}),/*#__PURE__*/_jsx(\"h4\",{className:\"fw-bold mb-1\",style:{color:dattaAbleTheme.colors.success.main},children:creditService.formatWalletBalance(totalPurchased)}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"Lifetime wallet top-ups\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:6,md:4,children:/*#__PURE__*/_jsx(Card,{className:\"h-100 border-0 shadow-sm\",style:{borderRadius:dattaAbleTheme.borderRadius.lg,transition:'all 0.3s ease'},children:/*#__PURE__*/_jsxs(Card.Body,{className:\"p-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center gap-3 mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"d-flex align-items-center justify-content-center\",style:{width:'48px',height:'48px',borderRadius:dattaAbleTheme.borderRadius.lg,backgroundColor:`${dattaAbleTheme.colors.warning.main}20`,color:dattaAbleTheme.colors.warning.main},children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-down\"})}),/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0 fw-semibold\",children:\"Total Spent\"})]}),/*#__PURE__*/_jsx(\"h4\",{className:\"fw-bold mb-1\",style:{color:dattaAbleTheme.colors.warning.main},children:creditService.formatWalletBalance(totalSpent)}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"Used for services\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:12,md:4,children:/*#__PURE__*/_jsx(Card,{className:\"h-100 border-0 shadow-sm\",style:{borderRadius:dattaAbleTheme.borderRadius.lg,transition:'all 0.3s ease'},children:/*#__PURE__*/_jsxs(Card.Body,{className:\"p-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center gap-3 mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"d-flex align-items-center justify-content-center\",style:{width:'48px',height:'48px',borderRadius:dattaAbleTheme.borderRadius.lg,backgroundColor:`${dattaAbleTheme.colors[walletStatus.color].main}20`,color:dattaAbleTheme.colors[walletStatus.color].main},children:/*#__PURE__*/_jsx(\"i\",{className:walletStatus.icon})}),/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0 fw-semibold\",children:\"Wallet Status\"})]}),/*#__PURE__*/_jsx(\"h4\",{className:\"fw-bold mb-1\",style:{color:dattaAbleTheme.colors[walletStatus.color].main},children:walletStatus.label}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"Current account status\"})]})})})]}),currentBalance>0&&currentBalance<10&&/*#__PURE__*/_jsxs(Alert,{variant:\"warning\",className:\"d-flex align-items-center justify-content-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-1 fw-semibold\",children:\"Low Balance Warning\"}),/*#__PURE__*/_jsx(\"small\",{children:\"Your wallet balance is running low. Consider topping up to avoid service interruptions.\"})]}),/*#__PURE__*/_jsx(Button,{variant:\"warning\",size:\"sm\",onClick:onTopUpClick,className:\"ms-3 flex-shrink-0\",children:\"Top Up Now\"})]}),currentBalance<=0&&/*#__PURE__*/_jsxs(Alert,{variant:\"info\",className:\"d-flex align-items-center justify-content-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-1 fw-semibold\",children:\"Get Started with Your Wallet\"}),/*#__PURE__*/_jsx(\"small\",{children:\"Add money to your wallet to start using our services. All transactions are secure and processed through Billplz.\"})]}),/*#__PURE__*/_jsx(Button,{variant:\"info\",size:\"sm\",onClick:onTopUpClick,className:\"ms-3 flex-shrink-0\",children:\"Add Money\"})]})]});};export default WalletBalance;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "creditService", "dattaAbleTheme", "jsx", "_jsx", "jsxs", "_jsxs", "WalletBalance", "_ref", "refreshTrigger", "onTopUpClick", "onHistoryClick", "statistics", "setStatistics", "loading", "setLoading", "refreshing", "setRefreshing", "fetchStatistics", "showRefreshing", "arguments", "length", "undefined", "data", "getStatistics", "error", "console", "handleRefresh", "className", "style", "minHeight", "children", "animation", "variant", "currentBalance", "current_balance", "totalSpent", "total_spent", "totalPurchased", "total_purchased", "getWalletStatus", "label", "color", "icon", "walletStatus", "background", "colors", "primary", "main", "dark", "borderRadius", "border", "boxShadow", "shadows", "lg", "Body", "zIndex", "width", "height", "backgroundColor", "<PERSON><PERSON>ilter", "fontSize", "opacity", "size", "onClick", "disabled", "borderColor", "textShadow", "formatWalletBalance", "padding", "spacing", "xs", "sm", "md", "transition", "success", "warning"], "sources": ["C:/laragon/www/frontend/src/components/wallet/WalletBalance.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  Col,\n  <PERSON>,\n  <PERSON>ton,\n  Alert,\n  Spinner,\n} from 'react-bootstrap';\nimport creditService, { CreditStatistics } from '../../services/creditService';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\n\ninterface WalletBalanceProps {\n  refreshTrigger: number;\n  onTopUpClick: () => void;\n  onHistoryClick: () => void;\n}\n\nconst WalletBalance: React.FC<WalletBalanceProps> = ({\n  refreshTrigger,\n  onTopUpClick,\n  onHistoryClick,\n}) => {\n  const [statistics, setStatistics] = useState<CreditStatistics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n\n  const fetchStatistics = async (showRefreshing = false) => {\n    try {\n      if (showRefreshing) setRefreshing(true);\n      const data = await creditService.getStatistics();\n      setStatistics(data);\n    } catch (error) {\n      console.error('Failed to fetch wallet statistics:', error);\n    } finally {\n      setLoading(false);\n      if (showRefreshing) setRefreshing(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchStatistics();\n  }, [refreshTrigger]);\n\n  const handleRefresh = () => {\n    fetchStatistics(true);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '200px' }}>\n        <Spinner animation=\"border\" variant=\"primary\" />\n      </div>\n    );\n  }\n\n  if (!statistics) {\n    return (\n      <Alert variant=\"danger\" className=\"mb-3\">\n        Failed to load wallet information. Please try refreshing the page.\n      </Alert>\n    );\n  }\n\n  const currentBalance = statistics.current_balance || 0;\n  const totalSpent = statistics.total_spent || 0;\n  const totalPurchased = statistics.total_purchased || 0;\n\n  // Determine wallet status\n  const getWalletStatus = () => {\n    if (currentBalance <= 0) return { label: 'Empty', color: 'warning', icon: 'fas fa-exclamation-triangle' };\n    if (currentBalance < 10) return { label: 'Low Balance', color: 'warning', icon: 'fas fa-exclamation-triangle' };\n    return { label: 'Active', color: 'success', icon: 'fas fa-check-circle' };\n  };\n\n  const walletStatus = getWalletStatus();\n\n  return (\n    <div className=\"mb-4\">\n      {/* Main Balance Card */}\n      <Card \n        className=\"text-white mb-4 position-relative overflow-hidden\"\n        style={{\n          background: `linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%)`,\n          borderRadius: dattaAbleTheme.borderRadius['2xl'],\n          border: 'none',\n          boxShadow: dattaAbleTheme.shadows.lg,\n        }}\n      >\n        <Card.Body className=\"p-4 position-relative\" style={{ zIndex: 1 }}>\n          {/* Header */}\n          <div className=\"d-flex justify-content-between align-items-start mb-4\">\n            <div className=\"d-flex align-items-center gap-3\">\n              <div \n                className=\"d-flex align-items-center justify-content-center\"\n                style={{\n                  width: '60px',\n                  height: '60px',\n                  borderRadius: dattaAbleTheme.borderRadius.lg,\n                  backgroundColor: 'rgba(255, 255, 255, 0.15)',\n                  backdropFilter: 'blur(10px)',\n                }}\n              >\n                <i className=\"fas fa-wallet\" style={{ fontSize: '2rem' }}></i>\n              </div>\n              <div>\n                <h6 className=\"mb-1 fw-semibold\">My Wallet</h6>\n                <div className=\"d-flex align-items-center gap-2\">\n                  <i className={walletStatus.icon} style={{ fontSize: '1rem' }}></i>\n                  <small style={{ opacity: 0.9 }}>{walletStatus.label}</small>\n                </div>\n              </div>\n            </div>\n            <Button\n              variant=\"outline-light\"\n              size=\"sm\"\n              onClick={handleRefresh}\n              disabled={refreshing}\n              className=\"d-flex align-items-center gap-2\"\n              style={{\n                backgroundColor: 'rgba(255, 255, 255, 0.1)',\n                borderColor: 'rgba(255, 255, 255, 0.3)',\n              }}\n            >\n              {refreshing ? (\n                <Spinner animation=\"border\" size=\"sm\" />\n              ) : (\n                <i className=\"fas fa-sync-alt\"></i>\n              )}\n              Refresh\n            </Button>\n          </div>\n\n          {/* Main Balance Display */}\n          <div className=\"text-center mb-4\">\n            <small className=\"d-block mb-2\" style={{ opacity: 0.8 }}>\n              Available Balance\n            </small>\n            <h1 \n              className=\"display-3 fw-bold mb-3\"\n              style={{\n                textShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                fontSize: 'clamp(2.5rem, 5vw, 4rem)',\n              }}\n            >\n              {creditService.formatWalletBalance(currentBalance)}\n            </h1>\n\n            {/* Quick Action Buttons */}\n            <div className=\"d-flex gap-3 justify-content-center flex-column flex-sm-row\">\n              <Button\n                variant=\"light\"\n                size=\"lg\"\n                onClick={onTopUpClick}\n                className=\"d-flex align-items-center justify-content-center gap-2 fw-semibold\"\n                style={{\n                  backgroundColor: 'rgba(255, 255, 255, 0.15)',\n                  backdropFilter: 'blur(10px)',\n                  border: '1px solid rgba(255, 255, 255, 0.2)',\n                  borderRadius: dattaAbleTheme.borderRadius.lg,\n                  padding: `${dattaAbleTheme.spacing[3]} ${dattaAbleTheme.spacing[5]}`,\n                }}\n              >\n                <i className=\"fas fa-plus\"></i>\n                Top Up Wallet\n              </Button>\n              <Button\n                variant=\"outline-light\"\n                size=\"lg\"\n                onClick={onHistoryClick}\n                className=\"d-flex align-items-center justify-content-center gap-2 fw-semibold\"\n                style={{\n                  borderColor: 'rgba(255, 255, 255, 0.3)',\n                  borderRadius: dattaAbleTheme.borderRadius.lg,\n                  padding: `${dattaAbleTheme.spacing[3]} ${dattaAbleTheme.spacing[5]}`,\n                }}\n              >\n                <i className=\"fas fa-history\"></i>\n                View History\n              </Button>\n            </div>\n          </div>\n        </Card.Body>\n      </Card>\n\n      {/* Statistics Grid */}\n      <Row className=\"g-3 mb-3\">\n        <Col xs={12} sm={6} md={4}>\n          <Card \n            className=\"h-100 border-0 shadow-sm\"\n            style={{ \n              borderRadius: dattaAbleTheme.borderRadius.lg,\n              transition: 'all 0.3s ease',\n            }}\n          >\n            <Card.Body className=\"p-3\">\n              <div className=\"d-flex align-items-center gap-3 mb-2\">\n                <div \n                  className=\"d-flex align-items-center justify-content-center\"\n                  style={{\n                    width: '48px',\n                    height: '48px',\n                    borderRadius: dattaAbleTheme.borderRadius.lg,\n                    backgroundColor: `${dattaAbleTheme.colors.success.main}20`,\n                    color: dattaAbleTheme.colors.success.main,\n                  }}\n                >\n                  <i className=\"fas fa-arrow-up\"></i>\n                </div>\n                <h6 className=\"mb-0 fw-semibold\">Total Purchased</h6>\n              </div>\n              <h4 className=\"fw-bold mb-1\" style={{ color: dattaAbleTheme.colors.success.main }}>\n                {creditService.formatWalletBalance(totalPurchased)}\n              </h4>\n              <small className=\"text-muted\">Lifetime wallet top-ups</small>\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col xs={12} sm={6} md={4}>\n          <Card \n            className=\"h-100 border-0 shadow-sm\"\n            style={{ \n              borderRadius: dattaAbleTheme.borderRadius.lg,\n              transition: 'all 0.3s ease',\n            }}\n          >\n            <Card.Body className=\"p-3\">\n              <div className=\"d-flex align-items-center gap-3 mb-2\">\n                <div \n                  className=\"d-flex align-items-center justify-content-center\"\n                  style={{\n                    width: '48px',\n                    height: '48px',\n                    borderRadius: dattaAbleTheme.borderRadius.lg,\n                    backgroundColor: `${dattaAbleTheme.colors.warning.main}20`,\n                    color: dattaAbleTheme.colors.warning.main,\n                  }}\n                >\n                  <i className=\"fas fa-arrow-down\"></i>\n                </div>\n                <h6 className=\"mb-0 fw-semibold\">Total Spent</h6>\n              </div>\n              <h4 className=\"fw-bold mb-1\" style={{ color: dattaAbleTheme.colors.warning.main }}>\n                {creditService.formatWalletBalance(totalSpent)}\n              </h4>\n              <small className=\"text-muted\">Used for services</small>\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col xs={12} sm={12} md={4}>\n          <Card \n            className=\"h-100 border-0 shadow-sm\"\n            style={{ \n              borderRadius: dattaAbleTheme.borderRadius.lg,\n              transition: 'all 0.3s ease',\n            }}\n          >\n            <Card.Body className=\"p-3\">\n              <div className=\"d-flex align-items-center gap-3 mb-2\">\n                <div \n                  className=\"d-flex align-items-center justify-content-center\"\n                  style={{\n                    width: '48px',\n                    height: '48px',\n                    borderRadius: dattaAbleTheme.borderRadius.lg,\n                    backgroundColor: `${(dattaAbleTheme.colors as any)[walletStatus.color].main}20`,\n                    color: (dattaAbleTheme.colors as any)[walletStatus.color].main,\n                  }}\n                >\n                  <i className={walletStatus.icon}></i>\n                </div>\n                <h6 className=\"mb-0 fw-semibold\">Wallet Status</h6>\n              </div>\n              <h4 className=\"fw-bold mb-1\" style={{ color: (dattaAbleTheme.colors as any)[walletStatus.color].main }}>\n                {walletStatus.label}\n              </h4>\n              <small className=\"text-muted\">Current account status</small>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Low Balance Warning */}\n      {currentBalance > 0 && currentBalance < 10 && (\n        <Alert variant=\"warning\" className=\"d-flex align-items-center justify-content-between\">\n          <div>\n            <h6 className=\"mb-1 fw-semibold\">Low Balance Warning</h6>\n            <small>Your wallet balance is running low. Consider topping up to avoid service interruptions.</small>\n          </div>\n          <Button variant=\"warning\" size=\"sm\" onClick={onTopUpClick} className=\"ms-3 flex-shrink-0\">\n            Top Up Now\n          </Button>\n        </Alert>\n      )}\n\n      {/* Empty Balance Message */}\n      {currentBalance <= 0 && (\n        <Alert variant=\"info\" className=\"d-flex align-items-center justify-content-between\">\n          <div>\n            <h6 className=\"mb-1 fw-semibold\">Get Started with Your Wallet</h6>\n            <small>Add money to your wallet to start using our services. All transactions are secure and processed through Billplz.</small>\n          </div>\n          <Button variant=\"info\" size=\"sm\" onClick={onTopUpClick} className=\"ms-3 flex-shrink-0\">\n            Add Money\n          </Button>\n        </Alert>\n      )}\n    </div>\n  );\n};\n\nexport default WalletBalance;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAEEC,GAAG,CACHC,GAAG,CACHC,IAAI,CACJC,MAAM,CACNC,KAAK,CACLC,OAAO,KACF,iBAAiB,CACxB,MAAO,CAAAC,aAAa,KAA4B,8BAA8B,CAC9E,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQxD,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAI9C,IAJ+C,CACnDC,cAAc,CACdC,YAAY,CACZC,cACF,CAAC,CAAAH,IAAA,CACC,KAAM,CAACI,UAAU,CAAEC,aAAa,CAAC,CAAGpB,QAAQ,CAA0B,IAAI,CAAC,CAC3E,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACuB,UAAU,CAAEC,aAAa,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAEnD,KAAM,CAAAyB,eAAe,CAAG,cAAAA,CAAA,CAAkC,IAA3B,CAAAC,cAAc,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACnD,GAAI,CACF,GAAID,cAAc,CAAEF,aAAa,CAAC,IAAI,CAAC,CACvC,KAAM,CAAAM,IAAI,CAAG,KAAM,CAAAtB,aAAa,CAACuB,aAAa,CAAC,CAAC,CAChDX,aAAa,CAACU,IAAI,CAAC,CACrB,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC5D,CAAC,OAAS,CACRV,UAAU,CAAC,KAAK,CAAC,CACjB,GAAII,cAAc,CAAEF,aAAa,CAAC,KAAK,CAAC,CAC1C,CACF,CAAC,CAEDvB,SAAS,CAAC,IAAM,CACdwB,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAACT,cAAc,CAAC,CAAC,CAEpB,KAAM,CAAAkB,aAAa,CAAGA,CAAA,GAAM,CAC1BT,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,GAAIJ,OAAO,CAAE,CACX,mBACEV,IAAA,QAAKwB,SAAS,CAAC,kDAAkD,CAACC,KAAK,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAAAC,QAAA,cAC9F3B,IAAA,CAACJ,OAAO,EAACgC,SAAS,CAAC,QAAQ,CAACC,OAAO,CAAC,SAAS,CAAE,CAAC,CAC7C,CAAC,CAEV,CAEA,GAAI,CAACrB,UAAU,CAAE,CACf,mBACER,IAAA,CAACL,KAAK,EAACkC,OAAO,CAAC,QAAQ,CAACL,SAAS,CAAC,MAAM,CAAAG,QAAA,CAAC,oEAEzC,CAAO,CAAC,CAEZ,CAEA,KAAM,CAAAG,cAAc,CAAGtB,UAAU,CAACuB,eAAe,EAAI,CAAC,CACtD,KAAM,CAAAC,UAAU,CAAGxB,UAAU,CAACyB,WAAW,EAAI,CAAC,CAC9C,KAAM,CAAAC,cAAc,CAAG1B,UAAU,CAAC2B,eAAe,EAAI,CAAC,CAEtD;AACA,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIN,cAAc,EAAI,CAAC,CAAE,MAAO,CAAEO,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,6BAA8B,CAAC,CACzG,GAAIT,cAAc,CAAG,EAAE,CAAE,MAAO,CAAEO,KAAK,CAAE,aAAa,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,6BAA8B,CAAC,CAC/G,MAAO,CAAEF,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,qBAAsB,CAAC,CAC3E,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGJ,eAAe,CAAC,CAAC,CAEtC,mBACElC,KAAA,QAAKsB,SAAS,CAAC,MAAM,CAAAG,QAAA,eAEnB3B,IAAA,CAACP,IAAI,EACH+B,SAAS,CAAC,mDAAmD,CAC7DC,KAAK,CAAE,CACLgB,UAAU,CAAE,2BAA2B3C,cAAc,CAAC4C,MAAM,CAACC,OAAO,CAACC,IAAI,QAAQ9C,cAAc,CAAC4C,MAAM,CAACC,OAAO,CAACE,IAAI,QAAQ,CAC3HC,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAAC,KAAK,CAAC,CAChDC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAElD,cAAc,CAACmD,OAAO,CAACC,EACpC,CAAE,CAAAvB,QAAA,cAEFzB,KAAA,CAACT,IAAI,CAAC0D,IAAI,EAAC3B,SAAS,CAAC,uBAAuB,CAACC,KAAK,CAAE,CAAE2B,MAAM,CAAE,CAAE,CAAE,CAAAzB,QAAA,eAEhEzB,KAAA,QAAKsB,SAAS,CAAC,uDAAuD,CAAAG,QAAA,eACpEzB,KAAA,QAAKsB,SAAS,CAAC,iCAAiC,CAAAG,QAAA,eAC9C3B,IAAA,QACEwB,SAAS,CAAC,kDAAkD,CAC5DC,KAAK,CAAE,CACL4B,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdR,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAACI,EAAE,CAC5CK,eAAe,CAAE,2BAA2B,CAC5CC,cAAc,CAAE,YAClB,CAAE,CAAA7B,QAAA,cAEF3B,IAAA,MAAGwB,SAAS,CAAC,eAAe,CAACC,KAAK,CAAE,CAAEgC,QAAQ,CAAE,MAAO,CAAE,CAAI,CAAC,CAC3D,CAAC,cACNvD,KAAA,QAAAyB,QAAA,eACE3B,IAAA,OAAIwB,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAC,WAAS,CAAI,CAAC,cAC/CzB,KAAA,QAAKsB,SAAS,CAAC,iCAAiC,CAAAG,QAAA,eAC9C3B,IAAA,MAAGwB,SAAS,CAAEgB,YAAY,CAACD,IAAK,CAACd,KAAK,CAAE,CAAEgC,QAAQ,CAAE,MAAO,CAAE,CAAI,CAAC,cAClEzD,IAAA,UAAOyB,KAAK,CAAE,CAAEiC,OAAO,CAAE,GAAI,CAAE,CAAA/B,QAAA,CAAEa,YAAY,CAACH,KAAK,CAAQ,CAAC,EACzD,CAAC,EACH,CAAC,EACH,CAAC,cACNnC,KAAA,CAACR,MAAM,EACLmC,OAAO,CAAC,eAAe,CACvB8B,IAAI,CAAC,IAAI,CACTC,OAAO,CAAErC,aAAc,CACvBsC,QAAQ,CAAEjD,UAAW,CACrBY,SAAS,CAAC,iCAAiC,CAC3CC,KAAK,CAAE,CACL8B,eAAe,CAAE,0BAA0B,CAC3CO,WAAW,CAAE,0BACf,CAAE,CAAAnC,QAAA,EAEDf,UAAU,cACTZ,IAAA,CAACJ,OAAO,EAACgC,SAAS,CAAC,QAAQ,CAAC+B,IAAI,CAAC,IAAI,CAAE,CAAC,cAExC3D,IAAA,MAAGwB,SAAS,CAAC,iBAAiB,CAAI,CACnC,CAAC,SAEJ,EAAQ,CAAC,EACN,CAAC,cAGNtB,KAAA,QAAKsB,SAAS,CAAC,kBAAkB,CAAAG,QAAA,eAC/B3B,IAAA,UAAOwB,SAAS,CAAC,cAAc,CAACC,KAAK,CAAE,CAAEiC,OAAO,CAAE,GAAI,CAAE,CAAA/B,QAAA,CAAC,mBAEzD,CAAO,CAAC,cACR3B,IAAA,OACEwB,SAAS,CAAC,wBAAwB,CAClCC,KAAK,CAAE,CACLsC,UAAU,CAAE,2BAA2B,CACvCN,QAAQ,CAAE,0BACZ,CAAE,CAAA9B,QAAA,CAED9B,aAAa,CAACmE,mBAAmB,CAAClC,cAAc,CAAC,CAChD,CAAC,cAGL5B,KAAA,QAAKsB,SAAS,CAAC,6DAA6D,CAAAG,QAAA,eAC1EzB,KAAA,CAACR,MAAM,EACLmC,OAAO,CAAC,OAAO,CACf8B,IAAI,CAAC,IAAI,CACTC,OAAO,CAAEtD,YAAa,CACtBkB,SAAS,CAAC,oEAAoE,CAC9EC,KAAK,CAAE,CACL8B,eAAe,CAAE,2BAA2B,CAC5CC,cAAc,CAAE,YAAY,CAC5BT,MAAM,CAAE,oCAAoC,CAC5CD,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAACI,EAAE,CAC5Ce,OAAO,CAAE,GAAGnE,cAAc,CAACoE,OAAO,CAAC,CAAC,CAAC,IAAIpE,cAAc,CAACoE,OAAO,CAAC,CAAC,CAAC,EACpE,CAAE,CAAAvC,QAAA,eAEF3B,IAAA,MAAGwB,SAAS,CAAC,aAAa,CAAI,CAAC,gBAEjC,EAAQ,CAAC,cACTtB,KAAA,CAACR,MAAM,EACLmC,OAAO,CAAC,eAAe,CACvB8B,IAAI,CAAC,IAAI,CACTC,OAAO,CAAErD,cAAe,CACxBiB,SAAS,CAAC,oEAAoE,CAC9EC,KAAK,CAAE,CACLqC,WAAW,CAAE,0BAA0B,CACvChB,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAACI,EAAE,CAC5Ce,OAAO,CAAE,GAAGnE,cAAc,CAACoE,OAAO,CAAC,CAAC,CAAC,IAAIpE,cAAc,CAACoE,OAAO,CAAC,CAAC,CAAC,EACpE,CAAE,CAAAvC,QAAA,eAEF3B,IAAA,MAAGwB,SAAS,CAAC,gBAAgB,CAAI,CAAC,eAEpC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACG,CAAC,CACR,CAAC,cAGPtB,KAAA,CAACX,GAAG,EAACiC,SAAS,CAAC,UAAU,CAAAG,QAAA,eACvB3B,IAAA,CAACR,GAAG,EAAC2E,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAA1C,QAAA,cACxB3B,IAAA,CAACP,IAAI,EACH+B,SAAS,CAAC,0BAA0B,CACpCC,KAAK,CAAE,CACLqB,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAACI,EAAE,CAC5CoB,UAAU,CAAE,eACd,CAAE,CAAA3C,QAAA,cAEFzB,KAAA,CAACT,IAAI,CAAC0D,IAAI,EAAC3B,SAAS,CAAC,KAAK,CAAAG,QAAA,eACxBzB,KAAA,QAAKsB,SAAS,CAAC,sCAAsC,CAAAG,QAAA,eACnD3B,IAAA,QACEwB,SAAS,CAAC,kDAAkD,CAC5DC,KAAK,CAAE,CACL4B,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdR,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAACI,EAAE,CAC5CK,eAAe,CAAE,GAAGzD,cAAc,CAAC4C,MAAM,CAAC6B,OAAO,CAAC3B,IAAI,IAAI,CAC1DN,KAAK,CAAExC,cAAc,CAAC4C,MAAM,CAAC6B,OAAO,CAAC3B,IACvC,CAAE,CAAAjB,QAAA,cAEF3B,IAAA,MAAGwB,SAAS,CAAC,iBAAiB,CAAI,CAAC,CAChC,CAAC,cACNxB,IAAA,OAAIwB,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAC,iBAAe,CAAI,CAAC,EAClD,CAAC,cACN3B,IAAA,OAAIwB,SAAS,CAAC,cAAc,CAACC,KAAK,CAAE,CAAEa,KAAK,CAAExC,cAAc,CAAC4C,MAAM,CAAC6B,OAAO,CAAC3B,IAAK,CAAE,CAAAjB,QAAA,CAC/E9B,aAAa,CAACmE,mBAAmB,CAAC9B,cAAc,CAAC,CAChD,CAAC,cACLlC,IAAA,UAAOwB,SAAS,CAAC,YAAY,CAAAG,QAAA,CAAC,yBAAuB,CAAO,CAAC,EACpD,CAAC,CACR,CAAC,CACJ,CAAC,cAEN3B,IAAA,CAACR,GAAG,EAAC2E,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAA1C,QAAA,cACxB3B,IAAA,CAACP,IAAI,EACH+B,SAAS,CAAC,0BAA0B,CACpCC,KAAK,CAAE,CACLqB,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAACI,EAAE,CAC5CoB,UAAU,CAAE,eACd,CAAE,CAAA3C,QAAA,cAEFzB,KAAA,CAACT,IAAI,CAAC0D,IAAI,EAAC3B,SAAS,CAAC,KAAK,CAAAG,QAAA,eACxBzB,KAAA,QAAKsB,SAAS,CAAC,sCAAsC,CAAAG,QAAA,eACnD3B,IAAA,QACEwB,SAAS,CAAC,kDAAkD,CAC5DC,KAAK,CAAE,CACL4B,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdR,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAACI,EAAE,CAC5CK,eAAe,CAAE,GAAGzD,cAAc,CAAC4C,MAAM,CAAC8B,OAAO,CAAC5B,IAAI,IAAI,CAC1DN,KAAK,CAAExC,cAAc,CAAC4C,MAAM,CAAC8B,OAAO,CAAC5B,IACvC,CAAE,CAAAjB,QAAA,cAEF3B,IAAA,MAAGwB,SAAS,CAAC,mBAAmB,CAAI,CAAC,CAClC,CAAC,cACNxB,IAAA,OAAIwB,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAC,aAAW,CAAI,CAAC,EAC9C,CAAC,cACN3B,IAAA,OAAIwB,SAAS,CAAC,cAAc,CAACC,KAAK,CAAE,CAAEa,KAAK,CAAExC,cAAc,CAAC4C,MAAM,CAAC8B,OAAO,CAAC5B,IAAK,CAAE,CAAAjB,QAAA,CAC/E9B,aAAa,CAACmE,mBAAmB,CAAChC,UAAU,CAAC,CAC5C,CAAC,cACLhC,IAAA,UAAOwB,SAAS,CAAC,YAAY,CAAAG,QAAA,CAAC,mBAAiB,CAAO,CAAC,EAC9C,CAAC,CACR,CAAC,CACJ,CAAC,cAEN3B,IAAA,CAACR,GAAG,EAAC2E,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA1C,QAAA,cACzB3B,IAAA,CAACP,IAAI,EACH+B,SAAS,CAAC,0BAA0B,CACpCC,KAAK,CAAE,CACLqB,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAACI,EAAE,CAC5CoB,UAAU,CAAE,eACd,CAAE,CAAA3C,QAAA,cAEFzB,KAAA,CAACT,IAAI,CAAC0D,IAAI,EAAC3B,SAAS,CAAC,KAAK,CAAAG,QAAA,eACxBzB,KAAA,QAAKsB,SAAS,CAAC,sCAAsC,CAAAG,QAAA,eACnD3B,IAAA,QACEwB,SAAS,CAAC,kDAAkD,CAC5DC,KAAK,CAAE,CACL4B,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdR,YAAY,CAAEhD,cAAc,CAACgD,YAAY,CAACI,EAAE,CAC5CK,eAAe,CAAE,GAAIzD,cAAc,CAAC4C,MAAM,CAASF,YAAY,CAACF,KAAK,CAAC,CAACM,IAAI,IAAI,CAC/EN,KAAK,CAAGxC,cAAc,CAAC4C,MAAM,CAASF,YAAY,CAACF,KAAK,CAAC,CAACM,IAC5D,CAAE,CAAAjB,QAAA,cAEF3B,IAAA,MAAGwB,SAAS,CAAEgB,YAAY,CAACD,IAAK,CAAI,CAAC,CAClC,CAAC,cACNvC,IAAA,OAAIwB,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAC,eAAa,CAAI,CAAC,EAChD,CAAC,cACN3B,IAAA,OAAIwB,SAAS,CAAC,cAAc,CAACC,KAAK,CAAE,CAAEa,KAAK,CAAGxC,cAAc,CAAC4C,MAAM,CAASF,YAAY,CAACF,KAAK,CAAC,CAACM,IAAK,CAAE,CAAAjB,QAAA,CACpGa,YAAY,CAACH,KAAK,CACjB,CAAC,cACLrC,IAAA,UAAOwB,SAAS,CAAC,YAAY,CAAAG,QAAA,CAAC,wBAAsB,CAAO,CAAC,EACnD,CAAC,CACR,CAAC,CACJ,CAAC,EACH,CAAC,CAGLG,cAAc,CAAG,CAAC,EAAIA,cAAc,CAAG,EAAE,eACxC5B,KAAA,CAACP,KAAK,EAACkC,OAAO,CAAC,SAAS,CAACL,SAAS,CAAC,mDAAmD,CAAAG,QAAA,eACpFzB,KAAA,QAAAyB,QAAA,eACE3B,IAAA,OAAIwB,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAC,qBAAmB,CAAI,CAAC,cACzD3B,IAAA,UAAA2B,QAAA,CAAO,yFAAuF,CAAO,CAAC,EACnG,CAAC,cACN3B,IAAA,CAACN,MAAM,EAACmC,OAAO,CAAC,SAAS,CAAC8B,IAAI,CAAC,IAAI,CAACC,OAAO,CAAEtD,YAAa,CAACkB,SAAS,CAAC,oBAAoB,CAAAG,QAAA,CAAC,YAE1F,CAAQ,CAAC,EACJ,CACR,CAGAG,cAAc,EAAI,CAAC,eAClB5B,KAAA,CAACP,KAAK,EAACkC,OAAO,CAAC,MAAM,CAACL,SAAS,CAAC,mDAAmD,CAAAG,QAAA,eACjFzB,KAAA,QAAAyB,QAAA,eACE3B,IAAA,OAAIwB,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAC,8BAA4B,CAAI,CAAC,cAClE3B,IAAA,UAAA2B,QAAA,CAAO,kHAAgH,CAAO,CAAC,EAC5H,CAAC,cACN3B,IAAA,CAACN,MAAM,EAACmC,OAAO,CAAC,MAAM,CAAC8B,IAAI,CAAC,IAAI,CAACC,OAAO,CAAEtD,YAAa,CAACkB,SAAS,CAAC,oBAAoB,CAAAG,QAAA,CAAC,WAEvF,CAAQ,CAAC,EACJ,CACR,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}