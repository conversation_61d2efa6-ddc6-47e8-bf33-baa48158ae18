{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getCardActionsUtilityClass } from \"./cardActionsClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getCardActionsUtilityClass, classes);\n};\nconst CardActionsRoot = styled('div', {\n  name: 'MuiCardActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8,\n  variants: [{\n    props: {\n      disableSpacing: false\n    },\n    style: {\n      '& > :not(style) ~ :not(style)': {\n        marginLeft: 8\n      }\n    }\n  }]\n});\nconst CardActions = /*#__PURE__*/React.forwardRef(function CardActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActions'\n  });\n  const {\n    disableSpacing = false,\n    className,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    disableSpacing\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardActionsRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActions;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "getCardActionsUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "disableSpacing", "slots", "root", "CardActionsRoot", "name", "slot", "overridesResolver", "props", "styles", "spacing", "display", "alignItems", "padding", "variants", "style", "marginLeft", "CardActions", "forwardRef", "inProps", "ref", "className", "other", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "bool", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/CardActions/CardActions.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getCardActionsUtilityClass } from \"./cardActionsClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getCardActionsUtilityClass, classes);\n};\nconst CardActionsRoot = styled('div', {\n  name: 'MuiCardActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8,\n  variants: [{\n    props: {\n      disableSpacing: false\n    },\n    style: {\n      '& > :not(style) ~ :not(style)': {\n        marginLeft: 8\n      }\n    }\n  }]\n});\nconst CardActions = /*#__PURE__*/React.forwardRef(function CardActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActions'\n  });\n  const {\n    disableSpacing = false,\n    className,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    disableSpacing\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardActionsRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActions;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,0BAA0B,QAAQ,yBAAyB;AACpE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACF,cAAc,IAAI,SAAS;EAC7C,CAAC;EACD,OAAOT,cAAc,CAACU,KAAK,EAAEP,0BAA0B,EAAEK,OAAO,CAAC;AACnE,CAAC;AACD,MAAMI,eAAe,GAAGX,MAAM,CAAC,KAAK,EAAE;EACpCY,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAE,CAACJ,UAAU,CAACE,cAAc,IAAIQ,MAAM,CAACC,OAAO,CAAC;EACpE;AACF,CAAC,CAAC,CAAC;EACDC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;IACTN,KAAK,EAAE;MACLP,cAAc,EAAE;IAClB,CAAC;IACDc,KAAK,EAAE;MACL,+BAA+B,EAAE;QAC/BC,UAAU,EAAE;MACd;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,WAAW,GAAG,aAAa5B,KAAK,CAAC6B,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAMZ,KAAK,GAAGd,eAAe,CAAC;IAC5Bc,KAAK,EAAEW,OAAO;IACdd,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJJ,cAAc,GAAG,KAAK;IACtBoB,SAAS;IACT,GAAGC;EACL,CAAC,GAAGd,KAAK;EACT,MAAMT,UAAU,GAAG;IACjB,GAAGS,KAAK;IACRP;EACF,CAAC;EACD,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,eAAe,EAAE;IACxCiB,SAAS,EAAE9B,IAAI,CAACS,OAAO,CAACG,IAAI,EAAEkB,SAAS,CAAC;IACxCtB,UAAU,EAAEA,UAAU;IACtBqB,GAAG,EAAEA,GAAG;IACR,GAAGE;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,WAAW,CAACS,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAErC,SAAS,CAACsC,IAAI;EACxB;AACF;AACA;EACE5B,OAAO,EAAEV,SAAS,CAACuC,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAE/B,SAAS,CAACwC,MAAM;EAC3B;AACF;AACA;AACA;EACE7B,cAAc,EAAEX,SAAS,CAACyC,IAAI;EAC9B;AACF;AACA;EACEC,EAAE,EAAE1C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC4C,OAAO,CAAC5C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACuC,MAAM,EAAEvC,SAAS,CAACyC,IAAI,CAAC,CAAC,CAAC,EAAEzC,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACuC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeZ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}