{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport divWithClassName from './divWithClassName';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst OffcanvasTitle = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    className,\n    bsPrefix,\n    as: Component = DivStyledAsH5,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nOffcanvasTitle.displayName = 'OffcanvasTitle';\nexport default OffcanvasTitle;", "map": {"version": 3, "names": ["React", "classNames", "divWithClassName", "useBootstrapPrefix", "jsx", "_jsx", "DivStyledAsH5", "OffcanvasTitle", "forwardRef", "_ref", "ref", "className", "bsPrefix", "as", "Component", "props", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/OffcanvasTitle.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport divWithClassName from './divWithClassName';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst OffcanvasTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nOffcanvasTitle.displayName = 'OffcanvasTitle';\nexport default OffcanvasTitle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGJ,gBAAgB,CAAC,IAAI,CAAC;AAC5C,MAAMK,cAAc,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAAC,IAAA,EAKlDC,GAAG,KAAK;EAAA,IAL2C;IACpDC,SAAS;IACTC,QAAQ;IACRC,EAAE,EAAEC,SAAS,GAAGR,aAAa;IAC7B,GAAGS;EACL,CAAC,GAAAN,IAAA;EACCG,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,iBAAiB,CAAC;EAC1D,OAAO,aAAaP,IAAI,CAACS,SAAS,EAAE;IAClCJ,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,cAAc,CAACS,WAAW,GAAG,gBAAgB;AAC7C,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}