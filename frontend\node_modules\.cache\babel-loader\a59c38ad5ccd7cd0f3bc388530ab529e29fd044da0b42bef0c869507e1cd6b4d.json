{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ButtonToolbar = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    role = 'toolbar',\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-toolbar');\n  return /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(className, prefix),\n    role: role\n  });\n});\nButtonToolbar.displayName = 'ButtonToolbar';\nexport default ButtonToolbar;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "ButtonToolbar", "forwardRef", "_ref", "ref", "bsPrefix", "className", "role", "props", "prefix", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/ButtonToolbar.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ButtonToolbar = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  role = 'toolbar',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-toolbar');\n  return /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(className, prefix),\n    role: role\n  });\n});\nButtonToolbar.displayName = 'ButtonToolbar';\nexport default ButtonToolbar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAAC,IAAA,EAKjDC,GAAG,KAAK;EAAA,IAL0C;IACnDC,QAAQ;IACRC,SAAS;IACTC,IAAI,GAAG,SAAS;IAChB,GAAGC;EACL,CAAC,GAAAL,IAAA;EACC,MAAMM,MAAM,GAAGX,kBAAkB,CAACO,QAAQ,EAAE,aAAa,CAAC;EAC1D,OAAO,aAAaL,IAAI,CAAC,KAAK,EAAE;IAC9B,GAAGQ,KAAK;IACRJ,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAEG,MAAM,CAAC;IACxCF,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,aAAa,CAACS,WAAW,GAAG,eAAe;AAC3C,eAAeT,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}