{"ast": null, "code": "import React,{createContext,useContext,useEffect,useState}from'react';import authService from'../services/authService';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext(undefined);export const useAuth=()=>{const context=useContext(AuthContext);if(context===undefined){throw new Error('useAuth must be used within an AuthProvider');}return context;};export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[loading,setLoading]=useState(true);useEffect(()=>{initializeAuth();},[]);const initializeAuth=async()=>{try{const storedUser=authService.getStoredUser();const token=authService.getStoredToken();if(storedUser&&token){// Verify token is still valid by fetching current user\ntry{const currentUser=await authService.getProfile();setUser(currentUser);}catch(error){// Token is invalid, clear stored data\nauthService.clearAuthData();setUser(null);}}}catch(error){console.error('Auth initialization error:',error);}finally{setLoading(false);}};const login=async credentials=>{setLoading(true);try{const response=await authService.login(credentials);setUser(response.user);return{redirect_url:response.redirect_url};}finally{setLoading(false);}};const register=async data=>{setLoading(true);try{const response=await authService.register(data);setUser(response.user);return{redirect_url:response.redirect_url};}finally{setLoading(false);}};const logout=async()=>{setLoading(true);try{await authService.logout();setUser(null);}finally{setLoading(false);}};const updateProfile=async data=>{const updatedUser=await authService.updateProfile(data);setUser(updatedUser);};const uploadAvatar=async file=>{const updatedUser=await authService.uploadAvatar(file);setUser(updatedUser);};const refreshUser=async()=>{if(authService.isAuthenticated()){try{const currentUser=await authService.getProfile();setUser(currentUser);}catch(error){console.error('Failed to refresh user:',error);await logout();}}};const forgotPassword=async email=>{return authService.forgotPassword(email);};const resetPassword=async data=>{return authService.resetPassword(data);};const resendVerification=async()=>{return authService.resendVerification();};const value={user,loading,isAuthenticated:!!user,isEmailVerified:!!(user!==null&&user!==void 0&&user.email_verified_at),login,register,logout,updateProfile,uploadAvatar,refreshUser,forgotPassword,resetPassword,resendVerification};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "authService", "jsx", "_jsx", "AuthContext", "undefined", "useAuth", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "loading", "setLoading", "initializeAuth", "storedUser", "getStoredUser", "token", "getStoredToken", "currentUser", "getProfile", "error", "clearAuthData", "console", "login", "credentials", "response", "redirect_url", "register", "data", "logout", "updateProfile", "updatedUser", "uploadAvatar", "file", "refreshUser", "isAuthenticated", "forgotPassword", "email", "resetPassword", "resendVerification", "value", "isEmailVerified", "email_verified_at", "Provider"], "sources": ["C:/laragon/www/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport authService, { User, LoginCredentials, RegisterData, UpdateProfileData } from '../services/authService';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  isAuthenticated: boolean;\n  isEmailVerified: boolean;\n  login: (credentials: LoginCredentials) => Promise<{ redirect_url?: string }>;\n  register: (data: RegisterData) => Promise<{ redirect_url?: string }>;\n  logout: () => Promise<void>;\n  updateProfile: (data: UpdateProfileData) => Promise<void>;\n  uploadAvatar: (file: File) => Promise<void>;\n  refreshUser: () => Promise<void>;\n  forgotPassword: (email: string) => Promise<{ message: string }>;\n  resetPassword: (data: {\n    token: string;\n    email: string;\n    password: string;\n    password_confirmation: string;\n  }) => Promise<{ message: string }>;\n  resendVerification: () => Promise<{ message: string }>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n\n  const initializeAuth = async () => {\n    try {\n      const storedUser = authService.getStoredUser();\n      const token = authService.getStoredToken();\n\n      if (storedUser && token) {\n        // Verify token is still valid by fetching current user\n        try {\n          const currentUser = await authService.getProfile();\n          setUser(currentUser);\n        } catch (error) {\n          // Token is invalid, clear stored data\n          authService.clearAuthData();\n          setUser(null);\n        }\n      }\n    } catch (error) {\n      console.error('Auth initialization error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (credentials: LoginCredentials) => {\n    setLoading(true);\n    try {\n      const response = await authService.login(credentials);\n      setUser(response.user);\n      return { redirect_url: response.redirect_url };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setLoading(true);\n    try {\n      const response = await authService.register(data);\n      setUser(response.user);\n      return { redirect_url: response.redirect_url };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setLoading(true);\n    try {\n      await authService.logout();\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateProfile = async (data: UpdateProfileData) => {\n    const updatedUser = await authService.updateProfile(data);\n    setUser(updatedUser);\n  };\n\n  const uploadAvatar = async (file: File) => {\n    const updatedUser = await authService.uploadAvatar(file);\n    setUser(updatedUser);\n  };\n\n  const refreshUser = async () => {\n    if (authService.isAuthenticated()) {\n      try {\n        const currentUser = await authService.getProfile();\n        setUser(currentUser);\n      } catch (error) {\n        console.error('Failed to refresh user:', error);\n        await logout();\n      }\n    }\n  };\n\n  const forgotPassword = async (email: string) => {\n    return authService.forgotPassword(email);\n  };\n\n  const resetPassword = async (data: {\n    token: string;\n    email: string;\n    password: string;\n    password_confirmation: string;\n  }) => {\n    return authService.resetPassword(data);\n  };\n\n  const resendVerification = async () => {\n    return authService.resendVerification();\n  };\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    isAuthenticated: !!user,\n    isEmailVerified: !!user?.email_verified_at,\n    login,\n    register,\n    logout,\n    updateProfile,\n    uploadAvatar,\n    refreshUser,\n    forgotPassword,\n    resetPassword,\n    resendVerification,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,KAAmB,OAAO,CACxF,MAAO,CAAAC,WAAW,KAAmE,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAuB/G,KAAM,CAAAC,WAAW,cAAGP,aAAa,CAA8BQ,SAAS,CAAC,CAEzE,MAAO,MAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGT,UAAU,CAACM,WAAW,CAAC,CACvC,GAAIG,OAAO,GAAKF,SAAS,CAAE,CACzB,KAAM,IAAI,CAAAG,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAMD,MAAO,MAAM,CAAAE,YAAyC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACpE,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAc,IAAI,CAAC,CACnD,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdiB,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF,KAAM,CAAAC,UAAU,CAAGhB,WAAW,CAACiB,aAAa,CAAC,CAAC,CAC9C,KAAM,CAAAC,KAAK,CAAGlB,WAAW,CAACmB,cAAc,CAAC,CAAC,CAE1C,GAAIH,UAAU,EAAIE,KAAK,CAAE,CACvB;AACA,GAAI,CACF,KAAM,CAAAE,WAAW,CAAG,KAAM,CAAApB,WAAW,CAACqB,UAAU,CAAC,CAAC,CAClDT,OAAO,CAACQ,WAAW,CAAC,CACtB,CAAE,MAAOE,KAAK,CAAE,CACd;AACAtB,WAAW,CAACuB,aAAa,CAAC,CAAC,CAC3BX,OAAO,CAAC,IAAI,CAAC,CACf,CACF,CACF,CAAE,MAAOU,KAAK,CAAE,CACdE,OAAO,CAACF,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACpD,CAAC,OAAS,CACRR,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAW,KAAK,CAAG,KAAO,CAAAC,WAA6B,EAAK,CACrDZ,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAa,QAAQ,CAAG,KAAM,CAAA3B,WAAW,CAACyB,KAAK,CAACC,WAAW,CAAC,CACrDd,OAAO,CAACe,QAAQ,CAAChB,IAAI,CAAC,CACtB,MAAO,CAAEiB,YAAY,CAAED,QAAQ,CAACC,YAAa,CAAC,CAChD,CAAC,OAAS,CACRd,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAe,QAAQ,CAAG,KAAO,CAAAC,IAAkB,EAAK,CAC7ChB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAa,QAAQ,CAAG,KAAM,CAAA3B,WAAW,CAAC6B,QAAQ,CAACC,IAAI,CAAC,CACjDlB,OAAO,CAACe,QAAQ,CAAChB,IAAI,CAAC,CACtB,MAAO,CAAEiB,YAAY,CAAED,QAAQ,CAACC,YAAa,CAAC,CAChD,CAAC,OAAS,CACRd,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAiB,MAAM,CAAG,KAAAA,CAAA,GAAY,CACzBjB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAd,WAAW,CAAC+B,MAAM,CAAC,CAAC,CAC1BnB,OAAO,CAAC,IAAI,CAAC,CACf,CAAC,OAAS,CACRE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAkB,aAAa,CAAG,KAAO,CAAAF,IAAuB,EAAK,CACvD,KAAM,CAAAG,WAAW,CAAG,KAAM,CAAAjC,WAAW,CAACgC,aAAa,CAACF,IAAI,CAAC,CACzDlB,OAAO,CAACqB,WAAW,CAAC,CACtB,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAC,IAAU,EAAK,CACzC,KAAM,CAAAF,WAAW,CAAG,KAAM,CAAAjC,WAAW,CAACkC,YAAY,CAACC,IAAI,CAAC,CACxDvB,OAAO,CAACqB,WAAW,CAAC,CACtB,CAAC,CAED,KAAM,CAAAG,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAIpC,WAAW,CAACqC,eAAe,CAAC,CAAC,CAAE,CACjC,GAAI,CACF,KAAM,CAAAjB,WAAW,CAAG,KAAM,CAAApB,WAAW,CAACqB,UAAU,CAAC,CAAC,CAClDT,OAAO,CAACQ,WAAW,CAAC,CACtB,CAAE,MAAOE,KAAK,CAAE,CACdE,OAAO,CAACF,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,CAAAS,MAAM,CAAC,CAAC,CAChB,CACF,CACF,CAAC,CAED,KAAM,CAAAO,cAAc,CAAG,KAAO,CAAAC,KAAa,EAAK,CAC9C,MAAO,CAAAvC,WAAW,CAACsC,cAAc,CAACC,KAAK,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG,KAAO,CAAAV,IAK5B,EAAK,CACJ,MAAO,CAAA9B,WAAW,CAACwC,aAAa,CAACV,IAAI,CAAC,CACxC,CAAC,CAED,KAAM,CAAAW,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,MAAO,CAAAzC,WAAW,CAACyC,kBAAkB,CAAC,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,KAAsB,CAAG,CAC7B/B,IAAI,CACJE,OAAO,CACPwB,eAAe,CAAE,CAAC,CAAC1B,IAAI,CACvBgC,eAAe,CAAE,CAAC,EAAChC,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEiC,iBAAiB,EAC1CnB,KAAK,CACLI,QAAQ,CACRE,MAAM,CACNC,aAAa,CACbE,YAAY,CACZE,WAAW,CACXE,cAAc,CACdE,aAAa,CACbC,kBACF,CAAC,CAED,mBAAOvC,IAAA,CAACC,WAAW,CAAC0C,QAAQ,EAACH,KAAK,CAAEA,KAAM,CAAAhC,QAAA,CAAEA,QAAQ,CAAuB,CAAC,CAC9E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}