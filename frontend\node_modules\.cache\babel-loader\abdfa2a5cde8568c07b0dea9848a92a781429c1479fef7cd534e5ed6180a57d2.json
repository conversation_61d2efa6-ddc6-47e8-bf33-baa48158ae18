{"ast": null, "code": "export { default } from \"./TimelineSeparator.js\";\nexport { default as timelineSeparatorClasses } from \"./timelineSeparatorClasses.js\";\nexport * from \"./timelineSeparatorClasses.js\";", "map": {"version": 3, "names": ["default", "timelineSeparatorClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineSeparator/index.js"], "sourcesContent": ["export { default } from \"./TimelineSeparator.js\";\nexport { default as timelineSeparatorClasses } from \"./timelineSeparatorClasses.js\";\nexport * from \"./timelineSeparatorClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASA,OAAO,IAAIC,wBAAwB,QAAQ,+BAA+B;AACnF,cAAc,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}