{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport CancelIcon from \"../internal/svg-icons/Cancel.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport unsupportedProp from \"../utils/unsupportedProp.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport chipClasses, { getChipUtilityClass } from \"./chipClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    size,\n    color,\n    iconColor,\n    onDelete,\n    clickable,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, disabled && 'disabled', `size${capitalize(size)}`, `color${capitalize(color)}`, clickable && 'clickable', clickable && `clickableColor${capitalize(color)}`, onDelete && 'deletable', onDelete && `deletableColor${capitalize(color)}`, `${variant}${capitalize(color)}`],\n    label: ['label', `label${capitalize(size)}`],\n    avatar: ['avatar', `avatar${capitalize(size)}`, `avatarColor${capitalize(color)}`],\n    icon: ['icon', `icon${capitalize(size)}`, `iconColor${capitalize(iconColor)}`],\n    deleteIcon: ['deleteIcon', `deleteIcon${capitalize(size)}`, `deleteIconColor${capitalize(color)}`, `deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getChipUtilityClass, classes);\n};\nconst ChipRoot = styled('div', {\n  name: 'MuiChip',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      color,\n      iconColor,\n      clickable,\n      onDelete,\n      size,\n      variant\n    } = ownerState;\n    return [{\n      [`& .${chipClasses.avatar}`]: styles.avatar\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatar${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatarColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles.icon\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`icon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`iconColor${capitalize(iconColor)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles.deleteIcon\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIconColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n    }, styles.root, styles[`size${capitalize(size)}`], styles[`color${capitalize(color)}`], clickable && styles.clickable, clickable && color !== 'default' && styles[`clickableColor${capitalize(color)})`], onDelete && styles.deletable, onDelete && color !== 'default' && styles[`deletableColor${capitalize(color)}`], styles[variant], styles[`${variant}${capitalize(color)}`]];\n  }\n})(memoTheme(_ref => {\n  let {\n    theme\n  } = _ref;\n  const textColor = theme.palette.mode === 'light' ? theme.palette.grey[700] : theme.palette.grey[300];\n  return {\n    maxWidth: '100%',\n    fontFamily: theme.typography.fontFamily,\n    fontSize: theme.typography.pxToRem(13),\n    display: 'inline-flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: 32,\n    lineHeight: 1.5,\n    color: (theme.vars || theme).palette.text.primary,\n    backgroundColor: (theme.vars || theme).palette.action.selected,\n    borderRadius: 32 / 2,\n    whiteSpace: 'nowrap',\n    transition: theme.transitions.create(['background-color', 'box-shadow']),\n    // reset cursor explicitly in case ButtonBase is used\n    cursor: 'unset',\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    textDecoration: 'none',\n    border: 0,\n    // Remove `button` border\n    padding: 0,\n    // Remove `button` padding\n    verticalAlign: 'middle',\n    boxSizing: 'border-box',\n    [`&.${chipClasses.disabled}`]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [`& .${chipClasses.avatar}`]: {\n      marginLeft: 5,\n      marginRight: -6,\n      width: 24,\n      height: 24,\n      color: theme.vars ? theme.vars.palette.Chip.defaultAvatarColor : textColor,\n      fontSize: theme.typography.pxToRem(12)\n    },\n    [`& .${chipClasses.avatarColorPrimary}`]: {\n      color: (theme.vars || theme).palette.primary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.primary.dark\n    },\n    [`& .${chipClasses.avatarColorSecondary}`]: {\n      color: (theme.vars || theme).palette.secondary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.secondary.dark\n    },\n    [`& .${chipClasses.avatarSmall}`]: {\n      marginLeft: 4,\n      marginRight: -4,\n      width: 18,\n      height: 18,\n      fontSize: theme.typography.pxToRem(10)\n    },\n    [`& .${chipClasses.icon}`]: {\n      marginLeft: 5,\n      marginRight: -6\n    },\n    [`& .${chipClasses.deleteIcon}`]: {\n      WebkitTapHighlightColor: 'transparent',\n      color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.26)` : alpha(theme.palette.text.primary, 0.26),\n      fontSize: 22,\n      cursor: 'pointer',\n      margin: '0 5px 0 -6px',\n      '&:hover': {\n        color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.4)` : alpha(theme.palette.text.primary, 0.4)\n      }\n    },\n    variants: [{\n      props: {\n        size: 'small'\n      },\n      style: {\n        height: 24,\n        [`& .${chipClasses.icon}`]: {\n          fontSize: 18,\n          marginLeft: 4,\n          marginRight: -4\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          fontSize: 16,\n          marginRight: 4,\n          marginLeft: -4\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['contrastText'])).map(_ref2 => {\n      let [color] = _ref2;\n      return {\n        props: {\n          color\n        },\n        style: {\n          backgroundColor: (theme.vars || theme).palette[color].main,\n          color: (theme.vars || theme).palette[color].contrastText,\n          [`& .${chipClasses.deleteIcon}`]: {\n            color: theme.vars ? `rgba(${theme.vars.palette[color].contrastTextChannel} / 0.7)` : alpha(theme.palette[color].contrastText, 0.7),\n            '&:hover, &:active': {\n              color: (theme.vars || theme).palette[color].contrastText\n            }\n          }\n        }\n      };\n    }), {\n      props: props => props.iconColor === props.color,\n      style: {\n        [`& .${chipClasses.icon}`]: {\n          color: theme.vars ? theme.vars.palette.Chip.defaultIconColor : textColor\n        }\n      }\n    }, {\n      props: props => props.iconColor === props.color && props.color !== 'default',\n      style: {\n        [`& .${chipClasses.icon}`]: {\n          color: 'inherit'\n        }\n      }\n    }, {\n      props: {\n        onDelete: true\n      },\n      style: {\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['dark'])).map(_ref3 => {\n      let [color] = _ref3;\n      return {\n        props: {\n          color,\n          onDelete: true\n        },\n        style: {\n          [`&.${chipClasses.focusVisible}`]: {\n            background: (theme.vars || theme).palette[color].dark\n          }\n        }\n      };\n    }), {\n      props: {\n        clickable: true\n      },\n      style: {\n        userSelect: 'none',\n        WebkitTapHighlightColor: 'transparent',\n        cursor: 'pointer',\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity)\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n        },\n        '&:active': {\n          boxShadow: (theme.vars || theme).shadows[1]\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['dark'])).map(_ref4 => {\n      let [color] = _ref4;\n      return {\n        props: {\n          color,\n          clickable: true\n        },\n        style: {\n          [`&:hover, &.${chipClasses.focusVisible}`]: {\n            backgroundColor: (theme.vars || theme).palette[color].dark\n          }\n        }\n      };\n    }), {\n      props: {\n        variant: 'outlined'\n      },\n      style: {\n        backgroundColor: 'transparent',\n        border: theme.vars ? `1px solid ${theme.vars.palette.Chip.defaultBorder}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[700]}`,\n        [`&.${chipClasses.clickable}:hover`]: {\n          backgroundColor: (theme.vars || theme).palette.action.hover\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: (theme.vars || theme).palette.action.focus\n        },\n        [`& .${chipClasses.avatar}`]: {\n          marginLeft: 4\n        },\n        [`& .${chipClasses.avatarSmall}`]: {\n          marginLeft: 2\n        },\n        [`& .${chipClasses.icon}`]: {\n          marginLeft: 4\n        },\n        [`& .${chipClasses.iconSmall}`]: {\n          marginLeft: 2\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          marginRight: 5\n        },\n        [`& .${chipClasses.deleteIconSmall}`]: {\n          marginRight: 3\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()) // no need to check for mainChannel as it's calculated from main\n    .map(_ref5 => {\n      let [color] = _ref5;\n      return {\n        props: {\n          variant: 'outlined',\n          color\n        },\n        style: {\n          color: (theme.vars || theme).palette[color].main,\n          border: `1px solid ${theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.7)` : alpha(theme.palette[color].main, 0.7)}`,\n          [`&.${chipClasses.clickable}:hover`]: {\n            backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity)\n          },\n          [`&.${chipClasses.focusVisible}`]: {\n            backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.focusOpacity})` : alpha(theme.palette[color].main, theme.palette.action.focusOpacity)\n          },\n          [`& .${chipClasses.deleteIcon}`]: {\n            color: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.7)` : alpha(theme.palette[color].main, 0.7),\n            '&:hover, &:active': {\n              color: (theme.vars || theme).palette[color].main\n            }\n          }\n        }\n      };\n    })]\n  };\n}));\nconst ChipLabel = styled('span', {\n  name: 'MuiChip',\n  slot: 'Label',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      size\n    } = ownerState;\n    return [styles.label, styles[`label${capitalize(size)}`]];\n  }\n})({\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  paddingLeft: 12,\n  paddingRight: 12,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      variant: 'outlined'\n    },\n    style: {\n      paddingLeft: 11,\n      paddingRight: 11\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      paddingLeft: 8,\n      paddingRight: 8\n    }\n  }, {\n    props: {\n      size: 'small',\n      variant: 'outlined'\n    },\n    style: {\n      paddingLeft: 7,\n      paddingRight: 7\n    }\n  }]\n});\nfunction isDeleteKeyboardEvent(keyboardEvent) {\n  return keyboardEvent.key === 'Backspace' || keyboardEvent.key === 'Delete';\n}\n\n/**\n * Chips represent complex entities in small blocks, such as a contact.\n */\nconst Chip = /*#__PURE__*/React.forwardRef(function Chip(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiChip'\n  });\n  const {\n    avatar: avatarProp,\n    className,\n    clickable: clickableProp,\n    color = 'default',\n    component: ComponentProp,\n    deleteIcon: deleteIconProp,\n    disabled = false,\n    icon: iconProp,\n    label,\n    onClick,\n    onDelete,\n    onKeyDown,\n    onKeyUp,\n    size = 'medium',\n    variant = 'filled',\n    tabIndex,\n    skipFocusWhenDisabled = false,\n    // TODO v6: Rename to `focusableWhenDisabled`.\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const chipRef = React.useRef(null);\n  const handleRef = useForkRef(chipRef, ref);\n  const handleDeleteIconClick = event => {\n    // Stop the event from bubbling up to the `Chip`\n    event.stopPropagation();\n    if (onDelete) {\n      onDelete(event);\n    }\n  };\n  const handleKeyDown = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target && isDeleteKeyboardEvent(event)) {\n      // Will be handled in keyUp, otherwise some browsers\n      // might init navigation\n      event.preventDefault();\n    }\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n  };\n  const handleKeyUp = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target) {\n      if (onDelete && isDeleteKeyboardEvent(event)) {\n        onDelete(event);\n      }\n    }\n    if (onKeyUp) {\n      onKeyUp(event);\n    }\n  };\n  const clickable = clickableProp !== false && onClick ? true : clickableProp;\n  const component = clickable || onDelete ? ButtonBase : ComponentProp || 'div';\n  const ownerState = {\n    ...props,\n    component,\n    disabled,\n    size,\n    color,\n    iconColor: /*#__PURE__*/React.isValidElement(iconProp) ? iconProp.props.color || color : color,\n    onDelete: !!onDelete,\n    clickable,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const moreProps = component === ButtonBase ? {\n    component: ComponentProp || 'div',\n    focusVisibleClassName: classes.focusVisible,\n    ...(onDelete && {\n      disableRipple: true\n    })\n  } : {};\n  let deleteIcon = null;\n  if (onDelete) {\n    deleteIcon = deleteIconProp && /*#__PURE__*/React.isValidElement(deleteIconProp) ? (/*#__PURE__*/React.cloneElement(deleteIconProp, {\n      className: clsx(deleteIconProp.props.className, classes.deleteIcon),\n      onClick: handleDeleteIconClick\n    })) : /*#__PURE__*/_jsx(CancelIcon, {\n      className: classes.deleteIcon,\n      onClick: handleDeleteIconClick\n    });\n  }\n  let avatar = null;\n  if (avatarProp && /*#__PURE__*/React.isValidElement(avatarProp)) {\n    avatar = /*#__PURE__*/React.cloneElement(avatarProp, {\n      className: clsx(classes.avatar, avatarProp.props.className)\n    });\n  }\n  let icon = null;\n  if (iconProp && /*#__PURE__*/React.isValidElement(iconProp)) {\n    icon = /*#__PURE__*/React.cloneElement(iconProp, {\n      className: clsx(classes.icon, iconProp.props.className)\n    });\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (avatar && icon) {\n      console.error('MUI: The Chip component can not handle the avatar ' + 'and the icon prop at the same time. Pick one.');\n    }\n  }\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: ChipRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    // The `component` prop is preserved because `Chip` relies on it for internal logic. If `shouldForwardComponentProp` were `false`, `useSlot` would remove the `component` prop, potentially breaking the component's behavior.\n    shouldForwardComponentProp: true,\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    additionalProps: {\n      disabled: clickable && disabled ? true : undefined,\n      tabIndex: skipFocusWhenDisabled && disabled ? -1 : tabIndex,\n      ...moreProps\n    },\n    getSlotProps: handlers => ({\n      ...handlers,\n      onClick: event => {\n        handlers.onClick?.(event);\n        onClick?.(event);\n      },\n      onKeyDown: event => {\n        handlers.onKeyDown?.(event);\n        handleKeyDown?.(event);\n      },\n      onKeyUp: event => {\n        handlers.onKeyUp?.(event);\n        handleKeyUp?.(event);\n      }\n    })\n  });\n  const [LabelSlot, labelProps] = useSlot('label', {\n    elementType: ChipLabel,\n    externalForwardedProps,\n    ownerState,\n    className: classes.label\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    as: component,\n    ...rootProps,\n    children: [avatar || icon, /*#__PURE__*/_jsx(LabelSlot, {\n      ...labelProps,\n      children: label\n    }), deleteIcon]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Chip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The Avatar element to display.\n   */\n  avatar: PropTypes.element,\n  /**\n   * This prop isn't supported.\n   * Use the `component` prop if you need to change the children structure.\n   */\n  children: unsupportedProp,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the chip will appear clickable, and will raise when pressed,\n   * even if the onClick prop is not defined.\n   * If `false`, the chip will not appear clickable, even if onClick prop is defined.\n   * This can be used, for example,\n   * along with the component prop to indicate an anchor Chip is clickable.\n   * Note: this controls the UI and does not affect the onClick event.\n   */\n  clickable: PropTypes.bool,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Override the default delete icon element. Shown only if `onDelete` is set.\n   */\n  deleteIcon: PropTypes.element,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Icon element.\n   */\n  icon: PropTypes.element,\n  /**\n   * The content of the component.\n   */\n  label: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * Callback fired when the delete icon is clicked.\n   * If set, the delete icon will be shown.\n   */\n  onDelete: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * If `true`, allows the disabled chip to escape focus.\n   * If `false`, allows the disabled chip to receive focus.\n   * @default false\n   */\n  skipFocusWhenDisabled: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    label: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'filled'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Chip;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "alpha", "CancelIcon", "useForkRef", "unsupportedProp", "capitalize", "ButtonBase", "styled", "memoTheme", "createSimplePaletteValueFilter", "useDefaultProps", "chipClasses", "getChipUtilityClass", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "disabled", "size", "color", "iconColor", "onDelete", "clickable", "variant", "slots", "root", "label", "avatar", "icon", "deleteIcon", "ChipRoot", "name", "slot", "overridesResolver", "props", "styles", "deletable", "_ref", "theme", "textColor", "palette", "mode", "grey", "max<PERSON><PERSON><PERSON>", "fontFamily", "typography", "fontSize", "pxToRem", "display", "alignItems", "justifyContent", "height", "lineHeight", "vars", "text", "primary", "backgroundColor", "action", "selected", "borderRadius", "whiteSpace", "transition", "transitions", "create", "cursor", "outline", "textDecoration", "border", "padding", "verticalAlign", "boxSizing", "opacity", "disabledOpacity", "pointerEvents", "marginLeft", "marginRight", "width", "Chip", "defaultAvatarColor", "avatarColorPrimary", "contrastText", "dark", "avatarColorSecondary", "secondary", "avatar<PERSON><PERSON><PERSON>", "WebkitTapHighlightColor", "primaryChannel", "margin", "variants", "style", "Object", "entries", "filter", "map", "_ref2", "main", "contrastTextChannel", "defaultIconColor", "focusVisible", "selectedChannel", "selectedOpacity", "focusOpacity", "_ref3", "background", "userSelect", "hoverOpacity", "boxShadow", "shadows", "_ref4", "defaultBorder", "hover", "focus", "iconSmall", "deleteIconSmall", "_ref5", "mainChannel", "ChipLabel", "overflow", "textOverflow", "paddingLeft", "paddingRight", "isDeleteKeyboardEvent", "keyboardEvent", "key", "forwardRef", "inProps", "ref", "avatarProp", "className", "clickableProp", "component", "ComponentProp", "deleteIconProp", "iconProp", "onClick", "onKeyDown", "onKeyUp", "tabIndex", "skipFocusWhenDisabled", "slotProps", "other", "chipRef", "useRef", "handleRef", "handleDeleteIconClick", "event", "stopPropagation", "handleKeyDown", "currentTarget", "target", "preventDefault", "handleKeyUp", "isValidElement", "moreProps", "focusVisibleClassName", "disable<PERSON><PERSON><PERSON>", "cloneElement", "process", "env", "NODE_ENV", "console", "error", "externalForwardedProps", "RootSlot", "rootProps", "elementType", "shouldForwardComponentProp", "additionalProps", "undefined", "getSlotProps", "handlers", "LabelSlot", "labelProps", "as", "children", "propTypes", "element", "object", "string", "bool", "oneOfType", "oneOf", "node", "func", "shape", "sx", "arrayOf", "number"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Chip/Chip.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport CancelIcon from \"../internal/svg-icons/Cancel.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport unsupportedProp from \"../utils/unsupportedProp.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport chipClasses, { getChipUtilityClass } from \"./chipClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    size,\n    color,\n    iconColor,\n    onDelete,\n    clickable,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, disabled && 'disabled', `size${capitalize(size)}`, `color${capitalize(color)}`, clickable && 'clickable', clickable && `clickableColor${capitalize(color)}`, onDelete && 'deletable', onDelete && `deletableColor${capitalize(color)}`, `${variant}${capitalize(color)}`],\n    label: ['label', `label${capitalize(size)}`],\n    avatar: ['avatar', `avatar${capitalize(size)}`, `avatarColor${capitalize(color)}`],\n    icon: ['icon', `icon${capitalize(size)}`, `iconColor${capitalize(iconColor)}`],\n    deleteIcon: ['deleteIcon', `deleteIcon${capitalize(size)}`, `deleteIconColor${capitalize(color)}`, `deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getChipUtilityClass, classes);\n};\nconst ChipRoot = styled('div', {\n  name: 'MuiChip',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      color,\n      iconColor,\n      clickable,\n      onDelete,\n      size,\n      variant\n    } = ownerState;\n    return [{\n      [`& .${chipClasses.avatar}`]: styles.avatar\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatar${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatarColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles.icon\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`icon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`iconColor${capitalize(iconColor)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles.deleteIcon\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIconColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n    }, styles.root, styles[`size${capitalize(size)}`], styles[`color${capitalize(color)}`], clickable && styles.clickable, clickable && color !== 'default' && styles[`clickableColor${capitalize(color)})`], onDelete && styles.deletable, onDelete && color !== 'default' && styles[`deletableColor${capitalize(color)}`], styles[variant], styles[`${variant}${capitalize(color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => {\n  const textColor = theme.palette.mode === 'light' ? theme.palette.grey[700] : theme.palette.grey[300];\n  return {\n    maxWidth: '100%',\n    fontFamily: theme.typography.fontFamily,\n    fontSize: theme.typography.pxToRem(13),\n    display: 'inline-flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: 32,\n    lineHeight: 1.5,\n    color: (theme.vars || theme).palette.text.primary,\n    backgroundColor: (theme.vars || theme).palette.action.selected,\n    borderRadius: 32 / 2,\n    whiteSpace: 'nowrap',\n    transition: theme.transitions.create(['background-color', 'box-shadow']),\n    // reset cursor explicitly in case ButtonBase is used\n    cursor: 'unset',\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    textDecoration: 'none',\n    border: 0,\n    // Remove `button` border\n    padding: 0,\n    // Remove `button` padding\n    verticalAlign: 'middle',\n    boxSizing: 'border-box',\n    [`&.${chipClasses.disabled}`]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [`& .${chipClasses.avatar}`]: {\n      marginLeft: 5,\n      marginRight: -6,\n      width: 24,\n      height: 24,\n      color: theme.vars ? theme.vars.palette.Chip.defaultAvatarColor : textColor,\n      fontSize: theme.typography.pxToRem(12)\n    },\n    [`& .${chipClasses.avatarColorPrimary}`]: {\n      color: (theme.vars || theme).palette.primary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.primary.dark\n    },\n    [`& .${chipClasses.avatarColorSecondary}`]: {\n      color: (theme.vars || theme).palette.secondary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.secondary.dark\n    },\n    [`& .${chipClasses.avatarSmall}`]: {\n      marginLeft: 4,\n      marginRight: -4,\n      width: 18,\n      height: 18,\n      fontSize: theme.typography.pxToRem(10)\n    },\n    [`& .${chipClasses.icon}`]: {\n      marginLeft: 5,\n      marginRight: -6\n    },\n    [`& .${chipClasses.deleteIcon}`]: {\n      WebkitTapHighlightColor: 'transparent',\n      color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.26)` : alpha(theme.palette.text.primary, 0.26),\n      fontSize: 22,\n      cursor: 'pointer',\n      margin: '0 5px 0 -6px',\n      '&:hover': {\n        color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.4)` : alpha(theme.palette.text.primary, 0.4)\n      }\n    },\n    variants: [{\n      props: {\n        size: 'small'\n      },\n      style: {\n        height: 24,\n        [`& .${chipClasses.icon}`]: {\n          fontSize: 18,\n          marginLeft: 4,\n          marginRight: -4\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          fontSize: 16,\n          marginRight: 4,\n          marginLeft: -4\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['contrastText'])).map(([color]) => {\n      return {\n        props: {\n          color\n        },\n        style: {\n          backgroundColor: (theme.vars || theme).palette[color].main,\n          color: (theme.vars || theme).palette[color].contrastText,\n          [`& .${chipClasses.deleteIcon}`]: {\n            color: theme.vars ? `rgba(${theme.vars.palette[color].contrastTextChannel} / 0.7)` : alpha(theme.palette[color].contrastText, 0.7),\n            '&:hover, &:active': {\n              color: (theme.vars || theme).palette[color].contrastText\n            }\n          }\n        }\n      };\n    }), {\n      props: props => props.iconColor === props.color,\n      style: {\n        [`& .${chipClasses.icon}`]: {\n          color: theme.vars ? theme.vars.palette.Chip.defaultIconColor : textColor\n        }\n      }\n    }, {\n      props: props => props.iconColor === props.color && props.color !== 'default',\n      style: {\n        [`& .${chipClasses.icon}`]: {\n          color: 'inherit'\n        }\n      }\n    }, {\n      props: {\n        onDelete: true\n      },\n      style: {\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['dark'])).map(([color]) => {\n      return {\n        props: {\n          color,\n          onDelete: true\n        },\n        style: {\n          [`&.${chipClasses.focusVisible}`]: {\n            background: (theme.vars || theme).palette[color].dark\n          }\n        }\n      };\n    }), {\n      props: {\n        clickable: true\n      },\n      style: {\n        userSelect: 'none',\n        WebkitTapHighlightColor: 'transparent',\n        cursor: 'pointer',\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity)\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n        },\n        '&:active': {\n          boxShadow: (theme.vars || theme).shadows[1]\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['dark'])).map(([color]) => ({\n      props: {\n        color,\n        clickable: true\n      },\n      style: {\n        [`&:hover, &.${chipClasses.focusVisible}`]: {\n          backgroundColor: (theme.vars || theme).palette[color].dark\n        }\n      }\n    })), {\n      props: {\n        variant: 'outlined'\n      },\n      style: {\n        backgroundColor: 'transparent',\n        border: theme.vars ? `1px solid ${theme.vars.palette.Chip.defaultBorder}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[700]}`,\n        [`&.${chipClasses.clickable}:hover`]: {\n          backgroundColor: (theme.vars || theme).palette.action.hover\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: (theme.vars || theme).palette.action.focus\n        },\n        [`& .${chipClasses.avatar}`]: {\n          marginLeft: 4\n        },\n        [`& .${chipClasses.avatarSmall}`]: {\n          marginLeft: 2\n        },\n        [`& .${chipClasses.icon}`]: {\n          marginLeft: 4\n        },\n        [`& .${chipClasses.iconSmall}`]: {\n          marginLeft: 2\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          marginRight: 5\n        },\n        [`& .${chipClasses.deleteIconSmall}`]: {\n          marginRight: 3\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()) // no need to check for mainChannel as it's calculated from main\n    .map(([color]) => ({\n      props: {\n        variant: 'outlined',\n        color\n      },\n      style: {\n        color: (theme.vars || theme).palette[color].main,\n        border: `1px solid ${theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.7)` : alpha(theme.palette[color].main, 0.7)}`,\n        [`&.${chipClasses.clickable}:hover`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity)\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.focusOpacity})` : alpha(theme.palette[color].main, theme.palette.action.focusOpacity)\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          color: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.7)` : alpha(theme.palette[color].main, 0.7),\n          '&:hover, &:active': {\n            color: (theme.vars || theme).palette[color].main\n          }\n        }\n      }\n    }))]\n  };\n}));\nconst ChipLabel = styled('span', {\n  name: 'MuiChip',\n  slot: 'Label',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      size\n    } = ownerState;\n    return [styles.label, styles[`label${capitalize(size)}`]];\n  }\n})({\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  paddingLeft: 12,\n  paddingRight: 12,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      variant: 'outlined'\n    },\n    style: {\n      paddingLeft: 11,\n      paddingRight: 11\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      paddingLeft: 8,\n      paddingRight: 8\n    }\n  }, {\n    props: {\n      size: 'small',\n      variant: 'outlined'\n    },\n    style: {\n      paddingLeft: 7,\n      paddingRight: 7\n    }\n  }]\n});\nfunction isDeleteKeyboardEvent(keyboardEvent) {\n  return keyboardEvent.key === 'Backspace' || keyboardEvent.key === 'Delete';\n}\n\n/**\n * Chips represent complex entities in small blocks, such as a contact.\n */\nconst Chip = /*#__PURE__*/React.forwardRef(function Chip(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiChip'\n  });\n  const {\n    avatar: avatarProp,\n    className,\n    clickable: clickableProp,\n    color = 'default',\n    component: ComponentProp,\n    deleteIcon: deleteIconProp,\n    disabled = false,\n    icon: iconProp,\n    label,\n    onClick,\n    onDelete,\n    onKeyDown,\n    onKeyUp,\n    size = 'medium',\n    variant = 'filled',\n    tabIndex,\n    skipFocusWhenDisabled = false,\n    // TODO v6: Rename to `focusableWhenDisabled`.\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const chipRef = React.useRef(null);\n  const handleRef = useForkRef(chipRef, ref);\n  const handleDeleteIconClick = event => {\n    // Stop the event from bubbling up to the `Chip`\n    event.stopPropagation();\n    if (onDelete) {\n      onDelete(event);\n    }\n  };\n  const handleKeyDown = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target && isDeleteKeyboardEvent(event)) {\n      // Will be handled in keyUp, otherwise some browsers\n      // might init navigation\n      event.preventDefault();\n    }\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n  };\n  const handleKeyUp = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target) {\n      if (onDelete && isDeleteKeyboardEvent(event)) {\n        onDelete(event);\n      }\n    }\n    if (onKeyUp) {\n      onKeyUp(event);\n    }\n  };\n  const clickable = clickableProp !== false && onClick ? true : clickableProp;\n  const component = clickable || onDelete ? ButtonBase : ComponentProp || 'div';\n  const ownerState = {\n    ...props,\n    component,\n    disabled,\n    size,\n    color,\n    iconColor: /*#__PURE__*/React.isValidElement(iconProp) ? iconProp.props.color || color : color,\n    onDelete: !!onDelete,\n    clickable,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const moreProps = component === ButtonBase ? {\n    component: ComponentProp || 'div',\n    focusVisibleClassName: classes.focusVisible,\n    ...(onDelete && {\n      disableRipple: true\n    })\n  } : {};\n  let deleteIcon = null;\n  if (onDelete) {\n    deleteIcon = deleteIconProp && /*#__PURE__*/React.isValidElement(deleteIconProp) ? (/*#__PURE__*/React.cloneElement(deleteIconProp, {\n      className: clsx(deleteIconProp.props.className, classes.deleteIcon),\n      onClick: handleDeleteIconClick\n    })) : /*#__PURE__*/_jsx(CancelIcon, {\n      className: classes.deleteIcon,\n      onClick: handleDeleteIconClick\n    });\n  }\n  let avatar = null;\n  if (avatarProp && /*#__PURE__*/React.isValidElement(avatarProp)) {\n    avatar = /*#__PURE__*/React.cloneElement(avatarProp, {\n      className: clsx(classes.avatar, avatarProp.props.className)\n    });\n  }\n  let icon = null;\n  if (iconProp && /*#__PURE__*/React.isValidElement(iconProp)) {\n    icon = /*#__PURE__*/React.cloneElement(iconProp, {\n      className: clsx(classes.icon, iconProp.props.className)\n    });\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (avatar && icon) {\n      console.error('MUI: The Chip component can not handle the avatar ' + 'and the icon prop at the same time. Pick one.');\n    }\n  }\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: ChipRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    // The `component` prop is preserved because `Chip` relies on it for internal logic. If `shouldForwardComponentProp` were `false`, `useSlot` would remove the `component` prop, potentially breaking the component's behavior.\n    shouldForwardComponentProp: true,\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    additionalProps: {\n      disabled: clickable && disabled ? true : undefined,\n      tabIndex: skipFocusWhenDisabled && disabled ? -1 : tabIndex,\n      ...moreProps\n    },\n    getSlotProps: handlers => ({\n      ...handlers,\n      onClick: event => {\n        handlers.onClick?.(event);\n        onClick?.(event);\n      },\n      onKeyDown: event => {\n        handlers.onKeyDown?.(event);\n        handleKeyDown?.(event);\n      },\n      onKeyUp: event => {\n        handlers.onKeyUp?.(event);\n        handleKeyUp?.(event);\n      }\n    })\n  });\n  const [LabelSlot, labelProps] = useSlot('label', {\n    elementType: ChipLabel,\n    externalForwardedProps,\n    ownerState,\n    className: classes.label\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    as: component,\n    ...rootProps,\n    children: [avatar || icon, /*#__PURE__*/_jsx(LabelSlot, {\n      ...labelProps,\n      children: label\n    }), deleteIcon]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Chip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The Avatar element to display.\n   */\n  avatar: PropTypes.element,\n  /**\n   * This prop isn't supported.\n   * Use the `component` prop if you need to change the children structure.\n   */\n  children: unsupportedProp,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the chip will appear clickable, and will raise when pressed,\n   * even if the onClick prop is not defined.\n   * If `false`, the chip will not appear clickable, even if onClick prop is defined.\n   * This can be used, for example,\n   * along with the component prop to indicate an anchor Chip is clickable.\n   * Note: this controls the UI and does not affect the onClick event.\n   */\n  clickable: PropTypes.bool,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Override the default delete icon element. Shown only if `onDelete` is set.\n   */\n  deleteIcon: PropTypes.element,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Icon element.\n   */\n  icon: PropTypes.element,\n  /**\n   * The content of the component.\n   */\n  label: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * Callback fired when the delete icon is clicked.\n   * If set, the delete icon will be shown.\n   */\n  onDelete: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * If `true`, allows the disabled chip to escape focus.\n   * If `false`, allows the disabled chip to receive focus.\n   * @default false\n   */\n  skipFocusWhenDisabled: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    label: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'filled'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Chip;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,8BAA8B,MAAM,4CAA4C;AACvF,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,WAAW,IAAIC,mBAAmB,QAAQ,kBAAkB;AACnE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,SAAS;IACTC,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,OAAO,EAAEN,QAAQ,IAAI,UAAU,EAAE,OAAOhB,UAAU,CAACiB,IAAI,CAAC,EAAE,EAAE,QAAQjB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAEG,SAAS,IAAI,WAAW,EAAEA,SAAS,IAAI,iBAAiBrB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAEE,QAAQ,IAAI,WAAW,EAAEA,QAAQ,IAAI,iBAAiBpB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAE,GAAGI,OAAO,GAAGtB,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC;IACjSO,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQzB,UAAU,CAACiB,IAAI,CAAC,EAAE,CAAC;IAC5CS,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS1B,UAAU,CAACiB,IAAI,CAAC,EAAE,EAAE,cAAcjB,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC;IAClFS,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO3B,UAAU,CAACiB,IAAI,CAAC,EAAE,EAAE,YAAYjB,UAAU,CAACmB,SAAS,CAAC,EAAE,CAAC;IAC9ES,UAAU,EAAE,CAAC,YAAY,EAAE,aAAa5B,UAAU,CAACiB,IAAI,CAAC,EAAE,EAAE,kBAAkBjB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAE,aAAalB,UAAU,CAACsB,OAAO,CAAC,QAAQtB,UAAU,CAACkB,KAAK,CAAC,EAAE;EAChK,CAAC;EACD,OAAOvB,cAAc,CAAC4B,KAAK,EAAEhB,mBAAmB,EAAEQ,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMc,QAAQ,GAAG3B,MAAM,CAAC,KAAK,EAAE;EAC7B4B,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJpB;IACF,CAAC,GAAGmB,KAAK;IACT,MAAM;MACJf,KAAK;MACLC,SAAS;MACTE,SAAS;MACTD,QAAQ;MACRH,IAAI;MACJK;IACF,CAAC,GAAGR,UAAU;IACd,OAAO,CAAC;MACN,CAAC,MAAMR,WAAW,CAACoB,MAAM,EAAE,GAAGQ,MAAM,CAACR;IACvC,CAAC,EAAE;MACD,CAAC,MAAMpB,WAAW,CAACoB,MAAM,EAAE,GAAGQ,MAAM,CAAC,SAASlC,UAAU,CAACiB,IAAI,CAAC,EAAE;IAClE,CAAC,EAAE;MACD,CAAC,MAAMX,WAAW,CAACoB,MAAM,EAAE,GAAGQ,MAAM,CAAC,cAAclC,UAAU,CAACkB,KAAK,CAAC,EAAE;IACxE,CAAC,EAAE;MACD,CAAC,MAAMZ,WAAW,CAACqB,IAAI,EAAE,GAAGO,MAAM,CAACP;IACrC,CAAC,EAAE;MACD,CAAC,MAAMrB,WAAW,CAACqB,IAAI,EAAE,GAAGO,MAAM,CAAC,OAAOlC,UAAU,CAACiB,IAAI,CAAC,EAAE;IAC9D,CAAC,EAAE;MACD,CAAC,MAAMX,WAAW,CAACqB,IAAI,EAAE,GAAGO,MAAM,CAAC,YAAYlC,UAAU,CAACmB,SAAS,CAAC,EAAE;IACxE,CAAC,EAAE;MACD,CAAC,MAAMb,WAAW,CAACsB,UAAU,EAAE,GAAGM,MAAM,CAACN;IAC3C,CAAC,EAAE;MACD,CAAC,MAAMtB,WAAW,CAACsB,UAAU,EAAE,GAAGM,MAAM,CAAC,aAAalC,UAAU,CAACiB,IAAI,CAAC,EAAE;IAC1E,CAAC,EAAE;MACD,CAAC,MAAMX,WAAW,CAACsB,UAAU,EAAE,GAAGM,MAAM,CAAC,kBAAkBlC,UAAU,CAACkB,KAAK,CAAC,EAAE;IAChF,CAAC,EAAE;MACD,CAAC,MAAMZ,WAAW,CAACsB,UAAU,EAAE,GAAGM,MAAM,CAAC,aAAalC,UAAU,CAACsB,OAAO,CAAC,QAAQtB,UAAU,CAACkB,KAAK,CAAC,EAAE;IACtG,CAAC,EAAEgB,MAAM,CAACV,IAAI,EAAEU,MAAM,CAAC,OAAOlC,UAAU,CAACiB,IAAI,CAAC,EAAE,CAAC,EAAEiB,MAAM,CAAC,QAAQlC,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC,EAAEG,SAAS,IAAIa,MAAM,CAACb,SAAS,EAAEA,SAAS,IAAIH,KAAK,KAAK,SAAS,IAAIgB,MAAM,CAAC,iBAAiBlC,UAAU,CAACkB,KAAK,CAAC,GAAG,CAAC,EAAEE,QAAQ,IAAIc,MAAM,CAACC,SAAS,EAAEf,QAAQ,IAAIF,KAAK,KAAK,SAAS,IAAIgB,MAAM,CAAC,iBAAiBlC,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC,EAAEgB,MAAM,CAACZ,OAAO,CAAC,EAAEY,MAAM,CAAC,GAAGZ,OAAO,GAAGtB,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC,CAAC;EACrX;AACF,CAAC,CAAC,CAACf,SAAS,CAACiC,IAAA,IAEP;EAAA,IAFQ;IACZC;EACF,CAAC,GAAAD,IAAA;EACC,MAAME,SAAS,GAAGD,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAGH,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGJ,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EACpG,OAAO;IACLC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAEN,KAAK,CAACO,UAAU,CAACD,UAAU;IACvCE,QAAQ,EAAER,KAAK,CAACO,UAAU,CAACE,OAAO,CAAC,EAAE,CAAC;IACtCC,OAAO,EAAE,aAAa;IACtBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,GAAG;IACfjC,KAAK,EAAE,CAACmB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACc,IAAI,CAACC,OAAO;IACjDC,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACiB,MAAM,CAACC,QAAQ;IAC9DC,YAAY,EAAE,EAAE,GAAG,CAAC;IACpBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAEvB,KAAK,CAACwB,WAAW,CAACC,MAAM,CAAC,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;IACxE;IACAC,MAAM,EAAE,OAAO;IACf;IACAC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,MAAM;IACtBC,MAAM,EAAE,CAAC;IACT;IACAC,OAAO,EAAE,CAAC;IACV;IACAC,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAE,YAAY;IACvB,CAAC,KAAK/D,WAAW,CAACU,QAAQ,EAAE,GAAG;MAC7BsD,OAAO,EAAE,CAACjC,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACiB,MAAM,CAACe,eAAe;MAC7DC,aAAa,EAAE;IACjB,CAAC;IACD,CAAC,MAAMlE,WAAW,CAACoB,MAAM,EAAE,GAAG;MAC5B+C,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,CAAC;MACfC,KAAK,EAAE,EAAE;MACTzB,MAAM,EAAE,EAAE;MACVhC,KAAK,EAAEmB,KAAK,CAACe,IAAI,GAAGf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACqC,IAAI,CAACC,kBAAkB,GAAGvC,SAAS;MAC1EO,QAAQ,EAAER,KAAK,CAACO,UAAU,CAACE,OAAO,CAAC,EAAE;IACvC,CAAC;IACD,CAAC,MAAMxC,WAAW,CAACwE,kBAAkB,EAAE,GAAG;MACxC5D,KAAK,EAAE,CAACmB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACe,OAAO,CAACyB,YAAY;MACzDxB,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACe,OAAO,CAAC0B;IACzD,CAAC;IACD,CAAC,MAAM1E,WAAW,CAAC2E,oBAAoB,EAAE,GAAG;MAC1C/D,KAAK,EAAE,CAACmB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAAC2C,SAAS,CAACH,YAAY;MAC3DxB,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAAC2C,SAAS,CAACF;IAC3D,CAAC;IACD,CAAC,MAAM1E,WAAW,CAAC6E,WAAW,EAAE,GAAG;MACjCV,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,CAAC;MACfC,KAAK,EAAE,EAAE;MACTzB,MAAM,EAAE,EAAE;MACVL,QAAQ,EAAER,KAAK,CAACO,UAAU,CAACE,OAAO,CAAC,EAAE;IACvC,CAAC;IACD,CAAC,MAAMxC,WAAW,CAACqB,IAAI,EAAE,GAAG;MAC1B8C,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;IAChB,CAAC;IACD,CAAC,MAAMpE,WAAW,CAACsB,UAAU,EAAE,GAAG;MAChCwD,uBAAuB,EAAE,aAAa;MACtClE,KAAK,EAAEmB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACc,IAAI,CAACgC,cAAc,UAAU,GAAGzF,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACc,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC;MACtHT,QAAQ,EAAE,EAAE;MACZkB,MAAM,EAAE,SAAS;MACjBuB,MAAM,EAAE,cAAc;MACtB,SAAS,EAAE;QACTpE,KAAK,EAAEmB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACc,IAAI,CAACgC,cAAc,SAAS,GAAGzF,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACc,IAAI,CAACC,OAAO,EAAE,GAAG;MACrH;IACF,CAAC;IACDiC,QAAQ,EAAE,CAAC;MACTtD,KAAK,EAAE;QACLhB,IAAI,EAAE;MACR,CAAC;MACDuE,KAAK,EAAE;QACLtC,MAAM,EAAE,EAAE;QACV,CAAC,MAAM5C,WAAW,CAACqB,IAAI,EAAE,GAAG;UAC1BkB,QAAQ,EAAE,EAAE;UACZ4B,UAAU,EAAE,CAAC;UACbC,WAAW,EAAE,CAAC;QAChB,CAAC;QACD,CAAC,MAAMpE,WAAW,CAACsB,UAAU,EAAE,GAAG;UAChCiB,QAAQ,EAAE,EAAE;UACZ6B,WAAW,EAAE,CAAC;UACdD,UAAU,EAAE,CAAC;QACf;MACF;IACF,CAAC,EAAE,GAAGgB,MAAM,CAACC,OAAO,CAACrD,KAAK,CAACE,OAAO,CAAC,CAACoD,MAAM,CAACvF,8BAA8B,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAACwF,GAAG,CAACC,KAAA,IAAa;MAAA,IAAZ,CAAC3E,KAAK,CAAC,GAAA2E,KAAA;MACvG,OAAO;QACL5D,KAAK,EAAE;UACLf;QACF,CAAC;QACDsE,KAAK,EAAE;UACLjC,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACrB,KAAK,CAAC,CAAC4E,IAAI;UAC1D5E,KAAK,EAAE,CAACmB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACrB,KAAK,CAAC,CAAC6D,YAAY;UACxD,CAAC,MAAMzE,WAAW,CAACsB,UAAU,EAAE,GAAG;YAChCV,KAAK,EAAEmB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACrB,KAAK,CAAC,CAAC6E,mBAAmB,SAAS,GAAGnG,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACrB,KAAK,CAAC,CAAC6D,YAAY,EAAE,GAAG,CAAC;YAClI,mBAAmB,EAAE;cACnB7D,KAAK,EAAE,CAACmB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACrB,KAAK,CAAC,CAAC6D;YAC9C;UACF;QACF;MACF,CAAC;IACH,CAAC,CAAC,EAAE;MACF9C,KAAK,EAAEA,KAAK,IAAIA,KAAK,CAACd,SAAS,KAAKc,KAAK,CAACf,KAAK;MAC/CsE,KAAK,EAAE;QACL,CAAC,MAAMlF,WAAW,CAACqB,IAAI,EAAE,GAAG;UAC1BT,KAAK,EAAEmB,KAAK,CAACe,IAAI,GAAGf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACqC,IAAI,CAACoB,gBAAgB,GAAG1D;QACjE;MACF;IACF,CAAC,EAAE;MACDL,KAAK,EAAEA,KAAK,IAAIA,KAAK,CAACd,SAAS,KAAKc,KAAK,CAACf,KAAK,IAAIe,KAAK,CAACf,KAAK,KAAK,SAAS;MAC5EsE,KAAK,EAAE;QACL,CAAC,MAAMlF,WAAW,CAACqB,IAAI,EAAE,GAAG;UAC1BT,KAAK,EAAE;QACT;MACF;IACF,CAAC,EAAE;MACDe,KAAK,EAAE;QACLb,QAAQ,EAAE;MACZ,CAAC;MACDoE,KAAK,EAAE;QACL,CAAC,KAAKlF,WAAW,CAAC2F,YAAY,EAAE,GAAG;UACjC1C,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC0C,eAAe,WAAW7D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC2C,eAAe,MAAM9D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC4C,YAAY,IAAI,GAAGxG,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACiB,MAAM,CAACC,QAAQ,EAAEpB,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC2C,eAAe,GAAG9D,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC4C,YAAY;QACrS;MACF;IACF,CAAC,EAAE,GAAGX,MAAM,CAACC,OAAO,CAACrD,KAAK,CAACE,OAAO,CAAC,CAACoD,MAAM,CAACvF,8BAA8B,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAACwF,GAAG,CAACS,KAAA,IAAa;MAAA,IAAZ,CAACnF,KAAK,CAAC,GAAAmF,KAAA;MAC/F,OAAO;QACLpE,KAAK,EAAE;UACLf,KAAK;UACLE,QAAQ,EAAE;QACZ,CAAC;QACDoE,KAAK,EAAE;UACL,CAAC,KAAKlF,WAAW,CAAC2F,YAAY,EAAE,GAAG;YACjCK,UAAU,EAAE,CAACjE,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACrB,KAAK,CAAC,CAAC8D;UACnD;QACF;MACF,CAAC;IACH,CAAC,CAAC,EAAE;MACF/C,KAAK,EAAE;QACLZ,SAAS,EAAE;MACb,CAAC;MACDmE,KAAK,EAAE;QACLe,UAAU,EAAE,MAAM;QAClBnB,uBAAuB,EAAE,aAAa;QACtCrB,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE;UACTR,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC0C,eAAe,WAAW7D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC2C,eAAe,MAAM9D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAACgD,YAAY,IAAI,GAAG5G,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACiB,MAAM,CAACC,QAAQ,EAAEpB,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC2C,eAAe,GAAG9D,KAAK,CAACE,OAAO,CAACiB,MAAM,CAACgD,YAAY;QACrS,CAAC;QACD,CAAC,KAAKlG,WAAW,CAAC2F,YAAY,EAAE,GAAG;UACjC1C,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC0C,eAAe,WAAW7D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC2C,eAAe,MAAM9D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC4C,YAAY,IAAI,GAAGxG,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACiB,MAAM,CAACC,QAAQ,EAAEpB,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC2C,eAAe,GAAG9D,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC4C,YAAY;QACrS,CAAC;QACD,UAAU,EAAE;UACVK,SAAS,EAAE,CAACpE,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEqE,OAAO,CAAC,CAAC;QAC5C;MACF;IACF,CAAC,EAAE,GAAGjB,MAAM,CAACC,OAAO,CAACrD,KAAK,CAACE,OAAO,CAAC,CAACoD,MAAM,CAACvF,8BAA8B,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAACwF,GAAG,CAACe,KAAA;MAAA,IAAC,CAACzF,KAAK,CAAC,GAAAyF,KAAA;MAAA,OAAM;QACrG1E,KAAK,EAAE;UACLf,KAAK;UACLG,SAAS,EAAE;QACb,CAAC;QACDmE,KAAK,EAAE;UACL,CAAC,cAAclF,WAAW,CAAC2F,YAAY,EAAE,GAAG;YAC1C1C,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACrB,KAAK,CAAC,CAAC8D;UACxD;QACF;MACF,CAAC;IAAA,CAAC,CAAC,EAAE;MACH/C,KAAK,EAAE;QACLX,OAAO,EAAE;MACX,CAAC;MACDkE,KAAK,EAAE;QACLjC,eAAe,EAAE,aAAa;QAC9BW,MAAM,EAAE7B,KAAK,CAACe,IAAI,GAAG,aAAaf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACqC,IAAI,CAACgC,aAAa,EAAE,GAAG,aAAavE,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAGH,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGJ,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,EAAE;QAC7K,CAAC,KAAKnC,WAAW,CAACe,SAAS,QAAQ,GAAG;UACpCkC,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACiB,MAAM,CAACqD;QACxD,CAAC;QACD,CAAC,KAAKvG,WAAW,CAAC2F,YAAY,EAAE,GAAG;UACjC1C,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACiB,MAAM,CAACsD;QACxD,CAAC;QACD,CAAC,MAAMxG,WAAW,CAACoB,MAAM,EAAE,GAAG;UAC5B+C,UAAU,EAAE;QACd,CAAC;QACD,CAAC,MAAMnE,WAAW,CAAC6E,WAAW,EAAE,GAAG;UACjCV,UAAU,EAAE;QACd,CAAC;QACD,CAAC,MAAMnE,WAAW,CAACqB,IAAI,EAAE,GAAG;UAC1B8C,UAAU,EAAE;QACd,CAAC;QACD,CAAC,MAAMnE,WAAW,CAACyG,SAAS,EAAE,GAAG;UAC/BtC,UAAU,EAAE;QACd,CAAC;QACD,CAAC,MAAMnE,WAAW,CAACsB,UAAU,EAAE,GAAG;UAChC8C,WAAW,EAAE;QACf,CAAC;QACD,CAAC,MAAMpE,WAAW,CAAC0G,eAAe,EAAE,GAAG;UACrCtC,WAAW,EAAE;QACf;MACF;IACF,CAAC,EAAE,GAAGe,MAAM,CAACC,OAAO,CAACrD,KAAK,CAACE,OAAO,CAAC,CAACoD,MAAM,CAACvF,8BAA8B,CAAC,CAAC,CAAC,CAAC;IAAA,CAC5EwF,GAAG,CAACqB,KAAA;MAAA,IAAC,CAAC/F,KAAK,CAAC,GAAA+F,KAAA;MAAA,OAAM;QACjBhF,KAAK,EAAE;UACLX,OAAO,EAAE,UAAU;UACnBJ;QACF,CAAC;QACDsE,KAAK,EAAE;UACLtE,KAAK,EAAE,CAACmB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACrB,KAAK,CAAC,CAAC4E,IAAI;UAChD5B,MAAM,EAAE,aAAa7B,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACrB,KAAK,CAAC,CAACgG,WAAW,SAAS,GAAGtH,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACrB,KAAK,CAAC,CAAC4E,IAAI,EAAE,GAAG,CAAC,EAAE;UAClI,CAAC,KAAKxF,WAAW,CAACe,SAAS,QAAQ,GAAG;YACpCkC,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACrB,KAAK,CAAC,CAACgG,WAAW,MAAM7E,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAACgD,YAAY,GAAG,GAAG5G,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACrB,KAAK,CAAC,CAAC4E,IAAI,EAAEzD,KAAK,CAACE,OAAO,CAACiB,MAAM,CAACgD,YAAY;UACjM,CAAC;UACD,CAAC,KAAKlG,WAAW,CAAC2F,YAAY,EAAE,GAAG;YACjC1C,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACrB,KAAK,CAAC,CAACgG,WAAW,MAAM7E,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC4C,YAAY,GAAG,GAAGxG,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACrB,KAAK,CAAC,CAAC4E,IAAI,EAAEzD,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC4C,YAAY;UACjM,CAAC;UACD,CAAC,MAAM9F,WAAW,CAACsB,UAAU,EAAE,GAAG;YAChCV,KAAK,EAAEmB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACrB,KAAK,CAAC,CAACgG,WAAW,SAAS,GAAGtH,KAAK,CAACyC,KAAK,CAACE,OAAO,CAACrB,KAAK,CAAC,CAAC4E,IAAI,EAAE,GAAG,CAAC;YAClH,mBAAmB,EAAE;cACnB5E,KAAK,EAAE,CAACmB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACrB,KAAK,CAAC,CAAC4E;YAC9C;UACF;QACF;MACF,CAAC;IAAA,CAAC,CAAC;EACL,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMqB,SAAS,GAAGjH,MAAM,CAAC,MAAM,EAAE;EAC/B4B,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJpB;IACF,CAAC,GAAGmB,KAAK;IACT,MAAM;MACJhB;IACF,CAAC,GAAGH,UAAU;IACd,OAAO,CAACoB,MAAM,CAACT,KAAK,EAAES,MAAM,CAAC,QAAQlC,UAAU,CAACiB,IAAI,CAAC,EAAE,CAAC,CAAC;EAC3D;AACF,CAAC,CAAC,CAAC;EACDmG,QAAQ,EAAE,QAAQ;EAClBC,YAAY,EAAE,UAAU;EACxBC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE,EAAE;EAChB5D,UAAU,EAAE,QAAQ;EACpB4B,QAAQ,EAAE,CAAC;IACTtD,KAAK,EAAE;MACLX,OAAO,EAAE;IACX,CAAC;IACDkE,KAAK,EAAE;MACL8B,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE;IAChB;EACF,CAAC,EAAE;IACDtF,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACDuE,KAAK,EAAE;MACL8B,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB;EACF,CAAC,EAAE;IACDtF,KAAK,EAAE;MACLhB,IAAI,EAAE,OAAO;MACbK,OAAO,EAAE;IACX,CAAC;IACDkE,KAAK,EAAE;MACL8B,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB;EACF,CAAC;AACH,CAAC,CAAC;AACF,SAASC,qBAAqBA,CAACC,aAAa,EAAE;EAC5C,OAAOA,aAAa,CAACC,GAAG,KAAK,WAAW,IAAID,aAAa,CAACC,GAAG,KAAK,QAAQ;AAC5E;;AAEA;AACA;AACA;AACA,MAAM9C,IAAI,GAAG,aAAapF,KAAK,CAACmI,UAAU,CAAC,SAAS/C,IAAIA,CAACgD,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAM5F,KAAK,GAAG5B,eAAe,CAAC;IAC5B4B,KAAK,EAAE2F,OAAO;IACd9F,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJJ,MAAM,EAAEoG,UAAU;IAClBC,SAAS;IACT1G,SAAS,EAAE2G,aAAa;IACxB9G,KAAK,GAAG,SAAS;IACjB+G,SAAS,EAAEC,aAAa;IACxBtG,UAAU,EAAEuG,cAAc;IAC1BnH,QAAQ,GAAG,KAAK;IAChBW,IAAI,EAAEyG,QAAQ;IACd3G,KAAK;IACL4G,OAAO;IACPjH,QAAQ;IACRkH,SAAS;IACTC,OAAO;IACPtH,IAAI,GAAG,QAAQ;IACfK,OAAO,GAAG,QAAQ;IAClBkH,QAAQ;IACRC,qBAAqB,GAAG,KAAK;IAC7B;IACAlH,KAAK,GAAG,CAAC,CAAC;IACVmH,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAG1G,KAAK;EACT,MAAM2G,OAAO,GAAGpJ,KAAK,CAACqJ,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,SAAS,GAAGhJ,UAAU,CAAC8I,OAAO,EAAEf,GAAG,CAAC;EAC1C,MAAMkB,qBAAqB,GAAGC,KAAK,IAAI;IACrC;IACAA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI7H,QAAQ,EAAE;MACZA,QAAQ,CAAC4H,KAAK,CAAC;IACjB;EACF,CAAC;EACD,MAAME,aAAa,GAAGF,KAAK,IAAI;IAC7B;IACA,IAAIA,KAAK,CAACG,aAAa,KAAKH,KAAK,CAACI,MAAM,IAAI5B,qBAAqB,CAACwB,KAAK,CAAC,EAAE;MACxE;MACA;MACAA,KAAK,CAACK,cAAc,CAAC,CAAC;IACxB;IACA,IAAIf,SAAS,EAAE;MACbA,SAAS,CAACU,KAAK,CAAC;IAClB;EACF,CAAC;EACD,MAAMM,WAAW,GAAGN,KAAK,IAAI;IAC3B;IACA,IAAIA,KAAK,CAACG,aAAa,KAAKH,KAAK,CAACI,MAAM,EAAE;MACxC,IAAIhI,QAAQ,IAAIoG,qBAAqB,CAACwB,KAAK,CAAC,EAAE;QAC5C5H,QAAQ,CAAC4H,KAAK,CAAC;MACjB;IACF;IACA,IAAIT,OAAO,EAAE;MACXA,OAAO,CAACS,KAAK,CAAC;IAChB;EACF,CAAC;EACD,MAAM3H,SAAS,GAAG2G,aAAa,KAAK,KAAK,IAAIK,OAAO,GAAG,IAAI,GAAGL,aAAa;EAC3E,MAAMC,SAAS,GAAG5G,SAAS,IAAID,QAAQ,GAAGnB,UAAU,GAAGiI,aAAa,IAAI,KAAK;EAC7E,MAAMpH,UAAU,GAAG;IACjB,GAAGmB,KAAK;IACRgG,SAAS;IACTjH,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,SAAS,EAAE,aAAa3B,KAAK,CAAC+J,cAAc,CAACnB,QAAQ,CAAC,GAAGA,QAAQ,CAACnG,KAAK,CAACf,KAAK,IAAIA,KAAK,GAAGA,KAAK;IAC9FE,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBC,SAAS;IACTC;EACF,CAAC;EACD,MAAMP,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM0I,SAAS,GAAGvB,SAAS,KAAKhI,UAAU,GAAG;IAC3CgI,SAAS,EAAEC,aAAa,IAAI,KAAK;IACjCuB,qBAAqB,EAAE1I,OAAO,CAACkF,YAAY;IAC3C,IAAI7E,QAAQ,IAAI;MACdsI,aAAa,EAAE;IACjB,CAAC;EACH,CAAC,GAAG,CAAC,CAAC;EACN,IAAI9H,UAAU,GAAG,IAAI;EACrB,IAAIR,QAAQ,EAAE;IACZQ,UAAU,GAAGuG,cAAc,IAAI,aAAa3I,KAAK,CAAC+J,cAAc,CAACpB,cAAc,CAAC,IAAI,aAAa3I,KAAK,CAACmK,YAAY,CAACxB,cAAc,EAAE;MAClIJ,SAAS,EAAErI,IAAI,CAACyI,cAAc,CAAClG,KAAK,CAAC8F,SAAS,EAAEhH,OAAO,CAACa,UAAU,CAAC;MACnEyG,OAAO,EAAEU;IACX,CAAC,CAAC,IAAI,aAAarI,IAAI,CAACb,UAAU,EAAE;MAClCkI,SAAS,EAAEhH,OAAO,CAACa,UAAU;MAC7ByG,OAAO,EAAEU;IACX,CAAC,CAAC;EACJ;EACA,IAAIrH,MAAM,GAAG,IAAI;EACjB,IAAIoG,UAAU,IAAI,aAAatI,KAAK,CAAC+J,cAAc,CAACzB,UAAU,CAAC,EAAE;IAC/DpG,MAAM,GAAG,aAAalC,KAAK,CAACmK,YAAY,CAAC7B,UAAU,EAAE;MACnDC,SAAS,EAAErI,IAAI,CAACqB,OAAO,CAACW,MAAM,EAAEoG,UAAU,CAAC7F,KAAK,CAAC8F,SAAS;IAC5D,CAAC,CAAC;EACJ;EACA,IAAIpG,IAAI,GAAG,IAAI;EACf,IAAIyG,QAAQ,IAAI,aAAa5I,KAAK,CAAC+J,cAAc,CAACnB,QAAQ,CAAC,EAAE;IAC3DzG,IAAI,GAAG,aAAanC,KAAK,CAACmK,YAAY,CAACvB,QAAQ,EAAE;MAC/CL,SAAS,EAAErI,IAAI,CAACqB,OAAO,CAACY,IAAI,EAAEyG,QAAQ,CAACnG,KAAK,CAAC8F,SAAS;IACxD,CAAC,CAAC;EACJ;EACA,IAAI6B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIpI,MAAM,IAAIC,IAAI,EAAE;MAClBoI,OAAO,CAACC,KAAK,CAAC,oDAAoD,GAAG,+CAA+C,CAAC;IACvH;EACF;EACA,MAAMC,sBAAsB,GAAG;IAC7B1I,KAAK;IACLmH;EACF,CAAC;EACD,MAAM,CAACwB,QAAQ,EAAEC,SAAS,CAAC,GAAG3J,OAAO,CAAC,MAAM,EAAE;IAC5C4J,WAAW,EAAEvI,QAAQ;IACrBoI,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAGtB;IACL,CAAC;IACD7H,UAAU;IACV;IACAuJ,0BAA0B,EAAE,IAAI;IAChCxC,GAAG,EAAEiB,SAAS;IACdf,SAAS,EAAErI,IAAI,CAACqB,OAAO,CAACS,IAAI,EAAEuG,SAAS,CAAC;IACxCuC,eAAe,EAAE;MACftJ,QAAQ,EAAEK,SAAS,IAAIL,QAAQ,GAAG,IAAI,GAAGuJ,SAAS;MAClD/B,QAAQ,EAAEC,qBAAqB,IAAIzH,QAAQ,GAAG,CAAC,CAAC,GAAGwH,QAAQ;MAC3D,GAAGgB;IACL,CAAC;IACDgB,YAAY,EAAEC,QAAQ,KAAK;MACzB,GAAGA,QAAQ;MACXpC,OAAO,EAAEW,KAAK,IAAI;QAChByB,QAAQ,CAACpC,OAAO,GAAGW,KAAK,CAAC;QACzBX,OAAO,GAAGW,KAAK,CAAC;MAClB,CAAC;MACDV,SAAS,EAAEU,KAAK,IAAI;QAClByB,QAAQ,CAACnC,SAAS,GAAGU,KAAK,CAAC;QAC3BE,aAAa,GAAGF,KAAK,CAAC;MACxB,CAAC;MACDT,OAAO,EAAES,KAAK,IAAI;QAChByB,QAAQ,CAAClC,OAAO,GAAGS,KAAK,CAAC;QACzBM,WAAW,GAAGN,KAAK,CAAC;MACtB;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAM,CAAC0B,SAAS,EAAEC,UAAU,CAAC,GAAGnK,OAAO,CAAC,OAAO,EAAE;IAC/C4J,WAAW,EAAEjD,SAAS;IACtB8C,sBAAsB;IACtBnJ,UAAU;IACViH,SAAS,EAAEhH,OAAO,CAACU;EACrB,CAAC,CAAC;EACF,OAAO,aAAab,KAAK,CAACsJ,QAAQ,EAAE;IAClCU,EAAE,EAAE3C,SAAS;IACb,GAAGkC,SAAS;IACZU,QAAQ,EAAE,CAACnJ,MAAM,IAAIC,IAAI,EAAE,aAAajB,IAAI,CAACgK,SAAS,EAAE;MACtD,GAAGC,UAAU;MACbE,QAAQ,EAAEpJ;IACZ,CAAC,CAAC,EAAEG,UAAU;EAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AACFgI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlF,IAAI,CAACkG,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACEpJ,MAAM,EAAEjC,SAAS,CAACsL,OAAO;EACzB;AACF;AACA;AACA;EACEF,QAAQ,EAAE9K,eAAe;EACzB;AACF;AACA;EACEgB,OAAO,EAAEtB,SAAS,CAACuL,MAAM;EACzB;AACF;AACA;EACEjD,SAAS,EAAEtI,SAAS,CAACwL,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE5J,SAAS,EAAE5B,SAAS,CAACyL,IAAI;EACzB;AACF;AACA;AACA;AACA;AACA;EACEhK,KAAK,EAAEzB,SAAS,CAAC,sCAAsC0L,SAAS,CAAC,CAAC1L,SAAS,CAAC2L,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE3L,SAAS,CAACwL,MAAM,CAAC,CAAC;EACjL;AACF;AACA;AACA;EACEhD,SAAS,EAAExI,SAAS,CAAC2K,WAAW;EAChC;AACF;AACA;EACExI,UAAU,EAAEnC,SAAS,CAACsL,OAAO;EAC7B;AACF;AACA;AACA;EACE/J,QAAQ,EAAEvB,SAAS,CAACyL,IAAI;EACxB;AACF;AACA;EACEvJ,IAAI,EAAElC,SAAS,CAACsL,OAAO;EACvB;AACF;AACA;EACEtJ,KAAK,EAAEhC,SAAS,CAAC4L,IAAI;EACrB;AACF;AACA;EACEhD,OAAO,EAAE5I,SAAS,CAAC6L,IAAI;EACvB;AACF;AACA;AACA;EACElK,QAAQ,EAAE3B,SAAS,CAAC6L,IAAI;EACxB;AACF;AACA;EACEhD,SAAS,EAAE7I,SAAS,CAAC6L,IAAI;EACzB;AACF;AACA;EACE/C,OAAO,EAAE9I,SAAS,CAAC6L,IAAI;EACvB;AACF;AACA;AACA;EACErK,IAAI,EAAExB,SAAS,CAAC,sCAAsC0L,SAAS,CAAC,CAAC1L,SAAS,CAAC2L,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE3L,SAAS,CAACwL,MAAM,CAAC,CAAC;EACzH;AACF;AACA;AACA;AACA;EACExC,qBAAqB,EAAEhJ,SAAS,CAACyL,IAAI;EACrC;AACF;AACA;AACA;EACExC,SAAS,EAAEjJ,SAAS,CAAC8L,KAAK,CAAC;IACzB9J,KAAK,EAAEhC,SAAS,CAAC0L,SAAS,CAAC,CAAC1L,SAAS,CAAC6L,IAAI,EAAE7L,SAAS,CAACuL,MAAM,CAAC,CAAC;IAC9DxJ,IAAI,EAAE/B,SAAS,CAAC0L,SAAS,CAAC,CAAC1L,SAAS,CAAC6L,IAAI,EAAE7L,SAAS,CAACuL,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEzJ,KAAK,EAAE9B,SAAS,CAAC8L,KAAK,CAAC;IACrB9J,KAAK,EAAEhC,SAAS,CAAC2K,WAAW;IAC5B5I,IAAI,EAAE/B,SAAS,CAAC2K;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEoB,EAAE,EAAE/L,SAAS,CAAC0L,SAAS,CAAC,CAAC1L,SAAS,CAACgM,OAAO,CAAChM,SAAS,CAAC0L,SAAS,CAAC,CAAC1L,SAAS,CAAC6L,IAAI,EAAE7L,SAAS,CAACuL,MAAM,EAAEvL,SAAS,CAACyL,IAAI,CAAC,CAAC,CAAC,EAAEzL,SAAS,CAAC6L,IAAI,EAAE7L,SAAS,CAACuL,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACExC,QAAQ,EAAE/I,SAAS,CAACiM,MAAM;EAC1B;AACF;AACA;AACA;EACEpK,OAAO,EAAE7B,SAAS,CAAC,sCAAsC0L,SAAS,CAAC,CAAC1L,SAAS,CAAC2L,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE3L,SAAS,CAACwL,MAAM,CAAC;AAChI,CAAC,GAAG,KAAK,CAAC;AACV,eAAerG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}