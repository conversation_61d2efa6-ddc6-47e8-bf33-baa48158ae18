{"ast": null, "code": "import React,{useEffect}from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{Container}from'react-bootstrap';import{HelmetProvider}from'react-helmet-async';import{AuthProvider}from'./contexts/AuthContext';import{SettingsProvider}from'./contexts/SettingsContext';import{initializeErrorSuppression}from'./utils/errorHandlers';import Navbar from'./components/layout/Navbar';import DynamicHead from'./components/common/DynamicHead';import CmsHomepage from'./components/cms/CmsHomepage';import DynamicCmsPage from'./components/cms/DynamicCmsPage';import DynamicRouteHandler from'./components/routing/DynamicRouteHandler';import NotFound from'./pages/NotFound';import Login from'./pages/auth/Login';import Register from'./pages/auth/Register';import ForgotPassword from'./pages/auth/ForgotPassword';import ResetPassword from'./pages/auth/ResetPassword';import Profile from'./pages/user/Profile';import EditProfile from'./pages/user/EditProfile';import ProtectedRoute from'./components/auth/ProtectedRoute';import EmailVerificationNotice from'./components/auth/EmailVerificationNotice';// Material Dashboard imports\nimport DashboardRoute from'./components/dashboard/DashboardRoute';import Dashboard from'./pages/dashboard/Dashboard';import Wallet from'./pages/dashboard/Wallet';import Order from'./pages/dashboard/Order';import Orders from'./pages/dashboard/Orders';import PuckEditor from'./components/puck/PuckEditor';import'bootstrap/dist/css/bootstrap.min.css';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){// Initialize global error suppression for ResizeObserver errors\nuseEffect(()=>{const cleanup=initializeErrorSuppression();return cleanup;},[]);return/*#__PURE__*/_jsx(HelmetProvider,{children:/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsxs(SettingsProvider,{children:[/*#__PURE__*/_jsx(DynamicHead,{}),/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(DashboardRoute,{children:/*#__PURE__*/_jsx(Dashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard/wallet\",element:/*#__PURE__*/_jsx(DashboardRoute,{children:/*#__PURE__*/_jsx(Wallet,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard/credit\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard/wallet\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard/order\",element:/*#__PURE__*/_jsx(DashboardRoute,{children:/*#__PURE__*/_jsx(Order,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard/orders\",element:/*#__PURE__*/_jsx(DashboardRoute,{children:/*#__PURE__*/_jsx(Orders,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/*\",element:/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsx(Container,{className:\"mt-4\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(CmsHomepage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(Register,{})}),/*#__PURE__*/_jsx(Route,{path:\"/forgot-password\",element:/*#__PURE__*/_jsx(ForgotPassword,{})}),/*#__PURE__*/_jsx(Route,{path:\"/reset-password\",element:/*#__PURE__*/_jsx(ResetPassword,{})}),/*#__PURE__*/_jsx(Route,{path:\"/pages/:slug\",element:/*#__PURE__*/_jsx(DynamicCmsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/edit/:pageId\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(PuckEditor,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Profile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/profile/edit\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(EditProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/email-verification\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(EmailVerificationNotice,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/404\",element:/*#__PURE__*/_jsx(NotFound,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(DynamicRouteHandler,{})})]})})]})})]})})]})})});}export default App;", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Container", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initializeErrorSuppression", "<PERSON><PERSON><PERSON>", "DynamicHead", "CmsHomepage", "DynamicCmsPage", "DynamicRouteHandler", "NotFound", "<PERSON><PERSON>", "Register", "ForgotPassword", "ResetPassword", "Profile", "EditProfile", "ProtectedRoute", "EmailVerificationNotice", "DashboardRoute", "Dashboard", "Wallet", "Order", "Orders", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "App", "cleanup", "children", "path", "element", "to", "replace", "className"], "sources": ["C:/laragon/www/frontend/src/App.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Container } from 'react-bootstrap';\nimport { HelmetProvider } from 'react-helmet-async';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { SettingsProvider } from './contexts/SettingsContext';\nimport { initializeErrorSuppression } from './utils/errorHandlers';\nimport Navbar from './components/layout/Navbar';\nimport DynamicHead from './components/common/DynamicHead';\nimport CmsHomepage from './components/cms/CmsHomepage';\nimport DynamicCmsPage from './components/cms/DynamicCmsPage';\nimport DynamicRouteHandler from './components/routing/DynamicRouteHandler';\nimport NotFound from './pages/NotFound';\nimport Login from './pages/auth/Login';\nimport Register from './pages/auth/Register';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport Profile from './pages/user/Profile';\nimport EditProfile from './pages/user/EditProfile';\nimport PageView from './pages/cms/PageView';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\nimport EmailVerificationNotice from './components/auth/EmailVerificationNotice';\n// Material Dashboard imports\nimport DashboardRoute from './components/dashboard/DashboardRoute';\nimport Dashboard from './pages/dashboard/Dashboard';\nimport Wallet from './pages/dashboard/Wallet';\nimport Order from './pages/dashboard/Order';\nimport Orders from './pages/dashboard/Orders';\nimport PuckEditor from './components/puck/PuckEditor';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\n\nfunction App() {\n  // Initialize global error suppression for ResizeObserver errors\n  useEffect(() => {\n    const cleanup = initializeErrorSuppression();\n    return cleanup;\n  }, []);\n\n  return (\n    <HelmetProvider>\n      <AuthProvider>\n        <SettingsProvider>\n          <DynamicHead />\n          <Router>\n        <Routes>\n          {/* Dashboard routes - these don't use the main layout */}\n          <Route\n            path=\"/dashboard\"\n            element={\n              <DashboardRoute>\n                <Dashboard />\n              </DashboardRoute>\n            }\n          />\n          <Route\n            path=\"/dashboard/wallet\"\n            element={\n              <DashboardRoute>\n                <Wallet />\n              </DashboardRoute>\n            }\n          />\n          {/* Redirect old credit route to new wallet route */}\n          <Route\n            path=\"/dashboard/credit\"\n            element={<Navigate to=\"/dashboard/wallet\" replace />}\n          />\n\n          <Route\n            path=\"/dashboard/order\"\n            element={\n              <DashboardRoute>\n                <Order />\n              </DashboardRoute>\n            }\n          />\n          <Route\n            path=\"/dashboard/orders\"\n            element={\n              <DashboardRoute>\n                <Orders />\n              </DashboardRoute>\n            }\n          />\n\n\n\n          {/* Main application routes with Bootstrap layout */}\n          <Route\n            path=\"/*\"\n            element={\n              <div className=\"App\">\n                <Navbar />\n                <Container className=\"mt-4\">\n                  <Routes>\n                    {/* Public routes */}\n                    <Route path=\"/\" element={<CmsHomepage />} />\n                    <Route path=\"/login\" element={<Login />} />\n                    <Route path=\"/register\" element={<Register />} />\n                    <Route path=\"/forgot-password\" element={<ForgotPassword />} />\n                    <Route path=\"/reset-password\" element={<ResetPassword />} />\n                    <Route path=\"/pages/:slug\" element={<DynamicCmsPage />} />\n\n                    {/* Puck Editor Route - Protected for admin users */}\n                    <Route\n                      path=\"/edit/:pageId\"\n                      element={\n                        <ProtectedRoute>\n                          <PuckEditor />\n                        </ProtectedRoute>\n                      }\n                    />\n\n                    {/* Protected routes */}\n                    <Route\n                      path=\"/profile\"\n                      element={\n                        <ProtectedRoute>\n                          <Profile />\n                        </ProtectedRoute>\n                      }\n                    />\n                    <Route\n                      path=\"/profile/edit\"\n                      element={\n                        <ProtectedRoute>\n                          <EditProfile />\n                        </ProtectedRoute>\n                      }\n                    />\n\n                    {/* Email verification notice */}\n                    <Route\n                      path=\"/email-verification\"\n                      element={\n                        <ProtectedRoute>\n                          <EmailVerificationNotice />\n                        </ProtectedRoute>\n                      }\n                    />\n\n                    {/* 404 Page */}\n                    <Route path=\"/404\" element={<NotFound />} />\n\n                    {/* Dynamic CMS pages - catch all route */}\n                    <Route path=\"*\" element={<DynamicRouteHandler />} />\n                  </Routes>\n                </Container>\n              </div>\n            }\n          />\n        </Routes>\n        </Router>\n        </SettingsProvider>\n      </AuthProvider>\n    </HelmetProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,SAAS,KAAQ,iBAAiB,CAC3C,OAASC,cAAc,KAAQ,oBAAoB,CACnD,OAASC,YAAY,KAAQ,wBAAwB,CACrD,OAASC,gBAAgB,KAAQ,4BAA4B,CAC7D,OAASC,0BAA0B,KAAQ,uBAAuB,CAClE,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,WAAW,KAAM,iCAAiC,CACzD,MAAO,CAAAC,WAAW,KAAM,8BAA8B,CACtD,MAAO,CAAAC,cAAc,KAAM,iCAAiC,CAC5D,MAAO,CAAAC,mBAAmB,KAAM,0CAA0C,CAC1E,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,KAAK,KAAM,oBAAoB,CACtC,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAC1C,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAElD,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,uBAAuB,KAAM,2CAA2C,CAC/E;AACA,MAAO,CAAAC,cAAc,KAAM,uCAAuC,CAClE,MAAO,CAAAC,SAAS,KAAM,6BAA6B,CACnD,MAAO,CAAAC,MAAM,KAAM,0BAA0B,CAC7C,MAAO,CAAAC,KAAK,KAAM,yBAAyB,CAC3C,MAAO,CAAAC,MAAM,KAAM,0BAA0B,CAC7C,MAAO,CAAAC,UAAU,KAAM,8BAA8B,CACrD,MAAO,sCAAsC,CAC7C,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb;AACAnC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAoC,OAAO,CAAG1B,0BAA0B,CAAC,CAAC,CAC5C,MAAO,CAAA0B,OAAO,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEJ,IAAA,CAACzB,cAAc,EAAA8B,QAAA,cACbL,IAAA,CAACxB,YAAY,EAAA6B,QAAA,cACXH,KAAA,CAACzB,gBAAgB,EAAA4B,QAAA,eACfL,IAAA,CAACpB,WAAW,GAAE,CAAC,cACfoB,IAAA,CAAC9B,MAAM,EAAAmC,QAAA,cACTH,KAAA,CAAC/B,MAAM,EAAAkC,QAAA,eAELL,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,YAAY,CACjBC,OAAO,cACLP,IAAA,CAACP,cAAc,EAAAY,QAAA,cACbL,IAAA,CAACN,SAAS,GAAE,CAAC,CACC,CACjB,CACF,CAAC,cACFM,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,mBAAmB,CACxBC,OAAO,cACLP,IAAA,CAACP,cAAc,EAAAY,QAAA,cACbL,IAAA,CAACL,MAAM,GAAE,CAAC,CACI,CACjB,CACF,CAAC,cAEFK,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,mBAAmB,CACxBC,OAAO,cAAEP,IAAA,CAAC3B,QAAQ,EAACmC,EAAE,CAAC,mBAAmB,CAACC,OAAO,MAAE,CAAE,CACtD,CAAC,cAEFT,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACLP,IAAA,CAACP,cAAc,EAAAY,QAAA,cACbL,IAAA,CAACJ,KAAK,GAAE,CAAC,CACK,CACjB,CACF,CAAC,cACFI,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,mBAAmB,CACxBC,OAAO,cACLP,IAAA,CAACP,cAAc,EAAAY,QAAA,cACbL,IAAA,CAACH,MAAM,GAAE,CAAC,CACI,CACjB,CACF,CAAC,cAKFG,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,IAAI,CACTC,OAAO,cACLL,KAAA,QAAKQ,SAAS,CAAC,KAAK,CAAAL,QAAA,eAClBL,IAAA,CAACrB,MAAM,GAAE,CAAC,cACVqB,IAAA,CAAC1B,SAAS,EAACoC,SAAS,CAAC,MAAM,CAAAL,QAAA,cACzBH,KAAA,CAAC/B,MAAM,EAAAkC,QAAA,eAELL,IAAA,CAAC5B,KAAK,EAACkC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACnB,WAAW,GAAE,CAAE,CAAE,CAAC,cAC5CmB,IAAA,CAAC5B,KAAK,EAACkC,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEP,IAAA,CAACf,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3Ce,IAAA,CAAC5B,KAAK,EAACkC,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEP,IAAA,CAACd,QAAQ,GAAE,CAAE,CAAE,CAAC,cACjDc,IAAA,CAAC5B,KAAK,EAACkC,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEP,IAAA,CAACb,cAAc,GAAE,CAAE,CAAE,CAAC,cAC9Da,IAAA,CAAC5B,KAAK,EAACkC,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEP,IAAA,CAACZ,aAAa,GAAE,CAAE,CAAE,CAAC,cAC5DY,IAAA,CAAC5B,KAAK,EAACkC,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEP,IAAA,CAAClB,cAAc,GAAE,CAAE,CAAE,CAAC,cAG1DkB,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,eAAe,CACpBC,OAAO,cACLP,IAAA,CAACT,cAAc,EAAAc,QAAA,cACbL,IAAA,CAACF,UAAU,GAAE,CAAC,CACA,CACjB,CACF,CAAC,cAGFE,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,UAAU,CACfC,OAAO,cACLP,IAAA,CAACT,cAAc,EAAAc,QAAA,cACbL,IAAA,CAACX,OAAO,GAAE,CAAC,CACG,CACjB,CACF,CAAC,cACFW,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,eAAe,CACpBC,OAAO,cACLP,IAAA,CAACT,cAAc,EAAAc,QAAA,cACbL,IAAA,CAACV,WAAW,GAAE,CAAC,CACD,CACjB,CACF,CAAC,cAGFU,IAAA,CAAC5B,KAAK,EACJkC,IAAI,CAAC,qBAAqB,CAC1BC,OAAO,cACLP,IAAA,CAACT,cAAc,EAAAc,QAAA,cACbL,IAAA,CAACR,uBAAuB,GAAE,CAAC,CACb,CACjB,CACF,CAAC,cAGFQ,IAAA,CAAC5B,KAAK,EAACkC,IAAI,CAAC,MAAM,CAACC,OAAO,cAAEP,IAAA,CAAChB,QAAQ,GAAE,CAAE,CAAE,CAAC,cAG5CgB,IAAA,CAAC5B,KAAK,EAACkC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACjB,mBAAmB,GAAE,CAAE,CAAE,CAAC,EAC9C,CAAC,CACA,CAAC,EACT,CACN,CACF,CAAC,EACI,CAAC,CACD,CAAC,EACS,CAAC,CACP,CAAC,CACD,CAAC,CAErB,CAEA,cAAe,CAAAoB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}