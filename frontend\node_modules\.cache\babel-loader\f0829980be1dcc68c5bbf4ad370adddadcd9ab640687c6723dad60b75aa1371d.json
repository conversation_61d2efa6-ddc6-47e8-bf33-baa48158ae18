{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useDropdownMenu } from '@restart/ui/DropdownMenu';\nimport useIsomorphicEffect from '@restart/hooks/useIsomorphicEffect';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport warning from 'warning';\nimport DropdownContext from './DropdownContext';\nimport InputGroupContext from './InputGroupContext';\nimport NavbarContext from './NavbarContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport useWrappedRefWithWarning from './useWrappedRefWithWarning';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getDropdownMenuPlacement(alignEnd, dropDirection, isRTL) {\n  const topStart = isRTL ? 'top-end' : 'top-start';\n  const topEnd = isRTL ? 'top-start' : 'top-end';\n  const bottomStart = isRTL ? 'bottom-end' : 'bottom-start';\n  const bottomEnd = isRTL ? 'bottom-start' : 'bottom-end';\n  const leftStart = isRTL ? 'right-start' : 'left-start';\n  const leftEnd = isRTL ? 'right-end' : 'left-end';\n  const rightStart = isRTL ? 'left-start' : 'right-start';\n  const rightEnd = isRTL ? 'left-end' : 'right-end';\n  let placement = alignEnd ? bottomEnd : bottomStart;\n  if (dropDirection === 'up') placement = alignEnd ? topEnd : topStart;else if (dropDirection === 'end') placement = alignEnd ? rightEnd : rightStart;else if (dropDirection === 'start') placement = alignEnd ? leftEnd : leftStart;else if (dropDirection === 'down-centered') placement = 'bottom';else if (dropDirection === 'up-centered') placement = 'top';\n  return placement;\n}\nconst DropdownMenu = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    align,\n    rootCloseEvent,\n    flip = true,\n    show: showProps,\n    renderOnMount,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    popperConfig,\n    variant,\n    ...props\n  } = _ref;\n  let alignEnd = false;\n  const isNavbar = useContext(NavbarContext);\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-menu');\n  const {\n    align: contextAlign,\n    drop,\n    isRTL\n  } = useContext(DropdownContext);\n  align = align || contextAlign;\n  const isInputGroup = useContext(InputGroupContext);\n  const alignClasses = [];\n  if (align) {\n    if (typeof align === 'object') {\n      const keys = Object.keys(align);\n      process.env.NODE_ENV !== \"production\" ? warning(keys.length === 1, 'There should only be 1 breakpoint when passing an object to `align`') : void 0;\n      if (keys.length) {\n        const brkPoint = keys[0];\n        const direction = align[brkPoint];\n\n        // .dropdown-menu-end is required for responsively aligning\n        // left in addition to align left classes.\n        alignEnd = direction === 'start';\n        alignClasses.push(`${prefix}-${brkPoint}-${direction}`);\n      }\n    } else if (align === 'end') {\n      alignEnd = true;\n    }\n  }\n  const placement = getDropdownMenuPlacement(alignEnd, drop, isRTL);\n  const [menuProps, {\n    hasShown,\n    popper,\n    show,\n    toggle\n  }] = useDropdownMenu({\n    flip,\n    rootCloseEvent,\n    show: showProps,\n    usePopper: !isNavbar && alignClasses.length === 0,\n    offset: [0, 2],\n    popperConfig,\n    placement\n  });\n  menuProps.ref = useMergedRefs(useWrappedRefWithWarning(ref, 'DropdownMenu'), menuProps.ref);\n  useIsomorphicEffect(() => {\n    // Popper's initial position for the menu is incorrect when\n    // renderOnMount=true. Need to call update() to correct it.\n    if (show) popper == null || popper.update();\n  }, [show]);\n  if (!hasShown && !renderOnMount && !isInputGroup) return null;\n\n  // For custom components provide additional, non-DOM, props;\n  if (typeof Component !== 'string') {\n    menuProps.show = show;\n    menuProps.close = () => toggle == null ? void 0 : toggle(false);\n    menuProps.align = align;\n  }\n  let style = props.style;\n  if (popper != null && popper.placement) {\n    // we don't need the default popper style,\n    // menus are display: none when not shown.\n    style = {\n      ...props.style,\n      ...menuProps.style\n    };\n    props['x-placement'] = popper.placement;\n  }\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ...menuProps,\n    style: style\n    // Bootstrap css requires this data attrib to style responsive menus.\n    ,\n\n    ...((alignClasses.length || isNavbar) && {\n      'data-bs-popper': 'static'\n    }),\n    className: classNames(className, prefix, show && 'show', alignEnd && `${prefix}-end`, variant && `${prefix}-${variant}`, ...alignClasses)\n  });\n});\nDropdownMenu.displayName = 'DropdownMenu';\nexport default DropdownMenu;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useDropdownMenu", "useIsomorphicEffect", "useMergedRefs", "warning", "DropdownContext", "InputGroupContext", "NavbarContext", "useBootstrapPrefix", "useWrappedRefWithWarning", "alignPropType", "jsx", "_jsx", "getDropdownMenuPlacement", "alignEnd", "dropDirection", "isRTL", "topStart", "topEnd", "bottomStart", "bottomEnd", "leftStart", "leftEnd", "rightStart", "rightEnd", "placement", "DropdownMenu", "forwardRef", "_ref", "ref", "bsPrefix", "className", "align", "rootCloseEvent", "flip", "show", "showProps", "renderOnMount", "as", "Component", "popperConfig", "variant", "props", "isNavbar", "prefix", "contextAlign", "drop", "isInputGroup", "alignClasses", "keys", "Object", "process", "env", "NODE_ENV", "length", "brkPoint", "direction", "push", "menuProps", "hasShown", "popper", "toggle", "usePopper", "offset", "update", "close", "style", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/DropdownMenu.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useDropdownMenu } from '@restart/ui/DropdownMenu';\nimport useIsomorphicEffect from '@restart/hooks/useIsomorphicEffect';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport warning from 'warning';\nimport DropdownContext from './DropdownContext';\nimport InputGroupContext from './InputGroupContext';\nimport NavbarContext from './NavbarContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport useWrappedRefWithWarning from './useWrappedRefWithWarning';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getDropdownMenuPlacement(alignEnd, dropDirection, isRTL) {\n  const topStart = isRTL ? 'top-end' : 'top-start';\n  const topEnd = isRTL ? 'top-start' : 'top-end';\n  const bottomStart = isRTL ? 'bottom-end' : 'bottom-start';\n  const bottomEnd = isRTL ? 'bottom-start' : 'bottom-end';\n  const leftStart = isRTL ? 'right-start' : 'left-start';\n  const leftEnd = isRTL ? 'right-end' : 'left-end';\n  const rightStart = isRTL ? 'left-start' : 'right-start';\n  const rightEnd = isRTL ? 'left-end' : 'right-end';\n  let placement = alignEnd ? bottomEnd : bottomStart;\n  if (dropDirection === 'up') placement = alignEnd ? topEnd : topStart;else if (dropDirection === 'end') placement = alignEnd ? rightEnd : rightStart;else if (dropDirection === 'start') placement = alignEnd ? leftEnd : leftStart;else if (dropDirection === 'down-centered') placement = 'bottom';else if (dropDirection === 'up-centered') placement = 'top';\n  return placement;\n}\nconst DropdownMenu = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  align,\n  rootCloseEvent,\n  flip = true,\n  show: showProps,\n  renderOnMount,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  popperConfig,\n  variant,\n  ...props\n}, ref) => {\n  let alignEnd = false;\n  const isNavbar = useContext(NavbarContext);\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-menu');\n  const {\n    align: contextAlign,\n    drop,\n    isRTL\n  } = useContext(DropdownContext);\n  align = align || contextAlign;\n  const isInputGroup = useContext(InputGroupContext);\n  const alignClasses = [];\n  if (align) {\n    if (typeof align === 'object') {\n      const keys = Object.keys(align);\n      process.env.NODE_ENV !== \"production\" ? warning(keys.length === 1, 'There should only be 1 breakpoint when passing an object to `align`') : void 0;\n      if (keys.length) {\n        const brkPoint = keys[0];\n        const direction = align[brkPoint];\n\n        // .dropdown-menu-end is required for responsively aligning\n        // left in addition to align left classes.\n        alignEnd = direction === 'start';\n        alignClasses.push(`${prefix}-${brkPoint}-${direction}`);\n      }\n    } else if (align === 'end') {\n      alignEnd = true;\n    }\n  }\n  const placement = getDropdownMenuPlacement(alignEnd, drop, isRTL);\n  const [menuProps, {\n    hasShown,\n    popper,\n    show,\n    toggle\n  }] = useDropdownMenu({\n    flip,\n    rootCloseEvent,\n    show: showProps,\n    usePopper: !isNavbar && alignClasses.length === 0,\n    offset: [0, 2],\n    popperConfig,\n    placement\n  });\n  menuProps.ref = useMergedRefs(useWrappedRefWithWarning(ref, 'DropdownMenu'), menuProps.ref);\n  useIsomorphicEffect(() => {\n    // Popper's initial position for the menu is incorrect when\n    // renderOnMount=true. Need to call update() to correct it.\n    if (show) popper == null || popper.update();\n  }, [show]);\n  if (!hasShown && !renderOnMount && !isInputGroup) return null;\n\n  // For custom components provide additional, non-DOM, props;\n  if (typeof Component !== 'string') {\n    menuProps.show = show;\n    menuProps.close = () => toggle == null ? void 0 : toggle(false);\n    menuProps.align = align;\n  }\n  let style = props.style;\n  if (popper != null && popper.placement) {\n    // we don't need the default popper style,\n    // menus are display: none when not shown.\n    style = {\n      ...props.style,\n      ...menuProps.style\n    };\n    props['x-placement'] = popper.placement;\n  }\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ...menuProps,\n    style: style\n    // Bootstrap css requires this data attrib to style responsive menus.\n    ,\n    ...((alignClasses.length || isNavbar) && {\n      'data-bs-popper': 'static'\n    }),\n    className: classNames(className, prefix, show && 'show', alignEnd && `${prefix}-end`, variant && `${prefix}-${variant}`, ...alignClasses)\n  });\n});\nDropdownMenu.displayName = 'DropdownMenu';\nexport default DropdownMenu;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,OAAO,MAAM,SAAS;AAC7B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,SAASC,aAAa,QAAQ,SAAS;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,aAAa,EAAEC,KAAK,EAAE;EACvE,MAAMC,QAAQ,GAAGD,KAAK,GAAG,SAAS,GAAG,WAAW;EAChD,MAAME,MAAM,GAAGF,KAAK,GAAG,WAAW,GAAG,SAAS;EAC9C,MAAMG,WAAW,GAAGH,KAAK,GAAG,YAAY,GAAG,cAAc;EACzD,MAAMI,SAAS,GAAGJ,KAAK,GAAG,cAAc,GAAG,YAAY;EACvD,MAAMK,SAAS,GAAGL,KAAK,GAAG,aAAa,GAAG,YAAY;EACtD,MAAMM,OAAO,GAAGN,KAAK,GAAG,WAAW,GAAG,UAAU;EAChD,MAAMO,UAAU,GAAGP,KAAK,GAAG,YAAY,GAAG,aAAa;EACvD,MAAMQ,QAAQ,GAAGR,KAAK,GAAG,UAAU,GAAG,WAAW;EACjD,IAAIS,SAAS,GAAGX,QAAQ,GAAGM,SAAS,GAAGD,WAAW;EAClD,IAAIJ,aAAa,KAAK,IAAI,EAAEU,SAAS,GAAGX,QAAQ,GAAGI,MAAM,GAAGD,QAAQ,CAAC,KAAK,IAAIF,aAAa,KAAK,KAAK,EAAEU,SAAS,GAAGX,QAAQ,GAAGU,QAAQ,GAAGD,UAAU,CAAC,KAAK,IAAIR,aAAa,KAAK,OAAO,EAAEU,SAAS,GAAGX,QAAQ,GAAGQ,OAAO,GAAGD,SAAS,CAAC,KAAK,IAAIN,aAAa,KAAK,eAAe,EAAEU,SAAS,GAAG,QAAQ,CAAC,KAAK,IAAIV,aAAa,KAAK,aAAa,EAAEU,SAAS,GAAG,KAAK;EAC/V,OAAOA,SAAS;AAClB;AACA,MAAMC,YAAY,GAAG,aAAa3B,KAAK,CAAC4B,UAAU,CAAC,CAAAC,IAAA,EAahDC,GAAG,KAAK;EAAA,IAbyC;IAClDC,QAAQ;IACRC,SAAS;IACTC,KAAK;IACLC,cAAc;IACdC,IAAI,GAAG,IAAI;IACXC,IAAI,EAAEC,SAAS;IACfC,aAAa;IACb;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrBC,YAAY;IACZC,OAAO;IACP,GAAGC;EACL,CAAC,GAAAd,IAAA;EACC,IAAId,QAAQ,GAAG,KAAK;EACpB,MAAM6B,QAAQ,GAAG3C,UAAU,CAACO,aAAa,CAAC;EAC1C,MAAMqC,MAAM,GAAGpC,kBAAkB,CAACsB,QAAQ,EAAE,eAAe,CAAC;EAC5D,MAAM;IACJE,KAAK,EAAEa,YAAY;IACnBC,IAAI;IACJ9B;EACF,CAAC,GAAGhB,UAAU,CAACK,eAAe,CAAC;EAC/B2B,KAAK,GAAGA,KAAK,IAAIa,YAAY;EAC7B,MAAME,YAAY,GAAG/C,UAAU,CAACM,iBAAiB,CAAC;EAClD,MAAM0C,YAAY,GAAG,EAAE;EACvB,IAAIhB,KAAK,EAAE;IACT,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMiB,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACjB,KAAK,CAAC;MAC/BmB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjD,OAAO,CAAC6C,IAAI,CAACK,MAAM,KAAK,CAAC,EAAE,qEAAqE,CAAC,GAAG,KAAK,CAAC;MAClJ,IAAIL,IAAI,CAACK,MAAM,EAAE;QACf,MAAMC,QAAQ,GAAGN,IAAI,CAAC,CAAC,CAAC;QACxB,MAAMO,SAAS,GAAGxB,KAAK,CAACuB,QAAQ,CAAC;;QAEjC;QACA;QACAzC,QAAQ,GAAG0C,SAAS,KAAK,OAAO;QAChCR,YAAY,CAACS,IAAI,CAAC,GAAGb,MAAM,IAAIW,QAAQ,IAAIC,SAAS,EAAE,CAAC;MACzD;IACF,CAAC,MAAM,IAAIxB,KAAK,KAAK,KAAK,EAAE;MAC1BlB,QAAQ,GAAG,IAAI;IACjB;EACF;EACA,MAAMW,SAAS,GAAGZ,wBAAwB,CAACC,QAAQ,EAAEgC,IAAI,EAAE9B,KAAK,CAAC;EACjE,MAAM,CAAC0C,SAAS,EAAE;IAChBC,QAAQ;IACRC,MAAM;IACNzB,IAAI;IACJ0B;EACF,CAAC,CAAC,GAAG5D,eAAe,CAAC;IACnBiC,IAAI;IACJD,cAAc;IACdE,IAAI,EAAEC,SAAS;IACf0B,SAAS,EAAE,CAACnB,QAAQ,IAAIK,YAAY,CAACM,MAAM,KAAK,CAAC;IACjDS,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdvB,YAAY;IACZf;EACF,CAAC,CAAC;EACFiC,SAAS,CAAC7B,GAAG,GAAG1B,aAAa,CAACM,wBAAwB,CAACoB,GAAG,EAAE,cAAc,CAAC,EAAE6B,SAAS,CAAC7B,GAAG,CAAC;EAC3F3B,mBAAmB,CAAC,MAAM;IACxB;IACA;IACA,IAAIiC,IAAI,EAAEyB,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACI,MAAM,CAAC,CAAC;EAC7C,CAAC,EAAE,CAAC7B,IAAI,CAAC,CAAC;EACV,IAAI,CAACwB,QAAQ,IAAI,CAACtB,aAAa,IAAI,CAACU,YAAY,EAAE,OAAO,IAAI;;EAE7D;EACA,IAAI,OAAOR,SAAS,KAAK,QAAQ,EAAE;IACjCmB,SAAS,CAACvB,IAAI,GAAGA,IAAI;IACrBuB,SAAS,CAACO,KAAK,GAAG,MAAMJ,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,KAAK,CAAC;IAC/DH,SAAS,CAAC1B,KAAK,GAAGA,KAAK;EACzB;EACA,IAAIkC,KAAK,GAAGxB,KAAK,CAACwB,KAAK;EACvB,IAAIN,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACnC,SAAS,EAAE;IACtC;IACA;IACAyC,KAAK,GAAG;MACN,GAAGxB,KAAK,CAACwB,KAAK;MACd,GAAGR,SAAS,CAACQ;IACf,CAAC;IACDxB,KAAK,CAAC,aAAa,CAAC,GAAGkB,MAAM,CAACnC,SAAS;EACzC;EACA,OAAO,aAAab,IAAI,CAAC2B,SAAS,EAAE;IAClC,GAAGG,KAAK;IACR,GAAGgB,SAAS;IACZQ,KAAK,EAAEA;IACP;IAAA;;IAEA,IAAI,CAAClB,YAAY,CAACM,MAAM,IAAIX,QAAQ,KAAK;MACvC,gBAAgB,EAAE;IACpB,CAAC,CAAC;IACFZ,SAAS,EAAEjC,UAAU,CAACiC,SAAS,EAAEa,MAAM,EAAET,IAAI,IAAI,MAAM,EAAErB,QAAQ,IAAI,GAAG8B,MAAM,MAAM,EAAEH,OAAO,IAAI,GAAGG,MAAM,IAAIH,OAAO,EAAE,EAAE,GAAGO,YAAY;EAC1I,CAAC,CAAC;AACJ,CAAC,CAAC;AACFtB,YAAY,CAACyC,WAAW,GAAG,cAAc;AACzC,eAAezC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}