{"ast": null, "code": "import capitalize from '@mui/utils/capitalize';\nexport default capitalize;", "map": {"version": 3, "names": ["capitalize"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/utils/capitalize.js"], "sourcesContent": ["import capitalize from '@mui/utils/capitalize';\nexport default capitalize;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}