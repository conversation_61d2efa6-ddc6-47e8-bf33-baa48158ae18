{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst BreadcrumbItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  active = false,\n  children,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'li',\n  linkAs: LinkComponent = Anchor,\n  linkProps = {},\n  href,\n  title,\n  target,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb-item');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(prefix, className, {\n      active\n    }),\n    \"aria-current\": active ? 'page' : undefined,\n    children: active ? children : /*#__PURE__*/_jsx(LinkComponent, {\n      ...linkProps,\n      href: href,\n      title: title,\n      target: target,\n      children: children\n    })\n  });\n});\nBreadcrumbItem.displayName = 'BreadcrumbItem';\nexport default BreadcrumbItem;", "map": {"version": 3, "names": ["classNames", "React", "<PERSON><PERSON>", "useBootstrapPrefix", "jsx", "_jsx", "BreadcrumbItem", "forwardRef", "bsPrefix", "active", "children", "className", "as", "Component", "linkAs", "LinkComponent", "linkProps", "href", "title", "target", "props", "ref", "prefix", "undefined", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/BreadcrumbItem.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst BreadcrumbItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  active = false,\n  children,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'li',\n  linkAs: LinkComponent = Anchor,\n  linkProps = {},\n  href,\n  title,\n  target,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb-item');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(prefix, className, {\n      active\n    }),\n    \"aria-current\": active ? 'page' : undefined,\n    children: active ? children : /*#__PURE__*/_jsx(LinkComponent, {\n      ...linkProps,\n      href: href,\n      title: title,\n      target: target,\n      children: children\n    })\n  });\n});\nBreadcrumbItem.displayName = 'BreadcrumbItem';\nexport default BreadcrumbItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACpDC,QAAQ;EACRC,MAAM,GAAG,KAAK;EACdC,QAAQ;EACRC,SAAS;EACT;EACAC,EAAE,EAAEC,SAAS,GAAG,IAAI;EACpBC,MAAM,EAAEC,aAAa,GAAGb,MAAM;EAC9Bc,SAAS,GAAG,CAAC,CAAC;EACdC,IAAI;EACJC,KAAK;EACLC,MAAM;EACN,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGnB,kBAAkB,CAACK,QAAQ,EAAE,iBAAiB,CAAC;EAC9D,OAAO,aAAaH,IAAI,CAACQ,SAAS,EAAE;IAClCQ,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRT,SAAS,EAAEX,UAAU,CAACsB,MAAM,EAAEX,SAAS,EAAE;MACvCF;IACF,CAAC,CAAC;IACF,cAAc,EAAEA,MAAM,GAAG,MAAM,GAAGc,SAAS;IAC3Cb,QAAQ,EAAED,MAAM,GAAGC,QAAQ,GAAG,aAAaL,IAAI,CAACU,aAAa,EAAE;MAC7D,GAAGC,SAAS;MACZC,IAAI,EAAEA,IAAI;MACVC,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA,MAAM;MACdT,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,cAAc,CAACkB,WAAW,GAAG,gBAAgB;AAC7C,eAAelB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}