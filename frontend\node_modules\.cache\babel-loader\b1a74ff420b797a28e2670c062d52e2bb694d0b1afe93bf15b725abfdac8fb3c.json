{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getSnackbarContentUtilityClass } from \"./snackbarContentClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    action: ['action'],\n    message: ['message']\n  };\n  return composeClasses(slots, getSnackbarContentUtilityClass, classes);\n};\nconst SnackbarContentRoot = styled(Paper, {\n  name: 'MuiSnackbarContent',\n  slot: 'Root'\n})(memoTheme(_ref => {\n  let {\n    theme\n  } = _ref;\n  const emphasis = theme.palette.mode === 'light' ? 0.8 : 0.98;\n  return {\n    ...theme.typography.body2,\n    color: theme.vars ? theme.vars.palette.SnackbarContent.color : theme.palette.getContrastText(emphasize(theme.palette.background.default, emphasis)),\n    backgroundColor: theme.vars ? theme.vars.palette.SnackbarContent.bg : emphasize(theme.palette.background.default, emphasis),\n    display: 'flex',\n    alignItems: 'center',\n    flexWrap: 'wrap',\n    padding: '6px 16px',\n    flexGrow: 1,\n    [theme.breakpoints.up('sm')]: {\n      flexGrow: 'initial',\n      minWidth: 288\n    }\n  };\n}));\nconst SnackbarContentMessage = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Message'\n})({\n  padding: '8px 0'\n});\nconst SnackbarContentAction = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Action'\n})({\n  display: 'flex',\n  alignItems: 'center',\n  marginLeft: 'auto',\n  paddingLeft: 16,\n  marginRight: -8\n});\nconst SnackbarContent = /*#__PURE__*/React.forwardRef(function SnackbarContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSnackbarContent'\n  });\n  const {\n    action,\n    className,\n    message,\n    role = 'alert',\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(SnackbarContentRoot, {\n    role: role,\n    elevation: 6,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other,\n    children: [/*#__PURE__*/_jsx(SnackbarContentMessage, {\n      className: classes.message,\n      ownerState: ownerState,\n      children: message\n    }), action ? /*#__PURE__*/_jsx(SnackbarContentAction, {\n      className: classes.action,\n      ownerState: ownerState,\n      children: action\n    }) : null]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SnackbarContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The action to display. It renders after the message, at the end of the snackbar.\n   */\n  action: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The message to display.\n   */\n  message: PropTypes.node,\n  /**\n   * The ARIA role attribute of the element.\n   * @default 'alert'\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default SnackbarContent;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "emphasize", "styled", "memoTheme", "useDefaultProps", "Paper", "getSnackbarContentUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "action", "message", "SnackbarContentRoot", "name", "slot", "_ref", "theme", "emphasis", "palette", "mode", "typography", "body2", "color", "vars", "SnackbarContent", "getContrastText", "background", "default", "backgroundColor", "bg", "display", "alignItems", "flexWrap", "padding", "flexGrow", "breakpoints", "up", "min<PERSON><PERSON><PERSON>", "SnackbarContentMessage", "SnackbarContentAction", "marginLeft", "paddingLeft", "marginRight", "forwardRef", "inProps", "ref", "props", "className", "role", "other", "elevation", "children", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/SnackbarContent/SnackbarContent.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getSnackbarContentUtilityClass } from \"./snackbarContentClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    action: ['action'],\n    message: ['message']\n  };\n  return composeClasses(slots, getSnackbarContentUtilityClass, classes);\n};\nconst SnackbarContentRoot = styled(Paper, {\n  name: 'MuiSnackbarContent',\n  slot: 'Root'\n})(memoTheme(({\n  theme\n}) => {\n  const emphasis = theme.palette.mode === 'light' ? 0.8 : 0.98;\n  return {\n    ...theme.typography.body2,\n    color: theme.vars ? theme.vars.palette.SnackbarContent.color : theme.palette.getContrastText(emphasize(theme.palette.background.default, emphasis)),\n    backgroundColor: theme.vars ? theme.vars.palette.SnackbarContent.bg : emphasize(theme.palette.background.default, emphasis),\n    display: 'flex',\n    alignItems: 'center',\n    flexWrap: 'wrap',\n    padding: '6px 16px',\n    flexGrow: 1,\n    [theme.breakpoints.up('sm')]: {\n      flexGrow: 'initial',\n      minWidth: 288\n    }\n  };\n}));\nconst SnackbarContentMessage = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Message'\n})({\n  padding: '8px 0'\n});\nconst SnackbarContentAction = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Action'\n})({\n  display: 'flex',\n  alignItems: 'center',\n  marginLeft: 'auto',\n  paddingLeft: 16,\n  marginRight: -8\n});\nconst SnackbarContent = /*#__PURE__*/React.forwardRef(function SnackbarContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSnackbarContent'\n  });\n  const {\n    action,\n    className,\n    message,\n    role = 'alert',\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(SnackbarContentRoot, {\n    role: role,\n    elevation: 6,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other,\n    children: [/*#__PURE__*/_jsx(SnackbarContentMessage, {\n      className: classes.message,\n      ownerState: ownerState,\n      children: message\n    }), action ? /*#__PURE__*/_jsx(SnackbarContentAction, {\n      className: classes.action,\n      ownerState: ownerState,\n      children: action\n    }) : null]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SnackbarContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The action to display. It renders after the message, at the end of the snackbar.\n   */\n  action: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The message to display.\n   */\n  message: PropTypes.node,\n  /**\n   * The ARIA role attribute of the element.\n   * @default 'alert'\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default SnackbarContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,KAAK,MAAM,mBAAmB;AACrC,SAASC,8BAA8B,QAAQ,6BAA6B;AAC5E,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClBC,OAAO,EAAE,CAAC,SAAS;EACrB,CAAC;EACD,OAAOjB,cAAc,CAACc,KAAK,EAAER,8BAA8B,EAAEO,OAAO,CAAC;AACvE,CAAC;AACD,MAAMK,mBAAmB,GAAGhB,MAAM,CAACG,KAAK,EAAE;EACxCc,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE;AACR,CAAC,CAAC,CAACjB,SAAS,CAACkB,IAAA,IAEP;EAAA,IAFQ;IACZC;EACF,CAAC,GAAAD,IAAA;EACC,MAAME,QAAQ,GAAGD,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAG,GAAG,GAAG,IAAI;EAC5D,OAAO;IACL,GAAGH,KAAK,CAACI,UAAU,CAACC,KAAK;IACzBC,KAAK,EAAEN,KAAK,CAACO,IAAI,GAAGP,KAAK,CAACO,IAAI,CAACL,OAAO,CAACM,eAAe,CAACF,KAAK,GAAGN,KAAK,CAACE,OAAO,CAACO,eAAe,CAAC9B,SAAS,CAACqB,KAAK,CAACE,OAAO,CAACQ,UAAU,CAACC,OAAO,EAAEV,QAAQ,CAAC,CAAC;IACnJW,eAAe,EAAEZ,KAAK,CAACO,IAAI,GAAGP,KAAK,CAACO,IAAI,CAACL,OAAO,CAACM,eAAe,CAACK,EAAE,GAAGlC,SAAS,CAACqB,KAAK,CAACE,OAAO,CAACQ,UAAU,CAACC,OAAO,EAAEV,QAAQ,CAAC;IAC3Ha,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,CAAC;IACX,CAAClB,KAAK,CAACmB,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5BF,QAAQ,EAAE,SAAS;MACnBG,QAAQ,EAAE;IACZ;EACF,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMC,sBAAsB,GAAG1C,MAAM,CAAC,KAAK,EAAE;EAC3CiB,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDmB,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMM,qBAAqB,GAAG3C,MAAM,CAAC,KAAK,EAAE;EAC1CiB,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDgB,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBS,UAAU,EAAE,MAAM;EAClBC,WAAW,EAAE,EAAE;EACfC,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;AACF,MAAMlB,eAAe,GAAG,aAAajC,KAAK,CAACoD,UAAU,CAAC,SAASnB,eAAeA,CAACoB,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAMC,KAAK,GAAGhD,eAAe,CAAC;IAC5BgD,KAAK,EAAEF,OAAO;IACd/B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJH,MAAM;IACNqC,SAAS;IACTpC,OAAO;IACPqC,IAAI,GAAG,OAAO;IACd,GAAGC;EACL,CAAC,GAAGH,KAAK;EACT,MAAMxC,UAAU,GAAGwC,KAAK;EACxB,MAAMvC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,KAAK,CAACQ,mBAAmB,EAAE;IAC7CoC,IAAI,EAAEA,IAAI;IACVE,SAAS,EAAE,CAAC;IACZH,SAAS,EAAEtD,IAAI,CAACc,OAAO,CAACE,IAAI,EAAEsC,SAAS,CAAC;IACxCzC,UAAU,EAAEA,UAAU;IACtBuC,GAAG,EAAEA,GAAG;IACR,GAAGI,KAAK;IACRE,QAAQ,EAAE,CAAC,aAAajD,IAAI,CAACoC,sBAAsB,EAAE;MACnDS,SAAS,EAAExC,OAAO,CAACI,OAAO;MAC1BL,UAAU,EAAEA,UAAU;MACtB6C,QAAQ,EAAExC;IACZ,CAAC,CAAC,EAAED,MAAM,GAAG,aAAaR,IAAI,CAACqC,qBAAqB,EAAE;MACpDQ,SAAS,EAAExC,OAAO,CAACG,MAAM;MACzBJ,UAAU,EAAEA,UAAU;MACtB6C,QAAQ,EAAEzC;IACZ,CAAC,CAAC,GAAG,IAAI;EACX,CAAC,CAAC;AACJ,CAAC,CAAC;AACF0C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9B,eAAe,CAAC+B,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;EACE7C,MAAM,EAAElB,SAAS,CAACgE,IAAI;EACtB;AACF;AACA;EACEjD,OAAO,EAAEf,SAAS,CAACiE,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAEvD,SAAS,CAACkE,MAAM;EAC3B;AACF;AACA;EACE/C,OAAO,EAAEnB,SAAS,CAACgE,IAAI;EACvB;AACF;AACA;AACA;EACER,IAAI,EAAExD,SAAS,CAAC,sCAAsCkE,MAAM;EAC5D;AACF;AACA;EACEC,EAAE,EAAEnE,SAAS,CAACoE,SAAS,CAAC,CAACpE,SAAS,CAACqE,OAAO,CAACrE,SAAS,CAACoE,SAAS,CAAC,CAACpE,SAAS,CAACsE,IAAI,EAAEtE,SAAS,CAACiE,MAAM,EAAEjE,SAAS,CAACuE,IAAI,CAAC,CAAC,CAAC,EAAEvE,SAAS,CAACsE,IAAI,EAAEtE,SAAS,CAACiE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAejC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}