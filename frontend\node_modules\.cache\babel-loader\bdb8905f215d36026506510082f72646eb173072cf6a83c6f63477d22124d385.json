{"ast": null, "code": "export { default } from \"./Timeline.js\";\nexport * from \"./Timeline.types.js\";\nexport { default as timelineClasses } from \"./timelineClasses.js\";\nexport * from \"./timelineClasses.js\";", "map": {"version": 3, "names": ["default", "timelineClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/Timeline/index.js"], "sourcesContent": ["export { default } from \"./Timeline.js\";\nexport * from \"./Timeline.types.js\";\nexport { default as timelineClasses } from \"./timelineClasses.js\";\nexport * from \"./timelineClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC,cAAc,qBAAqB;AACnC,SAASA,OAAO,IAAIC,eAAe,QAAQ,sBAAsB;AACjE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}