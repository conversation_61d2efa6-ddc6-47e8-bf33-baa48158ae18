{"ast": null, "code": "var i = Symbol.for(\"preact-signals\");\nfunction t() {\n  if (!(s > 1)) {\n    var i,\n      t = !1;\n    while (void 0 !== h) {\n      var r = h;\n      h = void 0;\n      f++;\n      while (void 0 !== r) {\n        var o = r.o;\n        r.o = void 0;\n        r.f &= -3;\n        if (!(8 & r.f) && c(r)) try {\n          r.c();\n        } catch (r) {\n          if (!t) {\n            i = r;\n            t = !0;\n          }\n        }\n        r = o;\n      }\n    }\n    f = 0;\n    s--;\n    if (t) throw i;\n  } else s--;\n}\nfunction r(i) {\n  if (s > 0) return i();\n  s++;\n  try {\n    return i();\n  } finally {\n    t();\n  }\n}\nvar o = void 0;\nfunction n(i) {\n  var t = o;\n  o = void 0;\n  try {\n    return i();\n  } finally {\n    o = t;\n  }\n}\nvar h = void 0,\n  s = 0,\n  f = 0,\n  v = 0;\nfunction e(i) {\n  if (void 0 !== o) {\n    var t = i.n;\n    if (void 0 === t || t.t !== o) {\n      t = {\n        i: 0,\n        S: i,\n        p: o.s,\n        n: void 0,\n        t: o,\n        e: void 0,\n        x: void 0,\n        r: t\n      };\n      if (void 0 !== o.s) o.s.n = t;\n      o.s = t;\n      i.n = t;\n      if (32 & o.f) i.S(t);\n      return t;\n    } else if (-1 === t.i) {\n      t.i = 0;\n      if (void 0 !== t.n) {\n        t.n.p = t.p;\n        if (void 0 !== t.p) t.p.n = t.n;\n        t.p = o.s;\n        t.n = void 0;\n        o.s.n = t;\n        o.s = t;\n      }\n      return t;\n    }\n  }\n}\nfunction u(i, t) {\n  this.v = i;\n  this.i = 0;\n  this.n = void 0;\n  this.t = void 0;\n  this.W = null == t ? void 0 : t.watched;\n  this.Z = null == t ? void 0 : t.unwatched;\n}\nu.prototype.brand = i;\nu.prototype.h = function () {\n  return !0;\n};\nu.prototype.S = function (i) {\n  var t = this,\n    r = this.t;\n  if (r !== i && void 0 === i.e) {\n    i.x = r;\n    this.t = i;\n    if (void 0 !== r) r.e = i;else n(function () {\n      var i;\n      null == (i = t.W) || i.call(t);\n    });\n  }\n};\nu.prototype.U = function (i) {\n  var t = this;\n  if (void 0 !== this.t) {\n    var r = i.e,\n      o = i.x;\n    if (void 0 !== r) {\n      r.x = o;\n      i.e = void 0;\n    }\n    if (void 0 !== o) {\n      o.e = r;\n      i.x = void 0;\n    }\n    if (i === this.t) {\n      this.t = o;\n      if (void 0 === o) n(function () {\n        var i;\n        null == (i = t.Z) || i.call(t);\n      });\n    }\n  }\n};\nu.prototype.subscribe = function (i) {\n  var t = this;\n  return E(function () {\n    var r = t.value,\n      n = o;\n    o = void 0;\n    try {\n      i(r);\n    } finally {\n      o = n;\n    }\n  });\n};\nu.prototype.valueOf = function () {\n  return this.value;\n};\nu.prototype.toString = function () {\n  return this.value + \"\";\n};\nu.prototype.toJSON = function () {\n  return this.value;\n};\nu.prototype.peek = function () {\n  var i = o;\n  o = void 0;\n  try {\n    return this.value;\n  } finally {\n    o = i;\n  }\n};\nObject.defineProperty(u.prototype, \"value\", {\n  get: function () {\n    var i = e(this);\n    if (void 0 !== i) i.i = this.i;\n    return this.v;\n  },\n  set: function (i) {\n    if (i !== this.v) {\n      if (f > 100) throw new Error(\"Cycle detected\");\n      this.v = i;\n      this.i++;\n      v++;\n      s++;\n      try {\n        for (var r = this.t; void 0 !== r; r = r.x) r.t.N();\n      } finally {\n        t();\n      }\n    }\n  }\n});\nfunction d(i, t) {\n  return new u(i, t);\n}\nfunction c(i) {\n  for (var t = i.s; void 0 !== t; t = t.n) if (t.S.i !== t.i || !t.S.h() || t.S.i !== t.i) return !0;\n  return !1;\n}\nfunction a(i) {\n  for (var t = i.s; void 0 !== t; t = t.n) {\n    var r = t.S.n;\n    if (void 0 !== r) t.r = r;\n    t.S.n = t;\n    t.i = -1;\n    if (void 0 === t.n) {\n      i.s = t;\n      break;\n    }\n  }\n}\nfunction l(i) {\n  var t = i.s,\n    r = void 0;\n  while (void 0 !== t) {\n    var o = t.p;\n    if (-1 === t.i) {\n      t.S.U(t);\n      if (void 0 !== o) o.n = t.n;\n      if (void 0 !== t.n) t.n.p = o;\n    } else r = t;\n    t.S.n = t.r;\n    if (void 0 !== t.r) t.r = void 0;\n    t = o;\n  }\n  i.s = r;\n}\nfunction y(i, t) {\n  u.call(this, void 0);\n  this.x = i;\n  this.s = void 0;\n  this.g = v - 1;\n  this.f = 4;\n  this.W = null == t ? void 0 : t.watched;\n  this.Z = null == t ? void 0 : t.unwatched;\n}\ny.prototype = new u();\ny.prototype.h = function () {\n  this.f &= -3;\n  if (1 & this.f) return !1;\n  if (32 == (36 & this.f)) return !0;\n  this.f &= -5;\n  if (this.g === v) return !0;\n  this.g = v;\n  this.f |= 1;\n  if (this.i > 0 && !c(this)) {\n    this.f &= -2;\n    return !0;\n  }\n  var i = o;\n  try {\n    a(this);\n    o = this;\n    var t = this.x();\n    if (16 & this.f || this.v !== t || 0 === this.i) {\n      this.v = t;\n      this.f &= -17;\n      this.i++;\n    }\n  } catch (i) {\n    this.v = i;\n    this.f |= 16;\n    this.i++;\n  }\n  o = i;\n  l(this);\n  this.f &= -2;\n  return !0;\n};\ny.prototype.S = function (i) {\n  if (void 0 === this.t) {\n    this.f |= 36;\n    for (var t = this.s; void 0 !== t; t = t.n) t.S.S(t);\n  }\n  u.prototype.S.call(this, i);\n};\ny.prototype.U = function (i) {\n  if (void 0 !== this.t) {\n    u.prototype.U.call(this, i);\n    if (void 0 === this.t) {\n      this.f &= -33;\n      for (var t = this.s; void 0 !== t; t = t.n) t.S.U(t);\n    }\n  }\n};\ny.prototype.N = function () {\n  if (!(2 & this.f)) {\n    this.f |= 6;\n    for (var i = this.t; void 0 !== i; i = i.x) i.t.N();\n  }\n};\nObject.defineProperty(y.prototype, \"value\", {\n  get: function () {\n    if (1 & this.f) throw new Error(\"Cycle detected\");\n    var i = e(this);\n    this.h();\n    if (void 0 !== i) i.i = this.i;\n    if (16 & this.f) throw this.v;\n    return this.v;\n  }\n});\nfunction w(i, t) {\n  return new y(i, t);\n}\nfunction _(i) {\n  var r = i.u;\n  i.u = void 0;\n  if (\"function\" == typeof r) {\n    s++;\n    var n = o;\n    o = void 0;\n    try {\n      r();\n    } catch (t) {\n      i.f &= -2;\n      i.f |= 8;\n      b(i);\n      throw t;\n    } finally {\n      o = n;\n      t();\n    }\n  }\n}\nfunction b(i) {\n  for (var t = i.s; void 0 !== t; t = t.n) t.S.U(t);\n  i.x = void 0;\n  i.s = void 0;\n  _(i);\n}\nfunction g(i) {\n  if (o !== this) throw new Error(\"Out-of-order effect\");\n  l(this);\n  o = i;\n  this.f &= -2;\n  if (8 & this.f) b(this);\n  t();\n}\nfunction p(i) {\n  this.x = i;\n  this.u = void 0;\n  this.s = void 0;\n  this.o = void 0;\n  this.f = 32;\n}\np.prototype.c = function () {\n  var i = this.S();\n  try {\n    if (8 & this.f) return;\n    if (void 0 === this.x) return;\n    var t = this.x();\n    if (\"function\" == typeof t) this.u = t;\n  } finally {\n    i();\n  }\n};\np.prototype.S = function () {\n  if (1 & this.f) throw new Error(\"Cycle detected\");\n  this.f |= 1;\n  this.f &= -9;\n  _(this);\n  a(this);\n  s++;\n  var i = o;\n  o = this;\n  return g.bind(this, i);\n};\np.prototype.N = function () {\n  if (!(2 & this.f)) {\n    this.f |= 2;\n    this.o = h;\n    h = this;\n  }\n};\np.prototype.d = function () {\n  this.f |= 8;\n  if (!(1 & this.f)) b(this);\n};\np.prototype.dispose = function () {\n  this.d();\n};\nfunction E(i) {\n  var t = new p(i);\n  try {\n    t.c();\n  } catch (i) {\n    t.d();\n    throw i;\n  }\n  var r = t.d.bind(t);\n  r[Symbol.dispose] = r;\n  return r;\n}\nexport { u as Signal, r as batch, w as computed, E as effect, d as signal, n as untracked };", "map": {"version": 3, "names": ["i", "Symbol", "for", "t", "s", "h", "r", "f", "o", "c", "n", "v", "e", "S", "p", "x", "u", "W", "watched", "Z", "unwatched", "prototype", "brand", "call", "U", "subscribe", "E", "value", "valueOf", "toString", "toJSON", "peek", "Object", "defineProperty", "get", "set", "Error", "N", "d", "a", "l", "y", "g", "w", "_", "b", "bind", "dispose", "Signal", "batch", "computed", "effect", "signal", "untracked"], "sources": ["C:\\laragon\\www\\frontend\\node_modules\\@preact\\signals-core\\src\\index.ts"], "sourcesContent": ["// An named symbol/brand for detecting Signal instances even when they weren't\n// created using the same signals library version.\nconst BRAND_SYMBOL = Symbol.for(\"preact-signals\");\n\n// Flags for Computed and Effect.\nconst RUNNING = 1 << 0;\nconst NOTIFIED = 1 << 1;\nconst OUTDATED = 1 << 2;\nconst DISPOSED = 1 << 3;\nconst HAS_ERROR = 1 << 4;\nconst TRACKING = 1 << 5;\n\n// A linked list node used to track dependencies (sources) and dependents (targets).\n// Also used to remember the source's last version number that the target saw.\ntype Node = {\n\t// A source whose value the target depends on.\n\t_source: Signal;\n\t_prevSource?: Node;\n\t_nextSource?: Node;\n\n\t// A target that depends on the source and should be notified when the source changes.\n\t_target: Computed | Effect;\n\t_prevTarget?: Node;\n\t_nextTarget?: Node;\n\n\t// The version number of the source that target has last seen. We use version numbers\n\t// instead of storing the source value, because source values can take arbitrary amount\n\t// of memory, and computeds could hang on to them forever because they're lazily evaluated.\n\t// Use the special value -1 to mark potentially unused but recyclable nodes.\n\t_version: number;\n\n\t// Used to remember & roll back the source's previous `._node` value when entering &\n\t// exiting a new evaluation context.\n\t_rollbackNode?: Node;\n};\n\nfunction startBatch() {\n\tbatchDepth++;\n}\n\nfunction endBatch() {\n\tif (batchDepth > 1) {\n\t\tbatchDepth--;\n\t\treturn;\n\t}\n\n\tlet error: unknown;\n\tlet hasError = false;\n\n\twhile (batchedEffect !== undefined) {\n\t\tlet effect: Effect | undefined = batchedEffect;\n\t\tbatchedEffect = undefined;\n\n\t\tbatchIteration++;\n\n\t\twhile (effect !== undefined) {\n\t\t\tconst next: Effect | undefined = effect._nextBatchedEffect;\n\t\t\teffect._nextBatchedEffect = undefined;\n\t\t\teffect._flags &= ~NOTIFIED;\n\n\t\t\tif (!(effect._flags & DISPOSED) && needsToRecompute(effect)) {\n\t\t\t\ttry {\n\t\t\t\t\teffect._callback();\n\t\t\t\t} catch (err) {\n\t\t\t\t\tif (!hasError) {\n\t\t\t\t\t\terror = err;\n\t\t\t\t\t\thasError = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\teffect = next;\n\t\t}\n\t}\n\tbatchIteration = 0;\n\tbatchDepth--;\n\n\tif (hasError) {\n\t\tthrow error;\n\t}\n}\n\n/**\n * Combine multiple value updates into one \"commit\" at the end of the provided callback.\n *\n * Batches can be nested and changes are only flushed once the outermost batch callback\n * completes.\n *\n * Accessing a signal that has been modified within a batch will reflect its updated\n * value.\n *\n * @param fn The callback function.\n * @returns The value returned by the callback.\n */\nfunction batch<T>(fn: () => T): T {\n\tif (batchDepth > 0) {\n\t\treturn fn();\n\t}\n\t/*@__INLINE__**/ startBatch();\n\ttry {\n\t\treturn fn();\n\t} finally {\n\t\tendBatch();\n\t}\n}\n\n// Currently evaluated computed or effect.\nlet evalContext: Computed | Effect | undefined = undefined;\n\n/**\n * Run a callback function that can access signal values without\n * subscribing to the signal updates.\n *\n * @param fn The callback function.\n * @returns The value returned by the callback.\n */\nfunction untracked<T>(fn: () => T): T {\n\tconst prevContext = evalContext;\n\tevalContext = undefined;\n\ttry {\n\t\treturn fn();\n\t} finally {\n\t\tevalContext = prevContext;\n\t}\n}\n\n// Effects collected into a batch.\nlet batchedEffect: Effect | undefined = undefined;\nlet batchDepth = 0;\nlet batchIteration = 0;\n\n// A global version number for signals, used for fast-pathing repeated\n// computed.peek()/computed.value calls when nothing has changed globally.\nlet globalVersion = 0;\n\nfunction addDependency(signal: Signal): Node | undefined {\n\tif (evalContext === undefined) {\n\t\treturn undefined;\n\t}\n\n\tlet node = signal._node;\n\tif (node === undefined || node._target !== evalContext) {\n\t\t/**\n\t\t * `signal` is a new dependency. Create a new dependency node, and set it\n\t\t * as the tail of the current context's dependency list. e.g:\n\t\t *\n\t\t * { A <-> B       }\n\t\t *         ↑     ↑\n\t\t *        tail  node (new)\n\t\t *               ↓\n\t\t * { A <-> B <-> C }\n\t\t *               ↑\n\t\t *              tail (evalContext._sources)\n\t\t */\n\t\tnode = {\n\t\t\t_version: 0,\n\t\t\t_source: signal,\n\t\t\t_prevSource: evalContext._sources,\n\t\t\t_nextSource: undefined,\n\t\t\t_target: evalContext,\n\t\t\t_prevTarget: undefined,\n\t\t\t_nextTarget: undefined,\n\t\t\t_rollbackNode: node,\n\t\t};\n\n\t\tif (evalContext._sources !== undefined) {\n\t\t\tevalContext._sources._nextSource = node;\n\t\t}\n\t\tevalContext._sources = node;\n\t\tsignal._node = node;\n\n\t\t// Subscribe to change notifications from this dependency if we're in an effect\n\t\t// OR evaluating a computed signal that in turn has subscribers.\n\t\tif (evalContext._flags & TRACKING) {\n\t\t\tsignal._subscribe(node);\n\t\t}\n\t\treturn node;\n\t} else if (node._version === -1) {\n\t\t// `signal` is an existing dependency from a previous evaluation. Reuse it.\n\t\tnode._version = 0;\n\n\t\t/**\n\t\t * If `node` is not already the current tail of the dependency list (i.e.\n\t\t * there is a next node in the list), then make the `node` the new tail. e.g:\n\t\t *\n\t\t * { A <-> B <-> C <-> D }\n\t\t *         ↑           ↑\n\t\t *        node   ┌─── tail (evalContext._sources)\n\t\t *         └─────│─────┐\n\t\t *               ↓     ↓\n\t\t * { A <-> C <-> D <-> B }\n\t\t *                     ↑\n\t\t *                    tail (evalContext._sources)\n\t\t */\n\t\tif (node._nextSource !== undefined) {\n\t\t\tnode._nextSource._prevSource = node._prevSource;\n\n\t\t\tif (node._prevSource !== undefined) {\n\t\t\t\tnode._prevSource._nextSource = node._nextSource;\n\t\t\t}\n\n\t\t\tnode._prevSource = evalContext._sources;\n\t\t\tnode._nextSource = undefined;\n\n\t\t\tevalContext._sources!._nextSource = node;\n\t\t\tevalContext._sources = node;\n\t\t}\n\n\t\t// We can assume that the currently evaluated effect / computed signal is already\n\t\t// subscribed to change notifications from `signal` if needed.\n\t\treturn node;\n\t}\n\treturn undefined;\n}\n\n/**\n * The base class for plain and computed signals.\n */\n// @ts-ignore: \"Cannot redeclare exported variable 'Signal'.\"\n//\n// A function with the same name is defined later, so we need to ignore TypeScript's\n// warning about a redeclared variable.\n//\n// The class is declared here, but later implemented with ES5-style prototypes.\n// This enables better control of the transpiled output size.\ndeclare class Signal<T = any> {\n\t/** @internal */\n\t_value: unknown;\n\n\t/**\n\t * @internal\n\t * Version numbers should always be >= 0, because the special value -1 is used\n\t * by Nodes to signify potentially unused but recyclable nodes.\n\t */\n\t_version: number;\n\n\t/** @internal */\n\t_node?: Node;\n\n\t/** @internal */\n\t_targets?: Node;\n\n\tconstructor(value?: T, options?: SignalOptions<T>);\n\n\t/** @internal */\n\t_refresh(): boolean;\n\n\t/** @internal */\n\t_subscribe(node: Node): void;\n\n\t/** @internal */\n\t_unsubscribe(node: Node): void;\n\n\t/** @internal */\n\t_watched?(this: Signal<T>): void;\n\n\t/** @internal */\n\t_unwatched?(this: Signal<T>): void;\n\n\tsubscribe(fn: (value: T) => void): () => void;\n\n\tvalueOf(): T;\n\n\ttoString(): string;\n\n\ttoJSON(): T;\n\n\tpeek(): T;\n\n\tbrand: typeof BRAND_SYMBOL;\n\n\tget value(): T;\n\tset value(value: T);\n}\n\nexport interface SignalOptions<T = any> {\n\twatched?: (this: Signal<T>) => void;\n\tunwatched?: (this: Signal<T>) => void;\n}\n\n/** @internal */\n// @ts-ignore: \"Cannot redeclare exported variable 'Signal'.\"\n//\n// A class with the same name has already been declared, so we need to ignore\n// TypeScript's warning about a redeclared variable.\n//\n// The previously declared class is implemented here with ES5-style prototypes.\n// This enables better control of the transpiled output size.\nfunction Signal(this: Signal, value?: unknown, options?: SignalOptions) {\n\tthis._value = value;\n\tthis._version = 0;\n\tthis._node = undefined;\n\tthis._targets = undefined;\n\tthis._watched = options?.watched;\n\tthis._unwatched = options?.unwatched;\n}\n\nSignal.prototype.brand = BRAND_SYMBOL;\n\nSignal.prototype._refresh = function () {\n\treturn true;\n};\n\nSignal.prototype._subscribe = function (node) {\n\tconst targets = this._targets;\n\tif (targets !== node && node._prevTarget === undefined) {\n\t\tnode._nextTarget = targets;\n\t\tthis._targets = node;\n\n\t\tif (targets !== undefined) {\n\t\t\ttargets._prevTarget = node;\n\t\t} else {\n\t\t\tuntracked(() => {\n\t\t\t\tthis._watched?.call(this);\n\t\t\t});\n\t\t}\n\t}\n};\n\nSignal.prototype._unsubscribe = function (node) {\n\t// Only run the unsubscribe step if the signal has any subscribers to begin with.\n\tif (this._targets !== undefined) {\n\t\tconst prev = node._prevTarget;\n\t\tconst next = node._nextTarget;\n\t\tif (prev !== undefined) {\n\t\t\tprev._nextTarget = next;\n\t\t\tnode._prevTarget = undefined;\n\t\t}\n\n\t\tif (next !== undefined) {\n\t\t\tnext._prevTarget = prev;\n\t\t\tnode._nextTarget = undefined;\n\t\t}\n\n\t\tif (node === this._targets) {\n\t\t\tthis._targets = next;\n\t\t\tif (next === undefined) {\n\t\t\t\tuntracked(() => {\n\t\t\t\t\tthis._unwatched?.call(this);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n};\n\nSignal.prototype.subscribe = function (fn) {\n\treturn effect(() => {\n\t\tconst value = this.value;\n\n\t\tconst prevContext = evalContext;\n\t\tevalContext = undefined;\n\t\ttry {\n\t\t\tfn(value);\n\t\t} finally {\n\t\t\tevalContext = prevContext;\n\t\t}\n\t});\n};\n\nSignal.prototype.valueOf = function () {\n\treturn this.value;\n};\n\nSignal.prototype.toString = function () {\n\treturn this.value + \"\";\n};\n\nSignal.prototype.toJSON = function () {\n\treturn this.value;\n};\n\nSignal.prototype.peek = function () {\n\tconst prevContext = evalContext;\n\tevalContext = undefined;\n\ttry {\n\t\treturn this.value;\n\t} finally {\n\t\tevalContext = prevContext;\n\t}\n};\n\nObject.defineProperty(Signal.prototype, \"value\", {\n\tget(this: Signal) {\n\t\tconst node = addDependency(this);\n\t\tif (node !== undefined) {\n\t\t\tnode._version = this._version;\n\t\t}\n\t\treturn this._value;\n\t},\n\tset(this: Signal, value) {\n\t\tif (value !== this._value) {\n\t\t\tif (batchIteration > 100) {\n\t\t\t\tthrow new Error(\"Cycle detected\");\n\t\t\t}\n\n\t\t\tthis._value = value;\n\t\t\tthis._version++;\n\t\t\tglobalVersion++;\n\n\t\t\t/**@__INLINE__*/ startBatch();\n\t\t\ttry {\n\t\t\t\tfor (\n\t\t\t\t\tlet node = this._targets;\n\t\t\t\t\tnode !== undefined;\n\t\t\t\t\tnode = node._nextTarget\n\t\t\t\t) {\n\t\t\t\t\tnode._target._notify();\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tendBatch();\n\t\t\t}\n\t\t}\n\t},\n});\n\n/**\n * Create a new plain signal.\n *\n * @param value The initial value for the signal.\n * @returns A new signal.\n */\nexport function signal<T>(value: T, options?: SignalOptions<T>): Signal<T>;\nexport function signal<T = undefined>(): Signal<T | undefined>;\nexport function signal<T>(value?: T, options?: SignalOptions<T>): Signal<T> {\n\treturn new Signal(value, options);\n}\n\nfunction needsToRecompute(target: Computed | Effect): boolean {\n\t// Check the dependencies for changed values. The dependency list is already\n\t// in order of use. Therefore if multiple dependencies have changed values, only\n\t// the first used dependency is re-evaluated at this point.\n\tfor (\n\t\tlet node = target._sources;\n\t\tnode !== undefined;\n\t\tnode = node._nextSource\n\t) {\n\t\tif (\n\t\t\t// If the dependency has definitely been updated since its version number\n\t\t\t// was observed, then we need to recompute. This first check is not strictly\n\t\t\t// necessary for correctness, but allows us to skip the refresh call if the\n\t\t\t// dependency has already been updated.\n\t\t\tnode._source._version !== node._version ||\n\t\t\t// Refresh the dependency. If there's something blocking the refresh (e.g. a\n\t\t\t// dependency cycle), then we need to recompute.\n\t\t\t!node._source._refresh() ||\n\t\t\t// If the dependency got a new version after the refresh, then we need to recompute.\n\t\t\tnode._source._version !== node._version\n\t\t) {\n\t\t\treturn true;\n\t\t}\n\t}\n\t// If none of the dependencies have changed values since last recompute then\n\t// there's no need to recompute.\n\treturn false;\n}\n\nfunction prepareSources(target: Computed | Effect) {\n\t/**\n\t * 1. Mark all current sources as re-usable nodes (version: -1)\n\t * 2. Set a rollback node if the current node is being used in a different context\n\t * 3. Point 'target._sources' to the tail of the doubly-linked list, e.g:\n\t *\n\t *    { undefined <- A <-> B <-> C -> undefined }\n\t *                   ↑           ↑\n\t *                   │           └──────┐\n\t * target._sources = A; (node is head)  │\n\t *                   ↓                  │\n\t * target._sources = C; (node is tail) ─┘\n\t */\n\tfor (\n\t\tlet node = target._sources;\n\t\tnode !== undefined;\n\t\tnode = node._nextSource\n\t) {\n\t\tconst rollbackNode = node._source._node;\n\t\tif (rollbackNode !== undefined) {\n\t\t\tnode._rollbackNode = rollbackNode;\n\t\t}\n\t\tnode._source._node = node;\n\t\tnode._version = -1;\n\n\t\tif (node._nextSource === undefined) {\n\t\t\ttarget._sources = node;\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\nfunction cleanupSources(target: Computed | Effect) {\n\tlet node = target._sources;\n\tlet head: Node | undefined = undefined;\n\n\t/**\n\t * At this point 'target._sources' points to the tail of the doubly-linked list.\n\t * It contains all existing sources + new sources in order of use.\n\t * Iterate backwards until we find the head node while dropping old dependencies.\n\t */\n\twhile (node !== undefined) {\n\t\tconst prev = node._prevSource;\n\n\t\t/**\n\t\t * The node was not re-used, unsubscribe from its change notifications and remove itself\n\t\t * from the doubly-linked list. e.g:\n\t\t *\n\t\t * { A <-> B <-> C }\n\t\t *         ↓\n\t\t *    { A <-> C }\n\t\t */\n\t\tif (node._version === -1) {\n\t\t\tnode._source._unsubscribe(node);\n\n\t\t\tif (prev !== undefined) {\n\t\t\t\tprev._nextSource = node._nextSource;\n\t\t\t}\n\t\t\tif (node._nextSource !== undefined) {\n\t\t\t\tnode._nextSource._prevSource = prev;\n\t\t\t}\n\t\t} else {\n\t\t\t/**\n\t\t\t * The new head is the last node seen which wasn't removed/unsubscribed\n\t\t\t * from the doubly-linked list. e.g:\n\t\t\t *\n\t\t\t * { A <-> B <-> C }\n\t\t\t *   ↑     ↑     ↑\n\t\t\t *   │     │     └ head = node\n\t\t\t *   │     └ head = node\n\t\t\t *   └ head = node\n\t\t\t */\n\t\t\thead = node;\n\t\t}\n\n\t\tnode._source._node = node._rollbackNode;\n\t\tif (node._rollbackNode !== undefined) {\n\t\t\tnode._rollbackNode = undefined;\n\t\t}\n\n\t\tnode = prev;\n\t}\n\n\ttarget._sources = head;\n}\n\ndeclare class Computed<T = any> extends Signal<T> {\n\t_fn: () => T;\n\t_sources?: Node;\n\t_globalVersion: number;\n\t_flags: number;\n\n\tconstructor(fn: () => T, options?: SignalOptions<T>);\n\n\t_notify(): void;\n\tget value(): T;\n}\n\nfunction Computed(this: Computed, fn: () => unknown, options?: SignalOptions) {\n\tSignal.call(this, undefined);\n\n\tthis._fn = fn;\n\tthis._sources = undefined;\n\tthis._globalVersion = globalVersion - 1;\n\tthis._flags = OUTDATED;\n\tthis._watched = options?.watched;\n\tthis._unwatched = options?.unwatched;\n}\n\nComputed.prototype = new Signal() as Computed;\n\nComputed.prototype._refresh = function () {\n\tthis._flags &= ~NOTIFIED;\n\n\tif (this._flags & RUNNING) {\n\t\treturn false;\n\t}\n\n\t// If this computed signal has subscribed to updates from its dependencies\n\t// (TRACKING flag set) and none of them have notified about changes (OUTDATED\n\t// flag not set), then the computed value can't have changed.\n\tif ((this._flags & (OUTDATED | TRACKING)) === TRACKING) {\n\t\treturn true;\n\t}\n\tthis._flags &= ~OUTDATED;\n\n\tif (this._globalVersion === globalVersion) {\n\t\treturn true;\n\t}\n\tthis._globalVersion = globalVersion;\n\n\t// Mark this computed signal running before checking the dependencies for value\n\t// changes, so that the RUNNING flag can be used to notice cyclical dependencies.\n\tthis._flags |= RUNNING;\n\tif (this._version > 0 && !needsToRecompute(this)) {\n\t\tthis._flags &= ~RUNNING;\n\t\treturn true;\n\t}\n\n\tconst prevContext = evalContext;\n\ttry {\n\t\tprepareSources(this);\n\t\tevalContext = this;\n\t\tconst value = this._fn();\n\t\tif (\n\t\t\tthis._flags & HAS_ERROR ||\n\t\t\tthis._value !== value ||\n\t\t\tthis._version === 0\n\t\t) {\n\t\t\tthis._value = value;\n\t\t\tthis._flags &= ~HAS_ERROR;\n\t\t\tthis._version++;\n\t\t}\n\t} catch (err) {\n\t\tthis._value = err;\n\t\tthis._flags |= HAS_ERROR;\n\t\tthis._version++;\n\t}\n\tevalContext = prevContext;\n\tcleanupSources(this);\n\tthis._flags &= ~RUNNING;\n\treturn true;\n};\n\nComputed.prototype._subscribe = function (node) {\n\tif (this._targets === undefined) {\n\t\tthis._flags |= OUTDATED | TRACKING;\n\n\t\t// A computed signal subscribes lazily to its dependencies when it\n\t\t// gets its first subscriber.\n\t\tfor (\n\t\t\tlet node = this._sources;\n\t\t\tnode !== undefined;\n\t\t\tnode = node._nextSource\n\t\t) {\n\t\t\tnode._source._subscribe(node);\n\t\t}\n\t}\n\tSignal.prototype._subscribe.call(this, node);\n};\n\nComputed.prototype._unsubscribe = function (node) {\n\t// Only run the unsubscribe step if the computed signal has any subscribers.\n\tif (this._targets !== undefined) {\n\t\tSignal.prototype._unsubscribe.call(this, node);\n\n\t\t// Computed signal unsubscribes from its dependencies when it loses its last subscriber.\n\t\t// This makes it possible for unreferences subgraphs of computed signals to get garbage collected.\n\t\tif (this._targets === undefined) {\n\t\t\tthis._flags &= ~TRACKING;\n\n\t\t\tfor (\n\t\t\t\tlet node = this._sources;\n\t\t\t\tnode !== undefined;\n\t\t\t\tnode = node._nextSource\n\t\t\t) {\n\t\t\t\tnode._source._unsubscribe(node);\n\t\t\t}\n\t\t}\n\t}\n};\n\nComputed.prototype._notify = function () {\n\tif (!(this._flags & NOTIFIED)) {\n\t\tthis._flags |= OUTDATED | NOTIFIED;\n\n\t\tfor (\n\t\t\tlet node = this._targets;\n\t\t\tnode !== undefined;\n\t\t\tnode = node._nextTarget\n\t\t) {\n\t\t\tnode._target._notify();\n\t\t}\n\t}\n};\n\nObject.defineProperty(Computed.prototype, \"value\", {\n\tget(this: Computed) {\n\t\tif (this._flags & RUNNING) {\n\t\t\tthrow new Error(\"Cycle detected\");\n\t\t}\n\t\tconst node = addDependency(this);\n\t\tthis._refresh();\n\t\tif (node !== undefined) {\n\t\t\tnode._version = this._version;\n\t\t}\n\t\tif (this._flags & HAS_ERROR) {\n\t\t\tthrow this._value;\n\t\t}\n\t\treturn this._value;\n\t},\n});\n\n/**\n * An interface for read-only signals.\n */\ninterface ReadonlySignal<T = any> {\n\treadonly value: T;\n\tpeek(): T;\n\n\tsubscribe(fn: (value: T) => void): () => void;\n\tvalueOf(): T;\n\ttoString(): string;\n\ttoJSON(): T;\n\tbrand: typeof BRAND_SYMBOL;\n}\n\n/**\n * Create a new signal that is computed based on the values of other signals.\n *\n * The returned computed signal is read-only, and its value is automatically\n * updated when any signals accessed from within the callback function change.\n *\n * @param fn The effect callback.\n * @returns A new read-only signal.\n */\nfunction computed<T>(\n\tfn: () => T,\n\toptions?: SignalOptions<T>\n): ReadonlySignal<T> {\n\treturn new Computed(fn, options);\n}\n\nfunction cleanupEffect(effect: Effect) {\n\tconst cleanup = effect._cleanup;\n\teffect._cleanup = undefined;\n\n\tif (typeof cleanup === \"function\") {\n\t\t/*@__INLINE__**/ startBatch();\n\n\t\t// Run cleanup functions always outside of any context.\n\t\tconst prevContext = evalContext;\n\t\tevalContext = undefined;\n\t\ttry {\n\t\t\tcleanup();\n\t\t} catch (err) {\n\t\t\teffect._flags &= ~RUNNING;\n\t\t\teffect._flags |= DISPOSED;\n\t\t\tdisposeEffect(effect);\n\t\t\tthrow err;\n\t\t} finally {\n\t\t\tevalContext = prevContext;\n\t\t\tendBatch();\n\t\t}\n\t}\n}\n\nfunction disposeEffect(effect: Effect) {\n\tfor (\n\t\tlet node = effect._sources;\n\t\tnode !== undefined;\n\t\tnode = node._nextSource\n\t) {\n\t\tnode._source._unsubscribe(node);\n\t}\n\teffect._fn = undefined;\n\teffect._sources = undefined;\n\n\tcleanupEffect(effect);\n}\n\nfunction endEffect(this: Effect, prevContext?: Computed | Effect) {\n\tif (evalContext !== this) {\n\t\tthrow new Error(\"Out-of-order effect\");\n\t}\n\tcleanupSources(this);\n\tevalContext = prevContext;\n\n\tthis._flags &= ~RUNNING;\n\tif (this._flags & DISPOSED) {\n\t\tdisposeEffect(this);\n\t}\n\tendBatch();\n}\n\ntype EffectFn =\n\t| ((this: { dispose: () => void }) => void | (() => void))\n\t| (() => void | (() => void));\n\ndeclare class Effect {\n\t_fn?: EffectFn;\n\t_cleanup?: () => void;\n\t_sources?: Node;\n\t_nextBatchedEffect?: Effect;\n\t_flags: number;\n\n\tconstructor(fn: EffectFn);\n\n\t_callback(): void;\n\t_start(): () => void;\n\t_notify(): void;\n\t_dispose(): void;\n\tdispose(): void;\n}\n\nfunction Effect(this: Effect, fn: EffectFn) {\n\tthis._fn = fn;\n\tthis._cleanup = undefined;\n\tthis._sources = undefined;\n\tthis._nextBatchedEffect = undefined;\n\tthis._flags = TRACKING;\n}\n\nEffect.prototype._callback = function () {\n\tconst finish = this._start();\n\ttry {\n\t\tif (this._flags & DISPOSED) return;\n\t\tif (this._fn === undefined) return;\n\n\t\tconst cleanup = this._fn();\n\t\tif (typeof cleanup === \"function\") {\n\t\t\tthis._cleanup = cleanup;\n\t\t}\n\t} finally {\n\t\tfinish();\n\t}\n};\n\nEffect.prototype._start = function () {\n\tif (this._flags & RUNNING) {\n\t\tthrow new Error(\"Cycle detected\");\n\t}\n\tthis._flags |= RUNNING;\n\tthis._flags &= ~DISPOSED;\n\tcleanupEffect(this);\n\tprepareSources(this);\n\n\t/*@__INLINE__**/ startBatch();\n\tconst prevContext = evalContext;\n\tevalContext = this;\n\treturn endEffect.bind(this, prevContext);\n};\n\nEffect.prototype._notify = function () {\n\tif (!(this._flags & NOTIFIED)) {\n\t\tthis._flags |= NOTIFIED;\n\t\tthis._nextBatchedEffect = batchedEffect;\n\t\tbatchedEffect = this;\n\t}\n};\n\nEffect.prototype._dispose = function () {\n\tthis._flags |= DISPOSED;\n\n\tif (!(this._flags & RUNNING)) {\n\t\tdisposeEffect(this);\n\t}\n};\n\nEffect.prototype.dispose = function () {\n\tthis._dispose();\n};\n/**\n * Create an effect to run arbitrary code in response to signal changes.\n *\n * An effect tracks which signals are accessed within the given callback\n * function `fn`, and re-runs the callback when those signals change.\n *\n * The callback may return a cleanup function. The cleanup function gets\n * run once, either when the callback is next called or when the effect\n * gets disposed, whichever happens first.\n *\n * @param fn The effect callback.\n * @returns A function for disposing the effect.\n */\nfunction effect(fn: EffectFn): { (): void; [Symbol.dispose](): void } {\n\tconst effect = new Effect(fn);\n\ttry {\n\t\teffect._callback();\n\t} catch (err) {\n\t\teffect._dispose();\n\t\tthrow err;\n\t}\n\t// Return a bound function instead of a wrapper like `() => effect._dispose()`,\n\t// because bound functions seem to be just as fast and take up a lot less memory.\n\tconst dispose = effect._dispose.bind(effect);\n\t(dispose as any)[Symbol.dispose] = dispose;\n\treturn dispose as any;\n}\n\nexport { computed, effect, batch, untracked, Signal, ReadonlySignal };\n"], "mappings": "AAEA,IAAMA,CAAA,GAAeC,MAAA,CAAAC,GAAA,CAAW;AAsChC,SAASC,EAAA;EACR,MAAIC,CAAA,GAAa,IAAjB;IAKA,IAAIJ,CAAA;MACAG,CAAA,IAAW;IAEf,YAAyB,MAAlBE,CAAA,EAA6B;MACnC,IAAIC,CAAA,GAA6BD,CAAA;MACjCA,CAAA,QAAgB;MAEhBE,CAAA;MAEA,YAAkB,MAAXD,CAAA,EAAsB;QAC5B,IAAME,CAAA,GAA2BF,CAAA,CAAOE,CAAA;QACxCF,CAAA,CAAOE,CAAA,QAAqB;QAC5BF,CAAA,CAAOC,CAAA,KAAU;QAEjB,MApDc,IAoDRD,CAAA,CAAOC,CAAA,KAAsBE,CAAA,CAAiBH,CAAA,GACnD;UACCA,CAAA,CAAOG,CAAA,EAMR;QAAA,CALE,QAAOH,CAAA;UACR,KAAKH,CAAA,EAAU;YACdH,CAAA,GAAQM,CAAA;YACRH,CAAA,IAAW,CACZ;UAAA;QACD;QAEDG,CAAA,GAASE,CACV;MAAA;IACD;IACAD,CAAA,GAAiB;IACjBH,CAAA;IAEA,IAAID,CAAA,EACH,MAAMH,CAjCP;EAAA,OAFCI,CAAA,EAqCF;AAAA;AAcA,SAASE,EAASN,CAAA;EACjB,IAAII,CAAA,GAAa,GAChB,OAAOJ,CAAA;EA1DRI,CAAA;EA6DA;IACC,OAAOJ,CAAA,EAGR;EAAA,CAFC;IACAG,CAAA,EACD;EAAA;AACD;AAGA,IAAIK,CAAA,QAA6C;AASjD,SAASE,EAAaV,CAAA;EACrB,IAAMG,CAAA,GAAcK,CAAA;EACpBA,CAAA,QAAc;EACd;IACC,OAAOR,CAAA,EAGR;EAAA,CAFC;IACAQ,CAAA,GAAcL,CACf;EAAA;AACD;AAGA,IAAIE,CAAA,QAAoC;EACpCD,CAAA,GAAa;EACbG,CAAA,GAAiB;EAIjBI,CAAA,GAAgB;AAEpB,SAASC,EAAcZ,CAAA;EACtB,SAAoB,MAAhBQ,CAAA,EAAJ;IAIA,IAAIL,CAAA,GAAOH,CAAA,CAAOU,CAAA;IAClB,SAAa,MAATP,CAAA,IAAsBA,CAAA,CAAKA,CAAA,KAAYK,CAAA,EAAa;MAavDL,CAAA,GAAO;QACNH,CAAA,EAAU;QACVa,CAAA,EAASb,CAAA;QACTc,CAAA,EAAaN,CAAA,CAAYJ,CAAA;QACzBM,CAAA,OAAa;QACbP,CAAA,EAASK,CAAA;QACTI,CAAA,OAAa;QACbG,CAAA,OAAa;QACbT,CAAA,EAAeH;MAAA;MAGhB,SAA6B,MAAzBK,CAAA,CAAYJ,CAAA,EACfI,CAAA,CAAYJ,CAAA,CAASM,CAAA,GAAcP,CAAA;MAEpCK,CAAA,CAAYJ,CAAA,GAAWD,CAAA;MACvBH,CAAA,CAAOU,CAAA,GAAQP,CAAA;MAIf,IAlKe,KAkKXK,CAAA,CAAYD,CAAA,EACfP,CAAA,CAAOa,CAAA,CAAWV,CAAA;MAEnB,OAAOA,CACR;IAAA,OAAO,KAAuB,MAAnBA,CAAA,CAAKH,CAAA,EAAiB;MAEhCG,CAAA,CAAKH,CAAA,GAAW;MAehB,SAAyB,MAArBG,CAAA,CAAKO,CAAA,EAA2B;QACnCP,CAAA,CAAKO,CAAA,CAAYI,CAAA,GAAcX,CAAA,CAAKW,CAAA;QAEpC,SAAyB,MAArBX,CAAA,CAAKW,CAAA,EACRX,CAAA,CAAKW,CAAA,CAAYJ,CAAA,GAAcP,CAAA,CAAKO,CAAA;QAGrCP,CAAA,CAAKW,CAAA,GAAcN,CAAA,CAAYJ,CAAA;QAC/BD,CAAA,CAAKO,CAAA,QAAc;QAEnBF,CAAA,CAAYJ,CAAA,CAAUM,CAAA,GAAcP,CAAA;QACpCK,CAAA,CAAYJ,CAAA,GAAWD,CACxB;MAAA;MAIA,OAAOA,CACR;IAAA;EAzEA;AA2ED;AA2EA,SAASa,EAAqBhB,CAAA,EAAiBG,CAAA;EAC9C,KAAKQ,CAAA,GAASX,CAAA;EACd,KAAKA,CAAA,GAAW;EAChB,KAAKU,CAAA,QAAQ;EACb,KAAKP,CAAA,QAAW;EAChB,KAAKc,CAAA,GAAW,QAAAd,CAAA,YAAAA,CAAA,CAASe,OAAA;EACzB,KAAKC,CAAA,GAAoB,QAAPhB,CAAA,QAAO,IAAPA,CAAA,CAASiB,SAC5B;AAAA;AAEAJ,CAAA,CAAOK,SAAA,CAAUC,KAAA,GAAQtB,CAAA;AAEzBgB,CAAA,CAAOK,SAAA,CAAUhB,CAAA,GAAW;EAC3B,QAAO,CACR;AAAA;AAEAW,CAAA,CAAOK,SAAA,CAAUR,CAAA,GAAa,UAAUb,CAAA;EAAI,IAAAG,CAAA,GAC3C;IAAMG,CAAA,GAAU,KAAKH,CAAA;EACrB,IAAIG,CAAA,KAAYN,CAAA,SAA6B,MAArBA,CAAA,CAAKY,CAAA,EAA2B;IACvDZ,CAAA,CAAKe,CAAA,GAAcT,CAAA;IACnB,KAAKH,CAAA,GAAWH,CAAA;IAEhB,SAAgB,MAAZM,CAAA,EACHA,CAAA,CAAQM,CAAA,GAAcZ,CAAA,MAEtBU,CAAA,CAAU;MAAK,IAAAV,CAAA;MACd,SAAAA,CAAA,GAAAG,CAAA,CAAKc,CAAA,KAALjB,CAAA,CAAeuB,IAAA,CAAKpB,CAAA,CACrB;IAAA,EAEF;EAAA;AACD;AAEAa,CAAA,CAAOK,SAAA,CAAUG,CAAA,GAAe,UAAUxB,CAAA;EAAA,IAAIG,CAAA;EAE7C,SAAsB,MAAlB,KAAKA,CAAA,EAAwB;IAChC,IAAMG,CAAA,GAAON,CAAA,CAAKY,CAAA;MACZJ,CAAA,GAAOR,CAAA,CAAKe,CAAA;IAClB,SAAa,MAATT,CAAA,EAAoB;MACvBA,CAAA,CAAKS,CAAA,GAAcP,CAAA;MACnBR,CAAA,CAAKY,CAAA,QAAc,CACpB;IAAA;IAEA,SAAa,MAATJ,CAAA,EAAoB;MACvBA,CAAA,CAAKI,CAAA,GAAcN,CAAA;MACnBN,CAAA,CAAKe,CAAA,QAAc,CACpB;IAAA;IAEA,IAAIf,CAAA,KAAS,KAAKG,CAAA,EAAU;MAC3B,KAAKA,CAAA,GAAWK,CAAA;MAChB,SAAa,MAATA,CAAA,EACHE,CAAA,CAAU;QAAK,IAAAV,CAAA;QACC,SAAfA,CAAA,GAAAG,CAAA,CAAKgB,CAAA,KAALnB,CAAA,CAAiBuB,IAAA,CAAKpB,CAAA,CACvB;MAAA,EAEF;IAAA;EACD;AACD;AAEAa,CAAA,CAAOK,SAAA,CAAUI,SAAA,GAAY,UAAUzB,CAAA;EAAE,IAAAG,CAAA;EACxC,OAAOuB,CAAA,CAAO;IACb,IAAMpB,CAAA,GAAQH,CAAA,CAAKwB,KAAA;MAEbjB,CAAA,GAAcF,CAAA;IACpBA,CAAA,QAAc;IACd;MACCR,CAAA,CAAGM,CAAA,CAGJ;IAAA,CAFC;MACAE,CAAA,GAAcE,CACf;IAAA;EACD,EACD;AAAA;AAEAM,CAAA,CAAOK,SAAA,CAAUO,OAAA,GAAU;EAC1B,OAAW,KAACD,KACb;AAAA;AAEAX,CAAA,CAAOK,SAAA,CAAUQ,QAAA,GAAW;EAC3B,OAAO,KAAKF,KAAA,GAAQ,EACrB;AAAA;AAEAX,CAAA,CAAOK,SAAA,CAAUS,MAAA,GAAS;EACzB,OAAO,KAAKH,KACb;AAAA;AAEAX,CAAA,CAAOK,SAAA,CAAUU,IAAA,GAAO;EACvB,IAAM/B,CAAA,GAAcQ,CAAA;EACpBA,CAAA,QAAc;EACd;IACC,OAAW,KAACmB,KAGb;EAAA,CAFC;IACAnB,CAAA,GAAcR,CACf;EAAA;AACD;AAEAgC,MAAA,CAAOC,cAAA,CAAejB,CAAA,CAAOK,SAAA,EAAW,SAAS;EAChDa,GAAA,WAAAA,CAAA;IACC,IAAMlC,CAAA,GAAOY,CAAA,CAAc;IAC3B,SAAa,MAATZ,CAAA,EACHA,CAAA,CAAKA,CAAA,GAAW,KAAKA,CAAA;IAEtB,OAAO,KAAKW,CACb;EAAA;EACAwB,GAAA,EAAG,SAAAA,CAAenC,CAAA;IACjB,IAAIA,CAAA,KAAU,KAAKW,CAAA,EAAQ;MAC1B,IAAIJ,CAAA,GAAiB,KACpB,MAAU,IAAA6B,KAAA,CAAM;MAGjB,KAAKzB,CAAA,GAASX,CAAA;MACd,KAAKA,CAAA;MACLW,CAAA;MAvWFP,CAAA;MA0WE;QACC,KACC,IAAIE,CAAA,GAAO,KAAKH,CAAA,OACP,MAATG,CAAA,EACAA,CAAA,GAAOA,CAAA,CAAKS,CAAA,EAEZT,CAAA,CAAKH,CAAA,CAAQkC,CAAA,EAIf;MAAA,CAFC;QACAlC,CAAA,EACD;MAAA;IACD;EACD;AAAA;AAWe,SAAAmC,EAAUtC,CAAA,EAAWG,CAAA;EACpC,OAAW,IAAAa,CAAA,CAAOhB,CAAA,EAAOG,CAAA,CAC1B;AAAA;AAEA,SAASM,EAAiBT,CAAA;EAIzB,KACC,IAAIG,CAAA,GAAOH,CAAA,CAAOI,CAAA,OACT,MAATD,CAAA,EACAA,CAAA,GAAOA,CAAA,CAAKO,CAAA,EAEZ,IAKCP,CAAA,CAAKU,CAAA,CAAQb,CAAA,KAAaG,CAAA,CAAKH,CAAA,KAG9BG,CAAA,CAAKU,CAAA,CAAQR,CAAA,MAEdF,CAAA,CAAKU,CAAA,CAAQb,CAAA,KAAaG,CAAA,CAAKH,CAAA,EAE/B,QAAO;EAKT,QACD;AAAA;AAEA,SAASuC,EAAevC,CAAA;EAavB,KACC,IAAIG,CAAA,GAAOH,CAAA,CAAOI,CAAA,OACT,MAATD,CAAA,EACAA,CAAA,GAAOA,CAAA,CAAKO,CAAA,EACX;IACD,IAAMJ,CAAA,GAAeH,CAAA,CAAKU,CAAA,CAAQH,CAAA;IAClC,SAAqB,MAAjBJ,CAAA,EACHH,CAAA,CAAKG,CAAA,GAAgBA,CAAA;IAEtBH,CAAA,CAAKU,CAAA,CAAQH,CAAA,GAAQP,CAAA;IACrBA,CAAA,CAAKH,CAAA,IAAY;IAEjB,SAAyB,MAArBG,CAAA,CAAKO,CAAA,EAA2B;MACnCV,CAAA,CAAOI,CAAA,GAAWD,CAAA;MAClB;IACD;EACD;AACD;AAEA,SAASqC,EAAexC,CAAA;EACvB,IAAIG,CAAA,GAAOH,CAAA,CAAOI,CAAA;IACdE,CAAA,QAAyB;EAO7B,YAAgB,MAATH,CAAA,EAAoB;IAC1B,IAAMK,CAAA,GAAOL,CAAA,CAAKW,CAAA;IAUlB,KAAuB,MAAnBX,CAAA,CAAKH,CAAA,EAAiB;MACzBG,CAAA,CAAKU,CAAA,CAAQW,CAAA,CAAarB,CAAA;MAE1B,SAAa,MAATK,CAAA,EACHA,CAAA,CAAKE,CAAA,GAAcP,CAAA,CAAKO,CAAA;MAEzB,SAAyB,MAArBP,CAAA,CAAKO,CAAA,EACRP,CAAA,CAAKO,CAAA,CAAYI,CAAA,GAAcN,CAEjC;IAAA,OAWCF,CAAA,GAAOH,CAAA;IAGRA,CAAA,CAAKU,CAAA,CAAQH,CAAA,GAAQP,CAAA,CAAKG,CAAA;IAC1B,SAA2B,MAAvBH,CAAA,CAAKG,CAAA,EACRH,CAAA,CAAKG,CAAA,QAAgB;IAGtBH,CAAA,GAAOK,CACR;EAAA;EAEAR,CAAA,CAAOI,CAAA,GAAWE,CACnB;AAAA;AAcA,SAASmC,EAAyBzC,CAAA,EAAmBG,CAAA;EACpDa,CAAA,CAAOO,IAAA,CAAK,WAAM;EAElB,KAAKR,CAAA,GAAMf,CAAA;EACX,KAAKI,CAAA,QAAW;EAChB,KAAKsC,CAAA,GAAiB/B,CAAA,GAAgB;EACtC,KAAKJ,CAAA,GAxiBW;EAyiBhB,KAAKU,CAAA,GAAkB,QAAPd,CAAA,QAAO,IAAPA,CAAA,CAASe,OAAA;EACzB,KAAKC,CAAA,GAAoB,QAAPhB,CAAA,QAAO,IAAPA,CAAA,CAASiB,SAC5B;AAAA;AAEAqB,CAAA,CAASpB,SAAA,GAAY,IAAIL,CAAA;AAEzByB,CAAA,CAASpB,SAAA,CAAUhB,CAAA,GAAW;EAC7B,KAAKE,CAAA,KAAU;EAEf,IApjBe,IAojBX,KAAKA,CAAA,EACR,QACD;EAKA,IAtjBgB,OAsjBI,KAAf,KAAKA,CAAA,GACT;EAED,KAAKA,CAAA,KAAU;EAEf,IAAI,KAAKmC,CAAA,KAAmB/B,CAAA,EAC3B,QAAO;EAER,KAAK+B,CAAA,GAAiB/B,CAAA;EAItB,KAAKJ,CAAA,IAvkBU;EAwkBf,IAAI,KAAKP,CAAA,GAAW,MAAMS,CAAA,CAAiB,OAAO;IACjD,KAAKF,CAAA,KAAU;IACf,QAAO,CACR;EAAA;EAEA,IAAMP,CAAA,GAAcQ,CAAA;EACpB;IACC+B,CAAA,CAAe;IACf/B,CAAA,GAAc;IACd,IAAML,CAAA,GAAQ,KAAKY,CAAA;IACnB,IA9kBgB,KA+kBf,KAAKR,CAAA,IACL,KAAKI,CAAA,KAAWR,CAAA,IACE,MAAlB,KAAKH,CAAA,EACJ;MACD,KAAKW,CAAA,GAASR,CAAA;MACd,KAAKI,CAAA,KAAU;MACf,KAAKP,CAAA,EACN;IAAA;EAKD,CAJE,QAAOA,CAAA;IACR,KAAKW,CAAA,GAASX,CAAA;IACd,KAAKO,CAAA,IAzlBW;IA0lBhB,KAAKP,CAAA,EACN;EAAA;EACAQ,CAAA,GAAcR,CAAA;EACdwC,CAAA,CAAe;EACf,KAAKjC,CAAA,KAAU;EACf,QAAO,CACR;AAAA;AAEAkC,CAAA,CAASpB,SAAA,CAAUR,CAAA,GAAa,UAAUb,CAAA;EACzC,SAAsB,MAAlB,KAAKG,CAAA,EAAwB;IAChC,KAAKI,CAAA,IAAU;IAIf,KACC,IAAIJ,CAAA,GAAO,KAAKC,CAAA,OACP,MAATD,CAAA,EACAA,CAAA,GAAOA,CAAA,CAAKO,CAAA,EAEZP,CAAA,CAAKU,CAAA,CAAQA,CAAA,CAAWV,CAAA,CAE1B;EAAA;EACAa,CAAA,CAAOK,SAAA,CAAUR,CAAA,CAAWU,IAAA,CAAK,MAAMvB,CAAA,CACxC;AAAA;AAEAyC,CAAA,CAASpB,SAAA,CAAUG,CAAA,GAAe,UAAUxB,CAAA;EAE3C,SAAsB,MAAlB,KAAKG,CAAA,EAAwB;IAChCa,CAAA,CAAOK,SAAA,CAAUG,CAAA,CAAaD,IAAA,CAAK,MAAMvB,CAAA;IAIzC,SAAsB,MAAlB,KAAKG,CAAA,EAAwB;MAChC,KAAKI,CAAA,KAAU;MAEf,KACC,IAAIJ,CAAA,GAAO,KAAKC,CAAA,OACP,MAATD,CAAA,EACAA,CAAA,GAAOA,CAAA,CAAKO,CAAA,EAEZP,CAAA,CAAKU,CAAA,CAAQW,CAAA,CAAarB,CAAA,CAE5B;IAAA;EACD;AACD;AAEAsC,CAAA,CAASpB,SAAA,CAAUgB,CAAA,GAAU;EAC5B,MA5oBgB,IA4oBV,KAAK9B,CAAA,GAAoB;IAC9B,KAAKA,CAAA,IAAU;IAEf,KACC,IAAIP,CAAA,GAAO,KAAKG,CAAA,OACP,MAATH,CAAA,EACAA,CAAA,GAAOA,CAAA,CAAKe,CAAA,EAEZf,CAAA,CAAKG,CAAA,CAAQkC,CAAA,EAEf;EAAA;AACD;AAEAL,MAAA,CAAOC,cAAA,CAAeQ,CAAA,CAASpB,SAAA,EAAW,SAAS;EAClDa,GAAA,WAAAA,CAAA;IACC,IA5pBc,IA4pBV,KAAK3B,CAAA,EACR,MAAU,IAAA6B,KAAA,CAAM;IAEjB,IAAMpC,CAAA,GAAOY,CAAA,CAAc;IAC3B,KAAKP,CAAA;IACL,SAAa,MAATL,CAAA,EACHA,CAAA,CAAKA,CAAA,GAAW,KAAKA,CAAA;IAEtB,IAhqBgB,KAgqBZ,KAAKO,CAAA,EACR,MAAU,KAACI,CAAA;IAEZ,OAAW,KAACA,CACb;EAAA;AAAA;AA0BD,SAASgC,EACR3C,CAAA,EACAG,CAAA;EAEA,WAAWsC,CAAA,CAASzC,CAAA,EAAIG,CAAA,CACzB;AAAA;AAEA,SAASyC,EAAc5C,CAAA;EACtB,IAAMM,CAAA,GAAUN,CAAA,CAAOgB,CAAA;EACvBhB,CAAA,CAAOgB,CAAA,QAAW;EAElB,IAAuB,qBAAZV,CAAA,EAAwB;IA7qBnCF,CAAA;IAirBC,IAAMM,CAAA,GAAcF,CAAA;IACpBA,CAAA,QAAc;IACd;MACCF,CAAA,EASD;IAAA,CARE,QAAOH,CAAA;MACRH,CAAA,CAAOO,CAAA,KAAU;MACjBP,CAAA,CAAOO,CAAA,IAptBO;MAqtBdsC,CAAA,CAAc7C,CAAA;MACd,MAAMG,CACP;IAAA,CAAC;MACAK,CAAA,GAAcE,CAAA;MACdP,CAAA,EACD;IAAA;EACD;AACD;AAEA,SAAS0C,EAAc7C,CAAA;EACtB,KACC,IAAIG,CAAA,GAAOH,CAAA,CAAOI,CAAA,OACT,MAATD,CAAA,EACAA,CAAA,GAAOA,CAAA,CAAKO,CAAA,EAEZP,CAAA,CAAKU,CAAA,CAAQW,CAAA,CAAarB,CAAA;EAE3BH,CAAA,CAAOe,CAAA,QAAM;EACbf,CAAA,CAAOI,CAAA,QAAW;EAElBwC,CAAA,CAAc5C,CAAA,CACf;AAAA;AAEA,SAAS0C,EAAwB1C,CAAA;EAChC,IAAIQ,CAAA,KAAgB,MACnB,MAAU,IAAA4B,KAAA,CAAM;EAEjBI,CAAA,CAAe;EACfhC,CAAA,GAAcR,CAAA;EAEd,KAAKO,CAAA,KAAU;EACf,IApvBgB,IAovBZ,KAAKA,CAAA,EACRsC,CAAA,CAAc;EAEf1C,CAAA,EACD;AAAA;AAsBA,SAASW,EAAqBd,CAAA;EAC7B,KAAKe,CAAA,GAAMf,CAAA;EACX,KAAKgB,CAAA,QAAW;EAChB,KAAKZ,CAAA,QAAW;EAChB,KAAKI,CAAA,QAAqB;EAC1B,KAAKD,CAAA,GAjxBW,EAkxBjB;AAAA;AAEAO,CAAA,CAAOO,SAAA,CAAUZ,CAAA,GAAY;EAC5B,IAAMT,CAAA,GAAS,KAAKa,CAAA;EACpB;IACC,IAzxBe,IAyxBX,KAAKN,CAAA,EAAmB;IAC5B,SAAiB,MAAb,KAAKQ,CAAA,EAAmB;IAE5B,IAAMZ,CAAA,GAAU,KAAKY,CAAA;IACrB,IAAuB,qBAAZZ,CAAA,EACV,KAAKa,CAAA,GAAWb,CAIlB;EAAA,CAFC;IACAH,CAAA,EACD;EAAA;AACD;AAEAc,CAAA,CAAOO,SAAA,CAAUR,CAAA,GAAS;EACzB,IAzyBe,IAyyBX,KAAKN,CAAA,EACR,UAAU6B,KAAA,CAAM;EAEjB,KAAK7B,CAAA,IA5yBU;EA6yBf,KAAKA,CAAA,KAAU;EACfqC,CAAA,CAAc;EACdL,CAAA,CAAe;EA/wBfnC,CAAA;EAkxBA,IAAMJ,CAAA,GAAcQ,CAAA;EACpBA,CAAA,GAAc;EACd,OAAOkC,CAAA,CAAUI,IAAA,CAAK,MAAM9C,CAAA,CAC7B;AAAA;AAEAc,CAAA,CAAOO,SAAA,CAAUgB,CAAA,GAAU;EAC1B,MAvzBgB,IAuzBV,KAAK9B,CAAA,GAAoB;IAC9B,KAAKA,CAAA,IAxzBU;IAyzBf,KAAKC,CAAA,GAAqBH,CAAA;IAC1BA,CAAA,GAAgB,IACjB;EAAA;AACD;AAEAS,CAAA,CAAOO,SAAA,CAAUiB,CAAA,GAAW;EAC3B,KAAK/B,CAAA,IA7zBW;EA+zBhB,MAl0Be,IAk0BT,KAAKA,CAAA,GACVsC,CAAA,CAAc,KAEhB;AAAA;AAEA/B,CAAA,CAAOO,SAAA,CAAU0B,OAAA,GAAU;EAC1B,KAAKT,CAAA,EACN;AAAA;AAcA,SAASZ,EAAO1B,CAAA;EACf,IAAMG,CAAA,GAAS,IAAIW,CAAA,CAAOd,CAAA;EAC1B;IACCG,CAAA,CAAOM,CAAA,EAIR;EAAA,CAHE,QAAOT,CAAA;IACRG,CAAA,CAAOmC,CAAA;IACP,MAAMtC,CACP;EAAA;EAGA,IAAMM,CAAA,GAAUH,CAAA,CAAOmC,CAAA,CAASQ,IAAA,CAAK3C,CAAA;EACpCG,CAAA,CAAgBL,MAAA,CAAO8C,OAAA,IAAWzC,CAAA;EACnC,OAAOA,CACR;AAAA;AAAA,SAAAU,CAAA,IAAAgC,MAAA,EAAA1C,CAAA,IAAA2C,KAAA,EAAAN,CAAA,IAAAO,QAAA,EAAAxB,CAAA,IAAAyB,MAAA,EAAAb,CAAA,IAAAc,MAAA,EAAA1C,CAAA,IAAA2C,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}