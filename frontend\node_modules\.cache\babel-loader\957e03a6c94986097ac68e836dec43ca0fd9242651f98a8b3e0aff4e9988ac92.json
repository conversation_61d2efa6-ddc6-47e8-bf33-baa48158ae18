{"ast": null, "code": "// src/index.tsx\nimport React3, { Component as Component3 } from \"react\";\nimport fastCompare from \"react-fast-compare\";\nimport invariant from \"invariant\";\n\n// src/Provider.tsx\nimport React2, { Component } from \"react\";\n\n// src/server.ts\nimport React from \"react\";\n\n// src/constants.ts\nvar TAG_NAMES = /* @__PURE__ */(TAG_NAMES2 => {\n  TAG_NAMES2[\"BASE\"] = \"base\";\n  TAG_NAMES2[\"BODY\"] = \"body\";\n  TAG_NAMES2[\"HEAD\"] = \"head\";\n  TAG_NAMES2[\"HTML\"] = \"html\";\n  TAG_NAMES2[\"LINK\"] = \"link\";\n  TAG_NAMES2[\"META\"] = \"meta\";\n  TAG_NAMES2[\"NOSCRIPT\"] = \"noscript\";\n  TAG_NAMES2[\"SCRIPT\"] = \"script\";\n  TAG_NAMES2[\"STYLE\"] = \"style\";\n  TAG_NAMES2[\"TITLE\"] = \"title\";\n  TAG_NAMES2[\"FRAGMENT\"] = \"Symbol(react.fragment)\";\n  return TAG_NAMES2;\n})(TAG_NAMES || {});\nvar SEO_PRIORITY_TAGS = {\n  link: {\n    rel: [\"amphtml\", \"canonical\", \"alternate\"]\n  },\n  script: {\n    type: [\"application/ld+json\"]\n  },\n  meta: {\n    charset: \"\",\n    name: [\"generator\", \"robots\", \"description\"],\n    property: [\"og:type\", \"og:title\", \"og:url\", \"og:image\", \"og:image:alt\", \"og:description\", \"twitter:url\", \"twitter:title\", \"twitter:description\", \"twitter:image\", \"twitter:image:alt\", \"twitter:card\", \"twitter:site\"]\n  }\n};\nvar VALID_TAG_NAMES = Object.values(TAG_NAMES);\nvar REACT_TAG_MAP = {\n  accesskey: \"accessKey\",\n  charset: \"charSet\",\n  class: \"className\",\n  contenteditable: \"contentEditable\",\n  contextmenu: \"contextMenu\",\n  \"http-equiv\": \"httpEquiv\",\n  itemprop: \"itemProp\",\n  tabindex: \"tabIndex\"\n};\nvar HTML_TAG_MAP = Object.entries(REACT_TAG_MAP).reduce((carry, [key, value]) => {\n  carry[value] = key;\n  return carry;\n}, {});\nvar HELMET_ATTRIBUTE = \"data-rh\";\n\n// src/utils.ts\nvar HELMET_PROPS = {\n  DEFAULT_TITLE: \"defaultTitle\",\n  DEFER: \"defer\",\n  ENCODE_SPECIAL_CHARACTERS: \"encodeSpecialCharacters\",\n  ON_CHANGE_CLIENT_STATE: \"onChangeClientState\",\n  TITLE_TEMPLATE: \"titleTemplate\",\n  PRIORITIZE_SEO_TAGS: \"prioritizeSeoTags\"\n};\nvar getInnermostProperty = (propsList, property) => {\n  for (let i = propsList.length - 1; i >= 0; i -= 1) {\n    const props = propsList[i];\n    if (Object.prototype.hasOwnProperty.call(props, property)) {\n      return props[property];\n    }\n  }\n  return null;\n};\nvar getTitleFromPropsList = propsList => {\n  let innermostTitle = getInnermostProperty(propsList, \"title\" /* TITLE */);\n  const innermostTemplate = getInnermostProperty(propsList, HELMET_PROPS.TITLE_TEMPLATE);\n  if (Array.isArray(innermostTitle)) {\n    innermostTitle = innermostTitle.join(\"\");\n  }\n  if (innermostTemplate && innermostTitle) {\n    return innermostTemplate.replace(/%s/g, () => innermostTitle);\n  }\n  const innermostDefaultTitle = getInnermostProperty(propsList, HELMET_PROPS.DEFAULT_TITLE);\n  return innermostTitle || innermostDefaultTitle || void 0;\n};\nvar getOnChangeClientState = propsList => getInnermostProperty(propsList, HELMET_PROPS.ON_CHANGE_CLIENT_STATE) || (() => {});\nvar getAttributesFromPropsList = (tagType, propsList) => propsList.filter(props => typeof props[tagType] !== \"undefined\").map(props => props[tagType]).reduce((tagAttrs, current) => ({\n  ...tagAttrs,\n  ...current\n}), {});\nvar getBaseTagFromPropsList = (primaryAttributes, propsList) => propsList.filter(props => typeof props[\"base\" /* BASE */] !== \"undefined\").map(props => props[\"base\" /* BASE */]).reverse().reduce((innermostBaseTag, tag) => {\n  if (!innermostBaseTag.length) {\n    const keys = Object.keys(tag);\n    for (let i = 0; i < keys.length; i += 1) {\n      const attributeKey = keys[i];\n      const lowerCaseAttributeKey = attributeKey.toLowerCase();\n      if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && tag[lowerCaseAttributeKey]) {\n        return innermostBaseTag.concat(tag);\n      }\n    }\n  }\n  return innermostBaseTag;\n}, []);\nvar warn = msg => console && typeof console.warn === \"function\" && console.warn(msg);\nvar getTagsFromPropsList = (tagName, primaryAttributes, propsList) => {\n  const approvedSeenTags = {};\n  return propsList.filter(props => {\n    if (Array.isArray(props[tagName])) {\n      return true;\n    }\n    if (typeof props[tagName] !== \"undefined\") {\n      warn(`Helmet: ${tagName} should be of type \"Array\". Instead found type \"${typeof props[tagName]}\"`);\n    }\n    return false;\n  }).map(props => props[tagName]).reverse().reduce((approvedTags, instanceTags) => {\n    const instanceSeenTags = {};\n    instanceTags.filter(tag => {\n      let primaryAttributeKey;\n      const keys2 = Object.keys(tag);\n      for (let i = 0; i < keys2.length; i += 1) {\n        const attributeKey = keys2[i];\n        const lowerCaseAttributeKey = attributeKey.toLowerCase();\n        if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && !(primaryAttributeKey === \"rel\" /* REL */ && tag[primaryAttributeKey].toLowerCase() === \"canonical\") && !(lowerCaseAttributeKey === \"rel\" /* REL */ && tag[lowerCaseAttributeKey].toLowerCase() === \"stylesheet\")) {\n          primaryAttributeKey = lowerCaseAttributeKey;\n        }\n        if (primaryAttributes.indexOf(attributeKey) !== -1 && (attributeKey === \"innerHTML\" /* INNER_HTML */ || attributeKey === \"cssText\" /* CSS_TEXT */ || attributeKey === \"itemprop\" /* ITEM_PROP */)) {\n          primaryAttributeKey = attributeKey;\n        }\n      }\n      if (!primaryAttributeKey || !tag[primaryAttributeKey]) {\n        return false;\n      }\n      const value = tag[primaryAttributeKey].toLowerCase();\n      if (!approvedSeenTags[primaryAttributeKey]) {\n        approvedSeenTags[primaryAttributeKey] = {};\n      }\n      if (!instanceSeenTags[primaryAttributeKey]) {\n        instanceSeenTags[primaryAttributeKey] = {};\n      }\n      if (!approvedSeenTags[primaryAttributeKey][value]) {\n        instanceSeenTags[primaryAttributeKey][value] = true;\n        return true;\n      }\n      return false;\n    }).reverse().forEach(tag => approvedTags.push(tag));\n    const keys = Object.keys(instanceSeenTags);\n    for (let i = 0; i < keys.length; i += 1) {\n      const attributeKey = keys[i];\n      const tagUnion = {\n        ...approvedSeenTags[attributeKey],\n        ...instanceSeenTags[attributeKey]\n      };\n      approvedSeenTags[attributeKey] = tagUnion;\n    }\n    return approvedTags;\n  }, []).reverse();\n};\nvar getAnyTrueFromPropsList = (propsList, checkedTag) => {\n  if (Array.isArray(propsList) && propsList.length) {\n    for (let index = 0; index < propsList.length; index += 1) {\n      const prop = propsList[index];\n      if (prop[checkedTag]) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\nvar reducePropsToState = propsList => ({\n  baseTag: getBaseTagFromPropsList([\"href\" /* HREF */], propsList),\n  bodyAttributes: getAttributesFromPropsList(\"bodyAttributes\" /* BODY */, propsList),\n  defer: getInnermostProperty(propsList, HELMET_PROPS.DEFER),\n  encode: getInnermostProperty(propsList, HELMET_PROPS.ENCODE_SPECIAL_CHARACTERS),\n  htmlAttributes: getAttributesFromPropsList(\"htmlAttributes\" /* HTML */, propsList),\n  linkTags: getTagsFromPropsList(\"link\" /* LINK */, [\"rel\" /* REL */, \"href\" /* HREF */], propsList),\n  metaTags: getTagsFromPropsList(\"meta\" /* META */, [\"name\" /* NAME */, \"charset\" /* CHARSET */, \"http-equiv\" /* HTTPEQUIV */, \"property\" /* PROPERTY */, \"itemprop\" /* ITEM_PROP */], propsList),\n  noscriptTags: getTagsFromPropsList(\"noscript\" /* NOSCRIPT */, [\"innerHTML\" /* INNER_HTML */], propsList),\n  onChangeClientState: getOnChangeClientState(propsList),\n  scriptTags: getTagsFromPropsList(\"script\" /* SCRIPT */, [\"src\" /* SRC */, \"innerHTML\" /* INNER_HTML */], propsList),\n  styleTags: getTagsFromPropsList(\"style\" /* STYLE */, [\"cssText\" /* CSS_TEXT */], propsList),\n  title: getTitleFromPropsList(propsList),\n  titleAttributes: getAttributesFromPropsList(\"titleAttributes\" /* TITLE */, propsList),\n  prioritizeSeoTags: getAnyTrueFromPropsList(propsList, HELMET_PROPS.PRIORITIZE_SEO_TAGS)\n});\nvar flattenArray = possibleArray => Array.isArray(possibleArray) ? possibleArray.join(\"\") : possibleArray;\nvar checkIfPropsMatch = (props, toMatch) => {\n  const keys = Object.keys(props);\n  for (let i = 0; i < keys.length; i += 1) {\n    if (toMatch[keys[i]] && toMatch[keys[i]].includes(props[keys[i]])) {\n      return true;\n    }\n  }\n  return false;\n};\nvar prioritizer = (elementsList, propsToMatch) => {\n  if (Array.isArray(elementsList)) {\n    return elementsList.reduce((acc, elementAttrs) => {\n      if (checkIfPropsMatch(elementAttrs, propsToMatch)) {\n        acc.priority.push(elementAttrs);\n      } else {\n        acc.default.push(elementAttrs);\n      }\n      return acc;\n    }, {\n      priority: [],\n      default: []\n    });\n  }\n  return {\n    default: elementsList,\n    priority: []\n  };\n};\nvar without = (obj, key) => {\n  return {\n    ...obj,\n    [key]: void 0\n  };\n};\n\n// src/server.ts\nvar SELF_CLOSING_TAGS = [\"noscript\" /* NOSCRIPT */, \"script\" /* SCRIPT */, \"style\" /* STYLE */];\nvar encodeSpecialCharacters = (str, encode = true) => {\n  if (encode === false) {\n    return String(str);\n  }\n  return String(str).replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#x27;\");\n};\nvar generateElementAttributesAsString = attributes => Object.keys(attributes).reduce((str, key) => {\n  const attr = typeof attributes[key] !== \"undefined\" ? `${key}=\"${attributes[key]}\"` : `${key}`;\n  return str ? `${str} ${attr}` : attr;\n}, \"\");\nvar generateTitleAsString = (type, title, attributes, encode) => {\n  const attributeString = generateElementAttributesAsString(attributes);\n  const flattenedTitle = flattenArray(title);\n  return attributeString ? `<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeString}>${encodeSpecialCharacters(flattenedTitle, encode)}</${type}>` : `<${type} ${HELMET_ATTRIBUTE}=\"true\">${encodeSpecialCharacters(flattenedTitle, encode)}</${type}>`;\n};\nvar generateTagsAsString = (type, tags, encode = true) => tags.reduce((str, t) => {\n  const tag = t;\n  const attributeHtml = Object.keys(tag).filter(attribute => !(attribute === \"innerHTML\" /* INNER_HTML */ || attribute === \"cssText\" /* CSS_TEXT */)).reduce((string, attribute) => {\n    const attr = typeof tag[attribute] === \"undefined\" ? attribute : `${attribute}=\"${encodeSpecialCharacters(tag[attribute], encode)}\"`;\n    return string ? `${string} ${attr}` : attr;\n  }, \"\");\n  const tagContent = tag.innerHTML || tag.cssText || \"\";\n  const isSelfClosing = SELF_CLOSING_TAGS.indexOf(type) === -1;\n  return `${str}<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeHtml}${isSelfClosing ? `/>` : `>${tagContent}</${type}>`}`;\n}, \"\");\nvar convertElementAttributesToReactProps = (attributes, initProps = {}) => Object.keys(attributes).reduce((obj, key) => {\n  const mapped = REACT_TAG_MAP[key];\n  obj[mapped || key] = attributes[key];\n  return obj;\n}, initProps);\nvar generateTitleAsReactComponent = (_type, title, attributes) => {\n  const initProps = {\n    key: title,\n    [HELMET_ATTRIBUTE]: true\n  };\n  const props = convertElementAttributesToReactProps(attributes, initProps);\n  return [React.createElement(\"title\" /* TITLE */, props, title)];\n};\nvar generateTagsAsReactComponent = (type, tags) => tags.map((tag, i) => {\n  const mappedTag = {\n    key: i,\n    [HELMET_ATTRIBUTE]: true\n  };\n  Object.keys(tag).forEach(attribute => {\n    const mapped = REACT_TAG_MAP[attribute];\n    const mappedAttribute = mapped || attribute;\n    if (mappedAttribute === \"innerHTML\" /* INNER_HTML */ || mappedAttribute === \"cssText\" /* CSS_TEXT */) {\n      const content = tag.innerHTML || tag.cssText;\n      mappedTag.dangerouslySetInnerHTML = {\n        __html: content\n      };\n    } else {\n      mappedTag[mappedAttribute] = tag[attribute];\n    }\n  });\n  return React.createElement(type, mappedTag);\n});\nvar getMethodsForTag = (type, tags, encode = true) => {\n  switch (type) {\n    case \"title\" /* TITLE */:\n      return {\n        toComponent: () => generateTitleAsReactComponent(type, tags.title, tags.titleAttributes),\n        toString: () => generateTitleAsString(type, tags.title, tags.titleAttributes, encode)\n      };\n    case \"bodyAttributes\" /* BODY */:\n    case \"htmlAttributes\" /* HTML */:\n      return {\n        toComponent: () => convertElementAttributesToReactProps(tags),\n        toString: () => generateElementAttributesAsString(tags)\n      };\n    default:\n      return {\n        toComponent: () => generateTagsAsReactComponent(type, tags),\n        toString: () => generateTagsAsString(type, tags, encode)\n      };\n  }\n};\nvar getPriorityMethods = ({\n  metaTags,\n  linkTags,\n  scriptTags,\n  encode\n}) => {\n  const meta = prioritizer(metaTags, SEO_PRIORITY_TAGS.meta);\n  const link = prioritizer(linkTags, SEO_PRIORITY_TAGS.link);\n  const script = prioritizer(scriptTags, SEO_PRIORITY_TAGS.script);\n  const priorityMethods = {\n    toComponent: () => [...generateTagsAsReactComponent(\"meta\" /* META */, meta.priority), ...generateTagsAsReactComponent(\"link\" /* LINK */, link.priority), ...generateTagsAsReactComponent(\"script\" /* SCRIPT */, script.priority)],\n    toString: () =>\n    // generate all the tags as strings and concatenate them\n    `${getMethodsForTag(\"meta\" /* META */, meta.priority, encode)} ${getMethodsForTag(\"link\" /* LINK */, link.priority, encode)} ${getMethodsForTag(\"script\" /* SCRIPT */, script.priority, encode)}`\n  };\n  return {\n    priorityMethods,\n    metaTags: meta.default,\n    linkTags: link.default,\n    scriptTags: script.default\n  };\n};\nvar mapStateOnServer = props => {\n  const {\n    baseTag,\n    bodyAttributes,\n    encode = true,\n    htmlAttributes,\n    noscriptTags,\n    styleTags,\n    title = \"\",\n    titleAttributes,\n    prioritizeSeoTags\n  } = props;\n  let {\n    linkTags,\n    metaTags,\n    scriptTags\n  } = props;\n  let priorityMethods = {\n    toComponent: () => {},\n    toString: () => \"\"\n  };\n  if (prioritizeSeoTags) {\n    ({\n      priorityMethods,\n      linkTags,\n      metaTags,\n      scriptTags\n    } = getPriorityMethods(props));\n  }\n  return {\n    priority: priorityMethods,\n    base: getMethodsForTag(\"base\" /* BASE */, baseTag, encode),\n    bodyAttributes: getMethodsForTag(\"bodyAttributes\" /* BODY */, bodyAttributes, encode),\n    htmlAttributes: getMethodsForTag(\"htmlAttributes\" /* HTML */, htmlAttributes, encode),\n    link: getMethodsForTag(\"link\" /* LINK */, linkTags, encode),\n    meta: getMethodsForTag(\"meta\" /* META */, metaTags, encode),\n    noscript: getMethodsForTag(\"noscript\" /* NOSCRIPT */, noscriptTags, encode),\n    script: getMethodsForTag(\"script\" /* SCRIPT */, scriptTags, encode),\n    style: getMethodsForTag(\"style\" /* STYLE */, styleTags, encode),\n    title: getMethodsForTag(\"title\" /* TITLE */, {\n      title,\n      titleAttributes\n    }, encode)\n  };\n};\nvar server_default = mapStateOnServer;\n\n// src/HelmetData.ts\nvar instances = [];\nvar isDocument = !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\nvar HelmetData = class {\n  instances = [];\n  canUseDOM = isDocument;\n  context;\n  value = {\n    setHelmet: serverState => {\n      this.context.helmet = serverState;\n    },\n    helmetInstances: {\n      get: () => this.canUseDOM ? instances : this.instances,\n      add: instance => {\n        (this.canUseDOM ? instances : this.instances).push(instance);\n      },\n      remove: instance => {\n        const index = (this.canUseDOM ? instances : this.instances).indexOf(instance);\n        (this.canUseDOM ? instances : this.instances).splice(index, 1);\n      }\n    }\n  };\n  constructor(context, canUseDOM) {\n    this.context = context;\n    this.canUseDOM = canUseDOM || false;\n    if (!canUseDOM) {\n      context.helmet = server_default({\n        baseTag: [],\n        bodyAttributes: {},\n        encodeSpecialCharacters: true,\n        htmlAttributes: {},\n        linkTags: [],\n        metaTags: [],\n        noscriptTags: [],\n        scriptTags: [],\n        styleTags: [],\n        title: \"\",\n        titleAttributes: {}\n      });\n    }\n  }\n};\n\n// src/Provider.tsx\nvar defaultValue = {};\nvar Context = React2.createContext(defaultValue);\nvar HelmetProvider = class _HelmetProvider extends Component {\n  static canUseDOM = isDocument;\n  helmetData;\n  constructor(props) {\n    super(props);\n    this.helmetData = new HelmetData(this.props.context || {}, _HelmetProvider.canUseDOM);\n  }\n  render() {\n    return /* @__PURE__ */React2.createElement(Context.Provider, {\n      value: this.helmetData.value\n    }, this.props.children);\n  }\n};\n\n// src/Dispatcher.tsx\nimport { Component as Component2 } from \"react\";\nimport shallowEqual from \"shallowequal\";\n\n// src/client.ts\nvar updateTags = (type, tags) => {\n  const headElement = document.head || document.querySelector(\"head\" /* HEAD */);\n  const tagNodes = headElement.querySelectorAll(`${type}[${HELMET_ATTRIBUTE}]`);\n  const oldTags = [].slice.call(tagNodes);\n  const newTags = [];\n  let indexToDelete;\n  if (tags && tags.length) {\n    tags.forEach(tag => {\n      const newElement = document.createElement(type);\n      for (const attribute in tag) {\n        if (Object.prototype.hasOwnProperty.call(tag, attribute)) {\n          if (attribute === \"innerHTML\" /* INNER_HTML */) {\n            newElement.innerHTML = tag.innerHTML;\n          } else if (attribute === \"cssText\" /* CSS_TEXT */) {\n            if (newElement.styleSheet) {\n              newElement.styleSheet.cssText = tag.cssText;\n            } else {\n              newElement.appendChild(document.createTextNode(tag.cssText));\n            }\n          } else {\n            const attr = attribute;\n            const value = typeof tag[attr] === \"undefined\" ? \"\" : tag[attr];\n            newElement.setAttribute(attribute, value);\n          }\n        }\n      }\n      newElement.setAttribute(HELMET_ATTRIBUTE, \"true\");\n      if (oldTags.some((existingTag, index) => {\n        indexToDelete = index;\n        return newElement.isEqualNode(existingTag);\n      })) {\n        oldTags.splice(indexToDelete, 1);\n      } else {\n        newTags.push(newElement);\n      }\n    });\n  }\n  oldTags.forEach(tag => tag.parentNode?.removeChild(tag));\n  newTags.forEach(tag => headElement.appendChild(tag));\n  return {\n    oldTags,\n    newTags\n  };\n};\nvar updateAttributes = (tagName, attributes) => {\n  const elementTag = document.getElementsByTagName(tagName)[0];\n  if (!elementTag) {\n    return;\n  }\n  const helmetAttributeString = elementTag.getAttribute(HELMET_ATTRIBUTE);\n  const helmetAttributes = helmetAttributeString ? helmetAttributeString.split(\",\") : [];\n  const attributesToRemove = [...helmetAttributes];\n  const attributeKeys = Object.keys(attributes);\n  for (const attribute of attributeKeys) {\n    const value = attributes[attribute] || \"\";\n    if (elementTag.getAttribute(attribute) !== value) {\n      elementTag.setAttribute(attribute, value);\n    }\n    if (helmetAttributes.indexOf(attribute) === -1) {\n      helmetAttributes.push(attribute);\n    }\n    const indexToSave = attributesToRemove.indexOf(attribute);\n    if (indexToSave !== -1) {\n      attributesToRemove.splice(indexToSave, 1);\n    }\n  }\n  for (let i = attributesToRemove.length - 1; i >= 0; i -= 1) {\n    elementTag.removeAttribute(attributesToRemove[i]);\n  }\n  if (helmetAttributes.length === attributesToRemove.length) {\n    elementTag.removeAttribute(HELMET_ATTRIBUTE);\n  } else if (elementTag.getAttribute(HELMET_ATTRIBUTE) !== attributeKeys.join(\",\")) {\n    elementTag.setAttribute(HELMET_ATTRIBUTE, attributeKeys.join(\",\"));\n  }\n};\nvar updateTitle = (title, attributes) => {\n  if (typeof title !== \"undefined\" && document.title !== title) {\n    document.title = flattenArray(title);\n  }\n  updateAttributes(\"title\" /* TITLE */, attributes);\n};\nvar commitTagChanges = (newState, cb) => {\n  const {\n    baseTag,\n    bodyAttributes,\n    htmlAttributes,\n    linkTags,\n    metaTags,\n    noscriptTags,\n    onChangeClientState,\n    scriptTags,\n    styleTags,\n    title,\n    titleAttributes\n  } = newState;\n  updateAttributes(\"body\" /* BODY */, bodyAttributes);\n  updateAttributes(\"html\" /* HTML */, htmlAttributes);\n  updateTitle(title, titleAttributes);\n  const tagUpdates = {\n    baseTag: updateTags(\"base\" /* BASE */, baseTag),\n    linkTags: updateTags(\"link\" /* LINK */, linkTags),\n    metaTags: updateTags(\"meta\" /* META */, metaTags),\n    noscriptTags: updateTags(\"noscript\" /* NOSCRIPT */, noscriptTags),\n    scriptTags: updateTags(\"script\" /* SCRIPT */, scriptTags),\n    styleTags: updateTags(\"style\" /* STYLE */, styleTags)\n  };\n  const addedTags = {};\n  const removedTags = {};\n  Object.keys(tagUpdates).forEach(tagType => {\n    const {\n      newTags,\n      oldTags\n    } = tagUpdates[tagType];\n    if (newTags.length) {\n      addedTags[tagType] = newTags;\n    }\n    if (oldTags.length) {\n      removedTags[tagType] = tagUpdates[tagType].oldTags;\n    }\n  });\n  if (cb) {\n    cb();\n  }\n  onChangeClientState(newState, addedTags, removedTags);\n};\nvar _helmetCallback = null;\nvar handleStateChangeOnClient = newState => {\n  if (_helmetCallback) {\n    cancelAnimationFrame(_helmetCallback);\n  }\n  if (newState.defer) {\n    _helmetCallback = requestAnimationFrame(() => {\n      commitTagChanges(newState, () => {\n        _helmetCallback = null;\n      });\n    });\n  } else {\n    commitTagChanges(newState);\n    _helmetCallback = null;\n  }\n};\nvar client_default = handleStateChangeOnClient;\n\n// src/Dispatcher.tsx\nvar HelmetDispatcher = class extends Component2 {\n  rendered = false;\n  shouldComponentUpdate(nextProps) {\n    return !shallowEqual(nextProps, this.props);\n  }\n  componentDidUpdate() {\n    this.emitChange();\n  }\n  componentWillUnmount() {\n    const {\n      helmetInstances\n    } = this.props.context;\n    helmetInstances.remove(this);\n    this.emitChange();\n  }\n  emitChange() {\n    const {\n      helmetInstances,\n      setHelmet\n    } = this.props.context;\n    let serverState = null;\n    const state = reducePropsToState(helmetInstances.get().map(instance => {\n      const props = {\n        ...instance.props\n      };\n      delete props.context;\n      return props;\n    }));\n    if (HelmetProvider.canUseDOM) {\n      client_default(state);\n    } else if (server_default) {\n      serverState = server_default(state);\n    }\n    setHelmet(serverState);\n  }\n  // componentWillMount will be deprecated\n  // for SSR, initialize on first render\n  // constructor is also unsafe in StrictMode\n  init() {\n    if (this.rendered) {\n      return;\n    }\n    this.rendered = true;\n    const {\n      helmetInstances\n    } = this.props.context;\n    helmetInstances.add(this);\n    this.emitChange();\n  }\n  render() {\n    this.init();\n    return null;\n  }\n};\n\n// src/index.tsx\nvar Helmet = class extends Component3 {\n  static defaultProps = {\n    defer: true,\n    encodeSpecialCharacters: true,\n    prioritizeSeoTags: false\n  };\n  shouldComponentUpdate(nextProps) {\n    return !fastCompare(without(this.props, \"helmetData\"), without(nextProps, \"helmetData\"));\n  }\n  mapNestedChildrenToProps(child, nestedChildren) {\n    if (!nestedChildren) {\n      return null;\n    }\n    switch (child.type) {\n      case \"script\" /* SCRIPT */:\n      case \"noscript\" /* NOSCRIPT */:\n        return {\n          innerHTML: nestedChildren\n        };\n      case \"style\" /* STYLE */:\n        return {\n          cssText: nestedChildren\n        };\n      default:\n        throw new Error(`<${child.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`);\n    }\n  }\n  flattenArrayTypeChildren(child, arrayTypeChildren, newChildProps, nestedChildren) {\n    return {\n      ...arrayTypeChildren,\n      [child.type]: [...(arrayTypeChildren[child.type] || []), {\n        ...newChildProps,\n        ...this.mapNestedChildrenToProps(child, nestedChildren)\n      }]\n    };\n  }\n  mapObjectTypeChildren(child, newProps, newChildProps, nestedChildren) {\n    switch (child.type) {\n      case \"title\" /* TITLE */:\n        return {\n          ...newProps,\n          [child.type]: nestedChildren,\n          titleAttributes: {\n            ...newChildProps\n          }\n        };\n      case \"body\" /* BODY */:\n        return {\n          ...newProps,\n          bodyAttributes: {\n            ...newChildProps\n          }\n        };\n      case \"html\" /* HTML */:\n        return {\n          ...newProps,\n          htmlAttributes: {\n            ...newChildProps\n          }\n        };\n      default:\n        return {\n          ...newProps,\n          [child.type]: {\n            ...newChildProps\n          }\n        };\n    }\n  }\n  mapArrayTypeChildrenToProps(arrayTypeChildren, newProps) {\n    let newFlattenedProps = {\n      ...newProps\n    };\n    Object.keys(arrayTypeChildren).forEach(arrayChildName => {\n      newFlattenedProps = {\n        ...newFlattenedProps,\n        [arrayChildName]: arrayTypeChildren[arrayChildName]\n      };\n    });\n    return newFlattenedProps;\n  }\n  warnOnInvalidChildren(child, nestedChildren) {\n    invariant(VALID_TAG_NAMES.some(name => child.type === name), typeof child.type === \"function\" ? `You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.` : `Only elements types ${VALID_TAG_NAMES.join(\", \")} are allowed. Helmet does not support rendering <${child.type}> elements. Refer to our API for more information.`);\n    invariant(!nestedChildren || typeof nestedChildren === \"string\" || Array.isArray(nestedChildren) && !nestedChildren.some(nestedChild => typeof nestedChild !== \"string\"), `Helmet expects a string as a child of <${child.type}>. Did you forget to wrap your children in braces? ( <${child.type}>{\\`\\`}</${child.type}> ) Refer to our API for more information.`);\n    return true;\n  }\n  mapChildrenToProps(children, newProps) {\n    let arrayTypeChildren = {};\n    React3.Children.forEach(children, child => {\n      if (!child || !child.props) {\n        return;\n      }\n      const {\n        children: nestedChildren,\n        ...childProps\n      } = child.props;\n      const newChildProps = Object.keys(childProps).reduce((obj, key) => {\n        obj[HTML_TAG_MAP[key] || key] = childProps[key];\n        return obj;\n      }, {});\n      let {\n        type\n      } = child;\n      if (typeof type === \"symbol\") {\n        type = type.toString();\n      } else {\n        this.warnOnInvalidChildren(child, nestedChildren);\n      }\n      switch (type) {\n        case \"Symbol(react.fragment)\" /* FRAGMENT */:\n          newProps = this.mapChildrenToProps(nestedChildren, newProps);\n          break;\n        case \"link\" /* LINK */:\n        case \"meta\" /* META */:\n        case \"noscript\" /* NOSCRIPT */:\n        case \"script\" /* SCRIPT */:\n        case \"style\" /* STYLE */:\n          arrayTypeChildren = this.flattenArrayTypeChildren(child, arrayTypeChildren, newChildProps, nestedChildren);\n          break;\n        default:\n          newProps = this.mapObjectTypeChildren(child, newProps, newChildProps, nestedChildren);\n          break;\n      }\n    });\n    return this.mapArrayTypeChildrenToProps(arrayTypeChildren, newProps);\n  }\n  render() {\n    const {\n      children,\n      ...props\n    } = this.props;\n    let newProps = {\n      ...props\n    };\n    let {\n      helmetData\n    } = props;\n    if (children) {\n      newProps = this.mapChildrenToProps(children, newProps);\n    }\n    if (helmetData && !(helmetData instanceof HelmetData)) {\n      const data = helmetData;\n      helmetData = new HelmetData(data.context, true);\n      delete newProps.helmetData;\n    }\n    return helmetData ? /* @__PURE__ */React3.createElement(HelmetDispatcher, {\n      ...newProps,\n      context: helmetData.value\n    }) : /* @__PURE__ */React3.createElement(Context.Consumer, null, context => /* @__PURE__ */React3.createElement(HelmetDispatcher, {\n      ...newProps,\n      context\n    }));\n  }\n};\nexport { Helmet, HelmetData, HelmetProvider };", "map": {"version": 3, "names": ["React3", "Component", "Component3", "fastCompare", "invariant", "React2", "React", "TAG_NAMES", "TAG_NAMES2", "SEO_PRIORITY_TAGS", "link", "rel", "script", "type", "meta", "charset", "name", "property", "VALID_TAG_NAMES", "Object", "values", "REACT_TAG_MAP", "accesskey", "class", "contenteditable", "contextmenu", "itemprop", "tabindex", "HTML_TAG_MAP", "entries", "reduce", "carry", "key", "value", "HELMET_ATTRIBUTE", "HELMET_PROPS", "DEFAULT_TITLE", "DEFER", "ENCODE_SPECIAL_CHARACTERS", "ON_CHANGE_CLIENT_STATE", "TITLE_TEMPLATE", "PRIORITIZE_SEO_TAGS", "getInnermostProperty", "propsList", "i", "length", "props", "prototype", "hasOwnProperty", "call", "getTitleFromPropsList", "innermostTitle", "innermostTemplate", "Array", "isArray", "join", "replace", "innermostDefaultTitle", "getOnChangeClientState", "getAttributesFromPropsList", "tagType", "filter", "map", "tagAttrs", "current", "getBaseTagFromPropsList", "primaryAttributes", "reverse", "innermostBaseTag", "tag", "keys", "<PERSON><PERSON><PERSON>", "lowerCaseAttributeKey", "toLowerCase", "indexOf", "concat", "warn", "msg", "console", "getTagsFromPropsList", "tagName", "approvedSeenTags", "approvedTags", "instanceTags", "instanceSeenTags", "primaryAttributeKey", "keys2", "for<PERSON>ach", "push", "tagUnion", "getAnyTrueFromPropsList", "checkedTag", "index", "prop", "reducePropsToState", "baseTag", "bodyAttributes", "defer", "encode", "htmlAttributes", "linkTags", "metaTags", "noscriptTags", "onChangeClientState", "scriptTags", "styleTags", "title", "titleAttributes", "prioritizeSeoTags", "flattenArray", "possible<PERSON><PERSON>y", "checkIfPropsMatch", "toMatch", "includes", "prioritizer", "elementsList", "propsToMatch", "acc", "elementAttrs", "priority", "default", "without", "obj", "SELF_CLOSING_TAGS", "encodeSpecialCharacters", "str", "String", "generateElementAttributesAsString", "attributes", "attr", "generateTitleAsString", "attributeString", "flattenedTitle", "generateTagsAsString", "tags", "t", "attributeHtml", "attribute", "string", "tagContent", "innerHTML", "cssText", "isSelfClosing", "convertElementAttributesToReactProps", "initProps", "mapped", "generateTitleAsReactComponent", "_type", "createElement", "generateTagsAsReactComponent", "mappedTag", "mappedAttribute", "content", "dangerouslySetInnerHTML", "__html", "getMethodsForTag", "toComponent", "toString", "getPriorityMethods", "priorityMethods", "mapStateOnServer", "base", "noscript", "style", "server_default", "instances", "isDocument", "window", "document", "HelmetData", "canUseDOM", "context", "setHelmet", "serverState", "helmet", "helmetInstances", "get", "add", "instance", "remove", "splice", "constructor", "defaultValue", "Context", "createContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_He<PERSON><PERSON><PERSON><PERSON><PERSON>", "helmetData", "render", "Provider", "children", "Component2", "shallowEqual", "updateTags", "headElement", "head", "querySelector", "tagNodes", "querySelectorAll", "oldTags", "slice", "newTags", "indexToDelete", "newElement", "styleSheet", "append<PERSON><PERSON><PERSON>", "createTextNode", "setAttribute", "some", "existingTag", "isEqualNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "updateAttributes", "elementTag", "getElementsByTagName", "helmetAttributeString", "getAttribute", "helmetAttributes", "split", "attributesToRemove", "<PERSON><PERSON><PERSON><PERSON>", "indexToSave", "removeAttribute", "updateTitle", "commitTagChanges", "newState", "cb", "tagUpdates", "addedTags", "removedTags", "_helmet<PERSON><PERSON><PERSON>", "handleStateChangeOnClient", "cancelAnimationFrame", "requestAnimationFrame", "client_default", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rendered", "shouldComponentUpdate", "nextProps", "componentDidUpdate", "emitChange", "componentWillUnmount", "state", "init", "<PERSON><PERSON><PERSON>", "defaultProps", "mapNestedChildrenToProps", "child", "nested<PERSON><PERSON><PERSON><PERSON>", "Error", "flattenArrayTypeChildren", "arrayTypeChildren", "newChildProps", "mapObjectTypeChildren", "newProps", "mapArrayTypeChildrenToProps", "newFlattenedProps", "arrayChildName", "warnOnInvalidChildren", "nested<PERSON><PERSON><PERSON>", "mapChildrenToProps", "Children", "childProps", "data", "Consumer"], "sources": ["C:/laragon/www/frontend/node_modules/react-helmet-async/lib/index.esm.js"], "sourcesContent": ["// src/index.tsx\nimport React3, { Component as Component3 } from \"react\";\nimport fastCompare from \"react-fast-compare\";\nimport invariant from \"invariant\";\n\n// src/Provider.tsx\nimport React2, { Component } from \"react\";\n\n// src/server.ts\nimport React from \"react\";\n\n// src/constants.ts\nvar TAG_NAMES = /* @__PURE__ */ ((TAG_NAMES2) => {\n  TAG_NAMES2[\"BASE\"] = \"base\";\n  TAG_NAMES2[\"BODY\"] = \"body\";\n  TAG_NAMES2[\"HEAD\"] = \"head\";\n  TAG_NAMES2[\"HTML\"] = \"html\";\n  TAG_NAMES2[\"LINK\"] = \"link\";\n  TAG_NAMES2[\"META\"] = \"meta\";\n  TAG_NAMES2[\"NOSCRIPT\"] = \"noscript\";\n  TAG_NAMES2[\"SCRIPT\"] = \"script\";\n  TAG_NAMES2[\"STYLE\"] = \"style\";\n  TAG_NAMES2[\"TITLE\"] = \"title\";\n  TAG_NAMES2[\"FRAGMENT\"] = \"Symbol(react.fragment)\";\n  return TAG_NAMES2;\n})(TAG_NAMES || {});\nvar SEO_PRIORITY_TAGS = {\n  link: { rel: [\"amphtml\", \"canonical\", \"alternate\"] },\n  script: { type: [\"application/ld+json\"] },\n  meta: {\n    charset: \"\",\n    name: [\"generator\", \"robots\", \"description\"],\n    property: [\n      \"og:type\",\n      \"og:title\",\n      \"og:url\",\n      \"og:image\",\n      \"og:image:alt\",\n      \"og:description\",\n      \"twitter:url\",\n      \"twitter:title\",\n      \"twitter:description\",\n      \"twitter:image\",\n      \"twitter:image:alt\",\n      \"twitter:card\",\n      \"twitter:site\"\n    ]\n  }\n};\nvar VALID_TAG_NAMES = Object.values(TAG_NAMES);\nvar REACT_TAG_MAP = {\n  accesskey: \"accessKey\",\n  charset: \"charSet\",\n  class: \"className\",\n  contenteditable: \"contentEditable\",\n  contextmenu: \"contextMenu\",\n  \"http-equiv\": \"httpEquiv\",\n  itemprop: \"itemProp\",\n  tabindex: \"tabIndex\"\n};\nvar HTML_TAG_MAP = Object.entries(REACT_TAG_MAP).reduce(\n  (carry, [key, value]) => {\n    carry[value] = key;\n    return carry;\n  },\n  {}\n);\nvar HELMET_ATTRIBUTE = \"data-rh\";\n\n// src/utils.ts\nvar HELMET_PROPS = {\n  DEFAULT_TITLE: \"defaultTitle\",\n  DEFER: \"defer\",\n  ENCODE_SPECIAL_CHARACTERS: \"encodeSpecialCharacters\",\n  ON_CHANGE_CLIENT_STATE: \"onChangeClientState\",\n  TITLE_TEMPLATE: \"titleTemplate\",\n  PRIORITIZE_SEO_TAGS: \"prioritizeSeoTags\"\n};\nvar getInnermostProperty = (propsList, property) => {\n  for (let i = propsList.length - 1; i >= 0; i -= 1) {\n    const props = propsList[i];\n    if (Object.prototype.hasOwnProperty.call(props, property)) {\n      return props[property];\n    }\n  }\n  return null;\n};\nvar getTitleFromPropsList = (propsList) => {\n  let innermostTitle = getInnermostProperty(propsList, \"title\" /* TITLE */);\n  const innermostTemplate = getInnermostProperty(propsList, HELMET_PROPS.TITLE_TEMPLATE);\n  if (Array.isArray(innermostTitle)) {\n    innermostTitle = innermostTitle.join(\"\");\n  }\n  if (innermostTemplate && innermostTitle) {\n    return innermostTemplate.replace(/%s/g, () => innermostTitle);\n  }\n  const innermostDefaultTitle = getInnermostProperty(propsList, HELMET_PROPS.DEFAULT_TITLE);\n  return innermostTitle || innermostDefaultTitle || void 0;\n};\nvar getOnChangeClientState = (propsList) => getInnermostProperty(propsList, HELMET_PROPS.ON_CHANGE_CLIENT_STATE) || (() => {\n});\nvar getAttributesFromPropsList = (tagType, propsList) => propsList.filter((props) => typeof props[tagType] !== \"undefined\").map((props) => props[tagType]).reduce((tagAttrs, current) => ({ ...tagAttrs, ...current }), {});\nvar getBaseTagFromPropsList = (primaryAttributes, propsList) => propsList.filter((props) => typeof props[\"base\" /* BASE */] !== \"undefined\").map((props) => props[\"base\" /* BASE */]).reverse().reduce((innermostBaseTag, tag) => {\n  if (!innermostBaseTag.length) {\n    const keys = Object.keys(tag);\n    for (let i = 0; i < keys.length; i += 1) {\n      const attributeKey = keys[i];\n      const lowerCaseAttributeKey = attributeKey.toLowerCase();\n      if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && tag[lowerCaseAttributeKey]) {\n        return innermostBaseTag.concat(tag);\n      }\n    }\n  }\n  return innermostBaseTag;\n}, []);\nvar warn = (msg) => console && typeof console.warn === \"function\" && console.warn(msg);\nvar getTagsFromPropsList = (tagName, primaryAttributes, propsList) => {\n  const approvedSeenTags = {};\n  return propsList.filter((props) => {\n    if (Array.isArray(props[tagName])) {\n      return true;\n    }\n    if (typeof props[tagName] !== \"undefined\") {\n      warn(\n        `Helmet: ${tagName} should be of type \"Array\". Instead found type \"${typeof props[tagName]}\"`\n      );\n    }\n    return false;\n  }).map((props) => props[tagName]).reverse().reduce((approvedTags, instanceTags) => {\n    const instanceSeenTags = {};\n    instanceTags.filter((tag) => {\n      let primaryAttributeKey;\n      const keys2 = Object.keys(tag);\n      for (let i = 0; i < keys2.length; i += 1) {\n        const attributeKey = keys2[i];\n        const lowerCaseAttributeKey = attributeKey.toLowerCase();\n        if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && !(primaryAttributeKey === \"rel\" /* REL */ && tag[primaryAttributeKey].toLowerCase() === \"canonical\") && !(lowerCaseAttributeKey === \"rel\" /* REL */ && tag[lowerCaseAttributeKey].toLowerCase() === \"stylesheet\")) {\n          primaryAttributeKey = lowerCaseAttributeKey;\n        }\n        if (primaryAttributes.indexOf(attributeKey) !== -1 && (attributeKey === \"innerHTML\" /* INNER_HTML */ || attributeKey === \"cssText\" /* CSS_TEXT */ || attributeKey === \"itemprop\" /* ITEM_PROP */)) {\n          primaryAttributeKey = attributeKey;\n        }\n      }\n      if (!primaryAttributeKey || !tag[primaryAttributeKey]) {\n        return false;\n      }\n      const value = tag[primaryAttributeKey].toLowerCase();\n      if (!approvedSeenTags[primaryAttributeKey]) {\n        approvedSeenTags[primaryAttributeKey] = {};\n      }\n      if (!instanceSeenTags[primaryAttributeKey]) {\n        instanceSeenTags[primaryAttributeKey] = {};\n      }\n      if (!approvedSeenTags[primaryAttributeKey][value]) {\n        instanceSeenTags[primaryAttributeKey][value] = true;\n        return true;\n      }\n      return false;\n    }).reverse().forEach((tag) => approvedTags.push(tag));\n    const keys = Object.keys(instanceSeenTags);\n    for (let i = 0; i < keys.length; i += 1) {\n      const attributeKey = keys[i];\n      const tagUnion = {\n        ...approvedSeenTags[attributeKey],\n        ...instanceSeenTags[attributeKey]\n      };\n      approvedSeenTags[attributeKey] = tagUnion;\n    }\n    return approvedTags;\n  }, []).reverse();\n};\nvar getAnyTrueFromPropsList = (propsList, checkedTag) => {\n  if (Array.isArray(propsList) && propsList.length) {\n    for (let index = 0; index < propsList.length; index += 1) {\n      const prop = propsList[index];\n      if (prop[checkedTag]) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\nvar reducePropsToState = (propsList) => ({\n  baseTag: getBaseTagFromPropsList([\"href\" /* HREF */], propsList),\n  bodyAttributes: getAttributesFromPropsList(\"bodyAttributes\" /* BODY */, propsList),\n  defer: getInnermostProperty(propsList, HELMET_PROPS.DEFER),\n  encode: getInnermostProperty(propsList, HELMET_PROPS.ENCODE_SPECIAL_CHARACTERS),\n  htmlAttributes: getAttributesFromPropsList(\"htmlAttributes\" /* HTML */, propsList),\n  linkTags: getTagsFromPropsList(\n    \"link\" /* LINK */,\n    [\"rel\" /* REL */, \"href\" /* HREF */],\n    propsList\n  ),\n  metaTags: getTagsFromPropsList(\n    \"meta\" /* META */,\n    [\n      \"name\" /* NAME */,\n      \"charset\" /* CHARSET */,\n      \"http-equiv\" /* HTTPEQUIV */,\n      \"property\" /* PROPERTY */,\n      \"itemprop\" /* ITEM_PROP */\n    ],\n    propsList\n  ),\n  noscriptTags: getTagsFromPropsList(\"noscript\" /* NOSCRIPT */, [\"innerHTML\" /* INNER_HTML */], propsList),\n  onChangeClientState: getOnChangeClientState(propsList),\n  scriptTags: getTagsFromPropsList(\n    \"script\" /* SCRIPT */,\n    [\"src\" /* SRC */, \"innerHTML\" /* INNER_HTML */],\n    propsList\n  ),\n  styleTags: getTagsFromPropsList(\"style\" /* STYLE */, [\"cssText\" /* CSS_TEXT */], propsList),\n  title: getTitleFromPropsList(propsList),\n  titleAttributes: getAttributesFromPropsList(\"titleAttributes\" /* TITLE */, propsList),\n  prioritizeSeoTags: getAnyTrueFromPropsList(propsList, HELMET_PROPS.PRIORITIZE_SEO_TAGS)\n});\nvar flattenArray = (possibleArray) => Array.isArray(possibleArray) ? possibleArray.join(\"\") : possibleArray;\nvar checkIfPropsMatch = (props, toMatch) => {\n  const keys = Object.keys(props);\n  for (let i = 0; i < keys.length; i += 1) {\n    if (toMatch[keys[i]] && toMatch[keys[i]].includes(props[keys[i]])) {\n      return true;\n    }\n  }\n  return false;\n};\nvar prioritizer = (elementsList, propsToMatch) => {\n  if (Array.isArray(elementsList)) {\n    return elementsList.reduce(\n      (acc, elementAttrs) => {\n        if (checkIfPropsMatch(elementAttrs, propsToMatch)) {\n          acc.priority.push(elementAttrs);\n        } else {\n          acc.default.push(elementAttrs);\n        }\n        return acc;\n      },\n      { priority: [], default: [] }\n    );\n  }\n  return { default: elementsList, priority: [] };\n};\nvar without = (obj, key) => {\n  return {\n    ...obj,\n    [key]: void 0\n  };\n};\n\n// src/server.ts\nvar SELF_CLOSING_TAGS = [\"noscript\" /* NOSCRIPT */, \"script\" /* SCRIPT */, \"style\" /* STYLE */];\nvar encodeSpecialCharacters = (str, encode = true) => {\n  if (encode === false) {\n    return String(str);\n  }\n  return String(str).replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#x27;\");\n};\nvar generateElementAttributesAsString = (attributes) => Object.keys(attributes).reduce((str, key) => {\n  const attr = typeof attributes[key] !== \"undefined\" ? `${key}=\"${attributes[key]}\"` : `${key}`;\n  return str ? `${str} ${attr}` : attr;\n}, \"\");\nvar generateTitleAsString = (type, title, attributes, encode) => {\n  const attributeString = generateElementAttributesAsString(attributes);\n  const flattenedTitle = flattenArray(title);\n  return attributeString ? `<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeString}>${encodeSpecialCharacters(\n    flattenedTitle,\n    encode\n  )}</${type}>` : `<${type} ${HELMET_ATTRIBUTE}=\"true\">${encodeSpecialCharacters(\n    flattenedTitle,\n    encode\n  )}</${type}>`;\n};\nvar generateTagsAsString = (type, tags, encode = true) => tags.reduce((str, t) => {\n  const tag = t;\n  const attributeHtml = Object.keys(tag).filter(\n    (attribute) => !(attribute === \"innerHTML\" /* INNER_HTML */ || attribute === \"cssText\" /* CSS_TEXT */)\n  ).reduce((string, attribute) => {\n    const attr = typeof tag[attribute] === \"undefined\" ? attribute : `${attribute}=\"${encodeSpecialCharacters(tag[attribute], encode)}\"`;\n    return string ? `${string} ${attr}` : attr;\n  }, \"\");\n  const tagContent = tag.innerHTML || tag.cssText || \"\";\n  const isSelfClosing = SELF_CLOSING_TAGS.indexOf(type) === -1;\n  return `${str}<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeHtml}${isSelfClosing ? `/>` : `>${tagContent}</${type}>`}`;\n}, \"\");\nvar convertElementAttributesToReactProps = (attributes, initProps = {}) => Object.keys(attributes).reduce((obj, key) => {\n  const mapped = REACT_TAG_MAP[key];\n  obj[mapped || key] = attributes[key];\n  return obj;\n}, initProps);\nvar generateTitleAsReactComponent = (_type, title, attributes) => {\n  const initProps = {\n    key: title,\n    [HELMET_ATTRIBUTE]: true\n  };\n  const props = convertElementAttributesToReactProps(attributes, initProps);\n  return [React.createElement(\"title\" /* TITLE */, props, title)];\n};\nvar generateTagsAsReactComponent = (type, tags) => tags.map((tag, i) => {\n  const mappedTag = {\n    key: i,\n    [HELMET_ATTRIBUTE]: true\n  };\n  Object.keys(tag).forEach((attribute) => {\n    const mapped = REACT_TAG_MAP[attribute];\n    const mappedAttribute = mapped || attribute;\n    if (mappedAttribute === \"innerHTML\" /* INNER_HTML */ || mappedAttribute === \"cssText\" /* CSS_TEXT */) {\n      const content = tag.innerHTML || tag.cssText;\n      mappedTag.dangerouslySetInnerHTML = { __html: content };\n    } else {\n      mappedTag[mappedAttribute] = tag[attribute];\n    }\n  });\n  return React.createElement(type, mappedTag);\n});\nvar getMethodsForTag = (type, tags, encode = true) => {\n  switch (type) {\n    case \"title\" /* TITLE */:\n      return {\n        toComponent: () => generateTitleAsReactComponent(type, tags.title, tags.titleAttributes),\n        toString: () => generateTitleAsString(type, tags.title, tags.titleAttributes, encode)\n      };\n    case \"bodyAttributes\" /* BODY */:\n    case \"htmlAttributes\" /* HTML */:\n      return {\n        toComponent: () => convertElementAttributesToReactProps(tags),\n        toString: () => generateElementAttributesAsString(tags)\n      };\n    default:\n      return {\n        toComponent: () => generateTagsAsReactComponent(type, tags),\n        toString: () => generateTagsAsString(type, tags, encode)\n      };\n  }\n};\nvar getPriorityMethods = ({ metaTags, linkTags, scriptTags, encode }) => {\n  const meta = prioritizer(metaTags, SEO_PRIORITY_TAGS.meta);\n  const link = prioritizer(linkTags, SEO_PRIORITY_TAGS.link);\n  const script = prioritizer(scriptTags, SEO_PRIORITY_TAGS.script);\n  const priorityMethods = {\n    toComponent: () => [\n      ...generateTagsAsReactComponent(\"meta\" /* META */, meta.priority),\n      ...generateTagsAsReactComponent(\"link\" /* LINK */, link.priority),\n      ...generateTagsAsReactComponent(\"script\" /* SCRIPT */, script.priority)\n    ],\n    toString: () => (\n      // generate all the tags as strings and concatenate them\n      `${getMethodsForTag(\"meta\" /* META */, meta.priority, encode)} ${getMethodsForTag(\n        \"link\" /* LINK */,\n        link.priority,\n        encode\n      )} ${getMethodsForTag(\"script\" /* SCRIPT */, script.priority, encode)}`\n    )\n  };\n  return {\n    priorityMethods,\n    metaTags: meta.default,\n    linkTags: link.default,\n    scriptTags: script.default\n  };\n};\nvar mapStateOnServer = (props) => {\n  const {\n    baseTag,\n    bodyAttributes,\n    encode = true,\n    htmlAttributes,\n    noscriptTags,\n    styleTags,\n    title = \"\",\n    titleAttributes,\n    prioritizeSeoTags\n  } = props;\n  let { linkTags, metaTags, scriptTags } = props;\n  let priorityMethods = {\n    toComponent: () => {\n    },\n    toString: () => \"\"\n  };\n  if (prioritizeSeoTags) {\n    ({ priorityMethods, linkTags, metaTags, scriptTags } = getPriorityMethods(props));\n  }\n  return {\n    priority: priorityMethods,\n    base: getMethodsForTag(\"base\" /* BASE */, baseTag, encode),\n    bodyAttributes: getMethodsForTag(\"bodyAttributes\" /* BODY */, bodyAttributes, encode),\n    htmlAttributes: getMethodsForTag(\"htmlAttributes\" /* HTML */, htmlAttributes, encode),\n    link: getMethodsForTag(\"link\" /* LINK */, linkTags, encode),\n    meta: getMethodsForTag(\"meta\" /* META */, metaTags, encode),\n    noscript: getMethodsForTag(\"noscript\" /* NOSCRIPT */, noscriptTags, encode),\n    script: getMethodsForTag(\"script\" /* SCRIPT */, scriptTags, encode),\n    style: getMethodsForTag(\"style\" /* STYLE */, styleTags, encode),\n    title: getMethodsForTag(\"title\" /* TITLE */, { title, titleAttributes }, encode)\n  };\n};\nvar server_default = mapStateOnServer;\n\n// src/HelmetData.ts\nvar instances = [];\nvar isDocument = !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\nvar HelmetData = class {\n  instances = [];\n  canUseDOM = isDocument;\n  context;\n  value = {\n    setHelmet: (serverState) => {\n      this.context.helmet = serverState;\n    },\n    helmetInstances: {\n      get: () => this.canUseDOM ? instances : this.instances,\n      add: (instance) => {\n        (this.canUseDOM ? instances : this.instances).push(instance);\n      },\n      remove: (instance) => {\n        const index = (this.canUseDOM ? instances : this.instances).indexOf(instance);\n        (this.canUseDOM ? instances : this.instances).splice(index, 1);\n      }\n    }\n  };\n  constructor(context, canUseDOM) {\n    this.context = context;\n    this.canUseDOM = canUseDOM || false;\n    if (!canUseDOM) {\n      context.helmet = server_default({\n        baseTag: [],\n        bodyAttributes: {},\n        encodeSpecialCharacters: true,\n        htmlAttributes: {},\n        linkTags: [],\n        metaTags: [],\n        noscriptTags: [],\n        scriptTags: [],\n        styleTags: [],\n        title: \"\",\n        titleAttributes: {}\n      });\n    }\n  }\n};\n\n// src/Provider.tsx\nvar defaultValue = {};\nvar Context = React2.createContext(defaultValue);\nvar HelmetProvider = class _HelmetProvider extends Component {\n  static canUseDOM = isDocument;\n  helmetData;\n  constructor(props) {\n    super(props);\n    this.helmetData = new HelmetData(this.props.context || {}, _HelmetProvider.canUseDOM);\n  }\n  render() {\n    return /* @__PURE__ */ React2.createElement(Context.Provider, { value: this.helmetData.value }, this.props.children);\n  }\n};\n\n// src/Dispatcher.tsx\nimport { Component as Component2 } from \"react\";\nimport shallowEqual from \"shallowequal\";\n\n// src/client.ts\nvar updateTags = (type, tags) => {\n  const headElement = document.head || document.querySelector(\"head\" /* HEAD */);\n  const tagNodes = headElement.querySelectorAll(`${type}[${HELMET_ATTRIBUTE}]`);\n  const oldTags = [].slice.call(tagNodes);\n  const newTags = [];\n  let indexToDelete;\n  if (tags && tags.length) {\n    tags.forEach((tag) => {\n      const newElement = document.createElement(type);\n      for (const attribute in tag) {\n        if (Object.prototype.hasOwnProperty.call(tag, attribute)) {\n          if (attribute === \"innerHTML\" /* INNER_HTML */) {\n            newElement.innerHTML = tag.innerHTML;\n          } else if (attribute === \"cssText\" /* CSS_TEXT */) {\n            if (newElement.styleSheet) {\n              newElement.styleSheet.cssText = tag.cssText;\n            } else {\n              newElement.appendChild(document.createTextNode(tag.cssText));\n            }\n          } else {\n            const attr = attribute;\n            const value = typeof tag[attr] === \"undefined\" ? \"\" : tag[attr];\n            newElement.setAttribute(attribute, value);\n          }\n        }\n      }\n      newElement.setAttribute(HELMET_ATTRIBUTE, \"true\");\n      if (oldTags.some((existingTag, index) => {\n        indexToDelete = index;\n        return newElement.isEqualNode(existingTag);\n      })) {\n        oldTags.splice(indexToDelete, 1);\n      } else {\n        newTags.push(newElement);\n      }\n    });\n  }\n  oldTags.forEach((tag) => tag.parentNode?.removeChild(tag));\n  newTags.forEach((tag) => headElement.appendChild(tag));\n  return {\n    oldTags,\n    newTags\n  };\n};\nvar updateAttributes = (tagName, attributes) => {\n  const elementTag = document.getElementsByTagName(tagName)[0];\n  if (!elementTag) {\n    return;\n  }\n  const helmetAttributeString = elementTag.getAttribute(HELMET_ATTRIBUTE);\n  const helmetAttributes = helmetAttributeString ? helmetAttributeString.split(\",\") : [];\n  const attributesToRemove = [...helmetAttributes];\n  const attributeKeys = Object.keys(attributes);\n  for (const attribute of attributeKeys) {\n    const value = attributes[attribute] || \"\";\n    if (elementTag.getAttribute(attribute) !== value) {\n      elementTag.setAttribute(attribute, value);\n    }\n    if (helmetAttributes.indexOf(attribute) === -1) {\n      helmetAttributes.push(attribute);\n    }\n    const indexToSave = attributesToRemove.indexOf(attribute);\n    if (indexToSave !== -1) {\n      attributesToRemove.splice(indexToSave, 1);\n    }\n  }\n  for (let i = attributesToRemove.length - 1; i >= 0; i -= 1) {\n    elementTag.removeAttribute(attributesToRemove[i]);\n  }\n  if (helmetAttributes.length === attributesToRemove.length) {\n    elementTag.removeAttribute(HELMET_ATTRIBUTE);\n  } else if (elementTag.getAttribute(HELMET_ATTRIBUTE) !== attributeKeys.join(\",\")) {\n    elementTag.setAttribute(HELMET_ATTRIBUTE, attributeKeys.join(\",\"));\n  }\n};\nvar updateTitle = (title, attributes) => {\n  if (typeof title !== \"undefined\" && document.title !== title) {\n    document.title = flattenArray(title);\n  }\n  updateAttributes(\"title\" /* TITLE */, attributes);\n};\nvar commitTagChanges = (newState, cb) => {\n  const {\n    baseTag,\n    bodyAttributes,\n    htmlAttributes,\n    linkTags,\n    metaTags,\n    noscriptTags,\n    onChangeClientState,\n    scriptTags,\n    styleTags,\n    title,\n    titleAttributes\n  } = newState;\n  updateAttributes(\"body\" /* BODY */, bodyAttributes);\n  updateAttributes(\"html\" /* HTML */, htmlAttributes);\n  updateTitle(title, titleAttributes);\n  const tagUpdates = {\n    baseTag: updateTags(\"base\" /* BASE */, baseTag),\n    linkTags: updateTags(\"link\" /* LINK */, linkTags),\n    metaTags: updateTags(\"meta\" /* META */, metaTags),\n    noscriptTags: updateTags(\"noscript\" /* NOSCRIPT */, noscriptTags),\n    scriptTags: updateTags(\"script\" /* SCRIPT */, scriptTags),\n    styleTags: updateTags(\"style\" /* STYLE */, styleTags)\n  };\n  const addedTags = {};\n  const removedTags = {};\n  Object.keys(tagUpdates).forEach((tagType) => {\n    const { newTags, oldTags } = tagUpdates[tagType];\n    if (newTags.length) {\n      addedTags[tagType] = newTags;\n    }\n    if (oldTags.length) {\n      removedTags[tagType] = tagUpdates[tagType].oldTags;\n    }\n  });\n  if (cb) {\n    cb();\n  }\n  onChangeClientState(newState, addedTags, removedTags);\n};\nvar _helmetCallback = null;\nvar handleStateChangeOnClient = (newState) => {\n  if (_helmetCallback) {\n    cancelAnimationFrame(_helmetCallback);\n  }\n  if (newState.defer) {\n    _helmetCallback = requestAnimationFrame(() => {\n      commitTagChanges(newState, () => {\n        _helmetCallback = null;\n      });\n    });\n  } else {\n    commitTagChanges(newState);\n    _helmetCallback = null;\n  }\n};\nvar client_default = handleStateChangeOnClient;\n\n// src/Dispatcher.tsx\nvar HelmetDispatcher = class extends Component2 {\n  rendered = false;\n  shouldComponentUpdate(nextProps) {\n    return !shallowEqual(nextProps, this.props);\n  }\n  componentDidUpdate() {\n    this.emitChange();\n  }\n  componentWillUnmount() {\n    const { helmetInstances } = this.props.context;\n    helmetInstances.remove(this);\n    this.emitChange();\n  }\n  emitChange() {\n    const { helmetInstances, setHelmet } = this.props.context;\n    let serverState = null;\n    const state = reducePropsToState(\n      helmetInstances.get().map((instance) => {\n        const props = { ...instance.props };\n        delete props.context;\n        return props;\n      })\n    );\n    if (HelmetProvider.canUseDOM) {\n      client_default(state);\n    } else if (server_default) {\n      serverState = server_default(state);\n    }\n    setHelmet(serverState);\n  }\n  // componentWillMount will be deprecated\n  // for SSR, initialize on first render\n  // constructor is also unsafe in StrictMode\n  init() {\n    if (this.rendered) {\n      return;\n    }\n    this.rendered = true;\n    const { helmetInstances } = this.props.context;\n    helmetInstances.add(this);\n    this.emitChange();\n  }\n  render() {\n    this.init();\n    return null;\n  }\n};\n\n// src/index.tsx\nvar Helmet = class extends Component3 {\n  static defaultProps = {\n    defer: true,\n    encodeSpecialCharacters: true,\n    prioritizeSeoTags: false\n  };\n  shouldComponentUpdate(nextProps) {\n    return !fastCompare(without(this.props, \"helmetData\"), without(nextProps, \"helmetData\"));\n  }\n  mapNestedChildrenToProps(child, nestedChildren) {\n    if (!nestedChildren) {\n      return null;\n    }\n    switch (child.type) {\n      case \"script\" /* SCRIPT */:\n      case \"noscript\" /* NOSCRIPT */:\n        return {\n          innerHTML: nestedChildren\n        };\n      case \"style\" /* STYLE */:\n        return {\n          cssText: nestedChildren\n        };\n      default:\n        throw new Error(\n          `<${child.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`\n        );\n    }\n  }\n  flattenArrayTypeChildren(child, arrayTypeChildren, newChildProps, nestedChildren) {\n    return {\n      ...arrayTypeChildren,\n      [child.type]: [\n        ...arrayTypeChildren[child.type] || [],\n        {\n          ...newChildProps,\n          ...this.mapNestedChildrenToProps(child, nestedChildren)\n        }\n      ]\n    };\n  }\n  mapObjectTypeChildren(child, newProps, newChildProps, nestedChildren) {\n    switch (child.type) {\n      case \"title\" /* TITLE */:\n        return {\n          ...newProps,\n          [child.type]: nestedChildren,\n          titleAttributes: { ...newChildProps }\n        };\n      case \"body\" /* BODY */:\n        return {\n          ...newProps,\n          bodyAttributes: { ...newChildProps }\n        };\n      case \"html\" /* HTML */:\n        return {\n          ...newProps,\n          htmlAttributes: { ...newChildProps }\n        };\n      default:\n        return {\n          ...newProps,\n          [child.type]: { ...newChildProps }\n        };\n    }\n  }\n  mapArrayTypeChildrenToProps(arrayTypeChildren, newProps) {\n    let newFlattenedProps = { ...newProps };\n    Object.keys(arrayTypeChildren).forEach((arrayChildName) => {\n      newFlattenedProps = {\n        ...newFlattenedProps,\n        [arrayChildName]: arrayTypeChildren[arrayChildName]\n      };\n    });\n    return newFlattenedProps;\n  }\n  warnOnInvalidChildren(child, nestedChildren) {\n    invariant(\n      VALID_TAG_NAMES.some((name) => child.type === name),\n      typeof child.type === \"function\" ? `You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.` : `Only elements types ${VALID_TAG_NAMES.join(\n        \", \"\n      )} are allowed. Helmet does not support rendering <${child.type}> elements. Refer to our API for more information.`\n    );\n    invariant(\n      !nestedChildren || typeof nestedChildren === \"string\" || Array.isArray(nestedChildren) && !nestedChildren.some((nestedChild) => typeof nestedChild !== \"string\"),\n      `Helmet expects a string as a child of <${child.type}>. Did you forget to wrap your children in braces? ( <${child.type}>{\\`\\`}</${child.type}> ) Refer to our API for more information.`\n    );\n    return true;\n  }\n  mapChildrenToProps(children, newProps) {\n    let arrayTypeChildren = {};\n    React3.Children.forEach(children, (child) => {\n      if (!child || !child.props) {\n        return;\n      }\n      const { children: nestedChildren, ...childProps } = child.props;\n      const newChildProps = Object.keys(childProps).reduce((obj, key) => {\n        obj[HTML_TAG_MAP[key] || key] = childProps[key];\n        return obj;\n      }, {});\n      let { type } = child;\n      if (typeof type === \"symbol\") {\n        type = type.toString();\n      } else {\n        this.warnOnInvalidChildren(child, nestedChildren);\n      }\n      switch (type) {\n        case \"Symbol(react.fragment)\" /* FRAGMENT */:\n          newProps = this.mapChildrenToProps(nestedChildren, newProps);\n          break;\n        case \"link\" /* LINK */:\n        case \"meta\" /* META */:\n        case \"noscript\" /* NOSCRIPT */:\n        case \"script\" /* SCRIPT */:\n        case \"style\" /* STYLE */:\n          arrayTypeChildren = this.flattenArrayTypeChildren(\n            child,\n            arrayTypeChildren,\n            newChildProps,\n            nestedChildren\n          );\n          break;\n        default:\n          newProps = this.mapObjectTypeChildren(child, newProps, newChildProps, nestedChildren);\n          break;\n      }\n    });\n    return this.mapArrayTypeChildrenToProps(arrayTypeChildren, newProps);\n  }\n  render() {\n    const { children, ...props } = this.props;\n    let newProps = { ...props };\n    let { helmetData } = props;\n    if (children) {\n      newProps = this.mapChildrenToProps(children, newProps);\n    }\n    if (helmetData && !(helmetData instanceof HelmetData)) {\n      const data = helmetData;\n      helmetData = new HelmetData(data.context, true);\n      delete newProps.helmetData;\n    }\n    return helmetData ? /* @__PURE__ */ React3.createElement(HelmetDispatcher, { ...newProps, context: helmetData.value }) : /* @__PURE__ */ React3.createElement(Context.Consumer, null, (context) => /* @__PURE__ */ React3.createElement(HelmetDispatcher, { ...newProps, context }));\n  }\n};\nexport {\n  Helmet,\n  HelmetData,\n  HelmetProvider\n};\n"], "mappings": "AAAA;AACA,OAAOA,MAAM,IAAIC,SAAS,IAAIC,UAAU,QAAQ,OAAO;AACvD,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,SAAS,MAAM,WAAW;;AAEjC;AACA,OAAOC,MAAM,IAAIJ,SAAS,QAAQ,OAAO;;AAEzC;AACA,OAAOK,KAAK,MAAM,OAAO;;AAEzB;AACA,IAAIC,SAAS,GAAG,eAAgB,CAAEC,UAAU,IAAK;EAC/CA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;EAC3BA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;EAC3BA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;EAC3BA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;EAC3BA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;EAC3BA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;EAC3BA,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU;EACnCA,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC/BA,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;EAC7BA,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;EAC7BA,UAAU,CAAC,UAAU,CAAC,GAAG,wBAAwB;EACjD,OAAOA,UAAU;AACnB,CAAC,EAAED,SAAS,IAAI,CAAC,CAAC,CAAC;AACnB,IAAIE,iBAAiB,GAAG;EACtBC,IAAI,EAAE;IAAEC,GAAG,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW;EAAE,CAAC;EACpDC,MAAM,EAAE;IAAEC,IAAI,EAAE,CAAC,qBAAqB;EAAE,CAAC;EACzCC,IAAI,EAAE;IACJC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC;IAC5CC,QAAQ,EAAE,CACR,SAAS,EACT,UAAU,EACV,QAAQ,EACR,UAAU,EACV,cAAc,EACd,gBAAgB,EAChB,aAAa,EACb,eAAe,EACf,qBAAqB,EACrB,eAAe,EACf,mBAAmB,EACnB,cAAc,EACd,cAAc;EAElB;AACF,CAAC;AACD,IAAIC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAACb,SAAS,CAAC;AAC9C,IAAIc,aAAa,GAAG;EAClBC,SAAS,EAAE,WAAW;EACtBP,OAAO,EAAE,SAAS;EAClBQ,KAAK,EAAE,WAAW;EAClBC,eAAe,EAAE,iBAAiB;EAClCC,WAAW,EAAE,aAAa;EAC1B,YAAY,EAAE,WAAW;EACzBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,YAAY,GAAGT,MAAM,CAACU,OAAO,CAACR,aAAa,CAAC,CAACS,MAAM,CACrD,CAACC,KAAK,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;EACvBF,KAAK,CAACE,KAAK,CAAC,GAAGD,GAAG;EAClB,OAAOD,KAAK;AACd,CAAC,EACD,CAAC,CACH,CAAC;AACD,IAAIG,gBAAgB,GAAG,SAAS;;AAEhC;AACA,IAAIC,YAAY,GAAG;EACjBC,aAAa,EAAE,cAAc;EAC7BC,KAAK,EAAE,OAAO;EACdC,yBAAyB,EAAE,yBAAyB;EACpDC,sBAAsB,EAAE,qBAAqB;EAC7CC,cAAc,EAAE,eAAe;EAC/BC,mBAAmB,EAAE;AACvB,CAAC;AACD,IAAIC,oBAAoB,GAAGA,CAACC,SAAS,EAAE1B,QAAQ,KAAK;EAClD,KAAK,IAAI2B,CAAC,GAAGD,SAAS,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IACjD,MAAME,KAAK,GAAGH,SAAS,CAACC,CAAC,CAAC;IAC1B,IAAIzB,MAAM,CAAC4B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACH,KAAK,EAAE7B,QAAQ,CAAC,EAAE;MACzD,OAAO6B,KAAK,CAAC7B,QAAQ,CAAC;IACxB;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIiC,qBAAqB,GAAIP,SAAS,IAAK;EACzC,IAAIQ,cAAc,GAAGT,oBAAoB,CAACC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC;EACzE,MAAMS,iBAAiB,GAAGV,oBAAoB,CAACC,SAAS,EAAER,YAAY,CAACK,cAAc,CAAC;EACtF,IAAIa,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,EAAE;IACjCA,cAAc,GAAGA,cAAc,CAACI,IAAI,CAAC,EAAE,CAAC;EAC1C;EACA,IAAIH,iBAAiB,IAAID,cAAc,EAAE;IACvC,OAAOC,iBAAiB,CAACI,OAAO,CAAC,KAAK,EAAE,MAAML,cAAc,CAAC;EAC/D;EACA,MAAMM,qBAAqB,GAAGf,oBAAoB,CAACC,SAAS,EAAER,YAAY,CAACC,aAAa,CAAC;EACzF,OAAOe,cAAc,IAAIM,qBAAqB,IAAI,KAAK,CAAC;AAC1D,CAAC;AACD,IAAIC,sBAAsB,GAAIf,SAAS,IAAKD,oBAAoB,CAACC,SAAS,EAAER,YAAY,CAACI,sBAAsB,CAAC,KAAK,MAAM,CAC3H,CAAC,CAAC;AACF,IAAIoB,0BAA0B,GAAGA,CAACC,OAAO,EAAEjB,SAAS,KAAKA,SAAS,CAACkB,MAAM,CAAEf,KAAK,IAAK,OAAOA,KAAK,CAACc,OAAO,CAAC,KAAK,WAAW,CAAC,CAACE,GAAG,CAAEhB,KAAK,IAAKA,KAAK,CAACc,OAAO,CAAC,CAAC,CAAC9B,MAAM,CAAC,CAACiC,QAAQ,EAAEC,OAAO,MAAM;EAAE,GAAGD,QAAQ;EAAE,GAAGC;AAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3N,IAAIC,uBAAuB,GAAGA,CAACC,iBAAiB,EAAEvB,SAAS,KAAKA,SAAS,CAACkB,MAAM,CAAEf,KAAK,IAAK,OAAOA,KAAK,CAAC,MAAM,CAAC,WAAW,KAAK,WAAW,CAAC,CAACgB,GAAG,CAAEhB,KAAK,IAAKA,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAACqB,OAAO,CAAC,CAAC,CAACrC,MAAM,CAAC,CAACsC,gBAAgB,EAAEC,GAAG,KAAK;EAChO,IAAI,CAACD,gBAAgB,CAACvB,MAAM,EAAE;IAC5B,MAAMyB,IAAI,GAAGnD,MAAM,CAACmD,IAAI,CAACD,GAAG,CAAC;IAC7B,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,IAAI,CAACzB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACvC,MAAM2B,YAAY,GAAGD,IAAI,CAAC1B,CAAC,CAAC;MAC5B,MAAM4B,qBAAqB,GAAGD,YAAY,CAACE,WAAW,CAAC,CAAC;MACxD,IAAIP,iBAAiB,CAACQ,OAAO,CAACF,qBAAqB,CAAC,KAAK,CAAC,CAAC,IAAIH,GAAG,CAACG,qBAAqB,CAAC,EAAE;QACzF,OAAOJ,gBAAgB,CAACO,MAAM,CAACN,GAAG,CAAC;MACrC;IACF;EACF;EACA,OAAOD,gBAAgB;AACzB,CAAC,EAAE,EAAE,CAAC;AACN,IAAIQ,IAAI,GAAIC,GAAG,IAAKC,OAAO,IAAI,OAAOA,OAAO,CAACF,IAAI,KAAK,UAAU,IAAIE,OAAO,CAACF,IAAI,CAACC,GAAG,CAAC;AACtF,IAAIE,oBAAoB,GAAGA,CAACC,OAAO,EAAEd,iBAAiB,EAAEvB,SAAS,KAAK;EACpE,MAAMsC,gBAAgB,GAAG,CAAC,CAAC;EAC3B,OAAOtC,SAAS,CAACkB,MAAM,CAAEf,KAAK,IAAK;IACjC,IAAIO,KAAK,CAACC,OAAO,CAACR,KAAK,CAACkC,OAAO,CAAC,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;IACA,IAAI,OAAOlC,KAAK,CAACkC,OAAO,CAAC,KAAK,WAAW,EAAE;MACzCJ,IAAI,CACF,WAAWI,OAAO,mDAAmD,OAAOlC,KAAK,CAACkC,OAAO,CAAC,GAC5F,CAAC;IACH;IACA,OAAO,KAAK;EACd,CAAC,CAAC,CAAClB,GAAG,CAAEhB,KAAK,IAAKA,KAAK,CAACkC,OAAO,CAAC,CAAC,CAACb,OAAO,CAAC,CAAC,CAACrC,MAAM,CAAC,CAACoD,YAAY,EAAEC,YAAY,KAAK;IACjF,MAAMC,gBAAgB,GAAG,CAAC,CAAC;IAC3BD,YAAY,CAACtB,MAAM,CAAEQ,GAAG,IAAK;MAC3B,IAAIgB,mBAAmB;MACvB,MAAMC,KAAK,GAAGnE,MAAM,CAACmD,IAAI,CAACD,GAAG,CAAC;MAC9B,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,KAAK,CAACzC,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QACxC,MAAM2B,YAAY,GAAGe,KAAK,CAAC1C,CAAC,CAAC;QAC7B,MAAM4B,qBAAqB,GAAGD,YAAY,CAACE,WAAW,CAAC,CAAC;QACxD,IAAIP,iBAAiB,CAACQ,OAAO,CAACF,qBAAqB,CAAC,KAAK,CAAC,CAAC,IAAI,EAAEa,mBAAmB,KAAK,KAAK,CAAC,aAAahB,GAAG,CAACgB,mBAAmB,CAAC,CAACZ,WAAW,CAAC,CAAC,KAAK,WAAW,CAAC,IAAI,EAAED,qBAAqB,KAAK,KAAK,CAAC,aAAaH,GAAG,CAACG,qBAAqB,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK,YAAY,CAAC,EAAE;UAChRY,mBAAmB,GAAGb,qBAAqB;QAC7C;QACA,IAAIN,iBAAiB,CAACQ,OAAO,CAACH,YAAY,CAAC,KAAK,CAAC,CAAC,KAAKA,YAAY,KAAK,WAAW,CAAC,oBAAoBA,YAAY,KAAK,SAAS,CAAC,kBAAkBA,YAAY,KAAK,UAAU,CAAC,gBAAgB,EAAE;UACjMc,mBAAmB,GAAGd,YAAY;QACpC;MACF;MACA,IAAI,CAACc,mBAAmB,IAAI,CAAChB,GAAG,CAACgB,mBAAmB,CAAC,EAAE;QACrD,OAAO,KAAK;MACd;MACA,MAAMpD,KAAK,GAAGoC,GAAG,CAACgB,mBAAmB,CAAC,CAACZ,WAAW,CAAC,CAAC;MACpD,IAAI,CAACQ,gBAAgB,CAACI,mBAAmB,CAAC,EAAE;QAC1CJ,gBAAgB,CAACI,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAC5C;MACA,IAAI,CAACD,gBAAgB,CAACC,mBAAmB,CAAC,EAAE;QAC1CD,gBAAgB,CAACC,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAC5C;MACA,IAAI,CAACJ,gBAAgB,CAACI,mBAAmB,CAAC,CAACpD,KAAK,CAAC,EAAE;QACjDmD,gBAAgB,CAACC,mBAAmB,CAAC,CAACpD,KAAK,CAAC,GAAG,IAAI;QACnD,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC,CAACkC,OAAO,CAAC,CAAC,CAACoB,OAAO,CAAElB,GAAG,IAAKa,YAAY,CAACM,IAAI,CAACnB,GAAG,CAAC,CAAC;IACrD,MAAMC,IAAI,GAAGnD,MAAM,CAACmD,IAAI,CAACc,gBAAgB,CAAC;IAC1C,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,IAAI,CAACzB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACvC,MAAM2B,YAAY,GAAGD,IAAI,CAAC1B,CAAC,CAAC;MAC5B,MAAM6C,QAAQ,GAAG;QACf,GAAGR,gBAAgB,CAACV,YAAY,CAAC;QACjC,GAAGa,gBAAgB,CAACb,YAAY;MAClC,CAAC;MACDU,gBAAgB,CAACV,YAAY,CAAC,GAAGkB,QAAQ;IAC3C;IACA,OAAOP,YAAY;EACrB,CAAC,EAAE,EAAE,CAAC,CAACf,OAAO,CAAC,CAAC;AAClB,CAAC;AACD,IAAIuB,uBAAuB,GAAGA,CAAC/C,SAAS,EAAEgD,UAAU,KAAK;EACvD,IAAItC,KAAK,CAACC,OAAO,CAACX,SAAS,CAAC,IAAIA,SAAS,CAACE,MAAM,EAAE;IAChD,KAAK,IAAI+C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGjD,SAAS,CAACE,MAAM,EAAE+C,KAAK,IAAI,CAAC,EAAE;MACxD,MAAMC,IAAI,GAAGlD,SAAS,CAACiD,KAAK,CAAC;MAC7B,IAAIC,IAAI,CAACF,UAAU,CAAC,EAAE;QACpB,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd,CAAC;AACD,IAAIG,kBAAkB,GAAInD,SAAS,KAAM;EACvCoD,OAAO,EAAE9B,uBAAuB,CAAC,CAAC,MAAM,CAAC,WAAW,EAAEtB,SAAS,CAAC;EAChEqD,cAAc,EAAErC,0BAA0B,CAAC,gBAAgB,CAAC,YAAYhB,SAAS,CAAC;EAClFsD,KAAK,EAAEvD,oBAAoB,CAACC,SAAS,EAAER,YAAY,CAACE,KAAK,CAAC;EAC1D6D,MAAM,EAAExD,oBAAoB,CAACC,SAAS,EAAER,YAAY,CAACG,yBAAyB,CAAC;EAC/E6D,cAAc,EAAExC,0BAA0B,CAAC,gBAAgB,CAAC,YAAYhB,SAAS,CAAC;EAClFyD,QAAQ,EAAErB,oBAAoB,CAC5B,MAAM,CAAC,YACP,CAAC,KAAK,CAAC,WAAW,MAAM,CAAC,WAAW,EACpCpC,SACF,CAAC;EACD0D,QAAQ,EAAEtB,oBAAoB,CAC5B,MAAM,CAAC,YACP,CACE,MAAM,CAAC,YACP,SAAS,CAAC,eACV,YAAY,CAAC,iBACb,UAAU,CAAC,gBACX,UAAU,CAAC,gBACZ,EACDpC,SACF,CAAC;EACD2D,YAAY,EAAEvB,oBAAoB,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,CAAC,iBAAiB,EAAEpC,SAAS,CAAC;EACxG4D,mBAAmB,EAAE7C,sBAAsB,CAACf,SAAS,CAAC;EACtD6D,UAAU,EAAEzB,oBAAoB,CAC9B,QAAQ,CAAC,cACT,CAAC,KAAK,CAAC,WAAW,WAAW,CAAC,iBAAiB,EAC/CpC,SACF,CAAC;EACD8D,SAAS,EAAE1B,oBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAEpC,SAAS,CAAC;EAC3F+D,KAAK,EAAExD,qBAAqB,CAACP,SAAS,CAAC;EACvCgE,eAAe,EAAEhD,0BAA0B,CAAC,iBAAiB,CAAC,aAAahB,SAAS,CAAC;EACrFiE,iBAAiB,EAAElB,uBAAuB,CAAC/C,SAAS,EAAER,YAAY,CAACM,mBAAmB;AACxF,CAAC,CAAC;AACF,IAAIoE,YAAY,GAAIC,aAAa,IAAKzD,KAAK,CAACC,OAAO,CAACwD,aAAa,CAAC,GAAGA,aAAa,CAACvD,IAAI,CAAC,EAAE,CAAC,GAAGuD,aAAa;AAC3G,IAAIC,iBAAiB,GAAGA,CAACjE,KAAK,EAAEkE,OAAO,KAAK;EAC1C,MAAM1C,IAAI,GAAGnD,MAAM,CAACmD,IAAI,CAACxB,KAAK,CAAC;EAC/B,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,IAAI,CAACzB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACvC,IAAIoE,OAAO,CAAC1C,IAAI,CAAC1B,CAAC,CAAC,CAAC,IAAIoE,OAAO,CAAC1C,IAAI,CAAC1B,CAAC,CAAC,CAAC,CAACqE,QAAQ,CAACnE,KAAK,CAACwB,IAAI,CAAC1B,CAAC,CAAC,CAAC,CAAC,EAAE;MACjE,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd,CAAC;AACD,IAAIsE,WAAW,GAAGA,CAACC,YAAY,EAAEC,YAAY,KAAK;EAChD,IAAI/D,KAAK,CAACC,OAAO,CAAC6D,YAAY,CAAC,EAAE;IAC/B,OAAOA,YAAY,CAACrF,MAAM,CACxB,CAACuF,GAAG,EAAEC,YAAY,KAAK;MACrB,IAAIP,iBAAiB,CAACO,YAAY,EAAEF,YAAY,CAAC,EAAE;QACjDC,GAAG,CAACE,QAAQ,CAAC/B,IAAI,CAAC8B,YAAY,CAAC;MACjC,CAAC,MAAM;QACLD,GAAG,CAACG,OAAO,CAAChC,IAAI,CAAC8B,YAAY,CAAC;MAChC;MACA,OAAOD,GAAG;IACZ,CAAC,EACD;MAAEE,QAAQ,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAC9B,CAAC;EACH;EACA,OAAO;IAAEA,OAAO,EAAEL,YAAY;IAAEI,QAAQ,EAAE;EAAG,CAAC;AAChD,CAAC;AACD,IAAIE,OAAO,GAAGA,CAACC,GAAG,EAAE1F,GAAG,KAAK;EAC1B,OAAO;IACL,GAAG0F,GAAG;IACN,CAAC1F,GAAG,GAAG,KAAK;EACd,CAAC;AACH,CAAC;;AAED;AACA,IAAI2F,iBAAiB,GAAG,CAAC,UAAU,CAAC,gBAAgB,QAAQ,CAAC,cAAc,OAAO,CAAC,YAAY;AAC/F,IAAIC,uBAAuB,GAAGA,CAACC,GAAG,EAAE3B,MAAM,GAAG,IAAI,KAAK;EACpD,IAAIA,MAAM,KAAK,KAAK,EAAE;IACpB,OAAO4B,MAAM,CAACD,GAAG,CAAC;EACpB;EACA,OAAOC,MAAM,CAACD,GAAG,CAAC,CAACrE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;AACvI,CAAC;AACD,IAAIuE,iCAAiC,GAAIC,UAAU,IAAK7G,MAAM,CAACmD,IAAI,CAAC0D,UAAU,CAAC,CAAClG,MAAM,CAAC,CAAC+F,GAAG,EAAE7F,GAAG,KAAK;EACnG,MAAMiG,IAAI,GAAG,OAAOD,UAAU,CAAChG,GAAG,CAAC,KAAK,WAAW,GAAG,GAAGA,GAAG,KAAKgG,UAAU,CAAChG,GAAG,CAAC,GAAG,GAAG,GAAGA,GAAG,EAAE;EAC9F,OAAO6F,GAAG,GAAG,GAAGA,GAAG,IAAII,IAAI,EAAE,GAAGA,IAAI;AACtC,CAAC,EAAE,EAAE,CAAC;AACN,IAAIC,qBAAqB,GAAGA,CAACrH,IAAI,EAAE6F,KAAK,EAAEsB,UAAU,EAAE9B,MAAM,KAAK;EAC/D,MAAMiC,eAAe,GAAGJ,iCAAiC,CAACC,UAAU,CAAC;EACrE,MAAMI,cAAc,GAAGvB,YAAY,CAACH,KAAK,CAAC;EAC1C,OAAOyB,eAAe,GAAG,IAAItH,IAAI,IAAIqB,gBAAgB,WAAWiG,eAAe,IAAIP,uBAAuB,CACxGQ,cAAc,EACdlC,MACF,CAAC,KAAKrF,IAAI,GAAG,GAAG,IAAIA,IAAI,IAAIqB,gBAAgB,WAAW0F,uBAAuB,CAC5EQ,cAAc,EACdlC,MACF,CAAC,KAAKrF,IAAI,GAAG;AACf,CAAC;AACD,IAAIwH,oBAAoB,GAAGA,CAACxH,IAAI,EAAEyH,IAAI,EAAEpC,MAAM,GAAG,IAAI,KAAKoC,IAAI,CAACxG,MAAM,CAAC,CAAC+F,GAAG,EAAEU,CAAC,KAAK;EAChF,MAAMlE,GAAG,GAAGkE,CAAC;EACb,MAAMC,aAAa,GAAGrH,MAAM,CAACmD,IAAI,CAACD,GAAG,CAAC,CAACR,MAAM,CAC1C4E,SAAS,IAAK,EAAEA,SAAS,KAAK,WAAW,CAAC,oBAAoBA,SAAS,KAAK,SAAS,CAAC,eACzF,CAAC,CAAC3G,MAAM,CAAC,CAAC4G,MAAM,EAAED,SAAS,KAAK;IAC9B,MAAMR,IAAI,GAAG,OAAO5D,GAAG,CAACoE,SAAS,CAAC,KAAK,WAAW,GAAGA,SAAS,GAAG,GAAGA,SAAS,KAAKb,uBAAuB,CAACvD,GAAG,CAACoE,SAAS,CAAC,EAAEvC,MAAM,CAAC,GAAG;IACpI,OAAOwC,MAAM,GAAG,GAAGA,MAAM,IAAIT,IAAI,EAAE,GAAGA,IAAI;EAC5C,CAAC,EAAE,EAAE,CAAC;EACN,MAAMU,UAAU,GAAGtE,GAAG,CAACuE,SAAS,IAAIvE,GAAG,CAACwE,OAAO,IAAI,EAAE;EACrD,MAAMC,aAAa,GAAGnB,iBAAiB,CAACjD,OAAO,CAAC7D,IAAI,CAAC,KAAK,CAAC,CAAC;EAC5D,OAAO,GAAGgH,GAAG,IAAIhH,IAAI,IAAIqB,gBAAgB,WAAWsG,aAAa,GAAGM,aAAa,GAAG,IAAI,GAAG,IAAIH,UAAU,KAAK9H,IAAI,GAAG,EAAE;AACzH,CAAC,EAAE,EAAE,CAAC;AACN,IAAIkI,oCAAoC,GAAGA,CAACf,UAAU,EAAEgB,SAAS,GAAG,CAAC,CAAC,KAAK7H,MAAM,CAACmD,IAAI,CAAC0D,UAAU,CAAC,CAAClG,MAAM,CAAC,CAAC4F,GAAG,EAAE1F,GAAG,KAAK;EACtH,MAAMiH,MAAM,GAAG5H,aAAa,CAACW,GAAG,CAAC;EACjC0F,GAAG,CAACuB,MAAM,IAAIjH,GAAG,CAAC,GAAGgG,UAAU,CAAChG,GAAG,CAAC;EACpC,OAAO0F,GAAG;AACZ,CAAC,EAAEsB,SAAS,CAAC;AACb,IAAIE,6BAA6B,GAAGA,CAACC,KAAK,EAAEzC,KAAK,EAAEsB,UAAU,KAAK;EAChE,MAAMgB,SAAS,GAAG;IAChBhH,GAAG,EAAE0E,KAAK;IACV,CAACxE,gBAAgB,GAAG;EACtB,CAAC;EACD,MAAMY,KAAK,GAAGiG,oCAAoC,CAACf,UAAU,EAAEgB,SAAS,CAAC;EACzE,OAAO,CAAC1I,KAAK,CAAC8I,aAAa,CAAC,OAAO,CAAC,aAAatG,KAAK,EAAE4D,KAAK,CAAC,CAAC;AACjE,CAAC;AACD,IAAI2C,4BAA4B,GAAGA,CAACxI,IAAI,EAAEyH,IAAI,KAAKA,IAAI,CAACxE,GAAG,CAAC,CAACO,GAAG,EAAEzB,CAAC,KAAK;EACtE,MAAM0G,SAAS,GAAG;IAChBtH,GAAG,EAAEY,CAAC;IACN,CAACV,gBAAgB,GAAG;EACtB,CAAC;EACDf,MAAM,CAACmD,IAAI,CAACD,GAAG,CAAC,CAACkB,OAAO,CAAEkD,SAAS,IAAK;IACtC,MAAMQ,MAAM,GAAG5H,aAAa,CAACoH,SAAS,CAAC;IACvC,MAAMc,eAAe,GAAGN,MAAM,IAAIR,SAAS;IAC3C,IAAIc,eAAe,KAAK,WAAW,CAAC,oBAAoBA,eAAe,KAAK,SAAS,CAAC,gBAAgB;MACpG,MAAMC,OAAO,GAAGnF,GAAG,CAACuE,SAAS,IAAIvE,GAAG,CAACwE,OAAO;MAC5CS,SAAS,CAACG,uBAAuB,GAAG;QAAEC,MAAM,EAAEF;MAAQ,CAAC;IACzD,CAAC,MAAM;MACLF,SAAS,CAACC,eAAe,CAAC,GAAGlF,GAAG,CAACoE,SAAS,CAAC;IAC7C;EACF,CAAC,CAAC;EACF,OAAOnI,KAAK,CAAC8I,aAAa,CAACvI,IAAI,EAAEyI,SAAS,CAAC;AAC7C,CAAC,CAAC;AACF,IAAIK,gBAAgB,GAAGA,CAAC9I,IAAI,EAAEyH,IAAI,EAAEpC,MAAM,GAAG,IAAI,KAAK;EACpD,QAAQrF,IAAI;IACV,KAAK,OAAO,CAAC;MACX,OAAO;QACL+I,WAAW,EAAEA,CAAA,KAAMV,6BAA6B,CAACrI,IAAI,EAAEyH,IAAI,CAAC5B,KAAK,EAAE4B,IAAI,CAAC3B,eAAe,CAAC;QACxFkD,QAAQ,EAAEA,CAAA,KAAM3B,qBAAqB,CAACrH,IAAI,EAAEyH,IAAI,CAAC5B,KAAK,EAAE4B,IAAI,CAAC3B,eAAe,EAAET,MAAM;MACtF,CAAC;IACH,KAAK,gBAAgB,CAAC;IACtB,KAAK,gBAAgB,CAAC;MACpB,OAAO;QACL0D,WAAW,EAAEA,CAAA,KAAMb,oCAAoC,CAACT,IAAI,CAAC;QAC7DuB,QAAQ,EAAEA,CAAA,KAAM9B,iCAAiC,CAACO,IAAI;MACxD,CAAC;IACH;MACE,OAAO;QACLsB,WAAW,EAAEA,CAAA,KAAMP,4BAA4B,CAACxI,IAAI,EAAEyH,IAAI,CAAC;QAC3DuB,QAAQ,EAAEA,CAAA,KAAMxB,oBAAoB,CAACxH,IAAI,EAAEyH,IAAI,EAAEpC,MAAM;MACzD,CAAC;EACL;AACF,CAAC;AACD,IAAI4D,kBAAkB,GAAGA,CAAC;EAAEzD,QAAQ;EAAED,QAAQ;EAAEI,UAAU;EAAEN;AAAO,CAAC,KAAK;EACvE,MAAMpF,IAAI,GAAGoG,WAAW,CAACb,QAAQ,EAAE5F,iBAAiB,CAACK,IAAI,CAAC;EAC1D,MAAMJ,IAAI,GAAGwG,WAAW,CAACd,QAAQ,EAAE3F,iBAAiB,CAACC,IAAI,CAAC;EAC1D,MAAME,MAAM,GAAGsG,WAAW,CAACV,UAAU,EAAE/F,iBAAiB,CAACG,MAAM,CAAC;EAChE,MAAMmJ,eAAe,GAAG;IACtBH,WAAW,EAAEA,CAAA,KAAM,CACjB,GAAGP,4BAA4B,CAAC,MAAM,CAAC,YAAYvI,IAAI,CAACyG,QAAQ,CAAC,EACjE,GAAG8B,4BAA4B,CAAC,MAAM,CAAC,YAAY3I,IAAI,CAAC6G,QAAQ,CAAC,EACjE,GAAG8B,4BAA4B,CAAC,QAAQ,CAAC,cAAczI,MAAM,CAAC2G,QAAQ,CAAC,CACxE;IACDsC,QAAQ,EAAEA,CAAA;IACR;IACA,GAAGF,gBAAgB,CAAC,MAAM,CAAC,YAAY7I,IAAI,CAACyG,QAAQ,EAAErB,MAAM,CAAC,IAAIyD,gBAAgB,CAC/E,MAAM,CAAC,YACPjJ,IAAI,CAAC6G,QAAQ,EACbrB,MACF,CAAC,IAAIyD,gBAAgB,CAAC,QAAQ,CAAC,cAAc/I,MAAM,CAAC2G,QAAQ,EAAErB,MAAM,CAAC;EAEzE,CAAC;EACD,OAAO;IACL6D,eAAe;IACf1D,QAAQ,EAAEvF,IAAI,CAAC0G,OAAO;IACtBpB,QAAQ,EAAE1F,IAAI,CAAC8G,OAAO;IACtBhB,UAAU,EAAE5F,MAAM,CAAC4G;EACrB,CAAC;AACH,CAAC;AACD,IAAIwC,gBAAgB,GAAIlH,KAAK,IAAK;EAChC,MAAM;IACJiD,OAAO;IACPC,cAAc;IACdE,MAAM,GAAG,IAAI;IACbC,cAAc;IACdG,YAAY;IACZG,SAAS;IACTC,KAAK,GAAG,EAAE;IACVC,eAAe;IACfC;EACF,CAAC,GAAG9D,KAAK;EACT,IAAI;IAAEsD,QAAQ;IAAEC,QAAQ;IAAEG;EAAW,CAAC,GAAG1D,KAAK;EAC9C,IAAIiH,eAAe,GAAG;IACpBH,WAAW,EAAEA,CAAA,KAAM,CACnB,CAAC;IACDC,QAAQ,EAAEA,CAAA,KAAM;EAClB,CAAC;EACD,IAAIjD,iBAAiB,EAAE;IACrB,CAAC;MAAEmD,eAAe;MAAE3D,QAAQ;MAAEC,QAAQ;MAAEG;IAAW,CAAC,GAAGsD,kBAAkB,CAAChH,KAAK,CAAC;EAClF;EACA,OAAO;IACLyE,QAAQ,EAAEwC,eAAe;IACzBE,IAAI,EAAEN,gBAAgB,CAAC,MAAM,CAAC,YAAY5D,OAAO,EAAEG,MAAM,CAAC;IAC1DF,cAAc,EAAE2D,gBAAgB,CAAC,gBAAgB,CAAC,YAAY3D,cAAc,EAAEE,MAAM,CAAC;IACrFC,cAAc,EAAEwD,gBAAgB,CAAC,gBAAgB,CAAC,YAAYxD,cAAc,EAAED,MAAM,CAAC;IACrFxF,IAAI,EAAEiJ,gBAAgB,CAAC,MAAM,CAAC,YAAYvD,QAAQ,EAAEF,MAAM,CAAC;IAC3DpF,IAAI,EAAE6I,gBAAgB,CAAC,MAAM,CAAC,YAAYtD,QAAQ,EAAEH,MAAM,CAAC;IAC3DgE,QAAQ,EAAEP,gBAAgB,CAAC,UAAU,CAAC,gBAAgBrD,YAAY,EAAEJ,MAAM,CAAC;IAC3EtF,MAAM,EAAE+I,gBAAgB,CAAC,QAAQ,CAAC,cAAcnD,UAAU,EAAEN,MAAM,CAAC;IACnEiE,KAAK,EAAER,gBAAgB,CAAC,OAAO,CAAC,aAAalD,SAAS,EAAEP,MAAM,CAAC;IAC/DQ,KAAK,EAAEiD,gBAAgB,CAAC,OAAO,CAAC,aAAa;MAAEjD,KAAK;MAAEC;IAAgB,CAAC,EAAET,MAAM;EACjF,CAAC;AACH,CAAC;AACD,IAAIkE,cAAc,GAAGJ,gBAAgB;;AAErC;AACA,IAAIK,SAAS,GAAG,EAAE;AAClB,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACpB,aAAa,CAAC;AACtG,IAAIqB,UAAU,GAAG,MAAM;EACrBJ,SAAS,GAAG,EAAE;EACdK,SAAS,GAAGJ,UAAU;EACtBK,OAAO;EACP1I,KAAK,GAAG;IACN2I,SAAS,EAAGC,WAAW,IAAK;MAC1B,IAAI,CAACF,OAAO,CAACG,MAAM,GAAGD,WAAW;IACnC,CAAC;IACDE,eAAe,EAAE;MACfC,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACN,SAAS,GAAGL,SAAS,GAAG,IAAI,CAACA,SAAS;MACtDY,GAAG,EAAGC,QAAQ,IAAK;QACjB,CAAC,IAAI,CAACR,SAAS,GAAGL,SAAS,GAAG,IAAI,CAACA,SAAS,EAAE7E,IAAI,CAAC0F,QAAQ,CAAC;MAC9D,CAAC;MACDC,MAAM,EAAGD,QAAQ,IAAK;QACpB,MAAMtF,KAAK,GAAG,CAAC,IAAI,CAAC8E,SAAS,GAAGL,SAAS,GAAG,IAAI,CAACA,SAAS,EAAE3F,OAAO,CAACwG,QAAQ,CAAC;QAC7E,CAAC,IAAI,CAACR,SAAS,GAAGL,SAAS,GAAG,IAAI,CAACA,SAAS,EAAEe,MAAM,CAACxF,KAAK,EAAE,CAAC,CAAC;MAChE;IACF;EACF,CAAC;EACDyF,WAAWA,CAACV,OAAO,EAAED,SAAS,EAAE;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,SAAS,GAAGA,SAAS,IAAI,KAAK;IACnC,IAAI,CAACA,SAAS,EAAE;MACdC,OAAO,CAACG,MAAM,GAAGV,cAAc,CAAC;QAC9BrE,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,CAAC,CAAC;QAClB4B,uBAAuB,EAAE,IAAI;QAC7BzB,cAAc,EAAE,CAAC,CAAC;QAClBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,YAAY,EAAE,EAAE;QAChBE,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,eAAe,EAAE,CAAC;MACpB,CAAC,CAAC;IACJ;EACF;AACF,CAAC;;AAED;AACA,IAAI2E,YAAY,GAAG,CAAC,CAAC;AACrB,IAAIC,OAAO,GAAGlL,MAAM,CAACmL,aAAa,CAACF,YAAY,CAAC;AAChD,IAAIG,cAAc,GAAG,MAAMC,eAAe,SAASzL,SAAS,CAAC;EAC3D,OAAOyK,SAAS,GAAGJ,UAAU;EAC7BqB,UAAU;EACVN,WAAWA,CAACvI,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC6I,UAAU,GAAG,IAAIlB,UAAU,CAAC,IAAI,CAAC3H,KAAK,CAAC6H,OAAO,IAAI,CAAC,CAAC,EAAEe,eAAe,CAAChB,SAAS,CAAC;EACvF;EACAkB,MAAMA,CAAA,EAAG;IACP,OAAO,eAAgBvL,MAAM,CAAC+I,aAAa,CAACmC,OAAO,CAACM,QAAQ,EAAE;MAAE5J,KAAK,EAAE,IAAI,CAAC0J,UAAU,CAAC1J;IAAM,CAAC,EAAE,IAAI,CAACa,KAAK,CAACgJ,QAAQ,CAAC;EACtH;AACF,CAAC;;AAED;AACA,SAAS7L,SAAS,IAAI8L,UAAU,QAAQ,OAAO;AAC/C,OAAOC,YAAY,MAAM,cAAc;;AAEvC;AACA,IAAIC,UAAU,GAAGA,CAACpL,IAAI,EAAEyH,IAAI,KAAK;EAC/B,MAAM4D,WAAW,GAAG1B,QAAQ,CAAC2B,IAAI,IAAI3B,QAAQ,CAAC4B,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC;EAC9E,MAAMC,QAAQ,GAAGH,WAAW,CAACI,gBAAgB,CAAC,GAAGzL,IAAI,IAAIqB,gBAAgB,GAAG,CAAC;EAC7E,MAAMqK,OAAO,GAAG,EAAE,CAACC,KAAK,CAACvJ,IAAI,CAACoJ,QAAQ,CAAC;EACvC,MAAMI,OAAO,GAAG,EAAE;EAClB,IAAIC,aAAa;EACjB,IAAIpE,IAAI,IAAIA,IAAI,CAACzF,MAAM,EAAE;IACvByF,IAAI,CAAC/C,OAAO,CAAElB,GAAG,IAAK;MACpB,MAAMsI,UAAU,GAAGnC,QAAQ,CAACpB,aAAa,CAACvI,IAAI,CAAC;MAC/C,KAAK,MAAM4H,SAAS,IAAIpE,GAAG,EAAE;QAC3B,IAAIlD,MAAM,CAAC4B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACoB,GAAG,EAAEoE,SAAS,CAAC,EAAE;UACxD,IAAIA,SAAS,KAAK,WAAW,CAAC,kBAAkB;YAC9CkE,UAAU,CAAC/D,SAAS,GAAGvE,GAAG,CAACuE,SAAS;UACtC,CAAC,MAAM,IAAIH,SAAS,KAAK,SAAS,CAAC,gBAAgB;YACjD,IAAIkE,UAAU,CAACC,UAAU,EAAE;cACzBD,UAAU,CAACC,UAAU,CAAC/D,OAAO,GAAGxE,GAAG,CAACwE,OAAO;YAC7C,CAAC,MAAM;cACL8D,UAAU,CAACE,WAAW,CAACrC,QAAQ,CAACsC,cAAc,CAACzI,GAAG,CAACwE,OAAO,CAAC,CAAC;YAC9D;UACF,CAAC,MAAM;YACL,MAAMZ,IAAI,GAAGQ,SAAS;YACtB,MAAMxG,KAAK,GAAG,OAAOoC,GAAG,CAAC4D,IAAI,CAAC,KAAK,WAAW,GAAG,EAAE,GAAG5D,GAAG,CAAC4D,IAAI,CAAC;YAC/D0E,UAAU,CAACI,YAAY,CAACtE,SAAS,EAAExG,KAAK,CAAC;UAC3C;QACF;MACF;MACA0K,UAAU,CAACI,YAAY,CAAC7K,gBAAgB,EAAE,MAAM,CAAC;MACjD,IAAIqK,OAAO,CAACS,IAAI,CAAC,CAACC,WAAW,EAAErH,KAAK,KAAK;QACvC8G,aAAa,GAAG9G,KAAK;QACrB,OAAO+G,UAAU,CAACO,WAAW,CAACD,WAAW,CAAC;MAC5C,CAAC,CAAC,EAAE;QACFV,OAAO,CAACnB,MAAM,CAACsB,aAAa,EAAE,CAAC,CAAC;MAClC,CAAC,MAAM;QACLD,OAAO,CAACjH,IAAI,CAACmH,UAAU,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ;EACAJ,OAAO,CAAChH,OAAO,CAAElB,GAAG,IAAKA,GAAG,CAAC8I,UAAU,EAAEC,WAAW,CAAC/I,GAAG,CAAC,CAAC;EAC1DoI,OAAO,CAAClH,OAAO,CAAElB,GAAG,IAAK6H,WAAW,CAACW,WAAW,CAACxI,GAAG,CAAC,CAAC;EACtD,OAAO;IACLkI,OAAO;IACPE;EACF,CAAC;AACH,CAAC;AACD,IAAIY,gBAAgB,GAAGA,CAACrI,OAAO,EAAEgD,UAAU,KAAK;EAC9C,MAAMsF,UAAU,GAAG9C,QAAQ,CAAC+C,oBAAoB,CAACvI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC5D,IAAI,CAACsI,UAAU,EAAE;IACf;EACF;EACA,MAAME,qBAAqB,GAAGF,UAAU,CAACG,YAAY,CAACvL,gBAAgB,CAAC;EACvE,MAAMwL,gBAAgB,GAAGF,qBAAqB,GAAGA,qBAAqB,CAACG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;EACtF,MAAMC,kBAAkB,GAAG,CAAC,GAAGF,gBAAgB,CAAC;EAChD,MAAMG,aAAa,GAAG1M,MAAM,CAACmD,IAAI,CAAC0D,UAAU,CAAC;EAC7C,KAAK,MAAMS,SAAS,IAAIoF,aAAa,EAAE;IACrC,MAAM5L,KAAK,GAAG+F,UAAU,CAACS,SAAS,CAAC,IAAI,EAAE;IACzC,IAAI6E,UAAU,CAACG,YAAY,CAAChF,SAAS,CAAC,KAAKxG,KAAK,EAAE;MAChDqL,UAAU,CAACP,YAAY,CAACtE,SAAS,EAAExG,KAAK,CAAC;IAC3C;IACA,IAAIyL,gBAAgB,CAAChJ,OAAO,CAAC+D,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9CiF,gBAAgB,CAAClI,IAAI,CAACiD,SAAS,CAAC;IAClC;IACA,MAAMqF,WAAW,GAAGF,kBAAkB,CAAClJ,OAAO,CAAC+D,SAAS,CAAC;IACzD,IAAIqF,WAAW,KAAK,CAAC,CAAC,EAAE;MACtBF,kBAAkB,CAACxC,MAAM,CAAC0C,WAAW,EAAE,CAAC,CAAC;IAC3C;EACF;EACA,KAAK,IAAIlL,CAAC,GAAGgL,kBAAkB,CAAC/K,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IAC1D0K,UAAU,CAACS,eAAe,CAACH,kBAAkB,CAAChL,CAAC,CAAC,CAAC;EACnD;EACA,IAAI8K,gBAAgB,CAAC7K,MAAM,KAAK+K,kBAAkB,CAAC/K,MAAM,EAAE;IACzDyK,UAAU,CAACS,eAAe,CAAC7L,gBAAgB,CAAC;EAC9C,CAAC,MAAM,IAAIoL,UAAU,CAACG,YAAY,CAACvL,gBAAgB,CAAC,KAAK2L,aAAa,CAACtK,IAAI,CAAC,GAAG,CAAC,EAAE;IAChF+J,UAAU,CAACP,YAAY,CAAC7K,gBAAgB,EAAE2L,aAAa,CAACtK,IAAI,CAAC,GAAG,CAAC,CAAC;EACpE;AACF,CAAC;AACD,IAAIyK,WAAW,GAAGA,CAACtH,KAAK,EAAEsB,UAAU,KAAK;EACvC,IAAI,OAAOtB,KAAK,KAAK,WAAW,IAAI8D,QAAQ,CAAC9D,KAAK,KAAKA,KAAK,EAAE;IAC5D8D,QAAQ,CAAC9D,KAAK,GAAGG,YAAY,CAACH,KAAK,CAAC;EACtC;EACA2G,gBAAgB,CAAC,OAAO,CAAC,aAAarF,UAAU,CAAC;AACnD,CAAC;AACD,IAAIiG,gBAAgB,GAAGA,CAACC,QAAQ,EAAEC,EAAE,KAAK;EACvC,MAAM;IACJpI,OAAO;IACPC,cAAc;IACdG,cAAc;IACdC,QAAQ;IACRC,QAAQ;IACRC,YAAY;IACZC,mBAAmB;IACnBC,UAAU;IACVC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAGuH,QAAQ;EACZb,gBAAgB,CAAC,MAAM,CAAC,YAAYrH,cAAc,CAAC;EACnDqH,gBAAgB,CAAC,MAAM,CAAC,YAAYlH,cAAc,CAAC;EACnD6H,WAAW,CAACtH,KAAK,EAAEC,eAAe,CAAC;EACnC,MAAMyH,UAAU,GAAG;IACjBrI,OAAO,EAAEkG,UAAU,CAAC,MAAM,CAAC,YAAYlG,OAAO,CAAC;IAC/CK,QAAQ,EAAE6F,UAAU,CAAC,MAAM,CAAC,YAAY7F,QAAQ,CAAC;IACjDC,QAAQ,EAAE4F,UAAU,CAAC,MAAM,CAAC,YAAY5F,QAAQ,CAAC;IACjDC,YAAY,EAAE2F,UAAU,CAAC,UAAU,CAAC,gBAAgB3F,YAAY,CAAC;IACjEE,UAAU,EAAEyF,UAAU,CAAC,QAAQ,CAAC,cAAczF,UAAU,CAAC;IACzDC,SAAS,EAAEwF,UAAU,CAAC,OAAO,CAAC,aAAaxF,SAAS;EACtD,CAAC;EACD,MAAM4H,SAAS,GAAG,CAAC,CAAC;EACpB,MAAMC,WAAW,GAAG,CAAC,CAAC;EACtBnN,MAAM,CAACmD,IAAI,CAAC8J,UAAU,CAAC,CAAC7I,OAAO,CAAE3B,OAAO,IAAK;IAC3C,MAAM;MAAE6I,OAAO;MAAEF;IAAQ,CAAC,GAAG6B,UAAU,CAACxK,OAAO,CAAC;IAChD,IAAI6I,OAAO,CAAC5J,MAAM,EAAE;MAClBwL,SAAS,CAACzK,OAAO,CAAC,GAAG6I,OAAO;IAC9B;IACA,IAAIF,OAAO,CAAC1J,MAAM,EAAE;MAClByL,WAAW,CAAC1K,OAAO,CAAC,GAAGwK,UAAU,CAACxK,OAAO,CAAC,CAAC2I,OAAO;IACpD;EACF,CAAC,CAAC;EACF,IAAI4B,EAAE,EAAE;IACNA,EAAE,CAAC,CAAC;EACN;EACA5H,mBAAmB,CAAC2H,QAAQ,EAAEG,SAAS,EAAEC,WAAW,CAAC;AACvD,CAAC;AACD,IAAIC,eAAe,GAAG,IAAI;AAC1B,IAAIC,yBAAyB,GAAIN,QAAQ,IAAK;EAC5C,IAAIK,eAAe,EAAE;IACnBE,oBAAoB,CAACF,eAAe,CAAC;EACvC;EACA,IAAIL,QAAQ,CAACjI,KAAK,EAAE;IAClBsI,eAAe,GAAGG,qBAAqB,CAAC,MAAM;MAC5CT,gBAAgB,CAACC,QAAQ,EAAE,MAAM;QAC/BK,eAAe,GAAG,IAAI;MACxB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLN,gBAAgB,CAACC,QAAQ,CAAC;IAC1BK,eAAe,GAAG,IAAI;EACxB;AACF,CAAC;AACD,IAAII,cAAc,GAAGH,yBAAyB;;AAE9C;AACA,IAAII,gBAAgB,GAAG,cAAc7C,UAAU,CAAC;EAC9C8C,QAAQ,GAAG,KAAK;EAChBC,qBAAqBA,CAACC,SAAS,EAAE;IAC/B,OAAO,CAAC/C,YAAY,CAAC+C,SAAS,EAAE,IAAI,CAACjM,KAAK,CAAC;EAC7C;EACAkM,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAACC,UAAU,CAAC,CAAC;EACnB;EACAC,oBAAoBA,CAAA,EAAG;IACrB,MAAM;MAAEnE;IAAgB,CAAC,GAAG,IAAI,CAACjI,KAAK,CAAC6H,OAAO;IAC9CI,eAAe,CAACI,MAAM,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC8D,UAAU,CAAC,CAAC;EACnB;EACAA,UAAUA,CAAA,EAAG;IACX,MAAM;MAAElE,eAAe;MAAEH;IAAU,CAAC,GAAG,IAAI,CAAC9H,KAAK,CAAC6H,OAAO;IACzD,IAAIE,WAAW,GAAG,IAAI;IACtB,MAAMsE,KAAK,GAAGrJ,kBAAkB,CAC9BiF,eAAe,CAACC,GAAG,CAAC,CAAC,CAAClH,GAAG,CAAEoH,QAAQ,IAAK;MACtC,MAAMpI,KAAK,GAAG;QAAE,GAAGoI,QAAQ,CAACpI;MAAM,CAAC;MACnC,OAAOA,KAAK,CAAC6H,OAAO;MACpB,OAAO7H,KAAK;IACd,CAAC,CACH,CAAC;IACD,IAAI2I,cAAc,CAACf,SAAS,EAAE;MAC5BiE,cAAc,CAACQ,KAAK,CAAC;IACvB,CAAC,MAAM,IAAI/E,cAAc,EAAE;MACzBS,WAAW,GAAGT,cAAc,CAAC+E,KAAK,CAAC;IACrC;IACAvE,SAAS,CAACC,WAAW,CAAC;EACxB;EACA;EACA;EACA;EACAuE,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAACP,QAAQ,EAAE;MACjB;IACF;IACA,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,MAAM;MAAE9D;IAAgB,CAAC,GAAG,IAAI,CAACjI,KAAK,CAAC6H,OAAO;IAC9CI,eAAe,CAACE,GAAG,CAAC,IAAI,CAAC;IACzB,IAAI,CAACgE,UAAU,CAAC,CAAC;EACnB;EACArD,MAAMA,CAAA,EAAG;IACP,IAAI,CAACwD,IAAI,CAAC,CAAC;IACX,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,IAAIC,MAAM,GAAG,cAAcnP,UAAU,CAAC;EACpC,OAAOoP,YAAY,GAAG;IACpBrJ,KAAK,EAAE,IAAI;IACX2B,uBAAuB,EAAE,IAAI;IAC7BhB,iBAAiB,EAAE;EACrB,CAAC;EACDkI,qBAAqBA,CAACC,SAAS,EAAE;IAC/B,OAAO,CAAC5O,WAAW,CAACsH,OAAO,CAAC,IAAI,CAAC3E,KAAK,EAAE,YAAY,CAAC,EAAE2E,OAAO,CAACsH,SAAS,EAAE,YAAY,CAAC,CAAC;EAC1F;EACAQ,wBAAwBA,CAACC,KAAK,EAAEC,cAAc,EAAE;IAC9C,IAAI,CAACA,cAAc,EAAE;MACnB,OAAO,IAAI;IACb;IACA,QAAQD,KAAK,CAAC3O,IAAI;MAChB,KAAK,QAAQ,CAAC;MACd,KAAK,UAAU,CAAC;QACd,OAAO;UACL+H,SAAS,EAAE6G;QACb,CAAC;MACH,KAAK,OAAO,CAAC;QACX,OAAO;UACL5G,OAAO,EAAE4G;QACX,CAAC;MACH;QACE,MAAM,IAAIC,KAAK,CACb,IAAIF,KAAK,CAAC3O,IAAI,oGAChB,CAAC;IACL;EACF;EACA8O,wBAAwBA,CAACH,KAAK,EAAEI,iBAAiB,EAAEC,aAAa,EAAEJ,cAAc,EAAE;IAChF,OAAO;MACL,GAAGG,iBAAiB;MACpB,CAACJ,KAAK,CAAC3O,IAAI,GAAG,CACZ,IAAG+O,iBAAiB,CAACJ,KAAK,CAAC3O,IAAI,CAAC,IAAI,EAAE,GACtC;QACE,GAAGgP,aAAa;QAChB,GAAG,IAAI,CAACN,wBAAwB,CAACC,KAAK,EAAEC,cAAc;MACxD,CAAC;IAEL,CAAC;EACH;EACAK,qBAAqBA,CAACN,KAAK,EAAEO,QAAQ,EAAEF,aAAa,EAAEJ,cAAc,EAAE;IACpE,QAAQD,KAAK,CAAC3O,IAAI;MAChB,KAAK,OAAO,CAAC;QACX,OAAO;UACL,GAAGkP,QAAQ;UACX,CAACP,KAAK,CAAC3O,IAAI,GAAG4O,cAAc;UAC5B9I,eAAe,EAAE;YAAE,GAAGkJ;UAAc;QACtC,CAAC;MACH,KAAK,MAAM,CAAC;QACV,OAAO;UACL,GAAGE,QAAQ;UACX/J,cAAc,EAAE;YAAE,GAAG6J;UAAc;QACrC,CAAC;MACH,KAAK,MAAM,CAAC;QACV,OAAO;UACL,GAAGE,QAAQ;UACX5J,cAAc,EAAE;YAAE,GAAG0J;UAAc;QACrC,CAAC;MACH;QACE,OAAO;UACL,GAAGE,QAAQ;UACX,CAACP,KAAK,CAAC3O,IAAI,GAAG;YAAE,GAAGgP;UAAc;QACnC,CAAC;IACL;EACF;EACAG,2BAA2BA,CAACJ,iBAAiB,EAAEG,QAAQ,EAAE;IACvD,IAAIE,iBAAiB,GAAG;MAAE,GAAGF;IAAS,CAAC;IACvC5O,MAAM,CAACmD,IAAI,CAACsL,iBAAiB,CAAC,CAACrK,OAAO,CAAE2K,cAAc,IAAK;MACzDD,iBAAiB,GAAG;QAClB,GAAGA,iBAAiB;QACpB,CAACC,cAAc,GAAGN,iBAAiB,CAACM,cAAc;MACpD,CAAC;IACH,CAAC,CAAC;IACF,OAAOD,iBAAiB;EAC1B;EACAE,qBAAqBA,CAACX,KAAK,EAAEC,cAAc,EAAE;IAC3CrP,SAAS,CACPc,eAAe,CAAC8L,IAAI,CAAEhM,IAAI,IAAKwO,KAAK,CAAC3O,IAAI,KAAKG,IAAI,CAAC,EACnD,OAAOwO,KAAK,CAAC3O,IAAI,KAAK,UAAU,GAAG,mIAAmI,GAAG,uBAAuBK,eAAe,CAACqC,IAAI,CAClN,IACF,CAAC,oDAAoDiM,KAAK,CAAC3O,IAAI,oDACjE,CAAC;IACDT,SAAS,CACP,CAACqP,cAAc,IAAI,OAAOA,cAAc,KAAK,QAAQ,IAAIpM,KAAK,CAACC,OAAO,CAACmM,cAAc,CAAC,IAAI,CAACA,cAAc,CAACzC,IAAI,CAAEoD,WAAW,IAAK,OAAOA,WAAW,KAAK,QAAQ,CAAC,EAChK,0CAA0CZ,KAAK,CAAC3O,IAAI,yDAAyD2O,KAAK,CAAC3O,IAAI,YAAY2O,KAAK,CAAC3O,IAAI,4CAC/I,CAAC;IACD,OAAO,IAAI;EACb;EACAwP,kBAAkBA,CAACvE,QAAQ,EAAEiE,QAAQ,EAAE;IACrC,IAAIH,iBAAiB,GAAG,CAAC,CAAC;IAC1B5P,MAAM,CAACsQ,QAAQ,CAAC/K,OAAO,CAACuG,QAAQ,EAAG0D,KAAK,IAAK;MAC3C,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAAC1M,KAAK,EAAE;QAC1B;MACF;MACA,MAAM;QAAEgJ,QAAQ,EAAE2D,cAAc;QAAE,GAAGc;MAAW,CAAC,GAAGf,KAAK,CAAC1M,KAAK;MAC/D,MAAM+M,aAAa,GAAG1O,MAAM,CAACmD,IAAI,CAACiM,UAAU,CAAC,CAACzO,MAAM,CAAC,CAAC4F,GAAG,EAAE1F,GAAG,KAAK;QACjE0F,GAAG,CAAC9F,YAAY,CAACI,GAAG,CAAC,IAAIA,GAAG,CAAC,GAAGuO,UAAU,CAACvO,GAAG,CAAC;QAC/C,OAAO0F,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,IAAI;QAAE7G;MAAK,CAAC,GAAG2O,KAAK;MACpB,IAAI,OAAO3O,IAAI,KAAK,QAAQ,EAAE;QAC5BA,IAAI,GAAGA,IAAI,CAACgJ,QAAQ,CAAC,CAAC;MACxB,CAAC,MAAM;QACL,IAAI,CAACsG,qBAAqB,CAACX,KAAK,EAAEC,cAAc,CAAC;MACnD;MACA,QAAQ5O,IAAI;QACV,KAAK,wBAAwB,CAAC;UAC5BkP,QAAQ,GAAG,IAAI,CAACM,kBAAkB,CAACZ,cAAc,EAAEM,QAAQ,CAAC;UAC5D;QACF,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM,CAAC;QACZ,KAAK,UAAU,CAAC;QAChB,KAAK,QAAQ,CAAC;QACd,KAAK,OAAO,CAAC;UACXH,iBAAiB,GAAG,IAAI,CAACD,wBAAwB,CAC/CH,KAAK,EACLI,iBAAiB,EACjBC,aAAa,EACbJ,cACF,CAAC;UACD;QACF;UACEM,QAAQ,GAAG,IAAI,CAACD,qBAAqB,CAACN,KAAK,EAAEO,QAAQ,EAAEF,aAAa,EAAEJ,cAAc,CAAC;UACrF;MACJ;IACF,CAAC,CAAC;IACF,OAAO,IAAI,CAACO,2BAA2B,CAACJ,iBAAiB,EAAEG,QAAQ,CAAC;EACtE;EACAnE,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEE,QAAQ;MAAE,GAAGhJ;IAAM,CAAC,GAAG,IAAI,CAACA,KAAK;IACzC,IAAIiN,QAAQ,GAAG;MAAE,GAAGjN;IAAM,CAAC;IAC3B,IAAI;MAAE6I;IAAW,CAAC,GAAG7I,KAAK;IAC1B,IAAIgJ,QAAQ,EAAE;MACZiE,QAAQ,GAAG,IAAI,CAACM,kBAAkB,CAACvE,QAAQ,EAAEiE,QAAQ,CAAC;IACxD;IACA,IAAIpE,UAAU,IAAI,EAAEA,UAAU,YAAYlB,UAAU,CAAC,EAAE;MACrD,MAAM+F,IAAI,GAAG7E,UAAU;MACvBA,UAAU,GAAG,IAAIlB,UAAU,CAAC+F,IAAI,CAAC7F,OAAO,EAAE,IAAI,CAAC;MAC/C,OAAOoF,QAAQ,CAACpE,UAAU;IAC5B;IACA,OAAOA,UAAU,GAAG,eAAgB3L,MAAM,CAACoJ,aAAa,CAACwF,gBAAgB,EAAE;MAAE,GAAGmB,QAAQ;MAAEpF,OAAO,EAAEgB,UAAU,CAAC1J;IAAM,CAAC,CAAC,GAAG,eAAgBjC,MAAM,CAACoJ,aAAa,CAACmC,OAAO,CAACkF,QAAQ,EAAE,IAAI,EAAG9F,OAAO,IAAK,eAAgB3K,MAAM,CAACoJ,aAAa,CAACwF,gBAAgB,EAAE;MAAE,GAAGmB,QAAQ;MAAEpF;IAAQ,CAAC,CAAC,CAAC;EACtR;AACF,CAAC;AACD,SACE0E,MAAM,EACN5E,UAAU,EACVgB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}