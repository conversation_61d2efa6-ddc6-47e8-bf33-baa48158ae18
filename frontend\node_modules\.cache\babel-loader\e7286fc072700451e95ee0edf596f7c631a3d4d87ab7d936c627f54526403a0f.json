{"ast": null, "code": "import * as React from 'react';\nimport invariant from 'invariant';\nimport { useUncontrolled } from 'uncontrollable';\nimport chainFunction from './createChainedFunction';\nimport { map } from './ElementChildren';\nimport ButtonGroup from './ButtonGroup';\nimport ToggleButton from './ToggleButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ToggleButtonGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    children,\n    type = 'radio',\n    name,\n    value,\n    onChange,\n    vertical = false,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    value: 'onChange'\n  });\n  const getValues = () => value == null ? [] : [].concat(value);\n  const handleToggle = (inputVal, event) => {\n    if (!onChange) {\n      return;\n    }\n    const values = getValues();\n    const isActive = values.indexOf(inputVal) !== -1;\n    if (type === 'radio') {\n      if (!isActive) onChange(inputVal, event);\n      return;\n    }\n    if (isActive) {\n      onChange(values.filter(n => n !== inputVal), event);\n    } else {\n      onChange([...values, inputVal], event);\n    }\n  };\n  !(type !== 'radio' || !!name) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A `name` is required to group the toggle buttons when the `type` ' + 'is set to \"radio\"') : invariant(false) : void 0;\n  return /*#__PURE__*/_jsx(ButtonGroup, {\n    ...controlledProps,\n    ref: ref,\n    vertical: vertical,\n    children: map(children, child => {\n      const values = getValues();\n      const {\n        value: childVal,\n        onChange: childOnChange\n      } = child.props;\n      const handler = e => handleToggle(childVal, e);\n      return /*#__PURE__*/React.cloneElement(child, {\n        type,\n        name: child.name || name,\n        checked: values.indexOf(childVal) !== -1,\n        onChange: chainFunction(childOnChange, handler)\n      });\n    })\n  });\n});\nToggleButtonGroup.displayName = 'ToggleButtonGroup';\nexport default Object.assign(ToggleButtonGroup, {\n  Button: ToggleButton\n});", "map": {"version": 3, "names": ["React", "invariant", "useUncontrolled", "chainFunction", "map", "ButtonGroup", "ToggleButton", "jsx", "_jsx", "ToggleButtonGroup", "forwardRef", "props", "ref", "children", "type", "name", "value", "onChange", "vertical", "controlledProps", "getV<PERSON>ues", "concat", "handleToggle", "inputVal", "event", "values", "isActive", "indexOf", "filter", "n", "process", "env", "NODE_ENV", "child", "childVal", "child<PERSON>n<PERSON><PERSON><PERSON>", "handler", "e", "cloneElement", "checked", "displayName", "Object", "assign", "<PERSON><PERSON>"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/ToggleButtonGroup.js"], "sourcesContent": ["import * as React from 'react';\nimport invariant from 'invariant';\nimport { useUncontrolled } from 'uncontrollable';\nimport chainFunction from './createChainedFunction';\nimport { map } from './ElementChildren';\nimport ButtonGroup from './ButtonGroup';\nimport ToggleButton from './ToggleButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ToggleButtonGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    children,\n    type = 'radio',\n    name,\n    value,\n    onChange,\n    vertical = false,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    value: 'onChange'\n  });\n  const getValues = () => value == null ? [] : [].concat(value);\n  const handleToggle = (inputVal, event) => {\n    if (!onChange) {\n      return;\n    }\n    const values = getValues();\n    const isActive = values.indexOf(inputVal) !== -1;\n    if (type === 'radio') {\n      if (!isActive) onChange(inputVal, event);\n      return;\n    }\n    if (isActive) {\n      onChange(values.filter(n => n !== inputVal), event);\n    } else {\n      onChange([...values, inputVal], event);\n    }\n  };\n  !(type !== 'radio' || !!name) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A `name` is required to group the toggle buttons when the `type` ' + 'is set to \"radio\"') : invariant(false) : void 0;\n  return /*#__PURE__*/_jsx(ButtonGroup, {\n    ...controlledProps,\n    ref: ref,\n    vertical: vertical,\n    children: map(children, child => {\n      const values = getValues();\n      const {\n        value: childVal,\n        onChange: childOnChange\n      } = child.props;\n      const handler = e => handleToggle(childVal, e);\n      return /*#__PURE__*/React.cloneElement(child, {\n        type,\n        name: child.name || name,\n        checked: values.indexOf(childVal) !== -1,\n        onChange: chainFunction(childOnChange, handler)\n      });\n    })\n  });\n});\nToggleButtonGroup.displayName = 'ToggleButtonGroup';\nexport default Object.assign(ToggleButtonGroup, {\n  Button: ToggleButton\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,GAAG,QAAQ,mBAAmB;AACvC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAG,aAAaT,KAAK,CAACU,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACtE,MAAM;IACJC,QAAQ;IACRC,IAAI,GAAG,OAAO;IACdC,IAAI;IACJC,KAAK;IACLC,QAAQ;IACRC,QAAQ,GAAG,KAAK;IAChB,GAAGC;EACL,CAAC,GAAGjB,eAAe,CAACS,KAAK,EAAE;IACzBK,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMI,SAAS,GAAGA,CAAA,KAAMJ,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAACK,MAAM,CAACL,KAAK,CAAC;EAC7D,MAAMM,YAAY,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;IACxC,IAAI,CAACP,QAAQ,EAAE;MACb;IACF;IACA,MAAMQ,MAAM,GAAGL,SAAS,CAAC,CAAC;IAC1B,MAAMM,QAAQ,GAAGD,MAAM,CAACE,OAAO,CAACJ,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,IAAIT,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAACY,QAAQ,EAAET,QAAQ,CAACM,QAAQ,EAAEC,KAAK,CAAC;MACxC;IACF;IACA,IAAIE,QAAQ,EAAE;MACZT,QAAQ,CAACQ,MAAM,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKN,QAAQ,CAAC,EAAEC,KAAK,CAAC;IACrD,CAAC,MAAM;MACLP,QAAQ,CAAC,CAAC,GAAGQ,MAAM,EAAEF,QAAQ,CAAC,EAAEC,KAAK,CAAC;IACxC;EACF,CAAC;EACD,EAAEV,IAAI,KAAK,OAAO,IAAI,CAAC,CAACC,IAAI,CAAC,GAAGe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/B,SAAS,CAAC,KAAK,EAAE,mEAAmE,GAAG,mBAAmB,CAAC,GAAGA,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EAC/M,OAAO,aAAaO,IAAI,CAACH,WAAW,EAAE;IACpC,GAAGc,eAAe;IAClBP,GAAG,EAAEA,GAAG;IACRM,QAAQ,EAAEA,QAAQ;IAClBL,QAAQ,EAAET,GAAG,CAACS,QAAQ,EAAEoB,KAAK,IAAI;MAC/B,MAAMR,MAAM,GAAGL,SAAS,CAAC,CAAC;MAC1B,MAAM;QACJJ,KAAK,EAAEkB,QAAQ;QACfjB,QAAQ,EAAEkB;MACZ,CAAC,GAAGF,KAAK,CAACtB,KAAK;MACf,MAAMyB,OAAO,GAAGC,CAAC,IAAIf,YAAY,CAACY,QAAQ,EAAEG,CAAC,CAAC;MAC9C,OAAO,aAAarC,KAAK,CAACsC,YAAY,CAACL,KAAK,EAAE;QAC5CnB,IAAI;QACJC,IAAI,EAAEkB,KAAK,CAAClB,IAAI,IAAIA,IAAI;QACxBwB,OAAO,EAAEd,MAAM,CAACE,OAAO,CAACO,QAAQ,CAAC,KAAK,CAAC,CAAC;QACxCjB,QAAQ,EAAEd,aAAa,CAACgC,aAAa,EAAEC,OAAO;MAChD,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACF3B,iBAAiB,CAAC+B,WAAW,GAAG,mBAAmB;AACnD,eAAeC,MAAM,CAACC,MAAM,CAACjC,iBAAiB,EAAE;EAC9CkC,MAAM,EAAErC;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}