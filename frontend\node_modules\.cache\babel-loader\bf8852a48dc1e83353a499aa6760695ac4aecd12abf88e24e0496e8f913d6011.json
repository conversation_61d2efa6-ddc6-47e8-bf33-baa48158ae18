{"ast": null, "code": "var _NavigationDiagnostics;/**\n * Comprehensive navigation diagnostics to identify why navigation becomes unresponsive\n */class NavigationDiagnostics{constructor(){this.diagnostics=[];this.isMonitoring=false;this.originalNavigate=null;this.handleClick=e=>{const target=e.target;const isNavElement=target.closest('[data-testid=\"sidebar-nav\"]')||target.closest('.nav-link')||target.closest('a[href^=\"/dashboard\"]');if(isNavElement){var _target$textContent;this.log('NAVIGATION_CLICK',{target:(_target$textContent=target.textContent)===null||_target$textContent===void 0?void 0:_target$textContent.trim(),path:target.getAttribute('data-path'),timestamp:Date.now(),eventPhase:e.eventPhase,bubbles:e.bubbles,cancelable:e.cancelable,defaultPrevented:e.defaultPrevented,isTrusted:e.isTrusted});}};}static getInstance(){if(!NavigationDiagnostics.instance){NavigationDiagnostics.instance=new NavigationDiagnostics();}return NavigationDiagnostics.instance;}startMonitoring(){if(this.isMonitoring)return;this.isMonitoring=true;this.log('MONITORING_STARTED',{timestamp:Date.now()});// Monitor all click events\ndocument.addEventListener('click',this.handleClick,true);// Monitor all navigation attempts\nthis.interceptNavigationCalls();// Monitor React Router history changes\nthis.monitorHistoryChanges();// Monitor for blocked events\nthis.monitorBlockedEvents();// Monitor for memory leaks\nthis.monitorMemoryLeaks();}stopMonitoring(){if(!this.isMonitoring)return;this.isMonitoring=false;document.removeEventListener('click',this.handleClick,true);this.restoreNavigationCalls();this.log('MONITORING_STOPPED',{timestamp:Date.now()});}interceptNavigationCalls(){var _this=this;// Only intercept in development mode to avoid production interference\nif(process.env.NODE_ENV!=='development'){return;}// Store original functions for restoration\nif(!this.originalNavigate){this.originalNavigate={pushState:window.history.pushState,replaceState:window.history.replaceState};}// This is a bit hacky but necessary for debugging\nconst originalPushState=this.originalNavigate.pushState;const originalReplaceState=this.originalNavigate.replaceState;window.history.pushState=function(){for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++){args[_key]=arguments[_key];}_this.log('HISTORY_PUSH_STATE',{args,timestamp:Date.now()});try{return originalPushState.apply(window.history,args);}catch(error){console.error('Navigation pushState error:',error);throw error;}};window.history.replaceState=function(){for(var _len2=arguments.length,args=new Array(_len2),_key2=0;_key2<_len2;_key2++){args[_key2]=arguments[_key2];}_this.log('HISTORY_REPLACE_STATE',{args,timestamp:Date.now()});try{return originalReplaceState.apply(window.history,args);}catch(error){console.error('Navigation replaceState error:',error);throw error;}};}restoreNavigationCalls(){// Restore original methods if they were intercepted\nif(this.originalNavigate){window.history.pushState=this.originalNavigate.pushState;window.history.replaceState=this.originalNavigate.replaceState;this.originalNavigate=null;}}monitorHistoryChanges(){window.addEventListener('popstate',e=>{this.log('POPSTATE_EVENT',{state:e.state,timestamp:Date.now(),currentPath:window.location.pathname});});}monitorBlockedEvents(){// Monitor for events that might be preventing navigation\nconst events=['beforeunload','unload','pagehide'];events.forEach(eventType=>{window.addEventListener(eventType,e=>{this.log('POTENTIALLY_BLOCKING_EVENT',{type:eventType,timestamp:Date.now(),defaultPrevented:e.defaultPrevented});});});}monitorMemoryLeaks(){// Check for potential memory leaks every 30 seconds (reduced frequency)\n// Only in development mode to avoid production overhead\nif(process.env.NODE_ENV==='development'){setInterval(()=>{if(this.isMonitoring){this.log('MEMORY_CHECK',{timestamp:Date.now(),diagnosticsCount:this.diagnostics.length,// @ts-ignore - performance.memory is not in all browsers\nmemoryUsage:performance.memory?{// @ts-ignore\nusedJSHeapSize:performance.memory.usedJSHeapSize,// @ts-ignore\ntotalJSHeapSize:performance.memory.totalJSHeapSize}:'not available'});}},30000);// Reduced from 5 seconds to 30 seconds\n}}log(event,details){const diagnostic={timestamp:Date.now(),event,details};this.diagnostics.push(diagnostic);// Keep only last 100 diagnostics\nif(this.diagnostics.length>100){this.diagnostics=this.diagnostics.slice(-100);}console.log(`🔍 NavigationDiagnostics [${event}]:`,details);}getDiagnostics(){return[...this.diagnostics];}getRecentDiagnostics(){let seconds=arguments.length>0&&arguments[0]!==undefined?arguments[0]:30;const cutoff=Date.now()-seconds*1000;return this.diagnostics.filter(d=>d.timestamp>cutoff);}clearDiagnostics(){this.diagnostics=[];}// Method to check if navigation is currently blocked\nisNavigationBlocked(){const recent=this.getRecentDiagnostics(5);const clicks=recent.filter(d=>d.event==='NAVIGATION_CLICK');const navigations=recent.filter(d=>d.event==='HISTORY_PUSH_STATE'||d.event==='HISTORY_REPLACE_STATE');// If we have clicks but no corresponding navigation events, something is blocked\nreturn clicks.length>0&&navigations.length===0;}// Method to force navigation (emergency bypass)\nforceNavigation(path){this.log('FORCE_NAVIGATION_ATTEMPT',{path,timestamp:Date.now()});try{// Try multiple methods\nwindow.history.pushState({},'',path);window.dispatchEvent(new PopStateEvent('popstate',{state:{}}));// Also try direct location change as last resort\nsetTimeout(()=>{if(window.location.pathname!==path){window.location.href=path;}},100);}catch(error){const errorMessage=error instanceof Error?error.message:String(error);this.log('FORCE_NAVIGATION_ERROR',{path,error:errorMessage,timestamp:Date.now()});}}}_NavigationDiagnostics=NavigationDiagnostics;NavigationDiagnostics.instance=void 0;export const navigationDiagnostics=NavigationDiagnostics.getInstance();export default navigationDiagnostics;", "map": {"version": 3, "names": ["NavigationDiagnostics", "constructor", "diagnostics", "isMonitoring", "originalNavigate", "handleClick", "e", "target", "isNavElement", "closest", "_target$textContent", "log", "textContent", "trim", "path", "getAttribute", "timestamp", "Date", "now", "eventPhase", "bubbles", "cancelable", "defaultPrevented", "isTrusted", "getInstance", "instance", "startMonitoring", "document", "addEventListener", "interceptNavigationCalls", "monitorHistoryChanges", "monitorBlockedEvents", "monitorMemoryLeaks", "stopMonitoring", "removeEventListener", "restoreNavigationCalls", "_this", "process", "env", "NODE_ENV", "pushState", "window", "history", "replaceState", "originalPushState", "originalReplaceState", "_len", "arguments", "length", "args", "Array", "_key", "apply", "error", "console", "_len2", "_key2", "state", "currentPath", "location", "pathname", "events", "for<PERSON>ach", "eventType", "type", "setInterval", "diagnosticsCount", "memoryUsage", "performance", "memory", "usedJSHeapSize", "totalJSHeapSize", "event", "details", "diagnostic", "push", "slice", "getDiagnostics", "getRecentDiagnostics", "seconds", "undefined", "cutoff", "filter", "d", "clearDiagnostics", "isNavigationBlocked", "recent", "clicks", "navigations", "forceNavigation", "dispatchEvent", "PopStateEvent", "setTimeout", "href", "errorMessage", "Error", "message", "String", "_NavigationDiagnostics", "navigationDiagnostics"], "sources": ["C:/laragon/www/frontend/src/utils/navigationDiagnostics.ts"], "sourcesContent": ["/**\n * Comprehensive navigation diagnostics to identify why navigation becomes unresponsive\n */\n\ninterface NavigationDiagnostic {\n  timestamp: number;\n  event: string;\n  details: any;\n}\n\nclass NavigationDiagnostics {\n  private static instance: NavigationDiagnostics;\n  private diagnostics: NavigationDiagnostic[] = [];\n  private isMonitoring = false;\n  private originalNavigate: { pushState: any; replaceState: any } | null = null;\n\n  static getInstance(): NavigationDiagnostics {\n    if (!NavigationDiagnostics.instance) {\n      NavigationDiagnostics.instance = new NavigationDiagnostics();\n    }\n    return NavigationDiagnostics.instance;\n  }\n\n  startMonitoring() {\n    if (this.isMonitoring) return;\n    this.isMonitoring = true;\n\n    this.log('MONITORING_STARTED', { timestamp: Date.now() });\n\n    // Monitor all click events\n    document.addEventListener('click', this.handleClick, true);\n    \n    // Monitor all navigation attempts\n    this.interceptNavigationCalls();\n    \n    // Monitor React Router history changes\n    this.monitorHistoryChanges();\n    \n    // Monitor for blocked events\n    this.monitorBlockedEvents();\n    \n    // Monitor for memory leaks\n    this.monitorMemoryLeaks();\n  }\n\n  stopMonitoring() {\n    if (!this.isMonitoring) return;\n    this.isMonitoring = false;\n\n    document.removeEventListener('click', this.handleClick, true);\n    this.restoreNavigationCalls();\n    this.log('MONITORING_STOPPED', { timestamp: Date.now() });\n  }\n\n  private handleClick = (e: MouseEvent) => {\n    const target = e.target as HTMLElement;\n    const isNavElement = target.closest('[data-testid=\"sidebar-nav\"]') || \n                         target.closest('.nav-link') || \n                         target.closest('a[href^=\"/dashboard\"]');\n    \n    if (isNavElement) {\n      this.log('NAVIGATION_CLICK', {\n        target: target.textContent?.trim(),\n        path: target.getAttribute('data-path'),\n        timestamp: Date.now(),\n        eventPhase: e.eventPhase,\n        bubbles: e.bubbles,\n        cancelable: e.cancelable,\n        defaultPrevented: e.defaultPrevented,\n        isTrusted: e.isTrusted\n      });\n    }\n  };\n\n  private interceptNavigationCalls() {\n    // Only intercept in development mode to avoid production interference\n    if (process.env.NODE_ENV !== 'development') {\n      return;\n    }\n\n    // Store original functions for restoration\n    if (!this.originalNavigate) {\n      this.originalNavigate = {\n        pushState: window.history.pushState,\n        replaceState: window.history.replaceState\n      };\n    }\n\n    // This is a bit hacky but necessary for debugging\n    const originalPushState = this.originalNavigate.pushState;\n    const originalReplaceState = this.originalNavigate.replaceState;\n\n    window.history.pushState = (...args) => {\n      this.log('HISTORY_PUSH_STATE', { args, timestamp: Date.now() });\n      try {\n        return originalPushState.apply(window.history, args);\n      } catch (error) {\n        console.error('Navigation pushState error:', error);\n        throw error;\n      }\n    };\n\n    window.history.replaceState = (...args) => {\n      this.log('HISTORY_REPLACE_STATE', { args, timestamp: Date.now() });\n      try {\n        return originalReplaceState.apply(window.history, args);\n      } catch (error) {\n        console.error('Navigation replaceState error:', error);\n        throw error;\n      }\n    };\n  }\n\n  private restoreNavigationCalls() {\n    // Restore original methods if they were intercepted\n    if (this.originalNavigate) {\n      window.history.pushState = this.originalNavigate.pushState;\n      window.history.replaceState = this.originalNavigate.replaceState;\n      this.originalNavigate = null;\n    }\n  }\n\n  private monitorHistoryChanges() {\n    window.addEventListener('popstate', (e) => {\n      this.log('POPSTATE_EVENT', {\n        state: e.state,\n        timestamp: Date.now(),\n        currentPath: window.location.pathname\n      });\n    });\n  }\n\n  private monitorBlockedEvents() {\n    // Monitor for events that might be preventing navigation\n    const events = ['beforeunload', 'unload', 'pagehide'];\n    events.forEach(eventType => {\n      window.addEventListener(eventType, (e) => {\n        this.log('POTENTIALLY_BLOCKING_EVENT', {\n          type: eventType,\n          timestamp: Date.now(),\n          defaultPrevented: e.defaultPrevented\n        });\n      });\n    });\n  }\n\n  private monitorMemoryLeaks() {\n    // Check for potential memory leaks every 30 seconds (reduced frequency)\n    // Only in development mode to avoid production overhead\n    if (process.env.NODE_ENV === 'development') {\n      setInterval(() => {\n        if (this.isMonitoring) {\n          this.log('MEMORY_CHECK', {\n            timestamp: Date.now(),\n            diagnosticsCount: this.diagnostics.length,\n            // @ts-ignore - performance.memory is not in all browsers\n            memoryUsage: (performance as any).memory ? {\n              // @ts-ignore\n              usedJSHeapSize: (performance as any).memory.usedJSHeapSize,\n              // @ts-ignore\n              totalJSHeapSize: (performance as any).memory.totalJSHeapSize\n            } : 'not available'\n          });\n        }\n      }, 30000); // Reduced from 5 seconds to 30 seconds\n    }\n  }\n\n  private log(event: string, details: any) {\n    const diagnostic: NavigationDiagnostic = {\n      timestamp: Date.now(),\n      event,\n      details\n    };\n    \n    this.diagnostics.push(diagnostic);\n    \n    // Keep only last 100 diagnostics\n    if (this.diagnostics.length > 100) {\n      this.diagnostics = this.diagnostics.slice(-100);\n    }\n\n    console.log(`🔍 NavigationDiagnostics [${event}]:`, details);\n  }\n\n  getDiagnostics(): NavigationDiagnostic[] {\n    return [...this.diagnostics];\n  }\n\n  getRecentDiagnostics(seconds: number = 30): NavigationDiagnostic[] {\n    const cutoff = Date.now() - (seconds * 1000);\n    return this.diagnostics.filter(d => d.timestamp > cutoff);\n  }\n\n  clearDiagnostics() {\n    this.diagnostics = [];\n  }\n\n  // Method to check if navigation is currently blocked\n  isNavigationBlocked(): boolean {\n    const recent = this.getRecentDiagnostics(5);\n    const clicks = recent.filter(d => d.event === 'NAVIGATION_CLICK');\n    const navigations = recent.filter(d => d.event === 'HISTORY_PUSH_STATE' || d.event === 'HISTORY_REPLACE_STATE');\n    \n    // If we have clicks but no corresponding navigation events, something is blocked\n    return clicks.length > 0 && navigations.length === 0;\n  }\n\n  // Method to force navigation (emergency bypass)\n  forceNavigation(path: string) {\n    this.log('FORCE_NAVIGATION_ATTEMPT', { path, timestamp: Date.now() });\n    \n    try {\n      // Try multiple methods\n      window.history.pushState({}, '', path);\n      window.dispatchEvent(new PopStateEvent('popstate', { state: {} }));\n      \n      // Also try direct location change as last resort\n      setTimeout(() => {\n        if (window.location.pathname !== path) {\n          window.location.href = path;\n        }\n      }, 100);\n      \n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      this.log('FORCE_NAVIGATION_ERROR', { path, error: errorMessage, timestamp: Date.now() });\n    }\n  }\n}\n\nexport const navigationDiagnostics = NavigationDiagnostics.getInstance();\nexport default navigationDiagnostics;\n"], "mappings": "2BAAA;AACA;AACA,GAQA,KAAM,CAAAA,qBAAsB,CAAAC,YAAA,OAElBC,WAAW,CAA2B,EAAE,MACxCC,YAAY,CAAG,KAAK,MACpBC,gBAAgB,CAAiD,IAAI,MAwCrEC,WAAW,CAAIC,CAAa,EAAK,CACvC,KAAM,CAAAC,MAAM,CAAGD,CAAC,CAACC,MAAqB,CACtC,KAAM,CAAAC,YAAY,CAAGD,MAAM,CAACE,OAAO,CAAC,6BAA6B,CAAC,EAC7CF,MAAM,CAACE,OAAO,CAAC,WAAW,CAAC,EAC3BF,MAAM,CAACE,OAAO,CAAC,uBAAuB,CAAC,CAE5D,GAAID,YAAY,CAAE,KAAAE,mBAAA,CAChB,IAAI,CAACC,GAAG,CAAC,kBAAkB,CAAE,CAC3BJ,MAAM,EAAAG,mBAAA,CAAEH,MAAM,CAACK,WAAW,UAAAF,mBAAA,iBAAlBA,mBAAA,CAAoBG,IAAI,CAAC,CAAC,CAClCC,IAAI,CAAEP,MAAM,CAACQ,YAAY,CAAC,WAAW,CAAC,CACtCC,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACrBC,UAAU,CAAEb,CAAC,CAACa,UAAU,CACxBC,OAAO,CAAEd,CAAC,CAACc,OAAO,CAClBC,UAAU,CAAEf,CAAC,CAACe,UAAU,CACxBC,gBAAgB,CAAEhB,CAAC,CAACgB,gBAAgB,CACpCC,SAAS,CAAEjB,CAAC,CAACiB,SACf,CAAC,CAAC,CACJ,CACF,CAAC,EAxDD,MAAO,CAAAC,WAAWA,CAAA,CAA0B,CAC1C,GAAI,CAACxB,qBAAqB,CAACyB,QAAQ,CAAE,CACnCzB,qBAAqB,CAACyB,QAAQ,CAAG,GAAI,CAAAzB,qBAAqB,CAAC,CAAC,CAC9D,CACA,MAAO,CAAAA,qBAAqB,CAACyB,QAAQ,CACvC,CAEAC,eAAeA,CAAA,CAAG,CAChB,GAAI,IAAI,CAACvB,YAAY,CAAE,OACvB,IAAI,CAACA,YAAY,CAAG,IAAI,CAExB,IAAI,CAACQ,GAAG,CAAC,oBAAoB,CAAE,CAAEK,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAE,CAAC,CAAC,CAEzD;AACAS,QAAQ,CAACC,gBAAgB,CAAC,OAAO,CAAE,IAAI,CAACvB,WAAW,CAAE,IAAI,CAAC,CAE1D;AACA,IAAI,CAACwB,wBAAwB,CAAC,CAAC,CAE/B;AACA,IAAI,CAACC,qBAAqB,CAAC,CAAC,CAE5B;AACA,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAE3B;AACA,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAC3B,CAEAC,cAAcA,CAAA,CAAG,CACf,GAAI,CAAC,IAAI,CAAC9B,YAAY,CAAE,OACxB,IAAI,CAACA,YAAY,CAAG,KAAK,CAEzBwB,QAAQ,CAACO,mBAAmB,CAAC,OAAO,CAAE,IAAI,CAAC7B,WAAW,CAAE,IAAI,CAAC,CAC7D,IAAI,CAAC8B,sBAAsB,CAAC,CAAC,CAC7B,IAAI,CAACxB,GAAG,CAAC,oBAAoB,CAAE,CAAEK,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAE,CAAC,CAAC,CAC3D,CAsBQW,wBAAwBA,CAAA,CAAG,KAAAO,KAAA,MACjC;AACA,GAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1C,OACF,CAEA;AACA,GAAI,CAAC,IAAI,CAACnC,gBAAgB,CAAE,CAC1B,IAAI,CAACA,gBAAgB,CAAG,CACtBoC,SAAS,CAAEC,MAAM,CAACC,OAAO,CAACF,SAAS,CACnCG,YAAY,CAAEF,MAAM,CAACC,OAAO,CAACC,YAC/B,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,iBAAiB,CAAG,IAAI,CAACxC,gBAAgB,CAACoC,SAAS,CACzD,KAAM,CAAAK,oBAAoB,CAAG,IAAI,CAACzC,gBAAgB,CAACuC,YAAY,CAE/DF,MAAM,CAACC,OAAO,CAACF,SAAS,CAAG,UAAa,SAAAM,IAAA,CAAAC,SAAA,CAAAC,MAAA,CAATC,IAAI,KAAAC,KAAA,CAAAJ,IAAA,EAAAK,IAAA,GAAAA,IAAA,CAAAL,IAAA,CAAAK,IAAA,IAAJF,IAAI,CAAAE,IAAA,EAAAJ,SAAA,CAAAI,IAAA,GACjCf,KAAI,CAACzB,GAAG,CAAC,oBAAoB,CAAE,CAAEsC,IAAI,CAAEjC,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAE,CAAC,CAAC,CAC/D,GAAI,CACF,MAAO,CAAA0B,iBAAiB,CAACQ,KAAK,CAACX,MAAM,CAACC,OAAO,CAAEO,IAAI,CAAC,CACtD,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAEDZ,MAAM,CAACC,OAAO,CAACC,YAAY,CAAG,UAAa,SAAAY,KAAA,CAAAR,SAAA,CAAAC,MAAA,CAATC,IAAI,KAAAC,KAAA,CAAAK,KAAA,EAAAC,KAAA,GAAAA,KAAA,CAAAD,KAAA,CAAAC,KAAA,IAAJP,IAAI,CAAAO,KAAA,EAAAT,SAAA,CAAAS,KAAA,GACpCpB,KAAI,CAACzB,GAAG,CAAC,uBAAuB,CAAE,CAAEsC,IAAI,CAAEjC,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAE,CAAC,CAAC,CAClE,GAAI,CACF,MAAO,CAAA2B,oBAAoB,CAACO,KAAK,CAACX,MAAM,CAACC,OAAO,CAAEO,IAAI,CAAC,CACzD,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CACH,CAEQlB,sBAAsBA,CAAA,CAAG,CAC/B;AACA,GAAI,IAAI,CAAC/B,gBAAgB,CAAE,CACzBqC,MAAM,CAACC,OAAO,CAACF,SAAS,CAAG,IAAI,CAACpC,gBAAgB,CAACoC,SAAS,CAC1DC,MAAM,CAACC,OAAO,CAACC,YAAY,CAAG,IAAI,CAACvC,gBAAgB,CAACuC,YAAY,CAChE,IAAI,CAACvC,gBAAgB,CAAG,IAAI,CAC9B,CACF,CAEQ0B,qBAAqBA,CAAA,CAAG,CAC9BW,MAAM,CAACb,gBAAgB,CAAC,UAAU,CAAGtB,CAAC,EAAK,CACzC,IAAI,CAACK,GAAG,CAAC,gBAAgB,CAAE,CACzB8C,KAAK,CAAEnD,CAAC,CAACmD,KAAK,CACdzC,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACrBwC,WAAW,CAAEjB,MAAM,CAACkB,QAAQ,CAACC,QAC/B,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEQ7B,oBAAoBA,CAAA,CAAG,CAC7B;AACA,KAAM,CAAA8B,MAAM,CAAG,CAAC,cAAc,CAAE,QAAQ,CAAE,UAAU,CAAC,CACrDA,MAAM,CAACC,OAAO,CAACC,SAAS,EAAI,CAC1BtB,MAAM,CAACb,gBAAgB,CAACmC,SAAS,CAAGzD,CAAC,EAAK,CACxC,IAAI,CAACK,GAAG,CAAC,4BAA4B,CAAE,CACrCqD,IAAI,CAAED,SAAS,CACf/C,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACrBI,gBAAgB,CAAEhB,CAAC,CAACgB,gBACtB,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEQU,kBAAkBA,CAAA,CAAG,CAC3B;AACA;AACA,GAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1C0B,WAAW,CAAC,IAAM,CAChB,GAAI,IAAI,CAAC9D,YAAY,CAAE,CACrB,IAAI,CAACQ,GAAG,CAAC,cAAc,CAAE,CACvBK,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACrBgD,gBAAgB,CAAE,IAAI,CAAChE,WAAW,CAAC8C,MAAM,CACzC;AACAmB,WAAW,CAAGC,WAAW,CAASC,MAAM,CAAG,CACzC;AACAC,cAAc,CAAGF,WAAW,CAASC,MAAM,CAACC,cAAc,CAC1D;AACAC,eAAe,CAAGH,WAAW,CAASC,MAAM,CAACE,eAC/C,CAAC,CAAG,eACN,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,KAAK,CAAC,CAAE;AACb,CACF,CAEQ5D,GAAGA,CAAC6D,KAAa,CAAEC,OAAY,CAAE,CACvC,KAAM,CAAAC,UAAgC,CAAG,CACvC1D,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACrBsD,KAAK,CACLC,OACF,CAAC,CAED,IAAI,CAACvE,WAAW,CAACyE,IAAI,CAACD,UAAU,CAAC,CAEjC;AACA,GAAI,IAAI,CAACxE,WAAW,CAAC8C,MAAM,CAAG,GAAG,CAAE,CACjC,IAAI,CAAC9C,WAAW,CAAG,IAAI,CAACA,WAAW,CAAC0E,KAAK,CAAC,CAAC,GAAG,CAAC,CACjD,CAEAtB,OAAO,CAAC3C,GAAG,CAAC,6BAA6B6D,KAAK,IAAI,CAAEC,OAAO,CAAC,CAC9D,CAEAI,cAAcA,CAAA,CAA2B,CACvC,MAAO,CAAC,GAAG,IAAI,CAAC3E,WAAW,CAAC,CAC9B,CAEA4E,oBAAoBA,CAAA,CAA+C,IAA9C,CAAAC,OAAe,CAAAhC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAiC,SAAA,CAAAjC,SAAA,IAAG,EAAE,CACvC,KAAM,CAAAkC,MAAM,CAAGhE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAI6D,OAAO,CAAG,IAAK,CAC5C,MAAO,KAAI,CAAC7E,WAAW,CAACgF,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACnE,SAAS,CAAGiE,MAAM,CAAC,CAC3D,CAEAG,gBAAgBA,CAAA,CAAG,CACjB,IAAI,CAAClF,WAAW,CAAG,EAAE,CACvB,CAEA;AACAmF,mBAAmBA,CAAA,CAAY,CAC7B,KAAM,CAAAC,MAAM,CAAG,IAAI,CAACR,oBAAoB,CAAC,CAAC,CAAC,CAC3C,KAAM,CAAAS,MAAM,CAAGD,MAAM,CAACJ,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACX,KAAK,GAAK,kBAAkB,CAAC,CACjE,KAAM,CAAAgB,WAAW,CAAGF,MAAM,CAACJ,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACX,KAAK,GAAK,oBAAoB,EAAIW,CAAC,CAACX,KAAK,GAAK,uBAAuB,CAAC,CAE/G;AACA,MAAO,CAAAe,MAAM,CAACvC,MAAM,CAAG,CAAC,EAAIwC,WAAW,CAACxC,MAAM,GAAK,CAAC,CACtD,CAEA;AACAyC,eAAeA,CAAC3E,IAAY,CAAE,CAC5B,IAAI,CAACH,GAAG,CAAC,0BAA0B,CAAE,CAAEG,IAAI,CAAEE,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAE,CAAC,CAAC,CAErE,GAAI,CACF;AACAuB,MAAM,CAACC,OAAO,CAACF,SAAS,CAAC,CAAC,CAAC,CAAE,EAAE,CAAE1B,IAAI,CAAC,CACtC2B,MAAM,CAACiD,aAAa,CAAC,GAAI,CAAAC,aAAa,CAAC,UAAU,CAAE,CAAElC,KAAK,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC,CAElE;AACAmC,UAAU,CAAC,IAAM,CACf,GAAInD,MAAM,CAACkB,QAAQ,CAACC,QAAQ,GAAK9C,IAAI,CAAE,CACrC2B,MAAM,CAACkB,QAAQ,CAACkC,IAAI,CAAG/E,IAAI,CAC7B,CACF,CAAC,CAAE,GAAG,CAAC,CAET,CAAE,MAAOuC,KAAK,CAAE,CACd,KAAM,CAAAyC,YAAY,CAAGzC,KAAK,WAAY,CAAA0C,KAAK,CAAG1C,KAAK,CAAC2C,OAAO,CAAGC,MAAM,CAAC5C,KAAK,CAAC,CAC3E,IAAI,CAAC1C,GAAG,CAAC,wBAAwB,CAAE,CAAEG,IAAI,CAAEuC,KAAK,CAAEyC,YAAY,CAAE9E,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAE,CAAC,CAAC,CAC1F,CACF,CACF,CAACgF,sBAAA,CA3NKlG,qBAAqB,CAArBA,qBAAqB,CACVyB,QAAQ,QA4NzB,MAAO,MAAM,CAAA0E,qBAAqB,CAAGnG,qBAAqB,CAACwB,WAAW,CAAC,CAAC,CACxE,cAAe,CAAA2E,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}