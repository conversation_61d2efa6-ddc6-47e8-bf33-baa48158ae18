{"ast": null, "code": "import * as React from 'react';\nimport React__default from 'react';\nvar isCheckBoxInput = element => element.type === 'checkbox';\nvar isDateObject = value => value instanceof Date;\nvar isNullOrUndefined = value => value == null;\nconst isObjectType = value => typeof value === 'object';\nvar isObject = value => !isNullOrUndefined(value) && !Array.isArray(value) && isObjectType(value) && !isDateObject(value);\nvar getEventValue = event => isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = name => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\nvar isPlainObject = tempObject => {\n  const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n  return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf');\n};\nvar isWeb = typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined' && typeof document !== 'undefined';\nfunction cloneObject(data) {\n  let copy;\n  const isArray = Array.isArray(data);\n  const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n    copy = isArray ? [] : {};\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n  return copy;\n}\nvar isKey = value => /^\\w*$/.test(value);\nvar isUndefined = val => val === undefined;\nvar compact = value => Array.isArray(value) ? value.filter(Boolean) : [];\nvar stringToPath = input => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\nvar get = (object, path, defaultValue) => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n  const result = (isKey(path) ? [path] : stringToPath(path)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n  return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = value => typeof value === 'boolean';\nvar set = (object, path, value) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n    }\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n};\nconst EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change'\n};\nconst VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all'\n};\nconst INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate'\n};\nconst HookFormContext = React__default.createContext(null);\nHookFormContext.displayName = 'HookFormContext';\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => React__default.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = props => {\n  const {\n    children,\n    ...data\n  } = props;\n  return React__default.createElement(HookFormContext.Provider, {\n    value: data\n  }, children);\n};\nvar getProxyFormState = function (formState, control, localProxyFormState) {\n  let isRoot = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  const result = {\n    defaultValues: control._defaultValues\n  };\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key;\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      }\n    });\n  }\n  return result;\n};\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    disabled,\n    name,\n    exact\n  } = props || {};\n  const [formState, updateFormState] = React__default.useState(control._formState);\n  const _localProxyFormState = React__default.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  });\n  useIsomorphicLayoutEffect(() => control._subscribe({\n    name,\n    formState: _localProxyFormState.current,\n    exact,\n    callback: formState => {\n      !disabled && updateFormState({\n        ...control._formState,\n        ...formState\n      });\n    }\n  }), [name, disabled, exact]);\n  React__default.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n  return React__default.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\nvar isString = value => typeof value === 'string';\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n  if (Array.isArray(names)) {\n    return names.map(fieldName => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n  }\n  isGlobal && (_names.watchAll = true);\n  return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact\n  } = props || {};\n  const _defaultValue = React__default.useRef(defaultValue);\n  const [value, updateValue] = React__default.useState(control._getWatch(name, _defaultValue.current));\n  useIsomorphicLayoutEffect(() => control._subscribe({\n    name,\n    formState: {\n      values: true\n    },\n    exact,\n    callback: formState => !disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n  }), [name, control, disabled, exact]);\n  React__default.useEffect(() => control._removeUnmounted());\n  return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n  const methods = useFormContext();\n  const {\n    name,\n    disabled,\n    control = methods.control,\n    shouldUnregister\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n    exact: true\n  });\n  const formState = useFormState({\n    control,\n    name,\n    exact: true\n  });\n  const _props = React__default.useRef(props);\n  const _registerProps = React__default.useRef(control.register(name, {\n    ...props.rules,\n    value,\n    ...(isBoolean(props.disabled) ? {\n      disabled: props.disabled\n    } : {})\n  }));\n  const fieldState = React__default.useMemo(() => Object.defineProperties({}, {\n    invalid: {\n      enumerable: true,\n      get: () => !!get(formState.errors, name)\n    },\n    isDirty: {\n      enumerable: true,\n      get: () => !!get(formState.dirtyFields, name)\n    },\n    isTouched: {\n      enumerable: true,\n      get: () => !!get(formState.touchedFields, name)\n    },\n    isValidating: {\n      enumerable: true,\n      get: () => !!get(formState.validatingFields, name)\n    },\n    error: {\n      enumerable: true,\n      get: () => get(formState.errors, name)\n    }\n  }), [formState, name]);\n  const onChange = React__default.useCallback(event => _registerProps.current.onChange({\n    target: {\n      value: getEventValue(event),\n      name: name\n    },\n    type: EVENTS.CHANGE\n  }), [name]);\n  const onBlur = React__default.useCallback(() => _registerProps.current.onBlur({\n    target: {\n      value: get(control._formValues, name),\n      name: name\n    },\n    type: EVENTS.BLUR\n  }), [name, control._formValues]);\n  const ref = React__default.useCallback(elm => {\n    const field = get(control._fields, name);\n    if (field && elm) {\n      field._f.ref = {\n        focus: () => elm.focus && elm.focus(),\n        select: () => elm.select && elm.select(),\n        setCustomValidity: message => elm.setCustomValidity(message),\n        reportValidity: () => elm.reportValidity()\n      };\n    }\n  }, [control._fields, name]);\n  const field = React__default.useMemo(() => ({\n    name,\n    value,\n    ...(isBoolean(disabled) || formState.disabled ? {\n      disabled: formState.disabled || disabled\n    } : {}),\n    onChange,\n    onBlur,\n    ref\n  }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n  React__default.useEffect(() => {\n    const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled) ? {\n        disabled: _props.current.disabled\n      } : {})\n    });\n    const updateMounted = (name, value) => {\n      const field = get(control._fields, name);\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n    updateMounted(name, true);\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n    !isArrayField && control.register(name);\n    return () => {\n      (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n  React__default.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name\n    });\n  }, [disabled, name, control]);\n  return React__default.useMemo(() => ({\n    field,\n    formState,\n    fieldState\n  }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = props => props.render(useController(props));\nconst flatten = obj => {\n  const output = {};\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n  return output;\n};\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n  const methods = useFormContext();\n  const [mounted, setMounted] = React__default.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n  const submit = async event => {\n    let hasError = false;\n    let type = '';\n    await control.handleSubmit(async data => {\n      const formData = new FormData();\n      let formDataJson = '';\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch (_a) {}\n      const flattenFormValues = flatten(control._formValues);\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson\n        });\n      }\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [headers && headers['Content-Type'], encType].some(value => value && value.includes('json'));\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? {\n                'Content-Type': encType\n              } : {})\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData\n          });\n          if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n            hasError = true;\n            onError && onError({\n              response\n            });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({\n              response\n            });\n          }\n        } catch (error) {\n          hasError = true;\n          onError && onError({\n            error\n          });\n        }\n      }\n    })(event);\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false\n      });\n      props.control.setError('root.server', {\n        type\n      });\n    }\n  };\n  React__default.useEffect(() => {\n    setMounted(true);\n  }, []);\n  return render ? React__default.createElement(React__default.Fragment, null, render({\n    submit\n  })) : React__default.createElement(\"form\", {\n    noValidate: mounted,\n    action: action,\n    method: method,\n    encType: encType,\n    onSubmit: submit,\n    ...rest\n  }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria ? {\n  ...errors[name],\n  types: {\n    ...(errors[name] && errors[name].types ? errors[name].types : {}),\n    [type]: message || true\n  }\n} : {};\nvar convertToArrayPayload = value => Array.isArray(value) ? value : [value];\nvar createSubject = () => {\n  let _observers = [];\n  const next = value => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n  const subscribe = observer => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter(o => o !== observer);\n      }\n    };\n  };\n  const unsubscribe = () => {\n    _observers = [];\n  };\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe\n  };\n};\nvar isPrimitive = value => isNullOrUndefined(value) || !isObjectType(value);\nfunction deepEqual(object1, object2) {\n  let _internal_visited = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : new WeakSet();\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n  if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n    return true;\n  }\n  _internal_visited.add(object1);\n  _internal_visited.add(object2);\n  for (const key of keys1) {\n    const val1 = object1[key];\n    if (!keys2.includes(key)) {\n      return false;\n    }\n    if (key !== 'ref') {\n      const val2 = object2[key];\n      if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2, _internal_visited) : val1 !== val2) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nvar isEmptyObject = value => isObject(value) && !Object.keys(value).length;\nvar isFileInput = element => element.type === 'file';\nvar isFunction = value => typeof value === 'function';\nvar isHTMLElement = value => {\n  if (!isWeb) {\n    return false;\n  }\n  const owner = value ? value.ownerDocument : 0;\n  return value instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = element => element.type === `select-multiple`;\nvar isRadioInput = element => element.type === 'radio';\nvar isRadioOrCheckbox = ref => isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = ref => isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n  return object;\n}\nfunction isEmptyArray(obj) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction unset(object, path) {\n  const paths = Array.isArray(path) ? path : isKey(path) ? [path] : stringToPath(path);\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n  const index = paths.length - 1;\n  const key = paths[index];\n  if (childObject) {\n    delete childObject[key];\n  }\n  if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n    unset(object, paths.slice(0, -1));\n  }\n  return object;\n}\nvar objectHasFunction = data => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\nfunction markFieldsDirty(data) {\n  let fields = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n  return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n            ...markFieldsDirty(data[key])\n          };\n        } else {\n          getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n  return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n  value: false,\n  isValid: false\n};\nconst validResult = {\n  value: true,\n  isValid: true\n};\nvar getCheckboxValue = options => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options.filter(option => option && option.checked && !option.disabled).map(option => option.value);\n      return {\n        value: values,\n        isValid: !!values.length\n      };\n    }\n    return options[0].checked && !options[0].disabled ?\n    // @ts-expect-error expected to work in the browser\n    options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === '' ? validResult : {\n      value: options[0].value,\n      isValid: true\n    } : validResult : defaultResult;\n  }\n  return defaultResult;\n};\nvar getFieldValueAs = (value, _ref) => {\n  let {\n    valueAsNumber,\n    valueAsDate,\n    setValueAs\n  } = _ref;\n  return isUndefined(value) ? value : valueAsNumber ? value === '' ? NaN : value ? +value : value : valueAsDate && isString(value) ? new Date(value) : setValueAs ? setValueAs(value) : value;\n};\nconst defaultReturn = {\n  isValid: false,\n  value: null\n};\nvar getRadioValue = options => Array.isArray(options) ? options.reduce((previous, option) => option && option.checked && !option.disabled ? {\n  isValid: true,\n  value: option.value\n} : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n  const ref = _f.ref;\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(_ref2 => {\n      let {\n        value\n      } = _ref2;\n      return value;\n    });\n  }\n  if (isCheckBoxInput(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n  const fields = {};\n  for (const name of fieldsNames) {\n    const field = get(_fields, name);\n    field && set(fields, name, field._f);\n  }\n  return {\n    criteriaMode,\n    names: [...fieldsNames],\n    fields,\n    shouldUseNativeValidation\n  };\n};\nvar isRegex = value => value instanceof RegExp;\nvar getRuleValue = rule => isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = mode => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched\n});\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = fieldReference => !!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find(validateFunction => validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = options => options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent && (_names.watchAll || _names.watch.has(name) || [..._names.watch].some(watchName => name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n    if (field) {\n      const {\n        _f,\n        ...currentField\n      } = field;\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n  const error = get(errors, name);\n  if (error || isKey(name)) {\n    return {\n      error,\n      name\n    };\n  }\n  const names = name.split('.');\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return {\n        name\n      };\n    }\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError\n      };\n    }\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root\n      };\n    }\n    names.pop();\n  }\n  return {\n    name\n  };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n  updateFormState(formStateData);\n  const {\n    name,\n    ...formState\n  } = formStateData;\n  return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find(key => _proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact) => !name || !signalName || name === signalName || convertToArrayPayload(name).some(currentName => currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name) => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\nvar isMessage = value => isString(value);\nfunction getValidateError(result, ref) {\n  let type = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'validate';\n  if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref\n    };\n  }\n}\nvar getValueAndMessage = validationData => isObject(validationData) && !isRegex(validationData) ? validationData : {\n  value: validationData,\n  message: ''\n};\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount\n  } = field._f;\n  const inputValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef = refs ? refs[0] : ref;\n  const setCustomValidity = message => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === '' || inputValue === '' || Array.isArray(inputValue) && !inputValue.length;\n  const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n  const getMinMaxMessage = function (exceedMax, maxLengthMessage, minLengthMessage) {\n    let maxType = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : INPUT_VALIDATION_RULES.maxLength;\n    let minType = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : INPUT_VALIDATION_RULES.minLength;\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n    };\n  };\n  if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n    const {\n      value,\n      message\n    } = isMessage(required) ? {\n      value: !!required,\n      message: required\n    } : getValueAndMessage(required);\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n      const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate = ref.valueAsDate || new Date(inputValue);\n      const convertTimeToDate = time => new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n      }\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n      }\n    }\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n    const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const {\n      value: patternValue,\n      message\n    } = getValueAndMessage(pattern);\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {};\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n        const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message)\n          };\n          setCustomValidity(validateError.message);\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n  setCustomValidity(true);\n  return error;\n};\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true\n};\nfunction createFormControl() {\n  let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let _options = {\n    ...defaultOptions,\n    ...props\n  };\n  let _formState = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false\n  };\n  let _fields = {};\n  let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n  let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false\n  };\n  let _names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set()\n  };\n  let delayErrorCallback;\n  let timer = 0;\n  const _proxyFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState\n  };\n  const _subjects = {\n    array: createSubject(),\n    state: createSubject()\n  };\n  const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n  const debounce = callback => wait => {\n    clearTimeout(timer);\n    timer = setTimeout(callback, wait);\n  };\n  const _setValid = async shouldUpdateValid => {\n    if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n      const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid\n        });\n      }\n    }\n  };\n  const _updateIsValidating = (names, isValidating) => {\n    if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n      (names || Array.from(_names.mount)).forEach(name => {\n        if (name) {\n          isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n        }\n      });\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields)\n      });\n    }\n  };\n  const _setFieldArray = function (name) {\n    let values = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    let method = arguments.length > 2 ? arguments[2] : undefined;\n    let args = arguments.length > 3 ? arguments[3] : undefined;\n    let shouldSetValues = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    let shouldUpdateFieldsAndState = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : true;\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n        const errors = method(get(_formState.errors, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n      if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n        const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n  const updateErrors = (name, error) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors\n    });\n  };\n  const _setErrors = errors => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false\n    });\n  };\n  const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n    const field = get(_fields, name);\n    if (field) {\n      const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n      isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n      _state.mount && _setValid();\n    }\n  };\n  const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output = {\n      name\n    };\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n        const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n      }\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n        }\n      }\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n    return shouldUpdateField ? output : {};\n  };\n  const shouldRenderByError = (name, isValid, error, fieldState) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n    }\n    if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? {\n          isValid\n        } : {}),\n        errors: _formState.errors,\n        name\n      };\n      _formState = {\n        ..._formState,\n        ...updatedFormState\n      };\n      _subjects.state.next(updatedFormState);\n    }\n  };\n  const _runSchema = async name => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n    _updateIsValidating(name);\n    return result;\n  };\n  const executeSchemaAndUpdateState = async names => {\n    const {\n      errors\n    } = await _runSchema(names);\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n    return errors;\n  };\n  const executeBuiltInValidation = async function (fields, shouldOnlyCheckValid) {\n    let context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n      valid: true\n    };\n    for (const name in fields) {\n      const field = fields[name];\n      if (field) {\n        const {\n          _f,\n          ...fieldValue\n        } = field;\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n          const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n          !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n        }\n        !isEmptyObject(fieldValue) && (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n      }\n    }\n    return context.valid;\n  };\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field = get(_fields, name);\n      field && (field._f.refs ? field._f.refs.every(ref => !live(ref)) : !live(field._f.ref)) && unregister(name);\n    }\n    _names.unMount = new Set();\n  };\n  const _getDirty = (name, data) => !_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n  const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n    ...(_state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n      [names]: defaultValue\n    } : defaultValue)\n  }, isGlobal, defaultValue);\n  const _getFieldArray = name => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n  const setFieldValue = function (name, value) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const field = get(_fields, name);\n    let fieldValue = value;\n    if (field) {\n      const fieldReference = field._f;\n      if (fieldReference) {\n        !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value, fieldReference));\n        fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value) ? '' : value;\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(optionRef => optionRef.selected = fieldValue.includes(optionRef.value));\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach(checkboxRef => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(data => data === checkboxRef.value);\n                } else {\n                  checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(radioRef => radioRef.checked = radioRef.value === fieldValue);\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues)\n            });\n          }\n        }\n      }\n    }\n    (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n    options.shouldValidate && trigger(name);\n  };\n  const setValues = (name, value, options) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n      (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n  const setValue = function (name, value) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n    set(_formValues, name, cloneValue);\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues)\n      });\n      if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue)\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n    }\n    isWatched(name, _names) && _subjects.state.next({\n      ..._formState\n    });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues)\n    });\n  };\n  const onChange = async event => {\n    _state.mount = true;\n    const target = event.target;\n    let name = target.name;\n    let isFieldValueUpdated = true;\n    const field = get(_fields, name);\n    const _updateIsFieldValueUpdated = fieldValue => {\n      isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n      const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n      const watched = isWatched(name, _names, isBlurEvent);\n      set(_formValues, name, fieldValue);\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n      !isBlurEvent && _subjects.state.next({\n        name,\n        type: event.type,\n        values: cloneObject(_formValues)\n      });\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n        return shouldRender && _subjects.state.next({\n          name,\n          ...(watched ? {} : fieldState)\n        });\n      }\n      !isBlurEvent && watched && _subjects.state.next({\n        ..._formState\n      });\n      if (_options.resolver) {\n        const {\n          errors\n        } = await _runSchema([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n          const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n        _updateIsValidating([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n      if (isFieldValueUpdated) {\n        field._f.deps && trigger(field._f.deps);\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n  const _focusInput = (ref, key) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n  const trigger = async function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name);\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n      isValid = isEmptyObject(errors);\n      validationResult = name ? !fieldNames.some(name => get(errors, name)) : isValid;\n    } else if (name) {\n      validationResult = (await Promise.all(fieldNames.map(async fieldName => {\n        const field = get(_fields, fieldName);\n        return await executeBuiltInValidation(field && field._f ? {\n          [fieldName]: field\n        } : field);\n      }))).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n    _subjects.state.next({\n      ...(!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n        name\n      }),\n      ...(_options.resolver || !name ? {\n        isValid\n      } : {}),\n      errors: _formState.errors\n    });\n    options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n    return validationResult;\n  };\n  const getValues = fieldNames => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues)\n    };\n    return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map(name => get(values, name));\n  };\n  const getFieldState = (name, formState) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name)\n  });\n  const clearErrors = name => {\n    name && convertToArrayPayload(name).forEach(inputName => unset(_formState.errors, inputName));\n    _subjects.state.next({\n      errors: name ? _formState.errors : {}\n    });\n  };\n  const setError = (name, error, options) => {\n    const ref = (get(_fields, name, {\n      _f: {}\n    })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n    // Don't override existing error messages elsewhere in the object tree.\n    const {\n      ref: currentRef,\n      message,\n      type,\n      ...restOfErrorTree\n    } = currentError;\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref\n    });\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false\n    });\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n  const watch = (name, defaultValue) => isFunction(name) ? _subjects.state.subscribe({\n    next: payload => name(_getWatch(undefined, defaultValue), payload)\n  }) : _getWatch(name, defaultValue, true);\n  const _subscribe = props => _subjects.state.subscribe({\n    next: formState => {\n      if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n        props.callback({\n          values: {\n            ..._formValues\n          },\n          ..._formState,\n          ...formState\n        });\n      }\n    }\n  }).unsubscribe;\n  const subscribe = props => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState\n    });\n  };\n  const unregister = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n    }\n    _subjects.state.next({\n      values: cloneObject(_formValues)\n    });\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : {\n        isDirty: _getDirty()\n      })\n    });\n    !options.keepIsValid && _setValid();\n  };\n  const _setDisabledField = _ref3 => {\n    let {\n      disabled,\n      name\n    } = _ref3;\n    if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n  const register = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let field = get(_fields, name);\n    const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : {\n          ref: {\n            name\n          }\n        }),\n        name,\n        mount: true,\n        ...options\n      }\n    });\n    _names.mount.add(name);\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n        name\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n    return {\n      ...(disabledIsDefined ? {\n        disabled: options.disabled || _options.disabled\n      } : {}),\n      ...(_options.progressive ? {\n        required: !!options.required,\n        min: getRuleValue(options.min),\n        max: getRuleValue(options.max),\n        minLength: getRuleValue(options.minLength),\n        maxLength: getRuleValue(options.maxLength),\n        pattern: getRuleValue(options.pattern)\n      } : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: ref => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n          const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll('input,select,textarea')[0] || ref : ref : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n          if (radioOrCheckbox ? refs.find(option => option === fieldRef) : fieldRef === field._f.ref) {\n            return;\n          }\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox ? {\n                refs: [...refs.filter(live), fieldRef, ...(Array.isArray(get(_defaultValues, name)) ? [{}] : [])],\n                ref: {\n                  type: fieldRef.type,\n                  name\n                }\n              } : {\n                ref: fieldRef\n              })\n            }\n          });\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n          if (field._f) {\n            field._f.mount = false;\n          }\n          (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n        }\n      }\n    };\n  };\n  const _focusError = () => _options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n  const _disableForm = disabled => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({\n        disabled\n      });\n      iterateFieldsByAction(_fields, (ref, name) => {\n        const currentField = get(_fields, name);\n        if (currentField) {\n          ref.disabled = currentField._f.disabled || disabled;\n          if (Array.isArray(currentField._f.refs)) {\n            currentField._f.refs.forEach(inputRef => {\n              inputRef.disabled = currentField._f.disabled || disabled;\n            });\n          }\n        }\n      }, 0, false);\n    }\n  };\n  const handleSubmit = (onValid, onInvalid) => async e => {\n    let onValidError = undefined;\n    if (e) {\n      e.preventDefault && e.preventDefault();\n      e.persist && e.persist();\n    }\n    let fieldValues = cloneObject(_formValues);\n    _subjects.state.next({\n      isSubmitting: true\n    });\n    if (_options.resolver) {\n      const {\n        errors,\n        values\n      } = await _runSchema();\n      _formState.errors = errors;\n      fieldValues = cloneObject(values);\n    } else {\n      await executeBuiltInValidation(_fields);\n    }\n    if (_names.disabled.size) {\n      for (const name of _names.disabled) {\n        unset(fieldValues, name);\n      }\n    }\n    unset(_formState.errors, 'root');\n    if (isEmptyObject(_formState.errors)) {\n      _subjects.state.next({\n        errors: {}\n      });\n      try {\n        await onValid(fieldValues, e);\n      } catch (error) {\n        onValidError = error;\n      }\n    } else {\n      if (onInvalid) {\n        await onInvalid({\n          ..._formState.errors\n        }, e);\n      }\n      _focusError();\n      setTimeout(_focusError);\n    }\n    _subjects.state.next({\n      isSubmitted: true,\n      isSubmitting: false,\n      isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n      submitCount: _formState.submitCount + 1,\n      errors: _formState.errors\n    });\n    if (onValidError) {\n      throw onValidError;\n    }\n  };\n  const resetField = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(name, options.defaultValue);\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n      }\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n      _subjects.state.next({\n        ..._formState\n      });\n    }\n  };\n  const _reset = function (formValues) {\n    let keepStateOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([..._names.mount, ...Object.keys(getDirtyFields(_defaultValues, _formValues))]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n        if (keepStateOptions.keepFieldsRef) {\n          for (const fieldName of _names.mount) {\n            setValue(fieldName, get(values, fieldName));\n          }\n        } else {\n          _fields = {};\n        }\n      }\n      _formValues = _options.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n      _subjects.array.next({\n        values: {\n          ...values\n        }\n      });\n      _subjects.state.next({\n        values: {\n          ...values\n        }\n      });\n    }\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: ''\n    };\n    _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n    _state.watch = !!_options.shouldUnregister;\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n      isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n      isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n      dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n      touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n      isSubmitting: false\n    });\n  };\n  const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n  const setFocus = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n      }\n    }\n  };\n  const _setFormState = updatedFormState => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState\n    };\n  };\n  const _resetDefaultValues = () => isFunction(_options.defaultValues) && _options.defaultValues().then(values => {\n    reset(values, _options.resetOptions);\n    _subjects.state.next({\n      isLoading: false\n    });\n  });\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value\n        };\n      }\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState\n  };\n  return {\n    ...methods,\n    formControl: methods\n  };\n}\nvar generateId = () => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n  const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n};\nvar getFocusFieldName = function (name, index) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  return options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : '';\n};\nvar appendAt = (data, value) => [...data, ...convertToArrayPayload(value)];\nvar fillEmptyArray = value => Array.isArray(value) ? value.map(() => undefined) : undefined;\nfunction insert(data, index, value) {\n  return [...data.slice(0, index), ...convertToArrayPayload(value), ...data.slice(index)];\n}\nvar moveArrayAt = (data, from, to) => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n  return data;\n};\nvar prependAt = (data, value) => [...convertToArrayPayload(value), ...convertToArrayPayload(data)];\nfunction removeAtIndexes(data, indexes) {\n  let i = 0;\n  const temp = [...data];\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n  return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\nvar swapArrayAt = (data, indexA, indexB) => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\nvar updateAt = (fieldValues, index, value) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules\n  } = props;\n  const [fields, setFields] = React__default.useState(control._getFieldArray(name));\n  const ids = React__default.useRef(control._getFieldArray(name).map(generateId));\n  const _fieldIds = React__default.useRef(fields);\n  const _name = React__default.useRef(name);\n  const _actioned = React__default.useRef(false);\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n  rules && control.register(name, rules);\n  useIsomorphicLayoutEffect(() => control._subjects.array.subscribe({\n    next: _ref4 => {\n      let {\n        values,\n        name: fieldArrayName\n      } = _ref4;\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    }\n  }).unsubscribe, [control]);\n  const updateValues = React__default.useCallback(updatedFieldArrayValues => {\n    _actioned.current = true;\n    control._setFieldArray(name, updatedFieldArrayValues);\n  }, [control, name]);\n  const append = (value, options) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n    control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const prepend = (value, options) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const remove = index => {\n    const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index\n    });\n  };\n  const insert$1 = (index, value, options) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insert(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insert, {\n      argA: index,\n      argB: fillEmptyArray(value)\n    });\n  };\n  const swap = (indexA, indexB) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n      argA: indexA,\n      argB: indexB\n    }, false);\n  };\n  const move = (from, to) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n      argA: from,\n      argB: to\n    }, false);\n  };\n  const update = (index, value) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n    ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n      argA: index,\n      argB: updateValue\n    }, true, false);\n  };\n  const replace = value => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, [...updatedFieldArrayValues], data => data, {}, true, false);\n  };\n  React__default.useEffect(() => {\n    control._state.action = false;\n    isWatched(name, control._names) && control._subjects.state.next({\n      ...control._formState\n    });\n    if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then(result => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n          if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n            error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors\n            });\n          }\n        });\n      } else {\n        const field = get(control._fields, name);\n        if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n          validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then(error => !isEmptyObject(error) && control._subjects.state.next({\n            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n          }));\n        }\n      }\n    }\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues)\n    });\n    control._names.focus && iterateFieldsByAction(control._fields, (ref, key) => {\n      if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n        ref.focus();\n        return 1;\n      }\n      return;\n    });\n    control._names.focus = '';\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n  React__default.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n    return () => {\n      const updateMounted = (name, value) => {\n        const field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n      control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n  return {\n    swap: React__default.useCallback(swap, [updateValues, name, control]),\n    move: React__default.useCallback(move, [updateValues, name, control]),\n    prepend: React__default.useCallback(prepend, [updateValues, name, control]),\n    append: React__default.useCallback(append, [updateValues, name, control]),\n    remove: React__default.useCallback(remove, [updateValues, name, control]),\n    insert: React__default.useCallback(insert$1, [updateValues, name, control]),\n    update: React__default.useCallback(update, [updateValues, name, control]),\n    replace: React__default.useCallback(replace, [updateValues, name, control]),\n    fields: React__default.useMemo(() => fields.map((field, index) => ({\n      ...field,\n      [keyName]: ids.current[index] || generateId()\n    })), [fields, keyName])\n  };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm() {\n  let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const _formControl = React__default.useRef(undefined);\n  const _values = React__default.useRef(undefined);\n  const [formState, updateFormState] = React__default.useState({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n  });\n  if (!_formControl.current) {\n    if (props.formControl) {\n      _formControl.current = {\n        ...props.formControl,\n        formState\n      };\n      if (props.defaultValues && !isFunction(props.defaultValues)) {\n        props.formControl.reset(props.defaultValues, props.resetOptions);\n      }\n    } else {\n      const {\n        formControl,\n        ...rest\n      } = createFormControl(props);\n      _formControl.current = {\n        ...rest,\n        formState\n      };\n    }\n  }\n  const control = _formControl.current.control;\n  control._options = props;\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({\n        ...control._formState\n      }),\n      reRenderRoot: true\n    });\n    updateFormState(data => ({\n      ...data,\n      isReady: true\n    }));\n    control._formState.isReady = true;\n    return sub;\n  }, [control]);\n  React__default.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n  React__default.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n  React__default.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n  React__default.useEffect(() => {\n    props.shouldUnregister && control._subjects.state.next({\n      values: control._getWatch()\n    });\n  }, [control, props.shouldUnregister]);\n  React__default.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n  React__default.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, {\n        keepFieldsRef: true,\n        ...control._options.resetOptions\n      });\n      _values.current = props.values;\n      updateFormState(state => ({\n        ...state\n      }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n  React__default.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({\n        ...control._formState\n      });\n    }\n    control._removeUnmounted();\n  });\n  _formControl.current.formState = getProxyFormState(formState, control);\n  return _formControl.current;\n}\nexport { Controller, Form, FormProvider, appendErrors, createFormControl, get, set, useController, useFieldArray, useForm, useFormContext, useFormState, useWatch };", "map": {"version": 3, "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "getNodeParentName", "name", "substring", "search", "isNameInFieldArray", "names", "has", "isPlainObject", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Blob", "key", "is<PERSON>ey", "test", "isUndefined", "val", "undefined", "compact", "filter", "Boolean", "stringToPath", "input", "replace", "split", "get", "object", "path", "defaultValue", "result", "reduce", "isBoolean", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "BLUR", "FOCUS_OUT", "CHANGE", "VALIDATION_MODE", "onBlur", "onChange", "onSubmit", "onTouched", "all", "INPUT_VALIDATION_RULES", "max", "min", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "required", "validate", "HookFormContext", "React__default", "createContext", "displayName", "useFormContext", "useContext", "FormProvider", "props", "children", "createElement", "Provider", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "arguments", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "useIsomorphicLayoutEffect", "React", "useLayoutEffect", "useEffect", "useFormState", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "useRef", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_subscribe", "current", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "_defaultValue", "updateValue", "_getWatch", "values", "_formValues", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "useCallback", "ref", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_setDisabledField", "Controller", "render", "flatten", "obj", "output", "keys", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "Form", "mounted", "setMounted", "method", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "handleSubmit", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "some", "includes", "response", "fetch", "String", "body", "status", "_subjects", "state", "next", "isSubmitSuccessful", "setError", "Fragment", "noValidate", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observer", "subscribe", "push", "unsubscribe", "o", "observers", "isPrimitive", "deepEqual", "object1", "object2", "_internal_visited", "WeakSet", "getTime", "keys1", "keys2", "val1", "val2", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "isRadioOrCheckbox", "live", "isConnected", "baseGet", "updatePath", "slice", "isEmptyArray", "unset", "paths", "childObject", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "_ref", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "_ref2", "getResolverOptions", "fieldsNames", "criteriaMode", "shouldUseNativeValidation", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "find", "validateFunction", "hasValidation", "isWatched", "isBlurEvent", "watchName", "startsWith", "iterateFieldsByAction", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "root", "pop", "shouldRenderFormState", "formStateData", "shouldSubscribeByName", "signalName", "currentName", "skipValidation", "isSubmitted", "reValidateMode", "unsetEmptyArray", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "disabled<PERSON>ieldN<PERSON>s", "isFieldArray", "inputValue", "inputRef", "isRadio", "isCheckBox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueNumber", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "shouldFocusError", "createFormControl", "submitCount", "isReady", "isSubmitting", "Set", "unMount", "delayError<PERSON><PERSON><PERSON>", "timer", "_proxySubscribeFormState", "shouldDisplayAllAssociatedErrors", "debounce", "wait", "clearTimeout", "setTimeout", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "_setFieldArray", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "field<PERSON><PERSON><PERSON>", "argA", "argB", "_getDirty", "updateErrors", "_setErrors", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updatedFormState", "context", "executeSchemaAndUpdateState", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "getV<PERSON>ues", "_getFieldArray", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "deps", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "Promise", "shouldFocus", "getFieldState", "clearErrors", "inputName", "currentError", "currentRef", "restOfErrorTree", "payload", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "_ref3", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "_disableForm", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "size", "reset<PERSON>ield", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepFieldsRef", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "setFocus", "shouldSelect", "_resetDefaultValues", "then", "resetOptions", "formControl", "generateId", "crypto", "randomUUID", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeAtIndexes", "indexes", "i", "temp", "removeArrayAt", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "useFieldArray", "keyName", "setFields", "ids", "_fieldIds", "_name", "_actioned", "_ref4", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "appendValue", "prepend", "prependValue", "remove", "insert$1", "insertValue", "swap", "move", "update", "item", "existingError", "useForm", "_formControl", "_values", "sub"], "sources": ["C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isCheckBoxInput.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isDateObject.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isNullOrUndefined.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isObject.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getEventValue.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getNodeParentName.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\isNameInFieldArray.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isPlainObject.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isWeb.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\cloneObject.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isKey.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isUndefined.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\compact.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\stringToPath.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\get.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isBoolean.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\set.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\constants.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\useFormContext.tsx", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getProxyFormState.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\useIsomorphicLayoutEffect.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\useFormState.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isString.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\generateWatchOutput.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\useWatch.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\useController.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\controller.tsx", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\flatten.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\form.tsx", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\appendErrors.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\convertToArrayPayload.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\createSubject.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isPrimitive.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\deepEqual.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isEmptyObject.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isFileInput.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isFunction.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isHTMLElement.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isMultipleSelect.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isRadioInput.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isRadioOrCheckbox.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\live.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\unset.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\objectHasFunction.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getDirtyFields.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getCheckboxValue.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getFieldValueAs.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getRadioValue.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getFieldValue.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getResolverOptions.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isRegex.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getRuleValue.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getValidationModes.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\hasPromiseValidation.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\hasValidation.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\isWatched.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\iterateFieldsByAction.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\schemaErrorLookup.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\shouldRenderFormState.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\shouldSubscribeByName.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\skipValidation.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\unsetEmptyArray.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\updateFieldArrayRootError.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\isMessage.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getValidateError.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getValueAndMessage.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\validateField.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\createFormControl.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\generateId.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\logic\\getFocusFieldName.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\append.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\fillEmptyArray.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\insert.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\move.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\prepend.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\remove.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\swap.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\utils\\update.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\useFieldArray.ts", "C:\\laragon\\www\\frontend\\node_modules\\react-hook-form\\src\\useForm.ts"], "sourcesContent": ["import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "import type { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "export default (value: string) => /^\\w*$/.test(value);\n", "export default (val: unknown): val is undefined => val === undefined;\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import isKey from './isKey';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = (isKey(path) ? [path] : stringToPath(path)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "import type { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport type { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\nHookFormContext.displayName = 'HookFormContext';\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import * as React from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport type {\n  FieldValues,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import type { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName),\n        get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport type {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext<TFieldValues>();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _defaultValue = React.useRef(defaultValue);\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      _defaultValue.current as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) =>\n          !disabled &&\n          updateValue(\n            generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            ),\n          ),\n      }),\n    [name, control, disabled, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport type {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus && elm.focus(),\n          select: () => elm.select && elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import type { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import type { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport type { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import type {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import type { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import type { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(\n  object1: any,\n  object2: any,\n  _internal_visited = new WeakSet(),\n) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n    return true;\n  }\n  _internal_visited.add(object1);\n  _internal_visited.add(object2);\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2, _internal_visited)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import type { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import type { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import type { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import type { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import type { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import type {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import type {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import type { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import type { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import type { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import type { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import type { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport type {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | readonly string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import type { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "import type {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import type { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import type { Field<PERSON>rror, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import type { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport type {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport type {\n  BatchField<PERSON>rrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormSubscribe,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach((checkboxRef) => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(\n                    (data: string) => data === checkboxRef.value,\n                  );\n                } else {\n                  checkboxRef.checked =\n                    fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(\n      _options.reValidateMode,\n    );\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n          type?: EventType;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFormSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = cloneObject(values) as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          unset(fieldValues, name);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        if (keepStateOptions.keepFieldsRef) {\n          for (const fieldName of _names.mount) {\n            setValue(\n              fieldName as FieldPath<TFieldValues>,\n              get(values, fieldName),\n            );\n          }\n        } else {\n          _fields = {};\n        }\n      }\n\n      _formValues = _options.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? (cloneObject(_defaultValues) as TFieldValues)\n          : ({} as TFieldValues)\n        : (cloneObject(values) as TFieldValues);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "export default () => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import type { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport type {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  rules &&\n    (control as Control<TFieldValues, any, TTransformedValues>).register(\n      name as FieldPath<TFieldValues>,\n      rules as RegisterOptions<TFieldValues>,\n    );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === _name.current || !fieldArrayName) {\n            const fieldValues = get(values, _name.current);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport type {\n  FieldValues,\n  FormState,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    if (props.formControl) {\n      _formControl.current = {\n        ...props.formControl,\n        formState,\n      };\n\n      if (props.defaultValues && !isFunction(props.defaultValues)) {\n        props.formControl.reset(props.defaultValues, props.resetOptions);\n      }\n    } else {\n      const { formControl, ...rest } = createFormControl(props);\n\n      _formControl.current = {\n        ...rest,\n        formState,\n      };\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, {\n        keepFieldsRef: true,\n        ...control._options.resetOptions,\n      });\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "mappings": ";;AAEA,IAAAA,eAAA,GAAgBC,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,UAAU;ACH7B,IAAAC,YAAA,GAAgBC,KAAc,IAAoBA,KAAK,YAAYC,IAAI;ACAvE,IAAAC,iBAAA,GAAgBF,KAAc,IAAgCA,KAAK,IAAI,IAAI;ACGpE,MAAMG,YAAY,GAAIH,KAAc,IACzC,OAAOA,KAAK,KAAK,QAAQ;AAE3B,IAAAI,QAAA,GAAkCJ,KAAc,IAC9C,CAACE,iBAAiB,CAACF,KAAK,CAAC,IACzB,CAACK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,IACrBG,YAAY,CAACH,KAAK,CAAC,IACnB,CAACD,YAAY,CAACC,KAAK,CAAC;ACLtB,IAAAO,aAAA,GAAgBC,KAAc,IAC5BJ,QAAQ,CAACI,KAAK,CAAC,IAAKA,KAAe,CAACC,MAAA,GAChCb,eAAe,CAAEY,KAAe,CAACC,MAAM,IACpCD,KAAe,CAACC,MAAM,CAACC,OAAA,GACvBF,KAAe,CAACC,MAAM,CAACT,KAAA,GAC1BQ,KAAK;ACVX,IAAAG,iBAAA,GAAgBC,IAAY,IAC1BA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAED,IAAI,CAACE,MAAM,CAAC,aAAa,CAAC,CAAC,IAAIF,IAAI;ACGvD,IAAAG,kBAAA,GAAeA,CAACC,KAA6B,EAAEJ,IAAuB,KACpEI,KAAK,CAACC,GAAG,CAACN,iBAAiB,CAACC,IAAI,CAAC,CAAC;ACHpC,IAAAM,aAAA,GAAgBC,UAAkB,IAAI;EACpC,MAAMC,aAAa,GACjBD,UAAU,CAACE,WAAW,IAAIF,UAAU,CAACE,WAAW,CAACC,SAAS;EAE5D,OACElB,QAAQ,CAACgB,aAAa,CAAC,IAAIA,aAAa,CAACG,cAAc,CAAC,eAAe,CAAC;AAE5E,CAAC;ACTD,IAAAC,KAAA,GAAe,OAAOC,MAAM,KAAK,WAAW,IAC1C,OAAOA,MAAM,CAACC,WAAW,KAAK,WAAW,IACzC,OAAOC,QAAQ,KAAK,WAAW;ACEnB,SAAUC,WAAWA,CAAIC,IAAO;EAC5C,IAAIC,IAAS;EACb,MAAMxB,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EACnC,MAAME,kBAAkB,GACtB,OAAOC,QAAQ,KAAK,WAAW,GAAGH,IAAI,YAAYG,QAAQ,GAAG,KAAK;EAEpE,IAAIH,IAAI,YAAY5B,IAAI,EAAE;IACxB6B,IAAI,GAAG,IAAI7B,IAAI,CAAC4B,IAAI,CAAC;SAChB,IACL,EAAEL,KAAK,KAAKK,IAAI,YAAYI,IAAI,IAAIF,kBAAkB,CAAC,CAAC,KACvDzB,OAAO,IAAIF,QAAQ,CAACyB,IAAI,CAAC,CAAC,EAC3B;IACAC,IAAI,GAAGxB,OAAO,GAAG,EAAE,GAAG,EAAE;IAExB,IAAI,CAACA,OAAO,IAAI,CAACY,aAAa,CAACW,IAAI,CAAC,EAAE;MACpCC,IAAI,GAAGD,IAAI;WACN;MACL,KAAK,MAAMK,GAAG,IAAIL,IAAI,EAAE;QACtB,IAAIA,IAAI,CAACN,cAAc,CAACW,GAAG,CAAC,EAAE;UAC5BJ,IAAI,CAACI,GAAG,CAAC,GAAGN,WAAW,CAACC,IAAI,CAACK,GAAG,CAAC,CAAC;;;;SAInC;IACL,OAAOL,IAAI;;EAGb,OAAOC,IAAI;AACb;AChCA,IAAAK,KAAA,GAAgBnC,KAAa,IAAK,OAAO,CAACoC,IAAI,CAACpC,KAAK,CAAC;ACArD,IAAAqC,WAAA,GAAgBC,GAAY,IAAuBA,GAAG,KAAKC,SAAS;ACApE,IAAAC,OAAA,GAAwBxC,KAAe,IACrCK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAACyC,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE;ACCnD,IAAAC,YAAA,GAAgBC,KAAa,IAC3BJ,OAAO,CAACI,KAAK,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC;ACGxD,IAAAC,GAAA,GAAeA,CACbC,MAAS,EACTC,IAAoB,EACpBC,YAAsB,KACf;EACP,IAAI,CAACD,IAAI,IAAI,CAAC7C,QAAQ,CAAC4C,MAAM,CAAC,EAAE;IAC9B,OAAOE,YAAY;;EAGrB,MAAMC,MAAM,GAAG,CAAChB,KAAK,CAACc,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC,GAAGN,YAAY,CAACM,IAAI,CAAC,EAAEG,MAAM,CAC/D,CAACD,MAAM,EAAEjB,GAAG,KACVhC,iBAAiB,CAACiD,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAACjB,GAAe,CAAC,EAC9Dc,MAAM,CACP;EAED,OAAOX,WAAW,CAACc,MAAM,CAAC,IAAIA,MAAM,KAAKH,MAAA,GACrCX,WAAW,CAACW,MAAM,CAACC,IAAe,CAAC,IACjCC,YAAA,GACAF,MAAM,CAACC,IAAe,IACxBE,MAAM;AACZ,CAAC;AC1BD,IAAAE,SAAA,GAAgBrD,KAAc,IAAuB,OAAOA,KAAK,KAAK,SAAS;ACM/E,IAAAsD,GAAA,GAAeA,CACbN,MAAmB,EACnBC,IAA4B,EAC5BjD,KAAe,KACb;EACF,IAAIuD,KAAK,GAAG,EAAE;EACd,MAAMC,QAAQ,GAAGrB,KAAK,CAACc,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC,GAAGN,YAAY,CAACM,IAAI,CAAC;EAC1D,MAAMQ,MAAM,GAAGD,QAAQ,CAACC,MAAM;EAC9B,MAAMC,SAAS,GAAGD,MAAM,GAAG,CAAC;EAE5B,OAAO,EAAEF,KAAK,GAAGE,MAAM,EAAE;IACvB,MAAMvB,GAAG,GAAGsB,QAAQ,CAACD,KAAK,CAAC;IAC3B,IAAII,QAAQ,GAAG3D,KAAK;IAEpB,IAAIuD,KAAK,KAAKG,SAAS,EAAE;MACvB,MAAME,QAAQ,GAAGZ,MAAM,CAACd,GAAG,CAAC;MAC5ByB,QAAQ,GACNvD,QAAQ,CAACwD,QAAQ,CAAC,IAAIvD,KAAK,CAACC,OAAO,CAACsD,QAAQ,IACxCA,QAAA,GACA,CAACC,KAAK,CAAC,CAACL,QAAQ,CAACD,KAAK,GAAG,CAAC,CAAC,IACzB,KACA,EAAE;;IAGZ,IAAIrB,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,aAAa,IAAIA,GAAG,KAAK,WAAW,EAAE;MACvE;;IAGFc,MAAM,CAACd,GAAG,CAAC,GAAGyB,QAAQ;IACtBX,MAAM,GAAGA,MAAM,CAACd,GAAG,CAAC;;AAExB,CAAC;ACrCM,MAAM4B,MAAM,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE;CACA;AAEH,MAAMC,eAAe,GAAG;EAC7BC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,WAAW;EACtBC,GAAG,EAAE;CACG;AAEH,MAAMC,sBAAsB,GAAG;EACpCC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;CACF;AClBV,MAAMC,eAAe,GAAGC,cAAK,CAACC,aAAa,CAAuB,IAAI,CAAC;AACvEF,eAAe,CAACG,WAAW,GAAG,iBAAiB;AAE/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACI,MAAMC,cAAc,GAAGA,CAAA,KAK5BH,cAAK,CAACI,UAAU,CAACL,eAAe;AAMlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACI,MAAMM,YAAY,GAKvBC,KAAoE,IAClE;EACF,MAAM;IAAEC,QAAQ;IAAE,GAAG3D;EAAI,CAAE,GAAG0D,KAAK;EACnC,OACEN,cAAA,CAAAQ,aAAA,CAACT,eAAe,CAACU,QAAQ;IAAC1F,KAAK,EAAE6B;EAAgC,GAC9D2D,QAAQ,CACgB;AAE/B;ACxFA,IAAAG,iBAAA,GAAe,SAAAA,CAKbC,SAAkC,EAClCC,OAA4D,EAC5DC,mBAAmC,EAEjC;EAAA,IADFC,MAAM,GAAAC,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,IAAI;EAEb,MAAM7C,MAAM,GAAG;IACb8C,aAAa,EAAEJ,OAAO,CAACK;GACJ;EAErB,KAAK,MAAMhE,GAAG,IAAI0D,SAAS,EAAE;IAC3BO,MAAM,CAACC,cAAc,CAACjD,MAAM,EAAEjB,GAAG,EAAE;MACjCa,GAAG,EAAEA,CAAA,KAAK;QACR,MAAMsD,IAAI,GAAGnE,GAA0D;QAEvE,IAAI2D,OAAO,CAACS,eAAe,CAACD,IAAI,CAAC,KAAKnC,eAAe,CAACK,GAAG,EAAE;UACzDsB,OAAO,CAACS,eAAe,CAACD,IAAI,CAAC,GAAG,CAACN,MAAM,IAAI7B,eAAe,CAACK,GAAG;;QAGhEuB,mBAAmB,KAAKA,mBAAmB,CAACO,IAAI,CAAC,GAAG,IAAI,CAAC;QACzD,OAAOT,SAAS,CAACS,IAAI,CAAC;;IAEzB,EAAC;;EAGJ,OAAOlD,MAAM;AACf,CAAC;AC/BM,MAAMoD,yBAAyB,GACpC,OAAO9E,MAAM,KAAK,WAAW,GAAG+E,KAAK,CAACC,eAAe,GAAGD,KAAK,CAACE,SAAS;;ACQzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACG,SAAUC,YAAYA,CAI1BpB,KAA2D;EAE3D,MAAMqB,OAAO,GAAGxB,cAAc,EAAyC;EACvE,MAAM;IAAES,OAAO,GAAGe,OAAO,CAACf,OAAO;IAAEgB,QAAQ;IAAEjG,IAAI;IAAEkG;EAAK,CAAE,GAAGvB,KAAK,IAAI,EAAE;EACxE,MAAM,CAACK,SAAS,EAAEmB,eAAe,CAAC,GAAG9B,cAAK,CAAC+B,QAAQ,CAACnB,OAAO,CAACoB,UAAU,CAAC;EACvE,MAAMC,oBAAoB,GAAGjC,cAAK,CAACkC,MAAM,CAAC;IACxCC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,gBAAgB,EAAE,KAAK;IACvBC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;EACT,EAAC;EAEFpB,yBAAyB,CACvB,MACEV,OAAO,CAAC+B,UAAU,CAAC;IACjBhH,IAAI;IACJgF,SAAS,EAAEsB,oBAAoB,CAACW,OAAO;IACvCf,KAAK;IACLgB,QAAQ,EAAGlC,SAAS,IAAI;MACtB,CAACiB,QAAQ,IACPE,eAAe,CAAC;QACd,GAAGlB,OAAO,CAACoB,UAAU;QACrB,GAAGrB;MACJ,EAAC;;GAEP,CAAC,EACJ,CAAChF,IAAI,EAAEiG,QAAQ,EAAEC,KAAK,CAAC,CACxB;EAED7B,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnBQ,oBAAoB,CAACW,OAAO,CAACH,OAAO,IAAI7B,OAAO,CAACkC,SAAS,CAAC,IAAI,CAAC;EACjE,CAAC,EAAE,CAAClC,OAAO,CAAC,CAAC;EAEb,OAAOZ,cAAK,CAAC+C,OAAO,CAClB,MACErC,iBAAiB,CACfC,SAAS,EACTC,OAAO,EACPqB,oBAAoB,CAACW,OAAO,EAC5B,KAAK,CACN,EACH,CAACjC,SAAS,EAAEC,OAAO,CAAC,CACrB;AACH;AC5FA,IAAAoC,QAAA,GAAgBjI,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;ACI7E,IAAAkI,mBAAA,GAAeA,CACblH,KAAoC,EACpCmH,MAAa,EACbC,UAAwB,EACxBC,QAAkB,EAClBnF,YAAuC,KACrC;EACF,IAAI+E,QAAQ,CAACjH,KAAK,CAAC,EAAE;IACnBqH,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAACvH,KAAK,CAAC;IACnC,OAAO+B,GAAG,CAACqF,UAAU,EAAEpH,KAAK,EAAEkC,YAAY,CAAC;;EAG7C,IAAI7C,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAACwH,GAAG,CACbC,SAAS,KACRJ,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAACE,SAAS,CAAC,EACvC1F,GAAG,CAACqF,UAAU,EAAEK,SAAS,CAAC,CAC3B,CACF;;EAGHJ,QAAQ,KAAKF,MAAM,CAACO,QAAQ,GAAG,IAAI,CAAC;EAEpC,OAAON,UAAU;AACnB,CAAC;;ACoGD;;;;;;;;;;;;;;;AAeG;AACG,SAAUO,QAAQA,CACtBpD,KAAmC;EAEnC,MAAMqB,OAAO,GAAGxB,cAAc,EAAgB;EAC9C,MAAM;IACJS,OAAO,GAAGe,OAAO,CAACf,OAAO;IACzBjF,IAAI;IACJsC,YAAY;IACZ2D,QAAQ;IACRC;EAAK,CACN,GAAGvB,KAAK,IAAI,EAAE;EACf,MAAMqD,aAAa,GAAG3D,cAAK,CAACkC,MAAM,CAACjE,YAAY,CAAC;EAChD,MAAM,CAAClD,KAAK,EAAE6I,WAAW,CAAC,GAAG5D,cAAK,CAAC+B,QAAQ,CACzCnB,OAAO,CAACiD,SAAS,CACflI,IAAyB,EACzBgI,aAAa,CAACf,OAAgD,CAC/D,CACF;EAEDtB,yBAAyB,CACvB,MACEV,OAAO,CAAC+B,UAAU,CAAC;IACjBhH,IAAI;IACJgF,SAAS,EAAE;MACTmD,MAAM,EAAE;IACT;IACDjC,KAAK;IACLgB,QAAQ,EAAGlC,SAAS,IAClB,CAACiB,QAAQ,IACTgC,WAAW,CACTX,mBAAmB,CACjBtH,IAA+C,EAC/CiF,OAAO,CAACsC,MAAM,EACdvC,SAAS,CAACmD,MAAM,IAAIlD,OAAO,CAACmD,WAAW,EACvC,KAAK,EACLJ,aAAa,CAACf,OAAO,CACtB;GAEN,CAAC,EACJ,CAACjH,IAAI,EAAEiF,OAAO,EAAEgB,QAAQ,EAAEC,KAAK,CAAC,CACjC;EAED7B,cAAK,CAACyB,SAAS,CAAC,MAAMb,OAAO,CAACoD,gBAAgB,EAAE,CAAC;EAEjD,OAAOjJ,KAAK;AACd;;ACrKA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACG,SAAUkJ,aAAaA,CAK3B3D,KAAkE;EAElE,MAAMqB,OAAO,GAAGxB,cAAc,EAAyC;EACvE,MAAM;IAAExE,IAAI;IAAEiG,QAAQ;IAAEhB,OAAO,GAAGe,OAAO,CAACf,OAAO;IAAEsD;EAAgB,CAAE,GAAG5D,KAAK;EAC7E,MAAM6D,YAAY,GAAGrI,kBAAkB,CAAC8E,OAAO,CAACsC,MAAM,CAACkB,KAAK,EAAEzI,IAAI,CAAC;EACnE,MAAMZ,KAAK,GAAG2I,QAAQ,CAAC;IACrB9C,OAAO;IACPjF,IAAI;IACJsC,YAAY,EAAEH,GAAG,CACf8C,OAAO,CAACmD,WAAW,EACnBpI,IAAI,EACJmC,GAAG,CAAC8C,OAAO,CAACK,cAAc,EAAEtF,IAAI,EAAE2E,KAAK,CAACrC,YAAY,CAAC,CACtD;IACD4D,KAAK,EAAE;EACR,EAAwC;EACzC,MAAMlB,SAAS,GAAGe,YAAY,CAAC;IAC7Bd,OAAO;IACPjF,IAAI;IACJkG,KAAK,EAAE;EACR,EAAC;EAEF,MAAMwC,MAAM,GAAGrE,cAAK,CAACkC,MAAM,CAAC5B,KAAK,CAAC;EAClC,MAAMgE,cAAc,GAAGtE,cAAK,CAACkC,MAAM,CACjCtB,OAAO,CAAC2D,QAAQ,CAAC5I,IAAI,EAAE;IACrB,GAAG2E,KAAK,CAACkE,KAAK;IACdzJ,KAAK;IACL,IAAIqD,SAAS,CAACkC,KAAK,CAACsB,QAAQ,CAAC,GAAG;MAAEA,QAAQ,EAAEtB,KAAK,CAACsB;IAAQ,CAAE,GAAG,EAAE;EAClE,EAAC,CACH;EAED,MAAM6C,UAAU,GAAGzE,cAAK,CAAC+C,OAAO,CAC9B,MACE7B,MAAM,CAACwD,gBAAgB,CACrB,EAAE,EACF;IACEC,OAAO,EAAE;MACPC,UAAU,EAAE,IAAI;MAChB9G,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAAC6C,SAAS,CAAC+B,MAAM,EAAE/G,IAAI;IACxC;IACDwG,OAAO,EAAE;MACPyC,UAAU,EAAE,IAAI;MAChB9G,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAAC6C,SAAS,CAAC0B,WAAW,EAAE1G,IAAI;IAC7C;IACDkJ,SAAS,EAAE;MACTD,UAAU,EAAE,IAAI;MAChB9G,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAAC6C,SAAS,CAAC2B,aAAa,EAAE3G,IAAI;IAC/C;IACD6G,YAAY,EAAE;MACZoC,UAAU,EAAE,IAAI;MAChB9G,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAAC6C,SAAS,CAAC4B,gBAAgB,EAAE5G,IAAI;IAClD;IACDmJ,KAAK,EAAE;MACLF,UAAU,EAAE,IAAI;MAChB9G,GAAG,EAAEA,CAAA,KAAMA,GAAG,CAAC6C,SAAS,CAAC+B,MAAM,EAAE/G,IAAI;IACtC;EACF,EACsB,EAC3B,CAACgF,SAAS,EAAEhF,IAAI,CAAC,CAClB;EAED,MAAMwD,QAAQ,GAAGa,cAAK,CAAC+E,WAAW,CAC/BxJ,KAAU,IACT+I,cAAc,CAAC1B,OAAO,CAACzD,QAAQ,CAAC;IAC9B3D,MAAM,EAAE;MACNT,KAAK,EAAEO,aAAa,CAACC,KAAK,CAAC;MAC3BI,IAAI,EAAEA;IACP;IACDd,IAAI,EAAEgE,MAAM,CAACG;EACd,EAAC,EACJ,CAACrD,IAAI,CAAC,CACP;EAED,MAAMuD,MAAM,GAAGc,cAAK,CAAC+E,WAAW,CAC9B,MACET,cAAc,CAAC1B,OAAO,CAAC1D,MAAM,CAAC;IAC5B1D,MAAM,EAAE;MACNT,KAAK,EAAE+C,GAAG,CAAC8C,OAAO,CAACmD,WAAW,EAAEpI,IAAI,CAAC;MACrCA,IAAI,EAAEA;IACP;IACDd,IAAI,EAAEgE,MAAM,CAACC;GACd,CAAC,EACJ,CAACnD,IAAI,EAAEiF,OAAO,CAACmD,WAAW,CAAC,CAC5B;EAED,MAAMiB,GAAG,GAAGhF,cAAK,CAAC+E,WAAW,CAC1BE,GAAQ,IAAI;IACX,MAAMC,KAAK,GAAGpH,GAAG,CAAC8C,OAAO,CAACuE,OAAO,EAAExJ,IAAI,CAAC;IAExC,IAAIuJ,KAAK,IAAID,GAAG,EAAE;MAChBC,KAAK,CAACE,EAAE,CAACJ,GAAG,GAAG;QACbK,KAAK,EAAEA,CAAA,KAAMJ,GAAG,CAACI,KAAK,IAAIJ,GAAG,CAACI,KAAK,EAAE;QACrCC,MAAM,EAAEA,CAAA,KAAML,GAAG,CAACK,MAAM,IAAIL,GAAG,CAACK,MAAM,EAAE;QACxCC,iBAAiB,EAAGC,OAAe,IACjCP,GAAG,CAACM,iBAAiB,CAACC,OAAO,CAAC;QAChCC,cAAc,EAAEA,CAAA,KAAMR,GAAG,CAACQ,cAAc;OACzC;;GAEJ,EACD,CAAC7E,OAAO,CAACuE,OAAO,EAAExJ,IAAI,CAAC,CACxB;EAED,MAAMuJ,KAAK,GAAGlF,cAAK,CAAC+C,OAAO,CACzB,OAAO;IACLpH,IAAI;IACJZ,KAAK;IACL,IAAIqD,SAAS,CAACwD,QAAQ,CAAC,IAAIjB,SAAS,CAACiB,QAAA,GACjC;MAAEA,QAAQ,EAAEjB,SAAS,CAACiB,QAAQ,IAAIA;IAAQ,IAC1C,EAAE,CAAC;IACPzC,QAAQ;IACRD,MAAM;IACN8F;EACD,EAAC,EACF,CAACrJ,IAAI,EAAEiG,QAAQ,EAAEjB,SAAS,CAACiB,QAAQ,EAAEzC,QAAQ,EAAED,MAAM,EAAE8F,GAAG,EAAEjK,KAAK,CAAC,CACnE;EAEDiF,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnB,MAAMiE,sBAAsB,GAC1B9E,OAAO,CAAC+E,QAAQ,CAACzB,gBAAgB,IAAIA,gBAAgB;IAEvDtD,OAAO,CAAC2D,QAAQ,CAAC5I,IAAI,EAAE;MACrB,GAAG0I,MAAM,CAACzB,OAAO,CAAC4B,KAAK;MACvB,IAAIpG,SAAS,CAACiG,MAAM,CAACzB,OAAO,CAAChB,QAAQ,IACjC;QAAEA,QAAQ,EAAEyC,MAAM,CAACzB,OAAO,CAAChB;MAAQ,IACnC,EAAE;IACP,EAAC;IAEF,MAAMgE,aAAa,GAAGA,CAACjK,IAAuB,EAAEZ,KAAc,KAAI;MAChE,MAAMmK,KAAK,GAAUpH,GAAG,CAAC8C,OAAO,CAACuE,OAAO,EAAExJ,IAAI,CAAC;MAE/C,IAAIuJ,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;QACrBF,KAAK,CAACE,EAAE,CAACS,KAAK,GAAG9K,KAAK;;IAE1B,CAAC;IAED6K,aAAa,CAACjK,IAAI,EAAE,IAAI,CAAC;IAEzB,IAAI+J,sBAAsB,EAAE;MAC1B,MAAM3K,KAAK,GAAG4B,WAAW,CAACmB,GAAG,CAAC8C,OAAO,CAAC+E,QAAQ,CAAC3E,aAAa,EAAErF,IAAI,CAAC,CAAC;MACpE0C,GAAG,CAACuC,OAAO,CAACK,cAAc,EAAEtF,IAAI,EAAEZ,KAAK,CAAC;MACxC,IAAIqC,WAAW,CAACU,GAAG,CAAC8C,OAAO,CAACmD,WAAW,EAAEpI,IAAI,CAAC,CAAC,EAAE;QAC/C0C,GAAG,CAACuC,OAAO,CAACmD,WAAW,EAAEpI,IAAI,EAAEZ,KAAK,CAAC;;;IAIzC,CAACoJ,YAAY,IAAIvD,OAAO,CAAC2D,QAAQ,CAAC5I,IAAI,CAAC;IAEvC,OAAO,MAAK;MACV,CACEwI,YAAA,GACIuB,sBAAsB,IAAI,CAAC9E,OAAO,CAACkF,MAAM,CAACC,MAAA,GAC1CL,sBAAsB,IAExB9E,OAAO,CAACoF,UAAU,CAACrK,IAAI,IACvBiK,aAAa,CAACjK,IAAI,EAAE,KAAK,CAAC;IAChC,CAAC;GACF,EAAE,CAACA,IAAI,EAAEiF,OAAO,EAAEuD,YAAY,EAAED,gBAAgB,CAAC,CAAC;EAEnDlE,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnBb,OAAO,CAACqF,iBAAiB,CAAC;MACxBrE,QAAQ;MACRjG;IACD,EAAC;GACH,EAAE,CAACiG,QAAQ,EAAEjG,IAAI,EAAEiF,OAAO,CAAC,CAAC;EAE7B,OAAOZ,cAAK,CAAC+C,OAAO,CAClB,OAAO;IACLmC,KAAK;IACLvE,SAAS;IACT8D;GACD,CAAC,EACF,CAACS,KAAK,EAAEvE,SAAS,EAAE8D,UAAU,CAAC,CAC/B;AACH;;AC9NA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCG;AACH,MAAMyB,UAAU,GAKd5F,KAA+D,IAE/DA,KAAK,CAAC6F,MAAM,CAAClC,aAAa,CAA0C3D,KAAK,CAAC;AChDrE,MAAM8F,OAAO,GAAIC,GAAgB,IAAI;EAC1C,MAAMC,MAAM,GAAgB,EAAE;EAE9B,KAAK,MAAMrJ,GAAG,IAAIiE,MAAM,CAACqF,IAAI,CAACF,GAAG,CAAC,EAAE;IAClC,IAAInL,YAAY,CAACmL,GAAG,CAACpJ,GAAG,CAAC,CAAC,IAAIoJ,GAAG,CAACpJ,GAAG,CAAC,KAAK,IAAI,EAAE;MAC/C,MAAMuJ,MAAM,GAAGJ,OAAO,CAACC,GAAG,CAACpJ,GAAG,CAAC,CAAC;MAEhC,KAAK,MAAMwJ,SAAS,IAAIvF,MAAM,CAACqF,IAAI,CAACC,MAAM,CAAC,EAAE;QAC3CF,MAAM,CAAC,GAAGrJ,GAAG,IAAIwJ,SAAS,EAAE,CAAC,GAAGD,MAAM,CAACC,SAAS,CAAC;;WAE9C;MACLH,MAAM,CAACrJ,GAAG,CAAC,GAAGoJ,GAAG,CAACpJ,GAAG,CAAC;;;EAI1B,OAAOqJ,MAAM;AACf,CAAC;ACdD,MAAMI,YAAY,GAAG,MAAM;AAE3B;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,SAASC,IAAIA,CAGXrG,KAAkD;EAClD,MAAMqB,OAAO,GAAGxB,cAAc,EAAyC;EACvE,MAAM,CAACyG,OAAO,EAAEC,UAAU,CAAC,GAAG7G,cAAK,CAAC+B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IACJnB,OAAO,GAAGe,OAAO,CAACf,OAAO;IACzBxB,QAAQ;IACRmB,QAAQ;IACRwF,MAAM;IACNe,MAAM,GAAGJ,YAAY;IACrBK,OAAO;IACPC,OAAO;IACPC,OAAO;IACPd,MAAM;IACNe,SAAS;IACTC,cAAc;IACd,GAAGC;EAAI,CACR,GAAG9G,KAAK;EAET,MAAM+G,MAAM,GAAG,MAAO9L,KAAgC,IAAI;IACxD,IAAI+L,QAAQ,GAAG,KAAK;IACpB,IAAIzM,IAAI,GAAG,EAAE;IAEb,MAAM+F,OAAO,CAAC2G,YAAY,CAAC,MAAO3K,IAAI,IAAI;MACxC,MAAM4K,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAIC,YAAY,GAAG,EAAE;MAErB,IAAI;QACFA,YAAY,GAAGC,IAAI,CAACC,SAAS,CAAChL,IAAI,CAAC;QACnC,OAAAiL,EAAA,EAAM;MAER,MAAMC,iBAAiB,GAAG1B,OAAO,CAACxF,OAAO,CAACmD,WAAW,CAAC;MAEtD,KAAK,MAAM9G,GAAG,IAAI6K,iBAAiB,EAAE;QACnCN,QAAQ,CAACO,MAAM,CAAC9K,GAAG,EAAE6K,iBAAiB,CAAC7K,GAAG,CAAC,CAAC;;MAG9C,IAAImC,QAAQ,EAAE;QACZ,MAAMA,QAAQ,CAAC;UACbxC,IAAI;UACJrB,KAAK;UACLuL,MAAM;UACNU,QAAQ;UACRE;QACD,EAAC;;MAGJ,IAAI3B,MAAM,EAAE;QACV,IAAI;UACF,MAAMiC,6BAA6B,GAAG,CACpCjB,OAAO,IAAIA,OAAO,CAAC,cAAc,CAAC,EAClCC,OAAO,CACR,CAACiB,IAAI,CAAElN,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACmN,QAAQ,CAAC,MAAM,CAAC,CAAC;UAElD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACC,MAAM,CAACtC,MAAM,CAAC,EAAE;YAC3Ce,MAAM;YACNC,OAAO,EAAE;cACP,GAAGA,OAAO;cACV,IAAIC,OAAO,GAAG;gBAAE,cAAc,EAAEA;cAAO,CAAE,GAAG,EAAE;YAC/C;YACDsB,IAAI,EAAEN,6BAA6B,GAAGN,YAAY,GAAGF;UACtD,EAAC;UAEF,IACEW,QAAQ,KACPhB,cAAA,GACG,CAACA,cAAc,CAACgB,QAAQ,CAACI,MAAM,IAC/BJ,QAAQ,CAACI,MAAM,GAAG,GAAG,IAAIJ,QAAQ,CAACI,MAAM,IAAI,GAAG,CAAC,EACpD;YACAjB,QAAQ,GAAG,IAAI;YACfL,OAAO,IAAIA,OAAO,CAAC;cAAEkB;YAAQ,CAAE,CAAC;YAChCtN,IAAI,GAAGwN,MAAM,CAACF,QAAQ,CAACI,MAAM,CAAC;iBACzB;YACLrB,SAAS,IAAIA,SAAS,CAAC;cAAEiB;YAAQ,CAAE,CAAC;;UAEtC,OAAOrD,KAAc,EAAE;UACvBwC,QAAQ,GAAG,IAAI;UACfL,OAAO,IAAIA,OAAO,CAAC;YAAEnC;UAAK,CAAE,CAAC;;;IAGnC,CAAC,CAAC,CAACvJ,KAAK,CAAC;IAET,IAAI+L,QAAQ,IAAIhH,KAAK,CAACM,OAAO,EAAE;MAC7BN,KAAK,CAACM,OAAO,CAAC4H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACjCC,kBAAkB,EAAE;MACrB,EAAC;MACFrI,KAAK,CAACM,OAAO,CAACgI,QAAQ,CAAC,aAAa,EAAE;QACpC/N;MACD,EAAC;;EAEN,CAAC;EAEDmF,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnBoF,UAAU,CAAC,IAAI,CAAC;GACjB,EAAE,EAAE,CAAC;EAEN,OAAOV,MAAM,GACXnG,cAAA,CAAAQ,aAAA,CAAAR,cAAA,CAAA6I,QAAA,QACG1C,MAAM,CAAC;IACNkB;EACD,EAAC,CACD,GAEHrH,cAAA,CAAAQ,aAAA;IACEsI,UAAU,EAAElC,OAAO;IACnBb,MAAM,EAAEA,MAAM;IACde,MAAM,EAAEA,MAAM;IACdE,OAAO,EAAEA,OAAO;IAChB5H,QAAQ,EAAEiI,MAAM;IAAA,GACZD;EAAI,GAEP7G,QAAQ,CAEZ;AACH;AC5IA,IAAAwI,YAAA,GAAeA,CACbpN,IAAuB,EACvBqN,wBAAiC,EACjCtG,MAA2B,EAC3B7H,IAAY,EACZ2K,OAAuB,KAEvBwD,wBAAA,GACI;EACE,GAAGtG,MAAM,CAAC/G,IAAI,CAAC;EACfsN,KAAK,EAAE;IACL,IAAIvG,MAAM,CAAC/G,IAAI,CAAC,IAAI+G,MAAM,CAAC/G,IAAI,CAAE,CAACsN,KAAK,GAAGvG,MAAM,CAAC/G,IAAI,CAAE,CAACsN,KAAK,GAAG,EAAE,CAAC;IACnE,CAACpO,IAAI,GAAG2K,OAAO,IAAI;EACpB;AACF,IACD,EAAE;ACrBR,IAAA0D,qBAAA,GAAmBnO,KAAQ,IAAMK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAE;ACgBxE,IAAAoO,aAAA,GAAeA,CAAA,KAAoB;EACjC,IAAIC,UAAU,GAAkB,EAAE;EAElC,MAAMV,IAAI,GAAI3N,KAAQ,IAAI;IACxB,KAAK,MAAMsO,QAAQ,IAAID,UAAU,EAAE;MACjCC,QAAQ,CAACX,IAAI,IAAIW,QAAQ,CAACX,IAAI,CAAC3N,KAAK,CAAC;;EAEzC,CAAC;EAED,MAAMuO,SAAS,GAAID,QAAqB,IAAkB;IACxDD,UAAU,CAACG,IAAI,CAACF,QAAQ,CAAC;IACzB,OAAO;MACLG,WAAW,EAAEA,CAAA,KAAK;QAChBJ,UAAU,GAAGA,UAAU,CAAC5L,MAAM,CAAEiM,CAAC,IAAKA,CAAC,KAAKJ,QAAQ,CAAC;;KAExD;EACH,CAAC;EAED,MAAMG,WAAW,GAAGA,CAAA,KAAK;IACvBJ,UAAU,GAAG,EAAE;EACjB,CAAC;EAED,OAAO;IACL,IAAIM,SAASA,CAAA;MACX,OAAON,UAAU;KAClB;IACDV,IAAI;IACJY,SAAS;IACTE;GACD;AACH,CAAC;ACzCD,IAAAG,WAAA,GAAgB5O,KAAc,IAC5BE,iBAAiB,CAACF,KAAK,CAAC,IAAI,CAACG,YAAY,CAACH,KAAK,CAAC;ACDpC,SAAU6O,SAASA,CAC/BC,OAAY,EACZC,OAAY,EACqB;EAAA,IAAjCC,iBAAiB,GAAAhJ,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,IAAIiJ,OAAO,EAAE;EAEjC,IAAIL,WAAW,CAACE,OAAO,CAAC,IAAIF,WAAW,CAACG,OAAO,CAAC,EAAE;IAChD,OAAOD,OAAO,KAAKC,OAAO;;EAG5B,IAAIhP,YAAY,CAAC+O,OAAO,CAAC,IAAI/O,YAAY,CAACgP,OAAO,CAAC,EAAE;IAClD,OAAOD,OAAO,CAACI,OAAO,EAAE,KAAKH,OAAO,CAACG,OAAO,EAAE;;EAGhD,MAAMC,KAAK,GAAGhJ,MAAM,CAACqF,IAAI,CAACsD,OAAO,CAAC;EAClC,MAAMM,KAAK,GAAGjJ,MAAM,CAACqF,IAAI,CAACuD,OAAO,CAAC;EAElC,IAAII,KAAK,CAAC1L,MAAM,KAAK2L,KAAK,CAAC3L,MAAM,EAAE;IACjC,OAAO,KAAK;;EAGd,IAAIuL,iBAAiB,CAAC/N,GAAG,CAAC6N,OAAO,CAAC,IAAIE,iBAAiB,CAAC/N,GAAG,CAAC8N,OAAO,CAAC,EAAE;IACpE,OAAO,IAAI;;EAEbC,iBAAiB,CAACzG,GAAG,CAACuG,OAAO,CAAC;EAC9BE,iBAAiB,CAACzG,GAAG,CAACwG,OAAO,CAAC;EAE9B,KAAK,MAAM7M,GAAG,IAAIiN,KAAK,EAAE;IACvB,MAAME,IAAI,GAAGP,OAAO,CAAC5M,GAAG,CAAC;IAEzB,IAAI,CAACkN,KAAK,CAACjC,QAAQ,CAACjL,GAAG,CAAC,EAAE;MACxB,OAAO,KAAK;;IAGd,IAAIA,GAAG,KAAK,KAAK,EAAE;MACjB,MAAMoN,IAAI,GAAGP,OAAO,CAAC7M,GAAG,CAAC;MAEzB,IACGnC,YAAY,CAACsP,IAAI,CAAC,IAAItP,YAAY,CAACuP,IAAI,CAAC,IACxClP,QAAQ,CAACiP,IAAI,CAAC,IAAIjP,QAAQ,CAACkP,IAAI,CAAE,IACjCjP,KAAK,CAACC,OAAO,CAAC+O,IAAI,CAAC,IAAIhP,KAAK,CAACC,OAAO,CAACgP,IAAI,CAAC,GACvC,CAACT,SAAS,CAACQ,IAAI,EAAEC,IAAI,EAAEN,iBAAiB,IACxCK,IAAI,KAAKC,IAAI,EACjB;QACA,OAAO,KAAK;;;;EAKlB,OAAO,IAAI;AACb;AClDA,IAAAC,aAAA,GAAgBvP,KAAc,IAC5BI,QAAQ,CAACJ,KAAK,CAAC,IAAI,CAACmG,MAAM,CAACqF,IAAI,CAACxL,KAAK,CAAC,CAACyD,MAAM;ACH/C,IAAA+L,WAAA,GAAgB3P,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,MAAM;ACHzB,IAAA2P,UAAA,GAAgBzP,KAAc,IAC5B,OAAOA,KAAK,KAAK,UAAU;ACC7B,IAAA0P,aAAA,GAAgB1P,KAAc,IAA0B;EACtD,IAAI,CAACwB,KAAK,EAAE;IACV,OAAO,KAAK;;EAGd,MAAMmO,KAAK,GAAG3P,KAAK,GAAKA,KAAqB,CAAC4P,aAA0B,GAAG,CAAC;EAC5E,OACE5P,KAAK,aACJ2P,KAAK,IAAIA,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACnO,WAAW,GAAGA,WAAW,CAAC;AAE9E,CAAC;ACVD,IAAAoO,gBAAA,GAAgBjQ,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,iBAAiB;ACDpC,IAAAiQ,YAAA,GAAgBlQ,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,OAAO;ACE1B,IAAAkQ,iBAAA,GAAgB/F,GAAiB,IAC/B8F,YAAY,CAAC9F,GAAG,CAAC,IAAIrK,eAAe,CAACqK,GAAG,CAAC;ACF3C,IAAAgG,IAAA,GAAgBhG,GAAQ,IAAKyF,aAAa,CAACzF,GAAG,CAAC,IAAIA,GAAG,CAACiG,WAAW;ACElE,SAASC,OAAOA,CAACnN,MAAW,EAAEoN,UAA+B;EAC3D,MAAM3M,MAAM,GAAG2M,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC5M,MAAM;EAC7C,IAAIF,KAAK,GAAG,CAAC;EAEb,OAAOA,KAAK,GAAGE,MAAM,EAAE;IACrBT,MAAM,GAAGX,WAAW,CAACW,MAAM,CAAC,GAAGO,KAAK,EAAE,GAAGP,MAAM,CAACoN,UAAU,CAAC7M,KAAK,EAAE,CAAC,CAAC;;EAGtE,OAAOP,MAAM;AACf;AAEA,SAASsN,YAAYA,CAAChF,GAAc;EAClC,KAAK,MAAMpJ,GAAG,IAAIoJ,GAAG,EAAE;IACrB,IAAIA,GAAG,CAAC/J,cAAc,CAACW,GAAG,CAAC,IAAI,CAACG,WAAW,CAACiJ,GAAG,CAACpJ,GAAG,CAAC,CAAC,EAAE;MACrD,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEc,SAAUqO,KAAKA,CAACvN,MAAW,EAAEC,IAAkC;EAC3E,MAAMuN,KAAK,GAAGnQ,KAAK,CAACC,OAAO,CAAC2C,IAAI,IAC5BA,IAAA,GACAd,KAAK,CAACc,IAAI,IACR,CAACA,IAAI,IACLN,YAAY,CAACM,IAAI,CAAC;EAExB,MAAMwN,WAAW,GAAGD,KAAK,CAAC/M,MAAM,KAAK,CAAC,GAAGT,MAAM,GAAGmN,OAAO,CAACnN,MAAM,EAAEwN,KAAK,CAAC;EAExE,MAAMjN,KAAK,GAAGiN,KAAK,CAAC/M,MAAM,GAAG,CAAC;EAC9B,MAAMvB,GAAG,GAAGsO,KAAK,CAACjN,KAAK,CAAC;EAExB,IAAIkN,WAAW,EAAE;IACf,OAAOA,WAAW,CAACvO,GAAG,CAAC;;EAGzB,IACEqB,KAAK,KAAK,CAAC,KACTnD,QAAQ,CAACqQ,WAAW,CAAC,IAAIlB,aAAa,CAACkB,WAAW,CAAC,IAClDpQ,KAAK,CAACC,OAAO,CAACmQ,WAAW,CAAC,IAAIH,YAAY,CAACG,WAAW,CAAE,CAAC,EAC5D;IACAF,KAAK,CAACvN,MAAM,EAAEwN,KAAK,CAACH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;EAGnC,OAAOrN,MAAM;AACf;ACjDA,IAAA0N,iBAAA,GAAmB7O,IAAO,IAAa;EACrC,KAAK,MAAMK,GAAG,IAAIL,IAAI,EAAE;IACtB,IAAI4N,UAAU,CAAC5N,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;MACzB,OAAO,IAAI;;;EAGf,OAAO,KAAK;AACd,CAAC;ACFD,SAASyO,eAAeA,CAAI9O,IAAO,EAAkC;EAAA,IAAhC+O,MAAA,GAAA5K,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAA8B,EAAE;EACnE,MAAM6K,iBAAiB,GAAGxQ,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAIgP,iBAAiB,EAAE;IACvC,KAAK,MAAM3O,GAAG,IAAIL,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,IACvB9B,QAAQ,CAACyB,IAAI,CAACK,GAAG,CAAC,CAAC,IAAI,CAACwO,iBAAiB,CAAC7O,IAAI,CAACK,GAAG,CAAC,CAAE,EACtD;QACA0O,MAAM,CAAC1O,GAAG,CAAC,GAAG7B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;QAChDyO,eAAe,CAAC9O,IAAI,CAACK,GAAG,CAAC,EAAE0O,MAAM,CAAC1O,GAAG,CAAC,CAAC;aAClC,IAAI,CAAChC,iBAAiB,CAAC2B,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;QACxC0O,MAAM,CAAC1O,GAAG,CAAC,GAAG,IAAI;;;;EAKxB,OAAO0O,MAAM;AACf;AAEA,SAASE,+BAA+BA,CACtCjP,IAAO,EACPuG,UAAa,EACb2I,qBAGC;EAED,MAAMF,iBAAiB,GAAGxQ,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAIgP,iBAAiB,EAAE;IACvC,KAAK,MAAM3O,GAAG,IAAIL,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,IACvB9B,QAAQ,CAACyB,IAAI,CAACK,GAAG,CAAC,CAAC,IAAI,CAACwO,iBAAiB,CAAC7O,IAAI,CAACK,GAAG,CAAC,CAAE,EACtD;QACA,IACEG,WAAW,CAAC+F,UAAU,CAAC,IACvBwG,WAAW,CAACmC,qBAAqB,CAAC7O,GAAG,CAAC,CAAC,EACvC;UACA6O,qBAAqB,CAAC7O,GAAG,CAAC,GAAG7B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,IAChDyO,eAAe,CAAC9O,IAAI,CAACK,GAAG,CAAC,EAAE,EAAE,IAC7B;YAAE,GAAGyO,eAAe,CAAC9O,IAAI,CAACK,GAAG,CAAC;UAAC,CAAE;eAChC;UACL4O,+BAA+B,CAC7BjP,IAAI,CAACK,GAAG,CAAC,EACThC,iBAAiB,CAACkI,UAAU,CAAC,GAAG,EAAE,GAAGA,UAAU,CAAClG,GAAG,CAAC,EACpD6O,qBAAqB,CAAC7O,GAAG,CAAC,CAC3B;;aAEE;QACL6O,qBAAqB,CAAC7O,GAAG,CAAC,GAAG,CAAC2M,SAAS,CAAChN,IAAI,CAACK,GAAG,CAAC,EAAEkG,UAAU,CAAClG,GAAG,CAAC,CAAC;;;;EAKzE,OAAO6O,qBAAqB;AAC9B;AAEA,IAAAC,cAAA,GAAeA,CAAI/K,aAAgB,EAAEmC,UAAa,KAChD0I,+BAA+B,CAC7B7K,aAAa,EACbmC,UAAU,EACVuI,eAAe,CAACvI,UAAU,CAAC,CAC5B;AChEH,MAAM6I,aAAa,GAAwB;EACzCjR,KAAK,EAAE,KAAK;EACZ0H,OAAO,EAAE;CACV;AAED,MAAMwJ,WAAW,GAAG;EAAElR,KAAK,EAAE,IAAI;EAAE0H,OAAO,EAAE;AAAI,CAAE;AAElD,IAAAyJ,gBAAA,GAAgBC,OAA4B,IAAyB;EACnE,IAAI/Q,KAAK,CAACC,OAAO,CAAC8Q,OAAO,CAAC,EAAE;IAC1B,IAAIA,OAAO,CAAC3N,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMsF,MAAM,GAAGqI,OAAA,CACZ3O,MAAM,CAAE4O,MAAM,IAAKA,MAAM,IAAIA,MAAM,CAAC3Q,OAAO,IAAI,CAAC2Q,MAAM,CAACxK,QAAQ,EAC/D2B,GAAG,CAAE6I,MAAM,IAAKA,MAAM,CAACrR,KAAK,CAAC;MAChC,OAAO;QAAEA,KAAK,EAAE+I,MAAM;QAAErB,OAAO,EAAE,CAAC,CAACqB,MAAM,CAACtF;MAAM,CAAE;;IAGpD,OAAO2N,OAAO,CAAC,CAAC,CAAC,CAAC1Q,OAAO,IAAI,CAAC0Q,OAAO,CAAC,CAAC,CAAC,CAACvK,QAAA;IACvC;IACEuK,OAAO,CAAC,CAAC,CAAC,CAACE,UAAU,IAAI,CAACjP,WAAW,CAAC+O,OAAO,CAAC,CAAC,CAAC,CAACE,UAAU,CAACtR,KAAK,IAC/DqC,WAAW,CAAC+O,OAAO,CAAC,CAAC,CAAC,CAACpR,KAAK,CAAC,IAAIoR,OAAO,CAAC,CAAC,CAAC,CAACpR,KAAK,KAAK,KACpDkR,WAAA,GACA;MAAElR,KAAK,EAAEoR,OAAO,CAAC,CAAC,CAAC,CAACpR,KAAK;MAAE0H,OAAO,EAAE;IAAI,IAC1CwJ,WAAA,GACFD,aAAa;;EAGnB,OAAOA,aAAa;AACtB,CAAC;AC9BD,IAAAM,eAAA,GAAeA,CACbvR,KAAQ,EAAAwR,IAAA;EAAA,IACR;IAAEC,aAAa;IAAEC,WAAW;IAAEC;EAAU,CAAe,GAAAH,IAAA;EAAA,OAEvDnP,WAAW,CAACrC,KAAK,IACbA,KAAA,GACAyR,aAAA,GACEzR,KAAK,KAAK,KACR4R,GAAA,GACA5R,KAAA,GACE,CAACA,KAAA,GACDA,KAAA,GACJ0R,WAAW,IAAIzJ,QAAQ,CAACjI,KAAK,IAC3B,IAAIC,IAAI,CAACD,KAAK,IACd2R,UAAA,GACEA,UAAU,CAAC3R,KAAK,IAChBA,KAAK;AAAA;ACfjB,MAAM6R,aAAa,GAAqB;EACtCnK,OAAO,EAAE,KAAK;EACd1H,KAAK,EAAE;CACR;AAED,IAAA8R,aAAA,GAAgBV,OAA4B,IAC1C/Q,KAAK,CAACC,OAAO,CAAC8Q,OAAO,IACjBA,OAAO,CAAChO,MAAM,CACZ,CAAC2O,QAAQ,EAAEV,MAAM,KACfA,MAAM,IAAIA,MAAM,CAAC3Q,OAAO,IAAI,CAAC2Q,MAAM,CAACxK,QAAA,GAChC;EACEa,OAAO,EAAE,IAAI;EACb1H,KAAK,EAAEqR,MAAM,CAACrR;AACf,IACD+R,QAAQ,EACdF,aAAa,IAEfA,aAAa;ACXL,SAAUG,aAAaA,CAAC3H,EAAe;EACnD,MAAMJ,GAAG,GAAGI,EAAE,CAACJ,GAAG;EAElB,IAAIuF,WAAW,CAACvF,GAAG,CAAC,EAAE;IACpB,OAAOA,GAAG,CAACgI,KAAK;;EAGlB,IAAIlC,YAAY,CAAC9F,GAAG,CAAC,EAAE;IACrB,OAAO6H,aAAa,CAACzH,EAAE,CAAC6H,IAAI,CAAC,CAAClS,KAAK;;EAGrC,IAAI8P,gBAAgB,CAAC7F,GAAG,CAAC,EAAE;IACzB,OAAO,CAAC,GAAGA,GAAG,CAACkI,eAAe,CAAC,CAAC3J,GAAG,CAAC4J,KAAA;MAAA,IAAC;QAAEpS;MAAK,CAAE,GAAAoS,KAAA;MAAA,OAAKpS,KAAK;IAAA,EAAC;;EAG3D,IAAIJ,eAAU,CAACqK,GAAG,CAAC,EAAE;IACnB,OAAOkH,gBAAgB,CAAC9G,EAAE,CAAC6H,IAAI,CAAC,CAAClS,KAAK;;EAGxC,OAAOuR,eAAe,CAAClP,WAAW,CAAC4H,GAAG,CAACjK,KAAK,CAAC,GAAGqK,EAAE,CAACJ,GAAG,CAACjK,KAAK,GAAGiK,GAAG,CAACjK,KAAK,EAAEqK,EAAE,CAAC;AAC/E;ACpBA,IAAAgI,kBAAA,GAAeA,CACbC,WAAyD,EACzDlI,OAAkB,EAClBmI,YAA2B,EAC3BC,yBAA+C,KAC7C;EACF,MAAM5B,MAAM,GAA2C,EAAE;EAEzD,KAAK,MAAMhQ,IAAI,IAAI0R,WAAW,EAAE;IAC9B,MAAMnI,KAAK,GAAUpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;IAEvCuJ,KAAK,IAAI7G,GAAG,CAACsN,MAAM,EAAEhQ,IAAI,EAAEuJ,KAAK,CAACE,EAAE,CAAC;;EAGtC,OAAO;IACLkI,YAAY;IACZvR,KAAK,EAAE,CAAC,GAAGsR,WAAW,CAA8B;IACpD1B,MAAM;IACN4B;GACD;AACH,CAAC;AC/BD,IAAAC,OAAA,GAAgBzS,KAAc,IAAsBA,KAAK,YAAY0S,MAAM;ACS3E,IAAAC,YAAA,GACEC,IAAoD,IAEpDvQ,WAAW,CAACuQ,IAAI,IACZA,IAAA,GACAH,OAAO,CAACG,IAAI,IACVA,IAAI,CAACC,MAAA,GACLzS,QAAQ,CAACwS,IAAI,IACXH,OAAO,CAACG,IAAI,CAAC5S,KAAK,IAChB4S,IAAI,CAAC5S,KAAK,CAAC6S,MAAA,GACXD,IAAI,CAAC5S,KAAA,GACP4S,IAAI;ACjBd,IAAAE,kBAAA,GAAgBC,IAAW,KAA2B;EACpDC,UAAU,EAAE,CAACD,IAAI,IAAIA,IAAI,KAAK7O,eAAe,CAACG,QAAQ;EACtD4O,QAAQ,EAAEF,IAAI,KAAK7O,eAAe,CAACC,MAAM;EACzC+O,UAAU,EAAEH,IAAI,KAAK7O,eAAe,CAACE,QAAQ;EAC7C+O,OAAO,EAAEJ,IAAI,KAAK7O,eAAe,CAACK,GAAG;EACrC6O,SAAS,EAAEL,IAAI,KAAK7O,eAAe,CAACI;AACrC,EAAC;ACLF,MAAM+O,cAAc,GAAG,eAAe;AAEtC,IAAAC,oBAAA,GAAgBC,cAA2B,IACzC,CAAC,CAACA,cAAc,IAChB,CAAC,CAACA,cAAc,CAACxO,QAAQ,IACzB,CAAC,EACE0K,UAAU,CAAC8D,cAAc,CAACxO,QAAQ,CAAC,IAClCwO,cAAc,CAACxO,QAAQ,CAAC1D,WAAW,CAACT,IAAI,KAAKyS,cAAc,IAC5DjT,QAAQ,CAACmT,cAAc,CAACxO,QAAQ,CAAC,IAChCoB,MAAM,CAAC4C,MAAM,CAACwK,cAAc,CAACxO,QAAQ,CAAC,CAACyO,IAAI,CACxCC,gBAA4C,IAC3CA,gBAAgB,CAACpS,WAAW,CAACT,IAAI,KAAKyS,cAAc,CACtD,CACL;ACfH,IAAAK,aAAA,GAAgBtC,OAAoB,IAClCA,OAAO,CAACtG,KAAK,KACZsG,OAAO,CAACtM,QAAQ,IACfsM,OAAO,CAAC1M,GAAG,IACX0M,OAAO,CAAC3M,GAAG,IACX2M,OAAO,CAACzM,SAAS,IACjByM,OAAO,CAACxM,SAAS,IACjBwM,OAAO,CAACvM,OAAO,IACfuM,OAAO,CAACrM,QAAQ,CAAC;ACRrB,IAAA4O,SAAA,GAAeA,CACb/S,IAAuB,EACvBuH,MAAa,EACbyL,WAAqB,KAErB,CAACA,WAAW,KACXzL,MAAM,CAACO,QAAQ,IACdP,MAAM,CAACG,KAAK,CAACrH,GAAG,CAACL,IAAI,CAAC,IACtB,CAAC,GAAGuH,MAAM,CAACG,KAAK,CAAC,CAAC4E,IAAI,CACnB2G,SAAS,IACRjT,IAAI,CAACkT,UAAU,CAACD,SAAS,CAAC,IAC1B,QAAQ,CAACzR,IAAI,CAACxB,IAAI,CAACyP,KAAK,CAACwD,SAAS,CAACpQ,MAAM,CAAC,CAAC,CAC9C,CAAC;ACVN,MAAMsQ,qBAAqB,GAAGA,CAC5BnD,MAAiB,EACjB5F,MAAwD,EACxDsH,WAA8D,EAC9D0B,UAAoB,KAClB;EACF,KAAK,MAAM9R,GAAG,IAAIoQ,WAAW,IAAInM,MAAM,CAACqF,IAAI,CAACoF,MAAM,CAAC,EAAE;IACpD,MAAMzG,KAAK,GAAGpH,GAAG,CAAC6N,MAAM,EAAE1O,GAAG,CAAC;IAE9B,IAAIiI,KAAK,EAAE;MACT,MAAM;QAAEE,EAAE;QAAE,GAAG4J;MAAY,CAAE,GAAG9J,KAAK;MAErC,IAAIE,EAAE,EAAE;QACN,IAAIA,EAAE,CAAC6H,IAAI,IAAI7H,EAAE,CAAC6H,IAAI,CAAC,CAAC,CAAC,IAAIlH,MAAM,CAACX,EAAE,CAAC6H,IAAI,CAAC,CAAC,CAAC,EAAEhQ,GAAG,CAAC,IAAI,CAAC8R,UAAU,EAAE;UACnE,OAAO,IAAI;eACN,IAAI3J,EAAE,CAACJ,GAAG,IAAIe,MAAM,CAACX,EAAE,CAACJ,GAAG,EAAEI,EAAE,CAACzJ,IAAI,CAAC,IAAI,CAACoT,UAAU,EAAE;UAC3D,OAAO,IAAI;eACN;UACL,IAAID,qBAAqB,CAACE,YAAY,EAAEjJ,MAAM,CAAC,EAAE;YAC/C;;;aAGC,IAAI5K,QAAQ,CAAC6T,YAAY,CAAC,EAAE;QACjC,IAAIF,qBAAqB,CAACE,YAAyB,EAAEjJ,MAAM,CAAC,EAAE;UAC5D;;;;;EAKR;AACF,CAAC;AC9Ba,SAAUkJ,iBAAiBA,CACvCvM,MAAsB,EACtByC,OAAoB,EACpBxJ,IAAY;EAKZ,MAAMmJ,KAAK,GAAGhH,GAAG,CAAC4E,MAAM,EAAE/G,IAAI,CAAC;EAE/B,IAAImJ,KAAK,IAAI5H,KAAK,CAACvB,IAAI,CAAC,EAAE;IACxB,OAAO;MACLmJ,KAAK;MACLnJ;KACD;;EAGH,MAAMI,KAAK,GAAGJ,IAAI,CAACkC,KAAK,CAAC,GAAG,CAAC;EAE7B,OAAO9B,KAAK,CAACyC,MAAM,EAAE;IACnB,MAAMgF,SAAS,GAAGzH,KAAK,CAACmT,IAAI,CAAC,GAAG,CAAC;IACjC,MAAMhK,KAAK,GAAGpH,GAAG,CAACqH,OAAO,EAAE3B,SAAS,CAAC;IACrC,MAAM2L,UAAU,GAAGrR,GAAG,CAAC4E,MAAM,EAAEc,SAAS,CAAC;IAEzC,IAAI0B,KAAK,IAAI,CAAC9J,KAAK,CAACC,OAAO,CAAC6J,KAAK,CAAC,IAAIvJ,IAAI,KAAK6H,SAAS,EAAE;MACxD,OAAO;QAAE7H;MAAI,CAAE;;IAGjB,IAAIwT,UAAU,IAAIA,UAAU,CAACtU,IAAI,EAAE;MACjC,OAAO;QACLc,IAAI,EAAE6H,SAAS;QACfsB,KAAK,EAAEqK;OACR;;IAGH,IAAIA,UAAU,IAAIA,UAAU,CAACC,IAAI,IAAID,UAAU,CAACC,IAAI,CAACvU,IAAI,EAAE;MACzD,OAAO;QACLc,IAAI,EAAE,GAAG6H,SAAS,OAAO;QACzBsB,KAAK,EAAEqK,UAAU,CAACC;OACnB;;IAGHrT,KAAK,CAACsT,GAAG,EAAE;;EAGb,OAAO;IACL1T;GACD;AACH;AC3CA,IAAA2T,qBAAA,GAAeA,CACbC,aAGC,EACDlO,eAAkB,EAClBS,eAA2D,EAC3DhB,MAAgB,KACd;EACFgB,eAAe,CAACyN,aAAa,CAAC;EAC9B,MAAM;IAAE5T,IAAI;IAAE,GAAGgF;EAAS,CAAE,GAAG4O,aAAa;EAE5C,OACEjF,aAAa,CAAC3J,SAAS,CAAC,IACxBO,MAAM,CAACqF,IAAI,CAAC5F,SAAS,CAAC,CAACnC,MAAM,IAAI0C,MAAM,CAACqF,IAAI,CAAClF,eAAe,CAAC,CAAC7C,MAAM,IACpE0C,MAAM,CAACqF,IAAI,CAAC5F,SAAS,CAAC,CAAC4N,IAAI,CACxBtR,GAAG,IACFoE,eAAe,CAACpE,GAA0B,CAAC,MAC1C,CAAC6D,MAAM,IAAI7B,eAAe,CAACK,GAAG,CAAC,CACnC;AAEL,CAAC;AC5BD,IAAAkQ,qBAAA,GAAeA,CACb7T,IAAQ,EACR8T,UAAmB,EACnB5N,KAAe,KAEf,CAAClG,IAAI,IACL,CAAC8T,UAAU,IACX9T,IAAI,KAAK8T,UAAU,IACnBvG,qBAAqB,CAACvN,IAAI,CAAC,CAACsM,IAAI,CAC7ByH,WAAW,IACVA,WAAW,KACV7N,KAAA,GACG6N,WAAW,KAAKD,UAAA,GAChBC,WAAW,CAACb,UAAU,CAACY,UAAU,CAAC,IAClCA,UAAU,CAACZ,UAAU,CAACa,WAAW,CAAC,CAAC,CAC1C;ACfH,IAAAC,cAAA,GAAeA,CACbhB,WAAoB,EACpB9J,SAAkB,EAClB+K,WAAoB,EACpBC,cAGC,EACD/B,IAAkC,KAChC;EACF,IAAIA,IAAI,CAACI,OAAO,EAAE;IAChB,OAAO,KAAK;SACP,IAAI,CAAC0B,WAAW,IAAI9B,IAAI,CAACK,SAAS,EAAE;IACzC,OAAO,EAAEtJ,SAAS,IAAI8J,WAAW,CAAC;SAC7B,IAAIiB,WAAW,GAAGC,cAAc,CAAC7B,QAAQ,GAAGF,IAAI,CAACE,QAAQ,EAAE;IAChE,OAAO,CAACW,WAAW;SACd,IAAIiB,WAAW,GAAGC,cAAc,CAAC5B,UAAU,GAAGH,IAAI,CAACG,UAAU,EAAE;IACpE,OAAOU,WAAW;;EAEpB,OAAO,IAAI;AACb,CAAC;AClBD,IAAAmB,eAAA,GAAeA,CAAI9K,GAAM,EAAErJ,IAAY,KACrC,CAAC4B,OAAO,CAACO,GAAG,CAACkH,GAAG,EAAErJ,IAAI,CAAC,CAAC,CAAC6C,MAAM,IAAI8M,KAAK,CAACtG,GAAG,EAAErJ,IAAI,CAAC;ACKrD,IAAAoU,yBAAA,GAAeA,CACbrN,MAAsB,EACtBoC,KAA0C,EAC1CnJ,IAAuB,KACL;EAClB,MAAMqU,gBAAgB,GAAG9G,qBAAqB,CAACpL,GAAG,CAAC4E,MAAM,EAAE/G,IAAI,CAAC,CAAC;EACjE0C,GAAG,CAAC2R,gBAAgB,EAAE,MAAM,EAAElL,KAAK,CAACnJ,IAAI,CAAC,CAAC;EAC1C0C,GAAG,CAACqE,MAAM,EAAE/G,IAAI,EAAEqU,gBAAgB,CAAC;EACnC,OAAOtN,MAAM;AACf,CAAC;AChBD,IAAAuN,SAAA,GAAgBlV,KAAc,IAAuBiI,QAAQ,CAACjI,KAAK,CAAC;ACCtD,SAAUmV,gBAAgBA,CACtChS,MAAsB,EACtB8G,GAAQ,EACS;EAAA,IAAjBnK,IAAI,GAAAkG,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,UAAU;EAEjB,IACEkP,SAAS,CAAC/R,MAAM,CAAC,IAChB9C,KAAK,CAACC,OAAO,CAAC6C,MAAM,CAAC,IAAIA,MAAM,CAACiS,KAAK,CAACF,SAAS,CAAE,IACjD7R,SAAS,CAACF,MAAM,CAAC,IAAI,CAACA,MAAO,EAC9B;IACA,OAAO;MACLrD,IAAI;MACJ2K,OAAO,EAAEyK,SAAS,CAAC/R,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE;MACxC8G;KACD;;AAEL;AChBA,IAAAoL,kBAAA,GAAgBC,cAA+B,IAC7ClV,QAAQ,CAACkV,cAAc,CAAC,IAAI,CAAC7C,OAAO,CAAC6C,cAAc,IAC/CA,cAAA,GACA;EACEtV,KAAK,EAAEsV,cAAc;EACrB7K,OAAO,EAAE;CACV;ACuBP,IAAA8K,aAAA,GAAe,MAAAA,CACbpL,KAAY,EACZqL,kBAAmC,EACnCpN,UAAa,EACb6F,wBAAiC,EACjCuE,yBAAmC,EACnCiD,YAAsB,KACU;EAChC,MAAM;IACJxL,GAAG;IACHiI,IAAI;IACJpN,QAAQ;IACRH,SAAS;IACTC,SAAS;IACTF,GAAG;IACHD,GAAG;IACHI,OAAO;IACPE,QAAQ;IACRnE,IAAI;IACJ6Q,aAAa;IACb3G;EAAK,CACN,GAAGX,KAAK,CAACE,EAAE;EACZ,MAAMqL,UAAU,GAAqB3S,GAAG,CAACqF,UAAU,EAAExH,IAAI,CAAC;EAC1D,IAAI,CAACkK,KAAK,IAAI0K,kBAAkB,CAACvU,GAAG,CAACL,IAAI,CAAC,EAAE;IAC1C,OAAO,EAAE;;EAEX,MAAM+U,QAAQ,GAAqBzD,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAIjI,GAAwB;EAC7E,MAAMO,iBAAiB,GAAIC,OAA0B,IAAI;IACvD,IAAI+H,yBAAyB,IAAImD,QAAQ,CAACjL,cAAc,EAAE;MACxDiL,QAAQ,CAACnL,iBAAiB,CAACnH,SAAS,CAACoH,OAAO,CAAC,GAAG,EAAE,GAAGA,OAAO,IAAI,EAAE,CAAC;MACnEkL,QAAQ,CAACjL,cAAc,EAAE;;EAE7B,CAAC;EACD,MAAMX,KAAK,GAAwB,EAAE;EACrC,MAAM6L,OAAO,GAAG7F,YAAY,CAAC9F,GAAG,CAAC;EACjC,MAAM4L,UAAU,GAAGjW,eAAe,CAACqK,GAAG,CAAC;EACvC,MAAM+F,iBAAiB,GAAG4F,OAAO,IAAIC,UAAU;EAC/C,MAAMC,OAAO,GACV,CAACrE,aAAa,IAAIjC,WAAW,CAACvF,GAAG,CAAC,KACjC5H,WAAW,CAAC4H,GAAG,CAACjK,KAAK,CAAC,IACtBqC,WAAW,CAACqT,UAAU,CAAC,IACxBhG,aAAa,CAACzF,GAAG,CAAC,IAAIA,GAAG,CAACjK,KAAK,KAAK,EAAG,IACxC0V,UAAU,KAAK,EAAE,IAChBrV,KAAK,CAACC,OAAO,CAACoV,UAAU,CAAC,IAAI,CAACA,UAAU,CAACjS,MAAO;EACnD,MAAMsS,iBAAiB,GAAG/H,YAAY,CAACgI,IAAI,CACzC,IAAI,EACJpV,IAAI,EACJqN,wBAAwB,EACxBlE,KAAK,CACN;EACD,MAAMkM,gBAAgB,GAAG,SAAAA,CACvBC,SAAkB,EAClBC,gBAAyB,EACzBC,gBAAyB,EAGvB;IAAA,IAFFC,OAAA,GAAArQ,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAmBxB,sBAAsB,CAACG,SAAS;IAAA,IACnD2R,OAAA,GAAAtQ,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAmBxB,sBAAsB,CAACI,SAAS;IAEnD,MAAM6F,OAAO,GAAGyL,SAAS,GAAGC,gBAAgB,GAAGC,gBAAgB;IAC/DrM,KAAK,CAACnJ,IAAI,CAAC,GAAG;MACZd,IAAI,EAAEoW,SAAS,GAAGG,OAAO,GAAGC,OAAO;MACnC7L,OAAO;MACPR,GAAG;MACH,GAAG8L,iBAAiB,CAACG,SAAS,GAAGG,OAAO,GAAGC,OAAO,EAAE7L,OAAO;KAC5D;EACH,CAAC;EAED,IACEgL,YAAA,GACI,CAACpV,KAAK,CAACC,OAAO,CAACoV,UAAU,CAAC,IAAI,CAACA,UAAU,CAACjS,MAAA,GAC1CqB,QAAQ,KACN,CAACkL,iBAAiB,KAAK8F,OAAO,IAAI5V,iBAAiB,CAACwV,UAAU,CAAC,CAAC,IAC/DrS,SAAS,CAACqS,UAAU,CAAC,IAAI,CAACA,UAAW,IACrCG,UAAU,IAAI,CAAC1E,gBAAgB,CAACe,IAAI,CAAC,CAACxK,OAAQ,IAC9CkO,OAAO,IAAI,CAAC9D,aAAa,CAACI,IAAI,CAAC,CAACxK,OAAQ,CAAC,EAChD;IACA,MAAM;MAAE1H,KAAK;MAAEyK;IAAO,CAAE,GAAGyK,SAAS,CAACpQ,QAAQ,IACzC;MAAE9E,KAAK,EAAE,CAAC,CAAC8E,QAAQ;MAAE2F,OAAO,EAAE3F;IAAQ,IACtCuQ,kBAAkB,CAACvQ,QAAQ,CAAC;IAEhC,IAAI9E,KAAK,EAAE;MACT+J,KAAK,CAACnJ,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE0E,sBAAsB,CAACM,QAAQ;QACrC2F,OAAO;QACPR,GAAG,EAAE0L,QAAQ;QACb,GAAGI,iBAAiB,CAACvR,sBAAsB,CAACM,QAAQ,EAAE2F,OAAO;OAC9D;MACD,IAAI,CAACwD,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOV,KAAK;;;;EAKlB,IAAI,CAAC+L,OAAO,KAAK,CAAC5V,iBAAiB,CAACwE,GAAG,CAAC,IAAI,CAACxE,iBAAiB,CAACuE,GAAG,CAAC,CAAC,EAAE;IACpE,IAAIyR,SAAS;IACb,IAAIK,SAAS;IACb,MAAMC,SAAS,GAAGnB,kBAAkB,CAAC5Q,GAAG,CAAC;IACzC,MAAMgS,SAAS,GAAGpB,kBAAkB,CAAC3Q,GAAG,CAAC;IAEzC,IAAI,CAACxE,iBAAiB,CAACwV,UAAU,CAAC,IAAI,CAAC7R,KAAK,CAAC6R,UAAoB,CAAC,EAAE;MAClE,MAAMgB,WAAW,GACdzM,GAAwB,CAACwH,aAAa,KACtCiE,UAAU,GAAG,CAACA,UAAU,GAAGA,UAAU,CAAC;MACzC,IAAI,CAACxV,iBAAiB,CAACsW,SAAS,CAACxW,KAAK,CAAC,EAAE;QACvCkW,SAAS,GAAGQ,WAAW,GAAGF,SAAS,CAACxW,KAAK;;MAE3C,IAAI,CAACE,iBAAiB,CAACuW,SAAS,CAACzW,KAAK,CAAC,EAAE;QACvCuW,SAAS,GAAGG,WAAW,GAAGD,SAAS,CAACzW,KAAK;;WAEtC;MACL,MAAM2W,SAAS,GACZ1M,GAAwB,CAACyH,WAAW,IAAI,IAAIzR,IAAI,CAACyV,UAAoB,CAAC;MACzE,MAAMkB,iBAAiB,GAAIC,IAAa,IACtC,IAAI5W,IAAI,CAAC,IAAIA,IAAI,EAAE,CAAC6W,YAAY,EAAE,GAAG,GAAG,GAAGD,IAAI,CAAC;MAClD,MAAME,MAAM,GAAG9M,GAAG,CAACnK,IAAI,IAAI,MAAM;MACjC,MAAMkX,MAAM,GAAG/M,GAAG,CAACnK,IAAI,IAAI,MAAM;MAEjC,IAAImI,QAAQ,CAACuO,SAAS,CAACxW,KAAK,CAAC,IAAI0V,UAAU,EAAE;QAC3CQ,SAAS,GAAGa,MAAA,GACRH,iBAAiB,CAAClB,UAAU,CAAC,GAAGkB,iBAAiB,CAACJ,SAAS,CAACxW,KAAK,IACjEgX,MAAA,GACEtB,UAAU,GAAGc,SAAS,CAACxW,KAAA,GACvB2W,SAAS,GAAG,IAAI1W,IAAI,CAACuW,SAAS,CAACxW,KAAK,CAAC;;MAG7C,IAAIiI,QAAQ,CAACwO,SAAS,CAACzW,KAAK,CAAC,IAAI0V,UAAU,EAAE;QAC3Ca,SAAS,GAAGQ,MAAA,GACRH,iBAAiB,CAAClB,UAAU,CAAC,GAAGkB,iBAAiB,CAACH,SAAS,CAACzW,KAAK,IACjEgX,MAAA,GACEtB,UAAU,GAAGe,SAAS,CAACzW,KAAA,GACvB2W,SAAS,GAAG,IAAI1W,IAAI,CAACwW,SAAS,CAACzW,KAAK,CAAC;;;IAI/C,IAAIkW,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACd,CAAC,CAACC,SAAS,EACXM,SAAS,CAAC/L,OAAO,EACjBgM,SAAS,CAAChM,OAAO,EACjBjG,sBAAsB,CAACC,GAAG,EAC1BD,sBAAsB,CAACE,GAAG,CAC3B;MACD,IAAI,CAACuJ,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACT,KAAK,CAACnJ,IAAI,CAAE,CAAC6J,OAAO,CAAC;QACvC,OAAOV,KAAK;;;;EAKlB,IACE,CAACpF,SAAS,IAAIC,SAAS,KACvB,CAACkR,OAAO,KACP7N,QAAQ,CAACyN,UAAU,CAAC,IAAKD,YAAY,IAAIpV,KAAK,CAACC,OAAO,CAACoV,UAAU,CAAE,CAAC,EACrE;IACA,MAAMuB,eAAe,GAAG5B,kBAAkB,CAAC1Q,SAAS,CAAC;IACrD,MAAMuS,eAAe,GAAG7B,kBAAkB,CAACzQ,SAAS,CAAC;IACrD,MAAMsR,SAAS,GACb,CAAChW,iBAAiB,CAAC+W,eAAe,CAACjX,KAAK,CAAC,IACzC0V,UAAU,CAACjS,MAAM,GAAG,CAACwT,eAAe,CAACjX,KAAK;IAC5C,MAAMuW,SAAS,GACb,CAACrW,iBAAiB,CAACgX,eAAe,CAAClX,KAAK,CAAC,IACzC0V,UAAU,CAACjS,MAAM,GAAG,CAACyT,eAAe,CAAClX,KAAK;IAE5C,IAAIkW,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACdC,SAAS,EACTe,eAAe,CAACxM,OAAO,EACvByM,eAAe,CAACzM,OAAO,CACxB;MACD,IAAI,CAACwD,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACT,KAAK,CAACnJ,IAAI,CAAE,CAAC6J,OAAO,CAAC;QACvC,OAAOV,KAAK;;;;EAKlB,IAAIlF,OAAO,IAAI,CAACiR,OAAO,IAAI7N,QAAQ,CAACyN,UAAU,CAAC,EAAE;IAC/C,MAAM;MAAE1V,KAAK,EAAEmX,YAAY;MAAE1M;IAAO,CAAE,GAAG4K,kBAAkB,CAACxQ,OAAO,CAAC;IAEpE,IAAI4N,OAAO,CAAC0E,YAAY,CAAC,IAAI,CAACzB,UAAU,CAAC0B,KAAK,CAACD,YAAY,CAAC,EAAE;MAC5DpN,KAAK,CAACnJ,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE0E,sBAAsB,CAACK,OAAO;QACpC4F,OAAO;QACPR,GAAG;QACH,GAAG8L,iBAAiB,CAACvR,sBAAsB,CAACK,OAAO,EAAE4F,OAAO;OAC7D;MACD,IAAI,CAACwD,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOV,KAAK;;;;EAKlB,IAAIhF,QAAQ,EAAE;IACZ,IAAI0K,UAAU,CAAC1K,QAAQ,CAAC,EAAE;MACxB,MAAM5B,MAAM,GAAG,MAAM4B,QAAQ,CAAC2Q,UAAU,EAAEtN,UAAU,CAAC;MACrD,MAAMiP,aAAa,GAAGlC,gBAAgB,CAAChS,MAAM,EAAEwS,QAAQ,CAAC;MAExD,IAAI0B,aAAa,EAAE;QACjBtN,KAAK,CAACnJ,IAAI,CAAC,GAAG;UACZ,GAAGyW,aAAa;UAChB,GAAGtB,iBAAiB,CAClBvR,sBAAsB,CAACO,QAAQ,EAC/BsS,aAAa,CAAC5M,OAAO;SAExB;QACD,IAAI,CAACwD,wBAAwB,EAAE;UAC7BzD,iBAAiB,CAAC6M,aAAa,CAAC5M,OAAO,CAAC;UACxC,OAAOV,KAAK;;;WAGX,IAAI3J,QAAQ,CAAC2E,QAAQ,CAAC,EAAE;MAC7B,IAAIuS,gBAAgB,GAAG,EAAgB;MAEvC,KAAK,MAAMpV,GAAG,IAAI6C,QAAQ,EAAE;QAC1B,IAAI,CAACwK,aAAa,CAAC+H,gBAAgB,CAAC,IAAI,CAACrJ,wBAAwB,EAAE;UACjE;;QAGF,MAAMoJ,aAAa,GAAGlC,gBAAgB,CACpC,MAAMpQ,QAAQ,CAAC7C,GAAG,CAAC,CAACwT,UAAU,EAAEtN,UAAU,CAAC,EAC3CuN,QAAQ,EACRzT,GAAG,CACJ;QAED,IAAImV,aAAa,EAAE;UACjBC,gBAAgB,GAAG;YACjB,GAAGD,aAAa;YAChB,GAAGtB,iBAAiB,CAAC7T,GAAG,EAAEmV,aAAa,CAAC5M,OAAO;WAChD;UAEDD,iBAAiB,CAAC6M,aAAa,CAAC5M,OAAO,CAAC;UAExC,IAAIwD,wBAAwB,EAAE;YAC5BlE,KAAK,CAACnJ,IAAI,CAAC,GAAG0W,gBAAgB;;;;MAKpC,IAAI,CAAC/H,aAAa,CAAC+H,gBAAgB,CAAC,EAAE;QACpCvN,KAAK,CAACnJ,IAAI,CAAC,GAAG;UACZqJ,GAAG,EAAE0L,QAAQ;UACb,GAAG2B;SACJ;QACD,IAAI,CAACrJ,wBAAwB,EAAE;UAC7B,OAAOlE,KAAK;;;;;EAMpBS,iBAAiB,CAAC,IAAI,CAAC;EACvB,OAAOT,KAAK;AACd,CAAC;ACpMD,MAAMwN,cAAc,GAAG;EACrBxE,IAAI,EAAE7O,eAAe,CAACG,QAAQ;EAC9ByQ,cAAc,EAAE5Q,eAAe,CAACE,QAAQ;EACxCoT,gBAAgB,EAAE;CACV;AAEJ,SAAUC,iBAAiBA,CAAA,EAKqC;EAAA,IAApElS,KAAA,GAAAS,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAkE,EAAE;EAUpE,IAAI4E,QAAQ,GAAG;IACb,GAAG2M,cAAc;IACjB,GAAGhS;GACJ;EACD,IAAI0B,UAAU,GAA4B;IACxCyQ,WAAW,EAAE,CAAC;IACdtQ,OAAO,EAAE,KAAK;IACduQ,OAAO,EAAE,KAAK;IACdtQ,SAAS,EAAEoI,UAAU,CAAC7E,QAAQ,CAAC3E,aAAa,CAAC;IAC7CwB,YAAY,EAAE,KAAK;IACnBoN,WAAW,EAAE,KAAK;IAClB+C,YAAY,EAAE,KAAK;IACnBhK,kBAAkB,EAAE,KAAK;IACzBlG,OAAO,EAAE,KAAK;IACdH,aAAa,EAAE,EAAE;IACjBD,WAAW,EAAE,EAAE;IACfE,gBAAgB,EAAE,EAAE;IACpBG,MAAM,EAAEiD,QAAQ,CAACjD,MAAM,IAAI,EAAE;IAC7Bd,QAAQ,EAAE+D,QAAQ,CAAC/D,QAAQ,IAAI;GAChC;EACD,IAAIuD,OAAO,GAAc,EAAE;EAC3B,IAAIlE,cAAc,GAChB9F,QAAQ,CAACwK,QAAQ,CAAC3E,aAAa,CAAC,IAAI7F,QAAQ,CAACwK,QAAQ,CAAC7B,MAAM,IACxDnH,WAAW,CAACgJ,QAAQ,CAAC3E,aAAa,IAAI2E,QAAQ,CAAC7B,MAAM,CAAC,IAAI,KAC1D,EAAE;EACR,IAAIC,WAAW,GAAG4B,QAAQ,CAACzB,gBAAA,GACtB,KACAvH,WAAW,CAACsE,cAAc,CAAkB;EACjD,IAAI6E,MAAM,GAAG;IACXC,MAAM,EAAE,KAAK;IACbF,KAAK,EAAE,KAAK;IACZxC,KAAK,EAAE;GACR;EACD,IAAIH,MAAM,GAAU;IAClB2C,KAAK,EAAE,IAAI+M,GAAG,EAAE;IAChBhR,QAAQ,EAAE,IAAIgR,GAAG,EAAE;IACnBC,OAAO,EAAE,IAAID,GAAG,EAAE;IAClBxO,KAAK,EAAE,IAAIwO,GAAG,EAAE;IAChBvP,KAAK,EAAE,IAAIuP,GAAG;GACf;EACD,IAAIE,kBAAwC;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,MAAM1R,eAAe,GAAkB;IACrCc,OAAO,EAAE,KAAK;IACdE,WAAW,EAAE,KAAK;IAClBE,gBAAgB,EAAE,KAAK;IACvBD,aAAa,EAAE,KAAK;IACpBE,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;GACT;EACD,IAAIsQ,wBAAwB,GAAG;IAC7B,GAAG3R;GACJ;EACD,MAAMmH,SAAS,GAA2B;IACxCpE,KAAK,EAAE+E,aAAa,EAAE;IACtBV,KAAK,EAAEU,aAAa;GACrB;EAED,MAAM8J,gCAAgC,GACpCtN,QAAQ,CAAC2H,YAAY,KAAKrO,eAAe,CAACK,GAAG;EAE/C,MAAM4T,QAAQ,GACSrQ,QAAW,IAC/BsQ,IAAY,IAAI;IACfC,YAAY,CAACL,KAAK,CAAC;IACnBA,KAAK,GAAGM,UAAU,CAACxQ,QAAQ,EAAEsQ,IAAI,CAAC;EACpC,CAAC;EAEH,MAAMrQ,SAAS,GAAG,MAAOwQ,iBAA2B,IAAI;IACtD,IACE,CAAC3N,QAAQ,CAAC/D,QAAQ,KACjBP,eAAe,CAACoB,OAAO,IACtBuQ,wBAAwB,CAACvQ,OAAO,IAChC6Q,iBAAiB,CAAC,EACpB;MACA,MAAM7Q,OAAO,GAAGkD,QAAQ,CAAC4N,QAAA,GACrBjJ,aAAa,CAAC,CAAC,MAAMkJ,UAAU,EAAE,EAAE9Q,MAAM,IACzC,MAAM+Q,wBAAwB,CAACtO,OAAO,EAAE,IAAI,CAAC;MAEjD,IAAI1C,OAAO,KAAKT,UAAU,CAACS,OAAO,EAAE;QAClC+F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UACnBjG;QACD,EAAC;;;EAGR,CAAC;EAED,MAAMiR,mBAAmB,GAAGA,CAAC3X,KAAgB,EAAEyG,YAAsB,KAAI;IACvE,IACE,CAACmD,QAAQ,CAAC/D,QAAQ,KACjBP,eAAe,CAACmB,YAAY,IAC3BnB,eAAe,CAACkB,gBAAgB,IAChCyQ,wBAAwB,CAACxQ,YAAY,IACrCwQ,wBAAwB,CAACzQ,gBAAgB,CAAC,EAC5C;MACA,CAACxG,KAAK,IAAIX,KAAK,CAACuY,IAAI,CAACzQ,MAAM,CAAC2C,KAAK,CAAC,EAAE+N,OAAO,CAAEjY,IAAI,IAAI;QACnD,IAAIA,IAAI,EAAE;UACR6G,YAAA,GACInE,GAAG,CAAC2D,UAAU,CAACO,gBAAgB,EAAE5G,IAAI,EAAE6G,YAAY,IACnD8I,KAAK,CAACtJ,UAAU,CAACO,gBAAgB,EAAE5G,IAAI,CAAC;;MAEhD,CAAC,CAAC;MAEF6M,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnBnG,gBAAgB,EAAEP,UAAU,CAACO,gBAAgB;QAC7CC,YAAY,EAAE,CAAC8H,aAAa,CAACtI,UAAU,CAACO,gBAAgB;MACzD,EAAC;;EAEN,CAAC;EAED,MAAMsR,cAAc,GAA0B,SAAAA,CAC5ClY,IAAI,EAMF;IAAA,IALFmI,MAAM,GAAA/C,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,EAAE;IAAA,IACX+F,MAAM,GAAA/F,SAAA,CAAAvC,MAAA,OAAAuC,SAAA,MAAAzD,SAAA;IAAA,IACNwW,IAAI,GAAA/S,SAAA,CAAAvC,MAAA,OAAAuC,SAAA,MAAAzD,SAAA;IAAA,IACJyW,eAAe,GAAAhT,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,IAAI;IAAA,IACtBiT,0BAA0B,GAAAjT,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,IAAI;IAEjC,IAAI+S,IAAI,IAAIhN,MAAM,IAAI,CAACnB,QAAQ,CAAC/D,QAAQ,EAAE;MACxCkE,MAAM,CAACC,MAAM,GAAG,IAAI;MACpB,IAAIiO,0BAA0B,IAAI5Y,KAAK,CAACC,OAAO,CAACyC,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC,CAAC,EAAE;QACnE,MAAMsY,WAAW,GAAGnN,MAAM,CAAChJ,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC,EAAEmY,IAAI,CAACI,IAAI,EAAEJ,IAAI,CAACK,IAAI,CAAC;QACpEJ,eAAe,IAAI1V,GAAG,CAAC8G,OAAO,EAAExJ,IAAI,EAAEsY,WAAW,CAAC;;MAGpD,IACED,0BAA0B,IAC1B5Y,KAAK,CAACC,OAAO,CAACyC,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC,CAAC,EAC3C;QACA,MAAM+G,MAAM,GAAGoE,MAAM,CACnBhJ,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC,EAC5BmY,IAAI,CAACI,IAAI,EACTJ,IAAI,CAACK,IAAI,CACV;QACDJ,eAAe,IAAI1V,GAAG,CAAC2D,UAAU,CAACU,MAAM,EAAE/G,IAAI,EAAE+G,MAAM,CAAC;QACvDoN,eAAe,CAAC9N,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC;;MAG1C,IACE,CAAC0F,eAAe,CAACiB,aAAa,IAC5B0Q,wBAAwB,CAAC1Q,aAAa,KACxC0R,0BAA0B,IAC1B5Y,KAAK,CAACC,OAAO,CAACyC,GAAG,CAACkE,UAAU,CAACM,aAAa,EAAE3G,IAAI,CAAC,CAAC,EAClD;QACA,MAAM2G,aAAa,GAAGwE,MAAM,CAC1BhJ,GAAG,CAACkE,UAAU,CAACM,aAAa,EAAE3G,IAAI,CAAC,EACnCmY,IAAI,CAACI,IAAI,EACTJ,IAAI,CAACK,IAAI,CACV;QACDJ,eAAe,IAAI1V,GAAG,CAAC2D,UAAU,CAACM,aAAa,EAAE3G,IAAI,EAAE2G,aAAa,CAAC;;MAGvE,IAAIjB,eAAe,CAACgB,WAAW,IAAI2Q,wBAAwB,CAAC3Q,WAAW,EAAE;QACvEL,UAAU,CAACK,WAAW,GAAG0J,cAAc,CAAC9K,cAAc,EAAE8C,WAAW,CAAC;;MAGtEyE,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnB/M,IAAI;QACJwG,OAAO,EAAEiS,SAAS,CAACzY,IAAI,EAAEmI,MAAM,CAAC;QAChCzB,WAAW,EAAEL,UAAU,CAACK,WAAW;QACnCK,MAAM,EAAEV,UAAU,CAACU,MAAM;QACzBD,OAAO,EAAET,UAAU,CAACS;MACrB,EAAC;WACG;MACLpE,GAAG,CAAC0F,WAAW,EAAEpI,IAAI,EAAEmI,MAAM,CAAC;;EAElC,CAAC;EAED,MAAMuQ,YAAY,GAAGA,CAAC1Y,IAAuB,EAAEmJ,KAAiB,KAAI;IAClEzG,GAAG,CAAC2D,UAAU,CAACU,MAAM,EAAE/G,IAAI,EAAEmJ,KAAK,CAAC;IACnC0D,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBhG,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;EACJ,CAAC;EAED,MAAM4R,UAAU,GAAI5R,MAAiC,IAAI;IACvDV,UAAU,CAACU,MAAM,GAAGA,MAAM;IAC1B8F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBhG,MAAM,EAAEV,UAAU,CAACU,MAAM;MACzBD,OAAO,EAAE;IACV,EAAC;EACJ,CAAC;EAED,MAAM8R,mBAAmB,GAAGA,CAC1B5Y,IAAuB,EACvB6Y,oBAA6B,EAC7BzZ,KAAe,EACfiK,GAAS,KACP;IACF,MAAME,KAAK,GAAUpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;IAEvC,IAAIuJ,KAAK,EAAE;MACT,MAAMjH,YAAY,GAAGH,GAAG,CACtBiG,WAAW,EACXpI,IAAI,EACJyB,WAAW,CAACrC,KAAK,CAAC,GAAG+C,GAAG,CAACmD,cAAc,EAAEtF,IAAI,CAAC,GAAGZ,KAAK,CACvD;MAEDqC,WAAW,CAACa,YAAY,CAAC,IACxB+G,GAAG,IAAKA,GAAwB,CAACyP,cAAe,IACjDD,oBAAA,GACInW,GAAG,CACD0F,WAAW,EACXpI,IAAI,EACJ6Y,oBAAoB,GAAGvW,YAAY,GAAG8O,aAAa,CAAC7H,KAAK,CAACE,EAAE,CAAC,IAE/DsP,aAAa,CAAC/Y,IAAI,EAAEsC,YAAY,CAAC;MAErC6H,MAAM,CAACD,KAAK,IAAI/C,SAAS,EAAE;;EAE/B,CAAC;EAED,MAAM6R,mBAAmB,GAAGA,CAC1BhZ,IAAuB,EACvBiZ,UAAmB,EACnBjG,WAAqB,EACrBkG,WAAqB,EACrBC,YAAsB,KAGpB;IACF,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,eAAe,GAAG,KAAK;IAC3B,MAAM1O,MAAM,GAAwD;MAClE3K;KACD;IAED,IAAI,CAACgK,QAAQ,CAAC/D,QAAQ,EAAE;MACtB,IAAI,CAAC+M,WAAW,IAAIkG,WAAW,EAAE;QAC/B,IAAIxT,eAAe,CAACc,OAAO,IAAI6Q,wBAAwB,CAAC7Q,OAAO,EAAE;UAC/D6S,eAAe,GAAGhT,UAAU,CAACG,OAAO;UACpCH,UAAU,CAACG,OAAO,GAAGmE,MAAM,CAACnE,OAAO,GAAGiS,SAAS,EAAE;UACjDW,iBAAiB,GAAGC,eAAe,KAAK1O,MAAM,CAACnE,OAAO;;QAGxD,MAAM8S,sBAAsB,GAAGrL,SAAS,CACtC9L,GAAG,CAACmD,cAAc,EAAEtF,IAAI,CAAC,EACzBiZ,UAAU,CACX;QAEDI,eAAe,GAAG,CAAC,CAAClX,GAAG,CAACkE,UAAU,CAACK,WAAW,EAAE1G,IAAI,CAAC;QACrDsZ,sBAAA,GACI3J,KAAK,CAACtJ,UAAU,CAACK,WAAW,EAAE1G,IAAI,IAClC0C,GAAG,CAAC2D,UAAU,CAACK,WAAW,EAAE1G,IAAI,EAAE,IAAI,CAAC;QAC3C2K,MAAM,CAACjE,WAAW,GAAGL,UAAU,CAACK,WAAW;QAC3C0S,iBAAiB,GACfA,iBAAiB,IAChB,CAAC1T,eAAe,CAACgB,WAAW,IAC3B2Q,wBAAwB,CAAC3Q,WAAW,KACpC2S,eAAe,KAAK,CAACC,sBAAuB;;MAGlD,IAAItG,WAAW,EAAE;QACf,MAAMuG,sBAAsB,GAAGpX,GAAG,CAACkE,UAAU,CAACM,aAAa,EAAE3G,IAAI,CAAC;QAElE,IAAI,CAACuZ,sBAAsB,EAAE;UAC3B7W,GAAG,CAAC2D,UAAU,CAACM,aAAa,EAAE3G,IAAI,EAAEgT,WAAW,CAAC;UAChDrI,MAAM,CAAChE,aAAa,GAAGN,UAAU,CAACM,aAAa;UAC/CyS,iBAAiB,GACfA,iBAAiB,IAChB,CAAC1T,eAAe,CAACiB,aAAa,IAC7B0Q,wBAAwB,CAAC1Q,aAAa,KACtC4S,sBAAsB,KAAKvG,WAAY;;;MAI/CoG,iBAAiB,IAAID,YAAY,IAAItM,SAAS,CAACC,KAAK,CAACC,IAAI,CAACpC,MAAM,CAAC;;IAGnE,OAAOyO,iBAAiB,GAAGzO,MAAM,GAAG,EAAE;EACxC,CAAC;EAED,MAAM6O,mBAAmB,GAAGA,CAC1BxZ,IAAuB,EACvB8G,OAAiB,EACjBqC,KAAkB,EAClBL,UAIC,KACC;IACF,MAAM2Q,kBAAkB,GAAGtX,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC;IACvD,MAAM2X,iBAAiB,GACrB,CAACjS,eAAe,CAACoB,OAAO,IAAIuQ,wBAAwB,CAACvQ,OAAO,KAC5DrE,SAAS,CAACqE,OAAO,CAAC,IAClBT,UAAU,CAACS,OAAO,KAAKA,OAAO;IAEhC,IAAIkD,QAAQ,CAAC0P,UAAU,IAAIvQ,KAAK,EAAE;MAChCgO,kBAAkB,GAAGI,QAAQ,CAAC,MAAMmB,YAAY,CAAC1Y,IAAI,EAAEmJ,KAAK,CAAC,CAAC;MAC9DgO,kBAAkB,CAACnN,QAAQ,CAAC0P,UAAU,CAAC;WAClC;MACLjC,YAAY,CAACL,KAAK,CAAC;MACnBD,kBAAkB,GAAG,IAAI;MACzBhO,KAAA,GACIzG,GAAG,CAAC2D,UAAU,CAACU,MAAM,EAAE/G,IAAI,EAAEmJ,KAAK,IAClCwG,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC;;IAGpC,IACE,CAACmJ,KAAK,GAAG,CAAC8E,SAAS,CAACwL,kBAAkB,EAAEtQ,KAAK,CAAC,GAAGsQ,kBAAkB,KACnE,CAAC9K,aAAa,CAAC7F,UAAU,CAAC,IAC1B6O,iBAAiB,EACjB;MACA,MAAMgC,gBAAgB,GAAG;QACvB,GAAG7Q,UAAU;QACb,IAAI6O,iBAAiB,IAAIlV,SAAS,CAACqE,OAAO,CAAC,GAAG;UAAEA;QAAO,CAAE,GAAG,EAAE,CAAC;QAC/DC,MAAM,EAAEV,UAAU,CAACU,MAAM;QACzB/G;OACD;MAEDqG,UAAU,GAAG;QACX,GAAGA,UAAU;QACb,GAAGsT;OACJ;MAED9M,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC4M,gBAAgB,CAAC;;EAE1C,CAAC;EAED,MAAM9B,UAAU,GAAG,MAAO7X,IAA0B,IAAI;IACtD+X,mBAAmB,CAAC/X,IAAI,EAAE,IAAI,CAAC;IAC/B,MAAMuC,MAAM,GAAG,MAAMyH,QAAQ,CAAC4N,QAAS,CACrCxP,WAA2B,EAC3B4B,QAAQ,CAAC4P,OAAO,EAChBnI,kBAAkB,CAChBzR,IAAI,IAAIuH,MAAM,CAAC2C,KAAK,EACpBV,OAAO,EACPQ,QAAQ,CAAC2H,YAAY,EACrB3H,QAAQ,CAAC4H,yBAAyB,CACnC,CACF;IACDmG,mBAAmB,CAAC/X,IAAI,CAAC;IACzB,OAAOuC,MAAM;EACf,CAAC;EAED,MAAMsX,2BAA2B,GAAG,MAAOzZ,KAA2B,IAAI;IACxE,MAAM;MAAE2G;IAAM,CAAE,GAAG,MAAM8Q,UAAU,CAACzX,KAAK,CAAC;IAE1C,IAAIA,KAAK,EAAE;MACT,KAAK,MAAMJ,IAAI,IAAII,KAAK,EAAE;QACxB,MAAM+I,KAAK,GAAGhH,GAAG,CAAC4E,MAAM,EAAE/G,IAAI,CAAC;QAC/BmJ,KAAA,GACIzG,GAAG,CAAC2D,UAAU,CAACU,MAAM,EAAE/G,IAAI,EAAEmJ,KAAK,IAClCwG,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC;;WAE/B;MACLqG,UAAU,CAACU,MAAM,GAAGA,MAAM;;IAG5B,OAAOA,MAAM;EACf,CAAC;EAED,MAAM+Q,wBAAwB,GAAG,eAAAA,CAC/B9H,MAAiB,EACjB8J,oBAA8B,EAM5B;IAAA,IALFF,OAAA,GAAAxU,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAEI;MACF2U,KAAK,EAAE;IACR;IAED,KAAK,MAAM/Z,IAAI,IAAIgQ,MAAM,EAAE;MACzB,MAAMzG,KAAK,GAAGyG,MAAM,CAAChQ,IAAI,CAAC;MAE1B,IAAIuJ,KAAK,EAAE;QACT,MAAM;UAAEE,EAAE;UAAE,GAAGwP;QAAU,CAAE,GAAG1P,KAAc;QAE5C,IAAIE,EAAE,EAAE;UACN,MAAMuQ,gBAAgB,GAAGzS,MAAM,CAACkB,KAAK,CAACpI,GAAG,CAACoJ,EAAE,CAACzJ,IAAI,CAAC;UAClD,MAAMia,iBAAiB,GACrB1Q,KAAK,CAACE,EAAE,IAAIiJ,oBAAoB,CAAEnJ,KAAe,CAACE,EAAE,CAAC;UAEvD,IAAIwQ,iBAAiB,IAAIvU,eAAe,CAACkB,gBAAgB,EAAE;YACzDmR,mBAAmB,CAAC,CAAC/X,IAAI,CAAC,EAAE,IAAI,CAAC;;UAGnC,MAAMka,UAAU,GAAG,MAAMvF,aAAa,CACpCpL,KAAc,EACdhC,MAAM,CAACtB,QAAQ,EACfmC,WAAW,EACXkP,gCAAgC,EAChCtN,QAAQ,CAAC4H,yBAAyB,IAAI,CAACkI,oBAAoB,EAC3DE,gBAAgB,CACjB;UAED,IAAIC,iBAAiB,IAAIvU,eAAe,CAACkB,gBAAgB,EAAE;YACzDmR,mBAAmB,CAAC,CAAC/X,IAAI,CAAC,CAAC;;UAG7B,IAAIka,UAAU,CAACzQ,EAAE,CAACzJ,IAAI,CAAC,EAAE;YACvB4Z,OAAO,CAACG,KAAK,GAAG,KAAK;YACrB,IAAID,oBAAoB,EAAE;cACxB;;;UAIJ,CAACA,oBAAoB,KAClB3X,GAAG,CAAC+X,UAAU,EAAEzQ,EAAE,CAACzJ,IAAI,IACpBga,gBAAA,GACE5F,yBAAyB,CACvB/N,UAAU,CAACU,MAAM,EACjBmT,UAAU,EACVzQ,EAAE,CAACzJ,IAAI,IAET0C,GAAG,CAAC2D,UAAU,CAACU,MAAM,EAAE0C,EAAE,CAACzJ,IAAI,EAAEka,UAAU,CAACzQ,EAAE,CAACzJ,IAAI,CAAC,IACrD2P,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE0C,EAAE,CAACzJ,IAAI,CAAC,CAAC;;QAG1C,CAAC2O,aAAa,CAACsK,UAAU,CAAC,KACvB,MAAMnB,wBAAwB,CAC7BmB,UAAU,EACVa,oBAAoB,EACpBF,OAAO,CACR,CAAC;;;IAIR,OAAOA,OAAO,CAACG,KAAK;EACtB,CAAC;EAED,MAAM1R,gBAAgB,GAAGA,CAAA,KAAK;IAC5B,KAAK,MAAMrI,IAAI,IAAIuH,MAAM,CAAC2P,OAAO,EAAE;MACjC,MAAM3N,KAAK,GAAUpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;MAEvCuJ,KAAK,KACFA,KAAK,CAACE,EAAE,CAAC6H,IAAA,GACN/H,KAAK,CAACE,EAAE,CAAC6H,IAAI,CAACkD,KAAK,CAAEnL,GAAG,IAAK,CAACgG,IAAI,CAAChG,GAAG,CAAC,IACvC,CAACgG,IAAI,CAAC9F,KAAK,CAACE,EAAE,CAACJ,GAAG,CAAC,CAAC,IACxBgB,UAAU,CAACrK,IAA+B,CAAC;;IAG/CuH,MAAM,CAAC2P,OAAO,GAAG,IAAID,GAAG,EAAE;EAC5B,CAAC;EAED,MAAMwB,SAAS,GAAeA,CAACzY,IAAI,EAAEiB,IAAI,KACvC,CAAC+I,QAAQ,CAAC/D,QAAQ,KACjBjG,IAAI,IAAIiB,IAAI,IAAIyB,GAAG,CAAC0F,WAAW,EAAEpI,IAAI,EAAEiB,IAAI,CAAC,EAC7C,CAACgN,SAAS,CAACkM,SAAS,EAAE,EAAE7U,cAAc,CAAC,CAAC;EAE1C,MAAM4C,SAAS,GAAgCA,CAC7C9H,KAAK,EACLkC,YAAY,EACZmF,QAAQ,KAERH,mBAAmB,CACjBlH,KAAK,EACLmH,MAAM,EACN;IACE,IAAI4C,MAAM,CAACD,KAAA,GACP9B,WAAA,GACA3G,WAAW,CAACa,YAAY,IACtBgD,cAAA,GACA+B,QAAQ,CAACjH,KAAK,IACZ;MAAE,CAACA,KAAK,GAAGkC;IAAY,IACvBA,YAAY;EACrB,GACDmF,QAAQ,EACRnF,YAAY,CACb;EAEH,MAAM8X,cAAc,GAClBpa,IAAuB,IAEvB4B,OAAO,CACLO,GAAG,CACDgI,MAAM,CAACD,KAAK,GAAG9B,WAAW,GAAG9C,cAAc,EAC3CtF,IAAI,EACJgK,QAAQ,CAACzB,gBAAgB,GAAGpG,GAAG,CAACmD,cAAc,EAAEtF,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,CAC/D,CACF;EAEH,MAAM+Y,aAAa,GAAG,SAAAA,CACpB/Y,IAAuB,EACvBZ,KAAkC,EAEhC;IAAA,IADFoR,OAAA,GAAApL,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAA0B,EAAE;IAE5B,MAAMmE,KAAK,GAAUpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;IACvC,IAAIiZ,UAAU,GAAY7Z,KAAK;IAE/B,IAAImK,KAAK,EAAE;MACT,MAAMoJ,cAAc,GAAGpJ,KAAK,CAACE,EAAE;MAE/B,IAAIkJ,cAAc,EAAE;QAClB,CAACA,cAAc,CAAC1M,QAAQ,IACtBvD,GAAG,CAAC0F,WAAW,EAAEpI,IAAI,EAAE2Q,eAAe,CAACvR,KAAK,EAAEuT,cAAc,CAAC,CAAC;QAEhEsG,UAAU,GACRnK,aAAa,CAAC6D,cAAc,CAACtJ,GAAG,CAAC,IAAI/J,iBAAiB,CAACF,KAAK,IACxD,KACAA,KAAK;QAEX,IAAI8P,gBAAgB,CAACyD,cAAc,CAACtJ,GAAG,CAAC,EAAE;UACxC,CAAC,GAAGsJ,cAAc,CAACtJ,GAAG,CAACmH,OAAO,CAAC,CAACyH,OAAO,CACpCoC,SAAS,IACPA,SAAS,CAACC,QAAQ,GACjBrB,UACD,CAAC1M,QAAQ,CAAC8N,SAAS,CAACjb,KAAK,CAAE,CAC/B;eACI,IAAIuT,cAAc,CAACrB,IAAI,EAAE;UAC9B,IAAItS,eAAe,CAAC2T,cAAc,CAACtJ,GAAG,CAAC,EAAE;YACvCsJ,cAAc,CAACrB,IAAI,CAAC2G,OAAO,CAAEsC,WAAW,IAAI;cAC1C,IAAI,CAACA,WAAW,CAACzB,cAAc,IAAI,CAACyB,WAAW,CAACtU,QAAQ,EAAE;gBACxD,IAAIxG,KAAK,CAACC,OAAO,CAACuZ,UAAU,CAAC,EAAE;kBAC7BsB,WAAW,CAACza,OAAO,GAAG,CAAC,CAACmZ,UAAU,CAACrG,IAAI,CACpC3R,IAAY,IAAKA,IAAI,KAAKsZ,WAAW,CAACnb,KAAK,CAC7C;uBACI;kBACLmb,WAAW,CAACza,OAAO,GACjBmZ,UAAU,KAAKsB,WAAW,CAACnb,KAAK,IAAI,CAAC,CAAC6Z,UAAU;;;YAGxD,CAAC,CAAC;iBACG;YACLtG,cAAc,CAACrB,IAAI,CAAC2G,OAAO,CACxBuC,QAA0B,IACxBA,QAAQ,CAAC1a,OAAO,GAAG0a,QAAQ,CAACpb,KAAK,KAAK6Z,UAAW,CACrD;;eAEE,IAAIrK,WAAW,CAAC+D,cAAc,CAACtJ,GAAG,CAAC,EAAE;UAC1CsJ,cAAc,CAACtJ,GAAG,CAACjK,KAAK,GAAG,EAAE;eACxB;UACLuT,cAAc,CAACtJ,GAAG,CAACjK,KAAK,GAAG6Z,UAAU;UAErC,IAAI,CAACtG,cAAc,CAACtJ,GAAG,CAACnK,IAAI,EAAE;YAC5B2N,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;cACnB/M,IAAI;cACJmI,MAAM,EAAEnH,WAAW,CAACoH,WAAW;YAChC,EAAC;;;;;IAMV,CAACoI,OAAO,CAAC0I,WAAW,IAAI1I,OAAO,CAACiK,WAAW,KACzCzB,mBAAmB,CACjBhZ,IAAI,EACJiZ,UAAU,EACVzI,OAAO,CAACiK,WAAW,EACnBjK,OAAO,CAAC0I,WAAW,EACnB,IAAI,CACL;IAEH1I,OAAO,CAACkK,cAAc,IAAIC,OAAO,CAAC3a,IAA0B,CAAC;EAC/D,CAAC;EAED,MAAM4a,SAAS,GAAGA,CAKhB5a,IAAO,EACPZ,KAAQ,EACRoR,OAAU,KACR;IACF,KAAK,MAAMqK,QAAQ,IAAIzb,KAAK,EAAE;MAC5B,IAAI,CAACA,KAAK,CAACuB,cAAc,CAACka,QAAQ,CAAC,EAAE;QACnC;;MAEF,MAAM5B,UAAU,GAAG7Z,KAAK,CAACyb,QAAQ,CAAC;MAClC,MAAMhT,SAAS,GAAG7H,IAAI,GAAG,GAAG,GAAG6a,QAAQ;MACvC,MAAMtR,KAAK,GAAGpH,GAAG,CAACqH,OAAO,EAAE3B,SAAS,CAAC;MAErC,CAACN,MAAM,CAACkB,KAAK,CAACpI,GAAG,CAACL,IAAI,CAAC,IACrBR,QAAQ,CAACyZ,UAAU,CAAC,IACnB1P,KAAK,IAAI,CAACA,KAAK,CAACE,EAAG,KACtB,CAACtK,YAAY,CAAC8Z,UAAU,IACpB2B,SAAS,CAAC/S,SAAS,EAAEoR,UAAU,EAAEzI,OAAO,IACxCuI,aAAa,CAAClR,SAAS,EAAEoR,UAAU,EAAEzI,OAAO,CAAC;;EAErD,CAAC;EAED,MAAMsK,QAAQ,GAAkC,SAAAA,CAC9C9a,IAAI,EACJZ,KAAK,EAEH;IAAA,IADFoR,OAAO,GAAApL,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,EAAE;IAEZ,MAAMmE,KAAK,GAAGpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;IAChC,MAAM6U,YAAY,GAAGtN,MAAM,CAACkB,KAAK,CAACpI,GAAG,CAACL,IAAI,CAAC;IAC3C,MAAM+a,UAAU,GAAG/Z,WAAW,CAAC5B,KAAK,CAAC;IAErCsD,GAAG,CAAC0F,WAAW,EAAEpI,IAAI,EAAE+a,UAAU,CAAC;IAElC,IAAIlG,YAAY,EAAE;MAChBhI,SAAS,CAACpE,KAAK,CAACsE,IAAI,CAAC;QACnB/M,IAAI;QACJmI,MAAM,EAAEnH,WAAW,CAACoH,WAAW;MAChC,EAAC;MAEF,IACE,CAAC1C,eAAe,CAACc,OAAO,IACtBd,eAAe,CAACgB,WAAW,IAC3B2Q,wBAAwB,CAAC7Q,OAAO,IAChC6Q,wBAAwB,CAAC3Q,WAAW,KACtC8J,OAAO,CAAC0I,WAAW,EACnB;QACArM,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UACnB/M,IAAI;UACJ0G,WAAW,EAAE0J,cAAc,CAAC9K,cAAc,EAAE8C,WAAW,CAAC;UACxD5B,OAAO,EAAEiS,SAAS,CAACzY,IAAI,EAAE+a,UAAU;QACpC,EAAC;;WAEC;MACLxR,KAAK,IAAI,CAACA,KAAK,CAACE,EAAE,IAAI,CAACnK,iBAAiB,CAACyb,UAAU,IAC/CH,SAAS,CAAC5a,IAAI,EAAE+a,UAAU,EAAEvK,OAAO,IACnCuI,aAAa,CAAC/Y,IAAI,EAAE+a,UAAU,EAAEvK,OAAO,CAAC;;IAG9CuC,SAAS,CAAC/S,IAAI,EAAEuH,MAAM,CAAC,IAAIsF,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAAE,GAAG1G;IAAU,CAAE,CAAC;IAClEwG,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB/M,IAAI,EAAEmK,MAAM,CAACD,KAAK,GAAGlK,IAAI,GAAG2B,SAAS;MACrCwG,MAAM,EAAEnH,WAAW,CAACoH,WAAW;IAChC,EAAC;EACJ,CAAC;EAED,MAAM5E,QAAQ,GAAkB,MAAO5D,KAAK,IAAI;IAC9CuK,MAAM,CAACD,KAAK,GAAG,IAAI;IACnB,MAAMrK,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,IAAIG,IAAI,GAAWH,MAAM,CAACG,IAAI;IAC9B,IAAIgb,mBAAmB,GAAG,IAAI;IAC9B,MAAMzR,KAAK,GAAUpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;IACvC,MAAMib,0BAA0B,GAAIhC,UAAmB,IAAI;MACzD+B,mBAAmB,GACjBE,MAAM,CAACjY,KAAK,CAACgW,UAAU,CAAC,IACvB9Z,YAAY,CAAC8Z,UAAU,CAAC,IAAIhW,KAAK,CAACgW,UAAU,CAAC3K,OAAO,EAAE,CAAE,IACzDL,SAAS,CAACgL,UAAU,EAAE9W,GAAG,CAACiG,WAAW,EAAEpI,IAAI,EAAEiZ,UAAU,CAAC,CAAC;IAC7D,CAAC;IACD,MAAMkC,0BAA0B,GAAGjJ,kBAAkB,CAAClI,QAAQ,CAACmI,IAAI,CAAC;IACpE,MAAMiJ,yBAAyB,GAAGlJ,kBAAkB,CAClDlI,QAAQ,CAACkK,cAAc,CACxB;IAED,IAAI3K,KAAK,EAAE;MACT,IAAIJ,KAAK;MACT,IAAIrC,OAAO;MACX,MAAMmS,UAAU,GAAGpZ,MAAM,CAACX,IAAA,GACtBkS,aAAa,CAAC7H,KAAK,CAACE,EAAE,IACtB9J,aAAa,CAACC,KAAK,CAAC;MACxB,MAAMoT,WAAW,GACfpT,KAAK,CAACV,IAAI,KAAKgE,MAAM,CAACC,IAAI,IAAIvD,KAAK,CAACV,IAAI,KAAKgE,MAAM,CAACE,SAAS;MAC/D,MAAMiY,oBAAoB,GACvB,CAACvI,aAAa,CAACvJ,KAAK,CAACE,EAAE,CAAC,IACvB,CAACO,QAAQ,CAAC4N,QAAQ,IAClB,CAACzV,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC,IAC7B,CAACuJ,KAAK,CAACE,EAAE,CAAC6R,IAAI,IAChBtH,cAAc,CACZhB,WAAW,EACX7Q,GAAG,CAACkE,UAAU,CAACM,aAAa,EAAE3G,IAAI,CAAC,EACnCqG,UAAU,CAAC4N,WAAW,EACtBmH,yBAAyB,EACzBD,0BAA0B,CAC3B;MACH,MAAMI,OAAO,GAAGxI,SAAS,CAAC/S,IAAI,EAAEuH,MAAM,EAAEyL,WAAW,CAAC;MAEpDtQ,GAAG,CAAC0F,WAAW,EAAEpI,IAAI,EAAEiZ,UAAU,CAAC;MAElC,IAAIjG,WAAW,EAAE;QACfzJ,KAAK,CAACE,EAAE,CAAClG,MAAM,IAAIgG,KAAK,CAACE,EAAE,CAAClG,MAAM,CAAC3D,KAAK,CAAC;QACzCuX,kBAAkB,IAAIA,kBAAkB,CAAC,CAAC,CAAC;aACtC,IAAI5N,KAAK,CAACE,EAAE,CAACjG,QAAQ,EAAE;QAC5B+F,KAAK,CAACE,EAAE,CAACjG,QAAQ,CAAC5D,KAAK,CAAC;;MAG1B,MAAMkJ,UAAU,GAAGkQ,mBAAmB,CAAChZ,IAAI,EAAEiZ,UAAU,EAAEjG,WAAW,CAAC;MAErE,MAAMmG,YAAY,GAAG,CAACxK,aAAa,CAAC7F,UAAU,CAAC,IAAIyS,OAAO;MAE1D,CAACvI,WAAW,IACVnG,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnB/M,IAAI;QACJd,IAAI,EAAEU,KAAK,CAACV,IAAI;QAChBiJ,MAAM,EAAEnH,WAAW,CAACoH,WAAW;MAChC,EAAC;MAEJ,IAAIiT,oBAAoB,EAAE;QACxB,IAAI3V,eAAe,CAACoB,OAAO,IAAIuQ,wBAAwB,CAACvQ,OAAO,EAAE;UAC/D,IAAIkD,QAAQ,CAACmI,IAAI,KAAK,QAAQ,EAAE;YAC9B,IAAIa,WAAW,EAAE;cACf7L,SAAS,EAAE;;iBAER,IAAI,CAAC6L,WAAW,EAAE;YACvB7L,SAAS,EAAE;;;QAIf,OACEgS,YAAY,IACZtM,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UAAE/M,IAAI;UAAE,IAAIub,OAAO,GAAG,EAAE,GAAGzS,UAAU;QAAC,CAAE,CAAC;;MAIlE,CAACkK,WAAW,IAAIuI,OAAO,IAAI1O,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE,GAAG1G;MAAU,CAAE,CAAC;MAElE,IAAI2D,QAAQ,CAAC4N,QAAQ,EAAE;QACrB,MAAM;UAAE7Q;QAAM,CAAE,GAAG,MAAM8Q,UAAU,CAAC,CAAC7X,IAAI,CAAC,CAAC;QAE3Cib,0BAA0B,CAAChC,UAAU,CAAC;QAEtC,IAAI+B,mBAAmB,EAAE;UACvB,MAAMQ,yBAAyB,GAAGlI,iBAAiB,CACjDjN,UAAU,CAACU,MAAM,EACjByC,OAAO,EACPxJ,IAAI,CACL;UACD,MAAMyb,iBAAiB,GAAGnI,iBAAiB,CACzCvM,MAAM,EACNyC,OAAO,EACPgS,yBAAyB,CAACxb,IAAI,IAAIA,IAAI,CACvC;UAEDmJ,KAAK,GAAGsS,iBAAiB,CAACtS,KAAK;UAC/BnJ,IAAI,GAAGyb,iBAAiB,CAACzb,IAAI;UAE7B8G,OAAO,GAAG6H,aAAa,CAAC5H,MAAM,CAAC;;aAE5B;QACLgR,mBAAmB,CAAC,CAAC/X,IAAI,CAAC,EAAE,IAAI,CAAC;QACjCmJ,KAAK,GAAG,CACN,MAAMwL,aAAa,CACjBpL,KAAK,EACLhC,MAAM,CAACtB,QAAQ,EACfmC,WAAW,EACXkP,gCAAgC,EAChCtN,QAAQ,CAAC4H,yBAAyB,CACnC,EACD5R,IAAI,CAAC;QACP+X,mBAAmB,CAAC,CAAC/X,IAAI,CAAC,CAAC;QAE3Bib,0BAA0B,CAAChC,UAAU,CAAC;QAEtC,IAAI+B,mBAAmB,EAAE;UACvB,IAAI7R,KAAK,EAAE;YACTrC,OAAO,GAAG,KAAK;iBACV,IACLpB,eAAe,CAACoB,OAAO,IACvBuQ,wBAAwB,CAACvQ,OAAO,EAChC;YACAA,OAAO,GAAG,MAAMgR,wBAAwB,CAACtO,OAAO,EAAE,IAAI,CAAC;;;;MAK7D,IAAIwR,mBAAmB,EAAE;QACvBzR,KAAK,CAACE,EAAE,CAAC6R,IAAI,IACXX,OAAO,CACLpR,KAAK,CAACE,EAAE,CAAC6R,IAEoB,CAC9B;QACH9B,mBAAmB,CAACxZ,IAAI,EAAE8G,OAAO,EAAEqC,KAAK,EAAEL,UAAU,CAAC;;;EAG3D,CAAC;EAED,MAAM4S,WAAW,GAAGA,CAACrS,GAAQ,EAAE/H,GAAW,KAAI;IAC5C,IAAIa,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAEzF,GAAG,CAAC,IAAI+H,GAAG,CAACK,KAAK,EAAE;MAC5CL,GAAG,CAACK,KAAK,EAAE;MACX,OAAO,CAAC;;IAEV;EACF,CAAC;EAED,MAAMiR,OAAO,GAAiC,eAAAA,CAAO3a,IAAI,EAAkB;IAAA,IAAhBwQ,OAAO,GAAApL,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,EAAE;IACrE,IAAI0B,OAAO;IACX,IAAI4P,gBAAgB;IACpB,MAAMiF,UAAU,GAAGpO,qBAAqB,CAACvN,IAAI,CAAwB;IAErE,IAAIgK,QAAQ,CAAC4N,QAAQ,EAAE;MACrB,MAAM7Q,MAAM,GAAG,MAAM8S,2BAA2B,CAC9CpY,WAAW,CAACzB,IAAI,CAAC,GAAGA,IAAI,GAAG2b,UAAU,CACtC;MAED7U,OAAO,GAAG6H,aAAa,CAAC5H,MAAM,CAAC;MAC/B2P,gBAAgB,GAAG1W,IAAA,GACf,CAAC2b,UAAU,CAACrP,IAAI,CAAEtM,IAAI,IAAKmC,GAAG,CAAC4E,MAAM,EAAE/G,IAAI,CAAC,IAC5C8G,OAAO;WACN,IAAI9G,IAAI,EAAE;MACf0W,gBAAgB,GAAG,CACjB,MAAMkF,OAAO,CAACjY,GAAG,CACfgY,UAAU,CAAC/T,GAAG,CAAC,MAAOC,SAAS,IAAI;QACjC,MAAM0B,KAAK,GAAGpH,GAAG,CAACqH,OAAO,EAAE3B,SAAS,CAAC;QACrC,OAAO,MAAMiQ,wBAAwB,CACnCvO,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAG;UAAE,CAAC5B,SAAS,GAAG0B;QAAK,CAAE,GAAGA,KAAK,CACnD;OACF,CAAC,CACH,EACDiL,KAAK,CAAC1S,OAAO,CAAC;MAChB,EAAE,CAAC4U,gBAAgB,IAAI,CAACrQ,UAAU,CAACS,OAAO,CAAC,IAAIK,SAAS,EAAE;WACrD;MACLuP,gBAAgB,GAAG5P,OAAO,GAAG,MAAMgR,wBAAwB,CAACtO,OAAO,CAAC;;IAGtEqD,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB,IAAI,CAAC1F,QAAQ,CAACrH,IAAI,CAAC,IAClB,CAAC0F,eAAe,CAACoB,OAAO,IAAIuQ,wBAAwB,CAACvQ,OAAO,KAC3DA,OAAO,KAAKT,UAAU,CAACS,OAAO,GAC5B,KACA;QAAE9G;MAAI,CAAE,CAAC;MACb,IAAIgK,QAAQ,CAAC4N,QAAQ,IAAI,CAAC5X,IAAI,GAAG;QAAE8G;MAAO,CAAE,GAAG,EAAE,CAAC;MAClDC,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;IAEFyJ,OAAO,CAACqL,WAAW,IACjB,CAACnF,gBAAgB,IACjBvD,qBAAqB,CACnB3J,OAAO,EACPkS,WAAW,EACX1b,IAAI,GAAG2b,UAAU,GAAGpU,MAAM,CAAC2C,KAAK,CACjC;IAEH,OAAOwM,gBAAgB;EACzB,CAAC;EAED,MAAMyD,SAAS,GACbwB,UAE0C,IACxC;IACF,MAAMxT,MAAM,GAAG;MACb,IAAIgC,MAAM,CAACD,KAAK,GAAG9B,WAAW,GAAG9C,cAAc;KAChD;IAED,OAAO7D,WAAW,CAACka,UAAU,IACzBxT,MAAA,GACAd,QAAQ,CAACsU,UAAU,IACjBxZ,GAAG,CAACgG,MAAM,EAAEwT,UAAU,IACtBA,UAAU,CAAC/T,GAAG,CAAE5H,IAAI,IAAKmC,GAAG,CAACgG,MAAM,EAAEnI,IAAI,CAAC,CAAC;EACnD,CAAC;EAED,MAAM8b,aAAa,GAAuCA,CACxD9b,IAAI,EACJgF,SAAS,MACL;IACJgE,OAAO,EAAE,CAAC,CAAC7G,GAAG,CAAC,CAAC6C,SAAS,IAAIqB,UAAU,EAAEU,MAAM,EAAE/G,IAAI,CAAC;IACtDwG,OAAO,EAAE,CAAC,CAACrE,GAAG,CAAC,CAAC6C,SAAS,IAAIqB,UAAU,EAAEK,WAAW,EAAE1G,IAAI,CAAC;IAC3DmJ,KAAK,EAAEhH,GAAG,CAAC,CAAC6C,SAAS,IAAIqB,UAAU,EAAEU,MAAM,EAAE/G,IAAI,CAAC;IAClD6G,YAAY,EAAE,CAAC,CAAC1E,GAAG,CAACkE,UAAU,CAACO,gBAAgB,EAAE5G,IAAI,CAAC;IACtDkJ,SAAS,EAAE,CAAC,CAAC/G,GAAG,CAAC,CAAC6C,SAAS,IAAIqB,UAAU,EAAEM,aAAa,EAAE3G,IAAI;EAC/D,EAAC;EAEF,MAAM+b,WAAW,GAAsC/b,IAAI,IAAI;IAC7DA,IAAI,IACFuN,qBAAqB,CAACvN,IAAI,CAAC,CAACiY,OAAO,CAAE+D,SAAS,IAC5CrM,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAEiV,SAAS,CAAC,CACpC;IAEHnP,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBhG,MAAM,EAAE/G,IAAI,GAAGqG,UAAU,CAACU,MAAM,GAAG;IACpC,EAAC;EACJ,CAAC;EAED,MAAMkG,QAAQ,GAAkCA,CAACjN,IAAI,EAAEmJ,KAAK,EAAEqH,OAAO,KAAI;IACvE,MAAMnH,GAAG,GAAG,CAAClH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,EAAE;MAAEyJ,EAAE,EAAE;IAAE,CAAE,CAAC,CAACA,EAAE,IAAI,EAAE,EAAEJ,GAAG;IACzD,MAAM4S,YAAY,GAAG9Z,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC,IAAI,EAAE;;IAGvD,MAAM;MAAEqJ,GAAG,EAAE6S,UAAU;MAAErS,OAAO;MAAE3K,IAAI;MAAE,GAAGid;IAAe,CAAE,GAAGF,YAAY;IAE3EvZ,GAAG,CAAC2D,UAAU,CAACU,MAAM,EAAE/G,IAAI,EAAE;MAC3B,GAAGmc,eAAe;MAClB,GAAGhT,KAAK;MACRE;IACD,EAAC;IAEFwD,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB/M,IAAI;MACJ+G,MAAM,EAAEV,UAAU,CAACU,MAAM;MACzBD,OAAO,EAAE;IACV,EAAC;IAEF0J,OAAO,IAAIA,OAAO,CAACqL,WAAW,IAAIxS,GAAG,IAAIA,GAAG,CAACK,KAAK,IAAIL,GAAG,CAACK,KAAK,EAAE;EACnE,CAAC;EAED,MAAMhC,KAAK,GAA+BA,CACxC1H,IAG+B,EAC/BsC,YAAwC,KAExCuM,UAAU,CAAC7O,IAAI,IACX6M,SAAS,CAACC,KAAK,CAACa,SAAS,CAAC;IACxBZ,IAAI,EAAGqP,OAAO,IACZpc,IAAI,CACFkI,SAAS,CAACvG,SAAS,EAAEW,YAAY,CAAC,EAClC8Z,OAIC;GAEN,IACDlU,SAAS,CACPlI,IAA+C,EAC/CsC,YAAY,EACZ,IAAI,CACL;EAEP,MAAM0E,UAAU,GAAiCrC,KAAK,IACpDkI,SAAS,CAACC,KAAK,CAACa,SAAS,CAAC;IACxBZ,IAAI,EACF/H,SAIC,IACC;MACF,IACE6O,qBAAqB,CAAClP,KAAK,CAAC3E,IAAI,EAAEgF,SAAS,CAAChF,IAAI,EAAE2E,KAAK,CAACuB,KAAK,CAAC,IAC9DyN,qBAAqB,CACnB3O,SAAS,EACRL,KAAK,CAACK,SAA2B,IAAIU,eAAe,EACrD2W,aAAa,EACb1X,KAAK,CAAC2X,YAAY,CACnB,EACD;QACA3X,KAAK,CAACuC,QAAQ,CAAC;UACbiB,MAAM,EAAE;YAAE,GAAGC;UAAW,CAAkB;UAC1C,GAAG/B,UAAU;UACb,GAAGrB;QACJ,EAAC;;;GAGP,CAAC,CAAC6I,WAAW;EAEhB,MAAMF,SAAS,GAAoChJ,KAAK,IAAI;IAC1DwF,MAAM,CAACD,KAAK,GAAG,IAAI;IACnBmN,wBAAwB,GAAG;MACzB,GAAGA,wBAAwB;MAC3B,GAAG1S,KAAK,CAACK;KACV;IACD,OAAOgC,UAAU,CAAC;MAChB,GAAGrC,KAAK;MACRK,SAAS,EAAEqS;IACZ,EAAC;EACJ,CAAC;EAED,MAAMhN,UAAU,GAAoC,SAAAA,CAACrK,IAAI,EAAkB;IAAA,IAAhBwQ,OAAO,GAAApL,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,EAAE;IACrE,KAAK,MAAMyC,SAAS,IAAI7H,IAAI,GAAGuN,qBAAqB,CAACvN,IAAI,CAAC,GAAGuH,MAAM,CAAC2C,KAAK,EAAE;MACzE3C,MAAM,CAAC2C,KAAK,CAACqS,MAAM,CAAC1U,SAAS,CAAC;MAC9BN,MAAM,CAACkB,KAAK,CAAC8T,MAAM,CAAC1U,SAAS,CAAC;MAE9B,IAAI,CAAC2I,OAAO,CAACgM,SAAS,EAAE;QACtB7M,KAAK,CAACnG,OAAO,EAAE3B,SAAS,CAAC;QACzB8H,KAAK,CAACvH,WAAW,EAAEP,SAAS,CAAC;;MAG/B,CAAC2I,OAAO,CAACiM,SAAS,IAAI9M,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAEc,SAAS,CAAC;MACzD,CAAC2I,OAAO,CAACkM,SAAS,IAAI/M,KAAK,CAACtJ,UAAU,CAACK,WAAW,EAAEmB,SAAS,CAAC;MAC9D,CAAC2I,OAAO,CAACmM,WAAW,IAAIhN,KAAK,CAACtJ,UAAU,CAACM,aAAa,EAAEkB,SAAS,CAAC;MAClE,CAAC2I,OAAO,CAACoM,gBAAgB,IACvBjN,KAAK,CAACtJ,UAAU,CAACO,gBAAgB,EAAEiB,SAAS,CAAC;MAC/C,CAACmC,QAAQ,CAACzB,gBAAgB,IACxB,CAACiI,OAAO,CAACqM,gBAAgB,IACzBlN,KAAK,CAACrK,cAAc,EAAEuC,SAAS,CAAC;;IAGpCgF,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB5E,MAAM,EAAEnH,WAAW,CAACoH,WAAW;IAChC,EAAC;IAEFyE,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB,GAAG1G,UAAU;MACb,IAAI,CAACmK,OAAO,CAACkM,SAAS,GAAG,EAAE,GAAG;QAAElW,OAAO,EAAEiS,SAAS;MAAE,CAAE;IACvD,EAAC;IAEF,CAACjI,OAAO,CAACsM,WAAW,IAAI3V,SAAS,EAAE;EACrC,CAAC;EAED,MAAMmD,iBAAiB,GAA+CyS,KAAA,IAGjE;IAAA,IAHkE;MACrE9W,QAAQ;MACRjG;IAAI,CACL,GAAA+c,KAAA;IACC,IACGta,SAAS,CAACwD,QAAQ,CAAC,IAAIkE,MAAM,CAACD,KAAK,IACpC,CAAC,CAACjE,QAAQ,IACVsB,MAAM,CAACtB,QAAQ,CAAC5F,GAAG,CAACL,IAAI,CAAC,EACzB;MACAiG,QAAQ,GAAGsB,MAAM,CAACtB,QAAQ,CAAC0B,GAAG,CAAC3H,IAAI,CAAC,GAAGuH,MAAM,CAACtB,QAAQ,CAACsW,MAAM,CAACvc,IAAI,CAAC;;EAEvE,CAAC;EAED,MAAM4I,QAAQ,GAAkC,SAAAA,CAAC5I,IAAI,EAAkB;IAAA,IAAhBwQ,OAAO,GAAApL,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,EAAE;IACjE,IAAImE,KAAK,GAAGpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;IAC9B,MAAMgd,iBAAiB,GACrBva,SAAS,CAAC+N,OAAO,CAACvK,QAAQ,CAAC,IAAIxD,SAAS,CAACuH,QAAQ,CAAC/D,QAAQ,CAAC;IAE7DvD,GAAG,CAAC8G,OAAO,EAAExJ,IAAI,EAAE;MACjB,IAAIuJ,KAAK,IAAI,EAAE,CAAC;MAChBE,EAAE,EAAE;QACF,IAAIF,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAGF,KAAK,CAACE,EAAE,GAAG;UAAEJ,GAAG,EAAE;YAAErJ;UAAI;QAAE,CAAE,CAAC;QACrDA,IAAI;QACJkK,KAAK,EAAE,IAAI;QACX,GAAGsG;MACJ;IACF,EAAC;IACFjJ,MAAM,CAAC2C,KAAK,CAACvC,GAAG,CAAC3H,IAAI,CAAC;IAEtB,IAAIuJ,KAAK,EAAE;MACTe,iBAAiB,CAAC;QAChBrE,QAAQ,EAAExD,SAAS,CAAC+N,OAAO,CAACvK,QAAQ,IAChCuK,OAAO,CAACvK,QAAA,GACR+D,QAAQ,CAAC/D,QAAQ;QACrBjG;MACD,EAAC;WACG;MACL4Y,mBAAmB,CAAC5Y,IAAI,EAAE,IAAI,EAAEwQ,OAAO,CAACpR,KAAK,CAAC;;IAGhD,OAAO;MACL,IAAI4d,iBAAA,GACA;QAAE/W,QAAQ,EAAEuK,OAAO,CAACvK,QAAQ,IAAI+D,QAAQ,CAAC/D;MAAQ,IACjD,EAAE,CAAC;MACP,IAAI+D,QAAQ,CAACiT,WAAA,GACT;QACE/Y,QAAQ,EAAE,CAAC,CAACsM,OAAO,CAACtM,QAAQ;QAC5BJ,GAAG,EAAEiO,YAAY,CAACvB,OAAO,CAAC1M,GAAG,CAAC;QAC9BD,GAAG,EAAEkO,YAAY,CAACvB,OAAO,CAAC3M,GAAG,CAAC;QAC9BG,SAAS,EAAE+N,YAAY,CAASvB,OAAO,CAACxM,SAAS,CAAW;QAC5DD,SAAS,EAAEgO,YAAY,CAACvB,OAAO,CAACzM,SAAS,CAAW;QACpDE,OAAO,EAAE8N,YAAY,CAACvB,OAAO,CAACvM,OAAO;MACtC,IACD,EAAE,CAAC;MACPjE,IAAI;MACJwD,QAAQ;MACRD,MAAM,EAAEC,QAAQ;MAChB6F,GAAG,EAAGA,GAA4B,IAAU;QAC1C,IAAIA,GAAG,EAAE;UACPT,QAAQ,CAAC5I,IAAI,EAAEwQ,OAAO,CAAC;UACvBjH,KAAK,GAAGpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;UAE1B,MAAMkd,QAAQ,GAAGzb,WAAW,CAAC4H,GAAG,CAACjK,KAAK,IAClCiK,GAAG,CAAC8T,gBAAA,GACD9T,GAAG,CAAC8T,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAS,IAAI9T,GAAA,GAC7DA,GAAA,GACFA,GAAG;UACP,MAAM+T,eAAe,GAAGhO,iBAAiB,CAAC8N,QAAQ,CAAC;UACnD,MAAM5L,IAAI,GAAG/H,KAAK,CAACE,EAAE,CAAC6H,IAAI,IAAI,EAAE;UAEhC,IACE8L,eAAA,GACI9L,IAAI,CAACsB,IAAI,CAAEnC,MAAW,IAAKA,MAAM,KAAKyM,QAAQ,IAC9CA,QAAQ,KAAK3T,KAAK,CAACE,EAAE,CAACJ,GAAG,EAC7B;YACA;;UAGF3G,GAAG,CAAC8G,OAAO,EAAExJ,IAAI,EAAE;YACjByJ,EAAE,EAAE;cACF,GAAGF,KAAK,CAACE,EAAE;cACX,IAAI2T,eAAA,GACA;gBACE9L,IAAI,EAAE,CACJ,GAAGA,IAAI,CAACzP,MAAM,CAACwN,IAAI,CAAC,EACpB6N,QAAQ,EACR,IAAIzd,KAAK,CAACC,OAAO,CAACyC,GAAG,CAACmD,cAAc,EAAEtF,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAC1D;gBACDqJ,GAAG,EAAE;kBAAEnK,IAAI,EAAEge,QAAQ,CAAChe,IAAI;kBAAEc;gBAAI;cACjC,IACD;gBAAEqJ,GAAG,EAAE6T;cAAQ,CAAE;YACtB;UACF,EAAC;UAEFtE,mBAAmB,CAAC5Y,IAAI,EAAE,KAAK,EAAE2B,SAAS,EAAEub,QAAQ,CAAC;eAChD;UACL3T,KAAK,GAAGpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,EAAE,EAAE,CAAC;UAE9B,IAAIuJ,KAAK,CAACE,EAAE,EAAE;YACZF,KAAK,CAACE,EAAE,CAACS,KAAK,GAAG,KAAK;;UAGxB,CAACF,QAAQ,CAACzB,gBAAgB,IAAIiI,OAAO,CAACjI,gBAAgB,KACpD,EAAEpI,kBAAkB,CAACoH,MAAM,CAACkB,KAAK,EAAEzI,IAAI,CAAC,IAAImK,MAAM,CAACC,MAAM,CAAC,IAC1D7C,MAAM,CAAC2P,OAAO,CAACvP,GAAG,CAAC3H,IAAI,CAAC;;;KAG/B;EACH,CAAC;EAED,MAAMqd,WAAW,GAAGA,CAAA,KAClBrT,QAAQ,CAAC4M,gBAAgB,IACzBzD,qBAAqB,CAAC3J,OAAO,EAAEkS,WAAW,EAAEnU,MAAM,CAAC2C,KAAK,CAAC;EAE3D,MAAMoT,YAAY,GAAIrX,QAAkB,IAAI;IAC1C,IAAIxD,SAAS,CAACwD,QAAQ,CAAC,EAAE;MACvB4G,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE9G;MAAQ,CAAE,CAAC;MAClCkN,qBAAqB,CACnB3J,OAAO,EACP,CAACH,GAAG,EAAErJ,IAAI,KAAI;QACZ,MAAMqT,YAAY,GAAUlR,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;QAC9C,IAAIqT,YAAY,EAAE;UAChBhK,GAAG,CAACpD,QAAQ,GAAGoN,YAAY,CAAC5J,EAAE,CAACxD,QAAQ,IAAIA,QAAQ;UAEnD,IAAIxG,KAAK,CAACC,OAAO,CAAC2T,YAAY,CAAC5J,EAAE,CAAC6H,IAAI,CAAC,EAAE;YACvC+B,YAAY,CAAC5J,EAAE,CAAC6H,IAAI,CAAC2G,OAAO,CAAElD,QAAQ,IAAI;cACxCA,QAAQ,CAAC9O,QAAQ,GAAGoN,YAAY,CAAC5J,EAAE,CAACxD,QAAQ,IAAIA,QAAQ;YAC1D,CAAC,CAAC;;;MAGR,CAAC,EACD,CAAC,EACD,KAAK,CACN;;EAEL,CAAC;EAED,MAAM2F,YAAY,GAChBA,CAAC2R,OAAO,EAAEC,SAAS,KAAK,MAAOC,CAAC,IAAI;IAClC,IAAIC,YAAY,GAAG/b,SAAS;IAC5B,IAAI8b,CAAC,EAAE;MACLA,CAAC,CAACE,cAAc,IAAIF,CAAC,CAACE,cAAc,EAAE;MACrCF,CAA8B,CAACG,OAAO,IACpCH,CAA8B,CAACG,OAAO,EAAE;;IAE7C,IAAItF,WAAW,GACbtX,WAAW,CAACoH,WAAW,CAAC;IAE1ByE,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBiK,YAAY,EAAE;IACf,EAAC;IAEF,IAAIhN,QAAQ,CAAC4N,QAAQ,EAAE;MACrB,MAAM;QAAE7Q,MAAM;QAAEoB;MAAM,CAAE,GAAG,MAAM0P,UAAU,EAAE;MAC7CxR,UAAU,CAACU,MAAM,GAAGA,MAAM;MAC1BuR,WAAW,GAAGtX,WAAW,CAACmH,MAAM,CAAiB;WAC5C;MACL,MAAM2P,wBAAwB,CAACtO,OAAO,CAAC;;IAGzC,IAAIjC,MAAM,CAACtB,QAAQ,CAAC4X,IAAI,EAAE;MACxB,KAAK,MAAM7d,IAAI,IAAIuH,MAAM,CAACtB,QAAQ,EAAE;QAClC0J,KAAK,CAAC2I,WAAW,EAAEtY,IAAI,CAAC;;;IAI5B2P,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE,MAAM,CAAC;IAEhC,IAAI4H,aAAa,CAACtI,UAAU,CAACU,MAAM,CAAC,EAAE;MACpC8F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnBhG,MAAM,EAAE;MACT,EAAC;MACF,IAAI;QACF,MAAMwW,OAAO,CAACjF,WAAiC,EAAEmF,CAAC,CAAC;QACnD,OAAOtU,KAAK,EAAE;QACduU,YAAY,GAAGvU,KAAK;;WAEjB;MACL,IAAIqU,SAAS,EAAE;QACb,MAAMA,SAAS,CAAC;UAAE,GAAGnX,UAAU,CAACU;QAAM,CAAE,EAAE0W,CAAC,CAAC;;MAE9CJ,WAAW,EAAE;MACb3F,UAAU,CAAC2F,WAAW,CAAC;;IAGzBxQ,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBkH,WAAW,EAAE,IAAI;MACjB+C,YAAY,EAAE,KAAK;MACnBhK,kBAAkB,EAAE2B,aAAa,CAACtI,UAAU,CAACU,MAAM,CAAC,IAAI,CAAC2W,YAAY;MACrE5G,WAAW,EAAEzQ,UAAU,CAACyQ,WAAW,GAAG,CAAC;MACvC/P,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;IACF,IAAI2W,YAAY,EAAE;MAChB,MAAMA,YAAY;;EAEtB,CAAC;EAEH,MAAMI,UAAU,GAAoC,SAAAA,CAAC9d,IAAI,EAAkB;IAAA,IAAhBwQ,OAAO,GAAApL,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,EAAE;IACrE,IAAIjD,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC,EAAE;MACtB,IAAIyB,WAAW,CAAC+O,OAAO,CAAClO,YAAY,CAAC,EAAE;QACrCwY,QAAQ,CAAC9a,IAAI,EAAEgB,WAAW,CAACmB,GAAG,CAACmD,cAAc,EAAEtF,IAAI,CAAC,CAAC,CAAC;aACjD;QACL8a,QAAQ,CACN9a,IAAI,EACJwQ,OAAO,CAAClO,YAA2D,CACpE;QACDI,GAAG,CAAC4C,cAAc,EAAEtF,IAAI,EAAEgB,WAAW,CAACwP,OAAO,CAAClO,YAAY,CAAC,CAAC;;MAG9D,IAAI,CAACkO,OAAO,CAACmM,WAAW,EAAE;QACxBhN,KAAK,CAACtJ,UAAU,CAACM,aAAa,EAAE3G,IAAI,CAAC;;MAGvC,IAAI,CAACwQ,OAAO,CAACkM,SAAS,EAAE;QACtB/M,KAAK,CAACtJ,UAAU,CAACK,WAAW,EAAE1G,IAAI,CAAC;QACnCqG,UAAU,CAACG,OAAO,GAAGgK,OAAO,CAAClO,YAAA,GACzBmW,SAAS,CAACzY,IAAI,EAAEgB,WAAW,CAACmB,GAAG,CAACmD,cAAc,EAAEtF,IAAI,CAAC,CAAC,IACtDyY,SAAS,EAAE;;MAGjB,IAAI,CAACjI,OAAO,CAACiM,SAAS,EAAE;QACtB9M,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC;QAC9B0F,eAAe,CAACoB,OAAO,IAAIK,SAAS,EAAE;;MAGxC0F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE,GAAG1G;MAAU,CAAE,CAAC;;EAE3C,CAAC;EAED,MAAM0X,MAAM,GAA+B,SAAAA,CACzCvW,UAAU,EAER;IAAA,IADFwW,gBAAgB,GAAA5Y,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,EAAE;IAErB,MAAM6Y,aAAa,GAAGzW,UAAU,GAAGxG,WAAW,CAACwG,UAAU,CAAC,GAAGlC,cAAc;IAC3E,MAAM4Y,kBAAkB,GAAGld,WAAW,CAACid,aAAa,CAAC;IACrD,MAAME,kBAAkB,GAAGxP,aAAa,CAACnH,UAAU,CAAC;IACpD,MAAMW,MAAM,GAAGgW,kBAAkB,GAAG7Y,cAAc,GAAG4Y,kBAAkB;IAEvE,IAAI,CAACF,gBAAgB,CAACI,iBAAiB,EAAE;MACvC9Y,cAAc,GAAG2Y,aAAa;;IAGhC,IAAI,CAACD,gBAAgB,CAACK,UAAU,EAAE;MAChC,IAAIL,gBAAgB,CAACM,eAAe,EAAE;QACpC,MAAMC,aAAa,GAAG,IAAItH,GAAG,CAAC,CAC5B,GAAG1P,MAAM,CAAC2C,KAAK,EACf,GAAG3E,MAAM,CAACqF,IAAI,CAACwF,cAAc,CAAC9K,cAAc,EAAE8C,WAAW,CAAC,CAAC,CAC5D,CAAC;QACF,KAAK,MAAMP,SAAS,IAAIpI,KAAK,CAACuY,IAAI,CAACuG,aAAa,CAAC,EAAE;UACjDpc,GAAG,CAACkE,UAAU,CAACK,WAAW,EAAEmB,SAAS,IACjCnF,GAAG,CAACyF,MAAM,EAAEN,SAAS,EAAE1F,GAAG,CAACiG,WAAW,EAAEP,SAAS,CAAC,IAClDiT,QAAQ,CACNjT,SAAoC,EACpC1F,GAAG,CAACgG,MAAM,EAAEN,SAAS,CAAC,CACvB;;aAEF;QACL,IAAIjH,KAAK,IAAIa,WAAW,CAAC+F,UAAU,CAAC,EAAE;UACpC,KAAK,MAAMxH,IAAI,IAAIuH,MAAM,CAAC2C,KAAK,EAAE;YAC/B,MAAMX,KAAK,GAAGpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;YAChC,IAAIuJ,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;cACrB,MAAMkJ,cAAc,GAAGlT,KAAK,CAACC,OAAO,CAAC6J,KAAK,CAACE,EAAE,CAAC6H,IAAI,IAC9C/H,KAAK,CAACE,EAAE,CAAC6H,IAAI,CAAC,CAAC,IACf/H,KAAK,CAACE,EAAE,CAACJ,GAAG;cAEhB,IAAIyF,aAAa,CAAC6D,cAAc,CAAC,EAAE;gBACjC,MAAM6L,IAAI,GAAG7L,cAAc,CAAC8L,OAAO,CAAC,MAAM,CAAC;gBAC3C,IAAID,IAAI,EAAE;kBACRA,IAAI,CAACE,KAAK,EAAE;kBACZ;;;;;;QAOV,IAAIV,gBAAgB,CAACW,aAAa,EAAE;UAClC,KAAK,MAAM9W,SAAS,IAAIN,MAAM,CAAC2C,KAAK,EAAE;YACpC4Q,QAAQ,CACNjT,SAAoC,EACpC1F,GAAG,CAACgG,MAAM,EAAEN,SAAS,CAAC,CACvB;;eAEE;UACL2B,OAAO,GAAG,EAAE;;;MAIhBpB,WAAW,GAAG4B,QAAQ,CAACzB,gBAAA,GACnByV,gBAAgB,CAACI,iBAAA,GACdpd,WAAW,CAACsE,cAAc,IAC1B,KACFtE,WAAW,CAACmH,MAAM,CAAkB;MAEzC0E,SAAS,CAACpE,KAAK,CAACsE,IAAI,CAAC;QACnB5E,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;MAEF0E,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnB5E,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;;IAGJZ,MAAM,GAAG;MACP2C,KAAK,EAAE8T,gBAAgB,CAACM,eAAe,GAAG/W,MAAM,CAAC2C,KAAK,GAAG,IAAI+M,GAAG,EAAE;MAClEC,OAAO,EAAE,IAAID,GAAG,EAAE;MAClBxO,KAAK,EAAE,IAAIwO,GAAG,EAAE;MAChBhR,QAAQ,EAAE,IAAIgR,GAAG,EAAE;MACnBvP,KAAK,EAAE,IAAIuP,GAAG,EAAE;MAChBnP,QAAQ,EAAE,KAAK;MACf4B,KAAK,EAAE;KACR;IAEDS,MAAM,CAACD,KAAK,GACV,CAACxE,eAAe,CAACoB,OAAO,IACxB,CAAC,CAACkX,gBAAgB,CAAClB,WAAW,IAC9B,CAAC,CAACkB,gBAAgB,CAACM,eAAe;IAEpCnU,MAAM,CAACzC,KAAK,GAAG,CAAC,CAACsC,QAAQ,CAACzB,gBAAgB;IAE1CsE,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB+J,WAAW,EAAEkH,gBAAgB,CAACY,eAAA,GAC1BvY,UAAU,CAACyQ,WAAA,GACX,CAAC;MACLtQ,OAAO,EAAE2X,kBAAA,GACL,QACAH,gBAAgB,CAACtB,SAAA,GACfrW,UAAU,CAACG,OAAA,GACX,CAAC,EACCwX,gBAAgB,CAACI,iBAAiB,IAClC,CAACnQ,SAAS,CAACzG,UAAU,EAAElC,cAAc,CAAC,CACvC;MACP2O,WAAW,EAAE+J,gBAAgB,CAACa,eAAA,GAC1BxY,UAAU,CAAC4N,WAAA,GACX,KAAK;MACTvN,WAAW,EAAEyX,kBAAA,GACT,KACAH,gBAAgB,CAACM,eAAA,GACfN,gBAAgB,CAACI,iBAAiB,IAAIhW,WAAA,GACpCgI,cAAc,CAAC9K,cAAc,EAAE8C,WAAW,IAC1C/B,UAAU,CAACK,WAAA,GACbsX,gBAAgB,CAACI,iBAAiB,IAAI5W,UAAA,GACpC4I,cAAc,CAAC9K,cAAc,EAAEkC,UAAU,IACzCwW,gBAAgB,CAACtB,SAAA,GACfrW,UAAU,CAACK,WAAA,GACX,EAAE;MACZC,aAAa,EAAEqX,gBAAgB,CAACrB,WAAA,GAC5BtW,UAAU,CAACM,aAAA,GACX,EAAE;MACNI,MAAM,EAAEiX,gBAAgB,CAACc,UAAU,GAAGzY,UAAU,CAACU,MAAM,GAAG,EAAE;MAC5DiG,kBAAkB,EAAEgR,gBAAgB,CAACe,sBAAA,GACjC1Y,UAAU,CAAC2G,kBAAA,GACX,KAAK;MACTgK,YAAY,EAAE;IACf,EAAC;EACJ,CAAC;EAED,MAAM0H,KAAK,GAA+BA,CAAClX,UAAU,EAAEwW,gBAAgB,KACrED,MAAM,CACJlP,UAAU,CAACrH,UAAU,IAChBA,UAAuB,CAACY,WAA2B,IACpDZ,UAAU,EACdwW,gBAAgB,CACjB;EAEH,MAAMgB,QAAQ,GAAkC,SAAAA,CAAChf,IAAI,EAAkB;IAAA,IAAhBwQ,OAAO,GAAApL,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAG,EAAE;IACjE,MAAMmE,KAAK,GAAGpH,GAAG,CAACqH,OAAO,EAAExJ,IAAI,CAAC;IAChC,MAAM2S,cAAc,GAAGpJ,KAAK,IAAIA,KAAK,CAACE,EAAE;IAExC,IAAIkJ,cAAc,EAAE;MAClB,MAAMuK,QAAQ,GAAGvK,cAAc,CAACrB,IAAA,GAC5BqB,cAAc,CAACrB,IAAI,CAAC,CAAC,IACrBqB,cAAc,CAACtJ,GAAG;MAEtB,IAAI6T,QAAQ,CAACxT,KAAK,EAAE;QAClBwT,QAAQ,CAACxT,KAAK,EAAE;QAChB8G,OAAO,CAACyO,YAAY,IAClBpQ,UAAU,CAACqO,QAAQ,CAACvT,MAAM,CAAC,IAC3BuT,QAAQ,CAACvT,MAAM,EAAE;;;EAGzB,CAAC;EAED,MAAM0S,aAAa,GACjB1C,gBAAkD,IAChD;IACFtT,UAAU,GAAG;MACX,GAAGA,UAAU;MACb,GAAGsT;KACJ;EACH,CAAC;EAED,MAAMuF,mBAAmB,GAAGA,CAAA,KAC1BrQ,UAAU,CAAC7E,QAAQ,CAAC3E,aAAa,CAAC,IACjC2E,QAAQ,CAAC3E,aAA0B,EAAE,CAAC8Z,IAAI,CAAEhX,MAAoB,IAAI;IACnEuW,KAAK,CAACvW,MAAM,EAAE6B,QAAQ,CAACoV,YAAY,CAAC;IACpCvS,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBtG,SAAS,EAAE;IACZ,EAAC;EACJ,CAAC,CAAC;EAEJ,MAAMT,OAAO,GAAG;IACdf,OAAO,EAAE;MACP2D,QAAQ;MACRyB,UAAU;MACVyR,aAAa;MACblQ,YAAY;MACZqB,QAAQ;MACRjG,UAAU;MACV6Q,UAAU;MACVwF,WAAW;MACXnV,SAAS;MACTuQ,SAAS;MACTtR,SAAS;MACT+Q,cAAc;MACd5N,iBAAiB;MACjBqO,UAAU;MACVyB,cAAc;MACd2D,MAAM;MACNmB,mBAAmB;MACnB7W,gBAAgB;MAChBiV,YAAY;MACZzQ,SAAS;MACTnH,eAAe;MACf,IAAI8D,OAAOA,CAAA;QACT,OAAOA,OAAO;OACf;MACD,IAAIpB,WAAWA,CAAA;QACb,OAAOA,WAAW;OACnB;MACD,IAAI+B,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAAC/K,KAAK;QACd+K,MAAM,GAAG/K,KAAK;OACf;MACD,IAAIkG,cAAcA,CAAA;QAChB,OAAOA,cAAc;OACtB;MACD,IAAIiC,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAACnI,KAAK;QACdmI,MAAM,GAAGnI,KAAK;OACf;MACD,IAAIiH,UAAUA,CAAA;QACZ,OAAOA,UAAU;OAClB;MACD,IAAI2D,QAAQA,CAAA;QACV,OAAOA,QAAQ;OAChB;MACD,IAAIA,QAAQA,CAAC5K,KAAK;QAChB4K,QAAQ,GAAG;UACT,GAAGA,QAAQ;UACX,GAAG5K;SACJ;;IAEJ;IACDuO,SAAS;IACTgN,OAAO;IACP/R,QAAQ;IACRgD,YAAY;IACZlE,KAAK;IACLoT,QAAQ;IACRX,SAAS;IACTuE,KAAK;IACLZ,UAAU;IACV/B,WAAW;IACX1R,UAAU;IACV4C,QAAQ;IACR+R,QAAQ;IACRlD;GACD;EAED,OAAO;IACL,GAAG9V,OAAO;IACVqZ,WAAW,EAAErZ;GACd;AACH;ACvhDA,IAAAsZ,UAAA,GAAeA,CAAA,KAAK;EAClB,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,UAAU,EAAE;IACtD,OAAOD,MAAM,CAACC,UAAU,EAAE;;EAG5B,MAAMC,CAAC,GACL,OAAOC,WAAW,KAAK,WAAW,GAAGrgB,IAAI,CAACsgB,GAAG,EAAE,GAAGD,WAAW,CAACC,GAAG,EAAE,GAAG,IAAI;EAE5E,OAAO,sCAAsC,CAAC1d,OAAO,CAAC,OAAO,EAAG2d,CAAC,IAAI;IACnE,MAAMC,CAAC,GAAG,CAACC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAGN,CAAC,IAAI,EAAE,GAAG,CAAC;IAE3C,OAAO,CAACG,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG,EAAEG,QAAQ,CAAC,EAAE,CAAC;EACtD,CAAC,CAAC;AACJ,CAAC;ACVD,IAAAC,iBAAA,GAAe,SAAAA,CACbjgB,IAAuB,EACvB2C,KAAa;EAAA,IACb6N,OAAA,GAAApL,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAiC,EAAE;EAAA,OAEnCoL,OAAO,CAACqL,WAAW,IAAIpa,WAAW,CAAC+O,OAAO,CAACqL,WAAW,IAClDrL,OAAO,CAAC0P,SAAS,IACjB,GAAGlgB,IAAI,IAAIyB,WAAW,CAAC+O,OAAO,CAAC2P,UAAU,CAAC,GAAGxd,KAAK,GAAG6N,OAAO,CAAC2P,UAAU,MACvE,EAAE;AAAA;ACTR,IAAAC,QAAA,GAAeA,CAAInf,IAAS,EAAE7B,KAAc,KAAU,CACpD,GAAG6B,IAAI,EACP,GAAGsM,qBAAqB,CAACnO,KAAK,CAAC,CAChC;ACLD,IAAAihB,cAAA,GAAmBjhB,KAAc,IAC/BK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAACwI,GAAG,CAAC,MAAMjG,SAAS,CAAC,GAAGA,SAAS;ACOjD,SAAU2e,MAAMA,CAC5Brf,IAAS,EACT0B,KAAa,EACbvD,KAAe;EAEf,OAAO,CACL,GAAG6B,IAAI,CAACwO,KAAK,CAAC,CAAC,EAAE9M,KAAK,CAAC,EACvB,GAAG4K,qBAAqB,CAACnO,KAAK,CAAC,EAC/B,GAAG6B,IAAI,CAACwO,KAAK,CAAC9M,KAAK,CAAC,CACrB;AACH;AChBA,IAAA4d,WAAA,GAAeA,CACbtf,IAAuB,EACvB+W,IAAY,EACZwI,EAAU,KACW;EACrB,IAAI,CAAC/gB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC,EAAE;IACxB,OAAO,EAAE;;EAGX,IAAIQ,WAAW,CAACR,IAAI,CAACuf,EAAE,CAAC,CAAC,EAAE;IACzBvf,IAAI,CAACuf,EAAE,CAAC,GAAG7e,SAAS;;EAEtBV,IAAI,CAACwf,MAAM,CAACD,EAAE,EAAE,CAAC,EAAEvf,IAAI,CAACwf,MAAM,CAACzI,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAE3C,OAAO/W,IAAI;AACb,CAAC;ACfD,IAAAyf,SAAA,GAAeA,CAAIzf,IAAS,EAAE7B,KAAc,KAAU,CACpD,GAAGmO,qBAAqB,CAACnO,KAAK,CAAC,EAC/B,GAAGmO,qBAAqB,CAACtM,IAAI,CAAC,CAC/B;ACDD,SAAS0f,eAAeA,CAAI1f,IAAS,EAAE2f,OAAiB;EACtD,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMC,IAAI,GAAG,CAAC,GAAG7f,IAAI,CAAC;EAEtB,KAAK,MAAM0B,KAAK,IAAIie,OAAO,EAAE;IAC3BE,IAAI,CAACL,MAAM,CAAC9d,KAAK,GAAGke,CAAC,EAAE,CAAC,CAAC;IACzBA,CAAC,EAAE;;EAGL,OAAOjf,OAAO,CAACkf,IAAI,CAAC,CAACje,MAAM,GAAGie,IAAI,GAAG,EAAE;AACzC;AAEA,IAAAC,aAAA,GAAeA,CAAI9f,IAAS,EAAE0B,KAAyB,KACrDlB,WAAW,CAACkB,KAAK,IACb,KACAge,eAAe,CACb1f,IAAI,EACHsM,qBAAqB,CAAC5K,KAAK,CAAc,CAACqe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACjE;ACtBP,IAAAC,WAAA,GAAeA,CAAIlgB,IAAS,EAAEmgB,MAAc,EAAEC,MAAc,KAAU;EACpE,CAACpgB,IAAI,CAACmgB,MAAM,CAAC,EAAEngB,IAAI,CAACogB,MAAM,CAAC,CAAC,GAAG,CAACpgB,IAAI,CAACogB,MAAM,CAAC,EAAEpgB,IAAI,CAACmgB,MAAM,CAAC,CAAC;AAC7D,CAAC;ACFD,IAAAE,QAAA,GAAeA,CAAIhJ,WAAgB,EAAE3V,KAAa,EAAEvD,KAAQ,KAAI;EAC9DkZ,WAAW,CAAC3V,KAAK,CAAC,GAAGvD,KAAK;EAC1B,OAAOkZ,WAAW;AACpB,CAAC;;ACwCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCG;AACG,SAAUiJ,aAAaA,CAO3B5c,KAKC;EAED,MAAMqB,OAAO,GAAGxB,cAAc,EAAE;EAChC,MAAM;IACJS,OAAO,GAAGe,OAAO,CAACf,OAAO;IACzBjF,IAAI;IACJwhB,OAAO,GAAG,IAAI;IACdjZ,gBAAgB;IAChBM;EAAK,CACN,GAAGlE,KAAK;EACT,MAAM,CAACqL,MAAM,EAAEyR,SAAS,CAAC,GAAGpd,cAAK,CAAC+B,QAAQ,CAACnB,OAAO,CAACmV,cAAc,CAACpa,IAAI,CAAC,CAAC;EACxE,MAAM0hB,GAAG,GAAGrd,cAAK,CAACkC,MAAM,CACtBtB,OAAO,CAACmV,cAAc,CAACpa,IAAI,CAAC,CAAC4H,GAAG,CAAC0X,UAAU,CAAC,CAC7C;EACD,MAAMqC,SAAS,GAAGtd,cAAK,CAACkC,MAAM,CAACyJ,MAAM,CAAC;EACtC,MAAM4R,KAAK,GAAGvd,cAAK,CAACkC,MAAM,CAACvG,IAAI,CAAC;EAChC,MAAM6hB,SAAS,GAAGxd,cAAK,CAACkC,MAAM,CAAC,KAAK,CAAC;EAErCqb,KAAK,CAAC3a,OAAO,GAAGjH,IAAI;EACpB2hB,SAAS,CAAC1a,OAAO,GAAG+I,MAAM;EAC1B/K,OAAO,CAACsC,MAAM,CAACkB,KAAK,CAACd,GAAG,CAAC3H,IAAI,CAAC;EAE9B6I,KAAK,IACF5D,OAA0D,CAAC2D,QAAQ,CAClE5I,IAA+B,EAC/B6I,KAAsC,CACvC;EAEHlD,yBAAyB,CACvB,MACEV,OAAO,CAAC4H,SAAS,CAACpE,KAAK,CAACkF,SAAS,CAAC;IAChCZ,IAAI,EAAE+U,KAAA,IAMD;MAAA,IANE;QACL3Z,MAAM;QACNnI,IAAI,EAAE+hB;MAAc,CAIrB,GAAAD,KAAA;MACC,IAAIC,cAAc,KAAKH,KAAK,CAAC3a,OAAO,IAAI,CAAC8a,cAAc,EAAE;QACvD,MAAMzJ,WAAW,GAAGnW,GAAG,CAACgG,MAAM,EAAEyZ,KAAK,CAAC3a,OAAO,CAAC;QAC9C,IAAIxH,KAAK,CAACC,OAAO,CAAC4Y,WAAW,CAAC,EAAE;UAC9BmJ,SAAS,CAACnJ,WAAW,CAAC;UACtBoJ,GAAG,CAACza,OAAO,GAAGqR,WAAW,CAAC1Q,GAAG,CAAC0X,UAAU,CAAC;;;;EAIhD,EAAC,CAACzR,WAAW,EAChB,CAAC5I,OAAO,CAAC,CACV;EAED,MAAM+c,YAAY,GAAG3d,cAAK,CAAC+E,WAAW,CAMlC6Y,uBAA0B,IACxB;IACFJ,SAAS,CAAC5a,OAAO,GAAG,IAAI;IACxBhC,OAAO,CAACiT,cAAc,CAAClY,IAAI,EAAEiiB,uBAAuB,CAAC;EACvD,CAAC,EACD,CAAChd,OAAO,EAAEjF,IAAI,CAAC,CAChB;EAED,MAAMoM,MAAM,GAAGA,CACbhN,KAEwD,EACxDoR,OAA+B,KAC7B;IACF,MAAM0R,WAAW,GAAG3U,qBAAqB,CAACvM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAM6iB,uBAAuB,GAAG7B,QAAQ,CACtCnb,OAAO,CAACmV,cAAc,CAACpa,IAAI,CAAC,EAC5BkiB,WAAW,CACZ;IACDjd,OAAO,CAACsC,MAAM,CAACmC,KAAK,GAAGuW,iBAAiB,CACtCjgB,IAAI,EACJiiB,uBAAuB,CAACpf,MAAM,GAAG,CAAC,EAClC2N,OAAO,CACR;IACDkR,GAAG,CAACza,OAAO,GAAGmZ,QAAQ,CAACsB,GAAG,CAACza,OAAO,EAAEib,WAAW,CAACta,GAAG,CAAC0X,UAAU,CAAC,CAAC;IAChE0C,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClChd,OAAO,CAACiT,cAAc,CAAClY,IAAI,EAAEiiB,uBAAuB,EAAE7B,QAAQ,EAAE;MAC9D7H,IAAI,EAAE8H,cAAc,CAACjhB,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAM+iB,OAAO,GAAGA,CACd/iB,KAEwD,EACxDoR,OAA+B,KAC7B;IACF,MAAM4R,YAAY,GAAG7U,qBAAqB,CAACvM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC9D,MAAM6iB,uBAAuB,GAAGvB,SAAS,CACvCzb,OAAO,CAACmV,cAAc,CAACpa,IAAI,CAAC,EAC5BoiB,YAAY,CACb;IACDnd,OAAO,CAACsC,MAAM,CAACmC,KAAK,GAAGuW,iBAAiB,CAACjgB,IAAI,EAAE,CAAC,EAAEwQ,OAAO,CAAC;IAC1DkR,GAAG,CAACza,OAAO,GAAGyZ,SAAS,CAACgB,GAAG,CAACza,OAAO,EAAEmb,YAAY,CAACxa,GAAG,CAAC0X,UAAU,CAAC,CAAC;IAClE0C,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClChd,OAAO,CAACiT,cAAc,CAAClY,IAAI,EAAEiiB,uBAAuB,EAAEvB,SAAS,EAAE;MAC/DnI,IAAI,EAAE8H,cAAc,CAACjhB,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAMijB,MAAM,GAAI1f,KAAyB,IAAI;IAC3C,MAAMsf,uBAAuB,GAEvBlB,aAAa,CAAC9b,OAAO,CAACmV,cAAc,CAACpa,IAAI,CAAC,EAAE2C,KAAK,CAAC;IACxD+e,GAAG,CAACza,OAAO,GAAG8Z,aAAa,CAACW,GAAG,CAACza,OAAO,EAAEtE,KAAK,CAAC;IAC/Cqf,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClC,CAACxiB,KAAK,CAACC,OAAO,CAACyC,GAAG,CAAC8C,OAAO,CAACuE,OAAO,EAAExJ,IAAI,CAAC,CAAC,IACxC0C,GAAG,CAACuC,OAAO,CAACuE,OAAO,EAAExJ,IAAI,EAAE2B,SAAS,CAAC;IACvCsD,OAAO,CAACiT,cAAc,CAAClY,IAAI,EAAEiiB,uBAAuB,EAAElB,aAAa,EAAE;MACnExI,IAAI,EAAE5V;IACP,EAAC;EACJ,CAAC;EAED,MAAM2f,QAAM,GAAGhC,CACb3d,KAAa,EACbvD,KAEwD,EACxDoR,OAA+B,KAC7B;IACF,MAAM+R,WAAW,GAAGhV,qBAAqB,CAACvM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAM6iB,uBAAuB,GAAG3B,MAAQ,CACtCrb,OAAO,CAACmV,cAAc,CAACpa,IAAI,CAAC,EAC5B2C,KAAK,EACL4f,WAAW,CACZ;IACDtd,OAAO,CAACsC,MAAM,CAACmC,KAAK,GAAGuW,iBAAiB,CAACjgB,IAAI,EAAE2C,KAAK,EAAE6N,OAAO,CAAC;IAC9DkR,GAAG,CAACza,OAAO,GAAGqZ,MAAQ,CAACoB,GAAG,CAACza,OAAO,EAAEtE,KAAK,EAAE4f,WAAW,CAAC3a,GAAG,CAAC0X,UAAU,CAAC,CAAC;IACvE0C,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClChd,OAAO,CAACiT,cAAc,CAAClY,IAAI,EAAEiiB,uBAAuB,EAAE3B,MAAQ,EAAE;MAC9D/H,IAAI,EAAE5V,KAAK;MACX6V,IAAI,EAAE6H,cAAc,CAACjhB,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAMojB,IAAI,GAAGA,CAACpB,MAAc,EAAEC,MAAc,KAAI;IAC9C,MAAMY,uBAAuB,GAAGhd,OAAO,CAACmV,cAAc,CAACpa,IAAI,CAAC;IAC5DmhB,WAAW,CAACc,uBAAuB,EAAEb,MAAM,EAAEC,MAAM,CAAC;IACpDF,WAAW,CAACO,GAAG,CAACza,OAAO,EAAEma,MAAM,EAAEC,MAAM,CAAC;IACxCW,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClChd,OAAO,CAACiT,cAAc,CACpBlY,IAAI,EACJiiB,uBAAuB,EACvBd,WAAW,EACX;MACE5I,IAAI,EAAE6I,MAAM;MACZ5I,IAAI,EAAE6I;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAMoB,IAAI,GAAGA,CAACzK,IAAY,EAAEwI,EAAU,KAAI;IACxC,MAAMyB,uBAAuB,GAAGhd,OAAO,CAACmV,cAAc,CAACpa,IAAI,CAAC;IAC5DugB,WAAW,CAAC0B,uBAAuB,EAAEjK,IAAI,EAAEwI,EAAE,CAAC;IAC9CD,WAAW,CAACmB,GAAG,CAACza,OAAO,EAAE+Q,IAAI,EAAEwI,EAAE,CAAC;IAClCwB,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClChd,OAAO,CAACiT,cAAc,CACpBlY,IAAI,EACJiiB,uBAAuB,EACvB1B,WAAW,EACX;MACEhI,IAAI,EAAEP,IAAI;MACVQ,IAAI,EAAEgI;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAMkC,MAAM,GAAGA,CACb/f,KAAa,EACbvD,KAAgD,KAC9C;IACF,MAAM6I,WAAW,GAAGjH,WAAW,CAAC5B,KAAK,CAAC;IACtC,MAAM6iB,uBAAuB,GAAGX,QAAQ,CACtCrc,OAAO,CAACmV,cAAc,CAEpBpa,IAAI,CAAC,EACP2C,KAAK,EACLsF,WAAwE,CACzE;IACDyZ,GAAG,CAACza,OAAO,GAAG,CAAC,GAAGgb,uBAAuB,CAAC,CAACra,GAAG,CAAC,CAAC+a,IAAI,EAAE9B,CAAC,KACrD,CAAC8B,IAAI,IAAI9B,CAAC,KAAKle,KAAK,GAAG2c,UAAU,EAAE,GAAGoC,GAAG,CAACza,OAAO,CAAC4Z,CAAC,CAAC,CACrD;IACDmB,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAAC,CAAC,GAAGQ,uBAAuB,CAAC,CAAC;IACvChd,OAAO,CAACiT,cAAc,CACpBlY,IAAI,EACJiiB,uBAAuB,EACvBX,QAAQ,EACR;MACE/I,IAAI,EAAE5V,KAAK;MACX6V,IAAI,EAAEvQ;IACP,GACD,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAED,MAAMhG,OAAO,GACX7C,KAEwD,IACtD;IACF,MAAM6iB,uBAAuB,GAAG1U,qBAAqB,CAACvM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IACzEsiB,GAAG,CAACza,OAAO,GAAGgb,uBAAuB,CAACra,GAAG,CAAC0X,UAAU,CAAC;IACrD0C,YAAY,CAAC,CAAC,GAAGC,uBAAuB,CAAC,CAAC;IAC1CR,SAAS,CAAC,CAAC,GAAGQ,uBAAuB,CAAC,CAAC;IACvChd,OAAO,CAACiT,cAAc,CACpBlY,IAAI,EACJ,CAAC,GAAGiiB,uBAAuB,CAAC,EACxBhhB,IAAO,IAAQA,IAAI,EACvB,EAAE,EACF,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAEDoD,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnBb,OAAO,CAACkF,MAAM,CAACC,MAAM,GAAG,KAAK;IAE7B2I,SAAS,CAAC/S,IAAI,EAAEiF,OAAO,CAACsC,MAAM,CAAC,IAC7BtC,OAAO,CAAC4H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAC3B,GAAG9H,OAAO,CAACoB;IACe,EAAC;IAE/B,IACEwb,SAAS,CAAC5a,OAAO,KAChB,CAACiL,kBAAkB,CAACjN,OAAO,CAAC+E,QAAQ,CAACmI,IAAI,CAAC,CAACC,UAAU,IACpDnN,OAAO,CAACoB,UAAU,CAAC4N,WAAW,CAAC,IACjC,CAAC/B,kBAAkB,CAACjN,OAAO,CAAC+E,QAAQ,CAACkK,cAAc,CAAC,CAAC9B,UAAU,EAC/D;MACA,IAAInN,OAAO,CAAC+E,QAAQ,CAAC4N,QAAQ,EAAE;QAC7B3S,OAAO,CAAC4S,UAAU,CAAC,CAAC7X,IAAI,CAAC,CAAC,CAACmf,IAAI,CAAE5c,MAAM,IAAI;UACzC,MAAM4G,KAAK,GAAGhH,GAAG,CAACI,MAAM,CAACwE,MAAM,EAAE/G,IAAI,CAAC;UACtC,MAAM4iB,aAAa,GAAGzgB,GAAG,CAAC8C,OAAO,CAACoB,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC;UAE1D,IACE4iB,aAAA,GACK,CAACzZ,KAAK,IAAIyZ,aAAa,CAAC1jB,IAAI,IAC5BiK,KAAK,KACHyZ,aAAa,CAAC1jB,IAAI,KAAKiK,KAAK,CAACjK,IAAI,IAChC0jB,aAAa,CAAC/Y,OAAO,KAAKV,KAAK,CAACU,OAAO,CAAC,GAC5CV,KAAK,IAAIA,KAAK,CAACjK,IAAI,EACvB;YACAiK,KAAA,GACIzG,GAAG,CAACuC,OAAO,CAACoB,UAAU,CAACU,MAAM,EAAE/G,IAAI,EAAEmJ,KAAK,IAC1CwG,KAAK,CAAC1K,OAAO,CAACoB,UAAU,CAACU,MAAM,EAAE/G,IAAI,CAAC;YAC1CiF,OAAO,CAAC4H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;cAC3BhG,MAAM,EAAE9B,OAAO,CAACoB,UAAU,CAACU;YAC5B,EAAC;;QAEN,CAAC,CAAC;aACG;QACL,MAAMwC,KAAK,GAAUpH,GAAG,CAAC8C,OAAO,CAACuE,OAAO,EAAExJ,IAAI,CAAC;QAC/C,IACEuJ,KAAK,IACLA,KAAK,CAACE,EAAE,IACR,EACEyI,kBAAkB,CAACjN,OAAO,CAAC+E,QAAQ,CAACkK,cAAc,CAAC,CAAC9B,UAAU,IAC9DF,kBAAkB,CAACjN,OAAO,CAAC+E,QAAQ,CAACmI,IAAI,CAAC,CAACC,UAAU,CACrD,EACD;UACAuC,aAAa,CACXpL,KAAK,EACLtE,OAAO,CAACsC,MAAM,CAACtB,QAAQ,EACvBhB,OAAO,CAACmD,WAAW,EACnBnD,OAAO,CAAC+E,QAAQ,CAAC2H,YAAY,KAAKrO,eAAe,CAACK,GAAG,EACrDsB,OAAO,CAAC+E,QAAQ,CAAC4H,yBAAyB,EAC1C,IAAI,CACL,CAACuN,IAAI,CACHhW,KAAK,IACJ,CAACwF,aAAa,CAACxF,KAAK,CAAC,IACrBlE,OAAO,CAAC4H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;YAC3BhG,MAAM,EAAEqN,yBAAyB,CAC/BnP,OAAO,CAACoB,UAAU,CAACU,MAAmC,EACtDoC,KAAK,EACLnJ,IAAI;UAEP,EAAC,CACL;;;;IAKPiF,OAAO,CAAC4H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAC3B/M,IAAI;MACJmI,MAAM,EAAEnH,WAAW,CAACiE,OAAO,CAACmD,WAAW;IACxC,EAAC;IAEFnD,OAAO,CAACsC,MAAM,CAACmC,KAAK,IAClByJ,qBAAqB,CAAClO,OAAO,CAACuE,OAAO,EAAE,CAACH,GAAG,EAAE/H,GAAW,KAAI;MAC1D,IACE2D,OAAO,CAACsC,MAAM,CAACmC,KAAK,IACpBpI,GAAG,CAAC4R,UAAU,CAACjO,OAAO,CAACsC,MAAM,CAACmC,KAAK,CAAC,IACpCL,GAAG,CAACK,KAAK,EACT;QACAL,GAAG,CAACK,KAAK,EAAE;QACX,OAAO,CAAC;;MAEV;IACF,CAAC,CAAC;IAEJzE,OAAO,CAACsC,MAAM,CAACmC,KAAK,GAAG,EAAE;IAEzBzE,OAAO,CAACkC,SAAS,EAAE;IACnB0a,SAAS,CAAC5a,OAAO,GAAG,KAAK;GAC1B,EAAE,CAAC+I,MAAM,EAAEhQ,IAAI,EAAEiF,OAAO,CAAC,CAAC;EAE3BZ,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnB,CAAC3D,GAAG,CAAC8C,OAAO,CAACmD,WAAW,EAAEpI,IAAI,CAAC,IAAIiF,OAAO,CAACiT,cAAc,CAAClY,IAAI,CAAC;IAE/D,OAAO,MAAK;MACV,MAAMiK,aAAa,GAAGA,CAACjK,IAAuB,EAAEZ,KAAc,KAAI;QAChE,MAAMmK,KAAK,GAAUpH,GAAG,CAAC8C,OAAO,CAACuE,OAAO,EAAExJ,IAAI,CAAC;QAC/C,IAAIuJ,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;UACrBF,KAAK,CAACE,EAAE,CAACS,KAAK,GAAG9K,KAAK;;MAE1B,CAAC;MAED6F,OAAO,CAAC+E,QAAQ,CAACzB,gBAAgB,IAAIA,gBAAA,GACjCtD,OAAO,CAACoF,UAAU,CAACrK,IAA+B,IAClDiK,aAAa,CAACjK,IAAI,EAAE,KAAK,CAAC;IAChC,CAAC;GACF,EAAE,CAACA,IAAI,EAAEiF,OAAO,EAAEuc,OAAO,EAAEjZ,gBAAgB,CAAC,CAAC;EAE9C,OAAO;IACLia,IAAI,EAAEne,cAAK,CAAC+E,WAAW,CAACoZ,IAAI,EAAE,CAACR,YAAY,EAAEhiB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAC5Dwd,IAAI,EAAEpe,cAAK,CAAC+E,WAAW,CAACqZ,IAAI,EAAE,CAACT,YAAY,EAAEhiB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAC5Dkd,OAAO,EAAE9d,cAAK,CAAC+E,WAAW,CAAC+Y,OAAO,EAAE,CAACH,YAAY,EAAEhiB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAClEmH,MAAM,EAAE/H,cAAK,CAAC+E,WAAW,CAACgD,MAAM,EAAE,CAAC4V,YAAY,EAAEhiB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEod,MAAM,EAAEhe,cAAK,CAAC+E,WAAW,CAACiZ,MAAM,EAAE,CAACL,YAAY,EAAEhiB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEqb,MAAM,EAAEjc,cAAK,CAAC+E,WAAW,CAACkZ,QAAM,EAAE,CAACN,YAAY,EAAEhiB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEyd,MAAM,EAAEre,cAAK,CAAC+E,WAAW,CAACsZ,MAAM,EAAE,CAACV,YAAY,EAAEhiB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEhD,OAAO,EAAEoC,cAAK,CAAC+E,WAAW,CAACnH,OAAO,EAAE,CAAC+f,YAAY,EAAEhiB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAClE+K,MAAM,EAAE3L,cAAK,CAAC+C,OAAO,CACnB,MACE4I,MAAM,CAACpI,GAAG,CAAC,CAAC2B,KAAK,EAAE5G,KAAK,MAAM;MAC5B,GAAG4G,KAAK;MACR,CAACiY,OAAO,GAAGE,GAAG,CAACza,OAAO,CAACtE,KAAK,CAAC,IAAI2c,UAAU;IAC5C,EAAC,CAAgE,EACpE,CAACtP,MAAM,EAAEwR,OAAO,CAAC;GAEpB;AACH;;AClbA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACG,SAAUqB,OAAOA,CAAA,EAK+C;EAAA,IAApEle,KAAA,GAAAS,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAAzD,SAAA,GAAAyD,SAAA,MAAkE,EAAE;EAEpE,MAAM0d,YAAY,GAAGze,cAAK,CAACkC,MAAM,CAE/B5E,SAAS,CAAC;EACZ,MAAMohB,OAAO,GAAG1e,cAAK,CAACkC,MAAM,CAAsB5E,SAAS,CAAC;EAC5D,MAAM,CAACqD,SAAS,EAAEmB,eAAe,CAAC,GAAG9B,cAAK,CAAC+B,QAAQ,CAA0B;IAC3EI,OAAO,EAAE,KAAK;IACdK,YAAY,EAAE,KAAK;IACnBJ,SAAS,EAAEoI,UAAU,CAAClK,KAAK,CAACU,aAAa,CAAC;IAC1C4O,WAAW,EAAE,KAAK;IAClB+C,YAAY,EAAE,KAAK;IACnBhK,kBAAkB,EAAE,KAAK;IACzBlG,OAAO,EAAE,KAAK;IACdgQ,WAAW,EAAE,CAAC;IACdpQ,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBG,MAAM,EAAEpC,KAAK,CAACoC,MAAM,IAAI,EAAE;IAC1Bd,QAAQ,EAAEtB,KAAK,CAACsB,QAAQ,IAAI,KAAK;IACjC8Q,OAAO,EAAE,KAAK;IACd1R,aAAa,EAAEwJ,UAAU,CAAClK,KAAK,CAACU,aAAa,IACzC1D,SAAA,GACAgD,KAAK,CAACU;EACX,EAAC;EAEF,IAAI,CAACyd,YAAY,CAAC7b,OAAO,EAAE;IACzB,IAAItC,KAAK,CAAC0a,WAAW,EAAE;MACrByD,YAAY,CAAC7b,OAAO,GAAG;QACrB,GAAGtC,KAAK,CAAC0a,WAAW;QACpBra;OACD;MAED,IAAIL,KAAK,CAACU,aAAa,IAAI,CAACwJ,UAAU,CAAClK,KAAK,CAACU,aAAa,CAAC,EAAE;QAC3DV,KAAK,CAAC0a,WAAW,CAACX,KAAK,CAAC/Z,KAAK,CAACU,aAAa,EAAEV,KAAK,CAACya,YAAY,CAAC;;WAE7D;MACL,MAAM;QAAEC,WAAW;QAAE,GAAG5T;MAAI,CAAE,GAAGoL,iBAAiB,CAAClS,KAAK,CAAC;MAEzDme,YAAY,CAAC7b,OAAO,GAAG;QACrB,GAAGwE,IAAI;QACPzG;OACD;;;EAIL,MAAMC,OAAO,GAAG6d,YAAY,CAAC7b,OAAO,CAAChC,OAAO;EAC5CA,OAAO,CAAC+E,QAAQ,GAAGrF,KAAK;EAExBgB,yBAAyB,CAAC,MAAK;IAC7B,MAAMqd,GAAG,GAAG/d,OAAO,CAAC+B,UAAU,CAAC;MAC7BhC,SAAS,EAAEC,OAAO,CAACS,eAAe;MAClCwB,QAAQ,EAAEA,CAAA,KAAMf,eAAe,CAAC;QAAE,GAAGlB,OAAO,CAACoB;MAAU,CAAE,CAAC;MAC1DiW,YAAY,EAAE;IACf,EAAC;IAEFnW,eAAe,CAAElF,IAAI,KAAM;MACzB,GAAGA,IAAI;MACP8V,OAAO,EAAE;IACV,EAAC,CAAC;IAEH9R,OAAO,CAACoB,UAAU,CAAC0Q,OAAO,GAAG,IAAI;IAEjC,OAAOiM,GAAG;EACZ,CAAC,EAAE,CAAC/d,OAAO,CAAC,CAAC;EAEbZ,cAAK,CAACyB,SAAS,CACb,MAAMb,OAAO,CAACqY,YAAY,CAAC3Y,KAAK,CAACsB,QAAQ,CAAC,EAC1C,CAAChB,OAAO,EAAEN,KAAK,CAACsB,QAAQ,CAAC,CAC1B;EAED5B,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnB,IAAInB,KAAK,CAACwN,IAAI,EAAE;MACdlN,OAAO,CAAC+E,QAAQ,CAACmI,IAAI,GAAGxN,KAAK,CAACwN,IAAI;;IAEpC,IAAIxN,KAAK,CAACuP,cAAc,EAAE;MACxBjP,OAAO,CAAC+E,QAAQ,CAACkK,cAAc,GAAGvP,KAAK,CAACuP,cAAc;;EAE1D,CAAC,EAAE,CAACjP,OAAO,EAAEN,KAAK,CAACwN,IAAI,EAAExN,KAAK,CAACuP,cAAc,CAAC,CAAC;EAE/C7P,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnB,IAAInB,KAAK,CAACoC,MAAM,EAAE;MAChB9B,OAAO,CAAC0T,UAAU,CAAChU,KAAK,CAACoC,MAAM,CAAC;MAChC9B,OAAO,CAACoY,WAAW,EAAE;;GAExB,EAAE,CAACpY,OAAO,EAAEN,KAAK,CAACoC,MAAM,CAAC,CAAC;EAE3B1C,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnBnB,KAAK,CAAC4D,gBAAgB,IACpBtD,OAAO,CAAC4H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAC3B5E,MAAM,EAAElD,OAAO,CAACiD,SAAS;IAC1B,EAAC;GACL,EAAE,CAACjD,OAAO,EAAEN,KAAK,CAAC4D,gBAAgB,CAAC,CAAC;EAErClE,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnB,IAAIb,OAAO,CAACS,eAAe,CAACc,OAAO,EAAE;MACnC,MAAMA,OAAO,GAAGvB,OAAO,CAACwT,SAAS,EAAE;MACnC,IAAIjS,OAAO,KAAKxB,SAAS,CAACwB,OAAO,EAAE;QACjCvB,OAAO,CAAC4H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UAC3BvG;QACD,EAAC;;;GAGP,EAAE,CAACvB,OAAO,EAAED,SAAS,CAACwB,OAAO,CAAC,CAAC;EAEhCnC,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnB,IAAInB,KAAK,CAACwD,MAAM,IAAI,CAAC8F,SAAS,CAACtJ,KAAK,CAACwD,MAAM,EAAE4a,OAAO,CAAC9b,OAAO,CAAC,EAAE;MAC7DhC,OAAO,CAAC8Y,MAAM,CAACpZ,KAAK,CAACwD,MAAM,EAAE;QAC3BwW,aAAa,EAAE,IAAI;QACnB,GAAG1Z,OAAO,CAAC+E,QAAQ,CAACoV;MACrB,EAAC;MACF2D,OAAO,CAAC9b,OAAO,GAAGtC,KAAK,CAACwD,MAAM;MAC9BhC,eAAe,CAAE2G,KAAK,KAAM;QAAE,GAAGA;MAAK,CAAE,CAAC,CAAC;WACrC;MACL7H,OAAO,CAACia,mBAAmB,EAAE;;GAEhC,EAAE,CAACja,OAAO,EAAEN,KAAK,CAACwD,MAAM,CAAC,CAAC;EAE3B9D,cAAK,CAACyB,SAAS,CAAC,MAAK;IACnB,IAAI,CAACb,OAAO,CAACkF,MAAM,CAACD,KAAK,EAAE;MACzBjF,OAAO,CAACkC,SAAS,EAAE;MACnBlC,OAAO,CAACkF,MAAM,CAACD,KAAK,GAAG,IAAI;;IAG7B,IAAIjF,OAAO,CAACkF,MAAM,CAACzC,KAAK,EAAE;MACxBzC,OAAO,CAACkF,MAAM,CAACzC,KAAK,GAAG,KAAK;MAC5BzC,OAAO,CAAC4H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE,GAAG9H,OAAO,CAACoB;MAAU,CAAE,CAAC;;IAGzDpB,OAAO,CAACoD,gBAAgB,EAAE;EAC5B,CAAC,CAAC;EAEFya,YAAY,CAAC7b,OAAO,CAACjC,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAEC,OAAO,CAAC;EAEtE,OAAO6d,YAAY,CAAC7b,OAAO;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}