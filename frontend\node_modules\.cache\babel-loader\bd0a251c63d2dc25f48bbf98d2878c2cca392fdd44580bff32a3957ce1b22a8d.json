{"ast": null, "code": "export { default } from \"./Accordion.js\";\nexport { default as accordionClasses } from \"./accordionClasses.js\";\nexport * from \"./accordionClasses.js\";", "map": {"version": 3, "names": ["default", "accordionClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Accordion/index.js"], "sourcesContent": ["export { default } from \"./Accordion.js\";\nexport { default as accordionClasses } from \"./accordionClasses.js\";\nexport * from \"./accordionClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,uBAAuB;AACnE,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}