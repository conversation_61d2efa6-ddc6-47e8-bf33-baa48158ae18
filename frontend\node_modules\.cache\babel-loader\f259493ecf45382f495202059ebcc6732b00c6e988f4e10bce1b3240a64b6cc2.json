{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormCheckInput = /*#__PURE__*/React.forwardRef(({\n  id,\n  bsPrefix,\n  className,\n  type = 'checkbox',\n  isValid = false,\n  isInvalid = false,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'input',\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check-input');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    type: type,\n    id: id || controlId,\n    className: classNames(className, bsPrefix, isValid && 'is-valid', isInvalid && 'is-invalid')\n  });\n});\nFormCheckInput.displayName = 'FormCheckInput';\nexport default FormCheckInput;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "FormContext", "useBootstrapPrefix", "jsx", "_jsx", "FormCheckInput", "forwardRef", "id", "bsPrefix", "className", "type", "<PERSON><PERSON><PERSON><PERSON>", "isInvalid", "as", "Component", "props", "ref", "controlId", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FormCheckInput.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormCheckInput = /*#__PURE__*/React.forwardRef(({\n  id,\n  bsPrefix,\n  className,\n  type = 'checkbox',\n  isValid = false,\n  isInvalid = false,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'input',\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check-input');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    type: type,\n    id: id || controlId,\n    className: classNames(className, bsPrefix, isValid && 'is-valid', isInvalid && 'is-invalid')\n  });\n});\nFormCheckInput.displayName = 'FormCheckInput';\nexport default FormCheckInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EACpDC,EAAE;EACFC,QAAQ;EACRC,SAAS;EACTC,IAAI,GAAG,UAAU;EACjBC,OAAO,GAAG,KAAK;EACfC,SAAS,GAAG,KAAK;EACjB;EACAC,EAAE,EAAEC,SAAS,GAAG,OAAO;EACvB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC;EACF,CAAC,GAAGjB,UAAU,CAACC,WAAW,CAAC;EAC3BO,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaJ,IAAI,CAACU,SAAS,EAAE;IAClC,GAAGC,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRN,IAAI,EAAEA,IAAI;IACVH,EAAE,EAAEA,EAAE,IAAIU,SAAS;IACnBR,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAED,QAAQ,EAAEG,OAAO,IAAI,UAAU,EAAEC,SAAS,IAAI,YAAY;EAC7F,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,cAAc,CAACa,WAAW,GAAG,gBAAgB;AAC7C,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}