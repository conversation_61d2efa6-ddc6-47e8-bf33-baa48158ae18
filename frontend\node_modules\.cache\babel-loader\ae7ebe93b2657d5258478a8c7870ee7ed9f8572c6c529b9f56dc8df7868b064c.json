{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\nvar invariant = function (condition, format, a, b, c, d, e, f) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(format.replace(/%s/g, function () {\n        return args[argIndex++];\n      }));\n      error.name = 'Invariant Violation';\n    }\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n};\nmodule.exports = invariant;", "map": {"version": 3, "names": ["invariant", "condition", "format", "a", "b", "c", "d", "e", "f", "process", "env", "NODE_ENV", "undefined", "Error", "error", "args", "argIndex", "replace", "name", "framesToPop", "module", "exports"], "sources": ["C:/laragon/www/frontend/node_modules/invariant/browser.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar invariant = function(condition, format, a, b, c, d, e, f) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error(\n        'Minified exception occurred; use the non-minified dev environment ' +\n        'for the full error message and additional helpful warnings.'\n      );\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(\n        format.replace(/%s/g, function() { return args[argIndex++]; })\n      );\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n};\n\nmodule.exports = invariant;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAIA,SAAS,GAAG,SAAAA,CAASC,SAAS,EAAEC,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC5D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIT,MAAM,KAAKU,SAAS,EAAE;MACxB,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;IACjE;EACF;EAEA,IAAI,CAACZ,SAAS,EAAE;IACd,IAAIa,KAAK;IACT,IAAIZ,MAAM,KAAKU,SAAS,EAAE;MACxBE,KAAK,GAAG,IAAID,KAAK,CACf,oEAAoE,GACpE,6DACF,CAAC;IACH,CAAC,MAAM;MACL,IAAIE,IAAI,GAAG,CAACZ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAC7B,IAAIQ,QAAQ,GAAG,CAAC;MAChBF,KAAK,GAAG,IAAID,KAAK,CACfX,MAAM,CAACe,OAAO,CAAC,KAAK,EAAE,YAAW;QAAE,OAAOF,IAAI,CAACC,QAAQ,EAAE,CAAC;MAAE,CAAC,CAC/D,CAAC;MACDF,KAAK,CAACI,IAAI,GAAG,qBAAqB;IACpC;IAEAJ,KAAK,CAACK,WAAW,GAAG,CAAC,CAAC,CAAC;IACvB,MAAML,KAAK;EACb;AACF,CAAC;AAEDM,MAAM,CAACC,OAAO,GAAGrB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}