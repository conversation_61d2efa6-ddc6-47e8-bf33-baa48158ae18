{"ast": null, "code": "export { default } from \"./FormLabel.js\";\nexport * from \"./FormLabel.js\";\nexport { default as formLabelClasses } from \"./formLabelClasses.js\";\nexport * from \"./formLabelClasses.js\";", "map": {"version": 3, "names": ["default", "formLabelClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/FormLabel/index.js"], "sourcesContent": ["export { default } from \"./FormLabel.js\";\nexport * from \"./FormLabel.js\";\nexport { default as formLabelClasses } from \"./formLabelClasses.js\";\nexport * from \"./formLabelClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,cAAc,gBAAgB;AAC9B,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,uBAAuB;AACnE,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}