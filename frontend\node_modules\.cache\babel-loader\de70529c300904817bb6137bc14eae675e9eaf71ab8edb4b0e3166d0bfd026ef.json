{"ast": null, "code": "export { default } from \"./TimelineOppositeContent.js\";\nexport { default as timelineOppositeContentClasses } from \"./timelineOppositeContentClasses.js\";\nexport * from \"./timelineOppositeContentClasses.js\";", "map": {"version": 3, "names": ["default", "timelineOppositeContentClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineOppositeContent/index.js"], "sourcesContent": ["export { default } from \"./TimelineOppositeContent.js\";\nexport { default as timelineOppositeContentClasses } from \"./timelineOppositeContentClasses.js\";\nexport * from \"./timelineOppositeContentClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,8BAA8B;AACtD,SAASA,OAAO,IAAIC,8BAA8B,QAAQ,qCAAqC;AAC/F,cAAc,qCAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}