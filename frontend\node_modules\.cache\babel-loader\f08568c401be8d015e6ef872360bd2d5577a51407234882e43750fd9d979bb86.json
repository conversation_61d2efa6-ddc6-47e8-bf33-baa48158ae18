{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarToggle = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    children,\n    label = 'Toggle navigation',\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'button',\n    onClick,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-toggler');\n  const {\n    onToggle,\n    expanded\n  } = useContext(NavbarContext) || {};\n  const handleClick = useEventCallback(e => {\n    if (onClick) onClick(e);\n    if (onToggle) onToggle();\n  });\n  if (Component === 'button') {\n    props.type = 'button';\n  }\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    onClick: handleClick,\n    \"aria-label\": label,\n    className: classNames(className, bsPrefix, !expanded && 'collapsed'),\n    children: children || /*#__PURE__*/_jsx(\"span\", {\n      className: `${bsPrefix}-icon`\n    })\n  });\n});\nNavbarToggle.displayName = 'NavbarToggle';\nexport default NavbarToggle;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useEventCallback", "useBootstrapPrefix", "NavbarContext", "jsx", "_jsx", "Navbar<PERSON><PERSON><PERSON>", "forwardRef", "_ref", "ref", "bsPrefix", "className", "children", "label", "as", "Component", "onClick", "props", "onToggle", "expanded", "handleClick", "e", "type", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/NavbarToggle.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarToggle = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  label = 'Toggle navigation',\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'button',\n  onClick,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-toggler');\n  const {\n    onToggle,\n    expanded\n  } = useContext(NavbarContext) || {};\n  const handleClick = useEventCallback(e => {\n    if (onClick) onClick(e);\n    if (onToggle) onToggle();\n  });\n  if (Component === 'button') {\n    props.type = 'button';\n  }\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    onClick: handleClick,\n    \"aria-label\": label,\n    className: classNames(className, bsPrefix, !expanded && 'collapsed'),\n    children: children || /*#__PURE__*/_jsx(\"span\", {\n      className: `${bsPrefix}-icon`\n    })\n  });\n});\nNavbarToggle.displayName = 'NavbarToggle';\nexport default NavbarToggle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAAC,IAAA,EAShDC,GAAG,KAAK;EAAA,IATyC;IAClDC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACRC,KAAK,GAAG,mBAAmB;IAC3B;IACAC,EAAE,EAAEC,SAAS,GAAG,QAAQ;IACxBC,OAAO;IACP,GAAGC;EACL,CAAC,GAAAT,IAAA;EACCE,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,gBAAgB,CAAC;EACzD,MAAM;IACJQ,QAAQ;IACRC;EACF,CAAC,GAAGnB,UAAU,CAACG,aAAa,CAAC,IAAI,CAAC,CAAC;EACnC,MAAMiB,WAAW,GAAGnB,gBAAgB,CAACoB,CAAC,IAAI;IACxC,IAAIL,OAAO,EAAEA,OAAO,CAACK,CAAC,CAAC;IACvB,IAAIH,QAAQ,EAAEA,QAAQ,CAAC,CAAC;EAC1B,CAAC,CAAC;EACF,IAAIH,SAAS,KAAK,QAAQ,EAAE;IAC1BE,KAAK,CAACK,IAAI,GAAG,QAAQ;EACvB;EACA,OAAO,aAAajB,IAAI,CAACU,SAAS,EAAE;IAClC,GAAGE,KAAK;IACRR,GAAG,EAAEA,GAAG;IACRO,OAAO,EAAEI,WAAW;IACpB,YAAY,EAAEP,KAAK;IACnBF,SAAS,EAAEb,UAAU,CAACa,SAAS,EAAED,QAAQ,EAAE,CAACS,QAAQ,IAAI,WAAW,CAAC;IACpEP,QAAQ,EAAEA,QAAQ,IAAI,aAAaP,IAAI,CAAC,MAAM,EAAE;MAC9CM,SAAS,EAAE,GAAGD,QAAQ;IACxB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,YAAY,CAACiB,WAAW,GAAG,cAAc;AACzC,eAAejB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}