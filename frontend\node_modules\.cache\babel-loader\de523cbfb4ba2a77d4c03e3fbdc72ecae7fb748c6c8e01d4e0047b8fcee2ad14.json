{"ast": null, "code": "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __knownSymbol = (name, symbol) => (symbol = Symbol[name]) ? symbol : Symbol.for(\"Symbol.\" + name);\nvar __typeError = msg => {\n  throw TypeError(msg);\n};\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __name = (target, value) => __defProp(target, \"name\", {\n  value,\n  configurable: true\n});\nvar __decoratorStart = base => {\n  var _a2;\n  return [,,, __create((_a2 = base == null ? void 0 : base[__knownSymbol(\"metadata\")]) != null ? _a2 : null)];\n};\nvar __decoratorStrings = [\"class\", \"method\", \"getter\", \"setter\", \"accessor\", \"field\", \"value\", \"get\", \"set\"];\nvar __expectFn = fn => fn !== void 0 && typeof fn !== \"function\" ? __typeError(\"Function expected\") : fn;\nvar __decoratorContext = (kind, name, done, metadata, fns) => ({\n  kind: __decoratorStrings[kind],\n  name,\n  metadata,\n  addInitializer: fn => done._ ? __typeError(\"Already initialized\") : fns.push(__expectFn(fn || null))\n});\nvar __decoratorMetadata = (array, target) => __defNormalProp(target, __knownSymbol(\"metadata\"), array[3]);\nvar __runInitializers = (array, flags, self, value) => {\n  for (var i = 0, fns = array[flags >> 1], n = fns && fns.length; i < n; i++) flags & 1 ? fns[i].call(self) : value = fns[i].call(self, value);\n  return value;\n};\nvar __decorateElement = (array, flags, name, decorators, target, extra) => {\n  var fn,\n    it,\n    done,\n    ctx,\n    access,\n    k = flags & 7,\n    s = !!(flags & 8),\n    p = !!(flags & 16);\n  var j = k > 3 ? array.length + 1 : k ? s ? 1 : 2 : 0,\n    key = __decoratorStrings[k + 5];\n  var initializers = k > 3 && (array[j - 1] = []),\n    extraInitializers = array[j] || (array[j] = []);\n  var desc = k && (!p && !s && (target = target.prototype), k < 5 && (k > 3 || !p) && __getOwnPropDesc(k < 4 ? target : {\n    get [name]() {\n      return __privateGet(this, extra);\n    },\n    set [name](x) {\n      return __privateSet(this, extra, x);\n    }\n  }, name));\n  k ? p && k < 4 && __name(extra, (k > 2 ? \"set \" : k > 1 ? \"get \" : \"\") + name) : __name(target, name);\n  for (var i = decorators.length - 1; i >= 0; i--) {\n    ctx = __decoratorContext(k, name, done = {}, array[3], extraInitializers);\n    if (k) {\n      ctx.static = s, ctx.private = p, access = ctx.access = {\n        has: p ? x => __privateIn(target, x) : x => name in x\n      };\n      if (k ^ 3) access.get = p ? x => (k ^ 1 ? __privateGet : __privateMethod)(x, target, k ^ 4 ? extra : desc.get) : x => x[name];\n      if (k > 2) access.set = p ? (x, y) => __privateSet(x, target, y, k ^ 4 ? extra : desc.set) : (x, y) => x[name] = y;\n    }\n    it = (0, decorators[i])(k ? k < 4 ? p ? extra : desc[key] : k > 4 ? void 0 : {\n      get: desc.get,\n      set: desc.set\n    } : target, ctx), done._ = 1;\n    if (k ^ 4 || it === void 0) __expectFn(it) && (k > 4 ? initializers.unshift(it) : k ? p ? extra = it : desc[key] = it : target = it);else if (typeof it !== \"object\" || it === null) __typeError(\"Object expected\");else __expectFn(fn = it.get) && (desc.get = fn), __expectFn(fn = it.set) && (desc.set = fn), __expectFn(fn = it.init) && initializers.unshift(fn);\n  }\n  return k || __decoratorMetadata(array, target), desc && __defProp(target, name, desc), p ? k ^ 4 ? extra : desc : target;\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateIn = (member, obj) => Object(obj) !== obj ? __typeError('Cannot use the \"in\" operator on this value') : member.has(obj);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), setter ? setter.call(obj, value) : member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\n\n// src/index.ts\nimport { batch as batch2, effect as effect2, untracked as untracked3, signal as signal2, Signal } from \"@preact/signals-core\";\n\n// src/computed.ts\nimport { computed as computedSignal } from \"@preact/signals-core\";\nfunction computed(compute, comparator) {\n  if (comparator) {\n    let previousValue;\n    return computedSignal(() => {\n      const value = compute();\n      if (value && previousValue && comparator(previousValue, value)) {\n        return previousValue;\n      }\n      previousValue = value;\n      return value;\n    });\n  }\n  return computedSignal(compute);\n}\n\n// src/comparators.ts\nfunction deepEqual(a, b) {\n  if (Object.is(a, b)) {\n    return true;\n  }\n  if (a === null || b === null) return false;\n  if (typeof a === \"function\" && typeof b === \"function\") {\n    return a === b;\n  }\n  if (a instanceof Set && b instanceof Set) {\n    if (a.size !== b.size) {\n      return false;\n    }\n    for (const value of a) {\n      if (!b.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (Array.isArray(a)) {\n    if (!Array.isArray(b) || a.length !== b.length) {\n      return false;\n    }\n    const hasDifferentValues = a.some((value, index) => !deepEqual(value, b[index]));\n    return !hasDifferentValues;\n  }\n  if (typeof a === \"object\" && typeof b === \"object\") {\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    if (aKeys.length !== bKeys.length) return false;\n    const hasDifferentValues = aKeys.some(key => !deepEqual(a[key], b[key]));\n    return !hasDifferentValues;\n  }\n  return false;\n}\n\n// src/decorators.ts\nimport { signal } from \"@preact/signals-core\";\nfunction reactive(_ref, _) {\n  let {\n    get\n  } = _ref;\n  return {\n    init(value) {\n      return signal(value);\n    },\n    get() {\n      const current = get.call(this);\n      return current.value;\n    },\n    set(newValue) {\n      const current = get.call(this);\n      if (current.peek() === newValue) {\n        return;\n      }\n      current.value = newValue;\n    }\n  };\n}\nfunction derived(target, _) {\n  const map = /* @__PURE__ */new WeakMap();\n  return function () {\n    let result = map.get(this);\n    if (!result) {\n      result = computed(target.bind(this));\n      map.set(this, result);\n    }\n    return result.value;\n  };\n}\nfunction enumerable() {\n  let enumerable2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  return function (_value, context) {\n    context.addInitializer(function () {\n      const host = context.kind === \"field\" ? this : context.static ? this : Object.getPrototypeOf(this);\n      const descriptor = Object.getOwnPropertyDescriptor(host, context.name);\n      if (descriptor) {\n        Object.defineProperty(host, context.name, __spreadProps(__spreadValues({}, descriptor), {\n          enumerable: enumerable2\n        }));\n      }\n    });\n  };\n}\n\n// src/effects.ts\nimport { effect } from \"@preact/signals-core\";\nfunction effects() {\n  for (var _len = arguments.length, entries = new Array(_len), _key = 0; _key < _len; _key++) {\n    entries[_key] = arguments[_key];\n  }\n  const effects2 = entries.map(effect);\n  return () => effects2.forEach(cleanup => cleanup());\n}\n\n// src/history.ts\nimport { batch, untracked } from \"@preact/signals-core\";\nvar _previous_dec, _initial_dec, _current_dec, _current_dec2, _previous_dec2, _initial_dec2, _init, _initial, _a, initial_get, initial_set, _ValueHistory_instances, _previous, _b, previous_get, previous_set, _current, _c, current_get, current_set;\n_initial_dec2 = [reactive], _previous_dec2 = [reactive], _current_dec2 = [reactive], _current_dec = [enumerable()], _initial_dec = [enumerable()], _previous_dec = [enumerable()];\nvar ValueHistory = class {\n  constructor(defaultValue) {\n    let equals = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Object.is;\n    this.defaultValue = defaultValue;\n    this.equals = equals;\n    __runInitializers(_init, 5, this);\n    __privateAdd(this, _ValueHistory_instances);\n    // @ts-ignore\n    __privateAdd(this, _initial, __runInitializers(_init, 8, this)), __runInitializers(_init, 11, this);\n    // @ts-ignore\n    __privateAdd(this, _previous, __runInitializers(_init, 12, this)), __runInitializers(_init, 15, this);\n    // @ts-ignore\n    __privateAdd(this, _current, __runInitializers(_init, 16, this)), __runInitializers(_init, 19, this);\n    this.reset = this.reset.bind(this);\n    this.reset();\n  }\n  get current() {\n    return __privateGet(this, _ValueHistory_instances, current_get);\n  }\n  get initial() {\n    return __privateGet(this, _ValueHistory_instances, initial_get);\n  }\n  get previous() {\n    return __privateGet(this, _ValueHistory_instances, previous_get);\n  }\n  /** Set the current value */\n  set current(value) {\n    const current = untracked(() => __privateGet(this, _ValueHistory_instances, current_get));\n    if (value && current && this.equals(current, value)) {\n      return;\n    }\n    batch(() => {\n      if (!__privateGet(this, _ValueHistory_instances, initial_get)) {\n        __privateSet(this, _ValueHistory_instances, value, initial_set);\n      }\n      __privateSet(this, _ValueHistory_instances, current, previous_set);\n      __privateSet(this, _ValueHistory_instances, value, current_set);\n    });\n  }\n  /** Reset the state to the initial value */\n  reset() {\n    let value = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.defaultValue;\n    batch(() => {\n      __privateSet(this, _ValueHistory_instances, void 0, previous_set);\n      __privateSet(this, _ValueHistory_instances, value, initial_set);\n      __privateSet(this, _ValueHistory_instances, value, current_set);\n    });\n  }\n};\n_init = __decoratorStart(null);\n_initial = new WeakMap();\n_ValueHistory_instances = new WeakSet();\n_previous = new WeakMap();\n_current = new WeakMap();\n_a = __decorateElement(_init, 20, \"#initial\", _initial_dec2, _ValueHistory_instances, _initial), initial_get = _a.get, initial_set = _a.set;\n_b = __decorateElement(_init, 20, \"#previous\", _previous_dec2, _ValueHistory_instances, _previous), previous_get = _b.get, previous_set = _b.set;\n_c = __decorateElement(_init, 20, \"#current\", _current_dec2, _ValueHistory_instances, _current), current_get = _c.get, current_set = _c.set;\n__decorateElement(_init, 2, \"current\", _current_dec, ValueHistory);\n__decorateElement(_init, 2, \"initial\", _initial_dec, ValueHistory);\n__decorateElement(_init, 2, \"previous\", _previous_dec, ValueHistory);\n__decoratorMetadata(_init, ValueHistory);\n\n// src/snapshot.ts\nimport { untracked as untracked2 } from \"@preact/signals-core\";\nfunction snapshot(value) {\n  return untracked2(() => {\n    const output = {};\n    for (const key in value) {\n      output[key] = value[key];\n    }\n    return output;\n  });\n}\n\n// src/store.ts\nvar _store;\nvar WeakStore = class {\n  constructor() {\n    __privateAdd(this, _store, /* @__PURE__ */new WeakMap());\n  }\n  get(key, id) {\n    var _a2;\n    return key ? (_a2 = __privateGet(this, _store).get(key)) == null ? void 0 : _a2.get(id) : void 0;\n  }\n  set(key, id, value) {\n    var _a2;\n    if (!key) return;\n    if (!__privateGet(this, _store).has(key)) __privateGet(this, _store).set(key, /* @__PURE__ */new Map());\n    return (_a2 = __privateGet(this, _store).get(key)) == null ? void 0 : _a2.set(id, value);\n  }\n  clear(key) {\n    var _a2;\n    return key ? (_a2 = __privateGet(this, _store).get(key)) == null ? void 0 : _a2.clear() : void 0;\n  }\n};\n_store = new WeakMap();\nexport { Signal, ValueHistory, WeakStore, batch2 as batch, computed, deepEqual, derived, effect2 as effect, effects, enumerable, reactive, signal2 as signal, snapshot, untracked3 as untracked };", "map": {"version": 3, "names": ["__create", "Object", "create", "__defProp", "defineProperty", "__defProps", "defineProperties", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropDescs", "getOwnPropertyDescriptors", "__getOwnPropSymbols", "getOwnPropertySymbols", "__hasOwnProp", "prototype", "hasOwnProperty", "__propIsEnum", "propertyIsEnumerable", "__knownSymbol", "name", "symbol", "Symbol", "for", "__typeError", "msg", "TypeError", "__defNormalProp", "obj", "key", "value", "enumerable", "configurable", "writable", "__spreadValues", "a", "b", "prop", "call", "__spreadProps", "__name", "target", "__decoratorStart", "base", "_a2", "__decoratorStrings", "__expectFn", "fn", "__decoratorContext", "kind", "done", "metadata", "fns", "addInitializer", "_", "push", "__decoratorMetadata", "array", "__runInitializers", "flags", "self", "i", "n", "length", "__decorateElement", "decorators", "extra", "it", "ctx", "access", "k", "s", "p", "j", "initializers", "extraInitializers", "desc", "__privateGet", "x", "__privateSet", "static", "private", "has", "__privateIn", "get", "__privateMethod", "set", "y", "unshift", "init", "__access<PERSON>heck", "member", "getter", "__privateAdd", "WeakSet", "add", "setter", "method", "batch", "batch2", "effect", "effect2", "untracked", "untracked3", "signal", "signal2", "Signal", "computed", "computedSignal", "compute", "comparator", "previousValue", "deepEqual", "is", "Set", "size", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "index", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "reactive", "_ref", "current", "newValue", "peek", "derived", "map", "WeakMap", "result", "bind", "enumerable2", "arguments", "undefined", "_value", "context", "host", "getPrototypeOf", "descriptor", "effects", "_len", "entries", "_key", "effects2", "for<PERSON>ach", "cleanup", "_previous_dec", "_initial_dec", "_current_dec", "_current_dec2", "_previous_dec2", "_initial_dec2", "_init", "_initial", "_a", "initial_get", "initial_set", "_ValueHistory_instances", "_previous", "_b", "previous_get", "previous_set", "_current", "_c", "current_get", "current_set", "ValueHistory", "constructor", "defaultValue", "equals", "reset", "initial", "previous", "untracked2", "snapshot", "output", "_store", "WeakStore", "id", "Map", "clear"], "sources": ["C:/laragon/www/frontend/node_modules/@dnd-kit/state/dist/index.mjs"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __knownSymbol = (name, symbol) => (symbol = Symbol[name]) ? symbol : Symbol.for(\"Symbol.\" + name);\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n};\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __decoratorStart = (base) => {\n  var _a2;\n  return [, , , __create((_a2 = base == null ? void 0 : base[__knownSymbol(\"metadata\")]) != null ? _a2 : null)];\n};\nvar __decoratorStrings = [\"class\", \"method\", \"getter\", \"setter\", \"accessor\", \"field\", \"value\", \"get\", \"set\"];\nvar __expectFn = (fn) => fn !== void 0 && typeof fn !== \"function\" ? __typeError(\"Function expected\") : fn;\nvar __decoratorContext = (kind, name, done, metadata, fns) => ({ kind: __decoratorStrings[kind], name, metadata, addInitializer: (fn) => done._ ? __typeError(\"Already initialized\") : fns.push(__expectFn(fn || null)) });\nvar __decoratorMetadata = (array, target) => __defNormalProp(target, __knownSymbol(\"metadata\"), array[3]);\nvar __runInitializers = (array, flags, self, value) => {\n  for (var i = 0, fns = array[flags >> 1], n = fns && fns.length; i < n; i++) flags & 1 ? fns[i].call(self) : value = fns[i].call(self, value);\n  return value;\n};\nvar __decorateElement = (array, flags, name, decorators, target, extra) => {\n  var fn, it, done, ctx, access, k = flags & 7, s = !!(flags & 8), p = !!(flags & 16);\n  var j = k > 3 ? array.length + 1 : k ? s ? 1 : 2 : 0, key = __decoratorStrings[k + 5];\n  var initializers = k > 3 && (array[j - 1] = []), extraInitializers = array[j] || (array[j] = []);\n  var desc = k && (!p && !s && (target = target.prototype), k < 5 && (k > 3 || !p) && __getOwnPropDesc(k < 4 ? target : { get [name]() {\n    return __privateGet(this, extra);\n  }, set [name](x) {\n    return __privateSet(this, extra, x);\n  } }, name));\n  k ? p && k < 4 && __name(extra, (k > 2 ? \"set \" : k > 1 ? \"get \" : \"\") + name) : __name(target, name);\n  for (var i = decorators.length - 1; i >= 0; i--) {\n    ctx = __decoratorContext(k, name, done = {}, array[3], extraInitializers);\n    if (k) {\n      ctx.static = s, ctx.private = p, access = ctx.access = { has: p ? (x) => __privateIn(target, x) : (x) => name in x };\n      if (k ^ 3) access.get = p ? (x) => (k ^ 1 ? __privateGet : __privateMethod)(x, target, k ^ 4 ? extra : desc.get) : (x) => x[name];\n      if (k > 2) access.set = p ? (x, y) => __privateSet(x, target, y, k ^ 4 ? extra : desc.set) : (x, y) => x[name] = y;\n    }\n    it = (0, decorators[i])(k ? k < 4 ? p ? extra : desc[key] : k > 4 ? void 0 : { get: desc.get, set: desc.set } : target, ctx), done._ = 1;\n    if (k ^ 4 || it === void 0) __expectFn(it) && (k > 4 ? initializers.unshift(it) : k ? p ? extra = it : desc[key] = it : target = it);\n    else if (typeof it !== \"object\" || it === null) __typeError(\"Object expected\");\n    else __expectFn(fn = it.get) && (desc.get = fn), __expectFn(fn = it.set) && (desc.set = fn), __expectFn(fn = it.init) && initializers.unshift(fn);\n  }\n  return k || __decoratorMetadata(array, target), desc && __defProp(target, name, desc), p ? k ^ 4 ? extra : desc : target;\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateIn = (member, obj) => Object(obj) !== obj ? __typeError('Cannot use the \"in\" operator on this value') : member.has(obj);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), setter ? setter.call(obj, value) : member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\n\n// src/index.ts\nimport {\n  batch as batch2,\n  effect as effect2,\n  untracked as untracked3,\n  signal as signal2,\n  Signal\n} from \"@preact/signals-core\";\n\n// src/computed.ts\nimport {\n  computed as computedSignal\n} from \"@preact/signals-core\";\nfunction computed(compute, comparator) {\n  if (comparator) {\n    let previousValue;\n    return computedSignal(() => {\n      const value = compute();\n      if (value && previousValue && comparator(previousValue, value)) {\n        return previousValue;\n      }\n      previousValue = value;\n      return value;\n    });\n  }\n  return computedSignal(compute);\n}\n\n// src/comparators.ts\nfunction deepEqual(a, b) {\n  if (Object.is(a, b)) {\n    return true;\n  }\n  if (a === null || b === null) return false;\n  if (typeof a === \"function\" && typeof b === \"function\") {\n    return a === b;\n  }\n  if (a instanceof Set && b instanceof Set) {\n    if (a.size !== b.size) {\n      return false;\n    }\n    for (const value of a) {\n      if (!b.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (Array.isArray(a)) {\n    if (!Array.isArray(b) || a.length !== b.length) {\n      return false;\n    }\n    const hasDifferentValues = a.some(\n      (value, index) => !deepEqual(value, b[index])\n    );\n    return !hasDifferentValues;\n  }\n  if (typeof a === \"object\" && typeof b === \"object\") {\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    if (aKeys.length !== bKeys.length) return false;\n    const hasDifferentValues = aKeys.some(\n      (key) => !deepEqual(a[key], b[key])\n    );\n    return !hasDifferentValues;\n  }\n  return false;\n}\n\n// src/decorators.ts\nimport { signal } from \"@preact/signals-core\";\nfunction reactive({ get }, _) {\n  return {\n    init(value) {\n      return signal(value);\n    },\n    get() {\n      const current = get.call(this);\n      return current.value;\n    },\n    set(newValue) {\n      const current = get.call(this);\n      if (current.peek() === newValue) {\n        return;\n      }\n      current.value = newValue;\n    }\n  };\n}\nfunction derived(target, _) {\n  const map = /* @__PURE__ */ new WeakMap();\n  return function() {\n    let result = map.get(this);\n    if (!result) {\n      result = computed(target.bind(this));\n      map.set(this, result);\n    }\n    return result.value;\n  };\n}\nfunction enumerable(enumerable2 = true) {\n  return function(_value, context) {\n    context.addInitializer(function() {\n      const host = context.kind === \"field\" ? this : context.static ? this : Object.getPrototypeOf(this);\n      const descriptor = Object.getOwnPropertyDescriptor(host, context.name);\n      if (descriptor) {\n        Object.defineProperty(host, context.name, __spreadProps(__spreadValues({}, descriptor), { enumerable: enumerable2 }));\n      }\n    });\n  };\n}\n\n// src/effects.ts\nimport { effect } from \"@preact/signals-core\";\nfunction effects(...entries) {\n  const effects2 = entries.map(effect);\n  return () => effects2.forEach((cleanup) => cleanup());\n}\n\n// src/history.ts\nimport { batch, untracked } from \"@preact/signals-core\";\nvar _previous_dec, _initial_dec, _current_dec, _current_dec2, _previous_dec2, _initial_dec2, _init, _initial, _a, initial_get, initial_set, _ValueHistory_instances, _previous, _b, previous_get, previous_set, _current, _c, current_get, current_set;\n_initial_dec2 = [reactive], _previous_dec2 = [reactive], _current_dec2 = [reactive], _current_dec = [enumerable()], _initial_dec = [enumerable()], _previous_dec = [enumerable()];\nvar ValueHistory = class {\n  constructor(defaultValue, equals = Object.is) {\n    this.defaultValue = defaultValue;\n    this.equals = equals;\n    __runInitializers(_init, 5, this);\n    __privateAdd(this, _ValueHistory_instances);\n    // @ts-ignore\n    __privateAdd(this, _initial, __runInitializers(_init, 8, this)), __runInitializers(_init, 11, this);\n    // @ts-ignore\n    __privateAdd(this, _previous, __runInitializers(_init, 12, this)), __runInitializers(_init, 15, this);\n    // @ts-ignore\n    __privateAdd(this, _current, __runInitializers(_init, 16, this)), __runInitializers(_init, 19, this);\n    this.reset = this.reset.bind(this);\n    this.reset();\n  }\n  get current() {\n    return __privateGet(this, _ValueHistory_instances, current_get);\n  }\n  get initial() {\n    return __privateGet(this, _ValueHistory_instances, initial_get);\n  }\n  get previous() {\n    return __privateGet(this, _ValueHistory_instances, previous_get);\n  }\n  /** Set the current value */\n  set current(value) {\n    const current = untracked(() => __privateGet(this, _ValueHistory_instances, current_get));\n    if (value && current && this.equals(current, value)) {\n      return;\n    }\n    batch(() => {\n      if (!__privateGet(this, _ValueHistory_instances, initial_get)) {\n        __privateSet(this, _ValueHistory_instances, value, initial_set);\n      }\n      __privateSet(this, _ValueHistory_instances, current, previous_set);\n      __privateSet(this, _ValueHistory_instances, value, current_set);\n    });\n  }\n  /** Reset the state to the initial value */\n  reset(value = this.defaultValue) {\n    batch(() => {\n      __privateSet(this, _ValueHistory_instances, void 0, previous_set);\n      __privateSet(this, _ValueHistory_instances, value, initial_set);\n      __privateSet(this, _ValueHistory_instances, value, current_set);\n    });\n  }\n};\n_init = __decoratorStart(null);\n_initial = new WeakMap();\n_ValueHistory_instances = new WeakSet();\n_previous = new WeakMap();\n_current = new WeakMap();\n_a = __decorateElement(_init, 20, \"#initial\", _initial_dec2, _ValueHistory_instances, _initial), initial_get = _a.get, initial_set = _a.set;\n_b = __decorateElement(_init, 20, \"#previous\", _previous_dec2, _ValueHistory_instances, _previous), previous_get = _b.get, previous_set = _b.set;\n_c = __decorateElement(_init, 20, \"#current\", _current_dec2, _ValueHistory_instances, _current), current_get = _c.get, current_set = _c.set;\n__decorateElement(_init, 2, \"current\", _current_dec, ValueHistory);\n__decorateElement(_init, 2, \"initial\", _initial_dec, ValueHistory);\n__decorateElement(_init, 2, \"previous\", _previous_dec, ValueHistory);\n__decoratorMetadata(_init, ValueHistory);\n\n// src/snapshot.ts\nimport { untracked as untracked2 } from \"@preact/signals-core\";\nfunction snapshot(value) {\n  return untracked2(() => {\n    const output = {};\n    for (const key in value) {\n      output[key] = value[key];\n    }\n    return output;\n  });\n}\n\n// src/store.ts\nvar _store;\nvar WeakStore = class {\n  constructor() {\n    __privateAdd(this, _store, /* @__PURE__ */ new WeakMap());\n  }\n  get(key, id) {\n    var _a2;\n    return key ? (_a2 = __privateGet(this, _store).get(key)) == null ? void 0 : _a2.get(id) : void 0;\n  }\n  set(key, id, value) {\n    var _a2;\n    if (!key) return;\n    if (!__privateGet(this, _store).has(key)) __privateGet(this, _store).set(key, /* @__PURE__ */ new Map());\n    return (_a2 = __privateGet(this, _store).get(key)) == null ? void 0 : _a2.set(id, value);\n  }\n  clear(key) {\n    var _a2;\n    return key ? (_a2 = __privateGet(this, _store).get(key)) == null ? void 0 : _a2.clear() : void 0;\n  }\n};\n_store = new WeakMap();\nexport {\n  Signal,\n  ValueHistory,\n  WeakStore,\n  batch2 as batch,\n  computed,\n  deepEqual,\n  derived,\n  effect2 as effect,\n  effects,\n  enumerable,\n  reactive,\n  signal2 as signal,\n  snapshot,\n  untracked3 as untracked\n};\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM;AAC5B,IAAIC,SAAS,GAAGF,MAAM,CAACG,cAAc;AACrC,IAAIC,UAAU,GAAGJ,MAAM,CAACK,gBAAgB;AACxC,IAAIC,gBAAgB,GAAGN,MAAM,CAACO,wBAAwB;AACtD,IAAIC,iBAAiB,GAAGR,MAAM,CAACS,yBAAyB;AACxD,IAAIC,mBAAmB,GAAGV,MAAM,CAACW,qBAAqB;AACtD,IAAIC,YAAY,GAAGZ,MAAM,CAACa,SAAS,CAACC,cAAc;AAClD,IAAIC,YAAY,GAAGf,MAAM,CAACa,SAAS,CAACG,oBAAoB;AACxD,IAAIC,aAAa,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK,CAACA,MAAM,GAAGC,MAAM,CAACF,IAAI,CAAC,IAAIC,MAAM,GAAGC,MAAM,CAACC,GAAG,CAAC,SAAS,GAAGH,IAAI,CAAC;AACrG,IAAII,WAAW,GAAIC,GAAG,IAAK;EACzB,MAAMC,SAAS,CAACD,GAAG,CAAC;AACtB,CAAC;AACD,IAAIE,eAAe,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAID,GAAG,GAAGxB,SAAS,CAACwB,GAAG,EAAEC,GAAG,EAAE;EAAEE,UAAU,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEH;AAAM,CAAC,CAAC,GAAGF,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;AAC/J,IAAII,cAAc,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC7B,KAAK,IAAIC,IAAI,IAAID,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC,EAC5B,IAAItB,YAAY,CAACwB,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC,IAAIzB,mBAAmB,EACrB,KAAK,IAAIyB,IAAI,IAAIzB,mBAAmB,CAACwB,CAAC,CAAC,EAAE;IACvC,IAAInB,YAAY,CAACqB,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC;EACF,OAAOF,CAAC;AACV,CAAC;AACD,IAAII,aAAa,GAAGA,CAACJ,CAAC,EAAEC,CAAC,KAAK9B,UAAU,CAAC6B,CAAC,EAAEzB,iBAAiB,CAAC0B,CAAC,CAAC,CAAC;AACjE,IAAII,MAAM,GAAGA,CAACC,MAAM,EAAEX,KAAK,KAAK1B,SAAS,CAACqC,MAAM,EAAE,MAAM,EAAE;EAAEX,KAAK;EAAEE,YAAY,EAAE;AAAK,CAAC,CAAC;AACxF,IAAIU,gBAAgB,GAAIC,IAAI,IAAK;EAC/B,IAAIC,GAAG;EACP,OAAO,KAAO3C,QAAQ,CAAC,CAAC2C,GAAG,GAAGD,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACxB,aAAa,CAAC,UAAU,CAAC,CAAC,KAAK,IAAI,GAAGyB,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/G,CAAC;AACD,IAAIC,kBAAkB,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;AAC5G,IAAIC,UAAU,GAAIC,EAAE,IAAKA,EAAE,KAAK,KAAK,CAAC,IAAI,OAAOA,EAAE,KAAK,UAAU,GAAGvB,WAAW,CAAC,mBAAmB,CAAC,GAAGuB,EAAE;AAC1G,IAAIC,kBAAkB,GAAGA,CAACC,IAAI,EAAE7B,IAAI,EAAE8B,IAAI,EAAEC,QAAQ,EAAEC,GAAG,MAAM;EAAEH,IAAI,EAAEJ,kBAAkB,CAACI,IAAI,CAAC;EAAE7B,IAAI;EAAE+B,QAAQ;EAAEE,cAAc,EAAGN,EAAE,IAAKG,IAAI,CAACI,CAAC,GAAG9B,WAAW,CAAC,qBAAqB,CAAC,GAAG4B,GAAG,CAACG,IAAI,CAACT,UAAU,CAACC,EAAE,IAAI,IAAI,CAAC;AAAE,CAAC,CAAC;AAC1N,IAAIS,mBAAmB,GAAGA,CAACC,KAAK,EAAEhB,MAAM,KAAKd,eAAe,CAACc,MAAM,EAAEtB,aAAa,CAAC,UAAU,CAAC,EAAEsC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzG,IAAIC,iBAAiB,GAAGA,CAACD,KAAK,EAAEE,KAAK,EAAEC,IAAI,EAAE9B,KAAK,KAAK;EACrD,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAET,GAAG,GAAGK,KAAK,CAACE,KAAK,IAAI,CAAC,CAAC,EAAEG,CAAC,GAAGV,GAAG,IAAIA,GAAG,CAACW,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAEF,KAAK,GAAG,CAAC,GAAGP,GAAG,CAACS,CAAC,CAAC,CAACvB,IAAI,CAACsB,IAAI,CAAC,GAAG9B,KAAK,GAAGsB,GAAG,CAACS,CAAC,CAAC,CAACvB,IAAI,CAACsB,IAAI,EAAE9B,KAAK,CAAC;EAC5I,OAAOA,KAAK;AACd,CAAC;AACD,IAAIkC,iBAAiB,GAAGA,CAACP,KAAK,EAAEE,KAAK,EAAEvC,IAAI,EAAE6C,UAAU,EAAExB,MAAM,EAAEyB,KAAK,KAAK;EACzE,IAAInB,EAAE;IAAEoB,EAAE;IAAEjB,IAAI;IAAEkB,GAAG;IAAEC,MAAM;IAAEC,CAAC,GAAGX,KAAK,GAAG,CAAC;IAAEY,CAAC,GAAG,CAAC,EAAEZ,KAAK,GAAG,CAAC,CAAC;IAAEa,CAAC,GAAG,CAAC,EAAEb,KAAK,GAAG,EAAE,CAAC;EACnF,IAAIc,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGb,KAAK,CAACM,MAAM,GAAG,CAAC,GAAGO,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE1C,GAAG,GAAGgB,kBAAkB,CAACyB,CAAC,GAAG,CAAC,CAAC;EACrF,IAAII,YAAY,GAAGJ,CAAC,GAAG,CAAC,KAAKb,KAAK,CAACgB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IAAEE,iBAAiB,GAAGlB,KAAK,CAACgB,CAAC,CAAC,KAAKhB,KAAK,CAACgB,CAAC,CAAC,GAAG,EAAE,CAAC;EAChG,IAAIG,IAAI,GAAGN,CAAC,KAAK,CAACE,CAAC,IAAI,CAACD,CAAC,KAAK9B,MAAM,GAAGA,MAAM,CAAC1B,SAAS,CAAC,EAAEuD,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAG,CAAC,IAAI,CAACE,CAAC,CAAC,IAAIhE,gBAAgB,CAAC8D,CAAC,GAAG,CAAC,GAAG7B,MAAM,GAAG;IAAE,KAAKrB,IAAI,IAAI;MACnI,OAAOyD,YAAY,CAAC,IAAI,EAAEX,KAAK,CAAC;IAClC,CAAC;IAAE,KAAK9C,IAAI,EAAE0D,CAAC,EAAE;MACf,OAAOC,YAAY,CAAC,IAAI,EAAEb,KAAK,EAAEY,CAAC,CAAC;IACrC;EAAE,CAAC,EAAE1D,IAAI,CAAC,CAAC;EACXkD,CAAC,GAAGE,CAAC,IAAIF,CAAC,GAAG,CAAC,IAAI9B,MAAM,CAAC0B,KAAK,EAAE,CAACI,CAAC,GAAG,CAAC,GAAG,MAAM,GAAGA,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,EAAE,IAAIlD,IAAI,CAAC,GAAGoB,MAAM,CAACC,MAAM,EAAErB,IAAI,CAAC;EACrG,KAAK,IAAIyC,CAAC,GAAGI,UAAU,CAACF,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC/CO,GAAG,GAAGpB,kBAAkB,CAACsB,CAAC,EAAElD,IAAI,EAAE8B,IAAI,GAAG,CAAC,CAAC,EAAEO,KAAK,CAAC,CAAC,CAAC,EAAEkB,iBAAiB,CAAC;IACzE,IAAIL,CAAC,EAAE;MACLF,GAAG,CAACY,MAAM,GAAGT,CAAC,EAAEH,GAAG,CAACa,OAAO,GAAGT,CAAC,EAAEH,MAAM,GAAGD,GAAG,CAACC,MAAM,GAAG;QAAEa,GAAG,EAAEV,CAAC,GAAIM,CAAC,IAAKK,WAAW,CAAC1C,MAAM,EAAEqC,CAAC,CAAC,GAAIA,CAAC,IAAK1D,IAAI,IAAI0D;MAAE,CAAC;MACpH,IAAIR,CAAC,GAAG,CAAC,EAAED,MAAM,CAACe,GAAG,GAAGZ,CAAC,GAAIM,CAAC,IAAK,CAACR,CAAC,GAAG,CAAC,GAAGO,YAAY,GAAGQ,eAAe,EAAEP,CAAC,EAAErC,MAAM,EAAE6B,CAAC,GAAG,CAAC,GAAGJ,KAAK,GAAGU,IAAI,CAACQ,GAAG,CAAC,GAAIN,CAAC,IAAKA,CAAC,CAAC1D,IAAI,CAAC;MACjI,IAAIkD,CAAC,GAAG,CAAC,EAAED,MAAM,CAACiB,GAAG,GAAGd,CAAC,GAAG,CAACM,CAAC,EAAES,CAAC,KAAKR,YAAY,CAACD,CAAC,EAAErC,MAAM,EAAE8C,CAAC,EAAEjB,CAAC,GAAG,CAAC,GAAGJ,KAAK,GAAGU,IAAI,CAACU,GAAG,CAAC,GAAG,CAACR,CAAC,EAAES,CAAC,KAAKT,CAAC,CAAC1D,IAAI,CAAC,GAAGmE,CAAC;IACpH;IACApB,EAAE,GAAG,CAAC,CAAC,EAAEF,UAAU,CAACJ,CAAC,CAAC,EAAES,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGE,CAAC,GAAGN,KAAK,GAAGU,IAAI,CAAC/C,GAAG,CAAC,GAAGyC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG;MAAEc,GAAG,EAAER,IAAI,CAACQ,GAAG;MAAEE,GAAG,EAAEV,IAAI,CAACU;IAAI,CAAC,GAAG7C,MAAM,EAAE2B,GAAG,CAAC,EAAElB,IAAI,CAACI,CAAC,GAAG,CAAC;IACxI,IAAIgB,CAAC,GAAG,CAAC,IAAIH,EAAE,KAAK,KAAK,CAAC,EAAErB,UAAU,CAACqB,EAAE,CAAC,KAAKG,CAAC,GAAG,CAAC,GAAGI,YAAY,CAACc,OAAO,CAACrB,EAAE,CAAC,GAAGG,CAAC,GAAGE,CAAC,GAAGN,KAAK,GAAGC,EAAE,GAAGS,IAAI,CAAC/C,GAAG,CAAC,GAAGsC,EAAE,GAAG1B,MAAM,GAAG0B,EAAE,CAAC,CAAC,KAChI,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,KAAK,IAAI,EAAE3C,WAAW,CAAC,iBAAiB,CAAC,CAAC,KAC1EsB,UAAU,CAACC,EAAE,GAAGoB,EAAE,CAACiB,GAAG,CAAC,KAAKR,IAAI,CAACQ,GAAG,GAAGrC,EAAE,CAAC,EAAED,UAAU,CAACC,EAAE,GAAGoB,EAAE,CAACmB,GAAG,CAAC,KAAKV,IAAI,CAACU,GAAG,GAAGvC,EAAE,CAAC,EAAED,UAAU,CAACC,EAAE,GAAGoB,EAAE,CAACsB,IAAI,CAAC,IAAIf,YAAY,CAACc,OAAO,CAACzC,EAAE,CAAC;EACnJ;EACA,OAAOuB,CAAC,IAAId,mBAAmB,CAACC,KAAK,EAAEhB,MAAM,CAAC,EAAEmC,IAAI,IAAIxE,SAAS,CAACqC,MAAM,EAAErB,IAAI,EAAEwD,IAAI,CAAC,EAAEJ,CAAC,GAAGF,CAAC,GAAG,CAAC,GAAGJ,KAAK,GAAGU,IAAI,GAAGnC,MAAM;AAC1H,CAAC;AACD,IAAIiD,aAAa,GAAGA,CAAC9D,GAAG,EAAE+D,MAAM,EAAElE,GAAG,KAAKkE,MAAM,CAACT,GAAG,CAACtD,GAAG,CAAC,IAAIJ,WAAW,CAAC,SAAS,GAAGC,GAAG,CAAC;AACzF,IAAI0D,WAAW,GAAGA,CAACQ,MAAM,EAAE/D,GAAG,KAAK1B,MAAM,CAAC0B,GAAG,CAAC,KAAKA,GAAG,GAAGJ,WAAW,CAAC,4CAA4C,CAAC,GAAGmE,MAAM,CAACT,GAAG,CAACtD,GAAG,CAAC;AACpI,IAAIiD,YAAY,GAAGA,CAACjD,GAAG,EAAE+D,MAAM,EAAEC,MAAM,MAAMF,aAAa,CAAC9D,GAAG,EAAE+D,MAAM,EAAE,yBAAyB,CAAC,EAAEC,MAAM,GAAGA,MAAM,CAACtD,IAAI,CAACV,GAAG,CAAC,GAAG+D,MAAM,CAACP,GAAG,CAACxD,GAAG,CAAC,CAAC;AAChJ,IAAIiE,YAAY,GAAGA,CAACjE,GAAG,EAAE+D,MAAM,EAAE7D,KAAK,KAAK6D,MAAM,CAACT,GAAG,CAACtD,GAAG,CAAC,GAAGJ,WAAW,CAAC,mDAAmD,CAAC,GAAGmE,MAAM,YAAYG,OAAO,GAAGH,MAAM,CAACI,GAAG,CAACnE,GAAG,CAAC,GAAG+D,MAAM,CAACL,GAAG,CAAC1D,GAAG,EAAEE,KAAK,CAAC;AACpM,IAAIiD,YAAY,GAAGA,CAACnD,GAAG,EAAE+D,MAAM,EAAE7D,KAAK,EAAEkE,MAAM,MAAMN,aAAa,CAAC9D,GAAG,EAAE+D,MAAM,EAAE,wBAAwB,CAAC,EAAEK,MAAM,GAAGA,MAAM,CAAC1D,IAAI,CAACV,GAAG,EAAEE,KAAK,CAAC,GAAG6D,MAAM,CAACL,GAAG,CAAC1D,GAAG,EAAEE,KAAK,CAAC,EAAEA,KAAK,CAAC;AAC3K,IAAIuD,eAAe,GAAGA,CAACzD,GAAG,EAAE+D,MAAM,EAAEM,MAAM,MAAMP,aAAa,CAAC9D,GAAG,EAAE+D,MAAM,EAAE,uBAAuB,CAAC,EAAEM,MAAM,CAAC;;AAE5G;AACA,SACEC,KAAK,IAAIC,MAAM,EACfC,MAAM,IAAIC,OAAO,EACjBC,SAAS,IAAIC,UAAU,EACvBC,MAAM,IAAIC,OAAO,EACjBC,MAAM,QACD,sBAAsB;;AAE7B;AACA,SACEC,QAAQ,IAAIC,cAAc,QACrB,sBAAsB;AAC7B,SAASD,QAAQA,CAACE,OAAO,EAAEC,UAAU,EAAE;EACrC,IAAIA,UAAU,EAAE;IACd,IAAIC,aAAa;IACjB,OAAOH,cAAc,CAAC,MAAM;MAC1B,MAAM9E,KAAK,GAAG+E,OAAO,CAAC,CAAC;MACvB,IAAI/E,KAAK,IAAIiF,aAAa,IAAID,UAAU,CAACC,aAAa,EAAEjF,KAAK,CAAC,EAAE;QAC9D,OAAOiF,aAAa;MACtB;MACAA,aAAa,GAAGjF,KAAK;MACrB,OAAOA,KAAK;IACd,CAAC,CAAC;EACJ;EACA,OAAO8E,cAAc,CAACC,OAAO,CAAC;AAChC;;AAEA;AACA,SAASG,SAASA,CAAC7E,CAAC,EAAEC,CAAC,EAAE;EACvB,IAAIlC,MAAM,CAAC+G,EAAE,CAAC9E,CAAC,EAAEC,CAAC,CAAC,EAAE;IACnB,OAAO,IAAI;EACb;EACA,IAAID,CAAC,KAAK,IAAI,IAAIC,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK;EAC1C,IAAI,OAAOD,CAAC,KAAK,UAAU,IAAI,OAAOC,CAAC,KAAK,UAAU,EAAE;IACtD,OAAOD,CAAC,KAAKC,CAAC;EAChB;EACA,IAAID,CAAC,YAAY+E,GAAG,IAAI9E,CAAC,YAAY8E,GAAG,EAAE;IACxC,IAAI/E,CAAC,CAACgF,IAAI,KAAK/E,CAAC,CAAC+E,IAAI,EAAE;MACrB,OAAO,KAAK;IACd;IACA,KAAK,MAAMrF,KAAK,IAAIK,CAAC,EAAE;MACrB,IAAI,CAACC,CAAC,CAAC8C,GAAG,CAACpD,KAAK,CAAC,EAAE;QACjB,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EACA,IAAIsF,KAAK,CAACC,OAAO,CAAClF,CAAC,CAAC,EAAE;IACpB,IAAI,CAACiF,KAAK,CAACC,OAAO,CAACjF,CAAC,CAAC,IAAID,CAAC,CAAC4B,MAAM,KAAK3B,CAAC,CAAC2B,MAAM,EAAE;MAC9C,OAAO,KAAK;IACd;IACA,MAAMuD,kBAAkB,GAAGnF,CAAC,CAACoF,IAAI,CAC/B,CAACzF,KAAK,EAAE0F,KAAK,KAAK,CAACR,SAAS,CAAClF,KAAK,EAAEM,CAAC,CAACoF,KAAK,CAAC,CAC9C,CAAC;IACD,OAAO,CAACF,kBAAkB;EAC5B;EACA,IAAI,OAAOnF,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IAClD,MAAMqF,KAAK,GAAGvH,MAAM,CAACwH,IAAI,CAACvF,CAAC,CAAC;IAC5B,MAAMwF,KAAK,GAAGzH,MAAM,CAACwH,IAAI,CAACtF,CAAC,CAAC;IAC5B,IAAIqF,KAAK,CAAC1D,MAAM,KAAK4D,KAAK,CAAC5D,MAAM,EAAE,OAAO,KAAK;IAC/C,MAAMuD,kBAAkB,GAAGG,KAAK,CAACF,IAAI,CAClC1F,GAAG,IAAK,CAACmF,SAAS,CAAC7E,CAAC,CAACN,GAAG,CAAC,EAAEO,CAAC,CAACP,GAAG,CAAC,CACpC,CAAC;IACD,OAAO,CAACyF,kBAAkB;EAC5B;EACA,OAAO,KAAK;AACd;;AAEA;AACA,SAASd,MAAM,QAAQ,sBAAsB;AAC7C,SAASoB,QAAQA,CAAAC,IAAA,EAAUvE,CAAC,EAAE;EAAA,IAAZ;IAAE8B;EAAI,CAAC,GAAAyC,IAAA;EACvB,OAAO;IACLpC,IAAIA,CAAC3D,KAAK,EAAE;MACV,OAAO0E,MAAM,CAAC1E,KAAK,CAAC;IACtB,CAAC;IACDsD,GAAGA,CAAA,EAAG;MACJ,MAAM0C,OAAO,GAAG1C,GAAG,CAAC9C,IAAI,CAAC,IAAI,CAAC;MAC9B,OAAOwF,OAAO,CAAChG,KAAK;IACtB,CAAC;IACDwD,GAAGA,CAACyC,QAAQ,EAAE;MACZ,MAAMD,OAAO,GAAG1C,GAAG,CAAC9C,IAAI,CAAC,IAAI,CAAC;MAC9B,IAAIwF,OAAO,CAACE,IAAI,CAAC,CAAC,KAAKD,QAAQ,EAAE;QAC/B;MACF;MACAD,OAAO,CAAChG,KAAK,GAAGiG,QAAQ;IAC1B;EACF,CAAC;AACH;AACA,SAASE,OAAOA,CAACxF,MAAM,EAAEa,CAAC,EAAE;EAC1B,MAAM4E,GAAG,GAAG,eAAgB,IAAIC,OAAO,CAAC,CAAC;EACzC,OAAO,YAAW;IAChB,IAAIC,MAAM,GAAGF,GAAG,CAAC9C,GAAG,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACgD,MAAM,EAAE;MACXA,MAAM,GAAGzB,QAAQ,CAAClE,MAAM,CAAC4F,IAAI,CAAC,IAAI,CAAC,CAAC;MACpCH,GAAG,CAAC5C,GAAG,CAAC,IAAI,EAAE8C,MAAM,CAAC;IACvB;IACA,OAAOA,MAAM,CAACtG,KAAK;EACrB,CAAC;AACH;AACA,SAASC,UAAUA,CAAA,EAAqB;EAAA,IAApBuG,WAAW,GAAAC,SAAA,CAAAxE,MAAA,QAAAwE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;EACpC,OAAO,UAASE,MAAM,EAAEC,OAAO,EAAE;IAC/BA,OAAO,CAACrF,cAAc,CAAC,YAAW;MAChC,MAAMsF,IAAI,GAAGD,OAAO,CAACzF,IAAI,KAAK,OAAO,GAAG,IAAI,GAAGyF,OAAO,CAAC1D,MAAM,GAAG,IAAI,GAAG9E,MAAM,CAAC0I,cAAc,CAAC,IAAI,CAAC;MAClG,MAAMC,UAAU,GAAG3I,MAAM,CAACO,wBAAwB,CAACkI,IAAI,EAAED,OAAO,CAACtH,IAAI,CAAC;MACtE,IAAIyH,UAAU,EAAE;QACd3I,MAAM,CAACG,cAAc,CAACsI,IAAI,EAAED,OAAO,CAACtH,IAAI,EAAEmB,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAE2G,UAAU,CAAC,EAAE;UAAE9G,UAAU,EAAEuG;QAAY,CAAC,CAAC,CAAC;MACvH;IACF,CAAC,CAAC;EACJ,CAAC;AACH;;AAEA;AACA,SAASlC,MAAM,QAAQ,sBAAsB;AAC7C,SAAS0C,OAAOA,CAAA,EAAa;EAAA,SAAAC,IAAA,GAAAR,SAAA,CAAAxE,MAAA,EAATiF,OAAO,OAAA5B,KAAA,CAAA2B,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;IAAPD,OAAO,CAAAC,IAAA,IAAAV,SAAA,CAAAU,IAAA;EAAA;EACzB,MAAMC,QAAQ,GAAGF,OAAO,CAACd,GAAG,CAAC9B,MAAM,CAAC;EACpC,OAAO,MAAM8C,QAAQ,CAACC,OAAO,CAAEC,OAAO,IAAKA,OAAO,CAAC,CAAC,CAAC;AACvD;;AAEA;AACA,SAASlD,KAAK,EAAEI,SAAS,QAAQ,sBAAsB;AACvD,IAAI+C,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,aAAa,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,WAAW,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,EAAE,EAAEC,YAAY,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,WAAW,EAAEC,WAAW;AACtPd,aAAa,GAAG,CAAC9B,QAAQ,CAAC,EAAE6B,cAAc,GAAG,CAAC7B,QAAQ,CAAC,EAAE4B,aAAa,GAAG,CAAC5B,QAAQ,CAAC,EAAE2B,YAAY,GAAG,CAACxH,UAAU,CAAC,CAAC,CAAC,EAAEuH,YAAY,GAAG,CAACvH,UAAU,CAAC,CAAC,CAAC,EAAEsH,aAAa,GAAG,CAACtH,UAAU,CAAC,CAAC,CAAC;AACjL,IAAI0I,YAAY,GAAG,MAAM;EACvBC,WAAWA,CAACC,YAAY,EAAsB;IAAA,IAApBC,MAAM,GAAArC,SAAA,CAAAxE,MAAA,QAAAwE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGrI,MAAM,CAAC+G,EAAE;IAC1C,IAAI,CAAC0D,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpBlH,iBAAiB,CAACiG,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC;IACjC9D,YAAY,CAAC,IAAI,EAAEmE,uBAAuB,CAAC;IAC3C;IACAnE,YAAY,CAAC,IAAI,EAAE+D,QAAQ,EAAElG,iBAAiB,CAACiG,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAEjG,iBAAiB,CAACiG,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IACnG;IACA9D,YAAY,CAAC,IAAI,EAAEoE,SAAS,EAAEvG,iBAAiB,CAACiG,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,EAAEjG,iBAAiB,CAACiG,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IACrG;IACA9D,YAAY,CAAC,IAAI,EAAEwE,QAAQ,EAAE3G,iBAAiB,CAACiG,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,EAAEjG,iBAAiB,CAACiG,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IACpG,IAAI,CAACkB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACxC,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACwC,KAAK,CAAC,CAAC;EACd;EACA,IAAI/C,OAAOA,CAAA,EAAG;IACZ,OAAOjD,YAAY,CAAC,IAAI,EAAEmF,uBAAuB,EAAEO,WAAW,CAAC;EACjE;EACA,IAAIO,OAAOA,CAAA,EAAG;IACZ,OAAOjG,YAAY,CAAC,IAAI,EAAEmF,uBAAuB,EAAEF,WAAW,CAAC;EACjE;EACA,IAAIiB,QAAQA,CAAA,EAAG;IACb,OAAOlG,YAAY,CAAC,IAAI,EAAEmF,uBAAuB,EAAEG,YAAY,CAAC;EAClE;EACA;EACA,IAAIrC,OAAOA,CAAChG,KAAK,EAAE;IACjB,MAAMgG,OAAO,GAAGxB,SAAS,CAAC,MAAMzB,YAAY,CAAC,IAAI,EAAEmF,uBAAuB,EAAEO,WAAW,CAAC,CAAC;IACzF,IAAIzI,KAAK,IAAIgG,OAAO,IAAI,IAAI,CAAC8C,MAAM,CAAC9C,OAAO,EAAEhG,KAAK,CAAC,EAAE;MACnD;IACF;IACAoE,KAAK,CAAC,MAAM;MACV,IAAI,CAACrB,YAAY,CAAC,IAAI,EAAEmF,uBAAuB,EAAEF,WAAW,CAAC,EAAE;QAC7D/E,YAAY,CAAC,IAAI,EAAEiF,uBAAuB,EAAElI,KAAK,EAAEiI,WAAW,CAAC;MACjE;MACAhF,YAAY,CAAC,IAAI,EAAEiF,uBAAuB,EAAElC,OAAO,EAAEsC,YAAY,CAAC;MAClErF,YAAY,CAAC,IAAI,EAAEiF,uBAAuB,EAAElI,KAAK,EAAE0I,WAAW,CAAC;IACjE,CAAC,CAAC;EACJ;EACA;EACAK,KAAKA,CAAA,EAA4B;IAAA,IAA3B/I,KAAK,GAAAyG,SAAA,CAAAxE,MAAA,QAAAwE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI,CAACoC,YAAY;IAC7BzE,KAAK,CAAC,MAAM;MACVnB,YAAY,CAAC,IAAI,EAAEiF,uBAAuB,EAAE,KAAK,CAAC,EAAEI,YAAY,CAAC;MACjErF,YAAY,CAAC,IAAI,EAAEiF,uBAAuB,EAAElI,KAAK,EAAEiI,WAAW,CAAC;MAC/DhF,YAAY,CAAC,IAAI,EAAEiF,uBAAuB,EAAElI,KAAK,EAAE0I,WAAW,CAAC;IACjE,CAAC,CAAC;EACJ;AACF,CAAC;AACDb,KAAK,GAAGjH,gBAAgB,CAAC,IAAI,CAAC;AAC9BkH,QAAQ,GAAG,IAAIzB,OAAO,CAAC,CAAC;AACxB6B,uBAAuB,GAAG,IAAIlE,OAAO,CAAC,CAAC;AACvCmE,SAAS,GAAG,IAAI9B,OAAO,CAAC,CAAC;AACzBkC,QAAQ,GAAG,IAAIlC,OAAO,CAAC,CAAC;AACxB0B,EAAE,GAAG7F,iBAAiB,CAAC2F,KAAK,EAAE,EAAE,EAAE,UAAU,EAAED,aAAa,EAAEM,uBAAuB,EAAEJ,QAAQ,CAAC,EAAEE,WAAW,GAAGD,EAAE,CAACzE,GAAG,EAAE2E,WAAW,GAAGF,EAAE,CAACvE,GAAG;AAC3I4E,EAAE,GAAGlG,iBAAiB,CAAC2F,KAAK,EAAE,EAAE,EAAE,WAAW,EAAEF,cAAc,EAAEO,uBAAuB,EAAEC,SAAS,CAAC,EAAEE,YAAY,GAAGD,EAAE,CAAC9E,GAAG,EAAEgF,YAAY,GAAGF,EAAE,CAAC5E,GAAG;AAChJgF,EAAE,GAAGtG,iBAAiB,CAAC2F,KAAK,EAAE,EAAE,EAAE,UAAU,EAAEH,aAAa,EAAEQ,uBAAuB,EAAEK,QAAQ,CAAC,EAAEE,WAAW,GAAGD,EAAE,CAAClF,GAAG,EAAEoF,WAAW,GAAGF,EAAE,CAAChF,GAAG;AAC3ItB,iBAAiB,CAAC2F,KAAK,EAAE,CAAC,EAAE,SAAS,EAAEJ,YAAY,EAAEkB,YAAY,CAAC;AAClEzG,iBAAiB,CAAC2F,KAAK,EAAE,CAAC,EAAE,SAAS,EAAEL,YAAY,EAAEmB,YAAY,CAAC;AAClEzG,iBAAiB,CAAC2F,KAAK,EAAE,CAAC,EAAE,UAAU,EAAEN,aAAa,EAAEoB,YAAY,CAAC;AACpEjH,mBAAmB,CAACmG,KAAK,EAAEc,YAAY,CAAC;;AAExC;AACA,SAASnE,SAAS,IAAI0E,UAAU,QAAQ,sBAAsB;AAC9D,SAASC,QAAQA,CAACnJ,KAAK,EAAE;EACvB,OAAOkJ,UAAU,CAAC,MAAM;IACtB,MAAME,MAAM,GAAG,CAAC,CAAC;IACjB,KAAK,MAAMrJ,GAAG,IAAIC,KAAK,EAAE;MACvBoJ,MAAM,CAACrJ,GAAG,CAAC,GAAGC,KAAK,CAACD,GAAG,CAAC;IAC1B;IACA,OAAOqJ,MAAM;EACf,CAAC,CAAC;AACJ;;AAEA;AACA,IAAIC,MAAM;AACV,IAAIC,SAAS,GAAG,MAAM;EACpBV,WAAWA,CAAA,EAAG;IACZ7E,YAAY,CAAC,IAAI,EAAEsF,MAAM,EAAE,eAAgB,IAAIhD,OAAO,CAAC,CAAC,CAAC;EAC3D;EACA/C,GAAGA,CAACvD,GAAG,EAAEwJ,EAAE,EAAE;IACX,IAAIzI,GAAG;IACP,OAAOf,GAAG,GAAG,CAACe,GAAG,GAAGiC,YAAY,CAAC,IAAI,EAAEsG,MAAM,CAAC,CAAC/F,GAAG,CAACvD,GAAG,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGe,GAAG,CAACwC,GAAG,CAACiG,EAAE,CAAC,GAAG,KAAK,CAAC;EAClG;EACA/F,GAAGA,CAACzD,GAAG,EAAEwJ,EAAE,EAAEvJ,KAAK,EAAE;IAClB,IAAIc,GAAG;IACP,IAAI,CAACf,GAAG,EAAE;IACV,IAAI,CAACgD,YAAY,CAAC,IAAI,EAAEsG,MAAM,CAAC,CAACjG,GAAG,CAACrD,GAAG,CAAC,EAAEgD,YAAY,CAAC,IAAI,EAAEsG,MAAM,CAAC,CAAC7F,GAAG,CAACzD,GAAG,EAAE,eAAgB,IAAIyJ,GAAG,CAAC,CAAC,CAAC;IACxG,OAAO,CAAC1I,GAAG,GAAGiC,YAAY,CAAC,IAAI,EAAEsG,MAAM,CAAC,CAAC/F,GAAG,CAACvD,GAAG,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGe,GAAG,CAAC0C,GAAG,CAAC+F,EAAE,EAAEvJ,KAAK,CAAC;EAC1F;EACAyJ,KAAKA,CAAC1J,GAAG,EAAE;IACT,IAAIe,GAAG;IACP,OAAOf,GAAG,GAAG,CAACe,GAAG,GAAGiC,YAAY,CAAC,IAAI,EAAEsG,MAAM,CAAC,CAAC/F,GAAG,CAACvD,GAAG,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGe,GAAG,CAAC2I,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;EAClG;AACF,CAAC;AACDJ,MAAM,GAAG,IAAIhD,OAAO,CAAC,CAAC;AACtB,SACEzB,MAAM,EACN+D,YAAY,EACZW,SAAS,EACTjF,MAAM,IAAID,KAAK,EACfS,QAAQ,EACRK,SAAS,EACTiB,OAAO,EACP5B,OAAO,IAAID,MAAM,EACjB0C,OAAO,EACP/G,UAAU,EACV6F,QAAQ,EACRnB,OAAO,IAAID,MAAM,EACjByE,QAAQ,EACR1E,UAAU,IAAID,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}