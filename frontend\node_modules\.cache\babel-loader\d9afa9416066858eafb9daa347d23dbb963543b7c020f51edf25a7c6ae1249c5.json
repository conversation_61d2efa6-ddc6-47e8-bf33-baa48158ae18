{"ast": null, "code": "export { default } from \"./Box.js\";\nexport { default as boxClasses } from \"./boxClasses.js\";\nexport * from \"./boxClasses.js\";", "map": {"version": 3, "names": ["default", "boxClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/system/esm/Box/index.js"], "sourcesContent": ["export { default } from \"./Box.js\";\nexport { default as boxClasses } from \"./boxClasses.js\";\nexport * from \"./boxClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,UAAU;AAClC,SAASA,OAAO,IAAIC,UAAU,QAAQ,iBAAiB;AACvD,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}