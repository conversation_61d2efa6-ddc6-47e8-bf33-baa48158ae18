{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport classNames from 'classnames';\nimport BaseOverlay from '@restart/ui/Overlay';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useIsomorphicEffect from '@restart/hooks/useIsomorphicEffect';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport useOverlayOffset from './useOverlayOffset';\nimport Fade from './Fade';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction wrapRefs(props, arrowProps) {\n  const {\n    ref\n  } = props;\n  const {\n    ref: aRef\n  } = arrowProps;\n  props.ref = ref.__wrapped || (ref.__wrapped = r => ref(safeFindDOMNode(r)));\n  arrowProps.ref = aRef.__wrapped || (aRef.__wrapped = r => aRef(safeFindDOMNode(r)));\n}\nconst Overlay = /*#__PURE__*/React.forwardRef((_ref, outerRef) => {\n  let {\n    children: overlay,\n    transition = Fade,\n    popperConfig = {},\n    rootClose = false,\n    placement = 'top',\n    show: outerShow = false,\n    ...outerProps\n  } = _ref;\n  const popperRef = useRef({});\n  const [firstRenderedState, setFirstRenderedState] = useState(null);\n  const [ref, modifiers] = useOverlayOffset(outerProps.offset);\n  const mergedRef = useMergedRefs(outerRef, ref);\n  const actualTransition = transition === true ? Fade : transition || undefined;\n  const handleFirstUpdate = useEventCallback(state => {\n    setFirstRenderedState(state);\n    popperConfig == null || popperConfig.onFirstUpdate == null || popperConfig.onFirstUpdate(state);\n  });\n  useIsomorphicEffect(() => {\n    if (firstRenderedState && outerProps.target) {\n      // Must wait for target element to resolve before updating popper.\n      popperRef.current.scheduleUpdate == null || popperRef.current.scheduleUpdate();\n    }\n  }, [firstRenderedState, outerProps.target]);\n  useEffect(() => {\n    if (!outerShow) {\n      setFirstRenderedState(null);\n    }\n  }, [outerShow]);\n  return /*#__PURE__*/_jsx(BaseOverlay, {\n    ...outerProps,\n    ref: mergedRef,\n    popperConfig: {\n      ...popperConfig,\n      modifiers: modifiers.concat(popperConfig.modifiers || []),\n      onFirstUpdate: handleFirstUpdate\n    },\n    transition: actualTransition,\n    rootClose: rootClose,\n    placement: placement,\n    show: outerShow,\n    children: (overlayProps, _ref2) => {\n      let {\n        arrowProps,\n        popper: popperObj,\n        show\n      } = _ref2;\n      var _popperObj$state;\n      wrapRefs(overlayProps, arrowProps);\n      // Need to get placement from popper object, handling case when overlay is flipped using 'flip' prop\n      const updatedPlacement = popperObj == null ? void 0 : popperObj.placement;\n      const popper = Object.assign(popperRef.current, {\n        state: popperObj == null ? void 0 : popperObj.state,\n        scheduleUpdate: popperObj == null ? void 0 : popperObj.update,\n        placement: updatedPlacement,\n        outOfBoundaries: (popperObj == null || (_popperObj$state = popperObj.state) == null || (_popperObj$state = _popperObj$state.modifiersData.hide) == null ? void 0 : _popperObj$state.isReferenceHidden) || false,\n        strategy: popperConfig.strategy\n      });\n      const hasDoneInitialMeasure = !!firstRenderedState;\n      if (typeof overlay === 'function') return overlay({\n        ...overlayProps,\n        placement: updatedPlacement,\n        show,\n        ...(!transition && show && {\n          className: 'show'\n        }),\n        popper,\n        arrowProps,\n        hasDoneInitialMeasure\n      });\n      return /*#__PURE__*/React.cloneElement(overlay, {\n        ...overlayProps,\n        placement: updatedPlacement,\n        arrowProps,\n        popper,\n        hasDoneInitialMeasure,\n        className: classNames(overlay.props.className, !transition && show && 'show'),\n        style: {\n          ...overlay.props.style,\n          ...overlayProps.style\n        }\n      });\n    }\n  });\n});\nOverlay.displayName = 'Overlay';\nexport default Overlay;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "classNames", "BaseOverlay", "useEventCallback", "useIsomorphicEffect", "useMergedRefs", "useOverlayOffset", "Fade", "safeFindDOMNode", "jsx", "_jsx", "wrapRefs", "props", "arrowProps", "ref", "aRef", "__wrapped", "r", "Overlay", "forwardRef", "_ref", "outerRef", "children", "overlay", "transition", "popperConfig", "rootClose", "placement", "show", "outerShow", "outerProps", "popperRef", "firstRenderedState", "setFirstRenderedState", "modifiers", "offset", "mergedRef", "actualTransition", "undefined", "handleFirstUpdate", "state", "onFirstUpdate", "target", "current", "scheduleUpdate", "concat", "overlayProps", "_ref2", "popper", "popperObj", "_popperObj$state", "updatedPlacement", "Object", "assign", "update", "outOfBoundaries", "modifiersData", "hide", "isReferenceHidden", "strategy", "hasDoneInitialMeasure", "className", "cloneElement", "style", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Overlay.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport classNames from 'classnames';\nimport BaseOverlay from '@restart/ui/Overlay';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useIsomorphicEffect from '@restart/hooks/useIsomorphicEffect';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport useOverlayOffset from './useOverlayOffset';\nimport Fade from './Fade';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction wrapRefs(props, arrowProps) {\n  const {\n    ref\n  } = props;\n  const {\n    ref: aRef\n  } = arrowProps;\n  props.ref = ref.__wrapped || (ref.__wrapped = r => ref(safeFindDOMNode(r)));\n  arrowProps.ref = aRef.__wrapped || (aRef.__wrapped = r => aRef(safeFindDOMNode(r)));\n}\nconst Overlay = /*#__PURE__*/React.forwardRef(({\n  children: overlay,\n  transition = Fade,\n  popperConfig = {},\n  rootClose = false,\n  placement = 'top',\n  show: outerShow = false,\n  ...outerProps\n}, outerRef) => {\n  const popperRef = useRef({});\n  const [firstRenderedState, setFirstRenderedState] = useState(null);\n  const [ref, modifiers] = useOverlayOffset(outerProps.offset);\n  const mergedRef = useMergedRefs(outerRef, ref);\n  const actualTransition = transition === true ? Fade : transition || undefined;\n  const handleFirstUpdate = useEventCallback(state => {\n    setFirstRenderedState(state);\n    popperConfig == null || popperConfig.onFirstUpdate == null || popperConfig.onFirstUpdate(state);\n  });\n  useIsomorphicEffect(() => {\n    if (firstRenderedState && outerProps.target) {\n      // Must wait for target element to resolve before updating popper.\n      popperRef.current.scheduleUpdate == null || popperRef.current.scheduleUpdate();\n    }\n  }, [firstRenderedState, outerProps.target]);\n  useEffect(() => {\n    if (!outerShow) {\n      setFirstRenderedState(null);\n    }\n  }, [outerShow]);\n  return /*#__PURE__*/_jsx(BaseOverlay, {\n    ...outerProps,\n    ref: mergedRef,\n    popperConfig: {\n      ...popperConfig,\n      modifiers: modifiers.concat(popperConfig.modifiers || []),\n      onFirstUpdate: handleFirstUpdate\n    },\n    transition: actualTransition,\n    rootClose: rootClose,\n    placement: placement,\n    show: outerShow,\n    children: (overlayProps, {\n      arrowProps,\n      popper: popperObj,\n      show\n    }) => {\n      var _popperObj$state;\n      wrapRefs(overlayProps, arrowProps);\n      // Need to get placement from popper object, handling case when overlay is flipped using 'flip' prop\n      const updatedPlacement = popperObj == null ? void 0 : popperObj.placement;\n      const popper = Object.assign(popperRef.current, {\n        state: popperObj == null ? void 0 : popperObj.state,\n        scheduleUpdate: popperObj == null ? void 0 : popperObj.update,\n        placement: updatedPlacement,\n        outOfBoundaries: (popperObj == null || (_popperObj$state = popperObj.state) == null || (_popperObj$state = _popperObj$state.modifiersData.hide) == null ? void 0 : _popperObj$state.isReferenceHidden) || false,\n        strategy: popperConfig.strategy\n      });\n      const hasDoneInitialMeasure = !!firstRenderedState;\n      if (typeof overlay === 'function') return overlay({\n        ...overlayProps,\n        placement: updatedPlacement,\n        show,\n        ...(!transition && show && {\n          className: 'show'\n        }),\n        popper,\n        arrowProps,\n        hasDoneInitialMeasure\n      });\n      return /*#__PURE__*/React.cloneElement(overlay, {\n        ...overlayProps,\n        placement: updatedPlacement,\n        arrowProps,\n        popper,\n        hasDoneInitialMeasure,\n        className: classNames(overlay.props.className, !transition && show && 'show'),\n        style: {\n          ...overlay.props.style,\n          ...overlayProps.style\n        }\n      });\n    }\n  });\n});\nOverlay.displayName = 'Overlay';\nexport default Overlay;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQA,CAACC,KAAK,EAAEC,UAAU,EAAE;EACnC,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,MAAM;IACJE,GAAG,EAAEC;EACP,CAAC,GAAGF,UAAU;EACdD,KAAK,CAACE,GAAG,GAAGA,GAAG,CAACE,SAAS,KAAKF,GAAG,CAACE,SAAS,GAAGC,CAAC,IAAIH,GAAG,CAACN,eAAe,CAACS,CAAC,CAAC,CAAC,CAAC;EAC3EJ,UAAU,CAACC,GAAG,GAAGC,IAAI,CAACC,SAAS,KAAKD,IAAI,CAACC,SAAS,GAAGC,CAAC,IAAIF,IAAI,CAACP,eAAe,CAACS,CAAC,CAAC,CAAC,CAAC;AACrF;AACA,MAAMC,OAAO,GAAG,aAAarB,KAAK,CAACsB,UAAU,CAAC,CAAAC,IAAA,EAQ3CC,QAAQ,KAAK;EAAA,IAR+B;IAC7CC,QAAQ,EAAEC,OAAO;IACjBC,UAAU,GAAGjB,IAAI;IACjBkB,YAAY,GAAG,CAAC,CAAC;IACjBC,SAAS,GAAG,KAAK;IACjBC,SAAS,GAAG,KAAK;IACjBC,IAAI,EAAEC,SAAS,GAAG,KAAK;IACvB,GAAGC;EACL,CAAC,GAAAV,IAAA;EACC,MAAMW,SAAS,GAAGhC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM,CAACiC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACc,GAAG,EAAEoB,SAAS,CAAC,GAAG5B,gBAAgB,CAACwB,UAAU,CAACK,MAAM,CAAC;EAC5D,MAAMC,SAAS,GAAG/B,aAAa,CAACgB,QAAQ,EAAEP,GAAG,CAAC;EAC9C,MAAMuB,gBAAgB,GAAGb,UAAU,KAAK,IAAI,GAAGjB,IAAI,GAAGiB,UAAU,IAAIc,SAAS;EAC7E,MAAMC,iBAAiB,GAAGpC,gBAAgB,CAACqC,KAAK,IAAI;IAClDP,qBAAqB,CAACO,KAAK,CAAC;IAC5Bf,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACgB,aAAa,IAAI,IAAI,IAAIhB,YAAY,CAACgB,aAAa,CAACD,KAAK,CAAC;EACjG,CAAC,CAAC;EACFpC,mBAAmB,CAAC,MAAM;IACxB,IAAI4B,kBAAkB,IAAIF,UAAU,CAACY,MAAM,EAAE;MAC3C;MACAX,SAAS,CAACY,OAAO,CAACC,cAAc,IAAI,IAAI,IAAIb,SAAS,CAACY,OAAO,CAACC,cAAc,CAAC,CAAC;IAChF;EACF,CAAC,EAAE,CAACZ,kBAAkB,EAAEF,UAAU,CAACY,MAAM,CAAC,CAAC;EAC3C5C,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+B,SAAS,EAAE;MACdI,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC,EAAE,CAACJ,SAAS,CAAC,CAAC;EACf,OAAO,aAAanB,IAAI,CAACR,WAAW,EAAE;IACpC,GAAG4B,UAAU;IACbhB,GAAG,EAAEsB,SAAS;IACdX,YAAY,EAAE;MACZ,GAAGA,YAAY;MACfS,SAAS,EAAEA,SAAS,CAACW,MAAM,CAACpB,YAAY,CAACS,SAAS,IAAI,EAAE,CAAC;MACzDO,aAAa,EAAEF;IACjB,CAAC;IACDf,UAAU,EAAEa,gBAAgB;IAC5BX,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA,SAAS;IACpBC,IAAI,EAAEC,SAAS;IACfP,QAAQ,EAAEA,CAACwB,YAAY,EAAAC,KAAA,KAIjB;MAAA,IAJmB;QACvBlC,UAAU;QACVmC,MAAM,EAAEC,SAAS;QACjBrB;MACF,CAAC,GAAAmB,KAAA;MACC,IAAIG,gBAAgB;MACpBvC,QAAQ,CAACmC,YAAY,EAAEjC,UAAU,CAAC;MAClC;MACA,MAAMsC,gBAAgB,GAAGF,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACtB,SAAS;MACzE,MAAMqB,MAAM,GAAGI,MAAM,CAACC,MAAM,CAACtB,SAAS,CAACY,OAAO,EAAE;QAC9CH,KAAK,EAAES,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACT,KAAK;QACnDI,cAAc,EAAEK,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACK,MAAM;QAC7D3B,SAAS,EAAEwB,gBAAgB;QAC3BI,eAAe,EAAE,CAACN,SAAS,IAAI,IAAI,IAAI,CAACC,gBAAgB,GAAGD,SAAS,CAACT,KAAK,KAAK,IAAI,IAAI,CAACU,gBAAgB,GAAGA,gBAAgB,CAACM,aAAa,CAACC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,gBAAgB,CAACQ,iBAAiB,KAAK,KAAK;QAC/MC,QAAQ,EAAElC,YAAY,CAACkC;MACzB,CAAC,CAAC;MACF,MAAMC,qBAAqB,GAAG,CAAC,CAAC5B,kBAAkB;MAClD,IAAI,OAAOT,OAAO,KAAK,UAAU,EAAE,OAAOA,OAAO,CAAC;QAChD,GAAGuB,YAAY;QACfnB,SAAS,EAAEwB,gBAAgB;QAC3BvB,IAAI;QACJ,IAAI,CAACJ,UAAU,IAAII,IAAI,IAAI;UACzBiC,SAAS,EAAE;QACb,CAAC,CAAC;QACFb,MAAM;QACNnC,UAAU;QACV+C;MACF,CAAC,CAAC;MACF,OAAO,aAAa/D,KAAK,CAACiE,YAAY,CAACvC,OAAO,EAAE;QAC9C,GAAGuB,YAAY;QACfnB,SAAS,EAAEwB,gBAAgB;QAC3BtC,UAAU;QACVmC,MAAM;QACNY,qBAAqB;QACrBC,SAAS,EAAE5D,UAAU,CAACsB,OAAO,CAACX,KAAK,CAACiD,SAAS,EAAE,CAACrC,UAAU,IAAII,IAAI,IAAI,MAAM,CAAC;QAC7EmC,KAAK,EAAE;UACL,GAAGxC,OAAO,CAACX,KAAK,CAACmD,KAAK;UACtB,GAAGjB,YAAY,CAACiB;QAClB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AACF7C,OAAO,CAAC8C,WAAW,GAAG,SAAS;AAC/B,eAAe9C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}