{"ast": null, "code": "import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport { default as THEME_ID } from \"./identifier.js\";\nexport { default as adaptV4Theme } from \"./adaptV4Theme.js\";\nexport { hexToRgb, rgbToHex, hslToRgb, decomposeColor, recomposeColor, getContrastRatio, getLuminance, emphasize, alpha, darken, lighten, css, keyframes } from '@mui/system';\nexport { unstable_createBreakpoints } from '@mui/system/createBreakpoints';\n// TODO: Remove this function in v6.\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function experimental_sx() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: The `experimental_sx` has been moved to `theme.unstable_sx`.' + 'For more details, see https://github.com/mui/material-ui/pull/35150.' : _formatErrorMessage(19));\n}\nexport { default as createTheme } from \"./createTheme.js\";\nexport { default as unstable_createMuiStrictModeTheme } from \"./createMuiStrictModeTheme.js\";\nexport { default as createStyles } from \"./createStyles.js\";\nexport { getUnit as unstable_getUnit, toUnitless as unstable_toUnitless } from \"./cssUtils.js\";\nexport { default as responsiveFontSizes } from \"./responsiveFontSizes.js\";\nexport { default as createTransitions, duration, easing } from \"./createTransitions.js\";\nexport { default as createColorScheme } from \"./createColorScheme.js\";\nexport { default as useTheme } from \"./useTheme.js\";\nexport { default as useThemeProps } from \"./useThemeProps.js\";\nexport { default as styled } from \"./styled.js\";\nexport { default as ThemeProvider } from \"./ThemeProvider.js\";\nexport { StyledEngineProvider } from '@mui/system';\n// The legacy utilities from @mui/styles\n// These are just empty functions that throws when invoked\nexport { default as makeStyles } from \"./makeStyles.js\";\nexport { default as withStyles } from \"./withStyles.js\";\nexport { default as withTheme } from \"./withTheme.js\";\nexport * from \"./ThemeProviderWithVars.js\";\nexport { default as extendTheme } from \"./createThemeWithVars.js\";\nexport { default as experimental_extendTheme } from \"./experimental_extendTheme.js\"; // TODO: Remove in v7\nexport { default as getOverlayAlpha } from \"./getOverlayAlpha.js\";\nexport { default as shouldSkipGeneratingVar } from \"./shouldSkipGeneratingVar.js\";\n\n// Private methods for creating parts of the theme\nexport { default as private_createTypography } from \"./createTypography.js\";\nexport { default as private_createMixins } from \"./createMixins.js\";\nexport { default as private_excludeVariablesFromRoot } from \"./excludeVariablesFromRoot.js\";", "map": {"version": 3, "names": ["_formatErrorMessage", "default", "THEME_ID", "adaptV4Theme", "hexToRgb", "rgbToHex", "hslToRgb", "decomposeColor", "recomposeColor", "getContrastRatio", "getLuminance", "emphasize", "alpha", "darken", "lighten", "css", "keyframes", "unstable_createBreakpoints", "experimental_sx", "Error", "process", "env", "NODE_ENV", "createTheme", "unstable_createMuiStrictModeTheme", "createStyles", "getUnit", "unstable_getUnit", "toUni<PERSON>s", "unstable_toUnitless", "responsiveFontSizes", "createTransitions", "duration", "easing", "createColorScheme", "useTheme", "useThemeProps", "styled", "ThemeProvider", "StyledEngineProvider", "makeStyles", "with<PERSON><PERSON><PERSON>", "withTheme", "extendTheme", "experimental_extendTheme", "getOverlayAlpha", "shouldSkipGeneratingVar", "private_createTypography", "private_createMixins", "private_excludeVariablesFromRoot"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/styles/index.js"], "sourcesContent": ["import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport { default as THEME_ID } from \"./identifier.js\";\nexport { default as adaptV4Theme } from \"./adaptV4Theme.js\";\nexport { hexToRgb, rgbToHex, hslToRgb, decomposeColor, recomposeColor, getContrastRatio, getLuminance, emphasize, alpha, darken, lighten, css, keyframes } from '@mui/system';\nexport { unstable_createBreakpoints } from '@mui/system/createBreakpoints';\n// TODO: Remove this function in v6.\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function experimental_sx() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: The `experimental_sx` has been moved to `theme.unstable_sx`.' + 'For more details, see https://github.com/mui/material-ui/pull/35150.' : _formatErrorMessage(19));\n}\nexport { default as createTheme } from \"./createTheme.js\";\nexport { default as unstable_createMuiStrictModeTheme } from \"./createMuiStrictModeTheme.js\";\nexport { default as createStyles } from \"./createStyles.js\";\nexport { getUnit as unstable_getUnit, toUnitless as unstable_toUnitless } from \"./cssUtils.js\";\nexport { default as responsiveFontSizes } from \"./responsiveFontSizes.js\";\nexport { default as createTransitions, duration, easing } from \"./createTransitions.js\";\nexport { default as createColorScheme } from \"./createColorScheme.js\";\nexport { default as useTheme } from \"./useTheme.js\";\nexport { default as useThemeProps } from \"./useThemeProps.js\";\nexport { default as styled } from \"./styled.js\";\nexport { default as ThemeProvider } from \"./ThemeProvider.js\";\nexport { StyledEngineProvider } from '@mui/system';\n// The legacy utilities from @mui/styles\n// These are just empty functions that throws when invoked\nexport { default as makeStyles } from \"./makeStyles.js\";\nexport { default as withStyles } from \"./withStyles.js\";\nexport { default as withTheme } from \"./withTheme.js\";\nexport * from \"./ThemeProviderWithVars.js\";\nexport { default as extendTheme } from \"./createThemeWithVars.js\";\nexport { default as experimental_extendTheme } from \"./experimental_extendTheme.js\"; // TODO: Remove in v7\nexport { default as getOverlayAlpha } from \"./getOverlayAlpha.js\";\nexport { default as shouldSkipGeneratingVar } from \"./shouldSkipGeneratingVar.js\";\n\n// Private methods for creating parts of the theme\nexport { default as private_createTypography } from \"./createTypography.js\";\nexport { default as private_createMixins } from \"./createMixins.js\";\nexport { default as private_excludeVariablesFromRoot } from \"./excludeVariablesFromRoot.js\";"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,kCAAkC;AAClE,SAASC,OAAO,IAAIC,QAAQ,QAAQ,iBAAiB;AACrD,SAASD,OAAO,IAAIE,YAAY,QAAQ,mBAAmB;AAC3D,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,SAAS,QAAQ,aAAa;AAC7K,SAASC,0BAA0B,QAAQ,+BAA+B;AAC1E;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,mEAAmE,GAAG,sEAAsE,GAAGtB,mBAAmB,CAAC,EAAE,CAAC,CAAC;AACjO;AACA,SAASC,OAAO,IAAIsB,WAAW,QAAQ,kBAAkB;AACzD,SAAStB,OAAO,IAAIuB,iCAAiC,QAAQ,+BAA+B;AAC5F,SAASvB,OAAO,IAAIwB,YAAY,QAAQ,mBAAmB;AAC3D,SAASC,OAAO,IAAIC,gBAAgB,EAAEC,UAAU,IAAIC,mBAAmB,QAAQ,eAAe;AAC9F,SAAS5B,OAAO,IAAI6B,mBAAmB,QAAQ,0BAA0B;AACzE,SAAS7B,OAAO,IAAI8B,iBAAiB,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,wBAAwB;AACvF,SAAShC,OAAO,IAAIiC,iBAAiB,QAAQ,wBAAwB;AACrE,SAASjC,OAAO,IAAIkC,QAAQ,QAAQ,eAAe;AACnD,SAASlC,OAAO,IAAImC,aAAa,QAAQ,oBAAoB;AAC7D,SAASnC,OAAO,IAAIoC,MAAM,QAAQ,aAAa;AAC/C,SAASpC,OAAO,IAAIqC,aAAa,QAAQ,oBAAoB;AAC7D,SAASC,oBAAoB,QAAQ,aAAa;AAClD;AACA;AACA,SAAStC,OAAO,IAAIuC,UAAU,QAAQ,iBAAiB;AACvD,SAASvC,OAAO,IAAIwC,UAAU,QAAQ,iBAAiB;AACvD,SAASxC,OAAO,IAAIyC,SAAS,QAAQ,gBAAgB;AACrD,cAAc,4BAA4B;AAC1C,SAASzC,OAAO,IAAI0C,WAAW,QAAQ,0BAA0B;AACjE,SAAS1C,OAAO,IAAI2C,wBAAwB,QAAQ,+BAA+B,CAAC,CAAC;AACrF,SAAS3C,OAAO,IAAI4C,eAAe,QAAQ,sBAAsB;AACjE,SAAS5C,OAAO,IAAI6C,uBAAuB,QAAQ,8BAA8B;;AAEjF;AACA,SAAS7C,OAAO,IAAI8C,wBAAwB,QAAQ,uBAAuB;AAC3E,SAAS9C,OAAO,IAAI+C,oBAAoB,QAAQ,mBAAmB;AACnE,SAAS/C,OAAO,IAAIgD,gCAAgC,QAAQ,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}