{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navbar as BootstrapNavbar, Nav, NavDropdown, Container, But<PERSON> } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useSiteName, useSiteLogo } from '../../contexts/SettingsContext';\nimport authService from '../../services/authService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const siteName = useSiteName();\n  const siteLogo = useSiteLogo();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n  const handleAdminPanel = async () => {\n    try {\n      await authService.navigateToAdminPanel();\n    } catch (error) {\n      var _process$env$REACT_AP;\n      console.error('Failed to access admin panel:', error);\n      // Fallback to direct navigation (user will need to login manually)\n      const baseUrl = ((_process$env$REACT_AP = process.env.REACT_APP_API_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.replace('/api', '')) || 'http://localhost:8000';\n      const adminUrl = `${baseUrl}/admin`;\n      window.open(adminUrl, '_blank');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(BootstrapNavbar, {\n    bg: \"dark\",\n    variant: \"dark\",\n    expand: \"lg\",\n    sticky: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(BootstrapNavbar.Brand, {\n        as: Link,\n        to: \"/\",\n        className: \"text-decoration-none d-flex align-items-center\",\n        children: [siteLogo && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: siteLogo,\n          alt: siteName,\n          height: \"30\",\n          className: \"me-2\",\n          style: {\n            maxWidth: '120px',\n            objectFit: 'contain'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this), siteName]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Toggle, {\n        \"aria-controls\": \"basic-navbar-nav\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Collapse, {\n        id: \"basic-navbar-nav\",\n        children: [/*#__PURE__*/_jsxDEV(Nav, {\n          className: \"me-auto\",\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/\",\n            className: \"text-decoration-none\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav, {\n          className: \"ms-auto\",\n          children: isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/dashboard\",\n              className: \"text-decoration-none\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavDropdown, {\n              title: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n              id: \"user-dropdown\",\n              align: \"end\",\n              children: [/*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: () => navigate('/profile'),\n                children: \"Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: () => navigate('/profile/edit'),\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(NavDropdown.Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                  onClick: handleAdminPanel,\n                  children: \"Admin Panel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(NavDropdown.Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: handleLogout,\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this), !(user !== null && user !== void 0 && user.email_verified_at) && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-warning\",\n              size: \"sm\",\n              className: \"ms-2\",\n              onClick: () => navigate('/email-verification'),\n              children: \"Verify Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/login\",\n              className: \"text-decoration-none\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              className: \"ms-2\",\n              onClick: () => navigate('/register'),\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"15JSOOFdfCzR9wbQQM1/LGlkRX4=\", false, function () {\n  return [useAuth, useSiteName, useSiteLogo, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "BootstrapNavbar", "Nav", "NavDropdown", "Container", "<PERSON><PERSON>", "Link", "useNavigate", "useAuth", "useSiteName", "useSiteLogo", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "_s", "user", "isAuthenticated", "logout", "siteName", "siteLogo", "navigate", "handleLogout", "error", "console", "handleAdminPanel", "navigateToAdminPanel", "_process$env$REACT_AP", "baseUrl", "process", "env", "REACT_APP_API_URL", "replace", "adminUrl", "window", "open", "bg", "variant", "expand", "sticky", "children", "Brand", "as", "to", "className", "src", "alt", "height", "style", "max<PERSON><PERSON><PERSON>", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "Collapse", "id", "title", "name", "align", "<PERSON><PERSON>", "onClick", "role", "Divider", "email_verified_at", "size", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navbar as BootstrapN<PERSON><PERSON>, Nav, NavDropdown, Container, But<PERSON> } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useSiteName, useSiteLogo } from '../../contexts/SettingsContext';\nimport authService from '../../services/authService';\n\nconst Navbar: React.FC = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const siteName = useSiteName();\n  const siteLogo = useSiteLogo();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const handleAdminPanel = async () => {\n    try {\n      await authService.navigateToAdminPanel();\n    } catch (error: any) {\n      console.error('Failed to access admin panel:', error);\n      // Fallback to direct navigation (user will need to login manually)\n      const baseUrl = process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:8000';\n      const adminUrl = `${baseUrl}/admin`;\n      window.open(adminUrl, '_blank');\n    }\n  };\n\n  return (\n    <BootstrapNavbar bg=\"dark\" variant=\"dark\" expand=\"lg\" sticky=\"top\">\n      <Container>\n        <BootstrapNavbar.Brand as={Link} to=\"/\" className=\"text-decoration-none d-flex align-items-center\">\n          {siteLogo && (\n            <img\n              src={siteLogo}\n              alt={siteName}\n              height=\"30\"\n              className=\"me-2\"\n              style={{ maxWidth: '120px', objectFit: 'contain' }}\n            />\n          )}\n          {siteName}\n        </BootstrapNavbar.Brand>\n\n        <BootstrapNavbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <BootstrapNavbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/\" className=\"text-decoration-none\">\n              Home\n            </Nav.Link>\n          </Nav>\n\n          <Nav className=\"ms-auto\">\n            {isAuthenticated ? (\n              <>\n                <Nav.Link as={Link} to=\"/dashboard\" className=\"text-decoration-none\">\n                  Dashboard\n                </Nav.Link>\n                <NavDropdown\n                  title={user?.name || 'User'}\n                  id=\"user-dropdown\"\n                  align=\"end\"\n                >\n                  <NavDropdown.Item onClick={() => navigate('/profile')}>\n                    Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => navigate('/profile/edit')}>\n                    Edit Profile\n                  </NavDropdown.Item>\n                  {user?.role === 'admin' && (\n                    <>\n                      <NavDropdown.Divider />\n                      <NavDropdown.Item onClick={handleAdminPanel}>\n                        Admin Panel\n                      </NavDropdown.Item>\n                    </>\n                  )}\n                  <NavDropdown.Divider />\n                  <NavDropdown.Item onClick={handleLogout}>\n                    Logout\n                  </NavDropdown.Item>\n                </NavDropdown>\n\n                {!user?.email_verified_at && (\n                  <Button\n                    variant=\"outline-warning\"\n                    size=\"sm\"\n                    className=\"ms-2\"\n                    onClick={() => navigate('/email-verification')}\n                  >\n                    Verify Email\n                  </Button>\n                )}\n              </>\n            ) : (\n              <>\n                <Nav.Link as={Link} to=\"/login\" className=\"text-decoration-none\">\n                  Login\n                </Nav.Link>\n                <Button\n                  variant=\"primary\"\n                  className=\"ms-2\"\n                  onClick={() => navigate('/register')}\n                >\n                  Register\n                </Button>\n              </>\n            )}\n          </Nav>\n        </BootstrapNavbar.Collapse>\n      </Container>\n    </BootstrapNavbar>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,IAAIC,eAAe,EAAEC,GAAG,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,iBAAiB;AAChG,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,EAAEC,WAAW,QAAQ,gCAAgC;AACzE,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMf,MAAgB,GAAGA,CAAA,KAAM;EAAAgB,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGX,OAAO,CAAC,CAAC;EACnD,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMJ,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMf,WAAW,CAACgB,oBAAoB,CAAC,CAAC;IAC1C,CAAC,CAAC,OAAOH,KAAU,EAAE;MAAA,IAAAI,qBAAA;MACnBH,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACA,MAAMK,OAAO,GAAG,EAAAD,qBAAA,GAAAE,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAAJ,qBAAA,uBAA7BA,qBAAA,CAA+BK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,KAAI,uBAAuB;MAC7F,MAAMC,QAAQ,GAAG,GAAGL,OAAO,QAAQ;MACnCM,MAAM,CAACC,IAAI,CAACF,QAAQ,EAAE,QAAQ,CAAC;IACjC;EACF,CAAC;EAED,oBACErB,OAAA,CAACZ,eAAe;IAACoC,EAAE,EAAC,MAAM;IAACC,OAAO,EAAC,MAAM;IAACC,MAAM,EAAC,IAAI;IAACC,MAAM,EAAC,KAAK;IAAAC,QAAA,eAChE5B,OAAA,CAACT,SAAS;MAAAqC,QAAA,gBACR5B,OAAA,CAACZ,eAAe,CAACyC,KAAK;QAACC,EAAE,EAAErC,IAAK;QAACsC,EAAE,EAAC,GAAG;QAACC,SAAS,EAAC,gDAAgD;QAAAJ,QAAA,GAC/FpB,QAAQ,iBACPR,OAAA;UACEiC,GAAG,EAAEzB,QAAS;UACd0B,GAAG,EAAE3B,QAAS;UACd4B,MAAM,EAAC,IAAI;UACXH,SAAS,EAAC,MAAM;UAChBI,KAAK,EAAE;YAAEC,QAAQ,EAAE,OAAO;YAAEC,SAAS,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CACF,EACAnC,QAAQ;MAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAExB1C,OAAA,CAACZ,eAAe,CAACuD,MAAM;QAAC,iBAAc;MAAkB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3D1C,OAAA,CAACZ,eAAe,CAACwD,QAAQ;QAACC,EAAE,EAAC,kBAAkB;QAAAjB,QAAA,gBAC7C5B,OAAA,CAACX,GAAG;UAAC2C,SAAS,EAAC,SAAS;UAAAJ,QAAA,eACtB5B,OAAA,CAACX,GAAG,CAACI,IAAI;YAACqC,EAAE,EAAErC,IAAK;YAACsC,EAAE,EAAC,GAAG;YAACC,SAAS,EAAC,sBAAsB;YAAAJ,QAAA,EAAC;UAE5D;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEN1C,OAAA,CAACX,GAAG;UAAC2C,SAAS,EAAC,SAAS;UAAAJ,QAAA,EACrBvB,eAAe,gBACdL,OAAA,CAAAE,SAAA;YAAA0B,QAAA,gBACE5B,OAAA,CAACX,GAAG,CAACI,IAAI;cAACqC,EAAE,EAAErC,IAAK;cAACsC,EAAE,EAAC,YAAY;cAACC,SAAS,EAAC,sBAAsB;cAAAJ,QAAA,EAAC;YAErE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACX1C,OAAA,CAACV,WAAW;cACVwD,KAAK,EAAE,CAAA1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2C,IAAI,KAAI,MAAO;cAC5BF,EAAE,EAAC,eAAe;cAClBG,KAAK,EAAC,KAAK;cAAApB,QAAA,gBAEX5B,OAAA,CAACV,WAAW,CAAC2D,IAAI;gBAACC,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,UAAU,CAAE;gBAAAmB,QAAA,EAAC;cAEvD;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eACnB1C,OAAA,CAACV,WAAW,CAAC2D,IAAI;gBAACC,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,eAAe,CAAE;gBAAAmB,QAAA,EAAC;cAE5D;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,EAClB,CAAAtC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,IAAI,MAAK,OAAO,iBACrBnD,OAAA,CAAAE,SAAA;gBAAA0B,QAAA,gBACE5B,OAAA,CAACV,WAAW,CAAC8D,OAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvB1C,OAAA,CAACV,WAAW,CAAC2D,IAAI;kBAACC,OAAO,EAAErC,gBAAiB;kBAAAe,QAAA,EAAC;gBAE7C;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkB,CAAC;cAAA,eACnB,CACH,eACD1C,OAAA,CAACV,WAAW,CAAC8D,OAAO;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvB1C,OAAA,CAACV,WAAW,CAAC2D,IAAI;gBAACC,OAAO,EAAExC,YAAa;gBAAAkB,QAAA,EAAC;cAEzC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEb,EAACtC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiD,iBAAiB,kBACvBrD,OAAA,CAACR,MAAM;cACLiC,OAAO,EAAC,iBAAiB;cACzB6B,IAAI,EAAC,IAAI;cACTtB,SAAS,EAAC,MAAM;cAChBkB,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,qBAAqB,CAAE;cAAAmB,QAAA,EAChD;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA,eACD,CAAC,gBAEH1C,OAAA,CAAAE,SAAA;YAAA0B,QAAA,gBACE5B,OAAA,CAACX,GAAG,CAACI,IAAI;cAACqC,EAAE,EAAErC,IAAK;cAACsC,EAAE,EAAC,QAAQ;cAACC,SAAS,EAAC,sBAAsB;cAAAJ,QAAA,EAAC;YAEjE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACX1C,OAAA,CAACR,MAAM;cACLiC,OAAO,EAAC,SAAS;cACjBO,SAAS,EAAC,MAAM;cAChBkB,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,WAAW,CAAE;cAAAmB,QAAA,EACtC;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACkB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAACvC,EAAA,CA/GIhB,MAAgB;EAAA,QACsBQ,OAAO,EAChCC,WAAW,EACXC,WAAW,EACXH,WAAW;AAAA;AAAA6D,EAAA,GAJxBpE,MAAgB;AAiHtB,eAAeA,MAAM;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}