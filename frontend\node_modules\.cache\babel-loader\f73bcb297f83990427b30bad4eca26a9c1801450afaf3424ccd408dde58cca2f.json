{"ast": null, "code": "export { default } from \"./Backdrop.js\";\nexport { default as backdropClasses } from \"./backdropClasses.js\";\nexport * from \"./backdropClasses.js\";", "map": {"version": 3, "names": ["default", "backdropClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Backdrop/index.js"], "sourcesContent": ["export { default } from \"./Backdrop.js\";\nexport { default as backdropClasses } from \"./backdropClasses.js\";\nexport * from \"./backdropClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,eAAe,QAAQ,sBAAsB;AACjE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}