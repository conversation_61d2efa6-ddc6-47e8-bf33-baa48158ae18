{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as SystemThemeProvider } from '@mui/system';\nimport THEME_ID from \"./identifier.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProviderNoVars(_ref) {\n  let {\n    theme: themeInput,\n    ...props\n  } = _ref;\n  const scopedTheme = THEME_ID in themeInput ? themeInput[THEME_ID] : undefined;\n  return /*#__PURE__*/_jsx(SystemThemeProvider, {\n    ...props,\n    themeId: scopedTheme ? THEME_ID : undefined,\n    theme: scopedTheme || themeInput\n  });\n}", "map": {"version": 3, "names": ["React", "ThemeProvider", "SystemThemeProvider", "THEME_ID", "jsx", "_jsx", "ThemeProviderNoVars", "_ref", "theme", "themeInput", "props", "scopedTheme", "undefined", "themeId"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/styles/ThemeProviderNoVars.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as SystemThemeProvider } from '@mui/system';\nimport THEME_ID from \"./identifier.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProviderNoVars({\n  theme: themeInput,\n  ...props\n}) {\n  const scopedTheme = THEME_ID in themeInput ? themeInput[THEME_ID] : undefined;\n  return /*#__PURE__*/_jsx(SystemThemeProvider, {\n    ...props,\n    themeId: scopedTheme ? THEME_ID : undefined,\n    theme: scopedTheme || themeInput\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,IAAIC,mBAAmB,QAAQ,aAAa;AAClE,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,mBAAmBA,CAAAC,IAAA,EAGxC;EAAA,IAHyC;IAC1CC,KAAK,EAAEC,UAAU;IACjB,GAAGC;EACL,CAAC,GAAAH,IAAA;EACC,MAAMI,WAAW,GAAGR,QAAQ,IAAIM,UAAU,GAAGA,UAAU,CAACN,QAAQ,CAAC,GAAGS,SAAS;EAC7E,OAAO,aAAaP,IAAI,CAACH,mBAAmB,EAAE;IAC5C,GAAGQ,KAAK;IACRG,OAAO,EAAEF,WAAW,GAAGR,QAAQ,GAAGS,SAAS;IAC3CJ,KAAK,EAAEG,WAAW,IAAIF;EACxB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}