{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 9h-1V4H8v5h-.78C6.67 8.39 5.89 8 5 8c-1.66 0-3 1.34-3 3v7c0 1.66 1.34 3 3 3 .89 0 1.67-.39 2.22-1H22v-8c0-1.66-1.34-3-3-3M6 18c0 .55-.45 1-1 1s-1-.45-1-1v-7c0-.55.45-1 1-1s1 .45 1 1zm4-12h6v3h-6zm10 12H8v-7h11c.55 0 1 .45 1 1z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"15\",\n  cy: \"13\",\n  r: \"1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"18\",\n  cy: \"13\",\n  r: \"1\"\n}, \"2\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"15\",\n  cy: \"16\",\n  r: \"1\"\n}, \"3\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"18\",\n  cy: \"16\",\n  r: \"1\"\n}, \"4\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9 12h4v5H9z\"\n}, \"5\")], 'FaxOutlined');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "cx", "cy", "r"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/icons-material/esm/FaxOutlined.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 9h-1V4H8v5h-.78C6.67 8.39 5.89 8 5 8c-1.66 0-3 1.34-3 3v7c0 1.66 1.34 3 3 3 .89 0 1.67-.39 2.22-1H22v-8c0-1.66-1.34-3-3-3M6 18c0 .55-.45 1-1 1s-1-.45-1-1v-7c0-.55.45-1 1-1s1 .45 1 1zm4-12h6v3h-6zm10 12H8v-7h11c.55 0 1 .45 1 1z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"15\",\n  cy: \"13\",\n  r: \"1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"18\",\n  cy: \"13\",\n  r: \"1\"\n}, \"2\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"15\",\n  cy: \"16\",\n  r: \"1\"\n}, \"3\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"18\",\n  cy: \"16\",\n  r: \"1\"\n}, \"4\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9 12h4v5H9z\"\n}, \"5\")], 'FaxOutlined');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}