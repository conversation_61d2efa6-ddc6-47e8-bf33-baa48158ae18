{"ast": null, "code": "const _excluded = [\"eventKey\", \"disabled\", \"onClick\", \"active\", \"as\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport NavContext from './NavContext';\nimport Button from './Button';\nimport { dataAttr } from './DataKey';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Create a dropdown item. Returns a set of props for the dropdown item component\n * including an `onClick` handler that prevents selection when the item is disabled\n */\nexport function useDropdownItem(_ref2) {\n  let {\n    key,\n    href,\n    active,\n    disabled,\n    onClick\n  } = _ref2;\n  const onSelectCtx = useContext(SelectableContext);\n  const navContext = useContext(NavContext);\n  const {\n    activeKey\n  } = navContext || {};\n  const eventKey = makeEventKey(key, href);\n  const isActive = active == null && key != null ? makeEventKey(activeKey) === eventKey : active;\n  const handleClick = useEventCallback(event => {\n    if (disabled) return;\n    onClick == null ? void 0 : onClick(event);\n    if (onSelectCtx && !event.isPropagationStopped()) {\n      onSelectCtx(eventKey, event);\n    }\n  });\n  return [{\n    onClick: handleClick,\n    'aria-disabled': disabled || undefined,\n    'aria-selected': isActive,\n    [dataAttr('dropdown-item')]: ''\n  }, {\n    isActive\n  }];\n}\nconst DropdownItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      eventKey,\n      disabled,\n      onClick,\n      active,\n      as: Component = Button\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [dropdownItemProps] = useDropdownItem({\n    key: eventKey,\n    href: props.href,\n    disabled,\n    onClick,\n    active\n  });\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, props, {\n    ref: ref\n  }, dropdownItemProps));\n});\nDropdownItem.displayName = 'DropdownItem';\nexport default DropdownItem;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "React", "useContext", "useEventCallback", "SelectableContext", "makeEventKey", "NavContext", "<PERSON><PERSON>", "dataAttr", "jsx", "_jsx", "useDropdownItem", "_ref2", "key", "href", "active", "disabled", "onClick", "onSelectCtx", "navContext", "active<PERSON><PERSON>", "eventKey", "isActive", "handleClick", "event", "isPropagationStopped", "undefined", "DropdownItem", "forwardRef", "_ref", "ref", "as", "Component", "props", "dropdownItemProps", "Object", "assign", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/@restart/ui/esm/DropdownItem.js"], "sourcesContent": ["const _excluded = [\"eventKey\", \"disabled\", \"onClick\", \"active\", \"as\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport NavContext from './NavContext';\nimport Button from './Button';\nimport { dataAttr } from './DataKey';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Create a dropdown item. Returns a set of props for the dropdown item component\n * including an `onClick` handler that prevents selection when the item is disabled\n */\nexport function useDropdownItem({\n  key,\n  href,\n  active,\n  disabled,\n  onClick\n}) {\n  const onSelectCtx = useContext(SelectableContext);\n  const navContext = useContext(NavContext);\n  const {\n    activeKey\n  } = navContext || {};\n  const eventKey = makeEventKey(key, href);\n  const isActive = active == null && key != null ? makeEventKey(activeKey) === eventKey : active;\n  const handleClick = useEventCallback(event => {\n    if (disabled) return;\n    onClick == null ? void 0 : onClick(event);\n    if (onSelectCtx && !event.isPropagationStopped()) {\n      onSelectCtx(eventKey, event);\n    }\n  });\n  return [{\n    onClick: handleClick,\n    'aria-disabled': disabled || undefined,\n    'aria-selected': isActive,\n    [dataAttr('dropdown-item')]: ''\n  }, {\n    isActive\n  }];\n}\nconst DropdownItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      eventKey,\n      disabled,\n      onClick,\n      active,\n      as: Component = Button\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [dropdownItemProps] = useDropdownItem({\n    key: eventKey,\n    href: props.href,\n    disabled,\n    onClick,\n    active\n  });\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, props, {\n    ref: ref\n  }, dropdownItemProps));\n});\nDropdownItem.displayName = 'DropdownItem';\nexport default DropdownItem;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;AACrE,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM,OAAO,KAAKK,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,iBAAiB,IAAIC,YAAY,QAAQ,qBAAqB;AACrE,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAAC,KAAA,EAM5B;EAAA,IAN6B;IAC9BC,GAAG;IACHC,IAAI;IACJC,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC,GAAAL,KAAA;EACC,MAAMM,WAAW,GAAGhB,UAAU,CAACE,iBAAiB,CAAC;EACjD,MAAMe,UAAU,GAAGjB,UAAU,CAACI,UAAU,CAAC;EACzC,MAAM;IACJc;EACF,CAAC,GAAGD,UAAU,IAAI,CAAC,CAAC;EACpB,MAAME,QAAQ,GAAGhB,YAAY,CAACQ,GAAG,EAAEC,IAAI,CAAC;EACxC,MAAMQ,QAAQ,GAAGP,MAAM,IAAI,IAAI,IAAIF,GAAG,IAAI,IAAI,GAAGR,YAAY,CAACe,SAAS,CAAC,KAAKC,QAAQ,GAAGN,MAAM;EAC9F,MAAMQ,WAAW,GAAGpB,gBAAgB,CAACqB,KAAK,IAAI;IAC5C,IAAIR,QAAQ,EAAE;IACdC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACO,KAAK,CAAC;IACzC,IAAIN,WAAW,IAAI,CAACM,KAAK,CAACC,oBAAoB,CAAC,CAAC,EAAE;MAChDP,WAAW,CAACG,QAAQ,EAAEG,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,OAAO,CAAC;IACNP,OAAO,EAAEM,WAAW;IACpB,eAAe,EAAEP,QAAQ,IAAIU,SAAS;IACtC,eAAe,EAAEJ,QAAQ;IACzB,CAACd,QAAQ,CAAC,eAAe,CAAC,GAAG;EAC/B,CAAC,EAAE;IACDc;EACF,CAAC,CAAC;AACJ;AACA,MAAMK,YAAY,GAAG,aAAa1B,KAAK,CAAC2B,UAAU,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;EAChE,IAAI;MACAT,QAAQ;MACRL,QAAQ;MACRC,OAAO;MACPF,MAAM;MACNgB,EAAE,EAAEC,SAAS,GAAGzB;IAClB,CAAC,GAAGsB,IAAI;IACRI,KAAK,GAAGxC,6BAA6B,CAACoC,IAAI,EAAErC,SAAS,CAAC;EACxD,MAAM,CAAC0C,iBAAiB,CAAC,GAAGvB,eAAe,CAAC;IAC1CE,GAAG,EAAEQ,QAAQ;IACbP,IAAI,EAAEmB,KAAK,CAACnB,IAAI;IAChBE,QAAQ;IACRC,OAAO;IACPF;EACF,CAAC,CAAC;EACF,OAAO,aAAaL,IAAI,CAACsB,SAAS,EAAEG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,KAAK,EAAE;IAC3DH,GAAG,EAAEA;EACP,CAAC,EAAEI,iBAAiB,CAAC,CAAC;AACxB,CAAC,CAAC;AACFP,YAAY,CAACU,WAAW,GAAG,cAAc;AACzC,eAAeV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}