{"ast": null, "code": "\"use client\";\n\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport DropdownContext from '@restart/ui/DropdownContext';\nimport { useDropdownToggle } from '@restart/ui/DropdownToggle';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport Button from './Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport useWrappedRefWithWarning from './useWrappedRefWithWarning';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownToggle = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    split,\n    className,\n    childBsPrefix,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = Button,\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-toggle');\n  const dropdownContext = useContext(DropdownContext);\n  if (childBsPrefix !== undefined) {\n    props.bsPrefix = childBsPrefix;\n  }\n  const [toggleProps] = useDropdownToggle();\n  toggleProps.ref = useMergedRefs(toggleProps.ref, useWrappedRefWithWarning(ref, 'DropdownToggle'));\n\n  // This intentionally forwards size and variant (if set) to the\n  // underlying component, to allow it to render size and style variants.\n  return /*#__PURE__*/_jsx(Component, {\n    className: classNames(className, prefix, split && `${prefix}-split`, (dropdownContext == null ? void 0 : dropdownContext.show) && 'show'),\n    ...toggleProps,\n    ...props\n  });\n});\nDropdownToggle.displayName = 'DropdownToggle';\nexport default DropdownToggle;", "map": {"version": 3, "names": ["useMergedRefs", "DropdownContext", "useDropdownToggle", "classNames", "React", "useContext", "<PERSON><PERSON>", "useBootstrapPrefix", "useWrappedRefWithWarning", "jsx", "_jsx", "DropdownToggle", "forwardRef", "_ref", "ref", "bsPrefix", "split", "className", "childBsPrefix", "as", "Component", "props", "prefix", "dropdownContext", "undefined", "toggleProps", "show", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/DropdownToggle.js"], "sourcesContent": ["\"use client\";\n\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport DropdownContext from '@restart/ui/DropdownContext';\nimport { useDropdownToggle } from '@restart/ui/DropdownToggle';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport Button from './Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport useWrappedRefWithWarning from './useWrappedRefWithWarning';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownToggle = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  split,\n  className,\n  childBsPrefix,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = Button,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-toggle');\n  const dropdownContext = useContext(DropdownContext);\n  if (childBsPrefix !== undefined) {\n    props.bsPrefix = childBsPrefix;\n  }\n  const [toggleProps] = useDropdownToggle();\n  toggleProps.ref = useMergedRefs(toggleProps.ref, useWrappedRefWithWarning(ref, 'DropdownToggle'));\n\n  // This intentionally forwards size and variant (if set) to the\n  // underlying component, to allow it to render size and style variants.\n  return /*#__PURE__*/_jsx(Component, {\n    className: classNames(className, prefix, split && `${prefix}-split`, (dropdownContext == null ? void 0 : dropdownContext.show) && 'show'),\n    ...toggleProps,\n    ...props\n  });\n});\nDropdownToggle.displayName = 'DropdownToggle';\nexport default DropdownToggle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAAC,IAAA,EAQlDC,GAAG,KAAK;EAAA,IAR2C;IACpDC,QAAQ;IACRC,KAAK;IACLC,SAAS;IACTC,aAAa;IACb;IACAC,EAAE,EAAEC,SAAS,GAAGd,MAAM;IACtB,GAAGe;EACL,CAAC,GAAAR,IAAA;EACC,MAAMS,MAAM,GAAGf,kBAAkB,CAACQ,QAAQ,EAAE,iBAAiB,CAAC;EAC9D,MAAMQ,eAAe,GAAGlB,UAAU,CAACJ,eAAe,CAAC;EACnD,IAAIiB,aAAa,KAAKM,SAAS,EAAE;IAC/BH,KAAK,CAACN,QAAQ,GAAGG,aAAa;EAChC;EACA,MAAM,CAACO,WAAW,CAAC,GAAGvB,iBAAiB,CAAC,CAAC;EACzCuB,WAAW,CAACX,GAAG,GAAGd,aAAa,CAACyB,WAAW,CAACX,GAAG,EAAEN,wBAAwB,CAACM,GAAG,EAAE,gBAAgB,CAAC,CAAC;;EAEjG;EACA;EACA,OAAO,aAAaJ,IAAI,CAACU,SAAS,EAAE;IAClCH,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAEK,MAAM,EAAEN,KAAK,IAAI,GAAGM,MAAM,QAAQ,EAAE,CAACC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACG,IAAI,KAAK,MAAM,CAAC;IACzI,GAAGD,WAAW;IACd,GAAGJ;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFV,cAAc,CAACgB,WAAW,GAAG,gBAAgB;AAC7C,eAAehB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}