{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport cardActionAreaClasses, { getCardActionAreaUtilityClass } from \"./cardActionAreaClasses.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    focusHighlight: ['focusHighlight']\n  };\n  return composeClasses(slots, getCardActionAreaUtilityClass, classes);\n};\nconst CardActionAreaRoot = styled(ButtonBase, {\n  name: 'MuiCardActionArea',\n  slot: 'Root'\n})(memoTheme(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    display: 'block',\n    textAlign: 'inherit',\n    borderRadius: 'inherit',\n    // for Safari to work https://github.com/mui/material-ui/issues/36285.\n    width: '100%',\n    [`&:hover .${cardActionAreaClasses.focusHighlight}`]: {\n      opacity: (theme.vars || theme).palette.action.hoverOpacity,\n      '@media (hover: none)': {\n        opacity: 0\n      }\n    },\n    [`&.${cardActionAreaClasses.focusVisible} .${cardActionAreaClasses.focusHighlight}`]: {\n      opacity: (theme.vars || theme).palette.action.focusOpacity\n    }\n  };\n}));\nconst CardActionAreaFocusHighlight = styled('span', {\n  name: 'MuiCardActionArea',\n  slot: 'FocusHighlight'\n})(memoTheme(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    overflow: 'hidden',\n    pointerEvents: 'none',\n    position: 'absolute',\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    borderRadius: 'inherit',\n    opacity: 0,\n    backgroundColor: 'currentcolor',\n    transition: theme.transitions.create('opacity', {\n      duration: theme.transitions.duration.short\n    })\n  };\n}));\nconst CardActionArea = /*#__PURE__*/React.forwardRef(function CardActionArea(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActionArea'\n  });\n  const {\n    children,\n    className,\n    focusVisibleClassName,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: CardActionAreaRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    shouldForwardComponentProp: true,\n    ownerState,\n    ref,\n    className: clsx(classes.root, className),\n    additionalProps: {\n      focusVisibleClassName: clsx(focusVisibleClassName, classes.focusVisible)\n    }\n  });\n  const [FocusHighlightSlot, focusHighlightProps] = useSlot('focusHighlight', {\n    elementType: CardActionAreaFocusHighlight,\n    externalForwardedProps,\n    ownerState,\n    ref,\n    className: classes.focusHighlight\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootProps,\n    children: [children, /*#__PURE__*/_jsx(FocusHighlightSlot, {\n      ...focusHighlightProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActionArea.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    focusHighlight: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    focusHighlight: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActionArea;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "memoTheme", "useDefaultProps", "cardActionAreaClasses", "getCardActionAreaUtilityClass", "ButtonBase", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "focusHighlight", "CardActionAreaRoot", "name", "slot", "_ref", "theme", "display", "textAlign", "borderRadius", "width", "opacity", "vars", "palette", "action", "hoverOpacity", "focusVisible", "focusOpacity", "CardActionAreaFocusHighlight", "_ref2", "overflow", "pointerEvents", "position", "top", "right", "bottom", "left", "backgroundColor", "transition", "transitions", "create", "duration", "short", "CardActionArea", "forwardRef", "inProps", "ref", "props", "children", "className", "focusVisibleClassName", "slotProps", "other", "externalForwardedProps", "RootSlot", "rootProps", "elementType", "shouldForwardComponentProp", "additionalProps", "FocusHighlightSlot", "focusHighlightProps", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "shape", "oneOfType", "func", "sx", "arrayOf", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/CardActionArea/CardActionArea.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport cardActionAreaClasses, { getCardActionAreaUtilityClass } from \"./cardActionAreaClasses.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    focusHighlight: ['focusHighlight']\n  };\n  return composeClasses(slots, getCardActionAreaUtilityClass, classes);\n};\nconst CardActionAreaRoot = styled(ButtonBase, {\n  name: 'MuiCardActionArea',\n  slot: 'Root'\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'block',\n  textAlign: 'inherit',\n  borderRadius: 'inherit',\n  // for Safari to work https://github.com/mui/material-ui/issues/36285.\n  width: '100%',\n  [`&:hover .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.hoverOpacity,\n    '@media (hover: none)': {\n      opacity: 0\n    }\n  },\n  [`&.${cardActionAreaClasses.focusVisible} .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.focusOpacity\n  }\n})));\nconst CardActionAreaFocusHighlight = styled('span', {\n  name: 'MuiCardActionArea',\n  slot: 'FocusHighlight'\n})(memoTheme(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  pointerEvents: 'none',\n  position: 'absolute',\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0,\n  borderRadius: 'inherit',\n  opacity: 0,\n  backgroundColor: 'currentcolor',\n  transition: theme.transitions.create('opacity', {\n    duration: theme.transitions.duration.short\n  })\n})));\nconst CardActionArea = /*#__PURE__*/React.forwardRef(function CardActionArea(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActionArea'\n  });\n  const {\n    children,\n    className,\n    focusVisibleClassName,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: CardActionAreaRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    shouldForwardComponentProp: true,\n    ownerState,\n    ref,\n    className: clsx(classes.root, className),\n    additionalProps: {\n      focusVisibleClassName: clsx(focusVisibleClassName, classes.focusVisible)\n    }\n  });\n  const [FocusHighlightSlot, focusHighlightProps] = useSlot('focusHighlight', {\n    elementType: CardActionAreaFocusHighlight,\n    externalForwardedProps,\n    ownerState,\n    ref,\n    className: classes.focusHighlight\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootProps,\n    children: [children, /*#__PURE__*/_jsx(FocusHighlightSlot, {\n      ...focusHighlightProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActionArea.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    focusHighlight: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    focusHighlight: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActionArea;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,qBAAqB,IAAIC,6BAA6B,QAAQ,4BAA4B;AACjG,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,cAAc,EAAE,CAAC,gBAAgB;EACnC,CAAC;EACD,OAAOjB,cAAc,CAACe,KAAK,EAAEV,6BAA6B,EAAES,OAAO,CAAC;AACtE,CAAC;AACD,MAAMI,kBAAkB,GAAGjB,MAAM,CAACK,UAAU,EAAE;EAC5Ca,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAClB,SAAS,CAACmB,IAAA;EAAA,IAAC;IACZC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,SAAS;IACpBC,YAAY,EAAE,SAAS;IACvB;IACAC,KAAK,EAAE,MAAM;IACb,CAAC,YAAYtB,qBAAqB,CAACa,cAAc,EAAE,GAAG;MACpDU,OAAO,EAAE,CAACL,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEO,OAAO,CAACC,MAAM,CAACC,YAAY;MAC1D,sBAAsB,EAAE;QACtBJ,OAAO,EAAE;MACX;IACF,CAAC;IACD,CAAC,KAAKvB,qBAAqB,CAAC4B,YAAY,KAAK5B,qBAAqB,CAACa,cAAc,EAAE,GAAG;MACpFU,OAAO,EAAE,CAACL,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEO,OAAO,CAACC,MAAM,CAACG;IAChD;EACF,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAMC,4BAA4B,GAAGjC,MAAM,CAAC,MAAM,EAAE;EAClDkB,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAClB,SAAS,CAACiC,KAAA;EAAA,IAAC;IACZb;EACF,CAAC,GAAAa,KAAA;EAAA,OAAM;IACLC,QAAQ,EAAE,QAAQ;IAClBC,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPjB,YAAY,EAAE,SAAS;IACvBE,OAAO,EAAE,CAAC;IACVgB,eAAe,EAAE,cAAc;IAC/BC,UAAU,EAAEtB,KAAK,CAACuB,WAAW,CAACC,MAAM,CAAC,SAAS,EAAE;MAC9CC,QAAQ,EAAEzB,KAAK,CAACuB,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC;EACH,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAMC,cAAc,GAAG,aAAapD,KAAK,CAACqD,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAMC,KAAK,GAAGlD,eAAe,CAAC;IAC5BkD,KAAK,EAAEF,OAAO;IACdhC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJmC,QAAQ;IACRC,SAAS;IACTC,qBAAqB;IACrBzC,KAAK,GAAG,CAAC,CAAC;IACV0C,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAGL,KAAK;EACT,MAAMxC,UAAU,GAAGwC,KAAK;EACxB,MAAMvC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM8C,sBAAsB,GAAG;IAC7B5C,KAAK;IACL0C;EACF,CAAC;EACD,MAAM,CAACG,QAAQ,EAAEC,SAAS,CAAC,GAAGtD,OAAO,CAAC,MAAM,EAAE;IAC5CuD,WAAW,EAAE5C,kBAAkB;IAC/ByC,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAGD;IACL,CAAC;IACDK,0BAA0B,EAAE,IAAI;IAChClD,UAAU;IACVuC,GAAG;IACHG,SAAS,EAAExD,IAAI,CAACe,OAAO,CAACE,IAAI,EAAEuC,SAAS,CAAC;IACxCS,eAAe,EAAE;MACfR,qBAAqB,EAAEzD,IAAI,CAACyD,qBAAqB,EAAE1C,OAAO,CAACkB,YAAY;IACzE;EACF,CAAC,CAAC;EACF,MAAM,CAACiC,kBAAkB,EAAEC,mBAAmB,CAAC,GAAG3D,OAAO,CAAC,gBAAgB,EAAE;IAC1EuD,WAAW,EAAE5B,4BAA4B;IACzCyB,sBAAsB;IACtB9C,UAAU;IACVuC,GAAG;IACHG,SAAS,EAAEzC,OAAO,CAACG;EACrB,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAACiD,QAAQ,EAAE;IAClC,GAAGC,SAAS;IACZP,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAa7C,IAAI,CAACwD,kBAAkB,EAAE;MACzD,GAAGC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpB,cAAc,CAACqB,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;EACEhB,QAAQ,EAAExD,SAAS,CAACyE,IAAI;EACxB;AACF;AACA;EACEzD,OAAO,EAAEhB,SAAS,CAAC0E,MAAM;EACzB;AACF;AACA;EACEjB,SAAS,EAAEzD,SAAS,CAAC2E,MAAM;EAC3B;AACF;AACA;EACEjB,qBAAqB,EAAE1D,SAAS,CAAC2E,MAAM;EACvC;AACF;AACA;AACA;EACEhB,SAAS,EAAE3D,SAAS,CAAC4E,KAAK,CAAC;IACzBzD,cAAc,EAAEnB,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAAC0E,MAAM,CAAC,CAAC;IACvExD,IAAI,EAAElB,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAAC0E,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEzD,KAAK,EAAEjB,SAAS,CAAC4E,KAAK,CAAC;IACrBzD,cAAc,EAAEnB,SAAS,CAACgE,WAAW;IACrC9C,IAAI,EAAElB,SAAS,CAACgE;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEe,EAAE,EAAE/E,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAACgF,OAAO,CAAChF,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAAC0E,MAAM,EAAE1E,SAAS,CAACiF,IAAI,CAAC,CAAC,CAAC,EAAEjF,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAAC0E,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAevB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}