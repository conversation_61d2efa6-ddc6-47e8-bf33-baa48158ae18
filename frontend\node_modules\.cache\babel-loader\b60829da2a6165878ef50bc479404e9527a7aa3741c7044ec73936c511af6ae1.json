{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\pages\\\\auth\\\\Register.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst schema = yup.object({\n  name: yup.string().required('Name is required').min(2, 'Name must be at least 2 characters'),\n  email: yup.string().email('Invalid email').required('Email is required'),\n  password: yup.string().required('Password is required').min(8, 'Password must be at least 8 characters'),\n  password_confirmation: yup.string().required('Password confirmation is required').oneOf([yup.ref('password')], 'Passwords must match'),\n  phone: yup.string().optional(),\n  date_of_birth: yup.string().optional()\n});\nconst Register = () => {\n  _s();\n  var _errors$name, _errors$email, _errors$password, _errors$password_conf, _errors$phone, _errors$date_of_birth;\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [success, setSuccess] = useState('');\n  const {\n    register: registerUser,\n    isAuthenticated\n  } = useAuth();\n  const navigate = useNavigate();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm({\n    resolver: yupResolver(schema)\n  });\n\n  // Redirect if already authenticated\n  React.useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard', {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, navigate]);\n  const onSubmit = async data => {\n    try {\n      setError('');\n      setSuccess('');\n      setLoading(true);\n      const result = await registerUser(data);\n      setSuccess('Registration successful! Please check your email for verification.');\n\n      // Use redirect URL from backend if provided, otherwise default to dashboard\n      const redirectUrl = result.redirect_url || '/dashboard';\n\n      // Redirect after a short delay\n      setTimeout(() => {\n        navigate(redirectUrl);\n      }, 2000);\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response2$data;\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Registration failed. Please try again.';\n      const validationErrors = (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.errors;\n      if (validationErrors) {\n        const errorMessages = Object.values(validationErrors).flat().join(', ');\n        setError(errorMessages);\n      } else {\n        setError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n              className: \"text-center mb-4\",\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 25\n            }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"success\",\n              children: success\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 27\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleSubmit(onSubmit),\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Full Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 89,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      ...register('name'),\n                      isInvalid: !!errors.name,\n                      placeholder: \"Enter your full name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 90,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$name = errors.name) === null || _errors$name === void 0 ? void 0 : _errors$name.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 96,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 104,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"email\",\n                      ...register('email'),\n                      isInvalid: !!errors.email,\n                      placeholder: \"Enter your email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 105,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$email = errors.email) === null || _errors$email === void 0 ? void 0 : _errors$email.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"password\",\n                      ...register('password'),\n                      isInvalid: !!errors.password,\n                      placeholder: \"Enter your password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 122,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$password = errors.password) === null || _errors$password === void 0 ? void 0 : _errors$password.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 128,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Confirm Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"password\",\n                      ...register('password_confirmation'),\n                      isInvalid: !!errors.password_confirmation,\n                      placeholder: \"Confirm your password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$password_conf = errors.password_confirmation) === null || _errors$password_conf === void 0 ? void 0 : _errors$password_conf.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 143,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Phone (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 153,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"tel\",\n                      ...register('phone'),\n                      isInvalid: !!errors.phone,\n                      placeholder: \"Enter your phone number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$phone = errors.phone) === null || _errors$phone === void 0 ? void 0 : _errors$phone.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Date of Birth (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"date\",\n                      ...register('date_of_birth'),\n                      isInvalid: !!errors.date_of_birth\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$date_of_birth = errors.date_of_birth) === null || _errors$date_of_birth === void 0 ? void 0 : _errors$date_of_birth.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                type: \"submit\",\n                className: \"w-100 mb-3\",\n                disabled: loading,\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 23\n                  }, this), \"Creating account...\"]\n                }, void 0, true) : 'Register'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Already have an account? \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"text-decoration-none\",\n                children: \"Login here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"RqsASo1fVHuXyvtNLIRFQnnmzxg=\", false, function () {\n  return [useAuth, useNavigate, useForm];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "useForm", "yupResolver", "yup", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "schema", "object", "name", "string", "required", "min", "email", "password", "password_confirmation", "oneOf", "ref", "phone", "optional", "date_of_birth", "Register", "_s", "_errors$name", "_errors$email", "_errors$password", "_errors$password_conf", "_errors$phone", "_errors$date_of_birth", "error", "setError", "loading", "setLoading", "success", "setSuccess", "register", "registerUser", "isAuthenticated", "navigate", "handleSubmit", "formState", "errors", "resolver", "useEffect", "replace", "onSubmit", "data", "result", "redirectUrl", "redirect_url", "setTimeout", "err", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "errorMessage", "response", "message", "validationErrors", "errorMessages", "Object", "values", "flat", "join", "children", "className", "md", "lg", "Body", "Title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "Group", "Label", "Control", "type", "isInvalid", "placeholder", "<PERSON><PERSON><PERSON>", "disabled", "as", "animation", "size", "role", "to", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/pages/auth/Register.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useN<PERSON><PERSON>, <PERSON> } from 'react-router-dom';\nimport { Container, Row, Col, Card, Form, <PERSON><PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { RegisterData } from '../../services/authService';\n\nconst schema: yup.ObjectSchema<RegisterData> = yup.object({\n  name: yup.string().required('Name is required').min(2, 'Name must be at least 2 characters'),\n  email: yup.string().email('Invalid email').required('Email is required'),\n  password: yup.string().required('Password is required').min(8, 'Password must be at least 8 characters'),\n  password_confirmation: yup.string()\n    .required('Password confirmation is required')\n    .oneOf([yup.ref('password')], 'Passwords must match'),\n  phone: yup.string().optional(),\n  date_of_birth: yup.string().optional(),\n});\n\nconst Register: React.FC = () => {\n  const [error, setError] = useState<string>('');\n  const [loading, setLoading] = useState(false);\n  const [success, setSuccess] = useState<string>('');\n  const { register: registerUser, isAuthenticated } = useAuth();\n  const navigate = useNavigate();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<RegisterData>({\n    resolver: yupResolver(schema),\n  });\n\n  // Redirect if already authenticated\n  React.useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard', { replace: true });\n    }\n  }, [isAuthenticated, navigate]);\n\n  const onSubmit = async (data: RegisterData) => {\n    try {\n      setError('');\n      setSuccess('');\n      setLoading(true);\n\n      const result = await registerUser(data);\n      setSuccess('Registration successful! Please check your email for verification.');\n\n      // Use redirect URL from backend if provided, otherwise default to dashboard\n      const redirectUrl = result.redirect_url || '/dashboard';\n\n      // Redirect after a short delay\n      setTimeout(() => {\n        navigate(redirectUrl);\n      }, 2000);\n    } catch (err: any) {\n      const errorMessage = err.response?.data?.message || 'Registration failed. Please try again.';\n      const validationErrors = err.response?.data?.errors;\n\n      if (validationErrors) {\n        const errorMessages = Object.values(validationErrors).flat().join(', ');\n        setError(errorMessages);\n      } else {\n        setError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container>\n      <Row className=\"justify-content-center\">\n        <Col md={8} lg={6}>\n          <Card>\n            <Card.Body>\n              <Card.Title className=\"text-center mb-4\">Register</Card.Title>\n\n              {error && <Alert variant=\"danger\">{error}</Alert>}\n              {success && <Alert variant=\"success\">{success}</Alert>}\n\n              <Form onSubmit={handleSubmit(onSubmit)}>\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Full Name</Form.Label>\n                      <Form.Control\n                        type=\"text\"\n                        {...register('name')}\n                        isInvalid={!!errors.name}\n                        placeholder=\"Enter your full name\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.name?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Email</Form.Label>\n                      <Form.Control\n                        type=\"email\"\n                        {...register('email')}\n                        isInvalid={!!errors.email}\n                        placeholder=\"Enter your email\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.email?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Password</Form.Label>\n                      <Form.Control\n                        type=\"password\"\n                        {...register('password')}\n                        isInvalid={!!errors.password}\n                        placeholder=\"Enter your password\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.password?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Confirm Password</Form.Label>\n                      <Form.Control\n                        type=\"password\"\n                        {...register('password_confirmation')}\n                        isInvalid={!!errors.password_confirmation}\n                        placeholder=\"Confirm your password\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.password_confirmation?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Phone (Optional)</Form.Label>\n                      <Form.Control\n                        type=\"tel\"\n                        {...register('phone')}\n                        isInvalid={!!errors.phone}\n                        placeholder=\"Enter your phone number\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.phone?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Date of Birth (Optional)</Form.Label>\n                      <Form.Control\n                        type=\"date\"\n                        {...register('date_of_birth')}\n                        isInvalid={!!errors.date_of_birth}\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.date_of_birth?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Button\n                  variant=\"primary\"\n                  type=\"submit\"\n                  className=\"w-100 mb-3\"\n                  disabled={loading}\n                >\n                  {loading ? (\n                    <>\n                      <Spinner\n                        as=\"span\"\n                        animation=\"border\"\n                        size=\"sm\"\n                        role=\"status\"\n                        aria-hidden=\"true\"\n                        className=\"me-2\"\n                      />\n                      Creating account...\n                    </>\n                  ) : (\n                    'Register'\n                  )}\n                </Button>\n              </Form>\n\n              <div className=\"text-center\">\n                <span>Already have an account? </span>\n                <Link to=\"/login\" className=\"text-decoration-none\">\n                  Login here\n                </Link>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACzF,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAMC,MAAsC,GAAGN,GAAG,CAACO,MAAM,CAAC;EACxDC,IAAI,EAAER,GAAG,CAACS,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,kBAAkB,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,oCAAoC,CAAC;EAC5FC,KAAK,EAAEZ,GAAG,CAACS,MAAM,CAAC,CAAC,CAACG,KAAK,CAAC,eAAe,CAAC,CAACF,QAAQ,CAAC,mBAAmB,CAAC;EACxEG,QAAQ,EAAEb,GAAG,CAACS,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,sBAAsB,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;EACxGG,qBAAqB,EAAEd,GAAG,CAACS,MAAM,CAAC,CAAC,CAChCC,QAAQ,CAAC,mCAAmC,CAAC,CAC7CK,KAAK,CAAC,CAACf,GAAG,CAACgB,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,sBAAsB,CAAC;EACvDC,KAAK,EAAEjB,GAAG,CAACS,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;EAC9BC,aAAa,EAAEnB,GAAG,CAACS,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC;AACvC,CAAC,CAAC;AAEF,MAAME,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,qBAAA;EAC/B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAS,EAAE,CAAC;EAClD,MAAM;IAAE+C,QAAQ,EAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGnC,OAAO,CAAC,CAAC;EAC7D,MAAMoC,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAE9B,MAAM;IACJ8C,QAAQ;IACRI,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO;EACtB,CAAC,GAAG1C,OAAO,CAAe;IACxB2C,QAAQ,EAAE1C,WAAW,CAACO,MAAM;EAC9B,CAAC,CAAC;;EAEF;EACApB,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB,IAAIN,eAAe,EAAE;MACnBC,QAAQ,CAAC,YAAY,EAAE;QAAEM,OAAO,EAAE;MAAK,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACP,eAAe,EAAEC,QAAQ,CAAC,CAAC;EAE/B,MAAMO,QAAQ,GAAG,MAAOC,IAAkB,IAAK;IAC7C,IAAI;MACFhB,QAAQ,CAAC,EAAE,CAAC;MACZI,UAAU,CAAC,EAAE,CAAC;MACdF,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMe,MAAM,GAAG,MAAMX,YAAY,CAACU,IAAI,CAAC;MACvCZ,UAAU,CAAC,oEAAoE,CAAC;;MAEhF;MACA,MAAMc,WAAW,GAAGD,MAAM,CAACE,YAAY,IAAI,YAAY;;MAEvD;MACAC,UAAU,CAAC,MAAM;QACfZ,QAAQ,CAACU,WAAW,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOG,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACjB,MAAMC,YAAY,GAAG,EAAAJ,aAAA,GAAAD,GAAG,CAACM,QAAQ,cAAAL,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcN,IAAI,cAAAO,kBAAA,uBAAlBA,kBAAA,CAAoBK,OAAO,KAAI,wCAAwC;MAC5F,MAAMC,gBAAgB,IAAAL,cAAA,GAAGH,GAAG,CAACM,QAAQ,cAAAH,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcR,IAAI,cAAAS,mBAAA,uBAAlBA,mBAAA,CAAoBd,MAAM;MAEnD,IAAIkB,gBAAgB,EAAE;QACpB,MAAMC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACH,gBAAgB,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACvElC,QAAQ,CAAC8B,aAAa,CAAC;MACzB,CAAC,MAAM;QACL9B,QAAQ,CAAC0B,YAAY,CAAC;MACxB;IACF,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACE5B,OAAA,CAACb,SAAS;IAAA0E,QAAA,eACR7D,OAAA,CAACZ,GAAG;MAAC0E,SAAS,EAAC,wBAAwB;MAAAD,QAAA,eACrC7D,OAAA,CAACX,GAAG;QAAC0E,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,eAChB7D,OAAA,CAACV,IAAI;UAAAuE,QAAA,eACH7D,OAAA,CAACV,IAAI,CAAC2E,IAAI;YAAAJ,QAAA,gBACR7D,OAAA,CAACV,IAAI,CAAC4E,KAAK;cAACJ,SAAS,EAAC,kBAAkB;cAAAD,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAE7D7C,KAAK,iBAAIzB,OAAA,CAACP,KAAK;cAAC8E,OAAO,EAAC,QAAQ;cAAAV,QAAA,EAAEpC;YAAK;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAChDzC,OAAO,iBAAI7B,OAAA,CAACP,KAAK;cAAC8E,OAAO,EAAC,SAAS;cAAAV,QAAA,EAAEhC;YAAO;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEtDtE,OAAA,CAACT,IAAI;cAACkD,QAAQ,EAAEN,YAAY,CAACM,QAAQ,CAAE;cAAAoB,QAAA,gBACrC7D,OAAA,CAACZ,GAAG;gBAAAyE,QAAA,gBACF7D,OAAA,CAACX,GAAG;kBAAC0E,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT7D,OAAA,CAACT,IAAI,CAACiF,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B7D,OAAA,CAACT,IAAI,CAACkF,KAAK;sBAAAZ,QAAA,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAClCtE,OAAA,CAACT,IAAI,CAACmF,OAAO;sBACXC,IAAI,EAAC,MAAM;sBAAA,GACP5C,QAAQ,CAAC,MAAM,CAAC;sBACpB6C,SAAS,EAAE,CAAC,CAACvC,MAAM,CAAChC,IAAK;sBACzBwE,WAAW,EAAC;oBAAsB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACFtE,OAAA,CAACT,IAAI,CAACmF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAA1C,YAAA,GAClCkB,MAAM,CAAChC,IAAI,cAAAc,YAAA,uBAAXA,YAAA,CAAamC;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENtE,OAAA,CAACX,GAAG;kBAAC0E,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT7D,OAAA,CAACT,IAAI,CAACiF,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B7D,OAAA,CAACT,IAAI,CAACkF,KAAK;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC9BtE,OAAA,CAACT,IAAI,CAACmF,OAAO;sBACXC,IAAI,EAAC,OAAO;sBAAA,GACR5C,QAAQ,CAAC,OAAO,CAAC;sBACrB6C,SAAS,EAAE,CAAC,CAACvC,MAAM,CAAC5B,KAAM;sBAC1BoE,WAAW,EAAC;oBAAkB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACFtE,OAAA,CAACT,IAAI,CAACmF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAAzC,aAAA,GAClCiB,MAAM,CAAC5B,KAAK,cAAAW,aAAA,uBAAZA,aAAA,CAAckC;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA,CAACZ,GAAG;gBAAAyE,QAAA,gBACF7D,OAAA,CAACX,GAAG;kBAAC0E,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT7D,OAAA,CAACT,IAAI,CAACiF,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B7D,OAAA,CAACT,IAAI,CAACkF,KAAK;sBAAAZ,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjCtE,OAAA,CAACT,IAAI,CAACmF,OAAO;sBACXC,IAAI,EAAC,UAAU;sBAAA,GACX5C,QAAQ,CAAC,UAAU,CAAC;sBACxB6C,SAAS,EAAE,CAAC,CAACvC,MAAM,CAAC3B,QAAS;sBAC7BmE,WAAW,EAAC;oBAAqB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,eACFtE,OAAA,CAACT,IAAI,CAACmF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAAxC,gBAAA,GAClCgB,MAAM,CAAC3B,QAAQ,cAAAW,gBAAA,uBAAfA,gBAAA,CAAiBiC;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENtE,OAAA,CAACX,GAAG;kBAAC0E,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT7D,OAAA,CAACT,IAAI,CAACiF,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B7D,OAAA,CAACT,IAAI,CAACkF,KAAK;sBAAAZ,QAAA,EAAC;oBAAgB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzCtE,OAAA,CAACT,IAAI,CAACmF,OAAO;sBACXC,IAAI,EAAC,UAAU;sBAAA,GACX5C,QAAQ,CAAC,uBAAuB,CAAC;sBACrC6C,SAAS,EAAE,CAAC,CAACvC,MAAM,CAAC1B,qBAAsB;sBAC1CkE,WAAW,EAAC;oBAAuB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACFtE,OAAA,CAACT,IAAI,CAACmF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAAvC,qBAAA,GAClCe,MAAM,CAAC1B,qBAAqB,cAAAW,qBAAA,uBAA5BA,qBAAA,CAA8BgC;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA,CAACZ,GAAG;gBAAAyE,QAAA,gBACF7D,OAAA,CAACX,GAAG;kBAAC0E,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT7D,OAAA,CAACT,IAAI,CAACiF,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B7D,OAAA,CAACT,IAAI,CAACkF,KAAK;sBAAAZ,QAAA,EAAC;oBAAgB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzCtE,OAAA,CAACT,IAAI,CAACmF,OAAO;sBACXC,IAAI,EAAC,KAAK;sBAAA,GACN5C,QAAQ,CAAC,OAAO,CAAC;sBACrB6C,SAAS,EAAE,CAAC,CAACvC,MAAM,CAACvB,KAAM;sBAC1B+D,WAAW,EAAC;oBAAyB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACFtE,OAAA,CAACT,IAAI,CAACmF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAAtC,aAAA,GAClCc,MAAM,CAACvB,KAAK,cAAAS,aAAA,uBAAZA,aAAA,CAAc+B;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENtE,OAAA,CAACX,GAAG;kBAAC0E,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT7D,OAAA,CAACT,IAAI,CAACiF,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B7D,OAAA,CAACT,IAAI,CAACkF,KAAK;sBAAAZ,QAAA,EAAC;oBAAwB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjDtE,OAAA,CAACT,IAAI,CAACmF,OAAO;sBACXC,IAAI,EAAC,MAAM;sBAAA,GACP5C,QAAQ,CAAC,eAAe,CAAC;sBAC7B6C,SAAS,EAAE,CAAC,CAACvC,MAAM,CAACrB;oBAAc;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACFtE,OAAA,CAACT,IAAI,CAACmF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAArC,qBAAA,GAClCa,MAAM,CAACrB,aAAa,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsB8B;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA,CAACR,MAAM;gBACL+E,OAAO,EAAC,SAAS;gBACjBI,IAAI,EAAC,QAAQ;gBACbb,SAAS,EAAC,YAAY;gBACtBiB,QAAQ,EAAEpD,OAAQ;gBAAAkC,QAAA,EAEjBlC,OAAO,gBACN3B,OAAA,CAAAE,SAAA;kBAAA2D,QAAA,gBACE7D,OAAA,CAACN,OAAO;oBACNsF,EAAE,EAAC,MAAM;oBACTC,SAAS,EAAC,QAAQ;oBAClBC,IAAI,EAAC,IAAI;oBACTC,IAAI,EAAC,QAAQ;oBACb,eAAY,MAAM;oBAClBrB,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,uBAEJ;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEPtE,OAAA;cAAK8D,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1B7D,OAAA;gBAAA6D,QAAA,EAAM;cAAyB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCtE,OAAA,CAACd,IAAI;gBAACkG,EAAE,EAAC,QAAQ;gBAACtB,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EAAC;cAEnD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACpD,EAAA,CApMID,QAAkB;EAAA,QAI8BnB,OAAO,EAC1Cb,WAAW,EAMxBU,OAAO;AAAA;AAAA0F,EAAA,GAXPpE,QAAkB;AAsMxB,eAAeA,QAAQ;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}