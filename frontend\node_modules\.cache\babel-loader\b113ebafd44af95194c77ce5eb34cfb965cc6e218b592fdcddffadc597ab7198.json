{"ast": null, "code": "export { default } from \"./AvatarGroup.js\";\nexport { default as avatarGroupClasses } from \"./avatarGroupClasses.js\";\nexport * from \"./avatarGroupClasses.js\";", "map": {"version": 3, "names": ["default", "avatarGroupClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/AvatarGroup/index.js"], "sourcesContent": ["export { default } from \"./AvatarGroup.js\";\nexport { default as avatarGroupClasses } from \"./avatarGroupClasses.js\";\nexport * from \"./avatarGroupClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,yBAAyB;AACvE,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}