{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  ...props\n}, ref) => {\n  const finalClassName = classNames(className, useBootstrapPrefix(bsPrefix, 'carousel-item'));\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: finalClassName\n  });\n});\nCarouselItem.displayName = 'CarouselItem';\nexport default CarouselItem;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "CarouselItem", "forwardRef", "as", "Component", "bsPrefix", "className", "props", "ref", "finalClassName", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/CarouselItem.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  ...props\n}, ref) => {\n  const finalClassName = classNames(className, useBootstrapPrefix(bsPrefix, 'carousel-item'));\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: finalClassName\n  });\n});\nCarouselItem.displayName = 'CarouselItem';\nexport default CarouselItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EAClD;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrBC,QAAQ;EACRC,SAAS;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,cAAc,GAAGb,UAAU,CAACU,SAAS,EAAER,kBAAkB,CAACO,QAAQ,EAAE,eAAe,CAAC,CAAC;EAC3F,OAAO,aAAaL,IAAI,CAACI,SAAS,EAAE;IAClCI,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRD,SAAS,EAAEG;EACb,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,YAAY,CAACS,WAAW,GAAG,cAAc;AACzC,eAAeT,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}