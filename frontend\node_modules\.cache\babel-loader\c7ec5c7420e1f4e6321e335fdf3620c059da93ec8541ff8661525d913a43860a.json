{"ast": null, "code": "export { default } from \"./composeClasses.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/utils/esm/composeClasses/index.js"], "sourcesContent": ["export { default } from \"./composeClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}