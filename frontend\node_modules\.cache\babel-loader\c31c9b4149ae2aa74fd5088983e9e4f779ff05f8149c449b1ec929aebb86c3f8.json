{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport warning from 'warning';\nimport Col from './Col';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormLabel = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'label',\n    bsPrefix,\n    column = false,\n    visuallyHidden = false,\n    className,\n    htmlFor,\n    ...props\n  } = _ref;\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-label');\n  let columnClass = 'col-form-label';\n  if (typeof column === 'string') columnClass = `${columnClass} ${columnClass}-${column}`;\n  const classes = classNames(className, bsPrefix, visuallyHidden && 'visually-hidden', column && columnClass);\n  process.env.NODE_ENV !== \"production\" ? warning(controlId == null || !htmlFor, '`controlId` is ignored on `<FormLabel>` when `htmlFor` is specified.') : void 0;\n  htmlFor = htmlFor || controlId;\n  if (column) return /*#__PURE__*/_jsx(Col, {\n    ref: ref,\n    as: \"label\",\n    className: classes,\n    htmlFor: htmlFor,\n    ...props\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classes,\n    htmlFor: htmlFor,\n    ...props\n  });\n});\nFormLabel.displayName = 'FormLabel';\nexport default FormLabel;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "warning", "Col", "FormContext", "useBootstrapPrefix", "jsx", "_jsx", "FormLabel", "forwardRef", "_ref", "ref", "as", "Component", "bsPrefix", "column", "visuallyHidden", "className", "htmlFor", "props", "controlId", "columnClass", "classes", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FormLabel.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport warning from 'warning';\nimport Col from './Col';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormLabel = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'label',\n  bsPrefix,\n  column = false,\n  visuallyHidden = false,\n  className,\n  htmlFor,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-label');\n  let columnClass = 'col-form-label';\n  if (typeof column === 'string') columnClass = `${columnClass} ${columnClass}-${column}`;\n  const classes = classNames(className, bsPrefix, visuallyHidden && 'visually-hidden', column && columnClass);\n  process.env.NODE_ENV !== \"production\" ? warning(controlId == null || !htmlFor, '`controlId` is ignored on `<FormLabel>` when `htmlFor` is specified.') : void 0;\n  htmlFor = htmlFor || controlId;\n  if (column) return /*#__PURE__*/_jsx(Col, {\n    ref: ref,\n    as: \"label\",\n    className: classes,\n    htmlFor: htmlFor,\n    ...props\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classes,\n    htmlFor: htmlFor,\n    ...props\n  });\n});\nFormLabel.displayName = 'FormLabel';\nexport default FormLabel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,OAAO,MAAM,SAAS;AAC7B,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAS7CC,GAAG,KAAK;EAAA,IATsC;IAC/C;IACAC,EAAE,EAAEC,SAAS,GAAG,OAAO;IACvBC,QAAQ;IACRC,MAAM,GAAG,KAAK;IACdC,cAAc,GAAG,KAAK;IACtBC,SAAS;IACTC,OAAO;IACP,GAAGC;EACL,CAAC,GAAAT,IAAA;EACC,MAAM;IACJU;EACF,CAAC,GAAGnB,UAAU,CAACG,WAAW,CAAC;EAC3BU,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,YAAY,CAAC;EACrD,IAAIO,WAAW,GAAG,gBAAgB;EAClC,IAAI,OAAON,MAAM,KAAK,QAAQ,EAAEM,WAAW,GAAG,GAAGA,WAAW,IAAIA,WAAW,IAAIN,MAAM,EAAE;EACvF,MAAMO,OAAO,GAAGvB,UAAU,CAACkB,SAAS,EAAEH,QAAQ,EAAEE,cAAc,IAAI,iBAAiB,EAAED,MAAM,IAAIM,WAAW,CAAC;EAC3GE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,OAAO,CAACkB,SAAS,IAAI,IAAI,IAAI,CAACF,OAAO,EAAE,sEAAsE,CAAC,GAAG,KAAK,CAAC;EAC/JA,OAAO,GAAGA,OAAO,IAAIE,SAAS;EAC9B,IAAIL,MAAM,EAAE,OAAO,aAAaR,IAAI,CAACJ,GAAG,EAAE;IACxCQ,GAAG,EAAEA,GAAG;IACRC,EAAE,EAAE,OAAO;IACXK,SAAS,EAAEK,OAAO;IAClBJ,OAAO,EAAEA,OAAO;IAChB,GAAGC;EACL,CAAC,CAAC;EACF,OAAO,aAAaZ,IAAI,CAACM,SAAS,EAAE;IAClCF,GAAG,EAAEA,GAAG;IACRM,SAAS,EAAEK,OAAO;IAClBJ,OAAO,EAAEA,OAAO;IAChB,GAAGC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFX,SAAS,CAACkB,WAAW,GAAG,WAAW;AACnC,eAAelB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}