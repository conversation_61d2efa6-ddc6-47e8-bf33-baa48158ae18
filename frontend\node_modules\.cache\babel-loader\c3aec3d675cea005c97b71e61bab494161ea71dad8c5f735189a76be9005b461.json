{"ast": null, "code": "'use client';\n\n// @inheritedComponent IconButton\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system/colorManipulator';\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport SwitchBase from \"../internal/SwitchBase.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport switchClasses, { getSwitchUtilityClass } from \"./switchClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return {\n    ...classes,\n    // forward the disabled and checked classes to the SwitchBase\n    ...composedClasses\n  };\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n})), memoTheme(({\n  theme\n}) => ({\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['light'])) // check all the used fields in the style below\n  .map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      [`&.${switchClasses.checked}`]: {\n        color: (theme.vars || theme).palette[color].main,\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n          '@media (hover: none)': {\n            backgroundColor: 'transparent'\n          }\n        },\n        [`&.${switchClasses.disabled}`]: {\n          color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.55)}`\n        }\n      },\n      [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n        backgroundColor: (theme.vars || theme).palette[color].main\n      }\n    }\n  }))]\n})));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track'\n})(memoTheme(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n})));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb'\n})(memoTheme(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n})));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n    className,\n    color = 'primary',\n    edge = false,\n    size = 'medium',\n    sx,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    color,\n    edge,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: SwitchRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      sx\n    }\n  });\n  const [ThumbSlot, thumbSlotProps] = useSlot('thumb', {\n    className: classes.thumb,\n    elementType: SwitchThumb,\n    externalForwardedProps,\n    ownerState\n  });\n  const icon = /*#__PURE__*/_jsx(ThumbSlot, {\n    ...thumbSlotProps\n  });\n  const [TrackSlot, trackSlotProps] = useSlot('track', {\n    className: classes.track,\n    elementType: SwitchTrack,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, {\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState,\n      ...other,\n      classes: {\n        ...classes,\n        root: classes.switchBase\n      },\n      slots: {\n        ...(slots.switchBase && {\n          root: slots.switchBase\n        }),\n        ...(slots.input && {\n          input: slots.input\n        })\n      },\n      slotProps: {\n        ...(slotProps.switchBase && {\n          root: typeof slotProps.switchBase === 'function' ? slotProps.switchBase(ownerState) : slotProps.switchBase\n        }),\n        ...(slotProps.input && {\n          input: typeof slotProps.input === 'function' ? slotProps.input(ownerState) : slotProps.input\n        })\n      }\n    }), /*#__PURE__*/_jsx(TrackSlot, {\n      ...trackSlotProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/input#attributes) applied to the `input` element.\n   * @deprecated Use `slotProps.input` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   * @deprecated Use `slotProps.input.ref` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    switchBase: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType,\n    switchBase: PropTypes.elementType,\n    thumb: PropTypes.elementType,\n    track: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "refType", "composeClasses", "alpha", "darken", "lighten", "capitalize", "createSimplePaletteValueFilter", "SwitchBase", "styled", "memoTheme", "useDefaultProps", "switchClasses", "getSwitchUtilityClass", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "edge", "size", "color", "checked", "disabled", "slots", "root", "switchBase", "thumb", "track", "input", "composedClasses", "SwitchRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "width", "height", "overflow", "padding", "boxSizing", "position", "flexShrink", "zIndex", "verticalAlign", "colorAdjust", "variants", "style", "marginLeft", "marginRight", "transform", "SwitchSwitchBase", "theme", "top", "left", "vars", "palette", "Switch", "defaultColor", "mode", "common", "white", "grey", "transition", "transitions", "create", "duration", "shortest", "defaultDisabledColor", "opacity", "switchTrackDisabled", "backgroundColor", "action", "activeChannel", "hoverOpacity", "active", "Object", "entries", "filter", "map", "main", "mainChannel", "SwitchTrack", "borderRadius", "onBackground", "black", "switchTrack", "SwitchThumb", "boxShadow", "shadows", "forwardRef", "inProps", "ref", "className", "sx", "slotProps", "other", "externalForwardedProps", "RootSlot", "rootSlotProps", "elementType", "additionalProps", "ThumbSlot", "thumbSlotProps", "icon", "TrackSlot", "trackSlotProps", "children", "type", "checkedIcon", "process", "env", "NODE_ENV", "propTypes", "bool", "node", "object", "string", "oneOfType", "oneOf", "defaultChecked", "disable<PERSON><PERSON><PERSON>", "id", "inputProps", "inputRef", "onChange", "func", "required", "shape", "arrayOf", "value", "any"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Switch/Switch.js"], "sourcesContent": ["'use client';\n\n// @inheritedComponent IconButton\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system/colorManipulator';\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport SwitchBase from \"../internal/SwitchBase.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport switchClasses, { getSwitchUtilityClass } from \"./switchClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return {\n    ...classes,\n    // forward the disabled and checked classes to the SwitchBase\n    ...composedClasses\n  };\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n})), memoTheme(({\n  theme\n}) => ({\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['light'])) // check all the used fields in the style below\n  .map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      [`&.${switchClasses.checked}`]: {\n        color: (theme.vars || theme).palette[color].main,\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n          '@media (hover: none)': {\n            backgroundColor: 'transparent'\n          }\n        },\n        [`&.${switchClasses.disabled}`]: {\n          color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.55)}`\n        }\n      },\n      [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n        backgroundColor: (theme.vars || theme).palette[color].main\n      }\n    }\n  }))]\n})));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track'\n})(memoTheme(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n})));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb'\n})(memoTheme(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n})));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n    className,\n    color = 'primary',\n    edge = false,\n    size = 'medium',\n    sx,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    color,\n    edge,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: SwitchRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      sx\n    }\n  });\n  const [ThumbSlot, thumbSlotProps] = useSlot('thumb', {\n    className: classes.thumb,\n    elementType: SwitchThumb,\n    externalForwardedProps,\n    ownerState\n  });\n  const icon = /*#__PURE__*/_jsx(ThumbSlot, {\n    ...thumbSlotProps\n  });\n  const [TrackSlot, trackSlotProps] = useSlot('track', {\n    className: classes.track,\n    elementType: SwitchTrack,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, {\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState,\n      ...other,\n      classes: {\n        ...classes,\n        root: classes.switchBase\n      },\n      slots: {\n        ...(slots.switchBase && {\n          root: slots.switchBase\n        }),\n        ...(slots.input && {\n          input: slots.input\n        })\n      },\n      slotProps: {\n        ...(slotProps.switchBase && {\n          root: typeof slotProps.switchBase === 'function' ? slotProps.switchBase(ownerState) : slotProps.switchBase\n        }),\n        ...(slotProps.input && {\n          input: typeof slotProps.input === 'function' ? slotProps.input(ownerState) : slotProps.input\n        })\n      }\n    }), /*#__PURE__*/_jsx(TrackSlot, {\n      ...trackSlotProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/input#attributes) applied to the `input` element.\n   * @deprecated Use `slotProps.input` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   * @deprecated Use `slotProps.input.ref` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    switchBase: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType,\n    switchBase: PropTypes.elementType,\n    thumb: PropTypes.elementType,\n    track: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,EAAEC,MAAM,EAAEC,OAAO,QAAQ,8BAA8B;AACrE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,8BAA8B,MAAM,4CAA4C;AACvF,OAAOC,UAAU,MAAM,2BAA2B;AAClD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,aAAa,IAAIC,qBAAqB,QAAQ,oBAAoB;AACzE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC,IAAI;IACJC,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,IAAI,IAAI,OAAOhB,UAAU,CAACgB,IAAI,CAAC,EAAE,EAAE,OAAOhB,UAAU,CAACiB,IAAI,CAAC,EAAE,CAAC;IAC5EM,UAAU,EAAE,CAAC,YAAY,EAAE,QAAQvB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAEC,OAAO,IAAI,SAAS,EAAEC,QAAQ,IAAI,UAAU,CAAC;IACrGI,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,MAAMC,eAAe,GAAG/B,cAAc,CAACyB,KAAK,EAAEd,qBAAqB,EAAEQ,OAAO,CAAC;EAC7E,OAAO;IACL,GAAGA,OAAO;IACV;IACA,GAAGY;EACL,CAAC;AACH,CAAC;AACD,MAAMC,UAAU,GAAGzB,MAAM,CAAC,MAAM,EAAE;EAChC0B,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACX,IAAI,EAAER,UAAU,CAACE,IAAI,IAAIiB,MAAM,CAAC,OAAOjC,UAAU,CAACc,UAAU,CAACE,IAAI,CAAC,EAAE,CAAC,EAAEiB,MAAM,CAAC,OAAOjC,UAAU,CAACc,UAAU,CAACG,IAAI,CAAC,EAAE,CAAC,CAAC;EACrI;AACF,CAAC,CAAC,CAAC;EACDiB,OAAO,EAAE,aAAa;EACtBC,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAClBC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACnBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,CAAC;EACT;EACAC,aAAa,EAAE,QAAQ;EACvB;EACA,cAAc,EAAE;IACdC,WAAW,EAAE;EACf,CAAC;EACDC,QAAQ,EAAE,CAAC;IACTb,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACD8B,KAAK,EAAE;MACLC,UAAU,EAAE,CAAC;IACf;EACF,CAAC,EAAE;IACDf,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACD8B,KAAK,EAAE;MACLE,WAAW,EAAE,CAAC;IAChB;EACF,CAAC,EAAE;IACDhB,KAAK,EAAE;MACLf,IAAI,EAAE;IACR,CAAC;IACD6B,KAAK,EAAE;MACLX,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVE,OAAO,EAAE,CAAC;MACV,CAAC,MAAMhC,aAAa,CAACkB,KAAK,EAAE,GAAG;QAC7BW,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;MACV,CAAC;MACD,CAAC,MAAM9B,aAAa,CAACiB,UAAU,EAAE,GAAG;QAClCe,OAAO,EAAE,CAAC;QACV,CAAC,KAAKhC,aAAa,CAACa,OAAO,EAAE,GAAG;UAC9B8B,SAAS,EAAE;QACb;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,gBAAgB,GAAG/C,MAAM,CAACD,UAAU,EAAE;EAC1C2B,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,YAAY;EAClBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACV,UAAU,EAAE;MACzB,CAAC,MAAMjB,aAAa,CAACoB,KAAK,EAAE,GAAGO,MAAM,CAACP;IACxC,CAAC,EAAEZ,UAAU,CAACI,KAAK,KAAK,SAAS,IAAIe,MAAM,CAAC,QAAQjC,UAAU,CAACc,UAAU,CAACI,KAAK,CAAC,EAAE,CAAC,CAAC;EACtF;AACF,CAAC,CAAC,CAACd,SAAS,CAAC,CAAC;EACZ+C;AACF,CAAC,MAAM;EACLX,QAAQ,EAAE,UAAU;EACpBY,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPX,MAAM,EAAE,CAAC;EACT;EACAxB,KAAK,EAAEiC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACC,YAAY,GAAG,GAAGN,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAK,GAAGT,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE;EACvJC,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;IAC1DC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC;EACF,CAAC,KAAK5D,aAAa,CAACa,OAAO,EAAE,GAAG;IAC9B8B,SAAS,EAAE;EACb,CAAC;EACD,CAAC,KAAK3C,aAAa,CAACc,QAAQ,EAAE,GAAG;IAC/BF,KAAK,EAAEiC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACW,oBAAoB,GAAG,GAAGhB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,GAAGV,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC;EAC5J,CAAC;EACD,CAAC,KAAKvD,aAAa,CAACa,OAAO,OAAOb,aAAa,CAACmB,KAAK,EAAE,GAAG;IACxD2C,OAAO,EAAE;EACX,CAAC;EACD,CAAC,KAAK9D,aAAa,CAACc,QAAQ,OAAOd,aAAa,CAACmB,KAAK,EAAE,GAAG;IACzD2C,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACC,mBAAmB,GAAG,GAAGlB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG;EAC/G,CAAC;EACD,CAAC,MAAMpD,aAAa,CAACoB,KAAK,EAAE,GAAG;IAC7B2B,IAAI,EAAE,OAAO;IACblB,KAAK,EAAE;EACT;AACF,CAAC,CAAC,CAAC,EAAE/B,SAAS,CAAC,CAAC;EACd+C;AACF,CAAC,MAAM;EACL,SAAS,EAAE;IACTmB,eAAe,EAAEnB,KAAK,CAACG,IAAI,GAAG,QAAQH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACgB,MAAM,CAACC,aAAa,MAAMrB,KAAK,CAACG,IAAI,CAACC,OAAO,CAACgB,MAAM,CAACE,YAAY,GAAG,GAAG5E,KAAK,CAACsD,KAAK,CAACI,OAAO,CAACgB,MAAM,CAACG,MAAM,EAAEvB,KAAK,CAACI,OAAO,CAACgB,MAAM,CAACE,YAAY,CAAC;IACpM;IACA,sBAAsB,EAAE;MACtBH,eAAe,EAAE;IACnB;EACF,CAAC;EACDzB,QAAQ,EAAE,CAAC,GAAG8B,MAAM,CAACC,OAAO,CAACzB,KAAK,CAACI,OAAO,CAAC,CAACsB,MAAM,CAAC5E,8BAA8B,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;EAAA,CAC7F6E,GAAG,CAAC,CAAC,CAAC5D,KAAK,CAAC,MAAM;IACjBc,KAAK,EAAE;MACLd;IACF,CAAC;IACD4B,KAAK,EAAE;MACL,CAAC,KAAKxC,aAAa,CAACa,OAAO,EAAE,GAAG;QAC9BD,KAAK,EAAE,CAACiC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACrC,KAAK,CAAC,CAAC6D,IAAI;QAChD,SAAS,EAAE;UACTT,eAAe,EAAEnB,KAAK,CAACG,IAAI,GAAG,QAAQH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACrC,KAAK,CAAC,CAAC8D,WAAW,MAAM7B,KAAK,CAACG,IAAI,CAACC,OAAO,CAACgB,MAAM,CAACE,YAAY,GAAG,GAAG5E,KAAK,CAACsD,KAAK,CAACI,OAAO,CAACrC,KAAK,CAAC,CAAC6D,IAAI,EAAE5B,KAAK,CAACI,OAAO,CAACgB,MAAM,CAACE,YAAY,CAAC;UAChM,sBAAsB,EAAE;YACtBH,eAAe,EAAE;UACnB;QACF,CAAC;QACD,CAAC,KAAKhE,aAAa,CAACc,QAAQ,EAAE,GAAG;UAC/BF,KAAK,EAAEiC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAAC,GAAGtC,KAAK,eAAe,CAAC,GAAG,GAAGiC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG3D,OAAO,CAACoD,KAAK,CAACI,OAAO,CAACrC,KAAK,CAAC,CAAC6D,IAAI,EAAE,IAAI,CAAC,GAAGjF,MAAM,CAACqD,KAAK,CAACI,OAAO,CAACrC,KAAK,CAAC,CAAC6D,IAAI,EAAE,IAAI,CAAC;QACjM;MACF,CAAC;MACD,CAAC,KAAKzE,aAAa,CAACa,OAAO,OAAOb,aAAa,CAACmB,KAAK,EAAE,GAAG;QACxD6C,eAAe,EAAE,CAACnB,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACrC,KAAK,CAAC,CAAC6D;MACxD;IACF;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AACJ,MAAME,WAAW,GAAG9E,MAAM,CAAC,MAAM,EAAE;EACjC0B,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC1B,SAAS,CAAC,CAAC;EACZ+C;AACF,CAAC,MAAM;EACLf,MAAM,EAAE,MAAM;EACdD,KAAK,EAAE,MAAM;EACb+C,YAAY,EAAE,EAAE,GAAG,CAAC;EACpBxC,MAAM,EAAE,CAAC,CAAC;EACVoB,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,SAAS,EAAE,kBAAkB,CAAC,EAAE;IACpEC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC;EACFI,eAAe,EAAEnB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACI,MAAM,CAACwB,YAAY,GAAG,GAAGhC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAACyB,KAAK,GAAGjC,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAK,EAAE;EACpKQ,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACiB,WAAW,GAAG,GAAGlC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG;AACvG,CAAC,CAAC,CAAC,CAAC;AACJ,MAAM4B,WAAW,GAAGnF,MAAM,CAAC,MAAM,EAAE;EACjC0B,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC1B,SAAS,CAAC,CAAC;EACZ+C;AACF,CAAC,MAAM;EACLoC,SAAS,EAAE,CAACpC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEqC,OAAO,CAAC,CAAC,CAAC;EAC3ClB,eAAe,EAAE,cAAc;EAC/BnC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACV8C,YAAY,EAAE;AAChB,CAAC,CAAC,CAAC,CAAC;AACJ,MAAM1B,MAAM,GAAG,aAAahE,KAAK,CAACiG,UAAU,CAAC,SAASjC,MAAMA,CAACkC,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAM3D,KAAK,GAAG3B,eAAe,CAAC;IAC5B2B,KAAK,EAAE0D,OAAO;IACd7D,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJ+D,SAAS;IACT1E,KAAK,GAAG,SAAS;IACjBF,IAAI,GAAG,KAAK;IACZC,IAAI,GAAG,QAAQ;IACf4E,EAAE;IACFxE,KAAK,GAAG,CAAC,CAAC;IACVyE,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAG/D,KAAK;EACT,MAAMlB,UAAU,GAAG;IACjB,GAAGkB,KAAK;IACRd,KAAK;IACLF,IAAI;IACJC;EACF,CAAC;EACD,MAAMF,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMkF,sBAAsB,GAAG;IAC7B3E,KAAK;IACLyE;EACF,CAAC;EACD,MAAM,CAACG,QAAQ,EAAEC,aAAa,CAAC,GAAG1F,OAAO,CAAC,MAAM,EAAE;IAChDoF,SAAS,EAAElG,IAAI,CAACqB,OAAO,CAACO,IAAI,EAAEsE,SAAS,CAAC;IACxCO,WAAW,EAAEvE,UAAU;IACvBoE,sBAAsB;IACtBlF,UAAU;IACVsF,eAAe,EAAE;MACfP;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAACQ,SAAS,EAAEC,cAAc,CAAC,GAAG9F,OAAO,CAAC,OAAO,EAAE;IACnDoF,SAAS,EAAE7E,OAAO,CAACS,KAAK;IACxB2E,WAAW,EAAEb,WAAW;IACxBU,sBAAsB;IACtBlF;EACF,CAAC,CAAC;EACF,MAAMyF,IAAI,GAAG,aAAa7F,IAAI,CAAC2F,SAAS,EAAE;IACxC,GAAGC;EACL,CAAC,CAAC;EACF,MAAM,CAACE,SAAS,EAAEC,cAAc,CAAC,GAAGjG,OAAO,CAAC,OAAO,EAAE;IACnDoF,SAAS,EAAE7E,OAAO,CAACU,KAAK;IACxB0E,WAAW,EAAElB,WAAW;IACxBe,sBAAsB;IACtBlF;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,KAAK,CAACqF,QAAQ,EAAE;IAClC,GAAGC,aAAa;IAChBQ,QAAQ,EAAE,CAAC,aAAahG,IAAI,CAACwC,gBAAgB,EAAE;MAC7CyD,IAAI,EAAE,UAAU;MAChBJ,IAAI,EAAEA,IAAI;MACVK,WAAW,EAAEL,IAAI;MACjBZ,GAAG,EAAEA,GAAG;MACR7E,UAAU,EAAEA,UAAU;MACtB,GAAGiF,KAAK;MACRhF,OAAO,EAAE;QACP,GAAGA,OAAO;QACVO,IAAI,EAAEP,OAAO,CAACQ;MAChB,CAAC;MACDF,KAAK,EAAE;QACL,IAAIA,KAAK,CAACE,UAAU,IAAI;UACtBD,IAAI,EAAED,KAAK,CAACE;QACd,CAAC,CAAC;QACF,IAAIF,KAAK,CAACK,KAAK,IAAI;UACjBA,KAAK,EAAEL,KAAK,CAACK;QACf,CAAC;MACH,CAAC;MACDoE,SAAS,EAAE;QACT,IAAIA,SAAS,CAACvE,UAAU,IAAI;UAC1BD,IAAI,EAAE,OAAOwE,SAAS,CAACvE,UAAU,KAAK,UAAU,GAAGuE,SAAS,CAACvE,UAAU,CAACT,UAAU,CAAC,GAAGgF,SAAS,CAACvE;QAClG,CAAC,CAAC;QACF,IAAIuE,SAAS,CAACpE,KAAK,IAAI;UACrBA,KAAK,EAAE,OAAOoE,SAAS,CAACpE,KAAK,KAAK,UAAU,GAAGoE,SAAS,CAACpE,KAAK,CAACZ,UAAU,CAAC,GAAGgF,SAAS,CAACpE;QACzF,CAAC;MACH;IACF,CAAC,CAAC,EAAE,aAAahB,IAAI,CAAC8F,SAAS,EAAE;MAC/B,GAAGC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvD,MAAM,CAACwD,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACE7F,OAAO,EAAE1B,SAAS,CAACwH,IAAI;EACvB;AACF;AACA;EACEL,WAAW,EAAEnH,SAAS,CAACyH,IAAI;EAC3B;AACF;AACA;EACEnG,OAAO,EAAEtB,SAAS,CAAC0H,MAAM;EACzB;AACF;AACA;EACEvB,SAAS,EAAEnG,SAAS,CAAC2H,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACElG,KAAK,EAAEzB,SAAS,CAAC,sCAAsC4H,SAAS,CAAC,CAAC5H,SAAS,CAAC6H,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE7H,SAAS,CAAC2H,MAAM,CAAC,CAAC;EACjL;AACF;AACA;EACEG,cAAc,EAAE9H,SAAS,CAACwH,IAAI;EAC9B;AACF;AACA;EACE7F,QAAQ,EAAE3B,SAAS,CAACwH,IAAI;EACxB;AACF;AACA;AACA;EACEO,aAAa,EAAE/H,SAAS,CAACwH,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;EACEjG,IAAI,EAAEvB,SAAS,CAAC6H,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC9C;AACF;AACA;EACEf,IAAI,EAAE9G,SAAS,CAACyH,IAAI;EACpB;AACF;AACA;EACEO,EAAE,EAAEhI,SAAS,CAAC2H,MAAM;EACpB;AACF;AACA;AACA;EACEM,UAAU,EAAEjI,SAAS,CAAC0H,MAAM;EAC5B;AACF;AACA;AACA;EACEQ,QAAQ,EAAEhI,OAAO;EACjB;AACF;AACA;AACA;AACA;AACA;AACA;EACEiI,QAAQ,EAAEnI,SAAS,CAACoI,IAAI;EACxB;AACF;AACA;AACA;EACEC,QAAQ,EAAErI,SAAS,CAACwH,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEhG,IAAI,EAAExB,SAAS,CAAC,sCAAsC4H,SAAS,CAAC,CAAC5H,SAAS,CAAC6H,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE7H,SAAS,CAAC2H,MAAM,CAAC,CAAC;EACzH;AACF;AACA;AACA;EACEtB,SAAS,EAAErG,SAAS,CAACsI,KAAK,CAAC;IACzBrG,KAAK,EAAEjC,SAAS,CAAC4H,SAAS,CAAC,CAAC5H,SAAS,CAACoI,IAAI,EAAEpI,SAAS,CAAC0H,MAAM,CAAC,CAAC;IAC9D7F,IAAI,EAAE7B,SAAS,CAAC4H,SAAS,CAAC,CAAC5H,SAAS,CAACoI,IAAI,EAAEpI,SAAS,CAAC0H,MAAM,CAAC,CAAC;IAC7D5F,UAAU,EAAE9B,SAAS,CAAC4H,SAAS,CAAC,CAAC5H,SAAS,CAACoI,IAAI,EAAEpI,SAAS,CAAC0H,MAAM,CAAC,CAAC;IACnE3F,KAAK,EAAE/B,SAAS,CAAC4H,SAAS,CAAC,CAAC5H,SAAS,CAACoI,IAAI,EAAEpI,SAAS,CAAC0H,MAAM,CAAC,CAAC;IAC9D1F,KAAK,EAAEhC,SAAS,CAAC4H,SAAS,CAAC,CAAC5H,SAAS,CAACoI,IAAI,EAAEpI,SAAS,CAAC0H,MAAM,CAAC;EAC/D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE9F,KAAK,EAAE5B,SAAS,CAACsI,KAAK,CAAC;IACrBrG,KAAK,EAAEjC,SAAS,CAAC0G,WAAW;IAC5B7E,IAAI,EAAE7B,SAAS,CAAC0G,WAAW;IAC3B5E,UAAU,EAAE9B,SAAS,CAAC0G,WAAW;IACjC3E,KAAK,EAAE/B,SAAS,CAAC0G,WAAW;IAC5B1E,KAAK,EAAEhC,SAAS,CAAC0G;EACnB,CAAC,CAAC;EACF;AACF;AACA;EACEN,EAAE,EAAEpG,SAAS,CAAC4H,SAAS,CAAC,CAAC5H,SAAS,CAACuI,OAAO,CAACvI,SAAS,CAAC4H,SAAS,CAAC,CAAC5H,SAAS,CAACoI,IAAI,EAAEpI,SAAS,CAAC0H,MAAM,EAAE1H,SAAS,CAACwH,IAAI,CAAC,CAAC,CAAC,EAAExH,SAAS,CAACoI,IAAI,EAAEpI,SAAS,CAAC0H,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEc,KAAK,EAAExI,SAAS,CAACyI;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe1E,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}