{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport setRef from '@mui/utils/setRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport useId from '@mui/utils/useId';\nimport usePreviousProps from '@mui/utils/usePreviousProps';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\nfunction stripDiacritics(string) {\n  return string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.startsWith(input) : candidate.includes(input);\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => listboxRef.current !== null && listboxRef.current.parentElement?.contains(document.activeElement);\nconst MULTIPLE_DEFAULT_VALUE = [];\nfunction getInputValue(value, multiple, getOptionLabel, renderValue) {\n  if (multiple || value == null || renderValue) {\n    return '';\n  }\n  const optionLabel = getOptionLabel(value);\n  return typeof optionLabel === 'string' ? optionLabel : '';\n}\nfunction useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => option.label ?? option,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    renderValue,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedItem, setFocusedItem] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n\n  // Calculate the initial inputValue on mount only.\n  // useRef ensures it doesn't update dynamically with defaultValue or value props.\n  const initialInputValue = React.useRef(getInputValue(defaultValue ?? valueProp, multiple, getOptionLabel)).current;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: initialInputValue,\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue, reason) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    const newInputValue = getInputValue(newValue, multiple, getOptionLabel, renderValue);\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, reason);\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value, renderValue]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value, 'reset');\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  const focusItem = useEventCallback(itemToFocus => {\n    if (itemToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      // Using `data-tag-index` for deprecated `renderTags`. Remove when `renderTags` is gone.\n      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';\n      anchorEl.querySelector(`[${indexType}=\"${itemToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedItem is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedItem > value.length - 1) {\n      setFocusedItem(-1);\n      focusItem(-1);\n    }\n  }, [value, multiple, focusedItem, focusItem]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange && ['mouse', 'keyboard', 'touch'].includes(reason)) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return filteredOptions.findIndex(option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && value.findIndex(val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = filteredOptions.findIndex(optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = newValue.findIndex(valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue, reason);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validItemIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n\n      // Using `data-tag-index` for deprecated `renderTags`. Remove when `renderTags` is removed.\n      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';\n      const option = anchorEl.querySelector(`[${indexType}=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusItem = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextItem = focusedItem;\n    if (focusedItem === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextItem = value.length - 1;\n      }\n    } else {\n      nextItem += direction === 'next' ? 1 : -1;\n      if (nextItem < 0) {\n        nextItem = 0;\n      }\n      if (nextItem === value.length) {\n        nextItem = -1;\n      }\n    }\n    nextItem = validItemIndex(nextItem, direction);\n    setFocusedItem(nextItem);\n    focusItem(nextItem);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedItem !== -1 && !['ArrowLeft', 'ArrowRight'].includes(event.key)) {\n      setFocusedItem(-1);\n      focusItem(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          if (!multiple && renderValue) {\n            focusItem(0);\n          } else {\n            handleFocusItem(event, 'previous');\n          }\n          break;\n        case 'ArrowRight':\n          if (!multiple && renderValue) {\n            focusItem(-1);\n          } else {\n            handleFocusItem(event, 'next');\n          }\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0 || renderValue)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedItem === -1 ? value.length - 1 : focusedItem;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          if (!multiple && renderValue && !readOnly) {\n            setValueState(null);\n            focusItem(-1);\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedItem !== -1) {\n            const index = focusedItem;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          if (!multiple && renderValue && !readOnly) {\n            setValueState(null);\n            focusItem(-1);\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value, 'blur');\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleItemDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handleSingleItemDelete = event => {\n    handleValue(event, null, 'removeOption', {\n      option: value\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => ({\n      ...other,\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getItemProps: ({\n      index = 0\n    } = {}) => ({\n      ...(multiple && {\n        key: index\n      }),\n      ...(renderValue ? {\n        'data-item-index': index\n      } : {\n        'data-tag-index': index\n      }),\n      tabIndex: -1,\n      ...(!readOnly && {\n        onDelete: multiple ? handleItemDelete(index) : handleSingleItemDelete\n      })\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    // deprecated\n    getTagProps: ({\n      index\n    }) => ({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1,\n      ...(!readOnly && {\n        onDelete: handleItemDelete(index)\n      })\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: getOptionKey?.(option) ?? getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedItem !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedItem,\n    // deprecated\n    focusedTag: focusedItem,\n    groupedOptions\n  };\n}\nexport default useAutocomplete;", "map": {"version": 3, "names": ["React", "setRef", "useEventCallback", "useControlled", "useId", "usePreviousProps", "stripDiacritics", "string", "normalize", "replace", "createFilterOptions", "config", "ignoreAccents", "ignoreCase", "limit", "matchFrom", "stringify", "trim", "options", "inputValue", "getOptionLabel", "input", "toLowerCase", "filteredOptions", "filter", "option", "candidate", "startsWith", "includes", "slice", "defaultFilterOptions", "pageSize", "defaultIsActiveElementInListbox", "listboxRef", "current", "parentElement", "contains", "document", "activeElement", "MULTIPLE_DEFAULT_VALUE", "getInputValue", "value", "multiple", "renderValue", "optionLabel", "useAutocomplete", "props", "unstable_isActiveElementInListbox", "unstable_classNamePrefix", "autoComplete", "autoHighlight", "autoSelect", "blurOnSelect", "clearOnBlur", "freeSolo", "clearOnEscape", "componentName", "defaultValue", "disableClearable", "disableCloseOnSelect", "disabled", "disabledProp", "disabledItemsFocusable", "disableListWrap", "filterOptions", "filterSelectedOptions", "getOptionDisabled", "getOption<PERSON>ey", "getOptionLabelProp", "label", "groupBy", "handleHomeEndKeys", "id", "idProp", "includeInputInList", "inputValueProp", "isOptionEqualToValue", "onChange", "onClose", "onHighlightChange", "onInputChange", "onOpen", "open", "openProp", "openOnFocus", "readOnly", "selectOnFocus", "valueProp", "process", "env", "NODE_ENV", "erroneousReturn", "undefined", "console", "error", "JSON", "String", "ignoreFocus", "useRef", "firstFocus", "inputRef", "anchorEl", "setAnchorEl", "useState", "focusedItem", "setFocusedItem", "defaultHighlighted", "highlightedIndexRef", "initialInputValue", "setValueState", "controlled", "default", "name", "setInputValueState", "state", "focused", "setFocused", "resetInputValue", "useCallback", "event", "newValue", "reason", "isOptionSelected", "length", "newInputValue", "setOpenState", "inputPristine", "setInputPristine", "inputValueIsSelectedValue", "popupOpen", "some", "value2", "previousProps", "useEffect", "valueChange", "listboxAvailable", "focusItem", "itemToFocus", "focus", "indexType", "querySelector", "validOptionIndex", "index", "direction", "nextFocus", "nextFocusDisabled", "getAttribute", "hasAttribute", "setHighlightedIndex", "removeAttribute", "setAttribute", "prev", "classList", "remove", "listboxNode", "scrollTop", "add", "scrollHeight", "clientHeight", "element", "scrollBottom", "elementBottom", "offsetTop", "offsetHeight", "changeHighlightedIndex", "diff", "getNextIndex", "maxIndex", "newIndex", "Math", "abs", "nextIndex", "indexOf", "setSelectionRange", "getPreviousHighlightedOptionIndex", "isSameValue", "value1", "label1", "label2", "every", "val", "i", "previousHighlightedOption", "findIndex", "syncHighlightedIndex", "previousHighlightedOptionIndex", "valueItem", "currentOption", "itemIndex", "optionItem", "handleListboxRef", "node", "nodeName", "warn", "join", "handleOpen", "handleClose", "handleValue", "details", "is<PERSON><PERSON>ch", "selectNewValue", "reasonProp", "origin", "Array", "isArray", "matches", "push", "splice", "ctrl<PERSON>ey", "metaKey", "blur", "validItemIndex", "handleFocusItem", "nextItem", "handleClear", "handleKeyDown", "other", "onKeyDown", "defaultMuiPrevented", "key", "which", "preventDefault", "stopPropagation", "handleFocus", "handleBlur", "handleInputChange", "target", "handleOptionMouseMove", "Number", "currentTarget", "handleOptionTouchStart", "handleOptionClick", "handleItemDelete", "handleSingleItemDelete", "handlePopupIndicator", "handleMouseDown", "handleClick", "selectionEnd", "selectionStart", "select", "handleInputMouseDown", "dirty", "groupedOptions", "indexBy", "Map", "reduce", "acc", "group", "get", "set", "getRootProps", "onMouseDown", "onClick", "getInputLabelProps", "htmlFor", "getInputProps", "onBlur", "onFocus", "ref", "autoCapitalize", "spell<PERSON>heck", "role", "getClearProps", "tabIndex", "type", "getItemProps", "onDelete", "getPopupIndicatorProps", "getTagProps", "getListboxProps", "getOptionProps", "selected", "onMouseMove", "onTouchStart", "expanded", "focusedTag"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/useAutocomplete/useAutocomplete.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport setRef from '@mui/utils/setRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport useId from '@mui/utils/useId';\nimport usePreviousProps from '@mui/utils/usePreviousProps';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\nfunction stripDiacritics(string) {\n  return string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.startsWith(input) : candidate.includes(input);\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => listboxRef.current !== null && listboxRef.current.parentElement?.contains(document.activeElement);\nconst MULTIPLE_DEFAULT_VALUE = [];\nfunction getInputValue(value, multiple, getOptionLabel, renderValue) {\n  if (multiple || value == null || renderValue) {\n    return '';\n  }\n  const optionLabel = getOptionLabel(value);\n  return typeof optionLabel === 'string' ? optionLabel : '';\n}\nfunction useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => option.label ?? option,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    renderValue,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedItem, setFocusedItem] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n\n  // Calculate the initial inputValue on mount only.\n  // useRef ensures it doesn't update dynamically with defaultValue or value props.\n  const initialInputValue = React.useRef(getInputValue(defaultValue ?? valueProp, multiple, getOptionLabel)).current;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: initialInputValue,\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue, reason) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    const newInputValue = getInputValue(newValue, multiple, getOptionLabel, renderValue);\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, reason);\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value, renderValue]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value, 'reset');\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  const focusItem = useEventCallback(itemToFocus => {\n    if (itemToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      // Using `data-tag-index` for deprecated `renderTags`. Remove when `renderTags` is gone.\n      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';\n      anchorEl.querySelector(`[${indexType}=\"${itemToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedItem is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedItem > value.length - 1) {\n      setFocusedItem(-1);\n      focusItem(-1);\n    }\n  }, [value, multiple, focusedItem, focusItem]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange && ['mouse', 'keyboard', 'touch'].includes(reason)) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return filteredOptions.findIndex(option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && value.findIndex(val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = filteredOptions.findIndex(optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = newValue.findIndex(valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue, reason);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validItemIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n\n      // Using `data-tag-index` for deprecated `renderTags`. Remove when `renderTags` is removed.\n      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';\n      const option = anchorEl.querySelector(`[${indexType}=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusItem = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextItem = focusedItem;\n    if (focusedItem === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextItem = value.length - 1;\n      }\n    } else {\n      nextItem += direction === 'next' ? 1 : -1;\n      if (nextItem < 0) {\n        nextItem = 0;\n      }\n      if (nextItem === value.length) {\n        nextItem = -1;\n      }\n    }\n    nextItem = validItemIndex(nextItem, direction);\n    setFocusedItem(nextItem);\n    focusItem(nextItem);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedItem !== -1 && !['ArrowLeft', 'ArrowRight'].includes(event.key)) {\n      setFocusedItem(-1);\n      focusItem(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          if (!multiple && renderValue) {\n            focusItem(0);\n          } else {\n            handleFocusItem(event, 'previous');\n          }\n          break;\n        case 'ArrowRight':\n          if (!multiple && renderValue) {\n            focusItem(-1);\n          } else {\n            handleFocusItem(event, 'next');\n          }\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0 || renderValue)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedItem === -1 ? value.length - 1 : focusedItem;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          if (!multiple && renderValue && !readOnly) {\n            setValueState(null);\n            focusItem(-1);\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedItem !== -1) {\n            const index = focusedItem;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          if (!multiple && renderValue && !readOnly) {\n            setValueState(null);\n            focusItem(-1);\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value, 'blur');\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleItemDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handleSingleItemDelete = event => {\n    handleValue(event, null, 'removeOption', {\n      option: value\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => ({\n      ...other,\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getItemProps: ({\n      index = 0\n    } = {}) => ({\n      ...(multiple && {\n        key: index\n      }),\n      ...(renderValue ? {\n        'data-item-index': index\n      } : {\n        'data-tag-index': index\n      }),\n      tabIndex: -1,\n      ...(!readOnly && {\n        onDelete: multiple ? handleItemDelete(index) : handleSingleItemDelete\n      })\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    // deprecated\n    getTagProps: ({\n      index\n    }) => ({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1,\n      ...(!readOnly && {\n        onDelete: handleItemDelete(index)\n      })\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: getOptionKey?.(option) ?? getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedItem !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedItem,\n    // deprecated\n    focusedTag: focusedItem,\n    groupedOptions\n  };\n}\nexport default useAutocomplete;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,gBAAgB,MAAM,6BAA6B;;AAE1D;AACA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAOA,MAAM,CAACC,SAAS,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;AAChE;AACA,OAAO,SAASC,mBAAmBA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAM;IACJC,aAAa,GAAG,IAAI;IACpBC,UAAU,GAAG,IAAI;IACjBC,KAAK;IACLC,SAAS,GAAG,KAAK;IACjBC,SAAS;IACTC,IAAI,GAAG;EACT,CAAC,GAAGN,MAAM;EACV,OAAO,CAACO,OAAO,EAAE;IACfC,UAAU;IACVC;EACF,CAAC,KAAK;IACJ,IAAIC,KAAK,GAAGJ,IAAI,GAAGE,UAAU,CAACF,IAAI,CAAC,CAAC,GAAGE,UAAU;IACjD,IAAIN,UAAU,EAAE;MACdQ,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;IAC7B;IACA,IAAIV,aAAa,EAAE;MACjBS,KAAK,GAAGf,eAAe,CAACe,KAAK,CAAC;IAChC;IACA,MAAME,eAAe,GAAG,CAACF,KAAK,GAAGH,OAAO,GAAGA,OAAO,CAACM,MAAM,CAACC,MAAM,IAAI;MAClE,IAAIC,SAAS,GAAG,CAACV,SAAS,IAAII,cAAc,EAAEK,MAAM,CAAC;MACrD,IAAIZ,UAAU,EAAE;QACda,SAAS,GAAGA,SAAS,CAACJ,WAAW,CAAC,CAAC;MACrC;MACA,IAAIV,aAAa,EAAE;QACjBc,SAAS,GAAGpB,eAAe,CAACoB,SAAS,CAAC;MACxC;MACA,OAAOX,SAAS,KAAK,OAAO,GAAGW,SAAS,CAACC,UAAU,CAACN,KAAK,CAAC,GAAGK,SAAS,CAACE,QAAQ,CAACP,KAAK,CAAC;IACxF,CAAC,CAAC;IACF,OAAO,OAAOP,KAAK,KAAK,QAAQ,GAAGS,eAAe,CAACM,KAAK,CAAC,CAAC,EAAEf,KAAK,CAAC,GAAGS,eAAe;EACtF,CAAC;AACH;AACA,MAAMO,oBAAoB,GAAGpB,mBAAmB,CAAC,CAAC;;AAElD;AACA,MAAMqB,QAAQ,GAAG,CAAC;AAClB,MAAMC,+BAA+B,GAAGC,UAAU,IAAIA,UAAU,CAACC,OAAO,KAAK,IAAI,IAAID,UAAU,CAACC,OAAO,CAACC,aAAa,EAAEC,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC;AACvJ,MAAMC,sBAAsB,GAAG,EAAE;AACjC,SAASC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAEtB,cAAc,EAAEuB,WAAW,EAAE;EACnE,IAAID,QAAQ,IAAID,KAAK,IAAI,IAAI,IAAIE,WAAW,EAAE;IAC5C,OAAO,EAAE;EACX;EACA,MAAMC,WAAW,GAAGxB,cAAc,CAACqB,KAAK,CAAC;EACzC,OAAO,OAAOG,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG,EAAE;AAC3D;AACA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,MAAM;IACJ;IACAC,iCAAiC,GAAGf,+BAA+B;IACnE;IACAgB,wBAAwB,GAAG,KAAK;IAChCC,YAAY,GAAG,KAAK;IACpBC,aAAa,GAAG,KAAK;IACrBC,UAAU,GAAG,KAAK;IAClBC,YAAY,GAAG,KAAK;IACpBC,WAAW,GAAG,CAACP,KAAK,CAACQ,QAAQ;IAC7BC,aAAa,GAAG,KAAK;IACrBC,aAAa,GAAG,iBAAiB;IACjCC,YAAY,GAAGX,KAAK,CAACJ,QAAQ,GAAGH,sBAAsB,GAAG,IAAI;IAC7DmB,gBAAgB,GAAG,KAAK;IACxBC,oBAAoB,GAAG,KAAK;IAC5BC,QAAQ,EAAEC,YAAY;IACtBC,sBAAsB,GAAG,KAAK;IAC9BC,eAAe,GAAG,KAAK;IACvBC,aAAa,GAAGlC,oBAAoB;IACpCmC,qBAAqB,GAAG,KAAK;IAC7BX,QAAQ,GAAG,KAAK;IAChBY,iBAAiB;IACjBC,YAAY;IACZ/C,cAAc,EAAEgD,kBAAkB,GAAG3C,MAAM,IAAIA,MAAM,CAAC4C,KAAK,IAAI5C,MAAM;IACrE6C,OAAO;IACPC,iBAAiB,GAAG,CAACzB,KAAK,CAACQ,QAAQ;IACnCkB,EAAE,EAAEC,MAAM;IACVC,kBAAkB,GAAG,KAAK;IAC1BvD,UAAU,EAAEwD,cAAc;IAC1BC,oBAAoB,GAAGA,CAACnD,MAAM,EAAEgB,KAAK,KAAKhB,MAAM,KAAKgB,KAAK;IAC1DC,QAAQ,GAAG,KAAK;IAChBmC,QAAQ;IACRC,OAAO;IACPC,iBAAiB;IACjBC,aAAa;IACbC,MAAM;IACNC,IAAI,EAAEC,QAAQ;IACdC,WAAW,GAAG,KAAK;IACnBlE,OAAO;IACPmE,QAAQ,GAAG,KAAK;IAChB1C,WAAW;IACX2C,aAAa,GAAG,CAACxC,KAAK,CAACQ,QAAQ;IAC/Bb,KAAK,EAAE8C;EACT,CAAC,GAAGzC,KAAK;EACT,MAAM0B,EAAE,GAAGpE,KAAK,CAACqE,MAAM,CAAC;EACxB,IAAIrD,cAAc,GAAGgD,kBAAkB;EACvChD,cAAc,GAAGK,MAAM,IAAI;IACzB,MAAMmB,WAAW,GAAGwB,kBAAkB,CAAC3C,MAAM,CAAC;IAC9C,IAAI,OAAOmB,WAAW,KAAK,QAAQ,EAAE;MACnC,IAAI4C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMC,eAAe,GAAG/C,WAAW,KAAKgD,SAAS,GAAG,WAAW,GAAG,GAAG,OAAOhD,WAAW,KAAKA,WAAW,GAAG;QAC1GiD,OAAO,CAACC,KAAK,CAAC,yCAAyCtC,aAAa,aAAamC,eAAe,4BAA4BI,IAAI,CAAC/E,SAAS,CAACS,MAAM,CAAC,GAAG,CAAC;MACxJ;MACA,OAAOuE,MAAM,CAACpD,WAAW,CAAC;IAC5B;IACA,OAAOA,WAAW;EACpB,CAAC;EACD,MAAMqD,WAAW,GAAGjG,KAAK,CAACkG,MAAM,CAAC,KAAK,CAAC;EACvC,MAAMC,UAAU,GAAGnG,KAAK,CAACkG,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAGpG,KAAK,CAACkG,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMjE,UAAU,GAAGjC,KAAK,CAACkG,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,KAAK,CAACuG,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzG,KAAK,CAACuG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAMG,kBAAkB,GAAGxD,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,MAAMyD,mBAAmB,GAAG3G,KAAK,CAACkG,MAAM,CAACQ,kBAAkB,CAAC;;EAE5D;EACA;EACA,MAAME,iBAAiB,GAAG5G,KAAK,CAACkG,MAAM,CAAC1D,aAAa,CAACiB,YAAY,IAAI8B,SAAS,EAAE7C,QAAQ,EAAEtB,cAAc,CAAC,CAAC,CAACc,OAAO;EAClH,MAAM,CAACO,KAAK,EAAEoE,aAAa,CAAC,GAAG1G,aAAa,CAAC;IAC3C2G,UAAU,EAAEvB,SAAS;IACrBwB,OAAO,EAAEtD,YAAY;IACrBuD,IAAI,EAAExD;EACR,CAAC,CAAC;EACF,MAAM,CAACrC,UAAU,EAAE8F,kBAAkB,CAAC,GAAG9G,aAAa,CAAC;IACrD2G,UAAU,EAAEnC,cAAc;IAC1BoC,OAAO,EAAEH,iBAAiB;IAC1BI,IAAI,EAAExD,aAAa;IACnB0D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpH,KAAK,CAACuG,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMc,eAAe,GAAGrH,KAAK,CAACsH,WAAW,CAAC,CAACC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,KAAK;IACrE;IACA;IACA,MAAMC,gBAAgB,GAAGhF,QAAQ,GAAGD,KAAK,CAACkF,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAGH,QAAQ,KAAK,IAAI;IACtF,IAAI,CAACE,gBAAgB,IAAI,CAACrE,WAAW,EAAE;MACrC;IACF;IACA,MAAMuE,aAAa,GAAGpF,aAAa,CAACgF,QAAQ,EAAE9E,QAAQ,EAAEtB,cAAc,EAAEuB,WAAW,CAAC;IACpF,IAAIxB,UAAU,KAAKyG,aAAa,EAAE;MAChC;IACF;IACAX,kBAAkB,CAACW,aAAa,CAAC;IACjC,IAAI5C,aAAa,EAAE;MACjBA,aAAa,CAACuC,KAAK,EAAEK,aAAa,EAAEH,MAAM,CAAC;IAC7C;EACF,CAAC,EAAE,CAACrG,cAAc,EAAED,UAAU,EAAEuB,QAAQ,EAAEsC,aAAa,EAAEiC,kBAAkB,EAAE5D,WAAW,EAAEZ,KAAK,EAAEE,WAAW,CAAC,CAAC;EAC9G,MAAM,CAACuC,IAAI,EAAE2C,YAAY,CAAC,GAAG1H,aAAa,CAAC;IACzC2G,UAAU,EAAE3B,QAAQ;IACpB4B,OAAO,EAAE,KAAK;IACdC,IAAI,EAAExD,aAAa;IACnB0D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAG/H,KAAK,CAACuG,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMyB,yBAAyB,GAAG,CAACtF,QAAQ,IAAID,KAAK,IAAI,IAAI,IAAItB,UAAU,KAAKC,cAAc,CAACqB,KAAK,CAAC;EACpG,MAAMwF,SAAS,GAAG/C,IAAI,IAAI,CAACG,QAAQ;EACnC,MAAM9D,eAAe,GAAG0G,SAAS,GAAGjE,aAAa,CAAC9C,OAAO,CAACM,MAAM,CAACC,MAAM,IAAI;IACzE,IAAIwC,qBAAqB,IAAI,CAACvB,QAAQ,GAAGD,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEyF,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,IAAI,IAAIvD,oBAAoB,CAACnD,MAAM,EAAE0G,MAAM,CAAC,CAAC,EAAE;MACjI,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;EACA;EACA;IACEhH,UAAU,EAAE6G,yBAAyB,IAAIF,aAAa,GAAG,EAAE,GAAG3G,UAAU;IACxEC;EACF,CAAC,CAAC,GAAG,EAAE;EACP,MAAMgH,aAAa,GAAG/H,gBAAgB,CAAC;IACrCkB,eAAe;IACfkB,KAAK;IACLtB;EACF,CAAC,CAAC;EACFnB,KAAK,CAACqI,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAG7F,KAAK,KAAK2F,aAAa,CAAC3F,KAAK;IACjD,IAAI0E,OAAO,IAAI,CAACmB,WAAW,EAAE;MAC3B;IACF;;IAEA;IACA,IAAIhF,QAAQ,IAAI,CAACgF,WAAW,EAAE;MAC5B;IACF;IACAjB,eAAe,CAAC,IAAI,EAAE5E,KAAK,EAAE,OAAO,CAAC;EACvC,CAAC,EAAE,CAACA,KAAK,EAAE4E,eAAe,EAAEF,OAAO,EAAEiB,aAAa,CAAC3F,KAAK,EAAEa,QAAQ,CAAC,CAAC;EACpE,MAAMiF,gBAAgB,GAAGrD,IAAI,IAAI3D,eAAe,CAACoG,MAAM,GAAG,CAAC,IAAI,CAACtC,QAAQ;EACxE,MAAMmD,SAAS,GAAGtI,gBAAgB,CAACuI,WAAW,IAAI;IAChD,IAAIA,WAAW,KAAK,CAAC,CAAC,EAAE;MACtBrC,QAAQ,CAAClE,OAAO,CAACwG,KAAK,CAAC,CAAC;IAC1B,CAAC,MAAM;MACL;MACA,MAAMC,SAAS,GAAGhG,WAAW,GAAG,iBAAiB,GAAG,gBAAgB;MACpE0D,QAAQ,CAACuC,aAAa,CAAC,IAAID,SAAS,KAAKF,WAAW,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC;IACnE;EACF,CAAC,CAAC;;EAEF;EACA1I,KAAK,CAACqI,SAAS,CAAC,MAAM;IACpB,IAAI3F,QAAQ,IAAI8D,WAAW,GAAG/D,KAAK,CAACkF,MAAM,GAAG,CAAC,EAAE;MAC9ClB,cAAc,CAAC,CAAC,CAAC,CAAC;MAClB+B,SAAS,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAAC/F,KAAK,EAAEC,QAAQ,EAAE8D,WAAW,EAAEgC,SAAS,CAAC,CAAC;EAC7C,SAASK,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAE;IAC1C,IAAI,CAAC9G,UAAU,CAACC,OAAO,IAAI4G,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIvH,eAAe,CAACoG,MAAM,EAAE;MACvE,OAAO,CAAC,CAAC;IACX;IACA,IAAIqB,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX,MAAMrH,MAAM,GAAGQ,UAAU,CAACC,OAAO,CAAC0G,aAAa,CAAC,uBAAuBI,SAAS,IAAI,CAAC;;MAErF;MACA,MAAMC,iBAAiB,GAAGnF,sBAAsB,GAAG,KAAK,GAAG,CAACrC,MAAM,IAAIA,MAAM,CAACmC,QAAQ,IAAInC,MAAM,CAACyH,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;MACxI,IAAIzH,MAAM,IAAIA,MAAM,CAAC0H,YAAY,CAAC,UAAU,CAAC,IAAI,CAACF,iBAAiB,EAAE;QACnE;QACA,OAAOD,SAAS;MAClB;;MAEA;MACA;MACA,IAAID,SAAS,KAAK,MAAM,EAAE;QACxBC,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,IAAIzH,eAAe,CAACoG,MAAM;MACtD,CAAC,MAAM;QACLqB,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,GAAGzH,eAAe,CAACoG,MAAM,IAAIpG,eAAe,CAACoG,MAAM;MAC/E;;MAEA;MACA;MACA,IAAIqB,SAAS,KAAKF,KAAK,EAAE;QACvB,OAAO,CAAC,CAAC;MACX;IACF;EACF;EACA,MAAMM,mBAAmB,GAAGlJ,gBAAgB,CAAC,CAAC;IAC5CqH,KAAK;IACLuB,KAAK;IACLrB;EACF,CAAC,KAAK;IACJd,mBAAmB,CAACzE,OAAO,GAAG4G,KAAK;;IAEnC;IACA,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB1C,QAAQ,CAAClE,OAAO,CAACmH,eAAe,CAAC,uBAAuB,CAAC;IAC3D,CAAC,MAAM;MACLjD,QAAQ,CAAClE,OAAO,CAACoH,YAAY,CAAC,uBAAuB,EAAE,GAAG9E,EAAE,WAAWsE,KAAK,EAAE,CAAC;IACjF;IACA,IAAI/D,iBAAiB,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAACnD,QAAQ,CAAC6F,MAAM,CAAC,EAAE;MACxE1C,iBAAiB,CAACwC,KAAK,EAAEuB,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGvH,eAAe,CAACuH,KAAK,CAAC,EAAErB,MAAM,CAAC;IAChF;IACA,IAAI,CAACxF,UAAU,CAACC,OAAO,EAAE;MACvB;IACF;IACA,MAAMqH,IAAI,GAAGtH,UAAU,CAACC,OAAO,CAAC0G,aAAa,CAAC,mBAAmB5F,wBAAwB,UAAU,CAAC;IACpG,IAAIuG,IAAI,EAAE;MACRA,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,GAAGzG,wBAAwB,UAAU,CAAC;MAC5DuG,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,GAAGzG,wBAAwB,eAAe,CAAC;IACnE;IACA,IAAI0G,WAAW,GAAGzH,UAAU,CAACC,OAAO;IACpC,IAAID,UAAU,CAACC,OAAO,CAACgH,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;MACzDQ,WAAW,GAAGzH,UAAU,CAACC,OAAO,CAACC,aAAa,CAACyG,aAAa,CAAC,kBAAkB,CAAC;IAClF;;IAEA;IACA,IAAI,CAACc,WAAW,EAAE;MAChB;IACF;IACA,IAAIZ,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBY,WAAW,CAACC,SAAS,GAAG,CAAC;MACzB;IACF;IACA,MAAMlI,MAAM,GAAGQ,UAAU,CAACC,OAAO,CAAC0G,aAAa,CAAC,uBAAuBE,KAAK,IAAI,CAAC;IACjF,IAAI,CAACrH,MAAM,EAAE;MACX;IACF;IACAA,MAAM,CAAC+H,SAAS,CAACI,GAAG,CAAC,GAAG5G,wBAAwB,UAAU,CAAC;IAC3D,IAAIyE,MAAM,KAAK,UAAU,EAAE;MACzBhG,MAAM,CAAC+H,SAAS,CAACI,GAAG,CAAC,GAAG5G,wBAAwB,eAAe,CAAC;IAClE;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAI0G,WAAW,CAACG,YAAY,GAAGH,WAAW,CAACI,YAAY,IAAIrC,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,OAAO,EAAE;MACnG,MAAMsC,OAAO,GAAGtI,MAAM;MACtB,MAAMuI,YAAY,GAAGN,WAAW,CAACI,YAAY,GAAGJ,WAAW,CAACC,SAAS;MACrE,MAAMM,aAAa,GAAGF,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY;MAC9D,IAAIF,aAAa,GAAGD,YAAY,EAAE;QAChCN,WAAW,CAACC,SAAS,GAAGM,aAAa,GAAGP,WAAW,CAACI,YAAY;MAClE,CAAC,MAAM,IAAIC,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAI7F,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC,GAAGoF,WAAW,CAACC,SAAS,EAAE;QACjGD,WAAW,CAACC,SAAS,GAAGI,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAI7F,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC;MACxF;IACF;EACF,CAAC,CAAC;EACF,MAAM8F,sBAAsB,GAAGlK,gBAAgB,CAAC,CAAC;IAC/CqH,KAAK;IACL8C,IAAI;IACJtB,SAAS,GAAG,MAAM;IAClBtB;EACF,CAAC,KAAK;IACJ,IAAI,CAACQ,SAAS,EAAE;MACd;IACF;IACA,MAAMqC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,QAAQ,GAAGhJ,eAAe,CAACoG,MAAM,GAAG,CAAC;MAC3C,IAAI0C,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO3D,kBAAkB;MAC3B;MACA,IAAI2D,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,CAAC;MACV;MACA,IAAIA,IAAI,KAAK,KAAK,EAAE;QAClB,OAAOE,QAAQ;MACjB;MACA,MAAMC,QAAQ,GAAG7D,mBAAmB,CAACzE,OAAO,GAAGmI,IAAI;MACnD,IAAIG,QAAQ,GAAG,CAAC,EAAE;QAChB,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAI9F,kBAAkB,EAAE;UACzC,OAAO,CAAC,CAAC;QACX;QACA,IAAIX,eAAe,IAAI4C,mBAAmB,CAACzE,OAAO,KAAK,CAAC,CAAC,IAAIuI,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UAC/E,OAAO,CAAC;QACV;QACA,OAAOE,QAAQ;MACjB;MACA,IAAIC,QAAQ,GAAGD,QAAQ,EAAE;QACvB,IAAIC,QAAQ,KAAKD,QAAQ,GAAG,CAAC,IAAI7F,kBAAkB,EAAE;UACnD,OAAO,CAAC,CAAC;QACX;QACA,IAAIX,eAAe,IAAI0G,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UACzC,OAAOE,QAAQ;QACjB;QACA,OAAO,CAAC;MACV;MACA,OAAOC,QAAQ;IACjB,CAAC;IACD,MAAMG,SAAS,GAAG9B,gBAAgB,CAACyB,YAAY,CAAC,CAAC,EAAEvB,SAAS,CAAC;IAC7DK,mBAAmB,CAAC;MAClBN,KAAK,EAAE6B,SAAS;MAChBlD,MAAM;MACNF;IACF,CAAC,CAAC;;IAEF;IACA,IAAItE,YAAY,IAAIoH,IAAI,KAAK,OAAO,EAAE;MACpC,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBvE,QAAQ,CAAClE,OAAO,CAACO,KAAK,GAAGtB,UAAU;MACrC,CAAC,MAAM;QACL,MAAMM,MAAM,GAAGL,cAAc,CAACG,eAAe,CAACoJ,SAAS,CAAC,CAAC;QACzDvE,QAAQ,CAAClE,OAAO,CAACO,KAAK,GAAGhB,MAAM;;QAE/B;QACA;QACA,MAAMqH,KAAK,GAAGrH,MAAM,CAACH,WAAW,CAAC,CAAC,CAACsJ,OAAO,CAACzJ,UAAU,CAACG,WAAW,CAAC,CAAC,CAAC;QACpE,IAAIwH,KAAK,KAAK,CAAC,IAAI3H,UAAU,CAACwG,MAAM,GAAG,CAAC,EAAE;UACxCvB,QAAQ,CAAClE,OAAO,CAAC2I,iBAAiB,CAAC1J,UAAU,CAACwG,MAAM,EAAElG,MAAM,CAACkG,MAAM,CAAC;QACtE;MACF;IACF;EACF,CAAC,CAAC;EACF,MAAMmD,iCAAiC,GAAGA,CAAA,KAAM;IAC9C,MAAMC,WAAW,GAAGA,CAACC,MAAM,EAAE7C,MAAM,KAAK;MACtC,MAAM8C,MAAM,GAAGD,MAAM,GAAG5J,cAAc,CAAC4J,MAAM,CAAC,GAAG,EAAE;MACnD,MAAME,MAAM,GAAG/C,MAAM,GAAG/G,cAAc,CAAC+G,MAAM,CAAC,GAAG,EAAE;MACnD,OAAO8C,MAAM,KAAKC,MAAM;IAC1B,CAAC;IACD,IAAIvE,mBAAmB,CAACzE,OAAO,KAAK,CAAC,CAAC,IAAIkG,aAAa,CAAC7G,eAAe,IAAI6G,aAAa,CAAC7G,eAAe,CAACoG,MAAM,KAAKpG,eAAe,CAACoG,MAAM,IAAIS,aAAa,CAACjH,UAAU,KAAKA,UAAU,KAAKuB,QAAQ,GAAGD,KAAK,CAACkF,MAAM,KAAKS,aAAa,CAAC3F,KAAK,CAACkF,MAAM,IAAIS,aAAa,CAAC3F,KAAK,CAAC0I,KAAK,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKjK,cAAc,CAACqB,KAAK,CAAC4I,CAAC,CAAC,CAAC,KAAKjK,cAAc,CAACgK,GAAG,CAAC,CAAC,GAAGL,WAAW,CAAC3C,aAAa,CAAC3F,KAAK,EAAEA,KAAK,CAAC,CAAC,EAAE;MACtX,MAAM6I,yBAAyB,GAAGlD,aAAa,CAAC7G,eAAe,CAACoF,mBAAmB,CAACzE,OAAO,CAAC;MAC5F,IAAIoJ,yBAAyB,EAAE;QAC7B,OAAO/J,eAAe,CAACgK,SAAS,CAAC9J,MAAM,IAAI;UACzC,OAAOL,cAAc,CAACK,MAAM,CAAC,KAAKL,cAAc,CAACkK,yBAAyB,CAAC;QAC7E,CAAC,CAAC;MACJ;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD,MAAME,oBAAoB,GAAGxL,KAAK,CAACsH,WAAW,CAAC,MAAM;IACnD,IAAI,CAACW,SAAS,EAAE;MACd;IACF;;IAEA;IACA;IACA,MAAMwD,8BAA8B,GAAGX,iCAAiC,CAAC,CAAC;IAC1E,IAAIW,8BAA8B,KAAK,CAAC,CAAC,EAAE;MACzC9E,mBAAmB,CAACzE,OAAO,GAAGuJ,8BAA8B;MAC5D;IACF;IACA,MAAMC,SAAS,GAAGhJ,QAAQ,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;;IAE7C;IACA,IAAIlB,eAAe,CAACoG,MAAM,KAAK,CAAC,IAAI+D,SAAS,IAAI,IAAI,EAAE;MACrDtB,sBAAsB,CAAC;QACrBC,IAAI,EAAE;MACR,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAACpI,UAAU,CAACC,OAAO,EAAE;MACvB;IACF;;IAEA;IACA,IAAIwJ,SAAS,IAAI,IAAI,EAAE;MACrB,MAAMC,aAAa,GAAGpK,eAAe,CAACoF,mBAAmB,CAACzE,OAAO,CAAC;;MAElE;MACA,IAAIQ,QAAQ,IAAIiJ,aAAa,IAAIlJ,KAAK,CAAC8I,SAAS,CAACH,GAAG,IAAIxG,oBAAoB,CAAC+G,aAAa,EAAEP,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACxG;MACF;MACA,MAAMQ,SAAS,GAAGrK,eAAe,CAACgK,SAAS,CAACM,UAAU,IAAIjH,oBAAoB,CAACiH,UAAU,EAAEH,SAAS,CAAC,CAAC;MACtG,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBxB,sBAAsB,CAAC;UACrBC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACLjB,mBAAmB,CAAC;UAClBN,KAAK,EAAE8C;QACT,CAAC,CAAC;MACJ;MACA;IACF;;IAEA;IACA,IAAIjF,mBAAmB,CAACzE,OAAO,IAAIX,eAAe,CAACoG,MAAM,GAAG,CAAC,EAAE;MAC7DyB,mBAAmB,CAAC;QAClBN,KAAK,EAAEvH,eAAe,CAACoG,MAAM,GAAG;MAClC,CAAC,CAAC;MACF;IACF;;IAEA;IACAyB,mBAAmB,CAAC;MAClBN,KAAK,EAAEnC,mBAAmB,CAACzE;IAC7B,CAAC,CAAC;IACF;IACA;EACF,CAAC,EAAE;EACH;EACAX,eAAe,CAACoG,MAAM;EACtB;EACA;EACAjF,QAAQ,GAAG,KAAK,GAAGD,KAAK,EAAEwB,qBAAqB,EAAEmG,sBAAsB,EAAEhB,mBAAmB,EAAEnB,SAAS,EAAE9G,UAAU,EAAEuB,QAAQ,CAAC,CAAC;EAC/H,MAAMoJ,gBAAgB,GAAG5L,gBAAgB,CAAC6L,IAAI,IAAI;IAChD9L,MAAM,CAACgC,UAAU,EAAE8J,IAAI,CAAC;IACxB,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IACAP,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,IAAIhG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA1F,KAAK,CAACqI,SAAS,CAAC,MAAM;MACpB,IAAI,CAACjC,QAAQ,CAAClE,OAAO,IAAIkE,QAAQ,CAAClE,OAAO,CAAC8J,QAAQ,KAAK,OAAO,EAAE;QAC9D,IAAI5F,QAAQ,CAAClE,OAAO,IAAIkE,QAAQ,CAAClE,OAAO,CAAC8J,QAAQ,KAAK,UAAU,EAAE;UAChEnG,OAAO,CAACoG,IAAI,CAAC,CAAC,sCAAsCzI,aAAa,4BAA4B,EAAE,4EAA4E,EAAE,4GAA4G,EAAE,mFAAmF,CAAC,CAAC0I,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7X,CAAC,MAAM;UACLrG,OAAO,CAACC,KAAK,CAAC,CAAC,6DAA6DM,QAAQ,CAAClE,OAAO,0CAA0C,EAAE,YAAYsB,aAAa,4BAA4B,EAAE,EAAE,EAAEA,aAAa,KAAK,iBAAiB,GAAG,kHAAkH,GAAG,8DAA8D,CAAC,CAAC0I,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3a;MACF;IACF,CAAC,EAAE,CAAC1I,aAAa,CAAC,CAAC;EACrB;EACAxD,KAAK,CAACqI,SAAS,CAAC,MAAM;IACpBmD,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAC1B,MAAMW,UAAU,GAAG5E,KAAK,IAAI;IAC1B,IAAIrC,IAAI,EAAE;MACR;IACF;IACA2C,YAAY,CAAC,IAAI,CAAC;IAClBE,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI9C,MAAM,EAAE;MACVA,MAAM,CAACsC,KAAK,CAAC;IACf;EACF,CAAC;EACD,MAAM6E,WAAW,GAAGA,CAAC7E,KAAK,EAAEE,MAAM,KAAK;IACrC,IAAI,CAACvC,IAAI,EAAE;MACT;IACF;IACA2C,YAAY,CAAC,KAAK,CAAC;IACnB,IAAI/C,OAAO,EAAE;MACXA,OAAO,CAACyC,KAAK,EAAEE,MAAM,CAAC;IACxB;EACF,CAAC;EACD,MAAM4E,WAAW,GAAGA,CAAC9E,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE6E,OAAO,KAAK;IACxD,IAAI5J,QAAQ,EAAE;MACZ,IAAID,KAAK,CAACkF,MAAM,KAAKH,QAAQ,CAACG,MAAM,IAAIlF,KAAK,CAAC0I,KAAK,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,KAAK5D,QAAQ,CAAC6D,CAAC,CAAC,CAAC,EAAE;QACpF;MACF;IACF,CAAC,MAAM,IAAI5I,KAAK,KAAK+E,QAAQ,EAAE;MAC7B;IACF;IACA,IAAI3C,QAAQ,EAAE;MACZA,QAAQ,CAAC0C,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE6E,OAAO,CAAC;IAC5C;IACAzF,aAAa,CAACW,QAAQ,CAAC;EACzB,CAAC;EACD,MAAM+E,OAAO,GAAGvM,KAAK,CAACkG,MAAM,CAAC,KAAK,CAAC;EACnC,MAAMsG,cAAc,GAAGA,CAACjF,KAAK,EAAE9F,MAAM,EAAEgL,UAAU,GAAG,cAAc,EAAEC,MAAM,GAAG,SAAS,KAAK;IACzF,IAAIjF,MAAM,GAAGgF,UAAU;IACvB,IAAIjF,QAAQ,GAAG/F,MAAM;IACrB,IAAIiB,QAAQ,EAAE;MACZ8E,QAAQ,GAAGmF,KAAK,CAACC,OAAO,CAACnK,KAAK,CAAC,GAAGA,KAAK,CAACZ,KAAK,CAAC,CAAC,GAAG,EAAE;MACpD,IAAI2D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMmH,OAAO,GAAGrF,QAAQ,CAAChG,MAAM,CAAC4J,GAAG,IAAIxG,oBAAoB,CAACnD,MAAM,EAAE2J,GAAG,CAAC,CAAC;QACzE,IAAIyB,OAAO,CAAClF,MAAM,GAAG,CAAC,EAAE;UACtB9B,OAAO,CAACC,KAAK,CAAC,CAAC,+CAA+CtC,aAAa,2CAA2C,EAAE,0EAA0EqJ,OAAO,CAAClF,MAAM,WAAW,CAAC,CAACuE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1O;MACF;MACA,MAAMN,SAAS,GAAGpE,QAAQ,CAAC+D,SAAS,CAACG,SAAS,IAAI9G,oBAAoB,CAACnD,MAAM,EAAEiK,SAAS,CAAC,CAAC;MAC1F,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBpE,QAAQ,CAACsF,IAAI,CAACrL,MAAM,CAAC;MACvB,CAAC,MAAM,IAAIiL,MAAM,KAAK,UAAU,EAAE;QAChClF,QAAQ,CAACuF,MAAM,CAACnB,SAAS,EAAE,CAAC,CAAC;QAC7BnE,MAAM,GAAG,cAAc;MACzB;IACF;IACAJ,eAAe,CAACE,KAAK,EAAEC,QAAQ,EAAEC,MAAM,CAAC;IACxC4E,WAAW,CAAC9E,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE;MACnChG;IACF,CAAC,CAAC;IACF,IAAI,CAACkC,oBAAoB,KAAK,CAAC4D,KAAK,IAAI,CAACA,KAAK,CAACyF,OAAO,IAAI,CAACzF,KAAK,CAAC0F,OAAO,CAAC,EAAE;MACzEb,WAAW,CAAC7E,KAAK,EAAEE,MAAM,CAAC;IAC5B;IACA,IAAIrE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,OAAO,IAAImJ,OAAO,CAACrK,OAAO,IAAIkB,YAAY,KAAK,OAAO,IAAI,CAACmJ,OAAO,CAACrK,OAAO,EAAE;MACxHkE,QAAQ,CAAClE,OAAO,CAACgL,IAAI,CAAC,CAAC;IACzB;EACF,CAAC;EACD,SAASC,cAAcA,CAACrE,KAAK,EAAEC,SAAS,EAAE;IACxC,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO,CAAC,CAAC;IACX;IACA,IAAIE,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX;MACA,IAAIC,SAAS,KAAK,MAAM,IAAIC,SAAS,KAAKvG,KAAK,CAACkF,MAAM,IAAIoB,SAAS,KAAK,UAAU,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QACtG,OAAO,CAAC,CAAC;MACX;;MAEA;MACA,MAAML,SAAS,GAAGhG,WAAW,GAAG,iBAAiB,GAAG,gBAAgB;MACpE,MAAMlB,MAAM,GAAG4E,QAAQ,CAACuC,aAAa,CAAC,IAAID,SAAS,KAAKK,SAAS,IAAI,CAAC;;MAEtE;MACA,IAAI,CAACvH,MAAM,IAAI,CAACA,MAAM,CAAC0H,YAAY,CAAC,UAAU,CAAC,IAAI1H,MAAM,CAACmC,QAAQ,IAAInC,MAAM,CAACyH,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;QACrHF,SAAS,IAAID,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAOC,SAAS;MAClB;IACF;EACF;EACA,MAAMoE,eAAe,GAAGA,CAAC7F,KAAK,EAAEwB,SAAS,KAAK;IAC5C,IAAI,CAACrG,QAAQ,EAAE;MACb;IACF;IACA,IAAIvB,UAAU,KAAK,EAAE,EAAE;MACrBiL,WAAW,CAAC7E,KAAK,EAAE,aAAa,CAAC;IACnC;IACA,IAAI8F,QAAQ,GAAG7G,WAAW;IAC1B,IAAIA,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,IAAIrF,UAAU,KAAK,EAAE,IAAI4H,SAAS,KAAK,UAAU,EAAE;QACjDsE,QAAQ,GAAG5K,KAAK,CAACkF,MAAM,GAAG,CAAC;MAC7B;IACF,CAAC,MAAM;MACL0F,QAAQ,IAAItE,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACzC,IAAIsE,QAAQ,GAAG,CAAC,EAAE;QAChBA,QAAQ,GAAG,CAAC;MACd;MACA,IAAIA,QAAQ,KAAK5K,KAAK,CAACkF,MAAM,EAAE;QAC7B0F,QAAQ,GAAG,CAAC,CAAC;MACf;IACF;IACAA,QAAQ,GAAGF,cAAc,CAACE,QAAQ,EAAEtE,SAAS,CAAC;IAC9CtC,cAAc,CAAC4G,QAAQ,CAAC;IACxB7E,SAAS,CAAC6E,QAAQ,CAAC;EACrB,CAAC;EACD,MAAMC,WAAW,GAAG/F,KAAK,IAAI;IAC3BtB,WAAW,CAAC/D,OAAO,GAAG,IAAI;IAC1B+E,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAIjC,aAAa,EAAE;MACjBA,aAAa,CAACuC,KAAK,EAAE,EAAE,EAAE,OAAO,CAAC;IACnC;IACA8E,WAAW,CAAC9E,KAAK,EAAE7E,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,CAAC;EACnD,CAAC;EACD,MAAM6K,aAAa,GAAGC,KAAK,IAAIjG,KAAK,IAAI;IACtC,IAAIiG,KAAK,CAACC,SAAS,EAAE;MACnBD,KAAK,CAACC,SAAS,CAAClG,KAAK,CAAC;IACxB;IACA,IAAIA,KAAK,CAACmG,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAIlH,WAAW,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC5E,QAAQ,CAAC2F,KAAK,CAACoG,GAAG,CAAC,EAAE;MAC1ElH,cAAc,CAAC,CAAC,CAAC,CAAC;MAClB+B,SAAS,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA;IACA,IAAIjB,KAAK,CAACqG,KAAK,KAAK,GAAG,EAAE;MACvB,QAAQrG,KAAK,CAACoG,GAAG;QACf,KAAK,MAAM;UACT,IAAI1F,SAAS,IAAI1D,iBAAiB,EAAE;YAClC;YACAgD,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtBzD,sBAAsB,CAAC;cACrBC,IAAI,EAAE,OAAO;cACbtB,SAAS,EAAE,MAAM;cACjBtB,MAAM,EAAE,UAAU;cAClBF;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,KAAK;UACR,IAAIU,SAAS,IAAI1D,iBAAiB,EAAE;YAClC;YACAgD,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtBzD,sBAAsB,CAAC;cACrBC,IAAI,EAAE,KAAK;cACXtB,SAAS,EAAE,UAAU;cACrBtB,MAAM,EAAE,UAAU;cAClBF;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,QAAQ;UACX;UACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;UACtBzD,sBAAsB,CAAC;YACrBC,IAAI,EAAE,CAACtI,QAAQ;YACfgH,SAAS,EAAE,UAAU;YACrBtB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF4E,UAAU,CAAC5E,KAAK,CAAC;UACjB;QACF,KAAK,UAAU;UACb;UACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;UACtBzD,sBAAsB,CAAC;YACrBC,IAAI,EAAEtI,QAAQ;YACdgH,SAAS,EAAE,MAAM;YACjBtB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF4E,UAAU,CAAC5E,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACd;UACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;UACtBzD,sBAAsB,CAAC;YACrBC,IAAI,EAAE,CAAC;YACPtB,SAAS,EAAE,MAAM;YACjBtB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF4E,UAAU,CAAC5E,KAAK,CAAC;UACjB;QACF,KAAK,SAAS;UACZ;UACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;UACtBzD,sBAAsB,CAAC;YACrBC,IAAI,EAAE,CAAC,CAAC;YACRtB,SAAS,EAAE,UAAU;YACrBtB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF4E,UAAU,CAAC5E,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACd,IAAI,CAAC7E,QAAQ,IAAIC,WAAW,EAAE;YAC5B6F,SAAS,CAAC,CAAC,CAAC;UACd,CAAC,MAAM;YACL4E,eAAe,CAAC7F,KAAK,EAAE,UAAU,CAAC;UACpC;UACA;QACF,KAAK,YAAY;UACf,IAAI,CAAC7E,QAAQ,IAAIC,WAAW,EAAE;YAC5B6F,SAAS,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,MAAM;YACL4E,eAAe,CAAC7F,KAAK,EAAE,MAAM,CAAC;UAChC;UACA;QACF,KAAK,OAAO;UACV,IAAIZ,mBAAmB,CAACzE,OAAO,KAAK,CAAC,CAAC,IAAI+F,SAAS,EAAE;YACnD,MAAMxG,MAAM,GAAGF,eAAe,CAACoF,mBAAmB,CAACzE,OAAO,CAAC;YAC3D,MAAM0B,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAACzC,MAAM,CAAC,GAAG,KAAK;;YAEtE;YACA8F,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtB,IAAIjK,QAAQ,EAAE;cACZ;YACF;YACA4I,cAAc,CAACjF,KAAK,EAAE9F,MAAM,EAAE,cAAc,CAAC;;YAE7C;YACA,IAAIwB,YAAY,EAAE;cAChBmD,QAAQ,CAAClE,OAAO,CAAC2I,iBAAiB,CAACzE,QAAQ,CAAClE,OAAO,CAACO,KAAK,CAACkF,MAAM,EAAEvB,QAAQ,CAAClE,OAAO,CAACO,KAAK,CAACkF,MAAM,CAAC;YAClG;UACF,CAAC,MAAM,IAAIrE,QAAQ,IAAInC,UAAU,KAAK,EAAE,IAAI6G,yBAAyB,KAAK,KAAK,EAAE;YAC/E,IAAItF,QAAQ,EAAE;cACZ;cACA6E,KAAK,CAACsG,cAAc,CAAC,CAAC;YACxB;YACArB,cAAc,CAACjF,KAAK,EAAEpG,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;UAC/D;UACA;QACF,KAAK,QAAQ;UACX,IAAI8G,SAAS,EAAE;YACb;YACAV,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtB;YACAtG,KAAK,CAACuG,eAAe,CAAC,CAAC;YACvB1B,WAAW,CAAC7E,KAAK,EAAE,QAAQ,CAAC;UAC9B,CAAC,MAAM,IAAIhE,aAAa,KAAKpC,UAAU,KAAK,EAAE,IAAIuB,QAAQ,IAAID,KAAK,CAACkF,MAAM,GAAG,CAAC,IAAIhF,WAAW,CAAC,EAAE;YAC9F;YACA4E,KAAK,CAACsG,cAAc,CAAC,CAAC;YACtB;YACAtG,KAAK,CAACuG,eAAe,CAAC,CAAC;YACvBR,WAAW,CAAC/F,KAAK,CAAC;UACpB;UACA;QACF,KAAK,WAAW;UACd;UACA,IAAI7E,QAAQ,IAAI,CAAC2C,QAAQ,IAAIlE,UAAU,KAAK,EAAE,IAAIsB,KAAK,CAACkF,MAAM,GAAG,CAAC,EAAE;YAClE,MAAMmB,KAAK,GAAGtC,WAAW,KAAK,CAAC,CAAC,GAAG/D,KAAK,CAACkF,MAAM,GAAG,CAAC,GAAGnB,WAAW;YACjE,MAAMgB,QAAQ,GAAG/E,KAAK,CAACZ,KAAK,CAAC,CAAC;YAC9B2F,QAAQ,CAACuF,MAAM,CAACjE,KAAK,EAAE,CAAC,CAAC;YACzBuD,WAAW,CAAC9E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3C/F,MAAM,EAAEgB,KAAK,CAACqG,KAAK;YACrB,CAAC,CAAC;UACJ;UACA,IAAI,CAACpG,QAAQ,IAAIC,WAAW,IAAI,CAAC0C,QAAQ,EAAE;YACzCwB,aAAa,CAAC,IAAI,CAAC;YACnB2B,SAAS,CAAC,CAAC,CAAC,CAAC;UACf;UACA;QACF,KAAK,QAAQ;UACX;UACA,IAAI9F,QAAQ,IAAI,CAAC2C,QAAQ,IAAIlE,UAAU,KAAK,EAAE,IAAIsB,KAAK,CAACkF,MAAM,GAAG,CAAC,IAAInB,WAAW,KAAK,CAAC,CAAC,EAAE;YACxF,MAAMsC,KAAK,GAAGtC,WAAW;YACzB,MAAMgB,QAAQ,GAAG/E,KAAK,CAACZ,KAAK,CAAC,CAAC;YAC9B2F,QAAQ,CAACuF,MAAM,CAACjE,KAAK,EAAE,CAAC,CAAC;YACzBuD,WAAW,CAAC9E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3C/F,MAAM,EAAEgB,KAAK,CAACqG,KAAK;YACrB,CAAC,CAAC;UACJ;UACA,IAAI,CAACpG,QAAQ,IAAIC,WAAW,IAAI,CAAC0C,QAAQ,EAAE;YACzCwB,aAAa,CAAC,IAAI,CAAC;YACnB2B,SAAS,CAAC,CAAC,CAAC,CAAC;UACf;UACA;QACF;MACF;IACF;EACF,CAAC;EACD,MAAMuF,WAAW,GAAGxG,KAAK,IAAI;IAC3BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIhC,WAAW,IAAI,CAACa,WAAW,CAAC/D,OAAO,EAAE;MACvCiK,UAAU,CAAC5E,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMyG,UAAU,GAAGzG,KAAK,IAAI;IAC1B;IACA,IAAIxE,iCAAiC,CAACd,UAAU,CAAC,EAAE;MACjDmE,QAAQ,CAAClE,OAAO,CAACwG,KAAK,CAAC,CAAC;MACxB;IACF;IACAtB,UAAU,CAAC,KAAK,CAAC;IACjBjB,UAAU,CAACjE,OAAO,GAAG,IAAI;IACzB+D,WAAW,CAAC/D,OAAO,GAAG,KAAK;IAC3B,IAAIiB,UAAU,IAAIwD,mBAAmB,CAACzE,OAAO,KAAK,CAAC,CAAC,IAAI+F,SAAS,EAAE;MACjEuE,cAAc,CAACjF,KAAK,EAAEhG,eAAe,CAACoF,mBAAmB,CAACzE,OAAO,CAAC,EAAE,MAAM,CAAC;IAC7E,CAAC,MAAM,IAAIiB,UAAU,IAAIG,QAAQ,IAAInC,UAAU,KAAK,EAAE,EAAE;MACtDqL,cAAc,CAACjF,KAAK,EAAEpG,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;IACvD,CAAC,MAAM,IAAIkC,WAAW,EAAE;MACtBgE,eAAe,CAACE,KAAK,EAAE9E,KAAK,EAAE,MAAM,CAAC;IACvC;IACA2J,WAAW,CAAC7E,KAAK,EAAE,MAAM,CAAC;EAC5B,CAAC;EACD,MAAM0G,iBAAiB,GAAG1G,KAAK,IAAI;IACjC,MAAMC,QAAQ,GAAGD,KAAK,CAAC2G,MAAM,CAACzL,KAAK;IACnC,IAAItB,UAAU,KAAKqG,QAAQ,EAAE;MAC3BP,kBAAkB,CAACO,QAAQ,CAAC;MAC5BO,gBAAgB,CAAC,KAAK,CAAC;MACvB,IAAI/C,aAAa,EAAE;QACjBA,aAAa,CAACuC,KAAK,EAAEC,QAAQ,EAAE,OAAO,CAAC;MACzC;IACF;IACA,IAAIA,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAC9D,gBAAgB,IAAI,CAAChB,QAAQ,EAAE;QAClC2J,WAAW,CAAC9E,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;MACnC;IACF,CAAC,MAAM;MACL4E,UAAU,CAAC5E,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAM4G,qBAAqB,GAAG5G,KAAK,IAAI;IACrC,MAAMuB,KAAK,GAAGsF,MAAM,CAAC7G,KAAK,CAAC8G,aAAa,CAACnF,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3E,IAAIvC,mBAAmB,CAACzE,OAAO,KAAK4G,KAAK,EAAE;MACzCM,mBAAmB,CAAC;QAClB7B,KAAK;QACLuB,KAAK;QACLrB,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM6G,sBAAsB,GAAG/G,KAAK,IAAI;IACtC6B,mBAAmB,CAAC;MAClB7B,KAAK;MACLuB,KAAK,EAAEsF,MAAM,CAAC7G,KAAK,CAAC8G,aAAa,CAACnF,YAAY,CAAC,mBAAmB,CAAC,CAAC;MACpEzB,MAAM,EAAE;IACV,CAAC,CAAC;IACF8E,OAAO,CAACrK,OAAO,GAAG,IAAI;EACxB,CAAC;EACD,MAAMqM,iBAAiB,GAAGhH,KAAK,IAAI;IACjC,MAAMuB,KAAK,GAAGsF,MAAM,CAAC7G,KAAK,CAAC8G,aAAa,CAACnF,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3EsD,cAAc,CAACjF,KAAK,EAAEhG,eAAe,CAACuH,KAAK,CAAC,EAAE,cAAc,CAAC;IAC7DyD,OAAO,CAACrK,OAAO,GAAG,KAAK;EACzB,CAAC;EACD,MAAMsM,gBAAgB,GAAG1F,KAAK,IAAIvB,KAAK,IAAI;IACzC,MAAMC,QAAQ,GAAG/E,KAAK,CAACZ,KAAK,CAAC,CAAC;IAC9B2F,QAAQ,CAACuF,MAAM,CAACjE,KAAK,EAAE,CAAC,CAAC;IACzBuD,WAAW,CAAC9E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;MAC3C/F,MAAM,EAAEgB,KAAK,CAACqG,KAAK;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,MAAM2F,sBAAsB,GAAGlH,KAAK,IAAI;IACtC8E,WAAW,CAAC9E,KAAK,EAAE,IAAI,EAAE,cAAc,EAAE;MACvC9F,MAAM,EAAEgB;IACV,CAAC,CAAC;EACJ,CAAC;EACD,MAAMiM,oBAAoB,GAAGnH,KAAK,IAAI;IACpC,IAAIrC,IAAI,EAAE;MACRkH,WAAW,CAAC7E,KAAK,EAAE,aAAa,CAAC;IACnC,CAAC,MAAM;MACL4E,UAAU,CAAC5E,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoH,eAAe,GAAGpH,KAAK,IAAI;IAC/B;IACA,IAAI,CAACA,KAAK,CAAC8G,aAAa,CAACjM,QAAQ,CAACmF,KAAK,CAAC2G,MAAM,CAAC,EAAE;MAC/C;IACF;IACA,IAAI3G,KAAK,CAAC2G,MAAM,CAAChF,YAAY,CAAC,IAAI,CAAC,KAAK1E,EAAE,EAAE;MAC1C+C,KAAK,CAACsG,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMe,WAAW,GAAGrH,KAAK,IAAI;IAC3B;IACA,IAAI,CAACA,KAAK,CAAC8G,aAAa,CAACjM,QAAQ,CAACmF,KAAK,CAAC2G,MAAM,CAAC,EAAE;MAC/C;IACF;IACA9H,QAAQ,CAAClE,OAAO,CAACwG,KAAK,CAAC,CAAC;IACxB,IAAIpD,aAAa,IAAIa,UAAU,CAACjE,OAAO,IAAIkE,QAAQ,CAAClE,OAAO,CAAC2M,YAAY,GAAGzI,QAAQ,CAAClE,OAAO,CAAC4M,cAAc,KAAK,CAAC,EAAE;MAChH1I,QAAQ,CAAClE,OAAO,CAAC6M,MAAM,CAAC,CAAC;IAC3B;IACA5I,UAAU,CAACjE,OAAO,GAAG,KAAK;EAC5B,CAAC;EACD,MAAM8M,oBAAoB,GAAGzH,KAAK,IAAI;IACpC,IAAI,CAAC1D,YAAY,KAAK1C,UAAU,KAAK,EAAE,IAAI,CAAC+D,IAAI,CAAC,EAAE;MACjDwJ,oBAAoB,CAACnH,KAAK,CAAC;IAC7B;EACF,CAAC;EACD,IAAI0H,KAAK,GAAG3L,QAAQ,IAAInC,UAAU,CAACwG,MAAM,GAAG,CAAC;EAC7CsH,KAAK,GAAGA,KAAK,KAAKvM,QAAQ,GAAGD,KAAK,CAACkF,MAAM,GAAG,CAAC,GAAGlF,KAAK,KAAK,IAAI,CAAC;EAC/D,IAAIyM,cAAc,GAAG3N,eAAe;EACpC,IAAI+C,OAAO,EAAE;IACX;IACA,MAAM6K,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAInD,IAAI,GAAG,KAAK;IAChBiD,cAAc,GAAG3N,eAAe,CAAC8N,MAAM,CAAC,CAACC,GAAG,EAAE7N,MAAM,EAAEqH,KAAK,KAAK;MAC9D,MAAMyG,KAAK,GAAGjL,OAAO,CAAC7C,MAAM,CAAC;MAC7B,IAAI6N,GAAG,CAAC3H,MAAM,GAAG,CAAC,IAAI2H,GAAG,CAACA,GAAG,CAAC3H,MAAM,GAAG,CAAC,CAAC,CAAC4H,KAAK,KAAKA,KAAK,EAAE;QACzDD,GAAG,CAACA,GAAG,CAAC3H,MAAM,GAAG,CAAC,CAAC,CAACzG,OAAO,CAAC4L,IAAI,CAACrL,MAAM,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI+D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAIyJ,OAAO,CAACK,GAAG,CAACD,KAAK,CAAC,IAAI,CAACtD,IAAI,EAAE;YAC/BpG,OAAO,CAACoG,IAAI,CAAC,qEAAqEzI,aAAa,8BAA8B,EAAE,8EAA8E,CAAC;YAC9MyI,IAAI,GAAG,IAAI;UACb;UACAkD,OAAO,CAACM,GAAG,CAACF,KAAK,EAAE,IAAI,CAAC;QAC1B;QACAD,GAAG,CAACxC,IAAI,CAAC;UACPa,GAAG,EAAE7E,KAAK;UACVA,KAAK;UACLyG,KAAK;UACLrO,OAAO,EAAE,CAACO,MAAM;QAClB,CAAC,CAAC;MACJ;MACA,OAAO6N,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EACA,IAAIzL,YAAY,IAAIsD,OAAO,EAAE;IAC3B6G,UAAU,CAAC,CAAC;EACd;EACA,OAAO;IACL0B,YAAY,EAAEA,CAAClC,KAAK,GAAG,CAAC,CAAC,MAAM;MAC7B,GAAGA,KAAK;MACRC,SAAS,EAAEF,aAAa,CAACC,KAAK,CAAC;MAC/BmC,WAAW,EAAEhB,eAAe;MAC5BiB,OAAO,EAAEhB;IACX,CAAC,CAAC;IACFiB,kBAAkB,EAAEA,CAAA,MAAO;MACzBrL,EAAE,EAAE,GAAGA,EAAE,QAAQ;MACjBsL,OAAO,EAAEtL;IACX,CAAC,CAAC;IACFuL,aAAa,EAAEA,CAAA,MAAO;MACpBvL,EAAE;MACF/B,KAAK,EAAEtB,UAAU;MACjB6O,MAAM,EAAEhC,UAAU;MAClBiC,OAAO,EAAElC,WAAW;MACpBlJ,QAAQ,EAAEoJ,iBAAiB;MAC3B0B,WAAW,EAAEX,oBAAoB;MACjC;MACA;MACA,uBAAuB,EAAE/G,SAAS,GAAG,EAAE,GAAG,IAAI;MAC9C,mBAAmB,EAAEhF,YAAY,GAAG,MAAM,GAAG,MAAM;MACnD,eAAe,EAAEsF,gBAAgB,GAAG,GAAG/D,EAAE,UAAU,GAAGoB,SAAS;MAC/D,eAAe,EAAE2C,gBAAgB;MACjC;MACA;MACAtF,YAAY,EAAE,KAAK;MACnBiN,GAAG,EAAE9J,QAAQ;MACb+J,cAAc,EAAE,MAAM;MACtBC,UAAU,EAAE,OAAO;MACnBC,IAAI,EAAE,UAAU;MAChBzM,QAAQ,EAAEC;IACZ,CAAC,CAAC;IACFyM,aAAa,EAAEA,CAAA,MAAO;MACpBC,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAEtC;IACX,CAAC,CAAC;IACFmD,YAAY,EAAEA,CAAC;MACb3H,KAAK,GAAG;IACV,CAAC,GAAG,CAAC,CAAC,MAAM;MACV,IAAIpG,QAAQ,IAAI;QACdiL,GAAG,EAAE7E;MACP,CAAC,CAAC;MACF,IAAInG,WAAW,GAAG;QAChB,iBAAiB,EAAEmG;MACrB,CAAC,GAAG;QACF,gBAAgB,EAAEA;MACpB,CAAC,CAAC;MACFyH,QAAQ,EAAE,CAAC,CAAC;MACZ,IAAI,CAAClL,QAAQ,IAAI;QACfqL,QAAQ,EAAEhO,QAAQ,GAAG8L,gBAAgB,CAAC1F,KAAK,CAAC,GAAG2F;MACjD,CAAC;IACH,CAAC,CAAC;IACFkC,sBAAsB,EAAEA,CAAA,MAAO;MAC7BJ,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAElB;IACX,CAAC,CAAC;IACF;IACAkC,WAAW,EAAEA,CAAC;MACZ9H;IACF,CAAC,MAAM;MACL6E,GAAG,EAAE7E,KAAK;MACV,gBAAgB,EAAEA,KAAK;MACvByH,QAAQ,EAAE,CAAC,CAAC;MACZ,IAAI,CAAClL,QAAQ,IAAI;QACfqL,QAAQ,EAAElC,gBAAgB,CAAC1F,KAAK;MAClC,CAAC;IACH,CAAC,CAAC;IACF+H,eAAe,EAAEA,CAAA,MAAO;MACtBR,IAAI,EAAE,SAAS;MACf7L,EAAE,EAAE,GAAGA,EAAE,UAAU;MACnB,iBAAiB,EAAE,GAAGA,EAAE,QAAQ;MAChC0L,GAAG,EAAEpE,gBAAgB;MACrB6D,WAAW,EAAEpI,KAAK,IAAI;QACpB;QACAA,KAAK,CAACsG,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IACFiD,cAAc,EAAEA,CAAC;MACfhI,KAAK;MACLrH;IACF,CAAC,KAAK;MACJ,MAAMsP,QAAQ,GAAG,CAACrO,QAAQ,GAAGD,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEyF,IAAI,CAACC,MAAM,IAAIA,MAAM,IAAI,IAAI,IAAIvD,oBAAoB,CAACnD,MAAM,EAAE0G,MAAM,CAAC,CAAC;MACpH,MAAMvE,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAACzC,MAAM,CAAC,GAAG,KAAK;MACtE,OAAO;QACLkM,GAAG,EAAExJ,YAAY,GAAG1C,MAAM,CAAC,IAAIL,cAAc,CAACK,MAAM,CAAC;QACrD8O,QAAQ,EAAE,CAAC,CAAC;QACZF,IAAI,EAAE,QAAQ;QACd7L,EAAE,EAAE,GAAGA,EAAE,WAAWsE,KAAK,EAAE;QAC3BkI,WAAW,EAAE7C,qBAAqB;QAClCyB,OAAO,EAAErB,iBAAiB;QAC1B0C,YAAY,EAAE3C,sBAAsB;QACpC,mBAAmB,EAAExF,KAAK;QAC1B,eAAe,EAAElF,QAAQ;QACzB,eAAe,EAAEmN;MACnB,CAAC;IACH,CAAC;IACDvM,EAAE;IACFrD,UAAU;IACVsB,KAAK;IACLwM,KAAK;IACLiC,QAAQ,EAAEjJ,SAAS,IAAI5B,QAAQ;IAC/B4B,SAAS;IACTd,OAAO,EAAEA,OAAO,IAAIX,WAAW,KAAK,CAAC,CAAC;IACtCH,QAAQ;IACRC,WAAW;IACXE,WAAW;IACX;IACA2K,UAAU,EAAE3K,WAAW;IACvB0I;EACF,CAAC;AACH;AACA,eAAerM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}