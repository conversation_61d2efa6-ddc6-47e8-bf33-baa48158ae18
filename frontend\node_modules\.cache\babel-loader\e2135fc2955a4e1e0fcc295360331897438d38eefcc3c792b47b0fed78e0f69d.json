{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Dropdown from './Dropdown';\nimport NavLink from './NavLink';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst NavDropdown = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    id,\n    title,\n    children,\n    bsPrefix,\n    className,\n    rootCloseEvent,\n    menuRole,\n    disabled,\n    active,\n    renderMenuOnMount,\n    menuVariant,\n    ...props\n  } = _ref;\n  /* NavItem has no additional logic, it's purely presentational. Can set nav item class here to support \"as\" */\n  const navItemPrefix = useBootstrapPrefix(undefined, 'nav-item');\n  return /*#__PURE__*/_jsxs(Dropdown, {\n    ref: ref,\n    ...props,\n    className: classNames(className, navItemPrefix),\n    children: [/*#__PURE__*/_jsx(Dropdown.Toggle, {\n      id: id,\n      eventKey: null,\n      active: active,\n      disabled: disabled,\n      childBsPrefix: bsPrefix,\n      as: NavLink,\n      children: title\n    }), /*#__PURE__*/_jsx(Dropdown.Menu, {\n      role: menuRole,\n      renderOnMount: renderMenuOnMount,\n      rootCloseEvent: rootCloseEvent,\n      variant: menuVariant,\n      children: children\n    })]\n  });\n});\nNavDropdown.displayName = 'NavDropdown';\nexport default Object.assign(NavDropdown, {\n  Item: Dropdown.Item,\n  ItemText: Dropdown.ItemText,\n  Divider: Dropdown.Divider,\n  Header: Dropdown.Header\n});", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "Dropdown", "NavLink", "jsx", "_jsx", "jsxs", "_jsxs", "NavDropdown", "forwardRef", "_ref", "ref", "id", "title", "children", "bsPrefix", "className", "rootCloseEvent", "menuRole", "disabled", "active", "renderMenuOnMount", "menuVariant", "props", "navItemPrefix", "undefined", "Toggle", "eventKey", "childBsPrefix", "as", "<PERSON><PERSON>", "role", "renderOnMount", "variant", "displayName", "Object", "assign", "<PERSON><PERSON>", "ItemText", "Divider", "Header"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/NavDropdown.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Dropdown from './Dropdown';\nimport NavLink from './NavLink';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst NavDropdown = /*#__PURE__*/React.forwardRef(({\n  id,\n  title,\n  children,\n  bsPrefix,\n  className,\n  rootCloseEvent,\n  menuRole,\n  disabled,\n  active,\n  renderMenuOnMount,\n  menuVariant,\n  ...props\n}, ref) => {\n  /* NavItem has no additional logic, it's purely presentational. Can set nav item class here to support \"as\" */\n  const navItemPrefix = useBootstrapPrefix(undefined, 'nav-item');\n  return /*#__PURE__*/_jsxs(Dropdown, {\n    ref: ref,\n    ...props,\n    className: classNames(className, navItemPrefix),\n    children: [/*#__PURE__*/_jsx(Dropdown.Toggle, {\n      id: id,\n      eventKey: null,\n      active: active,\n      disabled: disabled,\n      childBsPrefix: bsPrefix,\n      as: NavLink,\n      children: title\n    }), /*#__PURE__*/_jsx(Dropdown.Menu, {\n      role: menuRole,\n      renderOnMount: renderMenuOnMount,\n      rootCloseEvent: rootCloseEvent,\n      variant: menuVariant,\n      children: children\n    })]\n  });\n});\nNavDropdown.displayName = 'NavDropdown';\nexport default Object.assign(NavDropdown, {\n  Item: Dropdown.Item,\n  ItemText: Dropdown.ItemText,\n  Divider: Dropdown.Divider,\n  Header: Dropdown.Header\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,WAAW,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAa/CC,GAAG,KAAK;EAAA,IAbwC;IACjDC,EAAE;IACFC,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRC,SAAS;IACTC,cAAc;IACdC,QAAQ;IACRC,QAAQ;IACRC,MAAM;IACNC,iBAAiB;IACjBC,WAAW;IACX,GAAGC;EACL,CAAC,GAAAb,IAAA;EACC;EACA,MAAMc,aAAa,GAAGvB,kBAAkB,CAACwB,SAAS,EAAE,UAAU,CAAC;EAC/D,OAAO,aAAalB,KAAK,CAACL,QAAQ,EAAE;IAClCS,GAAG,EAAEA,GAAG;IACR,GAAGY,KAAK;IACRP,SAAS,EAAEjB,UAAU,CAACiB,SAAS,EAAEQ,aAAa,CAAC;IAC/CV,QAAQ,EAAE,CAAC,aAAaT,IAAI,CAACH,QAAQ,CAACwB,MAAM,EAAE;MAC5Cd,EAAE,EAAEA,EAAE;MACNe,QAAQ,EAAE,IAAI;MACdP,MAAM,EAAEA,MAAM;MACdD,QAAQ,EAAEA,QAAQ;MAClBS,aAAa,EAAEb,QAAQ;MACvBc,EAAE,EAAE1B,OAAO;MACXW,QAAQ,EAAED;IACZ,CAAC,CAAC,EAAE,aAAaR,IAAI,CAACH,QAAQ,CAAC4B,IAAI,EAAE;MACnCC,IAAI,EAAEb,QAAQ;MACdc,aAAa,EAAEX,iBAAiB;MAChCJ,cAAc,EAAEA,cAAc;MAC9BgB,OAAO,EAAEX,WAAW;MACpBR,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,WAAW,CAAC0B,WAAW,GAAG,aAAa;AACvC,eAAeC,MAAM,CAACC,MAAM,CAAC5B,WAAW,EAAE;EACxC6B,IAAI,EAAEnC,QAAQ,CAACmC,IAAI;EACnBC,QAAQ,EAAEpC,QAAQ,CAACoC,QAAQ;EAC3BC,OAAO,EAAErC,QAAQ,CAACqC,OAAO;EACzBC,MAAM,EAAEtC,QAAQ,CAACsC;AACnB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}