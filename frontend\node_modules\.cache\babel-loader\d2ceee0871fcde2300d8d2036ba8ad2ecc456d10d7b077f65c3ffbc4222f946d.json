{"ast": null, "code": "// src/utilities/currentValue.ts\nfunction isRef(value) {\n  return value != null && typeof value === \"object\" && \"current\" in value;\n}\nfunction currentValue(value) {\n  var _a;\n  if (value == null) {\n    return void 0;\n  }\n  if (isRef(value)) {\n    return (_a = value.current) != null ? _a : void 0;\n  }\n  return value;\n}\nexport { currentValue };", "map": {"version": 3, "names": ["isRef", "value", "currentValue", "_a", "current"], "sources": ["C:/laragon/www/frontend/node_modules/@dnd-kit/react/utilities.js"], "sourcesContent": ["// src/utilities/currentValue.ts\nfunction isRef(value) {\n  return value != null && typeof value === \"object\" && \"current\" in value;\n}\nfunction currentValue(value) {\n  var _a;\n  if (value == null) {\n    return void 0;\n  }\n  if (isRef(value)) {\n    return (_a = value.current) != null ? _a : void 0;\n  }\n  return value;\n}\n\nexport { currentValue };\n//# sourceMappingURL=utilities.js.map\n//# sourceMappingURL=utilities.js.map"], "mappings": "AAAA;AACA,SAASA,KAAKA,CAACC,KAAK,EAAE;EACpB,OAAOA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,SAAS,IAAIA,KAAK;AACzE;AACA,SAASC,YAAYA,CAACD,KAAK,EAAE;EAC3B,IAAIE,EAAE;EACN,IAAIF,KAAK,IAAI,IAAI,EAAE;IACjB,OAAO,KAAK,CAAC;EACf;EACA,IAAID,KAAK,CAACC,KAAK,CAAC,EAAE;IAChB,OAAO,CAACE,EAAE,GAAGF,KAAK,CAACG,OAAO,KAAK,IAAI,GAAGD,EAAE,GAAG,KAAK,CAAC;EACnD;EACA,OAAOF,KAAK;AACd;AAEA,SAASC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}