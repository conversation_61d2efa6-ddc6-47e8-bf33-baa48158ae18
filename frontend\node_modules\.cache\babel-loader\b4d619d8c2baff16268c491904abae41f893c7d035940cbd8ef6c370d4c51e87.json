{"ast": null, "code": "import * as React from 'react';\nimport { extendSxProp } from '@mui/system/styleFunctionSx';\nimport useTheme from \"../styles/useTheme.js\";\nimport GlobalStyles from \"../GlobalStyles/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport { css, keyframes } from '@mui/system';\nexport { default as styled } from \"../styles/styled.js\";\nexport function globalCss(styles) {\n  return function GlobalStylesWrapper(props) {\n    return (/*#__PURE__*/\n      // Pigment CSS `globalCss` support callback with theme inside an object but `GlobalStyles` support theme as a callback value.\n      _jsx(GlobalStyles, {\n        styles: typeof styles === 'function' ? theme => styles({\n          theme,\n          ...props\n        }) : styles\n      })\n    );\n  };\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function internal_createExtendSxProp() {\n  return extendSxProp;\n}\nexport { useTheme };", "map": {"version": 3, "names": ["React", "extendSxProp", "useTheme", "GlobalStyles", "jsx", "_jsx", "css", "keyframes", "default", "styled", "globalCss", "styles", "GlobalStylesWrapper", "props", "theme", "internal_createExtendSxProp"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/zero-styled/index.js"], "sourcesContent": ["import * as React from 'react';\nimport { extendSxProp } from '@mui/system/styleFunctionSx';\nimport useTheme from \"../styles/useTheme.js\";\nimport GlobalStyles from \"../GlobalStyles/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport { css, keyframes } from '@mui/system';\nexport { default as styled } from \"../styles/styled.js\";\nexport function globalCss(styles) {\n  return function GlobalStylesWrapper(props) {\n    return (\n      /*#__PURE__*/\n      // Pigment CSS `globalCss` support callback with theme inside an object but `GlobalStyles` support theme as a callback value.\n      _jsx(GlobalStyles, {\n        styles: typeof styles === 'function' ? theme => styles({\n          theme,\n          ...props\n        }) : styles\n      })\n    );\n  };\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function internal_createExtendSxProp() {\n  return extendSxProp;\n}\nexport { useTheme };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,GAAG,EAAEC,SAAS,QAAQ,aAAa;AAC5C,SAASC,OAAO,IAAIC,MAAM,QAAQ,qBAAqB;AACvD,OAAO,SAASC,SAASA,CAACC,MAAM,EAAE;EAChC,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;IACzC,QACE;MACA;MACAR,IAAI,CAACF,YAAY,EAAE;QACjBQ,MAAM,EAAE,OAAOA,MAAM,KAAK,UAAU,GAAGG,KAAK,IAAIH,MAAM,CAAC;UACrDG,KAAK;UACL,GAAGD;QACL,CAAC,CAAC,GAAGF;MACP,CAAC;IAAC;EAEN,CAAC;AACH;;AAEA;AACA,OAAO,SAASI,2BAA2BA,CAAA,EAAG;EAC5C,OAAOd,YAAY;AACrB;AACA,SAASC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}