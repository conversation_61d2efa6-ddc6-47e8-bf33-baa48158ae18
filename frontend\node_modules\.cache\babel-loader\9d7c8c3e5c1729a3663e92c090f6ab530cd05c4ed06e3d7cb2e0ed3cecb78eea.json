{"ast": null, "code": "import * as React from 'react';\nimport { useMemo } from 'react';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormGroup = /*#__PURE__*/React.forwardRef(({\n  controlId,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const context = useMemo(() => ({\n    controlId\n  }), [controlId]);\n  return /*#__PURE__*/_jsx(FormContext.Provider, {\n    value: context,\n    children: /*#__PURE__*/_jsx(Component, {\n      ...props,\n      ref: ref\n    })\n  });\n});\nFormGroup.displayName = 'FormGroup';\nexport default FormGroup;", "map": {"version": 3, "names": ["React", "useMemo", "FormContext", "jsx", "_jsx", "FormGroup", "forwardRef", "controlId", "as", "Component", "props", "ref", "context", "Provider", "value", "children", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FormGroup.js"], "sourcesContent": ["import * as React from 'react';\nimport { useMemo } from 'react';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormGroup = /*#__PURE__*/React.forwardRef(({\n  controlId,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const context = useMemo(() => ({\n    controlId\n  }), [controlId]);\n  return /*#__PURE__*/_jsx(FormContext.Provider, {\n    value: context,\n    children: /*#__PURE__*/_jsx(Component, {\n      ...props,\n      ref: ref\n    })\n  });\n});\nFormGroup.displayName = 'FormGroup';\nexport default FormGroup;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EAC/CC,SAAS;EACT;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,OAAO,GAAGX,OAAO,CAAC,OAAO;IAC7BM;EACF,CAAC,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAChB,OAAO,aAAaH,IAAI,CAACF,WAAW,CAACW,QAAQ,EAAE;IAC7CC,KAAK,EAAEF,OAAO;IACdG,QAAQ,EAAE,aAAaX,IAAI,CAACK,SAAS,EAAE;MACrC,GAAGC,KAAK;MACRC,GAAG,EAAEA;IACP,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,SAAS,CAACW,WAAW,GAAG,WAAW;AACnC,eAAeX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}