{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\pages\\\\StaticPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Container, <PERSON><PERSON>, Spinner } from 'react-bootstrap';\nimport { Helmet } from 'react-helmet-async';\nimport { Render } from '@measured/puck';\nimport { useSiteName } from '../../contexts/SettingsContext';\nimport '../puck/PuckRenderer.css';\n\n// Import the same Puck configuration used in the editor\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst puckConfig = {\n  components: {\n    // Layout Components\n    Hero: {\n      label: 'Hero Section',\n      defaultProps: {\n        title: 'Hero Title',\n        subtitle: 'Hero subtitle text',\n        textAlign: 'center',\n        minHeight: '400px',\n        backgroundType: 'gradient',\n        backgroundColor: '#667eea',\n        backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        textColor: '#ffffff'\n      },\n      fields: {\n        title: {\n          type: 'text'\n        },\n        subtitle: {\n          type: 'textarea'\n        },\n        textAlign: {\n          type: 'select',\n          options: [{\n            label: 'Left',\n            value: 'left'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Right',\n            value: 'right'\n          }]\n        },\n        minHeight: {\n          type: 'text'\n        },\n        backgroundType: {\n          type: 'select',\n          options: [{\n            label: 'Solid Color',\n            value: 'solid'\n          }, {\n            label: 'Gradient',\n            value: 'gradient'\n          }, {\n            label: 'Image',\n            value: 'image'\n          }]\n        },\n        backgroundColor: {\n          type: 'text'\n        },\n        backgroundGradient: {\n          type: 'text'\n        },\n        backgroundImage: {\n          type: 'text'\n        },\n        textColor: {\n          type: 'text'\n        }\n      },\n      render: ({\n        title,\n        subtitle,\n        textAlign,\n        minHeight,\n        backgroundType,\n        backgroundColor,\n        backgroundGradient,\n        backgroundImage,\n        textColor\n      }) => {\n        let backgroundStyle = {};\n        switch (backgroundType) {\n          case 'solid':\n            backgroundStyle.backgroundColor = backgroundColor || '#667eea';\n            break;\n          case 'gradient':\n            backgroundStyle.background = backgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n            break;\n          case 'image':\n            if (backgroundImage) {\n              backgroundStyle.backgroundImage = `url(${backgroundImage})`;\n              backgroundStyle.backgroundSize = 'cover';\n              backgroundStyle.backgroundPosition = 'center';\n            }\n            break;\n        }\n        return /*#__PURE__*/_jsxDEV(\"section\", {\n          style: {\n            minHeight: minHeight || '400px',\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',\n            textAlign: textAlign || 'center',\n            padding: '4rem 2rem',\n            color: textColor || 'white',\n            position: 'relative',\n            ...backgroundStyle\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative',\n              zIndex: 1,\n              maxWidth: '800px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              style: {\n                fontSize: 'clamp(2rem, 5vw, 3.5rem)',\n                marginBottom: '1rem',\n                fontWeight: 'bold',\n                lineHeight: 1.2\n              },\n              children: title || 'Hero Title'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n                opacity: 0.9,\n                lineHeight: 1.6,\n                marginBottom: '2rem'\n              },\n              children: subtitle || 'Hero subtitle text'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this);\n      }\n    },\n    // Text Component\n    Text: {\n      label: 'Text Block',\n      defaultProps: {\n        text: '<p>Add your text content here...</p>',\n        textAlign: 'left',\n        maxWidth: '100%',\n        fontSize: '16px',\n        lineHeight: '1.6',\n        color: '#333333',\n        padding: '1rem'\n      },\n      fields: {\n        text: {\n          type: 'textarea'\n        },\n        textAlign: {\n          type: 'select',\n          options: [{\n            label: 'Left',\n            value: 'left'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Right',\n            value: 'right'\n          }, {\n            label: 'Justify',\n            value: 'justify'\n          }]\n        },\n        maxWidth: {\n          type: 'text'\n        },\n        fontSize: {\n          type: 'text'\n        },\n        lineHeight: {\n          type: 'text'\n        },\n        color: {\n          type: 'text'\n        },\n        padding: {\n          type: 'text'\n        }\n      },\n      render: ({\n        text,\n        textAlign,\n        maxWidth,\n        fontSize,\n        lineHeight,\n        color,\n        padding\n      }) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: padding || '1rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: maxWidth || '100%',\n            margin: textAlign === 'center' ? '0 auto' : '0',\n            textAlign: textAlign || 'left',\n            fontSize: fontSize || '16px',\n            lineHeight: lineHeight || '1.6',\n            color: color || '#333333'\n          },\n          dangerouslySetInnerHTML: {\n            __html: text || '<p>Add your text content here...</p>'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }\n  }\n};\nconst StaticPage = ({\n  slug: propSlug\n}) => {\n  _s();\n  const {\n    slug: paramSlug\n  } = useParams();\n  const slug = propSlug || paramSlug;\n  const siteName = useSiteName();\n  const [pageData, setPageData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    if (!slug) {\n      setError('Page slug is required');\n      setLoading(false);\n      return;\n    }\n    const fetchPageData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(`${process.env.REACT_APP_API_URL}/pages/${slug}`);\n        if (!response.ok) {\n          if (response.status === 404) {\n            setError('Page not found');\n          } else {\n            setError('Failed to load page');\n          }\n          return;\n        }\n        const result = await response.json();\n        if (result.success) {\n          setPageData(result.data);\n        } else {\n          setError(result.message || 'Failed to load page');\n        }\n      } catch (err) {\n        console.error('Error fetching page:', err);\n        setError('Failed to load page');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPageData();\n  }, [slug]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-3\",\n        children: \"Loading page...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !pageData) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error || 'Page not found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: [pageData.meta_title || pageData.title, \" - \", siteName]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), pageData.meta_description && /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: pageData.meta_description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"puck-renderer\",\n      children: pageData.content && pageData.content.content ? /*#__PURE__*/_jsxDEV(Render, {\n        config: puckConfig,\n        data: pageData.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Container, {\n        className: \"py-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: pageData.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"This page is currently being set up. Please check back later.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(StaticPage, \"GkN8D7HUSdwfGX5n/QggloW41oQ=\", false, function () {\n  return [useParams, useSiteName];\n});\n_c = StaticPage;\nexport default StaticPage;\nvar _c;\n$RefreshReg$(_c, \"StaticPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Container", "<PERSON><PERSON>", "Spinner", "<PERSON><PERSON><PERSON>", "Render", "useSiteName", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "puckConfig", "components", "Hero", "label", "defaultProps", "title", "subtitle", "textAlign", "minHeight", "backgroundType", "backgroundColor", "backgroundGradient", "textColor", "fields", "type", "options", "value", "backgroundImage", "render", "backgroundStyle", "background", "backgroundSize", "backgroundPosition", "style", "display", "flexDirection", "justifyContent", "alignItems", "padding", "color", "position", "children", "zIndex", "max<PERSON><PERSON><PERSON>", "fontSize", "marginBottom", "fontWeight", "lineHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "opacity", "Text", "text", "margin", "dangerouslySetInnerHTML", "__html", "StaticPage", "slug", "propSlug", "_s", "paramSlug", "siteName", "pageData", "setPageData", "loading", "setLoading", "error", "setError", "fetchPageData", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "status", "result", "json", "success", "data", "message", "err", "console", "className", "animation", "role", "variant", "Heading", "meta_title", "meta_description", "name", "content", "config", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/pages/StaticPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';\nimport { Helmet } from 'react-helmet-async';\nimport { Render } from '@measured/puck';\nimport { useSiteName } from '../../contexts/SettingsContext';\nimport '../puck/PuckRenderer.css';\n\n// Import the same Puck configuration used in the editor\nconst puckConfig = {\n  components: {\n    // Layout Components\n    Hero: {\n      label: 'Hero Section',\n      defaultProps: {\n        title: 'Hero Title',\n        subtitle: 'Hero subtitle text',\n        textAlign: 'center',\n        minHeight: '400px',\n        backgroundType: 'gradient',\n        backgroundColor: '#667eea',\n        backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        textColor: '#ffffff',\n      },\n      fields: {\n        title: { type: 'text' },\n        subtitle: { type: 'textarea' },\n        textAlign: {\n          type: 'select',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n          ],\n        },\n        minHeight: { type: 'text' },\n        backgroundType: {\n          type: 'select',\n          options: [\n            { label: 'Solid Color', value: 'solid' },\n            { label: 'Gradient', value: 'gradient' },\n            { label: 'Image', value: 'image' },\n          ],\n        },\n        backgroundColor: { type: 'text' },\n        backgroundGradient: { type: 'text' },\n        backgroundImage: { type: 'text' },\n        textColor: { type: 'text' },\n      },\n      render: ({ title, subtitle, textAlign, minHeight, backgroundType, backgroundColor, backgroundGradient, backgroundImage, textColor }: any) => {\n        let backgroundStyle: React.CSSProperties = {};\n\n        switch (backgroundType) {\n          case 'solid':\n            backgroundStyle.backgroundColor = backgroundColor || '#667eea';\n            break;\n          case 'gradient':\n            backgroundStyle.background = backgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n            break;\n          case 'image':\n            if (backgroundImage) {\n              backgroundStyle.backgroundImage = `url(${backgroundImage})`;\n              backgroundStyle.backgroundSize = 'cover';\n              backgroundStyle.backgroundPosition = 'center';\n            }\n            break;\n        }\n\n        return (\n          <section\n            style={{\n              minHeight: minHeight || '400px',\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'center',\n              alignItems: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',\n              textAlign: textAlign || 'center',\n              padding: '4rem 2rem',\n              color: textColor || 'white',\n              position: 'relative',\n              ...backgroundStyle,\n            }}\n          >\n            <div style={{ position: 'relative', zIndex: 1, maxWidth: '800px' }}>\n              <h1 style={{\n                fontSize: 'clamp(2rem, 5vw, 3.5rem)',\n                marginBottom: '1rem',\n                fontWeight: 'bold',\n                lineHeight: 1.2,\n              }}>\n                {title || 'Hero Title'}\n              </h1>\n              <p style={{\n                fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n                opacity: 0.9,\n                lineHeight: 1.6,\n                marginBottom: '2rem',\n              }}>\n                {subtitle || 'Hero subtitle text'}\n              </p>\n            </div>\n          </section>\n        );\n      },\n    },\n\n    // Text Component\n    Text: {\n      label: 'Text Block',\n      defaultProps: {\n        text: '<p>Add your text content here...</p>',\n        textAlign: 'left',\n        maxWidth: '100%',\n        fontSize: '16px',\n        lineHeight: '1.6',\n        color: '#333333',\n        padding: '1rem',\n      },\n      fields: {\n        text: { type: 'textarea' },\n        textAlign: {\n          type: 'select',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n            { label: 'Justify', value: 'justify' },\n          ],\n        },\n        maxWidth: { type: 'text' },\n        fontSize: { type: 'text' },\n        lineHeight: { type: 'text' },\n        color: { type: 'text' },\n        padding: { type: 'text' },\n      },\n      render: ({ text, textAlign, maxWidth, fontSize, lineHeight, color, padding }: any) => (\n        <div style={{ padding: padding || '1rem' }}>\n          <div\n            style={{\n              maxWidth: maxWidth || '100%',\n              margin: textAlign === 'center' ? '0 auto' : '0',\n              textAlign: textAlign || 'left',\n              fontSize: fontSize || '16px',\n              lineHeight: lineHeight || '1.6',\n              color: color || '#333333',\n            }}\n            dangerouslySetInnerHTML={{ __html: text || '<p>Add your text content here...</p>' }}\n          />\n        </div>\n      ),\n    },\n  },\n};\n\ninterface PageData {\n  id: number;\n  title: string;\n  slug: string;\n  content: any;\n  meta_title?: string;\n  meta_description?: string;\n  status: string;\n  published_at: string;\n}\n\ninterface StaticPageProps {\n  slug?: string;\n}\n\nconst StaticPage: React.FC<StaticPageProps> = ({ slug: propSlug }) => {\n  const { slug: paramSlug } = useParams<{ slug: string }>();\n  const slug = propSlug || paramSlug;\n  const siteName = useSiteName();\n  \n  const [pageData, setPageData] = useState<PageData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    if (!slug) {\n      setError('Page slug is required');\n      setLoading(false);\n      return;\n    }\n\n    const fetchPageData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(`${process.env.REACT_APP_API_URL}/pages/${slug}`);\n        \n        if (!response.ok) {\n          if (response.status === 404) {\n            setError('Page not found');\n          } else {\n            setError('Failed to load page');\n          }\n          return;\n        }\n\n        const result = await response.json();\n        if (result.success) {\n          setPageData(result.data);\n        } else {\n          setError(result.message || 'Failed to load page');\n        }\n      } catch (err) {\n        console.error('Error fetching page:', err);\n        setError('Failed to load page');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchPageData();\n  }, [slug]);\n\n  if (loading) {\n    return (\n      <Container className=\"py-5 text-center\">\n        <Spinner animation=\"border\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </Spinner>\n        <p className=\"mt-3\">Loading page...</p>\n      </Container>\n    );\n  }\n\n  if (error || !pageData) {\n    return (\n      <Container className=\"py-5\">\n        <Alert variant=\"danger\">\n          <Alert.Heading>Error</Alert.Heading>\n          <p>{error || 'Page not found'}</p>\n        </Alert>\n      </Container>\n    );\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>{pageData.meta_title || pageData.title} - {siteName}</title>\n        {pageData.meta_description && (\n          <meta name=\"description\" content={pageData.meta_description} />\n        )}\n      </Helmet>\n\n      <div className=\"puck-renderer\">\n        {pageData.content && pageData.content.content ? (\n          <Render config={puckConfig} data={pageData.content} />\n        ) : (\n          <Container className=\"py-5\">\n            <h1>{pageData.title}</h1>\n            <p>This page is currently being set up. Please check back later.</p>\n          </Container>\n        )}\n      </div>\n    </>\n  );\n};\n\nexport default StaticPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AAC3D,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAO,0BAA0B;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,UAAU,GAAG;EACjBC,UAAU,EAAE;IACV;IACAC,IAAI,EAAE;MACJC,KAAK,EAAE,cAAc;MACrBC,YAAY,EAAE;QACZC,KAAK,EAAE,YAAY;QACnBC,QAAQ,EAAE,oBAAoB;QAC9BC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,OAAO;QAClBC,cAAc,EAAE,UAAU;QAC1BC,eAAe,EAAE,SAAS;QAC1BC,kBAAkB,EAAE,mDAAmD;QACvEC,SAAS,EAAE;MACb,CAAC;MACDC,MAAM,EAAE;QACNR,KAAK,EAAE;UAAES,IAAI,EAAE;QAAO,CAAC;QACvBR,QAAQ,EAAE;UAAEQ,IAAI,EAAE;QAAW,CAAC;QAC9BP,SAAS,EAAE;UACTO,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAEtC,CAAC;QACDR,SAAS,EAAE;UAAEM,IAAI,EAAE;QAAO,CAAC;QAC3BL,cAAc,EAAE;UACdK,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,aAAa;YAAEa,KAAK,EAAE;UAAQ,CAAC,EACxC;YAAEb,KAAK,EAAE,UAAU;YAAEa,KAAK,EAAE;UAAW,CAAC,EACxC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAEtC,CAAC;QACDN,eAAe,EAAE;UAAEI,IAAI,EAAE;QAAO,CAAC;QACjCH,kBAAkB,EAAE;UAAEG,IAAI,EAAE;QAAO,CAAC;QACpCG,eAAe,EAAE;UAAEH,IAAI,EAAE;QAAO,CAAC;QACjCF,SAAS,EAAE;UAAEE,IAAI,EAAE;QAAO;MAC5B,CAAC;MACDI,MAAM,EAAEA,CAAC;QAAEb,KAAK;QAAEC,QAAQ;QAAEC,SAAS;QAAEC,SAAS;QAAEC,cAAc;QAAEC,eAAe;QAAEC,kBAAkB;QAAEM,eAAe;QAAEL;MAAe,CAAC,KAAK;QAC3I,IAAIO,eAAoC,GAAG,CAAC,CAAC;QAE7C,QAAQV,cAAc;UACpB,KAAK,OAAO;YACVU,eAAe,CAACT,eAAe,GAAGA,eAAe,IAAI,SAAS;YAC9D;UACF,KAAK,UAAU;YACbS,eAAe,CAACC,UAAU,GAAGT,kBAAkB,IAAI,mDAAmD;YACtG;UACF,KAAK,OAAO;YACV,IAAIM,eAAe,EAAE;cACnBE,eAAe,CAACF,eAAe,GAAG,OAAOA,eAAe,GAAG;cAC3DE,eAAe,CAACE,cAAc,GAAG,OAAO;cACxCF,eAAe,CAACG,kBAAkB,GAAG,QAAQ;YAC/C;YACA;QACJ;QAEA,oBACEzB,OAAA;UACE0B,KAAK,EAAE;YACLf,SAAS,EAAEA,SAAS,IAAI,OAAO;YAC/BgB,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,cAAc,EAAE,QAAQ;YACxBC,UAAU,EAAEpB,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAGA,SAAS,KAAK,OAAO,GAAG,UAAU,GAAG,YAAY;YACjGA,SAAS,EAAEA,SAAS,IAAI,QAAQ;YAChCqB,OAAO,EAAE,WAAW;YACpBC,KAAK,EAAEjB,SAAS,IAAI,OAAO;YAC3BkB,QAAQ,EAAE,UAAU;YACpB,GAAGX;UACL,CAAE;UAAAY,QAAA,eAEFlC,OAAA;YAAK0B,KAAK,EAAE;cAAEO,QAAQ,EAAE,UAAU;cAAEE,MAAM,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAQ,CAAE;YAAAF,QAAA,gBACjElC,OAAA;cAAI0B,KAAK,EAAE;gBACTW,QAAQ,EAAE,0BAA0B;gBACpCC,YAAY,EAAE,MAAM;gBACpBC,UAAU,EAAE,MAAM;gBAClBC,UAAU,EAAE;cACd,CAAE;cAAAN,QAAA,EACC1B,KAAK,IAAI;YAAY;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACL5C,OAAA;cAAG0B,KAAK,EAAE;gBACRW,QAAQ,EAAE,6BAA6B;gBACvCQ,OAAO,EAAE,GAAG;gBACZL,UAAU,EAAE,GAAG;gBACfF,YAAY,EAAE;cAChB,CAAE;cAAAJ,QAAA,EACCzB,QAAQ,IAAI;YAAoB;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAEd;IACF,CAAC;IAED;IACAE,IAAI,EAAE;MACJxC,KAAK,EAAE,YAAY;MACnBC,YAAY,EAAE;QACZwC,IAAI,EAAE,sCAAsC;QAC5CrC,SAAS,EAAE,MAAM;QACjB0B,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,MAAM;QAChBG,UAAU,EAAE,KAAK;QACjBR,KAAK,EAAE,SAAS;QAChBD,OAAO,EAAE;MACX,CAAC;MACDf,MAAM,EAAE;QACN+B,IAAI,EAAE;UAAE9B,IAAI,EAAE;QAAW,CAAC;QAC1BP,SAAS,EAAE;UACTO,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAEb,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC;QAE1C,CAAC;QACDiB,QAAQ,EAAE;UAAEnB,IAAI,EAAE;QAAO,CAAC;QAC1BoB,QAAQ,EAAE;UAAEpB,IAAI,EAAE;QAAO,CAAC;QAC1BuB,UAAU,EAAE;UAAEvB,IAAI,EAAE;QAAO,CAAC;QAC5Be,KAAK,EAAE;UAAEf,IAAI,EAAE;QAAO,CAAC;QACvBc,OAAO,EAAE;UAAEd,IAAI,EAAE;QAAO;MAC1B,CAAC;MACDI,MAAM,EAAEA,CAAC;QAAE0B,IAAI;QAAErC,SAAS;QAAE0B,QAAQ;QAAEC,QAAQ;QAAEG,UAAU;QAAER,KAAK;QAAED;MAAa,CAAC,kBAC/E/B,OAAA;QAAK0B,KAAK,EAAE;UAAEK,OAAO,EAAEA,OAAO,IAAI;QAAO,CAAE;QAAAG,QAAA,eACzClC,OAAA;UACE0B,KAAK,EAAE;YACLU,QAAQ,EAAEA,QAAQ,IAAI,MAAM;YAC5BY,MAAM,EAAEtC,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,GAAG;YAC/CA,SAAS,EAAEA,SAAS,IAAI,MAAM;YAC9B2B,QAAQ,EAAEA,QAAQ,IAAI,MAAM;YAC5BG,UAAU,EAAEA,UAAU,IAAI,KAAK;YAC/BR,KAAK,EAAEA,KAAK,IAAI;UAClB,CAAE;UACFiB,uBAAuB,EAAE;YAAEC,MAAM,EAAEH,IAAI,IAAI;UAAuC;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAET;EACF;AACF,CAAC;AAiBD,MAAMO,UAAqC,GAAGA,CAAC;EAAEC,IAAI,EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM;IAAEF,IAAI,EAAEG;EAAU,CAAC,GAAG/D,SAAS,CAAmB,CAAC;EACzD,MAAM4D,IAAI,GAAGC,QAAQ,IAAIE,SAAS;EAClC,MAAMC,QAAQ,GAAG1D,WAAW,CAAC,CAAC;EAE9B,MAAM,CAAC2D,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAkB,IAAI,CAAC;EAC/D,MAAM,CAACqE,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuE,KAAK,EAAEC,QAAQ,CAAC,GAAGxE,QAAQ,CAAS,EAAE,CAAC;EAE9CC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6D,IAAI,EAAE;MACTU,QAAQ,CAAC,uBAAuB,CAAC;MACjCF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,UAAUhB,IAAI,EAAE,CAAC;QAE9E,IAAI,CAACY,QAAQ,CAACK,EAAE,EAAE;UAChB,IAAIL,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;YAC3BR,QAAQ,CAAC,gBAAgB,CAAC;UAC5B,CAAC,MAAM;YACLA,QAAQ,CAAC,qBAAqB,CAAC;UACjC;UACA;QACF;QAEA,MAAMS,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACpC,IAAID,MAAM,CAACE,OAAO,EAAE;UAClBf,WAAW,CAACa,MAAM,CAACG,IAAI,CAAC;QAC1B,CAAC,MAAM;UACLZ,QAAQ,CAACS,MAAM,CAACI,OAAO,IAAI,qBAAqB,CAAC;QACnD;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAAChB,KAAK,CAAC,sBAAsB,EAAEe,GAAG,CAAC;QAC1Cd,QAAQ,CAAC,qBAAqB,CAAC;MACjC,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACX,IAAI,CAAC,CAAC;EAEV,IAAIO,OAAO,EAAE;IACX,oBACE3D,OAAA,CAACP,SAAS;MAACqF,SAAS,EAAC,kBAAkB;MAAA5C,QAAA,gBACrClC,OAAA,CAACL,OAAO;QAACoF,SAAS,EAAC,QAAQ;QAACC,IAAI,EAAC,QAAQ;QAAA9C,QAAA,eACvClC,OAAA;UAAM8E,SAAS,EAAC,iBAAiB;UAAA5C,QAAA,EAAC;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACV5C,OAAA;QAAG8E,SAAS,EAAC,MAAM;QAAA5C,QAAA,EAAC;MAAe;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEhB;EAEA,IAAIiB,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEzD,OAAA,CAACP,SAAS;MAACqF,SAAS,EAAC,MAAM;MAAA5C,QAAA,eACzBlC,OAAA,CAACN,KAAK;QAACuF,OAAO,EAAC,QAAQ;QAAA/C,QAAA,gBACrBlC,OAAA,CAACN,KAAK,CAACwF,OAAO;UAAAhD,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eACpC5C,OAAA;UAAAkC,QAAA,EAAI2B,KAAK,IAAI;QAAgB;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACE5C,OAAA,CAAAE,SAAA;IAAAgC,QAAA,gBACElC,OAAA,CAACJ,MAAM;MAAAsC,QAAA,gBACLlC,OAAA;QAAAkC,QAAA,GAAQuB,QAAQ,CAAC0B,UAAU,IAAI1B,QAAQ,CAACjD,KAAK,EAAC,KAAG,EAACgD,QAAQ;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAClEa,QAAQ,CAAC2B,gBAAgB,iBACxBpF,OAAA;QAAMqF,IAAI,EAAC,aAAa;QAACC,OAAO,EAAE7B,QAAQ,CAAC2B;MAAiB;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC/D;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAET5C,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAAA5C,QAAA,EAC3BuB,QAAQ,CAAC6B,OAAO,IAAI7B,QAAQ,CAAC6B,OAAO,CAACA,OAAO,gBAC3CtF,OAAA,CAACH,MAAM;QAAC0F,MAAM,EAAEpF,UAAW;QAACuE,IAAI,EAAEjB,QAAQ,CAAC6B;MAAQ;QAAA7C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEtD5C,OAAA,CAACP,SAAS;QAACqF,SAAS,EAAC,MAAM;QAAA5C,QAAA,gBACzBlC,OAAA;UAAAkC,QAAA,EAAKuB,QAAQ,CAACjD;QAAK;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzB5C,OAAA;UAAAkC,QAAA,EAAG;QAA6D;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IACZ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACU,EAAA,CA1FIH,UAAqC;EAAA,QACb3D,SAAS,EAEpBM,WAAW;AAAA;AAAA0F,EAAA,GAHxBrC,UAAqC;AA4F3C,eAAeA,UAAU;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}