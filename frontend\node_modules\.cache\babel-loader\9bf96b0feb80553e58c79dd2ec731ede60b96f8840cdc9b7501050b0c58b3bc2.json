{"ast": null, "code": "import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n// It should to be noted that this function isn't equivalent to `text-transform: capitalize`.\n//\n// A strict capitalization should uppercase the first letter of each word in the sentence.\n// We only handle the first word.\nexport default function capitalize(string) {\n  if (typeof string !== 'string') {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `capitalize(string)` expects a string argument.' : _formatErrorMessage(7));\n  }\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}", "map": {"version": 3, "names": ["_formatErrorMessage", "capitalize", "string", "Error", "process", "env", "NODE_ENV", "char<PERSON>t", "toUpperCase", "slice"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/utils/esm/capitalize/capitalize.js"], "sourcesContent": ["import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n// It should to be noted that this function isn't equivalent to `text-transform: capitalize`.\n//\n// A strict capitalization should uppercase the first letter of each word in the sentence.\n// We only handle the first word.\nexport default function capitalize(string) {\n  if (typeof string !== 'string') {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `capitalize(string)` expects a string argument.' : _formatErrorMessage(7));\n  }\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,kCAAkC;AAClE;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,MAAM,EAAE;EACzC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,sDAAsD,GAAGN,mBAAmB,CAAC,CAAC,CAAC,CAAC;EAC1I;EACA,OAAOE,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}