{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12.03 17.5c-.02.17-.03.33-.03.5H6l-4 4V2h20v8.68c-.91-.43-1.92-.68-3-.68-3.87 0-7 3.13-7 7 0 .17.01.33.03.5M23 14.34l-1.41-1.41-4.24 4.24-2.12-2.12-1.41 1.41L17.34 20z\"\n}), 'MarkChatReadSharp');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/icons-material/esm/MarkChatReadSharp.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12.03 17.5c-.02.17-.03.33-.03.5H6l-4 4V2h20v8.68c-.91-.43-1.92-.68-3-.68-3.87 0-7 3.13-7 7 0 .17.01.33.03.5M23 14.34l-1.41-1.41-4.24 4.24-2.12-2.12-1.41 1.41L17.34 20z\"\n}), 'MarkChatReadSharp');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}