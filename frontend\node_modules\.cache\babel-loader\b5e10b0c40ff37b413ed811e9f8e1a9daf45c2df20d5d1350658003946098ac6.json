{"ast": null, "code": "// Datta Able Theme Configuration\n// This replaces the Material-UI theme with Bootstrap-based styling\n\n// Color palette based on Datta Able design\nexport const colors = {\n  primary: {\n    main: '#1976d2',\n    light: '#42a5f5',\n    dark: '#1565c0',\n    contrastText: '#ffffff'\n  },\n  secondary: {\n    main: '#dc004e',\n    light: '#ff5983',\n    dark: '#9a0036',\n    contrastText: '#ffffff'\n  },\n  success: {\n    main: '#4caf50',\n    light: '#81c784',\n    dark: '#388e3c',\n    contrastText: '#ffffff'\n  },\n  warning: {\n    main: '#ff9800',\n    light: '#ffb74d',\n    dark: '#f57c00',\n    contrastText: '#ffffff'\n  },\n  error: {\n    main: '#f44336',\n    light: '#e57373',\n    dark: '#d32f2f',\n    contrastText: '#ffffff'\n  },\n  info: {\n    main: '#2196f3',\n    light: '#64b5f6',\n    dark: '#1976d2',\n    contrastText: '#ffffff'\n  },\n  background: {\n    default: '#2c3e50',\n    paper: '#34495e',\n    light: '#3a4a5c'\n  },\n  text: {\n    primary: '#ffffff',\n    secondary: '#ecf0f1',\n    disabled: '#95a5a6'\n  },\n  divider: '#4a5568',\n  border: '#4a5568'\n};\n\n// Typography configuration\nexport const typography = {\n  fontFamily: '\"Open Sans\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n  fontSize: {\n    xs: '0.75rem',\n    // 12px\n    sm: '0.875rem',\n    // 14px\n    base: '1rem',\n    // 16px\n    lg: '1.125rem',\n    // 18px\n    xl: '1.25rem',\n    // 20px\n    '2xl': '1.5rem',\n    // 24px\n    '3xl': '1.875rem',\n    // 30px\n    '4xl': '2.25rem',\n    // 36px\n    '5xl': '3rem' // 48px\n  },\n  fontWeight: {\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700\n  },\n  lineHeight: {\n    tight: 1.25,\n    normal: 1.5,\n    relaxed: 1.75\n  }\n};\n\n// Spacing configuration (based on Bootstrap's spacing scale)\nexport const spacing = {\n  0: '0',\n  1: '0.25rem',\n  // 4px\n  2: '0.5rem',\n  // 8px\n  3: '0.75rem',\n  // 12px\n  4: '1rem',\n  // 16px\n  5: '1.25rem',\n  // 20px\n  6: '1.5rem',\n  // 24px\n  8: '2rem',\n  // 32px\n  10: '2.5rem',\n  // 40px\n  12: '3rem',\n  // 48px\n  16: '4rem',\n  // 64px\n  20: '5rem',\n  // 80px\n  24: '6rem' // 96px\n};\n\n// Border radius configuration\nexport const borderRadius = {\n  none: '0',\n  sm: '0.125rem',\n  // 2px\n  base: '0.25rem',\n  // 4px\n  md: '0.375rem',\n  // 6px\n  lg: '0.5rem',\n  // 8px\n  xl: '0.75rem',\n  // 12px\n  '2xl': '1rem',\n  // 16px\n  '3xl': '1.5rem',\n  // 24px\n  full: '9999px'\n};\n\n// Shadow configuration\nexport const shadows = {\n  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)'\n};\n\n// Breakpoints (Bootstrap breakpoints)\nexport const breakpoints = {\n  xs: '0px',\n  sm: '576px',\n  md: '768px',\n  lg: '992px',\n  xl: '1200px',\n  xxl: '1400px'\n};\n\n// Layout configuration\nexport const layout = {\n  sidebar: {\n    width: '260px',\n    collapsedWidth: '70px'\n  },\n  header: {\n    height: '70px'\n  },\n  footer: {\n    height: '60px'\n  }\n};\n\n// Component styles\nexport const components = {\n  card: {\n    borderRadius: borderRadius.lg,\n    boxShadow: shadows.sm,\n    border: `1px solid ${colors.border}`\n  },\n  button: {\n    borderRadius: borderRadius.md,\n    fontWeight: typography.fontWeight.medium,\n    padding: {\n      sm: `${spacing[2]} ${spacing[3]}`,\n      md: `${spacing[3]} ${spacing[4]}`,\n      lg: `${spacing[4]} ${spacing[6]}`\n    }\n  },\n  input: {\n    borderRadius: borderRadius.md,\n    border: `1px solid ${colors.border}`,\n    padding: `${spacing[3]} ${spacing[4]}`\n  },\n  modal: {\n    borderRadius: borderRadius.lg,\n    boxShadow: shadows.xl\n  }\n};\n\n// Utility functions\nexport const getColor = colorPath => {\n  const keys = colorPath.split('.');\n  let result = colors;\n  for (const key of keys) {\n    result = result[key];\n    if (!result) return colors.primary.main; // fallback\n  }\n  return result;\n};\nexport const getSpacing = value => {\n  if (typeof value === 'number') {\n    return spacing[value] || `${value * 0.25}rem`;\n  }\n  return spacing[value] || value;\n};\n\n// CSS custom properties for dynamic theming\nexport const cssVariables = {\n  '--color-primary': colors.primary.main,\n  '--color-primary-light': colors.primary.light,\n  '--color-primary-dark': colors.primary.dark,\n  '--color-secondary': colors.secondary.main,\n  '--color-success': colors.success.main,\n  '--color-warning': colors.warning.main,\n  '--color-error': colors.error.main,\n  '--color-info': colors.info.main,\n  '--color-background': colors.background.default,\n  '--color-paper': colors.background.paper,\n  '--color-text-primary': colors.text.primary,\n  '--color-text-secondary': colors.text.secondary,\n  '--color-border': colors.border,\n  '--font-family': typography.fontFamily,\n  '--sidebar-width': layout.sidebar.width,\n  '--header-height': layout.header.height,\n  '--border-radius': borderRadius.md,\n  '--shadow-sm': shadows.sm,\n  '--shadow-md': shadows.md,\n  '--shadow-lg': shadows.lg\n};\n\n// Export default theme object\nconst dattaAbleTheme = {\n  colors,\n  typography,\n  spacing,\n  borderRadius,\n  shadows,\n  breakpoints,\n  layout,\n  components,\n  cssVariables,\n  getColor,\n  getSpacing\n};\nexport default dattaAbleTheme;", "map": {"version": 3, "names": ["colors", "primary", "main", "light", "dark", "contrastText", "secondary", "success", "warning", "error", "info", "background", "default", "paper", "text", "disabled", "divider", "border", "typography", "fontFamily", "fontSize", "xs", "sm", "base", "lg", "xl", "fontWeight", "normal", "medium", "semibold", "bold", "lineHeight", "tight", "relaxed", "spacing", "borderRadius", "none", "md", "full", "shadows", "inner", "breakpoints", "xxl", "layout", "sidebar", "width", "collapsedWidth", "header", "height", "footer", "components", "card", "boxShadow", "button", "padding", "input", "modal", "getColor", "colorPath", "keys", "split", "result", "key", "getSpacing", "value", "cssVariables", "dattaAbleTheme"], "sources": ["C:/laragon/www/frontend/src/theme/dattaAbleTheme.js"], "sourcesContent": ["// Datta Able Theme Configuration\n// This replaces the Material-UI theme with Bootstrap-based styling\n\n// Color palette based on Datta Able design\nexport const colors = {\n  primary: {\n    main: '#1976d2',\n    light: '#42a5f5',\n    dark: '#1565c0',\n    contrastText: '#ffffff',\n  },\n  secondary: {\n    main: '#dc004e',\n    light: '#ff5983',\n    dark: '#9a0036',\n    contrastText: '#ffffff',\n  },\n  success: {\n    main: '#4caf50',\n    light: '#81c784',\n    dark: '#388e3c',\n    contrastText: '#ffffff',\n  },\n  warning: {\n    main: '#ff9800',\n    light: '#ffb74d',\n    dark: '#f57c00',\n    contrastText: '#ffffff',\n  },\n  error: {\n    main: '#f44336',\n    light: '#e57373',\n    dark: '#d32f2f',\n    contrastText: '#ffffff',\n  },\n  info: {\n    main: '#2196f3',\n    light: '#64b5f6',\n    dark: '#1976d2',\n    contrastText: '#ffffff',\n  },\n  background: {\n    default: '#2c3e50',\n    paper: '#34495e',\n    light: '#3a4a5c',\n  },\n  text: {\n    primary: '#ffffff',\n    secondary: '#ecf0f1',\n    disabled: '#95a5a6',\n  },\n  divider: '#4a5568',\n  border: '#4a5568',\n};\n\n// Typography configuration\nexport const typography = {\n  fontFamily: '\"Open Sans\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n  fontSize: {\n    xs: '0.75rem',    // 12px\n    sm: '0.875rem',   // 14px\n    base: '1rem',     // 16px\n    lg: '1.125rem',   // 18px\n    xl: '1.25rem',    // 20px\n    '2xl': '1.5rem',  // 24px\n    '3xl': '1.875rem', // 30px\n    '4xl': '2.25rem', // 36px\n    '5xl': '3rem',    // 48px\n  },\n  fontWeight: {\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700,\n  },\n  lineHeight: {\n    tight: 1.25,\n    normal: 1.5,\n    relaxed: 1.75,\n  },\n};\n\n// Spacing configuration (based on Bootstrap's spacing scale)\nexport const spacing = {\n  0: '0',\n  1: '0.25rem',  // 4px\n  2: '0.5rem',   // 8px\n  3: '0.75rem',  // 12px\n  4: '1rem',     // 16px\n  5: '1.25rem',  // 20px\n  6: '1.5rem',   // 24px\n  8: '2rem',     // 32px\n  10: '2.5rem',  // 40px\n  12: '3rem',    // 48px\n  16: '4rem',    // 64px\n  20: '5rem',    // 80px\n  24: '6rem',    // 96px\n};\n\n// Border radius configuration\nexport const borderRadius = {\n  none: '0',\n  sm: '0.125rem',   // 2px\n  base: '0.25rem',  // 4px\n  md: '0.375rem',   // 6px\n  lg: '0.5rem',     // 8px\n  xl: '0.75rem',    // 12px\n  '2xl': '1rem',    // 16px\n  '3xl': '1.5rem',  // 24px\n  full: '9999px',\n};\n\n// Shadow configuration\nexport const shadows = {\n  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',\n};\n\n// Breakpoints (Bootstrap breakpoints)\nexport const breakpoints = {\n  xs: '0px',\n  sm: '576px',\n  md: '768px',\n  lg: '992px',\n  xl: '1200px',\n  xxl: '1400px',\n};\n\n// Layout configuration\nexport const layout = {\n  sidebar: {\n    width: '260px',\n    collapsedWidth: '70px',\n  },\n  header: {\n    height: '70px',\n  },\n  footer: {\n    height: '60px',\n  },\n};\n\n// Component styles\nexport const components = {\n  card: {\n    borderRadius: borderRadius.lg,\n    boxShadow: shadows.sm,\n    border: `1px solid ${colors.border}`,\n  },\n  button: {\n    borderRadius: borderRadius.md,\n    fontWeight: typography.fontWeight.medium,\n    padding: {\n      sm: `${spacing[2]} ${spacing[3]}`,\n      md: `${spacing[3]} ${spacing[4]}`,\n      lg: `${spacing[4]} ${spacing[6]}`,\n    },\n  },\n  input: {\n    borderRadius: borderRadius.md,\n    border: `1px solid ${colors.border}`,\n    padding: `${spacing[3]} ${spacing[4]}`,\n  },\n  modal: {\n    borderRadius: borderRadius.lg,\n    boxShadow: shadows.xl,\n  },\n};\n\n// Utility functions\nexport const getColor = (colorPath) => {\n  const keys = colorPath.split('.');\n  let result = colors;\n  for (const key of keys) {\n    result = result[key];\n    if (!result) return colors.primary.main; // fallback\n  }\n  return result;\n};\n\nexport const getSpacing = (value) => {\n  if (typeof value === 'number') {\n    return spacing[value] || `${value * 0.25}rem`;\n  }\n  return spacing[value] || value;\n};\n\n// CSS custom properties for dynamic theming\nexport const cssVariables = {\n  '--color-primary': colors.primary.main,\n  '--color-primary-light': colors.primary.light,\n  '--color-primary-dark': colors.primary.dark,\n  '--color-secondary': colors.secondary.main,\n  '--color-success': colors.success.main,\n  '--color-warning': colors.warning.main,\n  '--color-error': colors.error.main,\n  '--color-info': colors.info.main,\n  '--color-background': colors.background.default,\n  '--color-paper': colors.background.paper,\n  '--color-text-primary': colors.text.primary,\n  '--color-text-secondary': colors.text.secondary,\n  '--color-border': colors.border,\n  '--font-family': typography.fontFamily,\n  '--sidebar-width': layout.sidebar.width,\n  '--header-height': layout.header.height,\n  '--border-radius': borderRadius.md,\n  '--shadow-sm': shadows.sm,\n  '--shadow-md': shadows.md,\n  '--shadow-lg': shadows.lg,\n};\n\n// Export default theme object\nconst dattaAbleTheme = {\n  colors,\n  typography,\n  spacing,\n  borderRadius,\n  shadows,\n  breakpoints,\n  layout,\n  components,\n  cssVariables,\n  getColor,\n  getSpacing,\n};\n\nexport default dattaAbleTheme;\n"], "mappings": "AAAA;AACA;;AAEA;AACA,OAAO,MAAMA,MAAM,GAAG;EACpBC,OAAO,EAAE;IACPC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,YAAY,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE;IACTJ,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,YAAY,EAAE;EAChB,CAAC;EACDE,OAAO,EAAE;IACPL,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,YAAY,EAAE;EAChB,CAAC;EACDG,OAAO,EAAE;IACPN,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,YAAY,EAAE;EAChB,CAAC;EACDI,KAAK,EAAE;IACLP,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,YAAY,EAAE;EAChB,CAAC;EACDK,IAAI,EAAE;IACJR,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,YAAY,EAAE;EAChB,CAAC;EACDM,UAAU,EAAE;IACVC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBV,KAAK,EAAE;EACT,CAAC;EACDW,IAAI,EAAE;IACJb,OAAO,EAAE,SAAS;IAClBK,SAAS,EAAE,SAAS;IACpBS,QAAQ,EAAE;EACZ,CAAC;EACDC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,UAAU,EAAE,yGAAyG;EACrHC,QAAQ,EAAE;IACRC,EAAE,EAAE,SAAS;IAAK;IAClBC,EAAE,EAAE,UAAU;IAAI;IAClBC,IAAI,EAAE,MAAM;IAAM;IAClBC,EAAE,EAAE,UAAU;IAAI;IAClBC,EAAE,EAAE,SAAS;IAAK;IAClB,KAAK,EAAE,QAAQ;IAAG;IAClB,KAAK,EAAE,UAAU;IAAE;IACnB,KAAK,EAAE,SAAS;IAAE;IAClB,KAAK,EAAE,MAAM,CAAK;EACpB,CAAC;EACDC,UAAU,EAAE;IACVvB,KAAK,EAAE,GAAG;IACVwB,MAAM,EAAE,GAAG;IACXC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE;EACR,CAAC;EACDC,UAAU,EAAE;IACVC,KAAK,EAAE,IAAI;IACXL,MAAM,EAAE,GAAG;IACXM,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,OAAO,GAAG;EACrB,CAAC,EAAE,GAAG;EACN,CAAC,EAAE,SAAS;EAAG;EACf,CAAC,EAAE,QAAQ;EAAI;EACf,CAAC,EAAE,SAAS;EAAG;EACf,CAAC,EAAE,MAAM;EAAM;EACf,CAAC,EAAE,SAAS;EAAG;EACf,CAAC,EAAE,QAAQ;EAAI;EACf,CAAC,EAAE,MAAM;EAAM;EACf,EAAE,EAAE,QAAQ;EAAG;EACf,EAAE,EAAE,MAAM;EAAK;EACf,EAAE,EAAE,MAAM;EAAK;EACf,EAAE,EAAE,MAAM;EAAK;EACf,EAAE,EAAE,MAAM,CAAK;AACjB,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,IAAI,EAAE,GAAG;EACTd,EAAE,EAAE,UAAU;EAAI;EAClBC,IAAI,EAAE,SAAS;EAAG;EAClBc,EAAE,EAAE,UAAU;EAAI;EAClBb,EAAE,EAAE,QAAQ;EAAM;EAClBC,EAAE,EAAE,SAAS;EAAK;EAClB,KAAK,EAAE,MAAM;EAAK;EAClB,KAAK,EAAE,QAAQ;EAAG;EAClBa,IAAI,EAAE;AACR,CAAC;;AAED;AACA,OAAO,MAAMC,OAAO,GAAG;EACrBjB,EAAE,EAAE,iCAAiC;EACrCC,IAAI,EAAE,iEAAiE;EACvEc,EAAE,EAAE,uEAAuE;EAC3Eb,EAAE,EAAE,yEAAyE;EAC7EC,EAAE,EAAE,2EAA2E;EAC/E,KAAK,EAAE,uCAAuC;EAC9Ce,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBpB,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,OAAO;EACXe,EAAE,EAAE,OAAO;EACXb,EAAE,EAAE,OAAO;EACXC,EAAE,EAAE,QAAQ;EACZiB,GAAG,EAAE;AACP,CAAC;;AAED;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,OAAO,EAAE;IACPC,KAAK,EAAE,OAAO;IACdC,cAAc,EAAE;EAClB,CAAC;EACDC,MAAM,EAAE;IACNC,MAAM,EAAE;EACV,CAAC;EACDC,MAAM,EAAE;IACND,MAAM,EAAE;EACV;AACF,CAAC;;AAED;AACA,OAAO,MAAME,UAAU,GAAG;EACxBC,IAAI,EAAE;IACJhB,YAAY,EAAEA,YAAY,CAACX,EAAE;IAC7B4B,SAAS,EAAEb,OAAO,CAACjB,EAAE;IACrBL,MAAM,EAAE,aAAajB,MAAM,CAACiB,MAAM;EACpC,CAAC;EACDoC,MAAM,EAAE;IACNlB,YAAY,EAAEA,YAAY,CAACE,EAAE;IAC7BX,UAAU,EAAER,UAAU,CAACQ,UAAU,CAACE,MAAM;IACxC0B,OAAO,EAAE;MACPhC,EAAE,EAAE,GAAGY,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE;MACjCG,EAAE,EAAE,GAAGH,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE;MACjCV,EAAE,EAAE,GAAGU,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC;IACjC;EACF,CAAC;EACDqB,KAAK,EAAE;IACLpB,YAAY,EAAEA,YAAY,CAACE,EAAE;IAC7BpB,MAAM,EAAE,aAAajB,MAAM,CAACiB,MAAM,EAAE;IACpCqC,OAAO,EAAE,GAAGpB,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC;EACtC,CAAC;EACDsB,KAAK,EAAE;IACLrB,YAAY,EAAEA,YAAY,CAACX,EAAE;IAC7B4B,SAAS,EAAEb,OAAO,CAACd;EACrB;AACF,CAAC;;AAED;AACA,OAAO,MAAMgC,QAAQ,GAAIC,SAAS,IAAK;EACrC,MAAMC,IAAI,GAAGD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC;EACjC,IAAIC,MAAM,GAAG7D,MAAM;EACnB,KAAK,MAAM8D,GAAG,IAAIH,IAAI,EAAE;IACtBE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC;IACpB,IAAI,CAACD,MAAM,EAAE,OAAO7D,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC;EAC3C;EACA,OAAO2D,MAAM;AACf,CAAC;AAED,OAAO,MAAME,UAAU,GAAIC,KAAK,IAAK;EACnC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO9B,OAAO,CAAC8B,KAAK,CAAC,IAAI,GAAGA,KAAK,GAAG,IAAI,KAAK;EAC/C;EACA,OAAO9B,OAAO,CAAC8B,KAAK,CAAC,IAAIA,KAAK;AAChC,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1B,iBAAiB,EAAEjE,MAAM,CAACC,OAAO,CAACC,IAAI;EACtC,uBAAuB,EAAEF,MAAM,CAACC,OAAO,CAACE,KAAK;EAC7C,sBAAsB,EAAEH,MAAM,CAACC,OAAO,CAACG,IAAI;EAC3C,mBAAmB,EAAEJ,MAAM,CAACM,SAAS,CAACJ,IAAI;EAC1C,iBAAiB,EAAEF,MAAM,CAACO,OAAO,CAACL,IAAI;EACtC,iBAAiB,EAAEF,MAAM,CAACQ,OAAO,CAACN,IAAI;EACtC,eAAe,EAAEF,MAAM,CAACS,KAAK,CAACP,IAAI;EAClC,cAAc,EAAEF,MAAM,CAACU,IAAI,CAACR,IAAI;EAChC,oBAAoB,EAAEF,MAAM,CAACW,UAAU,CAACC,OAAO;EAC/C,eAAe,EAAEZ,MAAM,CAACW,UAAU,CAACE,KAAK;EACxC,sBAAsB,EAAEb,MAAM,CAACc,IAAI,CAACb,OAAO;EAC3C,wBAAwB,EAAED,MAAM,CAACc,IAAI,CAACR,SAAS;EAC/C,gBAAgB,EAAEN,MAAM,CAACiB,MAAM;EAC/B,eAAe,EAAEC,UAAU,CAACC,UAAU;EACtC,iBAAiB,EAAEwB,MAAM,CAACC,OAAO,CAACC,KAAK;EACvC,iBAAiB,EAAEF,MAAM,CAACI,MAAM,CAACC,MAAM;EACvC,iBAAiB,EAAEb,YAAY,CAACE,EAAE;EAClC,aAAa,EAAEE,OAAO,CAACjB,EAAE;EACzB,aAAa,EAAEiB,OAAO,CAACF,EAAE;EACzB,aAAa,EAAEE,OAAO,CAACf;AACzB,CAAC;;AAED;AACA,MAAM0C,cAAc,GAAG;EACrBlE,MAAM;EACNkB,UAAU;EACVgB,OAAO;EACPC,YAAY;EACZI,OAAO;EACPE,WAAW;EACXE,MAAM;EACNO,UAAU;EACVe,YAAY;EACZR,QAAQ;EACRM;AACF,CAAC;AAED,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}