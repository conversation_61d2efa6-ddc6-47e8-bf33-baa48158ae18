{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\pages\\\\StaticPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';\nimport { Helmet } from 'react-helmet-async';\nimport { Render } from '@measured/puck';\nimport { useSiteName } from '../../contexts/SettingsContext';\nimport { puckConfig } from '../puck/puckConfig';\nimport authService from '../../services/authService';\nimport '../puck/PuckRenderer.css';\nimport './StaticPage.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StaticPage = ({\n  slug: propSlug\n}) => {\n  _s();\n  const {\n    slug: paramSlug\n  } = useParams();\n  const slug = propSlug || paramSlug;\n  const siteName = useSiteName();\n  const navigate = useNavigate();\n  const [pageData, setPageData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [adminCheckLoading, setAdminCheckLoading] = useState(true);\n\n  // Check admin status\n  useEffect(() => {\n    const checkAdminStatus = async () => {\n      try {\n        setAdminCheckLoading(true);\n        const adminStatus = await authService.checkAdminStatus();\n        setIsAdmin(adminStatus.is_admin);\n      } catch (error) {\n        console.error('Error checking admin status:', error);\n        setIsAdmin(false);\n      } finally {\n        setAdminCheckLoading(false);\n      }\n    };\n    checkAdminStatus();\n  }, []);\n  useEffect(() => {\n    if (!slug) {\n      setError('Page slug is required');\n      setLoading(false);\n      return;\n    }\n    const fetchPageData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(`${process.env.REACT_APP_API_URL}/pages/${slug}`);\n        if (!response.ok) {\n          if (response.status === 404) {\n            setError('Page not found');\n          } else {\n            setError('Failed to load page');\n          }\n          return;\n        }\n        const result = await response.json();\n        if (result.success) {\n          setPageData(result.data);\n        } else {\n          setError(result.message || 'Failed to load page');\n        }\n      } catch (err) {\n        console.error('Error fetching page:', err);\n        setError('Failed to load page');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPageData();\n  }, [slug]);\n\n  // Handle edit page button click\n  const handleEditPage = () => {\n    if (slug) {\n      navigate(`/visual-editor?page=${slug}`);\n    }\n  };\n\n  // Edit Page Button Component\n  const EditPageButton = () => {\n    // Don't show anything if not admin or still checking\n    if (!isAdmin || adminCheckLoading) {\n      return null;\n    }\n    return /*#__PURE__*/_jsxDEV(Button, {\n      onClick: handleEditPage,\n      className: \"edit-page-button\",\n      variant: \"primary\",\n      title: `Edit ${(pageData === null || pageData === void 0 ? void 0 : pageData.title) || 'this page'} in Visual Editor`,\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), \"Edit Page\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-3\",\n        children: \"Loading page...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !pageData) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error || 'Page not found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: [pageData.meta_title || pageData.title, \" - \", siteName]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), pageData.meta_description && /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: pageData.meta_description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditPageButton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"puck-renderer\",\n      children: pageData.content && pageData.content.content ? /*#__PURE__*/_jsxDEV(Render, {\n        config: puckConfig,\n        data: pageData.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Container, {\n        className: \"py-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: pageData.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"This page is currently being set up. Please check back later.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(StaticPage, \"FKlU1U8LsNm/wZRYElnwjYbUiCk=\", false, function () {\n  return [useParams, useSiteName, useNavigate];\n});\n_c = StaticPage;\nexport default StaticPage;\nvar _c;\n$RefreshReg$(_c, \"StaticPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Container", "<PERSON><PERSON>", "Spinner", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Render", "useSiteName", "puckConfig", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StaticPage", "slug", "propSlug", "_s", "paramSlug", "siteName", "navigate", "pageData", "setPageData", "loading", "setLoading", "error", "setError", "isAdmin", "setIsAdmin", "adminCheckLoading", "setAdminCheckLoading", "checkAdminStatus", "adminStatus", "is_admin", "console", "fetchPageData", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "status", "result", "json", "success", "data", "message", "err", "handleEditPage", "EditPageButton", "onClick", "className", "variant", "title", "children", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "animation", "role", "Heading", "meta_title", "meta_description", "name", "content", "config", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/pages/StaticPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Con<PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON> } from 'react-bootstrap';\nimport { Helmet } from 'react-helmet-async';\nimport { Render } from '@measured/puck';\nimport { useSiteName } from '../../contexts/SettingsContext';\nimport { puckConfig } from '../puck/puckConfig';\nimport authService from '../../services/authService';\nimport '../puck/PuckRenderer.css';\nimport './StaticPage.css';\n\ninterface PageData {\n  id: number;\n  title: string;\n  slug: string;\n  content: any;\n  meta_title?: string;\n  meta_description?: string;\n  status: string;\n  published_at: string;\n}\n\ninterface StaticPageProps {\n  slug?: string;\n}\n\nconst StaticPage: React.FC<StaticPageProps> = ({ slug: propSlug }) => {\n  const { slug: paramSlug } = useParams<{ slug: string }>();\n  const slug = propSlug || paramSlug;\n  const siteName = useSiteName();\n  const navigate = useNavigate();\n\n  const [pageData, setPageData] = useState<PageData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [adminCheckLoading, setAdminCheckLoading] = useState(true);\n\n  // Check admin status\n  useEffect(() => {\n    const checkAdminStatus = async () => {\n      try {\n        setAdminCheckLoading(true);\n        const adminStatus = await authService.checkAdminStatus();\n        setIsAdmin(adminStatus.is_admin);\n      } catch (error) {\n        console.error('Error checking admin status:', error);\n        setIsAdmin(false);\n      } finally {\n        setAdminCheckLoading(false);\n      }\n    };\n\n    checkAdminStatus();\n  }, []);\n\n  useEffect(() => {\n    if (!slug) {\n      setError('Page slug is required');\n      setLoading(false);\n      return;\n    }\n\n    const fetchPageData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(`${process.env.REACT_APP_API_URL}/pages/${slug}`);\n        \n        if (!response.ok) {\n          if (response.status === 404) {\n            setError('Page not found');\n          } else {\n            setError('Failed to load page');\n          }\n          return;\n        }\n\n        const result = await response.json();\n        if (result.success) {\n          setPageData(result.data);\n        } else {\n          setError(result.message || 'Failed to load page');\n        }\n      } catch (err) {\n        console.error('Error fetching page:', err);\n        setError('Failed to load page');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchPageData();\n  }, [slug]);\n\n  // Handle edit page button click\n  const handleEditPage = () => {\n    if (slug) {\n      navigate(`/visual-editor?page=${slug}`);\n    }\n  };\n\n  // Edit Page Button Component\n  const EditPageButton = () => {\n    // Don't show anything if not admin or still checking\n    if (!isAdmin || adminCheckLoading) {\n      return null;\n    }\n\n    return (\n      <Button\n        onClick={handleEditPage}\n        className=\"edit-page-button\"\n        variant=\"primary\"\n        title={`Edit ${pageData?.title || 'this page'} in Visual Editor`}\n      >\n        <svg\n          width=\"16\"\n          height=\"16\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        >\n          <path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\" />\n          <path d=\"m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z\" />\n        </svg>\n        Edit Page\n      </Button>\n    );\n  };\n\n  if (loading) {\n    return (\n      <Container className=\"py-5 text-center\">\n        <Spinner animation=\"border\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </Spinner>\n        <p className=\"mt-3\">Loading page...</p>\n      </Container>\n    );\n  }\n\n  if (error || !pageData) {\n    return (\n      <Container className=\"py-5\">\n        <Alert variant=\"danger\">\n          <Alert.Heading>Error</Alert.Heading>\n          <p>{error || 'Page not found'}</p>\n        </Alert>\n      </Container>\n    );\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>{pageData.meta_title || pageData.title} - {siteName}</title>\n        {pageData.meta_description && (\n          <meta name=\"description\" content={pageData.meta_description} />\n        )}\n      </Helmet>\n\n      {/* Admin Edit Button */}\n      <EditPageButton />\n\n      <div className=\"puck-renderer\">\n        {pageData.content && pageData.content.content ? (\n          <Render config={puckConfig} data={pageData.content} />\n        ) : (\n          <Container className=\"py-5\">\n            <h1>{pageData.title}</h1>\n            <p>This page is currently being set up. Please check back later.</p>\n          </Container>\n        )}\n      </div>\n    </>\n  );\n};\n\nexport default StaticPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,QAAQ,iBAAiB;AACnE,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAO,0BAA0B;AACjC,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAiB1B,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,IAAI,EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM;IAAEF,IAAI,EAAEG;EAAU,CAAC,GAAGnB,SAAS,CAAmB,CAAC;EACzD,MAAMgB,IAAI,GAAGC,QAAQ,IAAIE,SAAS;EAClC,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAkB,IAAI,CAAC;EAC/D,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMiC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFD,oBAAoB,CAAC,IAAI,CAAC;QAC1B,MAAME,WAAW,GAAG,MAAMvB,WAAW,CAACsB,gBAAgB,CAAC,CAAC;QACxDH,UAAU,CAACI,WAAW,CAACC,QAAQ,CAAC;MAClC,CAAC,CAAC,OAAOR,KAAK,EAAE;QACdS,OAAO,CAACT,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDG,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,SAAS;QACRE,oBAAoB,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC;IAEDC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAENjC,SAAS,CAAC,MAAM;IACd,IAAI,CAACiB,IAAI,EAAE;MACTW,QAAQ,CAAC,uBAAuB,CAAC;MACjCF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,MAAMW,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFX,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMY,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,UAAUzB,IAAI,EAAE,CAAC;QAE9E,IAAI,CAACqB,QAAQ,CAACK,EAAE,EAAE;UAChB,IAAIL,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;YAC3BhB,QAAQ,CAAC,gBAAgB,CAAC;UAC5B,CAAC,MAAM;YACLA,QAAQ,CAAC,qBAAqB,CAAC;UACjC;UACA;QACF;QAEA,MAAMiB,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACpC,IAAID,MAAM,CAACE,OAAO,EAAE;UAClBvB,WAAW,CAACqB,MAAM,CAACG,IAAI,CAAC;QAC1B,CAAC,MAAM;UACLpB,QAAQ,CAACiB,MAAM,CAACI,OAAO,IAAI,qBAAqB,CAAC;QACnD;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZd,OAAO,CAACT,KAAK,CAAC,sBAAsB,EAAEuB,GAAG,CAAC;QAC1CtB,QAAQ,CAAC,qBAAqB,CAAC;MACjC,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDW,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACpB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMkC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIlC,IAAI,EAAE;MACRK,QAAQ,CAAC,uBAAuBL,IAAI,EAAE,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMmC,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,IAAI,CAACvB,OAAO,IAAIE,iBAAiB,EAAE;MACjC,OAAO,IAAI;IACb;IAEA,oBACElB,OAAA,CAACP,MAAM;MACL+C,OAAO,EAAEF,cAAe;MACxBG,SAAS,EAAC,kBAAkB;MAC5BC,OAAO,EAAC,SAAS;MACjBC,KAAK,EAAE,QAAQ,CAAAjC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiC,KAAK,KAAI,WAAW,mBAAoB;MAAAC,QAAA,gBAEjE5C,OAAA;QACE6C,KAAK,EAAC,IAAI;QACVC,MAAM,EAAC,IAAI;QACXC,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,WAAW,EAAC,GAAG;QACfC,aAAa,EAAC,OAAO;QACrBC,cAAc,EAAC,OAAO;QAAAR,QAAA,gBAEtB5C,OAAA;UAAMqD,CAAC,EAAC;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEzD,OAAA;UAAMqD,CAAC,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,aAER;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAEb,CAAC;EAED,IAAI7C,OAAO,EAAE;IACX,oBACEZ,OAAA,CAACV,SAAS;MAACmD,SAAS,EAAC,kBAAkB;MAAAG,QAAA,gBACrC5C,OAAA,CAACR,OAAO;QAACkE,SAAS,EAAC,QAAQ;QAACC,IAAI,EAAC,QAAQ;QAAAf,QAAA,eACvC5C,OAAA;UAAMyC,SAAS,EAAC,iBAAiB;UAAAG,QAAA,EAAC;QAAU;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACVzD,OAAA;QAAGyC,SAAS,EAAC,MAAM;QAAAG,QAAA,EAAC;MAAe;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEhB;EAEA,IAAI3C,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEV,OAAA,CAACV,SAAS;MAACmD,SAAS,EAAC,MAAM;MAAAG,QAAA,eACzB5C,OAAA,CAACT,KAAK;QAACmD,OAAO,EAAC,QAAQ;QAAAE,QAAA,gBACrB5C,OAAA,CAACT,KAAK,CAACqE,OAAO;UAAAhB,QAAA,EAAC;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eACpCzD,OAAA;UAAA4C,QAAA,EAAI9B,KAAK,IAAI;QAAgB;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACEzD,OAAA,CAAAE,SAAA;IAAA0C,QAAA,gBACE5C,OAAA,CAACN,MAAM;MAAAkD,QAAA,gBACL5C,OAAA;QAAA4C,QAAA,GAAQlC,QAAQ,CAACmD,UAAU,IAAInD,QAAQ,CAACiC,KAAK,EAAC,KAAG,EAACnC,QAAQ;MAAA;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAClE/C,QAAQ,CAACoD,gBAAgB,iBACxB9D,OAAA;QAAM+D,IAAI,EAAC,aAAa;QAACC,OAAO,EAAEtD,QAAQ,CAACoD;MAAiB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC/D;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAGTzD,OAAA,CAACuC,cAAc;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAElBzD,OAAA;MAAKyC,SAAS,EAAC,eAAe;MAAAG,QAAA,EAC3BlC,QAAQ,CAACsD,OAAO,IAAItD,QAAQ,CAACsD,OAAO,CAACA,OAAO,gBAC3ChE,OAAA,CAACL,MAAM;QAACsE,MAAM,EAAEpE,UAAW;QAACsC,IAAI,EAAEzB,QAAQ,CAACsD;MAAQ;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEtDzD,OAAA,CAACV,SAAS;QAACmD,SAAS,EAAC,MAAM;QAAAG,QAAA,gBACzB5C,OAAA;UAAA4C,QAAA,EAAKlC,QAAQ,CAACiC;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzBzD,OAAA;UAAA4C,QAAA,EAAG;QAA6D;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IACZ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACnD,EAAA,CAzJIH,UAAqC;EAAA,QACbf,SAAS,EAEpBQ,WAAW,EACXP,WAAW;AAAA;AAAA6E,EAAA,GAJxB/D,UAAqC;AA2J3C,eAAeA,UAAU;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}