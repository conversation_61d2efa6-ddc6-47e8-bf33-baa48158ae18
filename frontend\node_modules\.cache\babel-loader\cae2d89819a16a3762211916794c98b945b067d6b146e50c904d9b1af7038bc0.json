{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport FormGroup from './FormGroup';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst FloatingLabel = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    children,\n    controlId,\n    label,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-floating');\n  return /*#__PURE__*/_jsxs(FormGroup, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    controlId: controlId,\n    ...props,\n    children: [children, /*#__PURE__*/_jsx(\"label\", {\n      htmlFor: controlId,\n      children: label\n    })]\n  });\n});\nFloatingLabel.displayName = 'FloatingLabel';\nexport default FloatingLabel;", "map": {"version": 3, "names": ["classNames", "React", "FormGroup", "useBootstrapPrefix", "jsx", "_jsx", "jsxs", "_jsxs", "FloatingLabel", "forwardRef", "_ref", "ref", "bsPrefix", "className", "children", "controlId", "label", "props", "htmlFor", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FloatingLabel.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport FormGroup from './FormGroup';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst FloatingLabel = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  controlId,\n  label,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-floating');\n  return /*#__PURE__*/_jsxs(FormGroup, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    controlId: controlId,\n    ...props,\n    children: [children, /*#__PURE__*/_jsx(\"label\", {\n      htmlFor: controlId,\n      children: label\n    })]\n  });\n});\nFloatingLabel.displayName = 'FloatingLabel';\nexport default FloatingLabel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,aAAa,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAAC,IAAA,EAOjDC,GAAG,KAAK;EAAA,IAP0C;IACnDC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACRC,SAAS;IACTC,KAAK;IACL,GAAGC;EACL,CAAC,GAAAP,IAAA;EACCE,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,eAAe,CAAC;EACxD,OAAO,aAAaL,KAAK,CAACL,SAAS,EAAE;IACnCS,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEb,UAAU,CAACa,SAAS,EAAED,QAAQ,CAAC;IAC1CG,SAAS,EAAEA,SAAS;IACpB,GAAGE,KAAK;IACRH,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAaT,IAAI,CAAC,OAAO,EAAE;MAC9Ca,OAAO,EAAEH,SAAS;MAClBD,QAAQ,EAAEE;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,aAAa,CAACW,WAAW,GAAG,eAAe;AAC3C,eAAeX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}