{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport exactProp from '@mui/utils/exactProp';\n/**\n * NoSsr purposely removes components from the subject of Server Side Rendering (SSR).\n *\n * This component can be useful in a variety of situations:\n *\n * * Escape hatch for broken dependencies not supporting SSR.\n * * Improve the time-to-first paint on the client by only rendering above the fold.\n * * Reduce the rendering time on the server.\n * * Under too heavy server load, you can turn on service degradation.\n *\n * Demos:\n *\n * - [No SSR](https://mui.com/material-ui/react-no-ssr/)\n *\n * API:\n *\n * - [NoSsr API](https://mui.com/material-ui/api/no-ssr/)\n */\nfunction NoSsr(props) {\n  const {\n    children,\n    defer = false,\n    fallback = null\n  } = props;\n  const [mountedState, setMountedState] = React.useState(false);\n  useEnhancedEffect(() => {\n    if (!defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n  React.useEffect(() => {\n    if (defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n\n  // TODO casting won't be needed at one point https://github.com/DefinitelyTyped/DefinitelyTyped/pull/65135\n  return mountedState ? children : fallback;\n}\nprocess.env.NODE_ENV !== \"production\" ? NoSsr.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the component will not only prevent server-side rendering.\n   * It will also defer the rendering of the children into a different screen frame.\n   * @default false\n   */\n  defer: PropTypes.bool,\n  /**\n   * The fallback content to display.\n   * @default null\n   */\n  fallback: PropTypes.node\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  NoSsr['propTypes' + ''] = exactProp(NoSsr.propTypes);\n}\nexport default NoSsr;", "map": {"version": 3, "names": ["React", "PropTypes", "useEnhancedEffect", "exactProp", "NoSsr", "props", "children", "defer", "fallback", "mountedState", "setMountedState", "useState", "useEffect", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/NoSsr/NoSsr.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport exactProp from '@mui/utils/exactProp';\n/**\n * NoSsr purposely removes components from the subject of Server Side Rendering (SSR).\n *\n * This component can be useful in a variety of situations:\n *\n * * Escape hatch for broken dependencies not supporting SSR.\n * * Improve the time-to-first paint on the client by only rendering above the fold.\n * * Reduce the rendering time on the server.\n * * Under too heavy server load, you can turn on service degradation.\n *\n * Demos:\n *\n * - [No SSR](https://mui.com/material-ui/react-no-ssr/)\n *\n * API:\n *\n * - [NoSsr API](https://mui.com/material-ui/api/no-ssr/)\n */\nfunction NoSsr(props) {\n  const {\n    children,\n    defer = false,\n    fallback = null\n  } = props;\n  const [mountedState, setMountedState] = React.useState(false);\n  useEnhancedEffect(() => {\n    if (!defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n  React.useEffect(() => {\n    if (defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n\n  // TODO casting won't be needed at one point https://github.com/DefinitelyTyped/DefinitelyTyped/pull/65135\n  return mountedState ? children : fallback;\n}\nprocess.env.NODE_ENV !== \"production\" ? NoSsr.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the component will not only prevent server-side rendering.\n   * It will also defer the rendering of the children into a different screen frame.\n   * @default false\n   */\n  defer: PropTypes.bool,\n  /**\n   * The fallback content to display.\n   * @default null\n   */\n  fallback: PropTypes.node\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  NoSsr['propTypes' + ''] = exactProp(NoSsr.propTypes);\n}\nexport default NoSsr;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,SAAS,MAAM,sBAAsB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,MAAM;IACJC,QAAQ;IACRC,KAAK,GAAG,KAAK;IACbC,QAAQ,GAAG;EACb,CAAC,GAAGH,KAAK;EACT,MAAM,CAACI,YAAY,EAAEC,eAAe,CAAC,GAAGV,KAAK,CAACW,QAAQ,CAAC,KAAK,CAAC;EAC7DT,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAACK,KAAK,EAAE;MACVG,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;EACXP,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,IAAIL,KAAK,EAAE;MACTG,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;;EAEX;EACA,OAAOE,YAAY,GAAGH,QAAQ,GAAGE,QAAQ;AAC3C;AACAK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,KAAK,CAACY,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEV,QAAQ,EAAEL,SAAS,CAACgB,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEV,KAAK,EAAEN,SAAS,CAACiB,IAAI;EACrB;AACF;AACA;AACA;EACEV,QAAQ,EAAEP,SAAS,CAACgB;AACtB,CAAC,GAAG,KAAK,CAAC;AACV,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACAX,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC,GAAGD,SAAS,CAACC,KAAK,CAACY,SAAS,CAAC;AACtD;AACA,eAAeZ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}