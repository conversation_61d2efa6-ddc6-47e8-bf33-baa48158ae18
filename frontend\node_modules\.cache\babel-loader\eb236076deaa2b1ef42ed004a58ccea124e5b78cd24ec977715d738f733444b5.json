{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport Collapse from './Collapse';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarCollapse = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    children,\n    bsPrefix,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-collapse');\n  const context = useContext(NavbarContext);\n  return /*#__PURE__*/_jsx(Collapse, {\n    in: !!(context && context.expanded),\n    ...props,\n    children: /*#__PURE__*/_jsx(\"div\", {\n      ref: ref,\n      className: bsPrefix,\n      children: children\n    })\n  });\n});\nNavbarCollapse.displayName = 'NavbarCollapse';\nexport default NavbarCollapse;", "map": {"version": 3, "names": ["React", "useContext", "Collapse", "useBootstrapPrefix", "NavbarContext", "jsx", "_jsx", "NavbarCollapse", "forwardRef", "_ref", "ref", "children", "bsPrefix", "props", "context", "in", "expanded", "className", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/NavbarCollapse.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport Collapse from './Collapse';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarCollapse = /*#__PURE__*/React.forwardRef(({\n  children,\n  bsPrefix,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-collapse');\n  const context = useContext(NavbarContext);\n  return /*#__PURE__*/_jsx(Collapse, {\n    in: !!(context && context.expanded),\n    ...props,\n    children: /*#__PURE__*/_jsx(\"div\", {\n      ref: ref,\n      className: bsPrefix,\n      children: children\n    })\n  });\n});\nNavbarCollapse.displayName = 'NavbarCollapse';\nexport default NavbarCollapse;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAAC,IAAA,EAIlDC,GAAG,KAAK;EAAA,IAJ2C;IACpDC,QAAQ;IACRC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAAJ,IAAA;EACCG,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,iBAAiB,CAAC;EAC1D,MAAME,OAAO,GAAGb,UAAU,CAACG,aAAa,CAAC;EACzC,OAAO,aAAaE,IAAI,CAACJ,QAAQ,EAAE;IACjCa,EAAE,EAAE,CAAC,EAAED,OAAO,IAAIA,OAAO,CAACE,QAAQ,CAAC;IACnC,GAAGH,KAAK;IACRF,QAAQ,EAAE,aAAaL,IAAI,CAAC,KAAK,EAAE;MACjCI,GAAG,EAAEA,GAAG;MACRO,SAAS,EAAEL,QAAQ;MACnBD,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,cAAc,CAACW,WAAW,GAAG,gBAAgB;AAC7C,eAAeX,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}