{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst Tablelvl2Context = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  Tablelvl2Context.displayName = 'Tablelvl2Context';\n}\nexport default Tablelvl2Context;", "map": {"version": 3, "names": ["React", "Tablelvl2Context", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Table/Tablelvl2Context.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst Tablelvl2Context = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  Tablelvl2Context.displayName = 'Tablelvl2Context';\n}\nexport default Tablelvl2Context;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC;AAC3D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,gBAAgB,CAACK,WAAW,GAAG,kBAAkB;AACnD;AACA,eAAeL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}