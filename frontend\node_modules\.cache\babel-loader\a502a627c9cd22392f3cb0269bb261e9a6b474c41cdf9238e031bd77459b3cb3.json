{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownHeader = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    className,\n    bsPrefix,\n    as: Component = 'div',\n    role = 'heading',\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'dropdown-header');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    role: role,\n    ...props\n  });\n});\nDropdownHeader.displayName = 'DropdownHeader';\nexport default DropdownHeader;", "map": {"version": 3, "names": ["React", "classNames", "useBootstrapPrefix", "jsx", "_jsx", "DropdownHeader", "forwardRef", "_ref", "ref", "className", "bsPrefix", "as", "Component", "role", "props", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/DropdownHeader.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownHeader = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  role = 'heading',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'dropdown-header');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    role: role,\n    ...props\n  });\n});\nDropdownHeader.displayName = 'DropdownHeader';\nexport default DropdownHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAAC,IAAA,EAMlDC,GAAG,KAAK;EAAA,IAN2C;IACpDC,SAAS;IACTC,QAAQ;IACRC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrBC,IAAI,GAAG,SAAS;IAChB,GAAGC;EACL,CAAC,GAAAP,IAAA;EACCG,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,iBAAiB,CAAC;EAC1D,OAAO,aAAaN,IAAI,CAACQ,SAAS,EAAE;IAClCJ,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEC,QAAQ,CAAC;IAC1CG,IAAI,EAAEA,IAAI;IACV,GAAGC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,cAAc,CAACU,WAAW,GAAG,gBAAgB;AAC7C,eAAeV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}