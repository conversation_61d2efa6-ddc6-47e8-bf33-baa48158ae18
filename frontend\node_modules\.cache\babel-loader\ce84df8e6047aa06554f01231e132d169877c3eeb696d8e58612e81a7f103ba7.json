{"ast": null, "code": "import { createContext, memo, forwardRef, useState, useRef, useMemo, startTransition, useImperativeHandle, useEffect, useContext, useCallback, createElement } from 'react';\nimport { DragDropManager, defaultPreset, Draggable, Feedback, Droppable } from '@dnd-kit/dom';\nexport { KeyboardSensor, PointerSensor } from '@dnd-kit/dom';\nimport { useIsomorphicLayoutEffect, useLatest, useOnValueChange, useDeepSignal, useOnElementChange, useComputed } from '@dnd-kit/react/hooks';\nimport { deepEqual, ValueHistory, derived, batch } from '@dnd-kit/state';\nimport { jsxs, jsx } from 'react/jsx-runtime';\nimport { currentValue } from '@dnd-kit/react/utilities';\nimport { CollisionPriority, CollisionType } from '@dnd-kit/abstract';\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols) for (var prop of __getOwnPropSymbols(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\nvar defaultManager = new DragDropManager();\nvar DragDropContext = createContext(defaultManager);\nvar Renderer = memo(forwardRef((_ref, ref) => {\n  let {\n    children\n  } = _ref;\n  const [transitionCount, setTransitionCount] = useState(0);\n  const rendering = useRef(null);\n  const resolver = useRef(null);\n  const renderer = useMemo(() => ({\n    renderer: {\n      get rendering() {\n        var _a2;\n        return (_a2 = rendering.current) != null ? _a2 : Promise.resolve();\n      }\n    },\n    trackRendering(callback) {\n      if (!rendering.current) {\n        rendering.current = new Promise(resolve => {\n          resolver.current = resolve;\n        });\n      }\n      startTransition(() => {\n        callback();\n        setTransitionCount(count => count + 1);\n      });\n    }\n  }), []);\n  useIsomorphicLayoutEffect(() => {\n    var _a2;\n    (_a2 = resolver.current) == null ? void 0 : _a2.call(resolver);\n    rendering.current = null;\n  }, [children, transitionCount]);\n  useImperativeHandle(ref, () => renderer);\n  return null;\n}));\nvar options = [void 0, deepEqual];\nfunction DragDropProvider(_a2) {\n  var _b = _a2,\n    {\n      children,\n      onCollision,\n      onBeforeDragStart,\n      onDragStart,\n      onDragMove,\n      onDragOver,\n      onDragEnd\n    } = _b,\n    input = __objRest(_b, [\"children\", \"onCollision\", \"onBeforeDragStart\", \"onDragStart\", \"onDragMove\", \"onDragOver\", \"onDragEnd\"]);\n  var _a3;\n  const rendererRef = useRef(null);\n  const [manager, setManager] = useState((_a3 = input.manager) != null ? _a3 : null);\n  const {\n    plugins,\n    modifiers,\n    sensors\n  } = input;\n  const handleBeforeDragStart = useLatest(onBeforeDragStart);\n  const handleDragStart = useLatest(onDragStart);\n  const handleDragOver = useLatest(onDragOver);\n  const handleDragMove = useLatest(onDragMove);\n  const handleDragEnd = useLatest(onDragEnd);\n  const handleCollision = useLatest(onCollision);\n  useEffect(() => {\n    var _a4;\n    if (!rendererRef.current) throw new Error(\"Renderer not found\");\n    const {\n      renderer,\n      trackRendering\n    } = rendererRef.current;\n    const manager2 = (_a4 = input.manager) != null ? _a4 : new DragDropManager(input);\n    manager2.renderer = renderer;\n    manager2.monitor.addEventListener(\"beforedragstart\", event => {\n      const callback = handleBeforeDragStart.current;\n      if (callback) {\n        trackRendering(() => callback(event, manager2));\n      }\n    });\n    manager2.monitor.addEventListener(\"dragstart\", event => {\n      var _a5;\n      return (_a5 = handleDragStart.current) == null ? void 0 : _a5.call(handleDragStart, event, manager2);\n    });\n    manager2.monitor.addEventListener(\"dragover\", event => {\n      const callback = handleDragOver.current;\n      if (callback) {\n        trackRendering(() => callback(event, manager2));\n      }\n    });\n    manager2.monitor.addEventListener(\"dragmove\", event => {\n      const callback = handleDragMove.current;\n      if (callback) {\n        trackRendering(() => callback(event, manager2));\n      }\n    });\n    manager2.monitor.addEventListener(\"dragend\", event => {\n      const callback = handleDragEnd.current;\n      if (callback) {\n        trackRendering(() => callback(event, manager2));\n      }\n    });\n    manager2.monitor.addEventListener(\"collision\", event => {\n      var _a5;\n      return (_a5 = handleCollision.current) == null ? void 0 : _a5.call(handleCollision, event, manager2);\n    });\n    startTransition(() => setManager(manager2));\n    return manager2.destroy;\n  }, [input.manager]);\n  useOnValueChange(plugins, () => manager && (manager.plugins = plugins != null ? plugins : defaultPreset.plugins), ...options);\n  useOnValueChange(sensors, () => manager && (manager.sensors = sensors != null ? sensors : defaultPreset.sensors), ...options);\n  useOnValueChange(modifiers, () => manager && (manager.modifiers = modifiers != null ? modifiers : defaultPreset.modifiers), ...options);\n  return /* @__PURE__ */jsxs(DragDropContext.Provider, {\n    value: manager,\n    children: [/* @__PURE__ */jsx(Renderer, {\n      ref: rendererRef,\n      children\n    }), children]\n  });\n}\nfunction useDragDropManager() {\n  return useContext(DragDropContext);\n}\n\n// src/core/hooks/useInstance.ts\nfunction useInstance(initializer) {\n  var _a2;\n  const manager = (_a2 = useDragDropManager()) != null ? _a2 : void 0;\n  const [instance] = useState(() => initializer(manager));\n  if (instance.manager !== manager) {\n    instance.manager = manager;\n  }\n  useIsomorphicLayoutEffect(instance.register, [manager, instance]);\n  return instance;\n}\n\n// src/core/draggable/useDraggable.ts\nfunction useDraggable(input) {\n  const {\n    disabled,\n    data,\n    element,\n    handle,\n    id,\n    modifiers,\n    sensors\n  } = input;\n  const draggable = useInstance(manager => new Draggable(__spreadProps(__spreadValues({}, input), {\n    register: false,\n    handle: currentValue(handle),\n    element: currentValue(element)\n  }), manager));\n  const trackedDraggable = useDeepSignal(draggable, shouldUpdateSynchronously);\n  useOnValueChange(id, () => draggable.id = id);\n  useOnElementChange(handle, handle2 => draggable.handle = handle2);\n  useOnElementChange(element, element2 => draggable.element = element2);\n  useOnValueChange(data, () => data && (draggable.data = data));\n  useOnValueChange(disabled, () => draggable.disabled = disabled === true);\n  useOnValueChange(sensors, () => draggable.sensors = sensors);\n  useOnValueChange(modifiers, () => draggable.modifiers = modifiers, void 0, deepEqual);\n  useOnValueChange(input.feedback, () => {\n    var _a2;\n    return draggable.feedback = (_a2 = input.feedback) != null ? _a2 : \"default\";\n  });\n  useOnValueChange(input.alignment, () => draggable.alignment = input.alignment);\n  return {\n    draggable: trackedDraggable,\n    get isDragging() {\n      return trackedDraggable.isDragging;\n    },\n    get isDropping() {\n      return trackedDraggable.isDropping;\n    },\n    get isDragSource() {\n      return trackedDraggable.isDragSource;\n    },\n    handleRef: useCallback(element2 => {\n      draggable.handle = element2 != null ? element2 : void 0;\n    }, [draggable]),\n    ref: useCallback(element2 => {\n      var _a2, _b;\n      if (!element2 && ((_a2 = draggable.element) == null ? void 0 : _a2.isConnected) && !((_b = draggable.manager) == null ? void 0 : _b.dragOperation.status.idle)) {\n        return;\n      }\n      draggable.element = element2 != null ? element2 : void 0;\n    }, [draggable])\n  };\n}\nfunction shouldUpdateSynchronously(key, oldValue, newValue) {\n  if (key === \"isDragSource\" && !newValue && oldValue) return true;\n  return false;\n}\nfunction DragOverlay(_ref2) {\n  let {\n    children,\n    className,\n    style,\n    tag,\n    disabled\n  } = _ref2;\n  var _a2;\n  const ref = useRef(null);\n  const manager = useDragDropManager();\n  const source = (_a2 = useComputed(() => manager == null ? void 0 : manager.dragOperation.source, [manager]).value) != null ? _a2 : null;\n  const isDisabled = typeof disabled === \"function\" ? disabled(source) : disabled;\n  useEffect(() => {\n    if (!ref.current || !manager || isDisabled) return;\n    const feedback = manager.plugins.find(plugin => plugin instanceof Feedback);\n    if (!feedback) return;\n    feedback.overlay = ref.current;\n    return () => {\n      feedback.overlay = void 0;\n    };\n  }, [manager, isDisabled]);\n  const patchedManager = useMemo(() => {\n    if (!manager) return null;\n    const patchedRegistry = new Proxy(manager.registry, {\n      get(target, property) {\n        if (property === \"register\" || property === \"unregister\") {\n          return noop;\n        }\n        return target[property];\n      }\n    });\n    return new Proxy(manager, {\n      get(target, property) {\n        if (property === \"registry\") {\n          return patchedRegistry;\n        }\n        return target[property];\n      }\n    });\n  }, [manager]);\n  return /* @__PURE__ */jsx(DragDropContext.Provider, {\n    value: patchedManager,\n    children: createElement(tag || \"div\", {\n      ref,\n      className,\n      style,\n      \"data-dnd-overlay\": true\n    }, renderChildren())\n  });\n  function renderChildren() {\n    if (!source || isDisabled) return null;\n    if (typeof children === \"function\") {\n      return /* @__PURE__ */jsx(Children, {\n        source,\n        children\n      });\n    }\n    return children;\n  }\n}\nfunction noop() {\n  return () => {};\n}\nfunction Children(_ref3) {\n  let {\n    children,\n    source\n  } = _ref3;\n  return children(useDeepSignal(source));\n}\nvar __create = Object.create;\nvar __defProp2 = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __knownSymbol = (name, symbol) => (symbol = Symbol[name]) ? symbol : Symbol.for(\"Symbol.\" + name);\nvar __typeError = msg => {\n  throw TypeError(msg);\n};\nvar __defNormalProp2 = (obj, key, value) => key in obj ? __defProp2(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __decoratorStart = base => {\n  var _a2;\n  return [,,, __create((_a2 = base == null ? void 0 : base[__knownSymbol(\"metadata\")]) != null ? _a2 : null)];\n};\nvar __decoratorStrings = [\"class\", \"method\", \"getter\", \"setter\", \"accessor\", \"field\", \"value\", \"get\", \"set\"];\nvar __expectFn = fn => fn !== void 0 && typeof fn !== \"function\" ? __typeError(\"Function expected\") : fn;\nvar __decoratorContext = (kind, name, done, metadata, fns) => ({\n  kind: __decoratorStrings[kind],\n  name,\n  metadata,\n  addInitializer: fn => done._ ? __typeError(\"Already initialized\") : fns.push(__expectFn(fn || null))\n});\nvar __decoratorMetadata = (array, target) => __defNormalProp2(target, __knownSymbol(\"metadata\"), array[3]);\nvar __runInitializers = (array, flags, self, value) => {\n  for (var i = 0, fns = array[flags >> 1], n = fns && fns.length; i < n; i++) fns[i].call(self);\n  return value;\n};\nvar __decorateElement = (array, flags, name, decorators, target, extra) => {\n  var it,\n    done,\n    ctx,\n    access,\n    k = flags & 7,\n    s = false,\n    p = false;\n  var j = 2,\n    key = __decoratorStrings[k + 5];\n  var extraInitializers = array[j] || (array[j] = []);\n  var desc = (target = target.prototype, __getOwnPropDesc(target, name));\n  for (var i = decorators.length - 1; i >= 0; i--) {\n    ctx = __decoratorContext(k, name, done = {}, array[3], extraInitializers);\n    {\n      ctx.static = s, ctx.private = p, access = ctx.access = {\n        has: x => name in x\n      };\n      access.get = x => x[name];\n    }\n    it = (0, decorators[i])(desc[key], ctx), done._ = 1;\n    __expectFn(it) && (desc[key] = it);\n  }\n  return desc && __defProp2(target, name, desc), target;\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), member.set(obj, value), value);\nvar Point = class _Point {\n  /**\n   * @param {number} Coordinate of the point on the horizontal axis\n   * @param {number} Coordinate of the point on the vertical axis\n   */\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n  /**\n   * Returns the delta between this point and another point.\n   *\n   * @param {Point} a - A point\n   * @param {Point} b - Another point\n   */\n  static delta(a, b) {\n    return new _Point(a.x - b.x, a.y - b.y);\n  }\n  /**\n   * Returns the distance (hypotenuse) between this point and another point.\n   *\n   * @param {Point} a - A point\n   * @param {Point} b - Another point\n   */\n  static distance(a, b) {\n    return Math.hypot(a.x - b.x, a.y - b.y);\n  }\n  /**\n   * Returns true if both points are equal.\n   *\n   * @param {Point} a - A point\n   * @param {Point} b - Another point\n   */\n  static equals(a, b) {\n    return a.x === b.x && a.y === b.y;\n  }\n  static from(_ref4) {\n    let {\n      x,\n      y\n    } = _ref4;\n    return new _Point(x, y);\n  }\n};\nvar _direction_dec;\nvar _delta_dec;\nvar _a;\nvar _timestamp;\nvar _init;\nvar Position = class extends (_a = ValueHistory, _delta_dec = [derived], _direction_dec = [derived], _a) {\n  constructor(initialValue) {\n    const point = Point.from(initialValue);\n    super(point, (a, b) => Point.equals(a, b));\n    __runInitializers(_init, 5, this);\n    __privateAdd(this, _timestamp, 0);\n    this.velocity = {\n      x: 0,\n      y: 0\n    };\n  }\n  get delta() {\n    return Point.delta(this.current, this.initial);\n  }\n  get direction() {\n    const {\n      current,\n      previous\n    } = this;\n    if (!previous) return null;\n    const delta = {\n      x: current.x - previous.x,\n      y: current.y - previous.y\n    };\n    if (!delta.x && !delta.y) {\n      return null;\n    }\n    if (Math.abs(delta.x) > Math.abs(delta.y)) {\n      return delta.x > 0 ? \"right\" : \"left\";\n    }\n    return delta.y > 0 ? \"down\" : \"up\";\n  }\n  get current() {\n    return super.current;\n  }\n  set current(coordinates) {\n    const {\n      current\n    } = this;\n    const point = Point.from(coordinates);\n    const delta = {\n      x: point.x - current.x,\n      y: point.y - current.y\n    };\n    const timestamp = Date.now();\n    const timeDelta = timestamp - __privateGet(this, _timestamp);\n    const velocity = delta2 => Math.round(delta2 / timeDelta * 100);\n    batch(() => {\n      __privateSet(this, _timestamp, timestamp);\n      this.velocity = {\n        x: velocity(delta.x),\n        y: velocity(delta.y)\n      };\n      super.current = point;\n    });\n  }\n  reset() {\n    let coordinates = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.defaultValue;\n    super.reset(Point.from(coordinates));\n    this.velocity = {\n      x: 0,\n      y: 0\n    };\n  }\n};\n_init = __decoratorStart(_a);\n_timestamp = /* @__PURE__ */new WeakMap();\n__decorateElement(_init, 2, \"delta\", _delta_dec, Position);\n__decorateElement(_init, 2, \"direction\", _direction_dec, Position);\n__decoratorMetadata(_init, Position);\nvar Axis = /* @__PURE__ */(Axis2 => {\n  Axis2[\"Horizontal\"] = \"x\";\n  Axis2[\"Vertical\"] = \"y\";\n  return Axis2;\n})(Axis || {});\nObject.values(Axis);\nvar pointerIntersection = _ref5 => {\n  let {\n    dragOperation,\n    droppable\n  } = _ref5;\n  const pointerCoordinates = dragOperation.position.current;\n  if (!pointerCoordinates) {\n    return null;\n  }\n  const {\n    id\n  } = droppable;\n  if (!droppable.shape) {\n    return null;\n  }\n  if (droppable.shape.containsPoint(pointerCoordinates)) {\n    const distance = Point.distance(droppable.shape.center, pointerCoordinates);\n    return {\n      id,\n      value: 1 / distance,\n      type: CollisionType.PointerIntersection,\n      priority: CollisionPriority.High\n    };\n  }\n  return null;\n};\nvar shapeIntersection = _ref6 => {\n  let {\n    dragOperation,\n    droppable\n  } = _ref6;\n  const {\n    shape\n  } = dragOperation;\n  if (!droppable.shape || !(shape == null ? void 0 : shape.current)) {\n    return null;\n  }\n  const intersectionArea = shape.current.intersectionArea(droppable.shape);\n  if (intersectionArea) {\n    const {\n      position\n    } = dragOperation;\n    const distance = Point.distance(droppable.shape.center, position.current);\n    const intersectionRatio = intersectionArea / (shape.current.area + droppable.shape.area - intersectionArea);\n    const value = intersectionRatio / distance;\n    return {\n      id: droppable.id,\n      value,\n      type: CollisionType.ShapeIntersection,\n      priority: CollisionPriority.Normal\n    };\n  }\n  return null;\n};\nvar defaultCollisionDetection = args => {\n  var _a2;\n  return (_a2 = pointerIntersection(args)) != null ? _a2 : shapeIntersection(args);\n};\n\n// src/core/droppable/useDroppable.ts\nfunction useDroppable(input) {\n  const {\n    collisionDetector,\n    data,\n    disabled,\n    element,\n    id,\n    accept,\n    type\n  } = input;\n  const droppable = useInstance(manager => new Droppable(__spreadProps(__spreadValues({}, input), {\n    register: false,\n    element: currentValue(element)\n  }), manager));\n  const trackedDroppalbe = useDeepSignal(droppable);\n  useOnValueChange(id, () => droppable.id = id);\n  useOnElementChange(element, element2 => droppable.element = element2);\n  useOnValueChange(accept, () => droppable.accept = accept, void 0, deepEqual);\n  useOnValueChange(collisionDetector, () => droppable.collisionDetector = collisionDetector != null ? collisionDetector : defaultCollisionDetection);\n  useOnValueChange(data, () => data && (droppable.data = data));\n  useOnValueChange(disabled, () => droppable.disabled = disabled === true);\n  useOnValueChange(type, () => droppable.type = type);\n  return {\n    droppable: trackedDroppalbe,\n    get isDropTarget() {\n      return trackedDroppalbe.isDropTarget;\n    },\n    ref: useCallback(element2 => {\n      var _a2, _b;\n      if (!element2 && ((_a2 = droppable.element) == null ? void 0 : _a2.isConnected) && !((_b = droppable.manager) == null ? void 0 : _b.dragOperation.status.idle)) {\n        return;\n      }\n      droppable.element = element2 != null ? element2 : void 0;\n    }, [droppable])\n  };\n}\nfunction useDragDropMonitor(handlers) {\n  const manager = useDragDropManager();\n  useEffect(() => {\n    if (!manager) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.warn(\"useDndMonitor hook was called outside of a DragDropProvider. Make sure your app is wrapped in a DragDropProvider component.\");\n      }\n      return;\n    }\n    const cleanupFns = Object.entries(handlers).reduce((acc, _ref7) => {\n      let [handlerName, handler] = _ref7;\n      if (handler) {\n        const eventName = handlerName.replace(/^on/, \"\").toLowerCase();\n        const unsubscribe = manager.monitor.addEventListener(eventName, handler);\n        acc.push(unsubscribe);\n      }\n      return acc;\n    }, []);\n    return () => cleanupFns.forEach(cleanup => cleanup == null ? void 0 : cleanup());\n  }, [manager, handlers]);\n}\nfunction useDragOperation() {\n  const manager = useDragDropManager();\n  const source = useComputed(() => manager == null ? void 0 : manager.dragOperation.source, [manager]);\n  const target = useComputed(() => manager == null ? void 0 : manager.dragOperation.target, [manager]);\n  return {\n    get source() {\n      return source.value;\n    },\n    get target() {\n      return target.value;\n    }\n  };\n}\nexport { DragDropProvider, DragOverlay, useDragDropManager, useDragDropMonitor, useDragOperation, useDraggable, useDroppable, useInstance };", "map": {"version": 3, "names": ["createContext", "memo", "forwardRef", "useState", "useRef", "useMemo", "startTransition", "useImperativeHandle", "useEffect", "useContext", "useCallback", "createElement", "DragDropManager", "defaultPreset", "Draggable", "<PERSON><PERSON><PERSON>", "Droppable", "KeyboardSensor", "PointerSensor", "useIsomorphicLayoutEffect", "useLatest", "useOnValueChange", "useDeepSignal", "useOnElementChange", "useComputed", "deepEqual", "ValueHistory", "derived", "batch", "jsxs", "jsx", "currentValue", "CollisionPriority", "CollisionType", "__defProp", "Object", "defineProperty", "__defProps", "defineProperties", "__getOwnPropDescs", "getOwnPropertyDescriptors", "__getOwnPropSymbols", "getOwnPropertySymbols", "__hasOwnProp", "prototype", "hasOwnProperty", "__propIsEnum", "propertyIsEnumerable", "__defNormalProp", "obj", "key", "value", "enumerable", "configurable", "writable", "__spreadValues", "a", "b", "prop", "call", "__spreadProps", "__objRest", "source", "exclude", "target", "indexOf", "defaultManager", "DragDropContext", "<PERSON><PERSON><PERSON>", "_ref", "ref", "children", "transitionCount", "setTransitionCount", "rendering", "resolver", "renderer", "_a2", "current", "Promise", "resolve", "trackRendering", "callback", "count", "options", "DragDropProvider", "_b", "onCollision", "onBeforeDragStart", "onDragStart", "onDragMove", "onDragOver", "onDragEnd", "input", "_a3", "rendererRef", "manager", "setManager", "plugins", "modifiers", "sensors", "handleBeforeDragStart", "handleDragStart", "handleDragOver", "handleDragMove", "handleDragEnd", "handleCollision", "_a4", "Error", "manager2", "monitor", "addEventListener", "event", "_a5", "destroy", "Provider", "useDragDropManager", "useInstance", "initializer", "instance", "register", "useDraggable", "disabled", "data", "element", "handle", "id", "draggable", "trackedDraggable", "shouldUpdateSynchronously", "handle2", "element2", "feedback", "alignment", "isDragging", "isDropping", "isDragSource", "handleRef", "isConnected", "dragOperation", "status", "idle", "oldValue", "newValue", "DragOverlay", "_ref2", "className", "style", "tag", "isDisabled", "find", "plugin", "overlay", "patchedManager", "patchedRegistry", "Proxy", "registry", "get", "property", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Children", "_ref3", "__create", "create", "__defProp2", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__knownSymbol", "name", "symbol", "Symbol", "for", "__typeError", "msg", "TypeError", "__defNormalProp2", "__decoratorStart", "base", "__decoratorStrings", "__expectFn", "fn", "__decoratorContext", "kind", "done", "metadata", "fns", "addInitializer", "_", "push", "__decoratorMetadata", "array", "__runInitializers", "flags", "self", "i", "n", "length", "__decorateElement", "decorators", "extra", "it", "ctx", "access", "k", "s", "p", "j", "extraInitializers", "desc", "static", "private", "has", "x", "__access<PERSON>heck", "member", "__privateGet", "getter", "__privateAdd", "WeakSet", "add", "set", "__privateSet", "setter", "Point", "_Point", "constructor", "y", "delta", "distance", "Math", "hypot", "equals", "from", "_ref4", "_direction_dec", "_delta_dec", "_a", "_timestamp", "_init", "Position", "initialValue", "point", "velocity", "initial", "direction", "previous", "abs", "coordinates", "timestamp", "Date", "now", "<PERSON><PERSON><PERSON><PERSON>", "delta2", "round", "reset", "arguments", "undefined", "defaultValue", "WeakMap", "Axis", "Axis2", "values", "pointerIntersection", "_ref5", "droppable", "pointerCoordinates", "position", "shape", "containsPoint", "center", "type", "PointerIntersection", "priority", "High", "shapeIntersection", "_ref6", "intersectionArea", "intersectionRatio", "area", "ShapeIntersection", "Normal", "defaultCollisionDetection", "args", "useDroppable", "collisionDetector", "accept", "trackedDroppalbe", "isDropTarget", "useDragDropMonitor", "handlers", "process", "env", "NODE_ENV", "console", "warn", "cleanupFns", "entries", "reduce", "acc", "_ref7", "handler<PERSON>ame", "handler", "eventName", "replace", "toLowerCase", "unsubscribe", "for<PERSON>ach", "cleanup", "useDragOperation"], "sources": ["C:/laragon/www/frontend/node_modules/@dnd-kit/react/index.js"], "sourcesContent": ["import { createContext, memo, forwardRef, useState, useRef, useMemo, startTransition, useImperativeHandle, useEffect, useContext, useCallback, createElement } from 'react';\nimport { DragDropManager, defaultPreset, Draggable, Feedback, Droppable } from '@dnd-kit/dom';\nexport { KeyboardSensor, PointerSensor } from '@dnd-kit/dom';\nimport { useIsomorphicLayoutEffect, useLatest, useOnValueChange, useDeepSignal, useOnElementChange, useComputed } from '@dnd-kit/react/hooks';\nimport { deepEqual, ValueHistory, derived, batch } from '@dnd-kit/state';\nimport { jsxs, jsx } from 'react/jsx-runtime';\nimport { currentValue } from '@dnd-kit/react/utilities';\nimport { CollisionPriority, CollisionType } from '@dnd-kit/abstract';\n\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nvar defaultManager = new DragDropManager();\nvar DragDropContext = createContext(\n  defaultManager\n);\nvar Renderer = memo(\n  forwardRef(({ children }, ref) => {\n    const [transitionCount, setTransitionCount] = useState(0);\n    const rendering = useRef(null);\n    const resolver = useRef(null);\n    const renderer = useMemo(\n      () => ({\n        renderer: {\n          get rendering() {\n            var _a2;\n            return (_a2 = rendering.current) != null ? _a2 : Promise.resolve();\n          }\n        },\n        trackRendering(callback) {\n          if (!rendering.current) {\n            rendering.current = new Promise((resolve) => {\n              resolver.current = resolve;\n            });\n          }\n          startTransition(() => {\n            callback();\n            setTransitionCount((count) => count + 1);\n          });\n        }\n      }),\n      []\n    );\n    useIsomorphicLayoutEffect(() => {\n      var _a2;\n      (_a2 = resolver.current) == null ? void 0 : _a2.call(resolver);\n      rendering.current = null;\n    }, [children, transitionCount]);\n    useImperativeHandle(ref, () => renderer);\n    return null;\n  })\n);\nvar options = [void 0, deepEqual];\nfunction DragDropProvider(_a2) {\n  var _b = _a2, {\n    children,\n    onCollision,\n    onBeforeDragStart,\n    onDragStart,\n    onDragMove,\n    onDragOver,\n    onDragEnd\n  } = _b, input = __objRest(_b, [\n    \"children\",\n    \"onCollision\",\n    \"onBeforeDragStart\",\n    \"onDragStart\",\n    \"onDragMove\",\n    \"onDragOver\",\n    \"onDragEnd\"\n  ]);\n  var _a3;\n  const rendererRef = useRef(null);\n  const [manager, setManager] = useState((_a3 = input.manager) != null ? _a3 : null);\n  const { plugins, modifiers, sensors } = input;\n  const handleBeforeDragStart = useLatest(onBeforeDragStart);\n  const handleDragStart = useLatest(onDragStart);\n  const handleDragOver = useLatest(onDragOver);\n  const handleDragMove = useLatest(onDragMove);\n  const handleDragEnd = useLatest(onDragEnd);\n  const handleCollision = useLatest(onCollision);\n  useEffect(() => {\n    var _a4;\n    if (!rendererRef.current) throw new Error(\"Renderer not found\");\n    const { renderer, trackRendering } = rendererRef.current;\n    const manager2 = (_a4 = input.manager) != null ? _a4 : new DragDropManager(input);\n    manager2.renderer = renderer;\n    manager2.monitor.addEventListener(\"beforedragstart\", (event) => {\n      const callback = handleBeforeDragStart.current;\n      if (callback) {\n        trackRendering(() => callback(event, manager2));\n      }\n    });\n    manager2.monitor.addEventListener(\n      \"dragstart\",\n      (event) => {\n        var _a5;\n        return (_a5 = handleDragStart.current) == null ? void 0 : _a5.call(handleDragStart, event, manager2);\n      }\n    );\n    manager2.monitor.addEventListener(\"dragover\", (event) => {\n      const callback = handleDragOver.current;\n      if (callback) {\n        trackRendering(() => callback(event, manager2));\n      }\n    });\n    manager2.monitor.addEventListener(\"dragmove\", (event) => {\n      const callback = handleDragMove.current;\n      if (callback) {\n        trackRendering(() => callback(event, manager2));\n      }\n    });\n    manager2.monitor.addEventListener(\"dragend\", (event) => {\n      const callback = handleDragEnd.current;\n      if (callback) {\n        trackRendering(() => callback(event, manager2));\n      }\n    });\n    manager2.monitor.addEventListener(\n      \"collision\",\n      (event) => {\n        var _a5;\n        return (_a5 = handleCollision.current) == null ? void 0 : _a5.call(handleCollision, event, manager2);\n      }\n    );\n    startTransition(() => setManager(manager2));\n    return manager2.destroy;\n  }, [input.manager]);\n  useOnValueChange(\n    plugins,\n    () => manager && (manager.plugins = plugins != null ? plugins : defaultPreset.plugins),\n    ...options\n  );\n  useOnValueChange(\n    sensors,\n    () => manager && (manager.sensors = sensors != null ? sensors : defaultPreset.sensors),\n    ...options\n  );\n  useOnValueChange(\n    modifiers,\n    () => manager && (manager.modifiers = modifiers != null ? modifiers : defaultPreset.modifiers),\n    ...options\n  );\n  return /* @__PURE__ */ jsxs(DragDropContext.Provider, { value: manager, children: [\n    /* @__PURE__ */ jsx(Renderer, { ref: rendererRef, children }),\n    children\n  ] });\n}\nfunction useDragDropManager() {\n  return useContext(DragDropContext);\n}\n\n// src/core/hooks/useInstance.ts\nfunction useInstance(initializer) {\n  var _a2;\n  const manager = (_a2 = useDragDropManager()) != null ? _a2 : void 0;\n  const [instance] = useState(() => initializer(manager));\n  if (instance.manager !== manager) {\n    instance.manager = manager;\n  }\n  useIsomorphicLayoutEffect(instance.register, [manager, instance]);\n  return instance;\n}\n\n// src/core/draggable/useDraggable.ts\nfunction useDraggable(input) {\n  const { disabled, data, element, handle, id, modifiers, sensors } = input;\n  const draggable = useInstance(\n    (manager) => new Draggable(\n      __spreadProps(__spreadValues({}, input), {\n        register: false,\n        handle: currentValue(handle),\n        element: currentValue(element)\n      }),\n      manager\n    )\n  );\n  const trackedDraggable = useDeepSignal(draggable, shouldUpdateSynchronously);\n  useOnValueChange(id, () => draggable.id = id);\n  useOnElementChange(handle, (handle2) => draggable.handle = handle2);\n  useOnElementChange(element, (element2) => draggable.element = element2);\n  useOnValueChange(data, () => data && (draggable.data = data));\n  useOnValueChange(disabled, () => draggable.disabled = disabled === true);\n  useOnValueChange(sensors, () => draggable.sensors = sensors);\n  useOnValueChange(\n    modifiers,\n    () => draggable.modifiers = modifiers,\n    void 0,\n    deepEqual\n  );\n  useOnValueChange(\n    input.feedback,\n    () => {\n      var _a2;\n      return draggable.feedback = (_a2 = input.feedback) != null ? _a2 : \"default\";\n    }\n  );\n  useOnValueChange(\n    input.alignment,\n    () => draggable.alignment = input.alignment\n  );\n  return {\n    draggable: trackedDraggable,\n    get isDragging() {\n      return trackedDraggable.isDragging;\n    },\n    get isDropping() {\n      return trackedDraggable.isDropping;\n    },\n    get isDragSource() {\n      return trackedDraggable.isDragSource;\n    },\n    handleRef: useCallback(\n      (element2) => {\n        draggable.handle = element2 != null ? element2 : void 0;\n      },\n      [draggable]\n    ),\n    ref: useCallback(\n      (element2) => {\n        var _a2, _b;\n        if (!element2 && ((_a2 = draggable.element) == null ? void 0 : _a2.isConnected) && !((_b = draggable.manager) == null ? void 0 : _b.dragOperation.status.idle)) {\n          return;\n        }\n        draggable.element = element2 != null ? element2 : void 0;\n      },\n      [draggable]\n    )\n  };\n}\nfunction shouldUpdateSynchronously(key, oldValue, newValue) {\n  if (key === \"isDragSource\" && !newValue && oldValue) return true;\n  return false;\n}\nfunction DragOverlay({\n  children,\n  className,\n  style,\n  tag,\n  disabled\n}) {\n  var _a2;\n  const ref = useRef(null);\n  const manager = useDragDropManager();\n  const source = (_a2 = useComputed(() => manager == null ? void 0 : manager.dragOperation.source, [manager]).value) != null ? _a2 : null;\n  const isDisabled = typeof disabled === \"function\" ? disabled(source) : disabled;\n  useEffect(() => {\n    if (!ref.current || !manager || isDisabled) return;\n    const feedback = manager.plugins.find(\n      (plugin) => plugin instanceof Feedback\n    );\n    if (!feedback) return;\n    feedback.overlay = ref.current;\n    return () => {\n      feedback.overlay = void 0;\n    };\n  }, [manager, isDisabled]);\n  const patchedManager = useMemo(() => {\n    if (!manager) return null;\n    const patchedRegistry = new Proxy(manager.registry, {\n      get(target, property) {\n        if (property === \"register\" || property === \"unregister\") {\n          return noop;\n        }\n        return target[property];\n      }\n    });\n    return new Proxy(manager, {\n      get(target, property) {\n        if (property === \"registry\") {\n          return patchedRegistry;\n        }\n        return target[property];\n      }\n    });\n  }, [manager]);\n  return /* @__PURE__ */ jsx(DragDropContext.Provider, { value: patchedManager, children: createElement(\n    tag || \"div\",\n    { ref, className, style, \"data-dnd-overlay\": true },\n    renderChildren()\n  ) });\n  function renderChildren() {\n    if (!source || isDisabled) return null;\n    if (typeof children === \"function\") {\n      return /* @__PURE__ */ jsx(Children, { source, children });\n    }\n    return children;\n  }\n}\nfunction noop() {\n  return () => {\n  };\n}\nfunction Children({\n  children,\n  source\n}) {\n  return children(useDeepSignal(source));\n}\nvar __create = Object.create;\nvar __defProp2 = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __knownSymbol = (name, symbol) => (symbol = Symbol[name]) ? symbol : Symbol.for(\"Symbol.\" + name);\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n};\nvar __defNormalProp2 = (obj, key, value) => key in obj ? __defProp2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __decoratorStart = (base) => {\n  var _a2;\n  return [, , , __create((_a2 = base == null ? void 0 : base[__knownSymbol(\"metadata\")]) != null ? _a2 : null)];\n};\nvar __decoratorStrings = [\"class\", \"method\", \"getter\", \"setter\", \"accessor\", \"field\", \"value\", \"get\", \"set\"];\nvar __expectFn = (fn) => fn !== void 0 && typeof fn !== \"function\" ? __typeError(\"Function expected\") : fn;\nvar __decoratorContext = (kind, name, done, metadata, fns) => ({ kind: __decoratorStrings[kind], name, metadata, addInitializer: (fn) => done._ ? __typeError(\"Already initialized\") : fns.push(__expectFn(fn || null)) });\nvar __decoratorMetadata = (array, target) => __defNormalProp2(target, __knownSymbol(\"metadata\"), array[3]);\nvar __runInitializers = (array, flags, self, value) => {\n  for (var i = 0, fns = array[flags >> 1], n = fns && fns.length; i < n; i++) fns[i].call(self) ;\n  return value;\n};\nvar __decorateElement = (array, flags, name, decorators, target, extra) => {\n  var it, done, ctx, access, k = flags & 7, s = false, p = false;\n  var j = 2 , key = __decoratorStrings[k + 5];\n  var extraInitializers = array[j] || (array[j] = []);\n  var desc = ((target = target.prototype), __getOwnPropDesc(target , name));\n  for (var i = decorators.length - 1; i >= 0; i--) {\n    ctx = __decoratorContext(k, name, done = {}, array[3], extraInitializers);\n    {\n      ctx.static = s, ctx.private = p, access = ctx.access = { has: (x) => name in x };\n      access.get = (x) => x[name];\n    }\n    it = (0, decorators[i])(desc[key]  , ctx), done._ = 1;\n    __expectFn(it) && (desc[key] = it );\n  }\n  return desc && __defProp2(target, name, desc), target;\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), member.set(obj, value), value);\nvar Point = class _Point {\n  /**\n   * @param {number} Coordinate of the point on the horizontal axis\n   * @param {number} Coordinate of the point on the vertical axis\n   */\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n  /**\n   * Returns the delta between this point and another point.\n   *\n   * @param {Point} a - A point\n   * @param {Point} b - Another point\n   */\n  static delta(a, b) {\n    return new _Point(a.x - b.x, a.y - b.y);\n  }\n  /**\n   * Returns the distance (hypotenuse) between this point and another point.\n   *\n   * @param {Point} a - A point\n   * @param {Point} b - Another point\n   */\n  static distance(a, b) {\n    return Math.hypot(a.x - b.x, a.y - b.y);\n  }\n  /**\n   * Returns true if both points are equal.\n   *\n   * @param {Point} a - A point\n   * @param {Point} b - Another point\n   */\n  static equals(a, b) {\n    return a.x === b.x && a.y === b.y;\n  }\n  static from({ x, y }) {\n    return new _Point(x, y);\n  }\n};\nvar _direction_dec;\nvar _delta_dec;\nvar _a;\nvar _timestamp;\nvar _init;\nvar Position = class extends (_a = ValueHistory, _delta_dec = [derived], _direction_dec = [derived], _a) {\n  constructor(initialValue) {\n    const point = Point.from(initialValue);\n    super(point, (a, b) => Point.equals(a, b));\n    __runInitializers(_init, 5, this);\n    __privateAdd(this, _timestamp, 0);\n    this.velocity = { x: 0, y: 0 };\n  }\n  get delta() {\n    return Point.delta(this.current, this.initial);\n  }\n  get direction() {\n    const { current, previous } = this;\n    if (!previous) return null;\n    const delta = {\n      x: current.x - previous.x,\n      y: current.y - previous.y\n    };\n    if (!delta.x && !delta.y) {\n      return null;\n    }\n    if (Math.abs(delta.x) > Math.abs(delta.y)) {\n      return delta.x > 0 ? \"right\" : \"left\";\n    }\n    return delta.y > 0 ? \"down\" : \"up\";\n  }\n  get current() {\n    return super.current;\n  }\n  set current(coordinates) {\n    const { current } = this;\n    const point = Point.from(coordinates);\n    const delta = {\n      x: point.x - current.x,\n      y: point.y - current.y\n    };\n    const timestamp = Date.now();\n    const timeDelta = timestamp - __privateGet(this, _timestamp);\n    const velocity = (delta2) => Math.round(delta2 / timeDelta * 100);\n    batch(() => {\n      __privateSet(this, _timestamp, timestamp);\n      this.velocity = {\n        x: velocity(delta.x),\n        y: velocity(delta.y)\n      };\n      super.current = point;\n    });\n  }\n  reset(coordinates = this.defaultValue) {\n    super.reset(Point.from(coordinates));\n    this.velocity = { x: 0, y: 0 };\n  }\n};\n_init = __decoratorStart(_a);\n_timestamp = /* @__PURE__ */ new WeakMap();\n__decorateElement(_init, 2, \"delta\", _delta_dec, Position);\n__decorateElement(_init, 2, \"direction\", _direction_dec, Position);\n__decoratorMetadata(_init, Position);\nvar Axis = /* @__PURE__ */ ((Axis2) => {\n  Axis2[\"Horizontal\"] = \"x\";\n  Axis2[\"Vertical\"] = \"y\";\n  return Axis2;\n})(Axis || {});\nObject.values(Axis);\nvar pointerIntersection = ({\n  dragOperation,\n  droppable\n}) => {\n  const pointerCoordinates = dragOperation.position.current;\n  if (!pointerCoordinates) {\n    return null;\n  }\n  const { id } = droppable;\n  if (!droppable.shape) {\n    return null;\n  }\n  if (droppable.shape.containsPoint(pointerCoordinates)) {\n    const distance = Point.distance(droppable.shape.center, pointerCoordinates);\n    return {\n      id,\n      value: 1 / distance,\n      type: CollisionType.PointerIntersection,\n      priority: CollisionPriority.High\n    };\n  }\n  return null;\n};\nvar shapeIntersection = ({\n  dragOperation,\n  droppable\n}) => {\n  const { shape } = dragOperation;\n  if (!droppable.shape || !(shape == null ? void 0 : shape.current)) {\n    return null;\n  }\n  const intersectionArea = shape.current.intersectionArea(droppable.shape);\n  if (intersectionArea) {\n    const { position } = dragOperation;\n    const distance = Point.distance(droppable.shape.center, position.current);\n    const intersectionRatio = intersectionArea / (shape.current.area + droppable.shape.area - intersectionArea);\n    const value = intersectionRatio / distance;\n    return {\n      id: droppable.id,\n      value,\n      type: CollisionType.ShapeIntersection,\n      priority: CollisionPriority.Normal\n    };\n  }\n  return null;\n};\nvar defaultCollisionDetection = (args) => {\n  var _a2;\n  return (_a2 = pointerIntersection(args)) != null ? _a2 : shapeIntersection(args);\n};\n\n// src/core/droppable/useDroppable.ts\nfunction useDroppable(input) {\n  const { collisionDetector, data, disabled, element, id, accept, type } = input;\n  const droppable = useInstance(\n    (manager) => new Droppable(\n      __spreadProps(__spreadValues({}, input), {\n        register: false,\n        element: currentValue(element)\n      }),\n      manager\n    )\n  );\n  const trackedDroppalbe = useDeepSignal(droppable);\n  useOnValueChange(id, () => droppable.id = id);\n  useOnElementChange(element, (element2) => droppable.element = element2);\n  useOnValueChange(\n    accept,\n    () => droppable.accept = accept,\n    void 0,\n    deepEqual\n  );\n  useOnValueChange(\n    collisionDetector,\n    () => droppable.collisionDetector = collisionDetector != null ? collisionDetector : defaultCollisionDetection\n  );\n  useOnValueChange(data, () => data && (droppable.data = data));\n  useOnValueChange(disabled, () => droppable.disabled = disabled === true);\n  useOnValueChange(type, () => droppable.type = type);\n  return {\n    droppable: trackedDroppalbe,\n    get isDropTarget() {\n      return trackedDroppalbe.isDropTarget;\n    },\n    ref: useCallback(\n      (element2) => {\n        var _a2, _b;\n        if (!element2 && ((_a2 = droppable.element) == null ? void 0 : _a2.isConnected) && !((_b = droppable.manager) == null ? void 0 : _b.dragOperation.status.idle)) {\n          return;\n        }\n        droppable.element = element2 != null ? element2 : void 0;\n      },\n      [droppable]\n    )\n  };\n}\nfunction useDragDropMonitor(handlers) {\n  const manager = useDragDropManager();\n  useEffect(() => {\n    if (!manager) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.warn(\n          \"useDndMonitor hook was called outside of a DragDropProvider. Make sure your app is wrapped in a DragDropProvider component.\"\n        );\n      }\n      return;\n    }\n    const cleanupFns = Object.entries(handlers).reduce(\n      (acc, [handlerName, handler]) => {\n        if (handler) {\n          const eventName = handlerName.replace(/^on/, \"\").toLowerCase();\n          const unsubscribe = manager.monitor.addEventListener(\n            eventName,\n            handler\n          );\n          acc.push(unsubscribe);\n        }\n        return acc;\n      },\n      []\n    );\n    return () => cleanupFns.forEach((cleanup) => cleanup == null ? void 0 : cleanup());\n  }, [manager, handlers]);\n}\nfunction useDragOperation() {\n  const manager = useDragDropManager();\n  const source = useComputed(() => manager == null ? void 0 : manager.dragOperation.source, [manager]);\n  const target = useComputed(() => manager == null ? void 0 : manager.dragOperation.target, [manager]);\n  return {\n    get source() {\n      return source.value;\n    },\n    get target() {\n      return target.value;\n    }\n  };\n}\n\nexport { DragDropProvider, DragOverlay, useDragDropManager, useDragDropMonitor, useDragOperation, useDraggable, useDroppable, useInstance };\n//# sourceMappingURL=index.js.map\n//# sourceMappingURL=index.js.map"], "mappings": "AAAA,SAASA,aAAa,EAAEC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,aAAa,QAAQ,OAAO;AAC3K,SAASC,eAAe,EAAEC,aAAa,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,cAAc;AAC7F,SAASC,cAAc,EAAEC,aAAa,QAAQ,cAAc;AAC5D,SAASC,yBAAyB,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,WAAW,QAAQ,sBAAsB;AAC7I,SAASC,SAAS,EAAEC,YAAY,EAAEC,OAAO,EAAEC,KAAK,QAAQ,gBAAgB;AACxE,SAASC,IAAI,EAAEC,GAAG,QAAQ,mBAAmB;AAC7C,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,mBAAmB;AAEpE,IAAIC,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,UAAU,GAAGF,MAAM,CAACG,gBAAgB;AACxC,IAAIC,iBAAiB,GAAGJ,MAAM,CAACK,yBAAyB;AACxD,IAAIC,mBAAmB,GAAGN,MAAM,CAACO,qBAAqB;AACtD,IAAIC,YAAY,GAAGR,MAAM,CAACS,SAAS,CAACC,cAAc;AAClD,IAAIC,YAAY,GAAGX,MAAM,CAACS,SAAS,CAACG,oBAAoB;AACxD,IAAIC,eAAe,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAID,GAAG,GAAGf,SAAS,CAACe,GAAG,EAAEC,GAAG,EAAE;EAAEE,UAAU,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEH;AAAM,CAAC,CAAC,GAAGF,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;AAC/J,IAAII,cAAc,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC7B,KAAK,IAAIC,IAAI,IAAID,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC,EAC5B,IAAId,YAAY,CAACgB,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC,IAAIjB,mBAAmB,EACrB,KAAK,IAAIiB,IAAI,IAAIjB,mBAAmB,CAACgB,CAAC,CAAC,EAAE;IACvC,IAAIX,YAAY,CAACa,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC;EACF,OAAOF,CAAC;AACV,CAAC;AACD,IAAII,aAAa,GAAGA,CAACJ,CAAC,EAAEC,CAAC,KAAKpB,UAAU,CAACmB,CAAC,EAAEjB,iBAAiB,CAACkB,CAAC,CAAC,CAAC;AACjE,IAAII,SAAS,GAAGA,CAACC,MAAM,EAAEC,OAAO,KAAK;EACnC,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAIN,IAAI,IAAII,MAAM,EACrB,IAAInB,YAAY,CAACgB,IAAI,CAACG,MAAM,EAAEJ,IAAI,CAAC,IAAIK,OAAO,CAACE,OAAO,CAACP,IAAI,CAAC,GAAG,CAAC,EAC9DM,MAAM,CAACN,IAAI,CAAC,GAAGI,MAAM,CAACJ,IAAI,CAAC;EAC/B,IAAII,MAAM,IAAI,IAAI,IAAIrB,mBAAmB,EACvC,KAAK,IAAIiB,IAAI,IAAIjB,mBAAmB,CAACqB,MAAM,CAAC,EAAE;IAC5C,IAAIC,OAAO,CAACE,OAAO,CAACP,IAAI,CAAC,GAAG,CAAC,IAAIZ,YAAY,CAACa,IAAI,CAACG,MAAM,EAAEJ,IAAI,CAAC,EAC9DM,MAAM,CAACN,IAAI,CAAC,GAAGI,MAAM,CAACJ,IAAI,CAAC;EAC/B;EACF,OAAOM,MAAM;AACf,CAAC;AACD,IAAIE,cAAc,GAAG,IAAItD,eAAe,CAAC,CAAC;AAC1C,IAAIuD,eAAe,GAAGnE,aAAa,CACjCkE,cACF,CAAC;AACD,IAAIE,QAAQ,GAAGnE,IAAI,CACjBC,UAAU,CAAC,CAAAmE,IAAA,EAAeC,GAAG,KAAK;EAAA,IAAtB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EACtB,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAMuE,SAAS,GAAGtE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMuE,QAAQ,GAAGvE,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMwE,QAAQ,GAAGvE,OAAO,CACtB,OAAO;IACLuE,QAAQ,EAAE;MACR,IAAIF,SAASA,CAAA,EAAG;QACd,IAAIG,GAAG;QACP,OAAO,CAACA,GAAG,GAAGH,SAAS,CAACI,OAAO,KAAK,IAAI,GAAGD,GAAG,GAAGE,OAAO,CAACC,OAAO,CAAC,CAAC;MACpE;IACF,CAAC;IACDC,cAAcA,CAACC,QAAQ,EAAE;MACvB,IAAI,CAACR,SAAS,CAACI,OAAO,EAAE;QACtBJ,SAAS,CAACI,OAAO,GAAG,IAAIC,OAAO,CAAEC,OAAO,IAAK;UAC3CL,QAAQ,CAACG,OAAO,GAAGE,OAAO;QAC5B,CAAC,CAAC;MACJ;MACA1E,eAAe,CAAC,MAAM;QACpB4E,QAAQ,CAAC,CAAC;QACVT,kBAAkB,CAAEU,KAAK,IAAKA,KAAK,GAAG,CAAC,CAAC;MAC1C,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,EACF,EACF,CAAC;EACDhE,yBAAyB,CAAC,MAAM;IAC9B,IAAI0D,GAAG;IACP,CAACA,GAAG,GAAGF,QAAQ,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,GAAG,CAAClB,IAAI,CAACgB,QAAQ,CAAC;IAC9DD,SAAS,CAACI,OAAO,GAAG,IAAI;EAC1B,CAAC,EAAE,CAACP,QAAQ,EAAEC,eAAe,CAAC,CAAC;EAC/BjE,mBAAmB,CAAC+D,GAAG,EAAE,MAAMM,QAAQ,CAAC;EACxC,OAAO,IAAI;AACb,CAAC,CACH,CAAC;AACD,IAAIQ,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE3D,SAAS,CAAC;AACjC,SAAS4D,gBAAgBA,CAACR,GAAG,EAAE;EAC7B,IAAIS,EAAE,GAAGT,GAAG;IAAE;MACZN,QAAQ;MACRgB,WAAW;MACXC,iBAAiB;MACjBC,WAAW;MACXC,UAAU;MACVC,UAAU;MACVC;IACF,CAAC,GAAGN,EAAE;IAAEO,KAAK,GAAGhC,SAAS,CAACyB,EAAE,EAAE,CAC5B,UAAU,EACV,aAAa,EACb,mBAAmB,EACnB,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,WAAW,CACZ,CAAC;EACF,IAAIQ,GAAG;EACP,MAAMC,WAAW,GAAG3F,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM,CAAC4F,OAAO,EAAEC,UAAU,CAAC,GAAG9F,QAAQ,CAAC,CAAC2F,GAAG,GAAGD,KAAK,CAACG,OAAO,KAAK,IAAI,GAAGF,GAAG,GAAG,IAAI,CAAC;EAClF,MAAM;IAAEI,OAAO;IAAEC,SAAS;IAAEC;EAAQ,CAAC,GAAGP,KAAK;EAC7C,MAAMQ,qBAAqB,GAAGjF,SAAS,CAACoE,iBAAiB,CAAC;EAC1D,MAAMc,eAAe,GAAGlF,SAAS,CAACqE,WAAW,CAAC;EAC9C,MAAMc,cAAc,GAAGnF,SAAS,CAACuE,UAAU,CAAC;EAC5C,MAAMa,cAAc,GAAGpF,SAAS,CAACsE,UAAU,CAAC;EAC5C,MAAMe,aAAa,GAAGrF,SAAS,CAACwE,SAAS,CAAC;EAC1C,MAAMc,eAAe,GAAGtF,SAAS,CAACmE,WAAW,CAAC;EAC9C/E,SAAS,CAAC,MAAM;IACd,IAAImG,GAAG;IACP,IAAI,CAACZ,WAAW,CAACjB,OAAO,EAAE,MAAM,IAAI8B,KAAK,CAAC,oBAAoB,CAAC;IAC/D,MAAM;MAAEhC,QAAQ;MAAEK;IAAe,CAAC,GAAGc,WAAW,CAACjB,OAAO;IACxD,MAAM+B,QAAQ,GAAG,CAACF,GAAG,GAAGd,KAAK,CAACG,OAAO,KAAK,IAAI,GAAGW,GAAG,GAAG,IAAI/F,eAAe,CAACiF,KAAK,CAAC;IACjFgB,QAAQ,CAACjC,QAAQ,GAAGA,QAAQ;IAC5BiC,QAAQ,CAACC,OAAO,CAACC,gBAAgB,CAAC,iBAAiB,EAAGC,KAAK,IAAK;MAC9D,MAAM9B,QAAQ,GAAGmB,qBAAqB,CAACvB,OAAO;MAC9C,IAAII,QAAQ,EAAE;QACZD,cAAc,CAAC,MAAMC,QAAQ,CAAC8B,KAAK,EAAEH,QAAQ,CAAC,CAAC;MACjD;IACF,CAAC,CAAC;IACFA,QAAQ,CAACC,OAAO,CAACC,gBAAgB,CAC/B,WAAW,EACVC,KAAK,IAAK;MACT,IAAIC,GAAG;MACP,OAAO,CAACA,GAAG,GAAGX,eAAe,CAACxB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmC,GAAG,CAACtD,IAAI,CAAC2C,eAAe,EAAEU,KAAK,EAAEH,QAAQ,CAAC;IACtG,CACF,CAAC;IACDA,QAAQ,CAACC,OAAO,CAACC,gBAAgB,CAAC,UAAU,EAAGC,KAAK,IAAK;MACvD,MAAM9B,QAAQ,GAAGqB,cAAc,CAACzB,OAAO;MACvC,IAAII,QAAQ,EAAE;QACZD,cAAc,CAAC,MAAMC,QAAQ,CAAC8B,KAAK,EAAEH,QAAQ,CAAC,CAAC;MACjD;IACF,CAAC,CAAC;IACFA,QAAQ,CAACC,OAAO,CAACC,gBAAgB,CAAC,UAAU,EAAGC,KAAK,IAAK;MACvD,MAAM9B,QAAQ,GAAGsB,cAAc,CAAC1B,OAAO;MACvC,IAAII,QAAQ,EAAE;QACZD,cAAc,CAAC,MAAMC,QAAQ,CAAC8B,KAAK,EAAEH,QAAQ,CAAC,CAAC;MACjD;IACF,CAAC,CAAC;IACFA,QAAQ,CAACC,OAAO,CAACC,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAK;MACtD,MAAM9B,QAAQ,GAAGuB,aAAa,CAAC3B,OAAO;MACtC,IAAII,QAAQ,EAAE;QACZD,cAAc,CAAC,MAAMC,QAAQ,CAAC8B,KAAK,EAAEH,QAAQ,CAAC,CAAC;MACjD;IACF,CAAC,CAAC;IACFA,QAAQ,CAACC,OAAO,CAACC,gBAAgB,CAC/B,WAAW,EACVC,KAAK,IAAK;MACT,IAAIC,GAAG;MACP,OAAO,CAACA,GAAG,GAAGP,eAAe,CAAC5B,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmC,GAAG,CAACtD,IAAI,CAAC+C,eAAe,EAAEM,KAAK,EAAEH,QAAQ,CAAC;IACtG,CACF,CAAC;IACDvG,eAAe,CAAC,MAAM2F,UAAU,CAACY,QAAQ,CAAC,CAAC;IAC3C,OAAOA,QAAQ,CAACK,OAAO;EACzB,CAAC,EAAE,CAACrB,KAAK,CAACG,OAAO,CAAC,CAAC;EACnB3E,gBAAgB,CACd6E,OAAO,EACP,MAAMF,OAAO,KAAKA,OAAO,CAACE,OAAO,GAAGA,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAGrF,aAAa,CAACqF,OAAO,CAAC,EACtF,GAAGd,OACL,CAAC;EACD/D,gBAAgB,CACd+E,OAAO,EACP,MAAMJ,OAAO,KAAKA,OAAO,CAACI,OAAO,GAAGA,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAGvF,aAAa,CAACuF,OAAO,CAAC,EACtF,GAAGhB,OACL,CAAC;EACD/D,gBAAgB,CACd8E,SAAS,EACT,MAAMH,OAAO,KAAKA,OAAO,CAACG,SAAS,GAAGA,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGtF,aAAa,CAACsF,SAAS,CAAC,EAC9F,GAAGf,OACL,CAAC;EACD,OAAO,eAAgBvD,IAAI,CAACsC,eAAe,CAACgD,QAAQ,EAAE;IAAEhE,KAAK,EAAE6C,OAAO;IAAEzB,QAAQ,EAAE,CAChF,eAAgBzC,GAAG,CAACsC,QAAQ,EAAE;MAAEE,GAAG,EAAEyB,WAAW;MAAExB;IAAS,CAAC,CAAC,EAC7DA,QAAQ;EACR,CAAC,CAAC;AACN;AACA,SAAS6C,kBAAkBA,CAAA,EAAG;EAC5B,OAAO3G,UAAU,CAAC0D,eAAe,CAAC;AACpC;;AAEA;AACA,SAASkD,WAAWA,CAACC,WAAW,EAAE;EAChC,IAAIzC,GAAG;EACP,MAAMmB,OAAO,GAAG,CAACnB,GAAG,GAAGuC,kBAAkB,CAAC,CAAC,KAAK,IAAI,GAAGvC,GAAG,GAAG,KAAK,CAAC;EACnE,MAAM,CAAC0C,QAAQ,CAAC,GAAGpH,QAAQ,CAAC,MAAMmH,WAAW,CAACtB,OAAO,CAAC,CAAC;EACvD,IAAIuB,QAAQ,CAACvB,OAAO,KAAKA,OAAO,EAAE;IAChCuB,QAAQ,CAACvB,OAAO,GAAGA,OAAO;EAC5B;EACA7E,yBAAyB,CAACoG,QAAQ,CAACC,QAAQ,EAAE,CAACxB,OAAO,EAAEuB,QAAQ,CAAC,CAAC;EACjE,OAAOA,QAAQ;AACjB;;AAEA;AACA,SAASE,YAAYA,CAAC5B,KAAK,EAAE;EAC3B,MAAM;IAAE6B,QAAQ;IAAEC,IAAI;IAAEC,OAAO;IAAEC,MAAM;IAAEC,EAAE;IAAE3B,SAAS;IAAEC;EAAQ,CAAC,GAAGP,KAAK;EACzE,MAAMkC,SAAS,GAAGV,WAAW,CAC1BrB,OAAO,IAAK,IAAIlF,SAAS,CACxB8C,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEsC,KAAK,CAAC,EAAE;IACvC2B,QAAQ,EAAE,KAAK;IACfK,MAAM,EAAE9F,YAAY,CAAC8F,MAAM,CAAC;IAC5BD,OAAO,EAAE7F,YAAY,CAAC6F,OAAO;EAC/B,CAAC,CAAC,EACF5B,OACF,CACF,CAAC;EACD,MAAMgC,gBAAgB,GAAG1G,aAAa,CAACyG,SAAS,EAAEE,yBAAyB,CAAC;EAC5E5G,gBAAgB,CAACyG,EAAE,EAAE,MAAMC,SAAS,CAACD,EAAE,GAAGA,EAAE,CAAC;EAC7CvG,kBAAkB,CAACsG,MAAM,EAAGK,OAAO,IAAKH,SAAS,CAACF,MAAM,GAAGK,OAAO,CAAC;EACnE3G,kBAAkB,CAACqG,OAAO,EAAGO,QAAQ,IAAKJ,SAAS,CAACH,OAAO,GAAGO,QAAQ,CAAC;EACvE9G,gBAAgB,CAACsG,IAAI,EAAE,MAAMA,IAAI,KAAKI,SAAS,CAACJ,IAAI,GAAGA,IAAI,CAAC,CAAC;EAC7DtG,gBAAgB,CAACqG,QAAQ,EAAE,MAAMK,SAAS,CAACL,QAAQ,GAAGA,QAAQ,KAAK,IAAI,CAAC;EACxErG,gBAAgB,CAAC+E,OAAO,EAAE,MAAM2B,SAAS,CAAC3B,OAAO,GAAGA,OAAO,CAAC;EAC5D/E,gBAAgB,CACd8E,SAAS,EACT,MAAM4B,SAAS,CAAC5B,SAAS,GAAGA,SAAS,EACrC,KAAK,CAAC,EACN1E,SACF,CAAC;EACDJ,gBAAgB,CACdwE,KAAK,CAACuC,QAAQ,EACd,MAAM;IACJ,IAAIvD,GAAG;IACP,OAAOkD,SAAS,CAACK,QAAQ,GAAG,CAACvD,GAAG,GAAGgB,KAAK,CAACuC,QAAQ,KAAK,IAAI,GAAGvD,GAAG,GAAG,SAAS;EAC9E,CACF,CAAC;EACDxD,gBAAgB,CACdwE,KAAK,CAACwC,SAAS,EACf,MAAMN,SAAS,CAACM,SAAS,GAAGxC,KAAK,CAACwC,SACpC,CAAC;EACD,OAAO;IACLN,SAAS,EAAEC,gBAAgB;IAC3B,IAAIM,UAAUA,CAAA,EAAG;MACf,OAAON,gBAAgB,CAACM,UAAU;IACpC,CAAC;IACD,IAAIC,UAAUA,CAAA,EAAG;MACf,OAAOP,gBAAgB,CAACO,UAAU;IACpC,CAAC;IACD,IAAIC,YAAYA,CAAA,EAAG;MACjB,OAAOR,gBAAgB,CAACQ,YAAY;IACtC,CAAC;IACDC,SAAS,EAAE/H,WAAW,CACnByH,QAAQ,IAAK;MACZJ,SAAS,CAACF,MAAM,GAAGM,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,KAAK,CAAC;IACzD,CAAC,EACD,CAACJ,SAAS,CACZ,CAAC;IACDzD,GAAG,EAAE5D,WAAW,CACbyH,QAAQ,IAAK;MACZ,IAAItD,GAAG,EAAES,EAAE;MACX,IAAI,CAAC6C,QAAQ,KAAK,CAACtD,GAAG,GAAGkD,SAAS,CAACH,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG/C,GAAG,CAAC6D,WAAW,CAAC,IAAI,EAAE,CAACpD,EAAE,GAAGyC,SAAS,CAAC/B,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGV,EAAE,CAACqD,aAAa,CAACC,MAAM,CAACC,IAAI,CAAC,EAAE;QAC9J;MACF;MACAd,SAAS,CAACH,OAAO,GAAGO,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,KAAK,CAAC;IAC1D,CAAC,EACD,CAACJ,SAAS,CACZ;EACF,CAAC;AACH;AACA,SAASE,yBAAyBA,CAAC/E,GAAG,EAAE4F,QAAQ,EAAEC,QAAQ,EAAE;EAC1D,IAAI7F,GAAG,KAAK,cAAc,IAAI,CAAC6F,QAAQ,IAAID,QAAQ,EAAE,OAAO,IAAI;EAChE,OAAO,KAAK;AACd;AACA,SAASE,WAAWA,CAAAC,KAAA,EAMjB;EAAA,IANkB;IACnB1E,QAAQ;IACR2E,SAAS;IACTC,KAAK;IACLC,GAAG;IACH1B;EACF,CAAC,GAAAuB,KAAA;EACC,IAAIpE,GAAG;EACP,MAAMP,GAAG,GAAGlE,MAAM,CAAC,IAAI,CAAC;EACxB,MAAM4F,OAAO,GAAGoB,kBAAkB,CAAC,CAAC;EACpC,MAAMtD,MAAM,GAAG,CAACe,GAAG,GAAGrD,WAAW,CAAC,MAAMwE,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2C,aAAa,CAAC7E,MAAM,EAAE,CAACkC,OAAO,CAAC,CAAC,CAAC7C,KAAK,KAAK,IAAI,GAAG0B,GAAG,GAAG,IAAI;EACvI,MAAMwE,UAAU,GAAG,OAAO3B,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC5D,MAAM,CAAC,GAAG4D,QAAQ;EAC/ElH,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8D,GAAG,CAACQ,OAAO,IAAI,CAACkB,OAAO,IAAIqD,UAAU,EAAE;IAC5C,MAAMjB,QAAQ,GAAGpC,OAAO,CAACE,OAAO,CAACoD,IAAI,CAClCC,MAAM,IAAKA,MAAM,YAAYxI,QAChC,CAAC;IACD,IAAI,CAACqH,QAAQ,EAAE;IACfA,QAAQ,CAACoB,OAAO,GAAGlF,GAAG,CAACQ,OAAO;IAC9B,OAAO,MAAM;MACXsD,QAAQ,CAACoB,OAAO,GAAG,KAAK,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACxD,OAAO,EAAEqD,UAAU,CAAC,CAAC;EACzB,MAAMI,cAAc,GAAGpJ,OAAO,CAAC,MAAM;IACnC,IAAI,CAAC2F,OAAO,EAAE,OAAO,IAAI;IACzB,MAAM0D,eAAe,GAAG,IAAIC,KAAK,CAAC3D,OAAO,CAAC4D,QAAQ,EAAE;MAClDC,GAAGA,CAAC7F,MAAM,EAAE8F,QAAQ,EAAE;QACpB,IAAIA,QAAQ,KAAK,UAAU,IAAIA,QAAQ,KAAK,YAAY,EAAE;UACxD,OAAOC,IAAI;QACb;QACA,OAAO/F,MAAM,CAAC8F,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC;IACF,OAAO,IAAIH,KAAK,CAAC3D,OAAO,EAAE;MACxB6D,GAAGA,CAAC7F,MAAM,EAAE8F,QAAQ,EAAE;QACpB,IAAIA,QAAQ,KAAK,UAAU,EAAE;UAC3B,OAAOJ,eAAe;QACxB;QACA,OAAO1F,MAAM,CAAC8F,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9D,OAAO,CAAC,CAAC;EACb,OAAO,eAAgBlE,GAAG,CAACqC,eAAe,CAACgD,QAAQ,EAAE;IAAEhE,KAAK,EAAEsG,cAAc;IAAElF,QAAQ,EAAE5D,aAAa,CACnGyI,GAAG,IAAI,KAAK,EACZ;MAAE9E,GAAG;MAAE4E,SAAS;MAAEC,KAAK;MAAE,kBAAkB,EAAE;IAAK,CAAC,EACnDa,cAAc,CAAC,CACjB;EAAE,CAAC,CAAC;EACJ,SAASA,cAAcA,CAAA,EAAG;IACxB,IAAI,CAAClG,MAAM,IAAIuF,UAAU,EAAE,OAAO,IAAI;IACtC,IAAI,OAAO9E,QAAQ,KAAK,UAAU,EAAE;MAClC,OAAO,eAAgBzC,GAAG,CAACmI,QAAQ,EAAE;QAAEnG,MAAM;QAAES;MAAS,CAAC,CAAC;IAC5D;IACA,OAAOA,QAAQ;EACjB;AACF;AACA,SAASwF,IAAIA,CAAA,EAAG;EACd,OAAO,MAAM,CACb,CAAC;AACH;AACA,SAASE,QAAQA,CAAAC,KAAA,EAGd;EAAA,IAHe;IAChB3F,QAAQ;IACRT;EACF,CAAC,GAAAoG,KAAA;EACC,OAAO3F,QAAQ,CAACjD,aAAa,CAACwC,MAAM,CAAC,CAAC;AACxC;AACA,IAAIqG,QAAQ,GAAGhI,MAAM,CAACiI,MAAM;AAC5B,IAAIC,UAAU,GAAGlI,MAAM,CAACC,cAAc;AACtC,IAAIkI,gBAAgB,GAAGnI,MAAM,CAACoI,wBAAwB;AACtD,IAAIC,aAAa,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK,CAACA,MAAM,GAAGC,MAAM,CAACF,IAAI,CAAC,IAAIC,MAAM,GAAGC,MAAM,CAACC,GAAG,CAAC,SAAS,GAAGH,IAAI,CAAC;AACrG,IAAII,WAAW,GAAIC,GAAG,IAAK;EACzB,MAAMC,SAAS,CAACD,GAAG,CAAC;AACtB,CAAC;AACD,IAAIE,gBAAgB,GAAGA,CAAC/H,GAAG,EAAEC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAID,GAAG,GAAGoH,UAAU,CAACpH,GAAG,EAAEC,GAAG,EAAE;EAAEE,UAAU,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEH;AAAM,CAAC,CAAC,GAAGF,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;AACjK,IAAI8H,gBAAgB,GAAIC,IAAI,IAAK;EAC/B,IAAIrG,GAAG;EACP,OAAO,KAAOsF,QAAQ,CAAC,CAACtF,GAAG,GAAGqG,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACV,aAAa,CAAC,UAAU,CAAC,CAAC,KAAK,IAAI,GAAG3F,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/G,CAAC;AACD,IAAIsG,kBAAkB,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;AAC5G,IAAIC,UAAU,GAAIC,EAAE,IAAKA,EAAE,KAAK,KAAK,CAAC,IAAI,OAAOA,EAAE,KAAK,UAAU,GAAGR,WAAW,CAAC,mBAAmB,CAAC,GAAGQ,EAAE;AAC1G,IAAIC,kBAAkB,GAAGA,CAACC,IAAI,EAAEd,IAAI,EAAEe,IAAI,EAAEC,QAAQ,EAAEC,GAAG,MAAM;EAAEH,IAAI,EAAEJ,kBAAkB,CAACI,IAAI,CAAC;EAAEd,IAAI;EAAEgB,QAAQ;EAAEE,cAAc,EAAGN,EAAE,IAAKG,IAAI,CAACI,CAAC,GAAGf,WAAW,CAAC,qBAAqB,CAAC,GAAGa,GAAG,CAACG,IAAI,CAACT,UAAU,CAACC,EAAE,IAAI,IAAI,CAAC;AAAE,CAAC,CAAC;AAC1N,IAAIS,mBAAmB,GAAGA,CAACC,KAAK,EAAE/H,MAAM,KAAKgH,gBAAgB,CAAChH,MAAM,EAAEwG,aAAa,CAAC,UAAU,CAAC,EAAEuB,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1G,IAAIC,iBAAiB,GAAGA,CAACD,KAAK,EAAEE,KAAK,EAAEC,IAAI,EAAE/I,KAAK,KAAK;EACrD,KAAK,IAAIgJ,CAAC,GAAG,CAAC,EAAET,GAAG,GAAGK,KAAK,CAACE,KAAK,IAAI,CAAC,CAAC,EAAEG,CAAC,GAAGV,GAAG,IAAIA,GAAG,CAACW,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAET,GAAG,CAACS,CAAC,CAAC,CAACxI,IAAI,CAACuI,IAAI,CAAC;EAC7F,OAAO/I,KAAK;AACd,CAAC;AACD,IAAImJ,iBAAiB,GAAGA,CAACP,KAAK,EAAEE,KAAK,EAAExB,IAAI,EAAE8B,UAAU,EAAEvI,MAAM,EAAEwI,KAAK,KAAK;EACzE,IAAIC,EAAE;IAAEjB,IAAI;IAAEkB,GAAG;IAAEC,MAAM;IAAEC,CAAC,GAAGX,KAAK,GAAG,CAAC;IAAEY,CAAC,GAAG,KAAK;IAAEC,CAAC,GAAG,KAAK;EAC9D,IAAIC,CAAC,GAAG,CAAC;IAAG7J,GAAG,GAAGiI,kBAAkB,CAACyB,CAAC,GAAG,CAAC,CAAC;EAC3C,IAAII,iBAAiB,GAAGjB,KAAK,CAACgB,CAAC,CAAC,KAAKhB,KAAK,CAACgB,CAAC,CAAC,GAAG,EAAE,CAAC;EACnD,IAAIE,IAAI,IAAKjJ,MAAM,GAAGA,MAAM,CAACpB,SAAS,EAAG0H,gBAAgB,CAACtG,MAAM,EAAGyG,IAAI,CAAC,CAAC;EACzE,KAAK,IAAI0B,CAAC,GAAGI,UAAU,CAACF,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC/CO,GAAG,GAAGpB,kBAAkB,CAACsB,CAAC,EAAEnC,IAAI,EAAEe,IAAI,GAAG,CAAC,CAAC,EAAEO,KAAK,CAAC,CAAC,CAAC,EAAEiB,iBAAiB,CAAC;IACzE;MACEN,GAAG,CAACQ,MAAM,GAAGL,CAAC,EAAEH,GAAG,CAACS,OAAO,GAAGL,CAAC,EAAEH,MAAM,GAAGD,GAAG,CAACC,MAAM,GAAG;QAAES,GAAG,EAAGC,CAAC,IAAK5C,IAAI,IAAI4C;MAAE,CAAC;MAChFV,MAAM,CAAC9C,GAAG,GAAIwD,CAAC,IAAKA,CAAC,CAAC5C,IAAI,CAAC;IAC7B;IACAgC,EAAE,GAAG,CAAC,CAAC,EAAEF,UAAU,CAACJ,CAAC,CAAC,EAAEc,IAAI,CAAC/J,GAAG,CAAC,EAAIwJ,GAAG,CAAC,EAAElB,IAAI,CAACI,CAAC,GAAG,CAAC;IACrDR,UAAU,CAACqB,EAAE,CAAC,KAAKQ,IAAI,CAAC/J,GAAG,CAAC,GAAGuJ,EAAE,CAAE;EACrC;EACA,OAAOQ,IAAI,IAAI5C,UAAU,CAACrG,MAAM,EAAEyG,IAAI,EAAEwC,IAAI,CAAC,EAAEjJ,MAAM;AACvD,CAAC;AACD,IAAIsJ,aAAa,GAAGA,CAACrK,GAAG,EAAEsK,MAAM,EAAEzC,GAAG,KAAKyC,MAAM,CAACH,GAAG,CAACnK,GAAG,CAAC,IAAI4H,WAAW,CAAC,SAAS,GAAGC,GAAG,CAAC;AACzF,IAAI0C,YAAY,GAAGA,CAACvK,GAAG,EAAEsK,MAAM,EAAEE,MAAM,MAAMH,aAAa,CAACrK,GAAG,EAAEsK,MAAM,EAAE,yBAAyB,CAAC,EAAEA,MAAM,CAAC1D,GAAG,CAAC5G,GAAG,CAAC,CAAC;AACpH,IAAIyK,YAAY,GAAGA,CAACzK,GAAG,EAAEsK,MAAM,EAAEpK,KAAK,KAAKoK,MAAM,CAACH,GAAG,CAACnK,GAAG,CAAC,GAAG4H,WAAW,CAAC,mDAAmD,CAAC,GAAG0C,MAAM,YAAYI,OAAO,GAAGJ,MAAM,CAACK,GAAG,CAAC3K,GAAG,CAAC,GAAGsK,MAAM,CAACM,GAAG,CAAC5K,GAAG,EAAEE,KAAK,CAAC;AACpM,IAAI2K,YAAY,GAAGA,CAAC7K,GAAG,EAAEsK,MAAM,EAAEpK,KAAK,EAAE4K,MAAM,MAAMT,aAAa,CAACrK,GAAG,EAAEsK,MAAM,EAAE,wBAAwB,CAAC,EAAEA,MAAM,CAACM,GAAG,CAAC5K,GAAG,EAAEE,KAAK,CAAC,EAAEA,KAAK,CAAC;AACxI,IAAI6K,KAAK,GAAG,MAAMC,MAAM,CAAC;EACvB;AACF;AACA;AACA;EACEC,WAAWA,CAACb,CAAC,EAAEc,CAAC,EAAE;IAChB,IAAI,CAACd,CAAC,GAAGA,CAAC;IACV,IAAI,CAACc,CAAC,GAAGA,CAAC;EACZ;EACA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAKA,CAAC5K,CAAC,EAAEC,CAAC,EAAE;IACjB,OAAO,IAAIwK,MAAM,CAACzK,CAAC,CAAC6J,CAAC,GAAG5J,CAAC,CAAC4J,CAAC,EAAE7J,CAAC,CAAC2K,CAAC,GAAG1K,CAAC,CAAC0K,CAAC,CAAC;EACzC;EACA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOE,QAAQA,CAAC7K,CAAC,EAAEC,CAAC,EAAE;IACpB,OAAO6K,IAAI,CAACC,KAAK,CAAC/K,CAAC,CAAC6J,CAAC,GAAG5J,CAAC,CAAC4J,CAAC,EAAE7J,CAAC,CAAC2K,CAAC,GAAG1K,CAAC,CAAC0K,CAAC,CAAC;EACzC;EACA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOK,MAAMA,CAAChL,CAAC,EAAEC,CAAC,EAAE;IAClB,OAAOD,CAAC,CAAC6J,CAAC,KAAK5J,CAAC,CAAC4J,CAAC,IAAI7J,CAAC,CAAC2K,CAAC,KAAK1K,CAAC,CAAC0K,CAAC;EACnC;EACA,OAAOM,IAAIA,CAAAC,KAAA,EAAW;IAAA,IAAV;MAAErB,CAAC;MAAEc;IAAE,CAAC,GAAAO,KAAA;IAClB,OAAO,IAAIT,MAAM,CAACZ,CAAC,EAAEc,CAAC,CAAC;EACzB;AACF,CAAC;AACD,IAAIQ,cAAc;AAClB,IAAIC,UAAU;AACd,IAAIC,EAAE;AACN,IAAIC,UAAU;AACd,IAAIC,KAAK;AACT,IAAIC,QAAQ,GAAG,eAAeH,EAAE,GAAGnN,YAAY,EAAEkN,UAAU,GAAG,CAACjN,OAAO,CAAC,EAAEgN,cAAc,GAAG,CAAChN,OAAO,CAAC,EAAEkN,EAAE,EAAE;EACvGX,WAAWA,CAACe,YAAY,EAAE;IACxB,MAAMC,KAAK,GAAGlB,KAAK,CAACS,IAAI,CAACQ,YAAY,CAAC;IACtC,KAAK,CAACC,KAAK,EAAE,CAAC1L,CAAC,EAAEC,CAAC,KAAKuK,KAAK,CAACQ,MAAM,CAAChL,CAAC,EAAEC,CAAC,CAAC,CAAC;IAC1CuI,iBAAiB,CAAC+C,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC;IACjCrB,YAAY,CAAC,IAAI,EAAEoB,UAAU,EAAE,CAAC,CAAC;IACjC,IAAI,CAACK,QAAQ,GAAG;MAAE9B,CAAC,EAAE,CAAC;MAAEc,CAAC,EAAE;IAAE,CAAC;EAChC;EACA,IAAIC,KAAKA,CAAA,EAAG;IACV,OAAOJ,KAAK,CAACI,KAAK,CAAC,IAAI,CAACtJ,OAAO,EAAE,IAAI,CAACsK,OAAO,CAAC;EAChD;EACA,IAAIC,SAASA,CAAA,EAAG;IACd,MAAM;MAAEvK,OAAO;MAAEwK;IAAS,CAAC,GAAG,IAAI;IAClC,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;IAC1B,MAAMlB,KAAK,GAAG;MACZf,CAAC,EAAEvI,OAAO,CAACuI,CAAC,GAAGiC,QAAQ,CAACjC,CAAC;MACzBc,CAAC,EAAErJ,OAAO,CAACqJ,CAAC,GAAGmB,QAAQ,CAACnB;IAC1B,CAAC;IACD,IAAI,CAACC,KAAK,CAACf,CAAC,IAAI,CAACe,KAAK,CAACD,CAAC,EAAE;MACxB,OAAO,IAAI;IACb;IACA,IAAIG,IAAI,CAACiB,GAAG,CAACnB,KAAK,CAACf,CAAC,CAAC,GAAGiB,IAAI,CAACiB,GAAG,CAACnB,KAAK,CAACD,CAAC,CAAC,EAAE;MACzC,OAAOC,KAAK,CAACf,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;IACvC;IACA,OAAOe,KAAK,CAACD,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI;EACpC;EACA,IAAIrJ,OAAOA,CAAA,EAAG;IACZ,OAAO,KAAK,CAACA,OAAO;EACtB;EACA,IAAIA,OAAOA,CAAC0K,WAAW,EAAE;IACvB,MAAM;MAAE1K;IAAQ,CAAC,GAAG,IAAI;IACxB,MAAMoK,KAAK,GAAGlB,KAAK,CAACS,IAAI,CAACe,WAAW,CAAC;IACrC,MAAMpB,KAAK,GAAG;MACZf,CAAC,EAAE6B,KAAK,CAAC7B,CAAC,GAAGvI,OAAO,CAACuI,CAAC;MACtBc,CAAC,EAAEe,KAAK,CAACf,CAAC,GAAGrJ,OAAO,CAACqJ;IACvB,CAAC;IACD,MAAMsB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMC,SAAS,GAAGH,SAAS,GAAGjC,YAAY,CAAC,IAAI,EAAEsB,UAAU,CAAC;IAC5D,MAAMK,QAAQ,GAAIU,MAAM,IAAKvB,IAAI,CAACwB,KAAK,CAACD,MAAM,GAAGD,SAAS,GAAG,GAAG,CAAC;IACjEhO,KAAK,CAAC,MAAM;MACVkM,YAAY,CAAC,IAAI,EAAEgB,UAAU,EAAEW,SAAS,CAAC;MACzC,IAAI,CAACN,QAAQ,GAAG;QACd9B,CAAC,EAAE8B,QAAQ,CAACf,KAAK,CAACf,CAAC,CAAC;QACpBc,CAAC,EAAEgB,QAAQ,CAACf,KAAK,CAACD,CAAC;MACrB,CAAC;MACD,KAAK,CAACrJ,OAAO,GAAGoK,KAAK;IACvB,CAAC,CAAC;EACJ;EACAa,KAAKA,CAAA,EAAkC;IAAA,IAAjCP,WAAW,GAAAQ,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI,CAACE,YAAY;IACnC,KAAK,CAACH,KAAK,CAAC/B,KAAK,CAACS,IAAI,CAACe,WAAW,CAAC,CAAC;IACpC,IAAI,CAACL,QAAQ,GAAG;MAAE9B,CAAC,EAAE,CAAC;MAAEc,CAAC,EAAE;IAAE,CAAC;EAChC;AACF,CAAC;AACDY,KAAK,GAAG9D,gBAAgB,CAAC4D,EAAE,CAAC;AAC5BC,UAAU,GAAG,eAAgB,IAAIqB,OAAO,CAAC,CAAC;AAC1C7D,iBAAiB,CAACyC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAEH,UAAU,EAAEI,QAAQ,CAAC;AAC1D1C,iBAAiB,CAACyC,KAAK,EAAE,CAAC,EAAE,WAAW,EAAEJ,cAAc,EAAEK,QAAQ,CAAC;AAClElD,mBAAmB,CAACiD,KAAK,EAAEC,QAAQ,CAAC;AACpC,IAAIoB,IAAI,GAAG,eAAgB,CAAEC,KAAK,IAAK;EACrCA,KAAK,CAAC,YAAY,CAAC,GAAG,GAAG;EACzBA,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG;EACvB,OAAOA,KAAK;AACd,CAAC,EAAED,IAAI,IAAI,CAAC,CAAC,CAAC;AACdjO,MAAM,CAACmO,MAAM,CAACF,IAAI,CAAC;AACnB,IAAIG,mBAAmB,GAAGC,KAAA,IAGpB;EAAA,IAHqB;IACzB7H,aAAa;IACb8H;EACF,CAAC,GAAAD,KAAA;EACC,MAAME,kBAAkB,GAAG/H,aAAa,CAACgI,QAAQ,CAAC7L,OAAO;EACzD,IAAI,CAAC4L,kBAAkB,EAAE;IACvB,OAAO,IAAI;EACb;EACA,MAAM;IAAE5I;EAAG,CAAC,GAAG2I,SAAS;EACxB,IAAI,CAACA,SAAS,CAACG,KAAK,EAAE;IACpB,OAAO,IAAI;EACb;EACA,IAAIH,SAAS,CAACG,KAAK,CAACC,aAAa,CAACH,kBAAkB,CAAC,EAAE;IACrD,MAAMrC,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACoC,SAAS,CAACG,KAAK,CAACE,MAAM,EAAEJ,kBAAkB,CAAC;IAC3E,OAAO;MACL5I,EAAE;MACF3E,KAAK,EAAE,CAAC,GAAGkL,QAAQ;MACnB0C,IAAI,EAAE9O,aAAa,CAAC+O,mBAAmB;MACvCC,QAAQ,EAAEjP,iBAAiB,CAACkP;IAC9B,CAAC;EACH;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIC,iBAAiB,GAAGC,KAAA,IAGlB;EAAA,IAHmB;IACvBzI,aAAa;IACb8H;EACF,CAAC,GAAAW,KAAA;EACC,MAAM;IAAER;EAAM,CAAC,GAAGjI,aAAa;EAC/B,IAAI,CAAC8H,SAAS,CAACG,KAAK,IAAI,EAAEA,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC9L,OAAO,CAAC,EAAE;IACjE,OAAO,IAAI;EACb;EACA,MAAMuM,gBAAgB,GAAGT,KAAK,CAAC9L,OAAO,CAACuM,gBAAgB,CAACZ,SAAS,CAACG,KAAK,CAAC;EACxE,IAAIS,gBAAgB,EAAE;IACpB,MAAM;MAAEV;IAAS,CAAC,GAAGhI,aAAa;IAClC,MAAM0F,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACoC,SAAS,CAACG,KAAK,CAACE,MAAM,EAAEH,QAAQ,CAAC7L,OAAO,CAAC;IACzE,MAAMwM,iBAAiB,GAAGD,gBAAgB,IAAIT,KAAK,CAAC9L,OAAO,CAACyM,IAAI,GAAGd,SAAS,CAACG,KAAK,CAACW,IAAI,GAAGF,gBAAgB,CAAC;IAC3G,MAAMlO,KAAK,GAAGmO,iBAAiB,GAAGjD,QAAQ;IAC1C,OAAO;MACLvG,EAAE,EAAE2I,SAAS,CAAC3I,EAAE;MAChB3E,KAAK;MACL4N,IAAI,EAAE9O,aAAa,CAACuP,iBAAiB;MACrCP,QAAQ,EAAEjP,iBAAiB,CAACyP;IAC9B,CAAC;EACH;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIC,yBAAyB,GAAIC,IAAI,IAAK;EACxC,IAAI9M,GAAG;EACP,OAAO,CAACA,GAAG,GAAG0L,mBAAmB,CAACoB,IAAI,CAAC,KAAK,IAAI,GAAG9M,GAAG,GAAGsM,iBAAiB,CAACQ,IAAI,CAAC;AAClF,CAAC;;AAED;AACA,SAASC,YAAYA,CAAC/L,KAAK,EAAE;EAC3B,MAAM;IAAEgM,iBAAiB;IAAElK,IAAI;IAAED,QAAQ;IAAEE,OAAO;IAAEE,EAAE;IAAEgK,MAAM;IAAEf;EAAK,CAAC,GAAGlL,KAAK;EAC9E,MAAM4K,SAAS,GAAGpJ,WAAW,CAC1BrB,OAAO,IAAK,IAAIhF,SAAS,CACxB4C,aAAa,CAACL,cAAc,CAAC,CAAC,CAAC,EAAEsC,KAAK,CAAC,EAAE;IACvC2B,QAAQ,EAAE,KAAK;IACfI,OAAO,EAAE7F,YAAY,CAAC6F,OAAO;EAC/B,CAAC,CAAC,EACF5B,OACF,CACF,CAAC;EACD,MAAM+L,gBAAgB,GAAGzQ,aAAa,CAACmP,SAAS,CAAC;EACjDpP,gBAAgB,CAACyG,EAAE,EAAE,MAAM2I,SAAS,CAAC3I,EAAE,GAAGA,EAAE,CAAC;EAC7CvG,kBAAkB,CAACqG,OAAO,EAAGO,QAAQ,IAAKsI,SAAS,CAAC7I,OAAO,GAAGO,QAAQ,CAAC;EACvE9G,gBAAgB,CACdyQ,MAAM,EACN,MAAMrB,SAAS,CAACqB,MAAM,GAAGA,MAAM,EAC/B,KAAK,CAAC,EACNrQ,SACF,CAAC;EACDJ,gBAAgB,CACdwQ,iBAAiB,EACjB,MAAMpB,SAAS,CAACoB,iBAAiB,GAAGA,iBAAiB,IAAI,IAAI,GAAGA,iBAAiB,GAAGH,yBACtF,CAAC;EACDrQ,gBAAgB,CAACsG,IAAI,EAAE,MAAMA,IAAI,KAAK8I,SAAS,CAAC9I,IAAI,GAAGA,IAAI,CAAC,CAAC;EAC7DtG,gBAAgB,CAACqG,QAAQ,EAAE,MAAM+I,SAAS,CAAC/I,QAAQ,GAAGA,QAAQ,KAAK,IAAI,CAAC;EACxErG,gBAAgB,CAAC0P,IAAI,EAAE,MAAMN,SAAS,CAACM,IAAI,GAAGA,IAAI,CAAC;EACnD,OAAO;IACLN,SAAS,EAAEsB,gBAAgB;IAC3B,IAAIC,YAAYA,CAAA,EAAG;MACjB,OAAOD,gBAAgB,CAACC,YAAY;IACtC,CAAC;IACD1N,GAAG,EAAE5D,WAAW,CACbyH,QAAQ,IAAK;MACZ,IAAItD,GAAG,EAAES,EAAE;MACX,IAAI,CAAC6C,QAAQ,KAAK,CAACtD,GAAG,GAAG4L,SAAS,CAAC7I,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG/C,GAAG,CAAC6D,WAAW,CAAC,IAAI,EAAE,CAACpD,EAAE,GAAGmL,SAAS,CAACzK,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGV,EAAE,CAACqD,aAAa,CAACC,MAAM,CAACC,IAAI,CAAC,EAAE;QAC9J;MACF;MACA4H,SAAS,CAAC7I,OAAO,GAAGO,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,KAAK,CAAC;IAC1D,CAAC,EACD,CAACsI,SAAS,CACZ;EACF,CAAC;AACH;AACA,SAASwB,kBAAkBA,CAACC,QAAQ,EAAE;EACpC,MAAMlM,OAAO,GAAGoB,kBAAkB,CAAC,CAAC;EACpC5G,SAAS,CAAC,MAAM;IACd,IAAI,CAACwF,OAAO,EAAE;MACZ,IAAImM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,IAAI,CACV,6HACF,CAAC;MACH;MACA;IACF;IACA,MAAMC,UAAU,GAAGrQ,MAAM,CAACsQ,OAAO,CAACP,QAAQ,CAAC,CAACQ,MAAM,CAChD,CAACC,GAAG,EAAAC,KAAA,KAA6B;MAAA,IAA3B,CAACC,WAAW,EAAEC,OAAO,CAAC,GAAAF,KAAA;MAC1B,IAAIE,OAAO,EAAE;QACX,MAAMC,SAAS,GAAGF,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;QAC9D,MAAMC,WAAW,GAAGlN,OAAO,CAACc,OAAO,CAACC,gBAAgB,CAClDgM,SAAS,EACTD,OACF,CAAC;QACDH,GAAG,CAAC9G,IAAI,CAACqH,WAAW,CAAC;MACvB;MACA,OAAOP,GAAG;IACZ,CAAC,EACD,EACF,CAAC;IACD,OAAO,MAAMH,UAAU,CAACW,OAAO,CAAEC,OAAO,IAAKA,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;EACpF,CAAC,EAAE,CAACpN,OAAO,EAAEkM,QAAQ,CAAC,CAAC;AACzB;AACA,SAASmB,gBAAgBA,CAAA,EAAG;EAC1B,MAAMrN,OAAO,GAAGoB,kBAAkB,CAAC,CAAC;EACpC,MAAMtD,MAAM,GAAGtC,WAAW,CAAC,MAAMwE,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2C,aAAa,CAAC7E,MAAM,EAAE,CAACkC,OAAO,CAAC,CAAC;EACpG,MAAMhC,MAAM,GAAGxC,WAAW,CAAC,MAAMwE,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2C,aAAa,CAAC3E,MAAM,EAAE,CAACgC,OAAO,CAAC,CAAC;EACpG,OAAO;IACL,IAAIlC,MAAMA,CAAA,EAAG;MACX,OAAOA,MAAM,CAACX,KAAK;IACrB,CAAC;IACD,IAAIa,MAAMA,CAAA,EAAG;MACX,OAAOA,MAAM,CAACb,KAAK;IACrB;EACF,CAAC;AACH;AAEA,SAASkC,gBAAgB,EAAE2D,WAAW,EAAE5B,kBAAkB,EAAE6K,kBAAkB,EAAEoB,gBAAgB,EAAE5L,YAAY,EAAEmK,YAAY,EAAEvK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}