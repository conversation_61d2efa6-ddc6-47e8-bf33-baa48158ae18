{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The CalendarPickerSkeleton component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { CalendarPickerSkeleton } from '@mui/x-date-pickers'`\", \"or `import { CalendarPickerSkeleton } from '@mui/x-date-pickers/CalendarPickerSkeleton'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The CalendarPickerSkeleton component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst CalendarPickerSkeleton = /*#__PURE__*/React.forwardRef(function DeprecatedCalendarPickerSkeleton() {\n  warn();\n  return null;\n});\nexport default CalendarPickerSkeleton;\nexport const calendarPickerSkeletonClasses = {};\nexport const getCalendarPickerSkeletonUtilityClass = slot => {\n  warn();\n  return '';\n};", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "CalendarPickerSkeleton", "forwardRef", "DeprecatedCalendarPickerSkeleton", "calendarPickerSkeletonClasses", "getCalendarPickerSkeletonUtilityClass", "slot"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/CalendarPickerSkeleton/CalendarPickerSkeleton.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The CalendarPickerSkeleton component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { CalendarPickerSkeleton } from '@mui/x-date-pickers'`\", \"or `import { CalendarPickerSkeleton } from '@mui/x-date-pickers/CalendarPickerSkeleton'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The CalendarPickerSkeleton component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst CalendarPickerSkeleton = /*#__PURE__*/React.forwardRef(function DeprecatedCalendarPickerSkeleton() {\n  warn();\n  return null;\n});\nexport default CalendarPickerSkeleton;\nexport const calendarPickerSkeletonClasses = {};\nexport const getCalendarPickerSkeletonUtilityClass = slot => {\n  warn();\n  return '';\n};"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,+FAA+F,EAAE,EAAE,EAAE,+EAA+E,EAAE,0FAA0F,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACtZH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,sBAAsB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,gCAAgCA,CAAA,EAAG;EACvGL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,sBAAsB;AACrC,OAAO,MAAMG,6BAA6B,GAAG,CAAC,CAAC;AAC/C,OAAO,MAAMC,qCAAqC,GAAGC,IAAI,IAAI;EAC3DR,IAAI,CAAC,CAAC;EACN,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}