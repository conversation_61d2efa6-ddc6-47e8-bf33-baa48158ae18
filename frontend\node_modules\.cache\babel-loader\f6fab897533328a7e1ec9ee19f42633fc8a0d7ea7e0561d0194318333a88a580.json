{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction toPercent(num) {\n  if (num <= 0) return '100%';\n  if (num < 1) return `${num * 100}%`;\n  return `${num}%`;\n}\nconst Ratio = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    children,\n    aspectRatio = '1x1',\n    style,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'ratio');\n  const isCustomRatio = typeof aspectRatio === 'number';\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...props,\n    style: {\n      ...style,\n      ...(isCustomRatio && {\n        '--bs-aspect-ratio': toPercent(aspectRatio)\n      })\n    },\n    className: classNames(bsPrefix, className, !isCustomRatio && `${bsPrefix}-${aspectRatio}`),\n    children: React.Children.only(children)\n  });\n});\nRatio.displayName = 'Ratio';\nexport default Ratio;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "toPercent", "num", "<PERSON><PERSON>", "forwardRef", "_ref", "ref", "bsPrefix", "className", "children", "aspectRatio", "style", "props", "isCustomRatio", "Children", "only", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Ratio.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction toPercent(num) {\n  if (num <= 0) return '100%';\n  if (num < 1) return `${num * 100}%`;\n  return `${num}%`;\n}\nconst Ratio = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  aspectRatio = '1x1',\n  style,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'ratio');\n  const isCustomRatio = typeof aspectRatio === 'number';\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...props,\n    style: {\n      ...style,\n      ...(isCustomRatio && {\n        '--bs-aspect-ratio': toPercent(aspectRatio)\n      })\n    },\n    className: classNames(bsPrefix, className, !isCustomRatio && `${bsPrefix}-${aspectRatio}`),\n    children: React.Children.only(children)\n  });\n});\nRatio.displayName = 'Ratio';\nexport default Ratio;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,MAAM;EAC3B,IAAIA,GAAG,GAAG,CAAC,EAAE,OAAO,GAAGA,GAAG,GAAG,GAAG,GAAG;EACnC,OAAO,GAAGA,GAAG,GAAG;AAClB;AACA,MAAMC,KAAK,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAOzCC,GAAG,KAAK;EAAA,IAPkC;IAC3CC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACRC,WAAW,GAAG,KAAK;IACnBC,KAAK;IACL,GAAGC;EACL,CAAC,GAAAP,IAAA;EACCE,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,OAAO,CAAC;EAChD,MAAMM,aAAa,GAAG,OAAOH,WAAW,KAAK,QAAQ;EACrD,OAAO,aAAaV,IAAI,CAAC,KAAK,EAAE;IAC9BM,GAAG,EAAEA,GAAG;IACR,GAAGM,KAAK;IACRD,KAAK,EAAE;MACL,GAAGA,KAAK;MACR,IAAIE,aAAa,IAAI;QACnB,mBAAmB,EAAEZ,SAAS,CAACS,WAAW;MAC5C,CAAC;IACH,CAAC;IACDF,SAAS,EAAEZ,UAAU,CAACW,QAAQ,EAAEC,SAAS,EAAE,CAACK,aAAa,IAAI,GAAGN,QAAQ,IAAIG,WAAW,EAAE,CAAC;IAC1FD,QAAQ,EAAEZ,KAAK,CAACiB,QAAQ,CAACC,IAAI,CAACN,QAAQ;EACxC,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,KAAK,CAACa,WAAW,GAAG,OAAO;AAC3B,eAAeb,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}