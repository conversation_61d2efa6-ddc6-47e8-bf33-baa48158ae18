{"ast": null, "code": "export { default } from \"./Snackbar.js\";\nexport { default as snackbarClasses } from \"./snackbarClasses.js\";\nexport * from \"./snackbarClasses.js\";", "map": {"version": 3, "names": ["default", "snackbarClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Snackbar/index.js"], "sourcesContent": ["export { default } from \"./Snackbar.js\";\nexport { default as snackbarClasses } from \"./snackbarClasses.js\";\nexport * from \"./snackbarClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,eAAe,QAAQ,sBAAsB;AACjE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}