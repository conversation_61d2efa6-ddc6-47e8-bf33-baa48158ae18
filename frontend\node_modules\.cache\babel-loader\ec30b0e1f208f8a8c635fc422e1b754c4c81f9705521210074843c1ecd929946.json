{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DatePicker } from '@mui/x-date-pickers'`\", \"or `import { DatePicker } from '@mui/x-date-pickers/DatePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @ignore - do not document.\n */\nconst DatePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDatePicker() {\n  warn();\n  return null;\n});\nexport default DatePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "DatePicker", "forwardRef", "DeprecatedDatePicker"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/DatePicker/DatePicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DatePicker } from '@mui/x-date-pickers'`\", \"or `import { DatePicker } from '@mui/x-date-pickers/DatePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @ignore - do not document.\n */\nconst DatePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDatePicker() {\n  warn();\n  return null;\n});\nexport default DatePicker;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,mFAAmF,EAAE,EAAE,EAAE,mEAAmE,EAAE,kEAAkE,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACtWH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA,MAAMI,UAAU,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,oBAAoBA,CAAA,EAAG;EAC/EL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}