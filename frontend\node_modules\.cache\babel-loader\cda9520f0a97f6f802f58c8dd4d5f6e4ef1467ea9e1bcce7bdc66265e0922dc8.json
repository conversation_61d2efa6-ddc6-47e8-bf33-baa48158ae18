{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst TimelineContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  TimelineContext.displayName = 'TimelineContext';\n}\nexport default TimelineContext;", "map": {"version": 3, "names": ["React", "TimelineContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/Timeline/TimelineContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst TimelineContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  TimelineContext.displayName = 'TimelineContext';\n}\nexport default TimelineContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA,MAAMC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC5D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,eAAe,CAACK,WAAW,GAAG,iBAAiB;AACjD;AACA,eAAeL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}