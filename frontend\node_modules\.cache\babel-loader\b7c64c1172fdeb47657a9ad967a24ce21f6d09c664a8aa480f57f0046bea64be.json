{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Puck}from'@measured/puck';import'@measured/puck/puck.css';import'./PuckEditor.css';import{useParams,useNavigate}from'react-router-dom';import{<PERSON><PERSON><PERSON>,<PERSON><PERSON>,Spin<PERSON>,<PERSON><PERSON>}from'react-bootstrap';import cmsService from'../../services/cmsService';import authService from'../../services/authService';import{suppressResizeObserverErrors}from'../../utils/errorHandlers';// Comprehensive Puck configuration with all essential components\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const puckConfig={components:{// Layout Components\nHero:{label:'Hero Section',defaultProps:{title:'Hero Title',subtitle:'Hero subtitle text',textAlign:'center',minHeight:'400px',backgroundType:'gradient',backgroundColor:'#667eea',backgroundGradient:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',textColor:'#ffffff'},fields:{title:{type:'text',label:'Title'},subtitle:{type:'textarea',label:'Subtitle'},textAlign:{type:'select',label:'Text Alignment',options:[{label:'Left',value:'left'},{label:'Center',value:'center'},{label:'Right',value:'right'}]},minHeight:{type:'text',label:'Min Height (px)'},backgroundType:{type:'select',label:'Background Type',options:[{label:'Solid Color',value:'solid'},{label:'Gradient',value:'gradient'},{label:'Image',value:'image'}]},backgroundColor:{type:'text',label:'Background Color'},backgroundGradient:{type:'text',label:'Background Gradient'},backgroundImage:{type:'text',label:'Background Image URL'},textColor:{type:'text',label:'Text Color'}},render:_ref=>{let{title,subtitle,textAlign,minHeight,backgroundType,backgroundColor,backgroundGradient,backgroundImage,textColor}=_ref;let backgroundStyle={};switch(backgroundType){case'solid':backgroundStyle={backgroundColor:backgroundColor||'#667eea'};break;case'gradient':backgroundStyle={background:backgroundGradient||'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};break;case'image':backgroundStyle={backgroundImage:`url(${backgroundImage})`,backgroundSize:'cover',backgroundPosition:'center',backgroundRepeat:'no-repeat'};break;default:backgroundStyle={background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};}return/*#__PURE__*/_jsx(\"section\",{style:{minHeight:minHeight||'400px',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:textAlign==='center'?'center':textAlign==='right'?'flex-end':'flex-start',textAlign:textAlign||'center',padding:'4rem 2rem',color:textColor||'white',position:'relative',...backgroundStyle},children:/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',zIndex:1,maxWidth:'800px'},children:[/*#__PURE__*/_jsx(\"h1\",{style:{fontSize:'clamp(2rem, 5vw, 3.5rem)',marginBottom:'1rem',fontWeight:'bold',lineHeight:1.2},children:title||'Hero Title'}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'clamp(1rem, 2.5vw, 1.25rem)',opacity:0.9,lineHeight:1.6,marginBottom:'2rem'},children:subtitle||'Hero subtitle text'})]})});}},// Content Components\nText:{label:'Text Block',defaultProps:{content:'<p>Enter your text content here...</p>',textAlign:'left',fontSize:'16px',color:'#333333',lineHeight:'1.6',marginTop:'0px',marginBottom:'20px'},fields:{content:{type:'textarea',label:'Content (HTML)'},textAlign:{type:'select',label:'Text Alignment',options:[{label:'Left',value:'left'},{label:'Center',value:'center'},{label:'Right',value:'right'},{label:'Justify',value:'justify'}]},fontSize:{type:'text',label:'Font Size'},color:{type:'text',label:'Text Color'},lineHeight:{type:'text',label:'Line Height'},marginTop:{type:'text',label:'Margin Top'},marginBottom:{type:'text',label:'Margin Bottom'}},render:_ref2=>{let{content,textAlign,fontSize,color,lineHeight,marginTop,marginBottom}=_ref2;return/*#__PURE__*/_jsx(\"div\",{className:\"text-component\",style:{textAlign:textAlign||'left',fontSize:fontSize||'16px',color:color||'#333333',lineHeight:lineHeight||'1.6',marginTop:marginTop||'0px',marginBottom:marginBottom||'20px',padding:'0 1rem'},children:/*#__PURE__*/_jsx(\"div\",{dangerouslySetInnerHTML:{__html:content||'<p>Enter your text content here...</p>'}})});}},Heading:{label:'Heading',defaultProps:{text:'Heading Text',level:'h2',textAlign:'left',color:'#333333',marginTop:'0px',marginBottom:'20px'},fields:{text:{type:'text',label:'Heading Text'},level:{type:'select',label:'Heading Level',options:[{label:'H1',value:'h1'},{label:'H2',value:'h2'},{label:'H3',value:'h3'},{label:'H4',value:'h4'},{label:'H5',value:'h5'},{label:'H6',value:'h6'}]},textAlign:{type:'select',label:'Text Alignment',options:[{label:'Left',value:'left'},{label:'Center',value:'center'},{label:'Right',value:'right'}]},color:{type:'text',label:'Text Color'},marginTop:{type:'text',label:'Margin Top'},marginBottom:{type:'text',label:'Margin Bottom'}},render:_ref3=>{let{text,level,textAlign,color,marginTop,marginBottom}=_ref3;const HeadingTag=level||'h2';const fontSizes={h1:'2.5rem',h2:'2rem',h3:'1.75rem',h4:'1.5rem',h5:'1.25rem',h6:'1rem'};const fontSize=fontSizes[level]||'2rem';return/*#__PURE__*/React.createElement(HeadingTag,{style:{textAlign:textAlign||'left',color:color||'#333333',fontSize,fontWeight:'bold',marginTop:marginTop||'0px',marginBottom:marginBottom||'20px',padding:'0 1rem',lineHeight:1.2}},text||'Heading Text');}},Button:{label:'Button',defaultProps:{text:'Button Text',href:'#',variant:'primary',size:'md',textAlign:'center',fullWidth:false,target:'_self'},fields:{text:{type:'text',label:'Button Text'},href:{type:'text',label:'Link URL'},variant:{type:'select',label:'Button Style',options:[{label:'Primary',value:'primary'},{label:'Secondary',value:'secondary'},{label:'Success',value:'success'},{label:'Danger',value:'danger'},{label:'Warning',value:'warning'},{label:'Info',value:'info'},{label:'Light',value:'light'},{label:'Dark',value:'dark'}]},size:{type:'select',label:'Button Size',options:[{label:'Small',value:'sm'},{label:'Medium',value:'md'},{label:'Large',value:'lg'}]},textAlign:{type:'select',label:'Alignment',options:[{label:'Left',value:'left'},{label:'Center',value:'center'},{label:'Right',value:'right'}]},fullWidth:{type:'radio',label:'Full Width',options:[{label:'Yes',value:true},{label:'No',value:false}]},target:{type:'select',label:'Link Target',options:[{label:'Same Window',value:'_self'},{label:'New Window',value:'_blank'}]}},render:_ref4=>{let{text,href,variant,size,textAlign,fullWidth,target}=_ref4;const colors={primary:'#007bff',secondary:'#6c757d',success:'#28a745',danger:'#dc3545',warning:'#ffc107',info:'#17a2b8',light:'#f8f9fa',dark:'#343a40'};const textColors={primary:'white',secondary:'white',success:'white',danger:'white',warning:'#212529',info:'white',light:'#212529',dark:'white'};const padding=size==='sm'?'0.5rem 1rem':size==='lg'?'1rem 2rem':'0.75rem 1.5rem';const fontSize=size==='sm'?'0.875rem':size==='lg'?'1.125rem':'1rem';return/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem',textAlign:textAlign||'center'},children:/*#__PURE__*/_jsx(\"a\",{href:href||'#',target:target||'_self',rel:target==='_blank'?'noopener noreferrer':undefined,style:{display:fullWidth?'block':'inline-block',width:fullWidth?'100%':'auto',padding,backgroundColor:colors[variant]||colors.primary,color:textColors[variant]||'white',textDecoration:'none',borderRadius:'0.375rem',fontWeight:'500',fontSize,textAlign:'center',transition:'all 0.2s ease-in-out',border:'none',cursor:'pointer'},onMouseOver:e=>{e.currentTarget.style.opacity='0.9';e.currentTarget.style.transform='translateY(-1px)';},onMouseOut:e=>{e.currentTarget.style.opacity='1';e.currentTarget.style.transform='translateY(0)';},children:text||'Button Text'})});}},Image:{label:'Image',defaultProps:{src:'https://via.placeholder.com/600x400',alt:'Image description',width:'100%',height:'auto',objectFit:'cover',borderRadius:'0px',marginTop:'0px',marginBottom:'20px'},fields:{src:{type:'text',label:'Image URL'},alt:{type:'text',label:'Alt Text'},width:{type:'text',label:'Width'},height:{type:'text',label:'Height'},objectFit:{type:'select',label:'Object Fit',options:[{label:'Cover',value:'cover'},{label:'Contain',value:'contain'},{label:'Fill',value:'fill'},{label:'None',value:'none'},{label:'Scale Down',value:'scale-down'}]},borderRadius:{type:'text',label:'Border Radius'},marginTop:{type:'text',label:'Margin Top'},marginBottom:{type:'text',label:'Margin Bottom'}},render:_ref5=>{let{src,alt,width,height,objectFit,borderRadius,marginTop,marginBottom}=_ref5;return/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem',marginTop:marginTop||'0px',marginBottom:marginBottom||'20px'},children:/*#__PURE__*/_jsx(\"img\",{src:src||'https://via.placeholder.com/600x400',alt:alt||'Image description',style:{width:width||'100%',height:height||'auto',objectFit:objectFit||'cover',borderRadius:borderRadius||'0px',display:'block',maxWidth:'100%'}})});}},Container:{label:'Container',defaultProps:{maxWidth:'lg',padding:'2rem',backgroundColor:'transparent',borderRadius:'0px',marginTop:'0px',marginBottom:'0px'},fields:{maxWidth:{type:'select',label:'Max Width',options:[{label:'Small (540px)',value:'sm'},{label:'Medium (720px)',value:'md'},{label:'Large (960px)',value:'lg'},{label:'Extra Large (1140px)',value:'xl'},{label:'Full Width',value:'fluid'}]},padding:{type:'text',label:'Padding'},backgroundColor:{type:'text',label:'Background Color'},borderRadius:{type:'text',label:'Border Radius'},marginTop:{type:'text',label:'Margin Top'},marginBottom:{type:'text',label:'Margin Bottom'}},render:_ref6=>{let{maxWidth,padding,backgroundColor,borderRadius,marginTop,marginBottom,children}=_ref6;const maxWidths={sm:'540px',md:'720px',lg:'960px',xl:'1140px',fluid:'100%'};return/*#__PURE__*/_jsx(\"div\",{style:{maxWidth:maxWidths[maxWidth]||'960px',margin:'0 auto',padding:padding||'2rem',backgroundColor:backgroundColor||'transparent',borderRadius:borderRadius||'0px',marginTop:marginTop||'0px',marginBottom:marginBottom||'0px'},children:children});}},Spacer:{label:'Spacer',defaultProps:{height:'40px'},fields:{height:{type:'text',label:'Height'}},render:_ref7=>{let{height}=_ref7;return/*#__PURE__*/_jsx(\"div\",{style:{height:height||'40px',width:'100%'}});}},Divider:{label:'Divider',defaultProps:{color:'#e0e0e0',thickness:'1px',style:'solid',marginTop:'20px',marginBottom:'20px'},fields:{color:{type:'text',label:'Color'},thickness:{type:'text',label:'Thickness'},style:{type:'select',label:'Style',options:[{label:'Solid',value:'solid'},{label:'Dashed',value:'dashed'},{label:'Dotted',value:'dotted'}]},marginTop:{type:'text',label:'Margin Top'},marginBottom:{type:'text',label:'Margin Bottom'}},render:_ref8=>{let{color,thickness,style,marginTop,marginBottom}=_ref8;return/*#__PURE__*/_jsx(\"div\",{style:{padding:'0 1rem',marginTop:marginTop||'20px',marginBottom:marginBottom||'20px'},children:/*#__PURE__*/_jsx(\"hr\",{style:{border:'none',borderTop:`${thickness||'1px'} ${style||'solid'} ${color||'#e0e0e0'}`,margin:0}})});}},Card:{label:'Card',defaultProps:{title:'Card Title',content:'<p>Card content goes here...</p>',imageUrl:'',backgroundColor:'#ffffff',borderColor:'#e0e0e0',borderRadius:'8px',padding:'1.5rem',shadow:true},fields:{title:{type:'text',label:'Card Title'},content:{type:'textarea',label:'Card Content (HTML)'},imageUrl:{type:'text',label:'Image URL (optional)'},backgroundColor:{type:'text',label:'Background Color'},borderColor:{type:'text',label:'Border Color'},borderRadius:{type:'text',label:'Border Radius'},padding:{type:'text',label:'Padding'},shadow:{type:'radio',label:'Drop Shadow',options:[{label:'Yes',value:true},{label:'No',value:false}]}},render:_ref9=>{let{title,content,imageUrl,backgroundColor,borderColor,borderRadius,padding,shadow}=_ref9;return/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:backgroundColor||'#ffffff',border:`1px solid ${borderColor||'#e0e0e0'}`,borderRadius:borderRadius||'8px',padding:padding||'1.5rem',boxShadow:shadow?'0 2px 4px rgba(0,0,0,0.1)':'none',overflow:'hidden'},children:[imageUrl&&/*#__PURE__*/_jsx(\"img\",{src:imageUrl,alt:title||'Card image',style:{width:'100%',height:'200px',objectFit:'cover',marginBottom:'1rem',borderRadius:'4px'}}),title&&/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1rem 0',fontSize:'1.25rem',fontWeight:'bold',color:'#333'},children:title}),/*#__PURE__*/_jsx(\"div\",{dangerouslySetInnerHTML:{__html:content||'<p>Card content goes here...</p>'}})]})});}},Columns:{label:'Columns',defaultProps:{columns:2,gap:'2rem',alignItems:'stretch'},fields:{columns:{type:'select',label:'Number of Columns',options:[{label:'2 Columns',value:2},{label:'3 Columns',value:3},{label:'4 Columns',value:4}]},gap:{type:'text',label:'Gap Between Columns'},alignItems:{type:'select',label:'Vertical Alignment',options:[{label:'Stretch',value:'stretch'},{label:'Top',value:'flex-start'},{label:'Center',value:'center'},{label:'Bottom',value:'flex-end'}]}},render:_ref0=>{let{columns,gap,alignItems,children}=_ref0;return/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem'},children:/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gridTemplateColumns:`repeat(${columns||2}, 1fr)`,gap:gap||'2rem',alignItems:alignItems||'stretch'},children:children})});}},List:{label:'List',defaultProps:{items:['List item 1','List item 2','List item 3'],listType:'ul',color:'#333333',fontSize:'16px'},fields:{items:{type:'array',label:'List Items',arrayFields:{item:{type:'text'}}},listType:{type:'select',label:'List Type',options:[{label:'Unordered (bullets)',value:'ul'},{label:'Ordered (numbers)',value:'ol'}]},color:{type:'text',label:'Text Color'},fontSize:{type:'text',label:'Font Size'}},render:_ref1=>{let{items,listType,color,fontSize}=_ref1;const ListTag=listType==='ol'?'ol':'ul';const itemsArray=Array.isArray(items)?items:['List item 1','List item 2','List item 3'];return/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem'},children:/*#__PURE__*/_jsx(ListTag,{style:{color:color||'#333333',fontSize:fontSize||'16px',lineHeight:'1.6',paddingLeft:'1.5rem'},children:itemsArray.map((item,index)=>/*#__PURE__*/_jsx(\"li\",{style:{marginBottom:'0.5rem'},children:item},index))})});}}}};const PuckEditor=_ref10=>{let{pageId:propPageId}=_ref10;const{pageId:routePageId}=useParams();const navigate=useNavigate();const pageId=propPageId||routePageId;const[page,setPage]=useState(null);const[puckData,setPuckData]=useState(null);const[loading,setLoading]=useState(true);const[saving,setSaving]=useState(false);const[error,setError]=useState('');const[isAdmin,setIsAdmin]=useState(false);useEffect(()=>{checkAdminAndLoadPage();},[pageId]);// Handle ResizeObserver errors more gracefully\nuseEffect(()=>{const cleanup=suppressResizeObserverErrors();// Additional error handling for Puck-specific issues\nconst handlePuckError=event=>{// Suppress common Puck-related errors that don't affect functionality\nif(event.message&&(event.message.includes('ResizeObserver')||event.message.includes('Cannot read properties of null')||event.message.includes('Cannot read property')||event.message.includes('reading \\'offsetHeight\\'')||event.message.includes('reading \\'offsetWidth\\''))){event.preventDefault();return false;}return true;};window.addEventListener('error',handlePuckError);return()=>{cleanup();window.removeEventListener('error',handlePuckError);};},[]);const checkAdminAndLoadPage=async()=>{try{setLoading(true);setError('');// Check if user is admin\nconst adminStatus=await authService.checkAdminStatus();if(!adminStatus.is_admin){setError('You must be an administrator to edit pages.');setLoading(false);return;}setIsAdmin(true);// Load page data\nif(pageId){const pageData=await cmsService.getPageById(parseInt(pageId));setPage(pageData);// Set initial Puck data\nif(pageData.editor_type==='puck'&&pageData.puck_data){setPuckData(pageData.puck_data);}else{// Initialize with empty Puck data structure\nsetPuckData({content:[],root:{title:pageData.title,metaDescription:pageData.meta_description||''}});}}}catch(err){console.error('Error loading page:',err);setError(err.message||'Failed to load page data');}finally{setLoading(false);}};const handleSave=async data=>{try{var _data$root,_data$root2;setSaving(true);setError('');if(!page){throw new Error('No page data available');}// Update page with Puck data using the specialized method\nconst updatedPage=await cmsService.updatePageWithPuckData(page.id,data,{title:((_data$root=data.root)===null||_data$root===void 0?void 0:_data$root.title)||page.title,meta_description:((_data$root2=data.root)===null||_data$root2===void 0?void 0:_data$root2.metaDescription)||page.meta_description});// Show success message\nalert('Page published successfully!');// Navigate based on page type\nif(updatedPage.slug==='home'){// For homepage, navigate to root URL to show the updated content\nnavigate('/');}else{// For other pages, navigate to the page URL\nnavigate(`/pages/${updatedPage.slug}`);}}catch(err){console.error('Error saving page:',err);setError(err.message||'Failed to save page');}finally{setSaving(false);}};const handleCancel=()=>{if(page){navigate(`/pages/${page.slug}`);}else{navigate('/');}};if(loading){return/*#__PURE__*/_jsxs(Container,{className:\"py-5 text-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-3\",children:\"Loading page editor...\"})]});}if(error){return/*#__PURE__*/_jsx(Container,{className:\"py-5\",children:/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",children:[/*#__PURE__*/_jsx(Alert.Heading,{children:\"Error\"}),/*#__PURE__*/_jsx(\"p\",{children:error}),/*#__PURE__*/_jsx(Button,{variant:\"outline-danger\",onClick:()=>navigate('/'),children:\"Go Home\"})]})});}if(!isAdmin){return/*#__PURE__*/_jsx(Container,{className:\"py-5\",children:/*#__PURE__*/_jsxs(Alert,{variant:\"warning\",children:[/*#__PURE__*/_jsx(Alert.Heading,{children:\"Access Denied\"}),/*#__PURE__*/_jsx(\"p\",{children:\"You must be an administrator to edit pages.\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline-warning\",onClick:()=>navigate('/'),children:\"Go Home\"})]})});}if(!page||!puckData){return/*#__PURE__*/_jsx(Container,{className:\"py-5\",children:/*#__PURE__*/_jsxs(Alert,{variant:\"warning\",children:[/*#__PURE__*/_jsx(Alert.Heading,{children:\"Page Not Found\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The requested page could not be found.\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline-warning\",onClick:()=>navigate('/'),children:\"Go Home\"})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"puck-editor-container\",style:{height:'100vh',width:'100vw',position:'fixed',top:0,left:0,zIndex:9999},children:[/*#__PURE__*/_jsx(Puck,{config:puckConfig,data:puckData,onPublish:handleSave,onChange:setPuckData,headerTitle:`Editing: ${page.title}`,renderHeaderActions:()=>/*#__PURE__*/_jsxs(\"div\",{className:\"puck-header-actions\",children:[/*#__PURE__*/_jsx(Button,{variant:\"primary\",size:\"sm\",onClick:()=>handleSave(puckData),disabled:saving,children:saving?'Publishing...':'Publish'}),/*#__PURE__*/_jsx(Button,{variant:\"outline-secondary\",size:\"sm\",onClick:handleCancel,disabled:saving,children:\"Cancel\"})]})}),saving&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:10000},children:/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'white',padding:'2rem',borderRadius:'0.5rem'},children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",className:\"me-2\"}),\"Saving page...\"]})})]});};export default PuckEditor;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "useParams", "useNavigate", "Container", "<PERSON><PERSON>", "Spinner", "<PERSON><PERSON>", "cmsService", "authService", "suppressResizeObserverErrors", "jsx", "_jsx", "jsxs", "_jsxs", "puckConfig", "components", "Hero", "label", "defaultProps", "title", "subtitle", "textAlign", "minHeight", "backgroundType", "backgroundColor", "backgroundGradient", "textColor", "fields", "type", "options", "value", "backgroundImage", "render", "_ref", "backgroundStyle", "background", "backgroundSize", "backgroundPosition", "backgroundRepeat", "style", "display", "flexDirection", "justifyContent", "alignItems", "padding", "color", "position", "children", "zIndex", "max<PERSON><PERSON><PERSON>", "fontSize", "marginBottom", "fontWeight", "lineHeight", "opacity", "Text", "content", "marginTop", "_ref2", "className", "dangerouslySetInnerHTML", "__html", "Heading", "text", "level", "_ref3", "HeadingTag", "fontSizes", "h1", "h2", "h3", "h4", "h5", "h6", "createElement", "href", "variant", "size", "fullWidth", "target", "_ref4", "colors", "primary", "secondary", "success", "danger", "warning", "info", "light", "dark", "textColors", "rel", "undefined", "width", "textDecoration", "borderRadius", "transition", "border", "cursor", "onMouseOver", "e", "currentTarget", "transform", "onMouseOut", "Image", "src", "alt", "height", "objectFit", "_ref5", "_ref6", "maxWid<PERSON>", "sm", "md", "lg", "xl", "fluid", "margin", "Spacer", "_ref7", "Divider", "thickness", "_ref8", "borderTop", "Card", "imageUrl", "borderColor", "shadow", "_ref9", "boxShadow", "overflow", "Columns", "columns", "gap", "_ref0", "gridTemplateColumns", "List", "items", "listType", "arrayFields", "item", "_ref1", "ListTag", "itemsArray", "Array", "isArray", "paddingLeft", "map", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref10", "pageId", "propPageId", "routePageId", "navigate", "page", "setPage", "puck<PERSON><PERSON>", "setPuckData", "loading", "setLoading", "saving", "setSaving", "error", "setError", "isAdmin", "setIsAdmin", "checkAdminAndLoadPage", "cleanup", "handlePuckError", "event", "message", "includes", "preventDefault", "window", "addEventListener", "removeEventListener", "adminStatus", "checkAdminStatus", "is_admin", "pageData", "getPageById", "parseInt", "editor_type", "puck_data", "root", "metaDescription", "meta_description", "err", "console", "handleSave", "data", "_data$root", "_data$root2", "Error", "updatedPage", "updatePageWithPuckData", "id", "alert", "slug", "handleCancel", "animation", "role", "onClick", "top", "left", "config", "onPublish", "onChange", "headerTitle", "renderHeaderActions", "disabled", "right", "bottom"], "sources": ["C:/laragon/www/frontend/src/components/puck/PuckEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Puck } from '@measured/puck';\nimport '@measured/puck/puck.css';\nimport './PuckEditor.css';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';\nimport cmsService, { CmsPage } from '../../services/cmsService';\nimport authService from '../../services/authService';\nimport { suppressResizeObserverErrors } from '../../utils/errorHandlers';\n\n// Comprehensive Puck configuration with all essential components\nconst puckConfig = {\n  components: {\n    // Layout Components\n    Hero: {\n      label: 'Hero Section',\n      defaultProps: {\n        title: 'Hero Title',\n        subtitle: 'Hero subtitle text',\n        textAlign: 'center',\n        minHeight: '400px',\n        backgroundType: 'gradient',\n        backgroundColor: '#667eea',\n        backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        textColor: '#ffffff',\n      },\n      fields: {\n        title: { type: 'text' as const, label: 'Title' },\n        subtitle: { type: 'textarea' as const, label: 'Subtitle' },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Text Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n          ],\n        },\n        minHeight: { type: 'text' as const, label: 'Min Height (px)' },\n        backgroundType: {\n          type: 'select' as const,\n          label: 'Background Type',\n          options: [\n            { label: 'Solid Color', value: 'solid' },\n            { label: 'Gradient', value: 'gradient' },\n            { label: 'Image', value: 'image' },\n          ],\n        },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n        backgroundGradient: { type: 'text' as const, label: 'Background Gradient' },\n        backgroundImage: { type: 'text' as const, label: 'Background Image URL' },\n        textColor: { type: 'text' as const, label: 'Text Color' },\n      },\n      render: ({ title, subtitle, textAlign, minHeight, backgroundType, backgroundColor, backgroundGradient, backgroundImage, textColor }: any) => {\n        let backgroundStyle = {};\n\n        switch (backgroundType) {\n          case 'solid':\n            backgroundStyle = { backgroundColor: backgroundColor || '#667eea' };\n            break;\n          case 'gradient':\n            backgroundStyle = { background: backgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };\n            break;\n          case 'image':\n            backgroundStyle = {\n              backgroundImage: `url(${backgroundImage})`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n              backgroundRepeat: 'no-repeat'\n            };\n            break;\n          default:\n            backgroundStyle = { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };\n        }\n\n        return (\n          <section\n            style={{\n              minHeight: minHeight || '400px',\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'center',\n              alignItems: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',\n              textAlign: textAlign || 'center',\n              padding: '4rem 2rem',\n              color: textColor || 'white',\n              position: 'relative',\n              ...backgroundStyle,\n            }}\n          >\n            <div style={{ position: 'relative', zIndex: 1, maxWidth: '800px' }}>\n              <h1 style={{\n                fontSize: 'clamp(2rem, 5vw, 3.5rem)',\n                marginBottom: '1rem',\n                fontWeight: 'bold',\n                lineHeight: 1.2,\n              }}>\n                {title || 'Hero Title'}\n              </h1>\n              <p style={{\n                fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n                opacity: 0.9,\n                lineHeight: 1.6,\n                marginBottom: '2rem',\n              }}>\n                {subtitle || 'Hero subtitle text'}\n              </p>\n            </div>\n          </section>\n        );\n      },\n    },\n    // Content Components\n    Text: {\n      label: 'Text Block',\n      defaultProps: {\n        content: '<p>Enter your text content here...</p>',\n        textAlign: 'left',\n        fontSize: '16px',\n        color: '#333333',\n        lineHeight: '1.6',\n        marginTop: '0px',\n        marginBottom: '20px',\n      },\n      fields: {\n        content: { type: 'textarea' as const, label: 'Content (HTML)' },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Text Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n            { label: 'Justify', value: 'justify' },\n          ],\n        },\n        fontSize: { type: 'text' as const, label: 'Font Size' },\n        color: { type: 'text' as const, label: 'Text Color' },\n        lineHeight: { type: 'text' as const, label: 'Line Height' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ content, textAlign, fontSize, color, lineHeight, marginTop, marginBottom }: any) => (\n        <div\n          className=\"text-component\"\n          style={{\n            textAlign: textAlign || 'left',\n            fontSize: fontSize || '16px',\n            color: color || '#333333',\n            lineHeight: lineHeight || '1.6',\n            marginTop: marginTop || '0px',\n            marginBottom: marginBottom || '20px',\n            padding: '0 1rem',\n          }}\n        >\n          <div dangerouslySetInnerHTML={{ __html: content || '<p>Enter your text content here...</p>' }} />\n        </div>\n      ),\n    },\n\n    Heading: {\n      label: 'Heading',\n      defaultProps: {\n        text: 'Heading Text',\n        level: 'h2',\n        textAlign: 'left',\n        color: '#333333',\n        marginTop: '0px',\n        marginBottom: '20px',\n      },\n      fields: {\n        text: { type: 'text' as const, label: 'Heading Text' },\n        level: {\n          type: 'select' as const,\n          label: 'Heading Level',\n          options: [\n            { label: 'H1', value: 'h1' },\n            { label: 'H2', value: 'h2' },\n            { label: 'H3', value: 'h3' },\n            { label: 'H4', value: 'h4' },\n            { label: 'H5', value: 'h5' },\n            { label: 'H6', value: 'h6' },\n          ],\n        },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Text Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n          ],\n        },\n        color: { type: 'text' as const, label: 'Text Color' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ text, level, textAlign, color, marginTop, marginBottom }: any) => {\n        const HeadingTag = level || 'h2';\n        const fontSizes: { [key: string]: string } = {\n          h1: '2.5rem',\n          h2: '2rem',\n          h3: '1.75rem',\n          h4: '1.5rem',\n          h5: '1.25rem',\n          h6: '1rem',\n        };\n        const fontSize = fontSizes[level] || '2rem';\n\n        return React.createElement(\n          HeadingTag,\n          {\n            style: {\n              textAlign: textAlign || 'left',\n              color: color || '#333333',\n              fontSize,\n              fontWeight: 'bold',\n              marginTop: marginTop || '0px',\n              marginBottom: marginBottom || '20px',\n              padding: '0 1rem',\n              lineHeight: 1.2,\n            }\n          },\n          text || 'Heading Text'\n        );\n      },\n    },\n    Button: {\n      label: 'Button',\n      defaultProps: {\n        text: 'Button Text',\n        href: '#',\n        variant: 'primary',\n        size: 'md',\n        textAlign: 'center',\n        fullWidth: false,\n        target: '_self',\n      },\n      fields: {\n        text: { type: 'text' as const, label: 'Button Text' },\n        href: { type: 'text' as const, label: 'Link URL' },\n        variant: {\n          type: 'select' as const,\n          label: 'Button Style',\n          options: [\n            { label: 'Primary', value: 'primary' },\n            { label: 'Secondary', value: 'secondary' },\n            { label: 'Success', value: 'success' },\n            { label: 'Danger', value: 'danger' },\n            { label: 'Warning', value: 'warning' },\n            { label: 'Info', value: 'info' },\n            { label: 'Light', value: 'light' },\n            { label: 'Dark', value: 'dark' },\n          ],\n        },\n        size: {\n          type: 'select' as const,\n          label: 'Button Size',\n          options: [\n            { label: 'Small', value: 'sm' },\n            { label: 'Medium', value: 'md' },\n            { label: 'Large', value: 'lg' },\n          ],\n        },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n          ],\n        },\n        fullWidth: { type: 'radio' as const, label: 'Full Width', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },\n        target: {\n          type: 'select' as const,\n          label: 'Link Target',\n          options: [\n            { label: 'Same Window', value: '_self' },\n            { label: 'New Window', value: '_blank' },\n          ],\n        },\n      },\n      render: ({ text, href, variant, size, textAlign, fullWidth, target }: any) => {\n        const colors: { [key: string]: string } = {\n          primary: '#007bff',\n          secondary: '#6c757d',\n          success: '#28a745',\n          danger: '#dc3545',\n          warning: '#ffc107',\n          info: '#17a2b8',\n          light: '#f8f9fa',\n          dark: '#343a40',\n        };\n\n        const textColors: { [key: string]: string } = {\n          primary: 'white',\n          secondary: 'white',\n          success: 'white',\n          danger: 'white',\n          warning: '#212529',\n          info: 'white',\n          light: '#212529',\n          dark: 'white',\n        };\n\n        const padding = size === 'sm' ? '0.5rem 1rem' : size === 'lg' ? '1rem 2rem' : '0.75rem 1.5rem';\n        const fontSize = size === 'sm' ? '0.875rem' : size === 'lg' ? '1.125rem' : '1rem';\n\n        return (\n          <div style={{ padding: '1rem', textAlign: textAlign || 'center' }}>\n            <a\n              href={href || '#'}\n              target={target || '_self'}\n              rel={target === '_blank' ? 'noopener noreferrer' : undefined}\n              style={{\n                display: fullWidth ? 'block' : 'inline-block',\n                width: fullWidth ? '100%' : 'auto',\n                padding,\n                backgroundColor: colors[variant] || colors.primary,\n                color: textColors[variant] || 'white',\n                textDecoration: 'none',\n                borderRadius: '0.375rem',\n                fontWeight: '500',\n                fontSize,\n                textAlign: 'center',\n                transition: 'all 0.2s ease-in-out',\n                border: 'none',\n                cursor: 'pointer',\n              }}\n              onMouseOver={(e) => {\n                e.currentTarget.style.opacity = '0.9';\n                e.currentTarget.style.transform = 'translateY(-1px)';\n              }}\n              onMouseOut={(e) => {\n                e.currentTarget.style.opacity = '1';\n                e.currentTarget.style.transform = 'translateY(0)';\n              }}\n            >\n              {text || 'Button Text'}\n            </a>\n          </div>\n        );\n      },\n    },\n    Image: {\n      label: 'Image',\n      defaultProps: {\n        src: 'https://via.placeholder.com/600x400',\n        alt: 'Image description',\n        width: '100%',\n        height: 'auto',\n        objectFit: 'cover',\n        borderRadius: '0px',\n        marginTop: '0px',\n        marginBottom: '20px',\n      },\n      fields: {\n        src: { type: 'text' as const, label: 'Image URL' },\n        alt: { type: 'text' as const, label: 'Alt Text' },\n        width: { type: 'text' as const, label: 'Width' },\n        height: { type: 'text' as const, label: 'Height' },\n        objectFit: {\n          type: 'select' as const,\n          label: 'Object Fit',\n          options: [\n            { label: 'Cover', value: 'cover' },\n            { label: 'Contain', value: 'contain' },\n            { label: 'Fill', value: 'fill' },\n            { label: 'None', value: 'none' },\n            { label: 'Scale Down', value: 'scale-down' },\n          ],\n        },\n        borderRadius: { type: 'text' as const, label: 'Border Radius' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ src, alt, width, height, objectFit, borderRadius, marginTop, marginBottom }: any) => (\n        <div style={{ padding: '1rem', marginTop: marginTop || '0px', marginBottom: marginBottom || '20px' }}>\n          <img\n            src={src || 'https://via.placeholder.com/600x400'}\n            alt={alt || 'Image description'}\n            style={{\n              width: width || '100%',\n              height: height || 'auto',\n              objectFit: objectFit || 'cover',\n              borderRadius: borderRadius || '0px',\n              display: 'block',\n              maxWidth: '100%',\n            }}\n          />\n        </div>\n      ),\n    },\n\n    Container: {\n      label: 'Container',\n      defaultProps: {\n        maxWidth: 'lg',\n        padding: '2rem',\n        backgroundColor: 'transparent',\n        borderRadius: '0px',\n        marginTop: '0px',\n        marginBottom: '0px',\n      },\n      fields: {\n        maxWidth: {\n          type: 'select' as const,\n          label: 'Max Width',\n          options: [\n            { label: 'Small (540px)', value: 'sm' },\n            { label: 'Medium (720px)', value: 'md' },\n            { label: 'Large (960px)', value: 'lg' },\n            { label: 'Extra Large (1140px)', value: 'xl' },\n            { label: 'Full Width', value: 'fluid' },\n          ],\n        },\n        padding: { type: 'text' as const, label: 'Padding' },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n        borderRadius: { type: 'text' as const, label: 'Border Radius' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ maxWidth, padding, backgroundColor, borderRadius, marginTop, marginBottom, children }: any) => {\n        const maxWidths: { [key: string]: string } = {\n          sm: '540px',\n          md: '720px',\n          lg: '960px',\n          xl: '1140px',\n          fluid: '100%',\n        };\n\n        return (\n          <div\n            style={{\n              maxWidth: maxWidths[maxWidth] || '960px',\n              margin: '0 auto',\n              padding: padding || '2rem',\n              backgroundColor: backgroundColor || 'transparent',\n              borderRadius: borderRadius || '0px',\n              marginTop: marginTop || '0px',\n              marginBottom: marginBottom || '0px',\n            }}\n          >\n            {children}\n          </div>\n        );\n      },\n    },\n\n    Spacer: {\n      label: 'Spacer',\n      defaultProps: {\n        height: '40px',\n      },\n      fields: {\n        height: { type: 'text' as const, label: 'Height' },\n      },\n      render: ({ height }: any) => (\n        <div style={{ height: height || '40px', width: '100%' }} />\n      ),\n    },\n\n    Divider: {\n      label: 'Divider',\n      defaultProps: {\n        color: '#e0e0e0',\n        thickness: '1px',\n        style: 'solid',\n        marginTop: '20px',\n        marginBottom: '20px',\n      },\n      fields: {\n        color: { type: 'text' as const, label: 'Color' },\n        thickness: { type: 'text' as const, label: 'Thickness' },\n        style: {\n          type: 'select' as const,\n          label: 'Style',\n          options: [\n            { label: 'Solid', value: 'solid' },\n            { label: 'Dashed', value: 'dashed' },\n            { label: 'Dotted', value: 'dotted' },\n          ],\n        },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ color, thickness, style, marginTop, marginBottom }: any) => (\n        <div style={{ padding: '0 1rem', marginTop: marginTop || '20px', marginBottom: marginBottom || '20px' }}>\n          <hr\n            style={{\n              border: 'none',\n              borderTop: `${thickness || '1px'} ${style || 'solid'} ${color || '#e0e0e0'}`,\n              margin: 0,\n            }}\n          />\n        </div>\n      ),\n    },\n\n    Card: {\n      label: 'Card',\n      defaultProps: {\n        title: 'Card Title',\n        content: '<p>Card content goes here...</p>',\n        imageUrl: '',\n        backgroundColor: '#ffffff',\n        borderColor: '#e0e0e0',\n        borderRadius: '8px',\n        padding: '1.5rem',\n        shadow: true,\n      },\n      fields: {\n        title: { type: 'text' as const, label: 'Card Title' },\n        content: { type: 'textarea' as const, label: 'Card Content (HTML)' },\n        imageUrl: { type: 'text' as const, label: 'Image URL (optional)' },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n        borderColor: { type: 'text' as const, label: 'Border Color' },\n        borderRadius: { type: 'text' as const, label: 'Border Radius' },\n        padding: { type: 'text' as const, label: 'Padding' },\n        shadow: { type: 'radio' as const, label: 'Drop Shadow', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },\n      },\n      render: ({ title, content, imageUrl, backgroundColor, borderColor, borderRadius, padding, shadow }: any) => (\n        <div style={{ padding: '1rem' }}>\n          <div\n            style={{\n              backgroundColor: backgroundColor || '#ffffff',\n              border: `1px solid ${borderColor || '#e0e0e0'}`,\n              borderRadius: borderRadius || '8px',\n              padding: padding || '1.5rem',\n              boxShadow: shadow ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',\n              overflow: 'hidden',\n            }}\n          >\n            {imageUrl && (\n              <img\n                src={imageUrl}\n                alt={title || 'Card image'}\n                style={{\n                  width: '100%',\n                  height: '200px',\n                  objectFit: 'cover',\n                  marginBottom: '1rem',\n                  borderRadius: '4px',\n                }}\n              />\n            )}\n            {title && (\n              <h3 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.25rem',\n                fontWeight: 'bold',\n                color: '#333',\n              }}>\n                {title}\n              </h3>\n            )}\n            <div dangerouslySetInnerHTML={{ __html: content || '<p>Card content goes here...</p>' }} />\n          </div>\n        </div>\n      ),\n    },\n\n    Columns: {\n      label: 'Columns',\n      defaultProps: {\n        columns: 2,\n        gap: '2rem',\n        alignItems: 'stretch',\n      },\n      fields: {\n        columns: {\n          type: 'select' as const,\n          label: 'Number of Columns',\n          options: [\n            { label: '2 Columns', value: 2 },\n            { label: '3 Columns', value: 3 },\n            { label: '4 Columns', value: 4 },\n          ],\n        },\n        gap: { type: 'text' as const, label: 'Gap Between Columns' },\n        alignItems: {\n          type: 'select' as const,\n          label: 'Vertical Alignment',\n          options: [\n            { label: 'Stretch', value: 'stretch' },\n            { label: 'Top', value: 'flex-start' },\n            { label: 'Center', value: 'center' },\n            { label: 'Bottom', value: 'flex-end' },\n          ],\n        },\n      },\n      render: ({ columns, gap, alignItems, children }: any) => (\n        <div style={{ padding: '1rem' }}>\n          <div\n            style={{\n              display: 'grid',\n              gridTemplateColumns: `repeat(${columns || 2}, 1fr)`,\n              gap: gap || '2rem',\n              alignItems: alignItems || 'stretch',\n            }}\n          >\n            {children}\n          </div>\n        </div>\n      ),\n    },\n\n    List: {\n      label: 'List',\n      defaultProps: {\n        items: ['List item 1', 'List item 2', 'List item 3'],\n        listType: 'ul',\n        color: '#333333',\n        fontSize: '16px',\n      },\n      fields: {\n        items: { type: 'array' as const, label: 'List Items', arrayFields: { item: { type: 'text' as const } } },\n        listType: {\n          type: 'select' as const,\n          label: 'List Type',\n          options: [\n            { label: 'Unordered (bullets)', value: 'ul' },\n            { label: 'Ordered (numbers)', value: 'ol' },\n          ],\n        },\n        color: { type: 'text' as const, label: 'Text Color' },\n        fontSize: { type: 'text' as const, label: 'Font Size' },\n      },\n      render: ({ items, listType, color, fontSize }: any) => {\n        const ListTag = listType === 'ol' ? 'ol' : 'ul';\n        const itemsArray = Array.isArray(items) ? items : ['List item 1', 'List item 2', 'List item 3'];\n\n        return (\n          <div style={{ padding: '1rem' }}>\n            <ListTag\n              style={{\n                color: color || '#333333',\n                fontSize: fontSize || '16px',\n                lineHeight: '1.6',\n                paddingLeft: '1.5rem',\n              }}\n            >\n              {itemsArray.map((item: string, index: number) => (\n                <li key={index} style={{ marginBottom: '0.5rem' }}>\n                  {item}\n                </li>\n              ))}\n            </ListTag>\n          </div>\n        );\n      },\n    },\n  },\n};\n\ninterface PuckEditorProps {\n  pageId?: string;\n}\n\nconst PuckEditor: React.FC<PuckEditorProps> = ({ pageId: propPageId }) => {\n  const { pageId: routePageId } = useParams<{ pageId: string }>();\n  const navigate = useNavigate();\n  const pageId = propPageId || routePageId;\n  \n  const [page, setPage] = useState<CmsPage | null>(null);\n  const [puckData, setPuckData] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState<string>('');\n  const [isAdmin, setIsAdmin] = useState(false);\n\n  useEffect(() => {\n    checkAdminAndLoadPage();\n  }, [pageId]);\n\n  // Handle ResizeObserver errors more gracefully\n  useEffect(() => {\n    const cleanup = suppressResizeObserverErrors();\n\n    // Additional error handling for Puck-specific issues\n    const handlePuckError = (event: ErrorEvent) => {\n      // Suppress common Puck-related errors that don't affect functionality\n      if (event.message && (\n        event.message.includes('ResizeObserver') ||\n        event.message.includes('Cannot read properties of null') ||\n        event.message.includes('Cannot read property') ||\n        event.message.includes('reading \\'offsetHeight\\'') ||\n        event.message.includes('reading \\'offsetWidth\\'')\n      )) {\n        event.preventDefault();\n        return false;\n      }\n      return true;\n    };\n\n    window.addEventListener('error', handlePuckError);\n\n    return () => {\n      cleanup();\n      window.removeEventListener('error', handlePuckError);\n    };\n  }, []);\n\n  const checkAdminAndLoadPage = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Check if user is admin\n      const adminStatus = await authService.checkAdminStatus();\n      if (!adminStatus.is_admin) {\n        setError('You must be an administrator to edit pages.');\n        setLoading(false);\n        return;\n      }\n      setIsAdmin(true);\n\n      // Load page data\n      if (pageId) {\n        const pageData = await cmsService.getPageById(parseInt(pageId));\n        setPage(pageData);\n        \n        // Set initial Puck data\n        if (pageData.editor_type === 'puck' && pageData.puck_data) {\n          setPuckData(pageData.puck_data);\n        } else {\n          // Initialize with empty Puck data structure\n          setPuckData({\n            content: [],\n            root: {\n              title: pageData.title,\n              metaDescription: pageData.meta_description || '',\n            },\n          });\n        }\n      }\n    } catch (err: any) {\n      console.error('Error loading page:', err);\n      setError(err.message || 'Failed to load page data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async (data: any) => {\n    try {\n      setSaving(true);\n      setError('');\n\n      if (!page) {\n        throw new Error('No page data available');\n      }\n\n      // Update page with Puck data using the specialized method\n      const updatedPage = await cmsService.updatePageWithPuckData(page.id, data, {\n        title: data.root?.title || page.title,\n        meta_description: data.root?.metaDescription || page.meta_description,\n      });\n\n      // Show success message\n      alert('Page published successfully!');\n\n      // Navigate based on page type\n      if (updatedPage.slug === 'home') {\n        // For homepage, navigate to root URL to show the updated content\n        navigate('/');\n      } else {\n        // For other pages, navigate to the page URL\n        navigate(`/pages/${updatedPage.slug}`);\n      }\n    } catch (err: any) {\n      console.error('Error saving page:', err);\n      setError(err.message || 'Failed to save page');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleCancel = () => {\n    if (page) {\n      navigate(`/pages/${page.slug}`);\n    } else {\n      navigate('/');\n    }\n  };\n\n  if (loading) {\n    return (\n      <Container className=\"py-5 text-center\">\n        <Spinner animation=\"border\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </Spinner>\n        <p className=\"mt-3\">Loading page editor...</p>\n      </Container>\n    );\n  }\n\n  if (error) {\n    return (\n      <Container className=\"py-5\">\n        <Alert variant=\"danger\">\n          <Alert.Heading>Error</Alert.Heading>\n          <p>{error}</p>\n          <Button variant=\"outline-danger\" onClick={() => navigate('/')}>\n            Go Home\n          </Button>\n        </Alert>\n      </Container>\n    );\n  }\n\n  if (!isAdmin) {\n    return (\n      <Container className=\"py-5\">\n        <Alert variant=\"warning\">\n          <Alert.Heading>Access Denied</Alert.Heading>\n          <p>You must be an administrator to edit pages.</p>\n          <Button variant=\"outline-warning\" onClick={() => navigate('/')}>\n            Go Home\n          </Button>\n        </Alert>\n      </Container>\n    );\n  }\n\n  if (!page || !puckData) {\n    return (\n      <Container className=\"py-5\">\n        <Alert variant=\"warning\">\n          <Alert.Heading>Page Not Found</Alert.Heading>\n          <p>The requested page could not be found.</p>\n          <Button variant=\"outline-warning\" onClick={() => navigate('/')}>\n            Go Home\n          </Button>\n        </Alert>\n      </Container>\n    );\n  }\n\n  return (\n    <div className=\"puck-editor-container\" style={{ height: '100vh', width: '100vw', position: 'fixed', top: 0, left: 0, zIndex: 9999 }}>\n      <Puck\n        config={puckConfig}\n        data={puckData}\n        onPublish={handleSave}\n        onChange={setPuckData}\n        headerTitle={`Editing: ${page.title}`}\n        renderHeaderActions={() => (\n          <div className=\"puck-header-actions\">\n            <Button\n              variant=\"primary\"\n              size=\"sm\"\n              onClick={() => handleSave(puckData)}\n              disabled={saving}\n            >\n              {saving ? 'Publishing...' : 'Publish'}\n            </Button>\n            <Button\n              variant=\"outline-secondary\"\n              size=\"sm\"\n              onClick={handleCancel}\n              disabled={saving}\n            >\n              Cancel\n            </Button>\n          </div>\n        )}\n      />\n      {saving && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 10000,\n          }}\n        >\n          <div style={{ backgroundColor: 'white', padding: '2rem', borderRadius: '0.5rem' }}>\n            <Spinner animation=\"border\" className=\"me-2\" />\n            Saving page...\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PuckEditor;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,KAAQ,gBAAgB,CACrC,MAAO,yBAAyB,CAChC,MAAO,kBAAkB,CACzB,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,OAASC,SAAS,CAAEC,KAAK,CAAEC,OAAO,CAAEC,MAAM,KAAQ,iBAAiB,CACnE,MAAO,CAAAC,UAAU,KAAmB,2BAA2B,CAC/D,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,OAASC,4BAA4B,KAAQ,2BAA2B,CAExE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,UAAU,CAAG,CACjBC,UAAU,CAAE,CACV;AACAC,IAAI,CAAE,CACJC,KAAK,CAAE,cAAc,CACrBC,YAAY,CAAE,CACZC,KAAK,CAAE,YAAY,CACnBC,QAAQ,CAAE,oBAAoB,CAC9BC,SAAS,CAAE,QAAQ,CACnBC,SAAS,CAAE,OAAO,CAClBC,cAAc,CAAE,UAAU,CAC1BC,eAAe,CAAE,SAAS,CAC1BC,kBAAkB,CAAE,mDAAmD,CACvEC,SAAS,CAAE,SACb,CAAC,CACDC,MAAM,CAAE,CACNR,KAAK,CAAE,CAAES,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,OAAQ,CAAC,CAChDG,QAAQ,CAAE,CAAEQ,IAAI,CAAE,UAAmB,CAAEX,KAAK,CAAE,UAAW,CAAC,CAC1DI,SAAS,CAAE,CACTO,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,gBAAgB,CACvBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,MAAM,CAAEa,KAAK,CAAE,MAAO,CAAC,CAChC,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,QAAS,CAAC,CACpC,CAAEb,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,OAAQ,CAAC,CAEtC,CAAC,CACDR,SAAS,CAAE,CAAEM,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,iBAAkB,CAAC,CAC9DM,cAAc,CAAE,CACdK,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,iBAAiB,CACxBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,aAAa,CAAEa,KAAK,CAAE,OAAQ,CAAC,CACxC,CAAEb,KAAK,CAAE,UAAU,CAAEa,KAAK,CAAE,UAAW,CAAC,CACxC,CAAEb,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,OAAQ,CAAC,CAEtC,CAAC,CACDN,eAAe,CAAE,CAAEI,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,kBAAmB,CAAC,CACrEQ,kBAAkB,CAAE,CAAEG,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,qBAAsB,CAAC,CAC3Ec,eAAe,CAAE,CAAEH,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,sBAAuB,CAAC,CACzES,SAAS,CAAE,CAAEE,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAC1D,CAAC,CACDe,MAAM,CAAEC,IAAA,EAAqI,IAApI,CAAEd,KAAK,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,SAAS,CAAEC,cAAc,CAAEC,eAAe,CAAEC,kBAAkB,CAAEM,eAAe,CAAEL,SAAe,CAAC,CAAAO,IAAA,CACtI,GAAI,CAAAC,eAAe,CAAG,CAAC,CAAC,CAExB,OAAQX,cAAc,EACpB,IAAK,OAAO,CACVW,eAAe,CAAG,CAAEV,eAAe,CAAEA,eAAe,EAAI,SAAU,CAAC,CACnE,MACF,IAAK,UAAU,CACbU,eAAe,CAAG,CAAEC,UAAU,CAAEV,kBAAkB,EAAI,mDAAoD,CAAC,CAC3G,MACF,IAAK,OAAO,CACVS,eAAe,CAAG,CAChBH,eAAe,CAAE,OAAOA,eAAe,GAAG,CAC1CK,cAAc,CAAE,OAAO,CACvBC,kBAAkB,CAAE,QAAQ,CAC5BC,gBAAgB,CAAE,WACpB,CAAC,CACD,MACF,QACEJ,eAAe,CAAG,CAAEC,UAAU,CAAE,mDAAoD,CAAC,CACzF,CAEA,mBACExB,IAAA,YACE4B,KAAK,CAAE,CACLjB,SAAS,CAAEA,SAAS,EAAI,OAAO,CAC/BkB,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAEtB,SAAS,GAAK,QAAQ,CAAG,QAAQ,CAAGA,SAAS,GAAK,OAAO,CAAG,UAAU,CAAG,YAAY,CACjGA,SAAS,CAAEA,SAAS,EAAI,QAAQ,CAChCuB,OAAO,CAAE,WAAW,CACpBC,KAAK,CAAEnB,SAAS,EAAI,OAAO,CAC3BoB,QAAQ,CAAE,UAAU,CACpB,GAAGZ,eACL,CAAE,CAAAa,QAAA,cAEFlC,KAAA,QAAK0B,KAAK,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEE,MAAM,CAAE,CAAC,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAF,QAAA,eACjEpC,IAAA,OAAI4B,KAAK,CAAE,CACTW,QAAQ,CAAE,0BAA0B,CACpCC,YAAY,CAAE,MAAM,CACpBC,UAAU,CAAE,MAAM,CAClBC,UAAU,CAAE,GACd,CAAE,CAAAN,QAAA,CACC5B,KAAK,EAAI,YAAY,CACpB,CAAC,cACLR,IAAA,MAAG4B,KAAK,CAAE,CACRW,QAAQ,CAAE,6BAA6B,CACvCI,OAAO,CAAE,GAAG,CACZD,UAAU,CAAE,GAAG,CACfF,YAAY,CAAE,MAChB,CAAE,CAAAJ,QAAA,CACC3B,QAAQ,EAAI,oBAAoB,CAChC,CAAC,EACD,CAAC,CACC,CAAC,CAEd,CACF,CAAC,CACD;AACAmC,IAAI,CAAE,CACJtC,KAAK,CAAE,YAAY,CACnBC,YAAY,CAAE,CACZsC,OAAO,CAAE,wCAAwC,CACjDnC,SAAS,CAAE,MAAM,CACjB6B,QAAQ,CAAE,MAAM,CAChBL,KAAK,CAAE,SAAS,CAChBQ,UAAU,CAAE,KAAK,CACjBI,SAAS,CAAE,KAAK,CAChBN,YAAY,CAAE,MAChB,CAAC,CACDxB,MAAM,CAAE,CACN6B,OAAO,CAAE,CAAE5B,IAAI,CAAE,UAAmB,CAAEX,KAAK,CAAE,gBAAiB,CAAC,CAC/DI,SAAS,CAAE,CACTO,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,gBAAgB,CACvBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,MAAM,CAAEa,KAAK,CAAE,MAAO,CAAC,CAChC,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,QAAS,CAAC,CACpC,CAAEb,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,OAAQ,CAAC,CAClC,CAAEb,KAAK,CAAE,SAAS,CAAEa,KAAK,CAAE,SAAU,CAAC,CAE1C,CAAC,CACDoB,QAAQ,CAAE,CAAEtB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,WAAY,CAAC,CACvD4B,KAAK,CAAE,CAAEjB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAAC,CACrDoC,UAAU,CAAE,CAAEzB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,aAAc,CAAC,CAC3DwC,SAAS,CAAE,CAAE7B,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAAC,CACzDkC,YAAY,CAAE,CAAEvB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,eAAgB,CAChE,CAAC,CACDe,MAAM,CAAE0B,KAAA,MAAC,CAAEF,OAAO,CAAEnC,SAAS,CAAE6B,QAAQ,CAAEL,KAAK,CAAEQ,UAAU,CAAEI,SAAS,CAAEN,YAAkB,CAAC,CAAAO,KAAA,oBACxF/C,IAAA,QACEgD,SAAS,CAAC,gBAAgB,CAC1BpB,KAAK,CAAE,CACLlB,SAAS,CAAEA,SAAS,EAAI,MAAM,CAC9B6B,QAAQ,CAAEA,QAAQ,EAAI,MAAM,CAC5BL,KAAK,CAAEA,KAAK,EAAI,SAAS,CACzBQ,UAAU,CAAEA,UAAU,EAAI,KAAK,CAC/BI,SAAS,CAAEA,SAAS,EAAI,KAAK,CAC7BN,YAAY,CAAEA,YAAY,EAAI,MAAM,CACpCP,OAAO,CAAE,QACX,CAAE,CAAAG,QAAA,cAEFpC,IAAA,QAAKiD,uBAAuB,CAAE,CAAEC,MAAM,CAAEL,OAAO,EAAI,wCAAyC,CAAE,CAAE,CAAC,CAC9F,CAAC,EAEV,CAAC,CAEDM,OAAO,CAAE,CACP7C,KAAK,CAAE,SAAS,CAChBC,YAAY,CAAE,CACZ6C,IAAI,CAAE,cAAc,CACpBC,KAAK,CAAE,IAAI,CACX3C,SAAS,CAAE,MAAM,CACjBwB,KAAK,CAAE,SAAS,CAChBY,SAAS,CAAE,KAAK,CAChBN,YAAY,CAAE,MAChB,CAAC,CACDxB,MAAM,CAAE,CACNoC,IAAI,CAAE,CAAEnC,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,cAAe,CAAC,CACtD+C,KAAK,CAAE,CACLpC,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,eAAe,CACtBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,IAAI,CAAEa,KAAK,CAAE,IAAK,CAAC,CAC5B,CAAEb,KAAK,CAAE,IAAI,CAAEa,KAAK,CAAE,IAAK,CAAC,CAC5B,CAAEb,KAAK,CAAE,IAAI,CAAEa,KAAK,CAAE,IAAK,CAAC,CAC5B,CAAEb,KAAK,CAAE,IAAI,CAAEa,KAAK,CAAE,IAAK,CAAC,CAC5B,CAAEb,KAAK,CAAE,IAAI,CAAEa,KAAK,CAAE,IAAK,CAAC,CAC5B,CAAEb,KAAK,CAAE,IAAI,CAAEa,KAAK,CAAE,IAAK,CAAC,CAEhC,CAAC,CACDT,SAAS,CAAE,CACTO,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,gBAAgB,CACvBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,MAAM,CAAEa,KAAK,CAAE,MAAO,CAAC,CAChC,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,QAAS,CAAC,CACpC,CAAEb,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,OAAQ,CAAC,CAEtC,CAAC,CACDe,KAAK,CAAE,CAAEjB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAAC,CACrDwC,SAAS,CAAE,CAAE7B,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAAC,CACzDkC,YAAY,CAAE,CAAEvB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,eAAgB,CAChE,CAAC,CACDe,MAAM,CAAEiC,KAAA,EAAqE,IAApE,CAAEF,IAAI,CAAEC,KAAK,CAAE3C,SAAS,CAAEwB,KAAK,CAAEY,SAAS,CAAEN,YAAkB,CAAC,CAAAc,KAAA,CACtE,KAAM,CAAAC,UAAU,CAAGF,KAAK,EAAI,IAAI,CAChC,KAAM,CAAAG,SAAoC,CAAG,CAC3CC,EAAE,CAAE,QAAQ,CACZC,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,SAAS,CACbC,EAAE,CAAE,QAAQ,CACZC,EAAE,CAAE,SAAS,CACbC,EAAE,CAAE,MACN,CAAC,CACD,KAAM,CAAAvB,QAAQ,CAAGiB,SAAS,CAACH,KAAK,CAAC,EAAI,MAAM,CAE3C,mBAAOnE,KAAK,CAAC6E,aAAa,CACxBR,UAAU,CACV,CACE3B,KAAK,CAAE,CACLlB,SAAS,CAAEA,SAAS,EAAI,MAAM,CAC9BwB,KAAK,CAAEA,KAAK,EAAI,SAAS,CACzBK,QAAQ,CACRE,UAAU,CAAE,MAAM,CAClBK,SAAS,CAAEA,SAAS,EAAI,KAAK,CAC7BN,YAAY,CAAEA,YAAY,EAAI,MAAM,CACpCP,OAAO,CAAE,QAAQ,CACjBS,UAAU,CAAE,GACd,CACF,CAAC,CACDU,IAAI,EAAI,cACV,CAAC,CACH,CACF,CAAC,CACDzD,MAAM,CAAE,CACNW,KAAK,CAAE,QAAQ,CACfC,YAAY,CAAE,CACZ6C,IAAI,CAAE,aAAa,CACnBY,IAAI,CAAE,GAAG,CACTC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,IAAI,CACVxD,SAAS,CAAE,QAAQ,CACnByD,SAAS,CAAE,KAAK,CAChBC,MAAM,CAAE,OACV,CAAC,CACDpD,MAAM,CAAE,CACNoC,IAAI,CAAE,CAAEnC,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,aAAc,CAAC,CACrD0D,IAAI,CAAE,CAAE/C,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,UAAW,CAAC,CAClD2D,OAAO,CAAE,CACPhD,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,cAAc,CACrBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,SAAS,CAAEa,KAAK,CAAE,SAAU,CAAC,CACtC,CAAEb,KAAK,CAAE,WAAW,CAAEa,KAAK,CAAE,WAAY,CAAC,CAC1C,CAAEb,KAAK,CAAE,SAAS,CAAEa,KAAK,CAAE,SAAU,CAAC,CACtC,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,QAAS,CAAC,CACpC,CAAEb,KAAK,CAAE,SAAS,CAAEa,KAAK,CAAE,SAAU,CAAC,CACtC,CAAEb,KAAK,CAAE,MAAM,CAAEa,KAAK,CAAE,MAAO,CAAC,CAChC,CAAEb,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,OAAQ,CAAC,CAClC,CAAEb,KAAK,CAAE,MAAM,CAAEa,KAAK,CAAE,MAAO,CAAC,CAEpC,CAAC,CACD+C,IAAI,CAAE,CACJjD,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,aAAa,CACpBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,IAAK,CAAC,CAC/B,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,IAAK,CAAC,CAChC,CAAEb,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,IAAK,CAAC,CAEnC,CAAC,CACDT,SAAS,CAAE,CACTO,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,WAAW,CAClBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,MAAM,CAAEa,KAAK,CAAE,MAAO,CAAC,CAChC,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,QAAS,CAAC,CACpC,CAAEb,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,OAAQ,CAAC,CAEtC,CAAC,CACDgD,SAAS,CAAE,CAAElD,IAAI,CAAE,OAAgB,CAAEX,KAAK,CAAE,YAAY,CAAEY,OAAO,CAAE,CAAC,CAAEZ,KAAK,CAAE,KAAK,CAAEa,KAAK,CAAE,IAAK,CAAC,CAAE,CAAEb,KAAK,CAAE,IAAI,CAAEa,KAAK,CAAE,KAAM,CAAC,CAAE,CAAC,CACnIiD,MAAM,CAAE,CACNnD,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,aAAa,CACpBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,aAAa,CAAEa,KAAK,CAAE,OAAQ,CAAC,CACxC,CAAEb,KAAK,CAAE,YAAY,CAAEa,KAAK,CAAE,QAAS,CAAC,CAE5C,CACF,CAAC,CACDE,MAAM,CAAEgD,KAAA,EAAsE,IAArE,CAAEjB,IAAI,CAAEY,IAAI,CAAEC,OAAO,CAAEC,IAAI,CAAExD,SAAS,CAAEyD,SAAS,CAAEC,MAAY,CAAC,CAAAC,KAAA,CACvE,KAAM,CAAAC,MAAiC,CAAG,CACxCC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,SAAS,CACpBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CAED,KAAM,CAAAC,UAAqC,CAAG,CAC5CR,OAAO,CAAE,OAAO,CAChBC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,OAAO,CAChBC,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,OACR,CAAC,CAED,KAAM,CAAA7C,OAAO,CAAGiC,IAAI,GAAK,IAAI,CAAG,aAAa,CAAGA,IAAI,GAAK,IAAI,CAAG,WAAW,CAAG,gBAAgB,CAC9F,KAAM,CAAA3B,QAAQ,CAAG2B,IAAI,GAAK,IAAI,CAAG,UAAU,CAAGA,IAAI,GAAK,IAAI,CAAG,UAAU,CAAG,MAAM,CAEjF,mBACElE,IAAA,QAAK4B,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEvB,SAAS,CAAEA,SAAS,EAAI,QAAS,CAAE,CAAA0B,QAAA,cAChEpC,IAAA,MACEgE,IAAI,CAAEA,IAAI,EAAI,GAAI,CAClBI,MAAM,CAAEA,MAAM,EAAI,OAAQ,CAC1BY,GAAG,CAAEZ,MAAM,GAAK,QAAQ,CAAG,qBAAqB,CAAGa,SAAU,CAC7DrD,KAAK,CAAE,CACLC,OAAO,CAAEsC,SAAS,CAAG,OAAO,CAAG,cAAc,CAC7Ce,KAAK,CAAEf,SAAS,CAAG,MAAM,CAAG,MAAM,CAClClC,OAAO,CACPpB,eAAe,CAAEyD,MAAM,CAACL,OAAO,CAAC,EAAIK,MAAM,CAACC,OAAO,CAClDrC,KAAK,CAAE6C,UAAU,CAACd,OAAO,CAAC,EAAI,OAAO,CACrCkB,cAAc,CAAE,MAAM,CACtBC,YAAY,CAAE,UAAU,CACxB3C,UAAU,CAAE,KAAK,CACjBF,QAAQ,CACR7B,SAAS,CAAE,QAAQ,CACnB2E,UAAU,CAAE,sBAAsB,CAClCC,MAAM,CAAE,MAAM,CACdC,MAAM,CAAE,SACV,CAAE,CACFC,WAAW,CAAGC,CAAC,EAAK,CAClBA,CAAC,CAACC,aAAa,CAAC9D,KAAK,CAACe,OAAO,CAAG,KAAK,CACrC8C,CAAC,CAACC,aAAa,CAAC9D,KAAK,CAAC+D,SAAS,CAAG,kBAAkB,CACtD,CAAE,CACFC,UAAU,CAAGH,CAAC,EAAK,CACjBA,CAAC,CAACC,aAAa,CAAC9D,KAAK,CAACe,OAAO,CAAG,GAAG,CACnC8C,CAAC,CAACC,aAAa,CAAC9D,KAAK,CAAC+D,SAAS,CAAG,eAAe,CACnD,CAAE,CAAAvD,QAAA,CAEDgB,IAAI,EAAI,aAAa,CACrB,CAAC,CACD,CAAC,CAEV,CACF,CAAC,CACDyC,KAAK,CAAE,CACLvF,KAAK,CAAE,OAAO,CACdC,YAAY,CAAE,CACZuF,GAAG,CAAE,qCAAqC,CAC1CC,GAAG,CAAE,mBAAmB,CACxBb,KAAK,CAAE,MAAM,CACbc,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,OAAO,CAClBb,YAAY,CAAE,KAAK,CACnBtC,SAAS,CAAE,KAAK,CAChBN,YAAY,CAAE,MAChB,CAAC,CACDxB,MAAM,CAAE,CACN8E,GAAG,CAAE,CAAE7E,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,WAAY,CAAC,CAClDyF,GAAG,CAAE,CAAE9E,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,UAAW,CAAC,CACjD4E,KAAK,CAAE,CAAEjE,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,OAAQ,CAAC,CAChD0F,MAAM,CAAE,CAAE/E,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,QAAS,CAAC,CAClD2F,SAAS,CAAE,CACThF,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,YAAY,CACnBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,OAAQ,CAAC,CAClC,CAAEb,KAAK,CAAE,SAAS,CAAEa,KAAK,CAAE,SAAU,CAAC,CACtC,CAAEb,KAAK,CAAE,MAAM,CAAEa,KAAK,CAAE,MAAO,CAAC,CAChC,CAAEb,KAAK,CAAE,MAAM,CAAEa,KAAK,CAAE,MAAO,CAAC,CAChC,CAAEb,KAAK,CAAE,YAAY,CAAEa,KAAK,CAAE,YAAa,CAAC,CAEhD,CAAC,CACDiE,YAAY,CAAE,CAAEnE,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,eAAgB,CAAC,CAC/DwC,SAAS,CAAE,CAAE7B,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAAC,CACzDkC,YAAY,CAAE,CAAEvB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,eAAgB,CAChE,CAAC,CACDe,MAAM,CAAE6E,KAAA,MAAC,CAAEJ,GAAG,CAAEC,GAAG,CAAEb,KAAK,CAAEc,MAAM,CAAEC,SAAS,CAAEb,YAAY,CAAEtC,SAAS,CAAEN,YAAkB,CAAC,CAAA0D,KAAA,oBACzFlG,IAAA,QAAK4B,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEa,SAAS,CAAEA,SAAS,EAAI,KAAK,CAAEN,YAAY,CAAEA,YAAY,EAAI,MAAO,CAAE,CAAAJ,QAAA,cACnGpC,IAAA,QACE8F,GAAG,CAAEA,GAAG,EAAI,qCAAsC,CAClDC,GAAG,CAAEA,GAAG,EAAI,mBAAoB,CAChCnE,KAAK,CAAE,CACLsD,KAAK,CAAEA,KAAK,EAAI,MAAM,CACtBc,MAAM,CAAEA,MAAM,EAAI,MAAM,CACxBC,SAAS,CAAEA,SAAS,EAAI,OAAO,CAC/Bb,YAAY,CAAEA,YAAY,EAAI,KAAK,CACnCvD,OAAO,CAAE,OAAO,CAChBS,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,CACC,CAAC,EAEV,CAAC,CAED9C,SAAS,CAAE,CACTc,KAAK,CAAE,WAAW,CAClBC,YAAY,CAAE,CACZ+B,QAAQ,CAAE,IAAI,CACdL,OAAO,CAAE,MAAM,CACfpB,eAAe,CAAE,aAAa,CAC9BuE,YAAY,CAAE,KAAK,CACnBtC,SAAS,CAAE,KAAK,CAChBN,YAAY,CAAE,KAChB,CAAC,CACDxB,MAAM,CAAE,CACNsB,QAAQ,CAAE,CACRrB,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,WAAW,CAClBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,eAAe,CAAEa,KAAK,CAAE,IAAK,CAAC,CACvC,CAAEb,KAAK,CAAE,gBAAgB,CAAEa,KAAK,CAAE,IAAK,CAAC,CACxC,CAAEb,KAAK,CAAE,eAAe,CAAEa,KAAK,CAAE,IAAK,CAAC,CACvC,CAAEb,KAAK,CAAE,sBAAsB,CAAEa,KAAK,CAAE,IAAK,CAAC,CAC9C,CAAEb,KAAK,CAAE,YAAY,CAAEa,KAAK,CAAE,OAAQ,CAAC,CAE3C,CAAC,CACDc,OAAO,CAAE,CAAEhB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,SAAU,CAAC,CACpDO,eAAe,CAAE,CAAEI,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,kBAAmB,CAAC,CACrE8E,YAAY,CAAE,CAAEnE,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,eAAgB,CAAC,CAC/DwC,SAAS,CAAE,CAAE7B,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAAC,CACzDkC,YAAY,CAAE,CAAEvB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,eAAgB,CAChE,CAAC,CACDe,MAAM,CAAE8E,KAAA,EAAkG,IAAjG,CAAE7D,QAAQ,CAAEL,OAAO,CAAEpB,eAAe,CAAEuE,YAAY,CAAEtC,SAAS,CAAEN,YAAY,CAAEJ,QAAc,CAAC,CAAA+D,KAAA,CACnG,KAAM,CAAAC,SAAoC,CAAG,CAC3CC,EAAE,CAAE,OAAO,CACXC,EAAE,CAAE,OAAO,CACXC,EAAE,CAAE,OAAO,CACXC,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,MACT,CAAC,CAED,mBACEzG,IAAA,QACE4B,KAAK,CAAE,CACLU,QAAQ,CAAE8D,SAAS,CAAC9D,QAAQ,CAAC,EAAI,OAAO,CACxCoE,MAAM,CAAE,QAAQ,CAChBzE,OAAO,CAAEA,OAAO,EAAI,MAAM,CAC1BpB,eAAe,CAAEA,eAAe,EAAI,aAAa,CACjDuE,YAAY,CAAEA,YAAY,EAAI,KAAK,CACnCtC,SAAS,CAAEA,SAAS,EAAI,KAAK,CAC7BN,YAAY,CAAEA,YAAY,EAAI,KAChC,CAAE,CAAAJ,QAAA,CAEDA,QAAQ,CACN,CAAC,CAEV,CACF,CAAC,CAEDuE,MAAM,CAAE,CACNrG,KAAK,CAAE,QAAQ,CACfC,YAAY,CAAE,CACZyF,MAAM,CAAE,MACV,CAAC,CACDhF,MAAM,CAAE,CACNgF,MAAM,CAAE,CAAE/E,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,QAAS,CACnD,CAAC,CACDe,MAAM,CAAEuF,KAAA,MAAC,CAAEZ,MAAY,CAAC,CAAAY,KAAA,oBACtB5G,IAAA,QAAK4B,KAAK,CAAE,CAAEoE,MAAM,CAAEA,MAAM,EAAI,MAAM,CAAEd,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,EAE/D,CAAC,CAED2B,OAAO,CAAE,CACPvG,KAAK,CAAE,SAAS,CAChBC,YAAY,CAAE,CACZ2B,KAAK,CAAE,SAAS,CAChB4E,SAAS,CAAE,KAAK,CAChBlF,KAAK,CAAE,OAAO,CACdkB,SAAS,CAAE,MAAM,CACjBN,YAAY,CAAE,MAChB,CAAC,CACDxB,MAAM,CAAE,CACNkB,KAAK,CAAE,CAAEjB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,OAAQ,CAAC,CAChDwG,SAAS,CAAE,CAAE7F,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,WAAY,CAAC,CACxDsB,KAAK,CAAE,CACLX,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,OAAO,CACdY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,OAAO,CAAEa,KAAK,CAAE,OAAQ,CAAC,CAClC,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,QAAS,CAAC,CACpC,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,QAAS,CAAC,CAExC,CAAC,CACD2B,SAAS,CAAE,CAAE7B,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAAC,CACzDkC,YAAY,CAAE,CAAEvB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,eAAgB,CAChE,CAAC,CACDe,MAAM,CAAE0F,KAAA,MAAC,CAAE7E,KAAK,CAAE4E,SAAS,CAAElF,KAAK,CAAEkB,SAAS,CAAEN,YAAkB,CAAC,CAAAuE,KAAA,oBAChE/G,IAAA,QAAK4B,KAAK,CAAE,CAAEK,OAAO,CAAE,QAAQ,CAAEa,SAAS,CAAEA,SAAS,EAAI,MAAM,CAAEN,YAAY,CAAEA,YAAY,EAAI,MAAO,CAAE,CAAAJ,QAAA,cACtGpC,IAAA,OACE4B,KAAK,CAAE,CACL0D,MAAM,CAAE,MAAM,CACd0B,SAAS,CAAE,GAAGF,SAAS,EAAI,KAAK,IAAIlF,KAAK,EAAI,OAAO,IAAIM,KAAK,EAAI,SAAS,EAAE,CAC5EwE,MAAM,CAAE,CACV,CAAE,CACH,CAAC,CACC,CAAC,EAEV,CAAC,CAEDO,IAAI,CAAE,CACJ3G,KAAK,CAAE,MAAM,CACbC,YAAY,CAAE,CACZC,KAAK,CAAE,YAAY,CACnBqC,OAAO,CAAE,kCAAkC,CAC3CqE,QAAQ,CAAE,EAAE,CACZrG,eAAe,CAAE,SAAS,CAC1BsG,WAAW,CAAE,SAAS,CACtB/B,YAAY,CAAE,KAAK,CACnBnD,OAAO,CAAE,QAAQ,CACjBmF,MAAM,CAAE,IACV,CAAC,CACDpG,MAAM,CAAE,CACNR,KAAK,CAAE,CAAES,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAAC,CACrDuC,OAAO,CAAE,CAAE5B,IAAI,CAAE,UAAmB,CAAEX,KAAK,CAAE,qBAAsB,CAAC,CACpE4G,QAAQ,CAAE,CAAEjG,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,sBAAuB,CAAC,CAClEO,eAAe,CAAE,CAAEI,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,kBAAmB,CAAC,CACrE6G,WAAW,CAAE,CAAElG,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,cAAe,CAAC,CAC7D8E,YAAY,CAAE,CAAEnE,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,eAAgB,CAAC,CAC/D2B,OAAO,CAAE,CAAEhB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,SAAU,CAAC,CACpD8G,MAAM,CAAE,CAAEnG,IAAI,CAAE,OAAgB,CAAEX,KAAK,CAAE,aAAa,CAAEY,OAAO,CAAE,CAAC,CAAEZ,KAAK,CAAE,KAAK,CAAEa,KAAK,CAAE,IAAK,CAAC,CAAE,CAAEb,KAAK,CAAE,IAAI,CAAEa,KAAK,CAAE,KAAM,CAAC,CAAE,CAClI,CAAC,CACDE,MAAM,CAAEgG,KAAA,MAAC,CAAE7G,KAAK,CAAEqC,OAAO,CAAEqE,QAAQ,CAAErG,eAAe,CAAEsG,WAAW,CAAE/B,YAAY,CAAEnD,OAAO,CAAEmF,MAAY,CAAC,CAAAC,KAAA,oBACrGrH,IAAA,QAAK4B,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAO,CAAE,CAAAG,QAAA,cAC9BlC,KAAA,QACE0B,KAAK,CAAE,CACLf,eAAe,CAAEA,eAAe,EAAI,SAAS,CAC7CyE,MAAM,CAAE,aAAa6B,WAAW,EAAI,SAAS,EAAE,CAC/C/B,YAAY,CAAEA,YAAY,EAAI,KAAK,CACnCnD,OAAO,CAAEA,OAAO,EAAI,QAAQ,CAC5BqF,SAAS,CAAEF,MAAM,CAAG,2BAA2B,CAAG,MAAM,CACxDG,QAAQ,CAAE,QACZ,CAAE,CAAAnF,QAAA,EAED8E,QAAQ,eACPlH,IAAA,QACE8F,GAAG,CAAEoB,QAAS,CACdnB,GAAG,CAAEvF,KAAK,EAAI,YAAa,CAC3BoB,KAAK,CAAE,CACLsD,KAAK,CAAE,MAAM,CACbc,MAAM,CAAE,OAAO,CACfC,SAAS,CAAE,OAAO,CAClBzD,YAAY,CAAE,MAAM,CACpB4C,YAAY,CAAE,KAChB,CAAE,CACH,CACF,CACA5E,KAAK,eACJR,IAAA,OAAI4B,KAAK,CAAE,CACT8E,MAAM,CAAE,YAAY,CACpBnE,QAAQ,CAAE,SAAS,CACnBE,UAAU,CAAE,MAAM,CAClBP,KAAK,CAAE,MACT,CAAE,CAAAE,QAAA,CACC5B,KAAK,CACJ,CACL,cACDR,IAAA,QAAKiD,uBAAuB,CAAE,CAAEC,MAAM,CAAEL,OAAO,EAAI,kCAAmC,CAAE,CAAE,CAAC,EACxF,CAAC,CACH,CAAC,EAEV,CAAC,CAED2E,OAAO,CAAE,CACPlH,KAAK,CAAE,SAAS,CAChBC,YAAY,CAAE,CACZkH,OAAO,CAAE,CAAC,CACVC,GAAG,CAAE,MAAM,CACX1F,UAAU,CAAE,SACd,CAAC,CACDhB,MAAM,CAAE,CACNyG,OAAO,CAAE,CACPxG,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,mBAAmB,CAC1BY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,WAAW,CAAEa,KAAK,CAAE,CAAE,CAAC,CAChC,CAAEb,KAAK,CAAE,WAAW,CAAEa,KAAK,CAAE,CAAE,CAAC,CAChC,CAAEb,KAAK,CAAE,WAAW,CAAEa,KAAK,CAAE,CAAE,CAAC,CAEpC,CAAC,CACDuG,GAAG,CAAE,CAAEzG,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,qBAAsB,CAAC,CAC5D0B,UAAU,CAAE,CACVf,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,oBAAoB,CAC3BY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,SAAS,CAAEa,KAAK,CAAE,SAAU,CAAC,CACtC,CAAEb,KAAK,CAAE,KAAK,CAAEa,KAAK,CAAE,YAAa,CAAC,CACrC,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,QAAS,CAAC,CACpC,CAAEb,KAAK,CAAE,QAAQ,CAAEa,KAAK,CAAE,UAAW,CAAC,CAE1C,CACF,CAAC,CACDE,MAAM,CAAEsG,KAAA,MAAC,CAAEF,OAAO,CAAEC,GAAG,CAAE1F,UAAU,CAAEI,QAAc,CAAC,CAAAuF,KAAA,oBAClD3H,IAAA,QAAK4B,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAO,CAAE,CAAAG,QAAA,cAC9BpC,IAAA,QACE4B,KAAK,CAAE,CACLC,OAAO,CAAE,MAAM,CACf+F,mBAAmB,CAAE,UAAUH,OAAO,EAAI,CAAC,QAAQ,CACnDC,GAAG,CAAEA,GAAG,EAAI,MAAM,CAClB1F,UAAU,CAAEA,UAAU,EAAI,SAC5B,CAAE,CAAAI,QAAA,CAEDA,QAAQ,CACN,CAAC,CACH,CAAC,EAEV,CAAC,CAEDyF,IAAI,CAAE,CACJvH,KAAK,CAAE,MAAM,CACbC,YAAY,CAAE,CACZuH,KAAK,CAAE,CAAC,aAAa,CAAE,aAAa,CAAE,aAAa,CAAC,CACpDC,QAAQ,CAAE,IAAI,CACd7F,KAAK,CAAE,SAAS,CAChBK,QAAQ,CAAE,MACZ,CAAC,CACDvB,MAAM,CAAE,CACN8G,KAAK,CAAE,CAAE7G,IAAI,CAAE,OAAgB,CAAEX,KAAK,CAAE,YAAY,CAAE0H,WAAW,CAAE,CAAEC,IAAI,CAAE,CAAEhH,IAAI,CAAE,MAAgB,CAAE,CAAE,CAAC,CACxG8G,QAAQ,CAAE,CACR9G,IAAI,CAAE,QAAiB,CACvBX,KAAK,CAAE,WAAW,CAClBY,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,qBAAqB,CAAEa,KAAK,CAAE,IAAK,CAAC,CAC7C,CAAEb,KAAK,CAAE,mBAAmB,CAAEa,KAAK,CAAE,IAAK,CAAC,CAE/C,CAAC,CACDe,KAAK,CAAE,CAAEjB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,YAAa,CAAC,CACrDiC,QAAQ,CAAE,CAAEtB,IAAI,CAAE,MAAe,CAAEX,KAAK,CAAE,WAAY,CACxD,CAAC,CACDe,MAAM,CAAE6G,KAAA,EAA+C,IAA9C,CAAEJ,KAAK,CAAEC,QAAQ,CAAE7F,KAAK,CAAEK,QAAc,CAAC,CAAA2F,KAAA,CAChD,KAAM,CAAAC,OAAO,CAAGJ,QAAQ,GAAK,IAAI,CAAG,IAAI,CAAG,IAAI,CAC/C,KAAM,CAAAK,UAAU,CAAGC,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,CAAGA,KAAK,CAAG,CAAC,aAAa,CAAE,aAAa,CAAE,aAAa,CAAC,CAE/F,mBACE9H,IAAA,QAAK4B,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAO,CAAE,CAAAG,QAAA,cAC9BpC,IAAA,CAACmI,OAAO,EACNvG,KAAK,CAAE,CACLM,KAAK,CAAEA,KAAK,EAAI,SAAS,CACzBK,QAAQ,CAAEA,QAAQ,EAAI,MAAM,CAC5BG,UAAU,CAAE,KAAK,CACjB6F,WAAW,CAAE,QACf,CAAE,CAAAnG,QAAA,CAEDgG,UAAU,CAACI,GAAG,CAAC,CAACP,IAAY,CAAEQ,KAAa,gBAC1CzI,IAAA,OAAgB4B,KAAK,CAAE,CAAEY,YAAY,CAAE,QAAS,CAAE,CAAAJ,QAAA,CAC/C6F,IAAI,EADEQ,KAEL,CACL,CAAC,CACK,CAAC,CACP,CAAC,CAEV,CACF,CACF,CACF,CAAC,CAMD,KAAM,CAAAC,UAAqC,CAAGC,MAAA,EAA4B,IAA3B,CAAEC,MAAM,CAAEC,UAAW,CAAC,CAAAF,MAAA,CACnE,KAAM,CAAEC,MAAM,CAAEE,WAAY,CAAC,CAAGxJ,SAAS,CAAqB,CAAC,CAC/D,KAAM,CAAAyJ,QAAQ,CAAGxJ,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAqJ,MAAM,CAAGC,UAAU,EAAIC,WAAW,CAExC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAG9J,QAAQ,CAAiB,IAAI,CAAC,CACtD,KAAM,CAAC+J,QAAQ,CAAEC,WAAW,CAAC,CAAGhK,QAAQ,CAAM,IAAI,CAAC,CACnD,KAAM,CAACiK,OAAO,CAAEC,UAAU,CAAC,CAAGlK,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACmK,MAAM,CAAEC,SAAS,CAAC,CAAGpK,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACqK,KAAK,CAAEC,QAAQ,CAAC,CAAGtK,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACuK,OAAO,CAAEC,UAAU,CAAC,CAAGxK,QAAQ,CAAC,KAAK,CAAC,CAE7CC,SAAS,CAAC,IAAM,CACdwK,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAAE,CAAChB,MAAM,CAAC,CAAC,CAEZ;AACAxJ,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyK,OAAO,CAAG/J,4BAA4B,CAAC,CAAC,CAE9C;AACA,KAAM,CAAAgK,eAAe,CAAIC,KAAiB,EAAK,CAC7C;AACA,GAAIA,KAAK,CAACC,OAAO,GACfD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EACxCF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EACxDF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAC9CF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0BAA0B,CAAC,EAClDF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,yBAAyB,CAAC,CAClD,CAAE,CACDF,KAAK,CAACG,cAAc,CAAC,CAAC,CACtB,MAAO,MAAK,CACd,CACA,MAAO,KAAI,CACb,CAAC,CAEDC,MAAM,CAACC,gBAAgB,CAAC,OAAO,CAAEN,eAAe,CAAC,CAEjD,MAAO,IAAM,CACXD,OAAO,CAAC,CAAC,CACTM,MAAM,CAACE,mBAAmB,CAAC,OAAO,CAAEP,eAAe,CAAC,CACtD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAF,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACFP,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA,KAAM,CAAAa,WAAW,CAAG,KAAM,CAAAzK,WAAW,CAAC0K,gBAAgB,CAAC,CAAC,CACxD,GAAI,CAACD,WAAW,CAACE,QAAQ,CAAE,CACzBf,QAAQ,CAAC,6CAA6C,CAAC,CACvDJ,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACAM,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,GAAIf,MAAM,CAAE,CACV,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAA7K,UAAU,CAAC8K,WAAW,CAACC,QAAQ,CAAC/B,MAAM,CAAC,CAAC,CAC/DK,OAAO,CAACwB,QAAQ,CAAC,CAEjB;AACA,GAAIA,QAAQ,CAACG,WAAW,GAAK,MAAM,EAAIH,QAAQ,CAACI,SAAS,CAAE,CACzD1B,WAAW,CAACsB,QAAQ,CAACI,SAAS,CAAC,CACjC,CAAC,IAAM,CACL;AACA1B,WAAW,CAAC,CACVtG,OAAO,CAAE,EAAE,CACXiI,IAAI,CAAE,CACJtK,KAAK,CAAEiK,QAAQ,CAACjK,KAAK,CACrBuK,eAAe,CAAEN,QAAQ,CAACO,gBAAgB,EAAI,EAChD,CACF,CAAC,CAAC,CACJ,CACF,CACF,CAAE,MAAOC,GAAQ,CAAE,CACjBC,OAAO,CAAC1B,KAAK,CAAC,qBAAqB,CAAEyB,GAAG,CAAC,CACzCxB,QAAQ,CAACwB,GAAG,CAACjB,OAAO,EAAI,0BAA0B,CAAC,CACrD,CAAC,OAAS,CACRX,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA8B,UAAU,CAAG,KAAO,CAAAC,IAAS,EAAK,CACtC,GAAI,KAAAC,UAAA,CAAAC,WAAA,CACF/B,SAAS,CAAC,IAAI,CAAC,CACfE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CAACT,IAAI,CAAE,CACT,KAAM,IAAI,CAAAuC,KAAK,CAAC,wBAAwB,CAAC,CAC3C,CAEA;AACA,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAA5L,UAAU,CAAC6L,sBAAsB,CAACzC,IAAI,CAAC0C,EAAE,CAAEN,IAAI,CAAE,CACzE5K,KAAK,CAAE,EAAA6K,UAAA,CAAAD,IAAI,CAACN,IAAI,UAAAO,UAAA,iBAATA,UAAA,CAAW7K,KAAK,GAAIwI,IAAI,CAACxI,KAAK,CACrCwK,gBAAgB,CAAE,EAAAM,WAAA,CAAAF,IAAI,CAACN,IAAI,UAAAQ,WAAA,iBAATA,WAAA,CAAWP,eAAe,GAAI/B,IAAI,CAACgC,gBACvD,CAAC,CAAC,CAEF;AACAW,KAAK,CAAC,8BAA8B,CAAC,CAErC;AACA,GAAIH,WAAW,CAACI,IAAI,GAAK,MAAM,CAAE,CAC/B;AACA7C,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,IAAM,CACL;AACAA,QAAQ,CAAC,UAAUyC,WAAW,CAACI,IAAI,EAAE,CAAC,CACxC,CACF,CAAE,MAAOX,GAAQ,CAAE,CACjBC,OAAO,CAAC1B,KAAK,CAAC,oBAAoB,CAAEyB,GAAG,CAAC,CACxCxB,QAAQ,CAACwB,GAAG,CAACjB,OAAO,EAAI,qBAAqB,CAAC,CAChD,CAAC,OAAS,CACRT,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED,KAAM,CAAAsC,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI7C,IAAI,CAAE,CACRD,QAAQ,CAAC,UAAUC,IAAI,CAAC4C,IAAI,EAAE,CAAC,CACjC,CAAC,IAAM,CACL7C,QAAQ,CAAC,GAAG,CAAC,CACf,CACF,CAAC,CAED,GAAIK,OAAO,CAAE,CACX,mBACElJ,KAAA,CAACV,SAAS,EAACwD,SAAS,CAAC,kBAAkB,CAAAZ,QAAA,eACrCpC,IAAA,CAACN,OAAO,EAACoM,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAAA3J,QAAA,cACvCpC,IAAA,SAAMgD,SAAS,CAAC,iBAAiB,CAAAZ,QAAA,CAAC,YAAU,CAAM,CAAC,CAC5C,CAAC,cACVpC,IAAA,MAAGgD,SAAS,CAAC,MAAM,CAAAZ,QAAA,CAAC,wBAAsB,CAAG,CAAC,EACrC,CAAC,CAEhB,CAEA,GAAIoH,KAAK,CAAE,CACT,mBACExJ,IAAA,CAACR,SAAS,EAACwD,SAAS,CAAC,MAAM,CAAAZ,QAAA,cACzBlC,KAAA,CAACT,KAAK,EAACwE,OAAO,CAAC,QAAQ,CAAA7B,QAAA,eACrBpC,IAAA,CAACP,KAAK,CAAC0D,OAAO,EAAAf,QAAA,CAAC,OAAK,CAAe,CAAC,cACpCpC,IAAA,MAAAoC,QAAA,CAAIoH,KAAK,CAAI,CAAC,cACdxJ,IAAA,CAACL,MAAM,EAACsE,OAAO,CAAC,gBAAgB,CAAC+H,OAAO,CAAEA,CAAA,GAAMjD,QAAQ,CAAC,GAAG,CAAE,CAAA3G,QAAA,CAAC,SAE/D,CAAQ,CAAC,EACJ,CAAC,CACC,CAAC,CAEhB,CAEA,GAAI,CAACsH,OAAO,CAAE,CACZ,mBACE1J,IAAA,CAACR,SAAS,EAACwD,SAAS,CAAC,MAAM,CAAAZ,QAAA,cACzBlC,KAAA,CAACT,KAAK,EAACwE,OAAO,CAAC,SAAS,CAAA7B,QAAA,eACtBpC,IAAA,CAACP,KAAK,CAAC0D,OAAO,EAAAf,QAAA,CAAC,eAAa,CAAe,CAAC,cAC5CpC,IAAA,MAAAoC,QAAA,CAAG,6CAA2C,CAAG,CAAC,cAClDpC,IAAA,CAACL,MAAM,EAACsE,OAAO,CAAC,iBAAiB,CAAC+H,OAAO,CAAEA,CAAA,GAAMjD,QAAQ,CAAC,GAAG,CAAE,CAAA3G,QAAA,CAAC,SAEhE,CAAQ,CAAC,EACJ,CAAC,CACC,CAAC,CAEhB,CAEA,GAAI,CAAC4G,IAAI,EAAI,CAACE,QAAQ,CAAE,CACtB,mBACElJ,IAAA,CAACR,SAAS,EAACwD,SAAS,CAAC,MAAM,CAAAZ,QAAA,cACzBlC,KAAA,CAACT,KAAK,EAACwE,OAAO,CAAC,SAAS,CAAA7B,QAAA,eACtBpC,IAAA,CAACP,KAAK,CAAC0D,OAAO,EAAAf,QAAA,CAAC,gBAAc,CAAe,CAAC,cAC7CpC,IAAA,MAAAoC,QAAA,CAAG,wCAAsC,CAAG,CAAC,cAC7CpC,IAAA,CAACL,MAAM,EAACsE,OAAO,CAAC,iBAAiB,CAAC+H,OAAO,CAAEA,CAAA,GAAMjD,QAAQ,CAAC,GAAG,CAAE,CAAA3G,QAAA,CAAC,SAEhE,CAAQ,CAAC,EACJ,CAAC,CACC,CAAC,CAEhB,CAEA,mBACElC,KAAA,QAAK8C,SAAS,CAAC,uBAAuB,CAACpB,KAAK,CAAE,CAAEoE,MAAM,CAAE,OAAO,CAAEd,KAAK,CAAE,OAAO,CAAE/C,QAAQ,CAAE,OAAO,CAAE8J,GAAG,CAAE,CAAC,CAAEC,IAAI,CAAE,CAAC,CAAE7J,MAAM,CAAE,IAAK,CAAE,CAAAD,QAAA,eAClIpC,IAAA,CAACX,IAAI,EACH8M,MAAM,CAAEhM,UAAW,CACnBiL,IAAI,CAAElC,QAAS,CACfkD,SAAS,CAAEjB,UAAW,CACtBkB,QAAQ,CAAElD,WAAY,CACtBmD,WAAW,CAAE,YAAYtD,IAAI,CAACxI,KAAK,EAAG,CACtC+L,mBAAmB,CAAEA,CAAA,gBACnBrM,KAAA,QAAK8C,SAAS,CAAC,qBAAqB,CAAAZ,QAAA,eAClCpC,IAAA,CAACL,MAAM,EACLsE,OAAO,CAAC,SAAS,CACjBC,IAAI,CAAC,IAAI,CACT8H,OAAO,CAAEA,CAAA,GAAMb,UAAU,CAACjC,QAAQ,CAAE,CACpCsD,QAAQ,CAAElD,MAAO,CAAAlH,QAAA,CAEhBkH,MAAM,CAAG,eAAe,CAAG,SAAS,CAC/B,CAAC,cACTtJ,IAAA,CAACL,MAAM,EACLsE,OAAO,CAAC,mBAAmB,CAC3BC,IAAI,CAAC,IAAI,CACT8H,OAAO,CAAEH,YAAa,CACtBW,QAAQ,CAAElD,MAAO,CAAAlH,QAAA,CAClB,QAED,CAAQ,CAAC,EACN,CACL,CACH,CAAC,CACDkH,MAAM,eACLtJ,IAAA,QACE4B,KAAK,CAAE,CACLO,QAAQ,CAAE,OAAO,CACjB8J,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPO,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACT7L,eAAe,CAAE,oBAAoB,CACrCgB,OAAO,CAAE,MAAM,CACfG,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBM,MAAM,CAAE,KACV,CAAE,CAAAD,QAAA,cAEFlC,KAAA,QAAK0B,KAAK,CAAE,CAAEf,eAAe,CAAE,OAAO,CAAEoB,OAAO,CAAE,MAAM,CAAEmD,YAAY,CAAE,QAAS,CAAE,CAAAhD,QAAA,eAChFpC,IAAA,CAACN,OAAO,EAACoM,SAAS,CAAC,QAAQ,CAAC9I,SAAS,CAAC,MAAM,CAAE,CAAC,iBAEjD,EAAK,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA0F,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}