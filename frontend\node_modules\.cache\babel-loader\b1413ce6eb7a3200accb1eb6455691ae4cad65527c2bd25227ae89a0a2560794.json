{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    variant,\n    as: Component = 'img',\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "CardImg", "forwardRef", "_ref", "ref", "bsPrefix", "className", "variant", "as", "Component", "props", "prefix", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/CardImg.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG,aAAaJ,KAAK,CAACK,UAAU;AAC7C;AACA,CAAAC,IAAA,EAMGC,GAAG,KAAK;EAAA,IANV;IACCC,QAAQ;IACRC,SAAS;IACTC,OAAO;IACPC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrB,GAAGC;EACL,CAAC,GAAAP,IAAA;EACC,MAAMQ,MAAM,GAAGb,kBAAkB,CAACO,QAAQ,EAAE,UAAU,CAAC;EACvD,OAAO,aAAaL,IAAI,CAACS,SAAS,EAAE;IAClCL,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEV,UAAU,CAACW,OAAO,GAAG,GAAGI,MAAM,IAAIJ,OAAO,EAAE,GAAGI,MAAM,EAAEL,SAAS,CAAC;IAC3E,GAAGI;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,OAAO,CAACW,WAAW,GAAG,SAAS;AAC/B,eAAeX,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}