{"ast": null, "code": "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n  return uuid;\n}\nexport default stringify;", "map": {"version": 3, "names": ["validate", "byteToHex", "i", "push", "toString", "slice", "unsafeStringify", "arr", "offset", "stringify", "uuid", "TypeError"], "sources": ["C:/laragon/www/frontend/node_modules/uuid/dist/esm-browser/stringify.js"], "sourcesContent": ["import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC;AACA;AACA;AACA;;AAEA,MAAMC,SAAS,GAAG,EAAE;AAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;EAC5BD,SAAS,CAACE,IAAI,CAAC,CAACD,CAAC,GAAG,KAAK,EAAEE,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnD;AAEA,OAAO,SAASC,eAAeA,CAACC,GAAG,EAAEC,MAAM,GAAG,CAAC,EAAE;EAC/C;EACA;EACA,OAAOP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACM,GAAG,CAACC,MAAM,GAAG,EAAE,CAAC,CAAC;AACpf;AAEA,SAASC,SAASA,CAACF,GAAG,EAAEC,MAAM,GAAG,CAAC,EAAE;EAClC,MAAME,IAAI,GAAGJ,eAAe,CAACC,GAAG,EAAEC,MAAM,CAAC,CAAC,CAAC;EAC3C;EACA;EACA;EACA;;EAEA,IAAI,CAACR,QAAQ,CAACU,IAAI,CAAC,EAAE;IACnB,MAAMC,SAAS,CAAC,6BAA6B,CAAC;EAChD;EAEA,OAAOD,IAAI;AACb;AAEA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}