{"ast": null, "code": "import React from'react';import{Container,Row,Col}from'react-bootstrap';import dattaAbleTheme from'../../theme/dattaAbleTheme';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DattaAbleFooter=()=>{const footerStyles={backgroundColor:dattaAbleTheme.colors.background.paper,borderTop:`1px solid ${dattaAbleTheme.colors.border}`,padding:`${dattaAbleTheme.spacing[4]} 0`,marginTop:'auto'};const textStyles={fontSize:dattaAbleTheme.typography.fontSize.sm,color:dattaAbleTheme.colors.text.secondary,margin:0};const linkStyles={color:dattaAbleTheme.colors.primary.main,textDecoration:'none',fontWeight:dattaAbleTheme.typography.fontWeight.medium};return/*#__PURE__*/_jsx(\"footer\",{style:footerStyles,children:/*#__PURE__*/_jsx(Container,{fluid:true,children:/*#__PURE__*/_jsxs(Row,{className:\"align-items-center\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(\"p\",{style:textStyles,children:[\"\\xA9 \",new Date().getFullYear(),\" Made with\",' ',/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-heart text-danger\"}),\" by\",' ',/*#__PURE__*/_jsx(\"a\",{href:\"#\",style:linkStyles,onMouseEnter:e=>{e.target.style.textDecoration='underline';},onMouseLeave:e=>{e.target.style.textDecoration='none';},children:\"Your Company\"})]})}),/*#__PURE__*/_jsx(Col,{md:6,className:\"text-md-end\",children:/*#__PURE__*/_jsxs(\"p\",{style:textStyles,children:[/*#__PURE__*/_jsx(\"a\",{href:\"#\",style:linkStyles,className:\"me-3\",onMouseEnter:e=>{e.target.style.textDecoration='underline';},onMouseLeave:e=>{e.target.style.textDecoration='none';},children:\"About\"}),/*#__PURE__*/_jsx(\"a\",{href:\"#\",style:linkStyles,className:\"me-3\",onMouseEnter:e=>{e.target.style.textDecoration='underline';},onMouseLeave:e=>{e.target.style.textDecoration='none';},children:\"Support\"}),/*#__PURE__*/_jsx(\"a\",{href:\"#\",style:linkStyles,onMouseEnter:e=>{e.target.style.textDecoration='underline';},onMouseLeave:e=>{e.target.style.textDecoration='none';},children:\"Contact\"})]})})]})})});};export default DattaAbleFooter;", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "dattaAbleTheme", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "footerStyles", "backgroundColor", "colors", "background", "paper", "borderTop", "border", "padding", "spacing", "marginTop", "textStyles", "fontSize", "typography", "sm", "color", "text", "secondary", "margin", "linkStyles", "primary", "main", "textDecoration", "fontWeight", "medium", "style", "children", "fluid", "className", "md", "Date", "getFullYear", "href", "onMouseEnter", "e", "target", "onMouseLeave"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DattaAbleFooter.tsx"], "sourcesContent": ["import React from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\n\nconst DattaAbleFooter = () => {\n  const footerStyles = {\n    backgroundColor: dattaAbleTheme.colors.background.paper,\n    borderTop: `1px solid ${dattaAbleTheme.colors.border}`,\n    padding: `${dattaAbleTheme.spacing[4]} 0`,\n    marginTop: 'auto',\n  };\n\n  const textStyles = {\n    fontSize: dattaAbleTheme.typography.fontSize.sm,\n    color: dattaAbleTheme.colors.text.secondary,\n    margin: 0,\n  };\n\n  const linkStyles = {\n    color: dattaAbleTheme.colors.primary.main,\n    textDecoration: 'none',\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n  };\n\n  return (\n    <footer style={footerStyles}>\n      <Container fluid>\n        <Row className=\"align-items-center\">\n          <Col md={6}>\n            <p style={textStyles}>\n              © {new Date().getFullYear()} Made with{' '}\n              <i className=\"fas fa-heart text-danger\"></i> by{' '}\n              <a\n                href=\"#\"\n                style={linkStyles}\n                onMouseEnter={(e) => {\n                  (e.target as HTMLElement).style.textDecoration = 'underline';\n                }}\n                onMouseLeave={(e) => {\n                  (e.target as HTMLElement).style.textDecoration = 'none';\n                }}\n              >\n                Your Company\n              </a>\n            </p>\n          </Col>\n          <Col md={6} className=\"text-md-end\">\n            <p style={textStyles}>\n              <a\n                href=\"#\"\n                style={linkStyles}\n                className=\"me-3\"\n                onMouseEnter={(e) => {\n                  (e.target as HTMLElement).style.textDecoration = 'underline';\n                }}\n                onMouseLeave={(e) => {\n                  (e.target as HTMLElement).style.textDecoration = 'none';\n                }}\n              >\n                About\n              </a>\n              <a\n                href=\"#\"\n                style={linkStyles}\n                className=\"me-3\"\n                onMouseEnter={(e) => {\n                  (e.target as HTMLElement).style.textDecoration = 'underline';\n                }}\n                onMouseLeave={(e) => {\n                  (e.target as HTMLElement).style.textDecoration = 'none';\n                }}\n              >\n                Support\n              </a>\n              <a\n                href=\"#\"\n                style={linkStyles}\n                onMouseEnter={(e) => {\n                  (e.target as HTMLElement).style.textDecoration = 'underline';\n                }}\n                onMouseLeave={(e) => {\n                  (e.target as HTMLElement).style.textDecoration = 'none';\n                }}\n              >\n                Contact\n              </a>\n            </p>\n          </Col>\n        </Row>\n      </Container>\n    </footer>\n  );\n};\n\nexport default DattaAbleFooter;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,KAAQ,iBAAiB,CACrD,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,YAAY,CAAG,CACnBC,eAAe,CAAEP,cAAc,CAACQ,MAAM,CAACC,UAAU,CAACC,KAAK,CACvDC,SAAS,CAAE,aAAaX,cAAc,CAACQ,MAAM,CAACI,MAAM,EAAE,CACtDC,OAAO,CAAE,GAAGb,cAAc,CAACc,OAAO,CAAC,CAAC,CAAC,IAAI,CACzCC,SAAS,CAAE,MACb,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG,CACjBC,QAAQ,CAAEjB,cAAc,CAACkB,UAAU,CAACD,QAAQ,CAACE,EAAE,CAC/CC,KAAK,CAAEpB,cAAc,CAACQ,MAAM,CAACa,IAAI,CAACC,SAAS,CAC3CC,MAAM,CAAE,CACV,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG,CACjBJ,KAAK,CAAEpB,cAAc,CAACQ,MAAM,CAACiB,OAAO,CAACC,IAAI,CACzCC,cAAc,CAAE,MAAM,CACtBC,UAAU,CAAE5B,cAAc,CAACkB,UAAU,CAACU,UAAU,CAACC,MACnD,CAAC,CAED,mBACE3B,IAAA,WAAQ4B,KAAK,CAAExB,YAAa,CAAAyB,QAAA,cAC1B7B,IAAA,CAACL,SAAS,EAACmC,KAAK,MAAAD,QAAA,cACd3B,KAAA,CAACN,GAAG,EAACmC,SAAS,CAAC,oBAAoB,CAAAF,QAAA,eACjC7B,IAAA,CAACH,GAAG,EAACmC,EAAE,CAAE,CAAE,CAAAH,QAAA,cACT3B,KAAA,MAAG0B,KAAK,CAAEd,UAAW,CAAAe,QAAA,EAAC,OAClB,CAAC,GAAI,CAAAI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,YAAU,CAAC,GAAG,cAC1ClC,IAAA,MAAG+B,SAAS,CAAC,0BAA0B,CAAI,CAAC,MAAG,CAAC,GAAG,cACnD/B,IAAA,MACEmC,IAAI,CAAC,GAAG,CACRP,KAAK,CAAEN,UAAW,CAClBc,YAAY,CAAGC,CAAC,EAAK,CAClBA,CAAC,CAACC,MAAM,CAAiBV,KAAK,CAACH,cAAc,CAAG,WAAW,CAC9D,CAAE,CACFc,YAAY,CAAGF,CAAC,EAAK,CAClBA,CAAC,CAACC,MAAM,CAAiBV,KAAK,CAACH,cAAc,CAAG,MAAM,CACzD,CAAE,CAAAI,QAAA,CACH,cAED,CAAG,CAAC,EACH,CAAC,CACD,CAAC,cACN7B,IAAA,CAACH,GAAG,EAACmC,EAAE,CAAE,CAAE,CAACD,SAAS,CAAC,aAAa,CAAAF,QAAA,cACjC3B,KAAA,MAAG0B,KAAK,CAAEd,UAAW,CAAAe,QAAA,eACnB7B,IAAA,MACEmC,IAAI,CAAC,GAAG,CACRP,KAAK,CAAEN,UAAW,CAClBS,SAAS,CAAC,MAAM,CAChBK,YAAY,CAAGC,CAAC,EAAK,CAClBA,CAAC,CAACC,MAAM,CAAiBV,KAAK,CAACH,cAAc,CAAG,WAAW,CAC9D,CAAE,CACFc,YAAY,CAAGF,CAAC,EAAK,CAClBA,CAAC,CAACC,MAAM,CAAiBV,KAAK,CAACH,cAAc,CAAG,MAAM,CACzD,CAAE,CAAAI,QAAA,CACH,OAED,CAAG,CAAC,cACJ7B,IAAA,MACEmC,IAAI,CAAC,GAAG,CACRP,KAAK,CAAEN,UAAW,CAClBS,SAAS,CAAC,MAAM,CAChBK,YAAY,CAAGC,CAAC,EAAK,CAClBA,CAAC,CAACC,MAAM,CAAiBV,KAAK,CAACH,cAAc,CAAG,WAAW,CAC9D,CAAE,CACFc,YAAY,CAAGF,CAAC,EAAK,CAClBA,CAAC,CAACC,MAAM,CAAiBV,KAAK,CAACH,cAAc,CAAG,MAAM,CACzD,CAAE,CAAAI,QAAA,CACH,SAED,CAAG,CAAC,cACJ7B,IAAA,MACEmC,IAAI,CAAC,GAAG,CACRP,KAAK,CAAEN,UAAW,CAClBc,YAAY,CAAGC,CAAC,EAAK,CAClBA,CAAC,CAACC,MAAM,CAAiBV,KAAK,CAACH,cAAc,CAAG,WAAW,CAC9D,CAAE,CACFc,YAAY,CAAGF,CAAC,EAAK,CAClBA,CAAC,CAACC,MAAM,CAAiBV,KAAK,CAACH,cAAc,CAAG,MAAM,CACzD,CAAE,CAAAI,QAAA,CACH,SAED,CAAG,CAAC,EACH,CAAC,CACD,CAAC,EACH,CAAC,CACG,CAAC,CACN,CAAC,CAEb,CAAC,CAED,cAAe,CAAA1B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}