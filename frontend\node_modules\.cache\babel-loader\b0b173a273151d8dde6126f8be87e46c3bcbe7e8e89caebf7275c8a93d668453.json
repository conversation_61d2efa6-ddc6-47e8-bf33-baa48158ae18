{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst BreadcrumbItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    active = false,\n    children,\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'li',\n    linkAs: LinkComponent = Anchor,\n    linkProps = {},\n    href,\n    title,\n    target,\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb-item');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(prefix, className, {\n      active\n    }),\n    \"aria-current\": active ? 'page' : undefined,\n    children: active ? children : /*#__PURE__*/_jsx(LinkComponent, {\n      ...linkProps,\n      href: href,\n      title: title,\n      target: target,\n      children: children\n    })\n  });\n});\nBreadcrumbItem.displayName = 'BreadcrumbItem';\nexport default BreadcrumbItem;", "map": {"version": 3, "names": ["classNames", "React", "<PERSON><PERSON>", "useBootstrapPrefix", "jsx", "_jsx", "BreadcrumbItem", "forwardRef", "_ref", "ref", "bsPrefix", "active", "children", "className", "as", "Component", "linkAs", "LinkComponent", "linkProps", "href", "title", "target", "props", "prefix", "undefined", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/BreadcrumbItem.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst BreadcrumbItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  active = false,\n  children,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'li',\n  linkAs: LinkComponent = Anchor,\n  linkProps = {},\n  href,\n  title,\n  target,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb-item');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(prefix, className, {\n      active\n    }),\n    \"aria-current\": active ? 'page' : undefined,\n    children: active ? children : /*#__PURE__*/_jsx(LinkComponent, {\n      ...linkProps,\n      href: href,\n      title: title,\n      target: target,\n      children: children\n    })\n  });\n});\nBreadcrumbItem.displayName = 'BreadcrumbItem';\nexport default BreadcrumbItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAAC,IAAA,EAalDC,GAAG,KAAK;EAAA,IAb2C;IACpDC,QAAQ;IACRC,MAAM,GAAG,KAAK;IACdC,QAAQ;IACRC,SAAS;IACT;IACAC,EAAE,EAAEC,SAAS,GAAG,IAAI;IACpBC,MAAM,EAAEC,aAAa,GAAGf,MAAM;IAC9BgB,SAAS,GAAG,CAAC,CAAC;IACdC,IAAI;IACJC,KAAK;IACLC,MAAM;IACN,GAAGC;EACL,CAAC,GAAAd,IAAA;EACC,MAAMe,MAAM,GAAGpB,kBAAkB,CAACO,QAAQ,EAAE,iBAAiB,CAAC;EAC9D,OAAO,aAAaL,IAAI,CAACU,SAAS,EAAE;IAClCN,GAAG,EAAEA,GAAG;IACR,GAAGa,KAAK;IACRT,SAAS,EAAEb,UAAU,CAACuB,MAAM,EAAEV,SAAS,EAAE;MACvCF;IACF,CAAC,CAAC;IACF,cAAc,EAAEA,MAAM,GAAG,MAAM,GAAGa,SAAS;IAC3CZ,QAAQ,EAAED,MAAM,GAAGC,QAAQ,GAAG,aAAaP,IAAI,CAACY,aAAa,EAAE;MAC7D,GAAGC,SAAS;MACZC,IAAI,EAAEA,IAAI;MACVC,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA,MAAM;MACdT,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,cAAc,CAACmB,WAAW,GAAG,gBAAgB;AAC7C,eAAenB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}