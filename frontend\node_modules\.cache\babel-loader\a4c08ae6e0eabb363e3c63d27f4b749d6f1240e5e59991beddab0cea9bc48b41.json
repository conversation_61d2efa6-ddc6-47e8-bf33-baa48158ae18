{"ast": null, "code": "export { default } from \"./FormGroup.js\";\nexport { default as formGroupClasses } from \"./formGroupClasses.js\";\nexport * from \"./formGroupClasses.js\";", "map": {"version": 3, "names": ["default", "formGroupClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/FormGroup/index.js"], "sourcesContent": ["export { default } from \"./FormGroup.js\";\nexport { default as formGroupClasses } from \"./formGroupClasses.js\";\nexport * from \"./formGroupClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,uBAAuB;AACnE,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}