{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 2.01 6 2c-1.11 0-2 .89-2 2v16c0 1.11.89 2 2 2h12c1.11 0 2-.89 2-2V4c0-1.11-.89-1.99-2-1.99M18 20H6L5.99 4H18z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"8\",\n  cy: \"6\",\n  r: \"1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"11\",\n  cy: \"6\",\n  r: \"1\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 19c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5m2.36-7.36c1.3 1.3 1.3 3.42 0 4.72s-3.42 1.3-4.72 0z\"\n}, \"3\")], 'LocalLaundryServiceOutlined');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "cx", "cy", "r"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/icons-material/esm/LocalLaundryServiceOutlined.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 2.01 6 2c-1.11 0-2 .89-2 2v16c0 1.11.89 2 2 2h12c1.11 0 2-.89 2-2V4c0-1.11-.89-1.99-2-1.99M18 20H6L5.99 4H18z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"8\",\n  cy: \"6\",\n  r: \"1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"11\",\n  cy: \"6\",\n  r: \"1\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 19c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5m2.36-7.36c1.3 1.3 1.3 3.42 0 4.72s-3.42 1.3-4.72 0z\"\n}, \"3\")], 'LocalLaundryServiceOutlined');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,GAAG;EACPC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,6BAA6B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}