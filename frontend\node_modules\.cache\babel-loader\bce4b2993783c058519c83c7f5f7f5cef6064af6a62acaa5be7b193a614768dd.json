{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getScopedCssBaselineUtilityClass(slot) {\n  return generateUtilityClass('MuiScopedCssBaseline', slot);\n}\nconst scopedCssBaselineClasses = generateUtilityClasses('MuiScopedCssBaseline', ['root']);\nexport default scopedCssBaselineClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getScopedCssBaselineUtilityClass", "slot", "scopedCssBaselineClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/ScopedCssBaseline/scopedCssBaselineClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getScopedCssBaselineUtilityClass(slot) {\n  return generateUtilityClass('MuiScopedCssBaseline', slot);\n}\nconst scopedCssBaselineClasses = generateUtilityClasses('MuiScopedCssBaseline', ['root']);\nexport default scopedCssBaselineClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,gCAAgCA,CAACC,IAAI,EAAE;EACrD,OAAOF,oBAAoB,CAAC,sBAAsB,EAAEE,IAAI,CAAC;AAC3D;AACA,MAAMC,wBAAwB,GAAGJ,sBAAsB,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC,CAAC;AACzF,eAAeI,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}