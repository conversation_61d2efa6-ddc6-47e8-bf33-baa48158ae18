{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Collapse from \"../Collapse/index.js\";\nimport StepperContext from \"../Stepper/StepperContext.js\";\nimport StepContext from \"../Step/StepContext.js\";\nimport { getStepContentUtilityClass } from \"./stepContentClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    last\n  } = ownerState;\n  const slots = {\n    root: ['root', last && 'last'],\n    transition: ['transition']\n  };\n  return composeClasses(slots, getStepContentUtilityClass, classes);\n};\nconst StepContentRoot = styled('div', {\n  name: 'MuiStepContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.last && styles.last];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  marginLeft: 12,\n  // half icon\n  paddingLeft: 8 + 12,\n  // margin + half icon\n  paddingRight: 8,\n  borderLeft: theme.vars ? `1px solid ${theme.vars.palette.StepContent.border}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[600]}`,\n  variants: [{\n    props: {\n      last: true\n    },\n    style: {\n      borderLeft: 'none'\n    }\n  }]\n})));\nconst StepContentTransition = styled(Collapse, {\n  name: 'MuiStepContent',\n  slot: 'Transition'\n})({});\nconst StepContent = /*#__PURE__*/React.forwardRef(function StepContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepContent'\n  });\n  const {\n    children,\n    className,\n    TransitionComponent = Collapse,\n    transitionDuration: transitionDurationProp = 'auto',\n    TransitionProps,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const {\n    orientation\n  } = React.useContext(StepperContext);\n  const {\n    active,\n    last,\n    expanded\n  } = React.useContext(StepContext);\n  const ownerState = {\n    ...props,\n    last\n  };\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    if (orientation !== 'vertical') {\n      console.error('MUI: <StepContent /> is only designed for use with the vertical stepper.');\n    }\n  }\n  let transitionDuration = transitionDurationProp;\n  if (transitionDurationProp === 'auto' && !TransitionComponent.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      transition: TransitionProps,\n      ...slotProps\n    }\n  };\n  const [TransitionSlot, transitionProps] = useSlot('transition', {\n    elementType: StepContentTransition,\n    externalForwardedProps,\n    ownerState,\n    className: classes.transition,\n    additionalProps: {\n      in: active || expanded,\n      timeout: transitionDuration,\n      unmountOnExit: true\n    }\n  });\n  return /*#__PURE__*/_jsx(StepContentRoot, {\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    ...other,\n    children: /*#__PURE__*/_jsx(TransitionSlot, {\n      as: TransitionComponent,\n      ...transitionProps,\n      children: children\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? StepContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Collapse\n   * @deprecated Use `slots.transition` instead. This prop will be removed in a future major release. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/).\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Adjust the duration of the content expand transition.\n   * Passed as a prop to the transition component.\n   *\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated Use `slotProps.transition` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default StepContent;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "memoTheme", "useDefaultProps", "Collapse", "StepperContext", "StepContext", "getStepContentUtilityClass", "useSlot", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "last", "slots", "root", "transition", "StepContentRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "marginLeft", "paddingLeft", "paddingRight", "borderLeft", "vars", "palette", "<PERSON><PERSON><PERSON><PERSON>", "border", "mode", "grey", "variants", "style", "StepContentTransition", "forwardRef", "inProps", "ref", "children", "className", "TransitionComponent", "transitionDuration", "transitionDurationProp", "TransitionProps", "slotProps", "other", "orientation", "useContext", "active", "expanded", "process", "env", "NODE_ENV", "console", "error", "muiSupportAuto", "undefined", "externalForwardedProps", "TransitionSlot", "transitionProps", "elementType", "additionalProps", "in", "timeout", "unmountOnExit", "as", "propTypes", "node", "object", "string", "shape", "oneOfType", "func", "sx", "arrayOf", "bool", "oneOf", "number", "appear", "enter", "exit"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/StepContent/StepContent.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Collapse from \"../Collapse/index.js\";\nimport StepperContext from \"../Stepper/StepperContext.js\";\nimport StepContext from \"../Step/StepContext.js\";\nimport { getStepContentUtilityClass } from \"./stepContentClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    last\n  } = ownerState;\n  const slots = {\n    root: ['root', last && 'last'],\n    transition: ['transition']\n  };\n  return composeClasses(slots, getStepContentUtilityClass, classes);\n};\nconst StepContentRoot = styled('div', {\n  name: 'MuiStepContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.last && styles.last];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  marginLeft: 12,\n  // half icon\n  paddingLeft: 8 + 12,\n  // margin + half icon\n  paddingRight: 8,\n  borderLeft: theme.vars ? `1px solid ${theme.vars.palette.StepContent.border}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[600]}`,\n  variants: [{\n    props: {\n      last: true\n    },\n    style: {\n      borderLeft: 'none'\n    }\n  }]\n})));\nconst StepContentTransition = styled(Collapse, {\n  name: 'MuiStepContent',\n  slot: 'Transition'\n})({});\nconst StepContent = /*#__PURE__*/React.forwardRef(function StepContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepContent'\n  });\n  const {\n    children,\n    className,\n    TransitionComponent = Collapse,\n    transitionDuration: transitionDurationProp = 'auto',\n    TransitionProps,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const {\n    orientation\n  } = React.useContext(StepperContext);\n  const {\n    active,\n    last,\n    expanded\n  } = React.useContext(StepContext);\n  const ownerState = {\n    ...props,\n    last\n  };\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    if (orientation !== 'vertical') {\n      console.error('MUI: <StepContent /> is only designed for use with the vertical stepper.');\n    }\n  }\n  let transitionDuration = transitionDurationProp;\n  if (transitionDurationProp === 'auto' && !TransitionComponent.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      transition: TransitionProps,\n      ...slotProps\n    }\n  };\n  const [TransitionSlot, transitionProps] = useSlot('transition', {\n    elementType: StepContentTransition,\n    externalForwardedProps,\n    ownerState,\n    className: classes.transition,\n    additionalProps: {\n      in: active || expanded,\n      timeout: transitionDuration,\n      unmountOnExit: true\n    }\n  });\n  return /*#__PURE__*/_jsx(StepContentRoot, {\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    ...other,\n    children: /*#__PURE__*/_jsx(TransitionSlot, {\n      as: TransitionComponent,\n      ...transitionProps,\n      children: children\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? StepContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Collapse\n   * @deprecated Use `slots.transition` instead. This prop will be removed in a future major release. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/).\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Adjust the duration of the content expand transition.\n   * Passed as a prop to the transition component.\n   *\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated Use `slotProps.transition` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default StepContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,0BAA0B,QAAQ,yBAAyB;AACpE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,IAAI,IAAI,MAAM,CAAC;IAC9BG,UAAU,EAAE,CAAC,YAAY;EAC3B,CAAC;EACD,OAAOjB,cAAc,CAACe,KAAK,EAAER,0BAA0B,EAAEM,OAAO,CAAC;AACnE,CAAC;AACD,MAAMK,eAAe,GAAGjB,MAAM,CAAC,KAAK,EAAE;EACpCkB,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,IAAI,EAAEJ,UAAU,CAACE,IAAI,IAAIS,MAAM,CAACT,IAAI,CAAC;EACtD;AACF,CAAC,CAAC,CAACZ,SAAS,CAAC,CAAC;EACZsB;AACF,CAAC,MAAM;EACLC,UAAU,EAAE,EAAE;EACd;EACAC,WAAW,EAAE,CAAC,GAAG,EAAE;EACnB;EACAC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAEJ,KAAK,CAACK,IAAI,GAAG,aAAaL,KAAK,CAACK,IAAI,CAACC,OAAO,CAACC,WAAW,CAACC,MAAM,EAAE,GAAG,aAAaR,KAAK,CAACM,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGT,KAAK,CAACM,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,GAAGV,KAAK,CAACM,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,EAAE;EACjLC,QAAQ,EAAE,CAAC;IACTb,KAAK,EAAE;MACLR,IAAI,EAAE;IACR,CAAC;IACDsB,KAAK,EAAE;MACLR,UAAU,EAAE;IACd;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMS,qBAAqB,GAAGpC,MAAM,CAACG,QAAQ,EAAE;EAC7Ce,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,MAAMW,WAAW,GAAG,aAAalC,KAAK,CAACyC,UAAU,CAAC,SAASP,WAAWA,CAACQ,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAMlB,KAAK,GAAGnB,eAAe,CAAC;IAC5BmB,KAAK,EAAEiB,OAAO;IACdpB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJsB,QAAQ;IACRC,SAAS;IACTC,mBAAmB,GAAGvC,QAAQ;IAC9BwC,kBAAkB,EAAEC,sBAAsB,GAAG,MAAM;IACnDC,eAAe;IACf/B,KAAK,GAAG,CAAC,CAAC;IACVgC,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAG1B,KAAK;EACT,MAAM;IACJ2B;EACF,CAAC,GAAGpD,KAAK,CAACqD,UAAU,CAAC7C,cAAc,CAAC;EACpC,MAAM;IACJ8C,MAAM;IACNrC,IAAI;IACJsC;EACF,CAAC,GAAGvD,KAAK,CAACqD,UAAU,CAAC5C,WAAW,CAAC;EACjC,MAAMM,UAAU,GAAG;IACjB,GAAGU,KAAK;IACRR;EACF,CAAC;EACD,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,IAAIyC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIN,WAAW,KAAK,UAAU,EAAE;MAC9BO,OAAO,CAACC,KAAK,CAAC,0EAA0E,CAAC;IAC3F;EACF;EACA,IAAIb,kBAAkB,GAAGC,sBAAsB;EAC/C,IAAIA,sBAAsB,KAAK,MAAM,IAAI,CAACF,mBAAmB,CAACe,cAAc,EAAE;IAC5Ed,kBAAkB,GAAGe,SAAS;EAChC;EACA,MAAMC,sBAAsB,GAAG;IAC7B7C,KAAK;IACLgC,SAAS,EAAE;MACT9B,UAAU,EAAE6B,eAAe;MAC3B,GAAGC;IACL;EACF,CAAC;EACD,MAAM,CAACc,cAAc,EAAEC,eAAe,CAAC,GAAGtD,OAAO,CAAC,YAAY,EAAE;IAC9DuD,WAAW,EAAE1B,qBAAqB;IAClCuB,sBAAsB;IACtBhD,UAAU;IACV8B,SAAS,EAAE7B,OAAO,CAACI,UAAU;IAC7B+C,eAAe,EAAE;MACfC,EAAE,EAAEd,MAAM,IAAIC,QAAQ;MACtBc,OAAO,EAAEtB,kBAAkB;MAC3BuB,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EACF,OAAO,aAAazD,IAAI,CAACQ,eAAe,EAAE;IACxCwB,SAAS,EAAE3C,IAAI,CAACc,OAAO,CAACG,IAAI,EAAE0B,SAAS,CAAC;IACxCF,GAAG,EAAEA,GAAG;IACR5B,UAAU,EAAEA,UAAU;IACtB,GAAGoC,KAAK;IACRP,QAAQ,EAAE,aAAa/B,IAAI,CAACmD,cAAc,EAAE;MAC1CO,EAAE,EAAEzB,mBAAmB;MACvB,GAAGmB,eAAe;MAClBrB,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,WAAW,CAACsC,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACE5B,QAAQ,EAAE3C,SAAS,CAACwE,IAAI;EACxB;AACF;AACA;EACEzD,OAAO,EAAEf,SAAS,CAACyE,MAAM;EACzB;AACF;AACA;EACE7B,SAAS,EAAE5C,SAAS,CAAC0E,MAAM;EAC3B;AACF;AACA;AACA;EACEzB,SAAS,EAAEjD,SAAS,CAAC2E,KAAK,CAAC;IACzBxD,UAAU,EAAEnB,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC6E,IAAI,EAAE7E,SAAS,CAACyE,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACExD,KAAK,EAAEjB,SAAS,CAAC2E,KAAK,CAAC;IACrBxD,UAAU,EAAEnB,SAAS,CAACiE;EACxB,CAAC,CAAC;EACF;AACF;AACA;EACEa,EAAE,EAAE9E,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC+E,OAAO,CAAC/E,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC6E,IAAI,EAAE7E,SAAS,CAACyE,MAAM,EAAEzE,SAAS,CAACgF,IAAI,CAAC,CAAC,CAAC,EAAEhF,SAAS,CAAC6E,IAAI,EAAE7E,SAAS,CAACyE,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;EACE5B,mBAAmB,EAAE7C,SAAS,CAACiE,WAAW;EAC1C;AACF;AACA;AACA;AACA;AACA;AACA;EACEnB,kBAAkB,EAAE9C,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAACiF,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAEjF,SAAS,CAACkF,MAAM,EAAElF,SAAS,CAAC2E,KAAK,CAAC;IACpGQ,MAAM,EAAEnF,SAAS,CAACkF,MAAM;IACxBE,KAAK,EAAEpF,SAAS,CAACkF,MAAM;IACvBG,IAAI,EAAErF,SAAS,CAACkF;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;EACElC,eAAe,EAAEhD,SAAS,CAACyE;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAexC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}