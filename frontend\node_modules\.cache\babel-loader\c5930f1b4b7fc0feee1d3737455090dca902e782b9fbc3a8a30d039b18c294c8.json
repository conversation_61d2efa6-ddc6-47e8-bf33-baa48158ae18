{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CardImg", "CardImgOverlay", "CardLink", "CardSubtitle", "CardText", "CardTitle", "jsx", "_jsx", "Card", "forwardRef", "bsPrefix", "className", "bg", "text", "border", "body", "children", "as", "Component", "props", "ref", "prefix", "displayName", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,IAAI,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,CAAC;EAC1CC,QAAQ;EACRC,SAAS;EACTC,EAAE;EACFC,IAAI;EACJC,MAAM;EACNC,IAAI,GAAG,KAAK;EACZC,QAAQ;EACR;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGzB,kBAAkB,CAACc,QAAQ,EAAE,MAAM,CAAC;EACnD,OAAO,aAAaH,IAAI,CAACW,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRR,SAAS,EAAEjB,UAAU,CAACiB,SAAS,EAAEU,MAAM,EAAET,EAAE,IAAI,MAAMA,EAAE,EAAE,EAAEC,IAAI,IAAI,QAAQA,IAAI,EAAE,EAAEC,MAAM,IAAI,UAAUA,MAAM,EAAE,CAAC;IAChHE,QAAQ,EAAED,IAAI,GAAG,aAAaR,IAAI,CAACV,QAAQ,EAAE;MAC3CmB,QAAQ,EAAEA;IACZ,CAAC,CAAC,GAAGA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,IAAI,CAACc,WAAW,GAAG,MAAM;AACzB,eAAeC,MAAM,CAACC,MAAM,CAAChB,IAAI,EAAE;EACjCiB,GAAG,EAAEzB,OAAO;EACZ0B,KAAK,EAAErB,SAAS;EAChBsB,QAAQ,EAAExB,YAAY;EACtByB,IAAI,EAAE/B,QAAQ;EACdgC,IAAI,EAAE3B,QAAQ;EACd4B,IAAI,EAAE1B,QAAQ;EACd2B,MAAM,EAAEhC,UAAU;EAClBiC,MAAM,EAAElC,UAAU;EAClBmC,UAAU,EAAEhC;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}