{"ast": null, "code": "import React from'react';import{Container,<PERSON>,<PERSON>}from'react-bootstrap';import <PERSON>uck<PERSON><PERSON><PERSON> from'../../puck/PuckRenderer';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MinimalTemplate=_ref=>{let{page,content,className}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:`minimal-template ${className||''}`,children:[/*#__PURE__*/_jsx(Container,{fluid:true,className:\"px-0\",children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{lg:8,xl:6,children:/*#__PURE__*/_jsxs(\"div\",{className:\"minimal-content py-5\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"text-center mb-5\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"display-6 fw-light mb-3\",style:{letterSpacing:'-0.02em'},children:page.title}),page.meta_description&&/*#__PURE__*/_jsx(\"p\",{className:\"lead text-muted fw-light mb-4\",style:{fontSize:'1.25rem'},children:page.meta_description}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-center mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'60px',height:'1px',backgroundColor:'#dee2e6'}}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-3\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-circle\",style:{fontSize:'4px',color:'#6c757d'}})}),/*#__PURE__*/_jsx(\"div\",{style:{width:'60px',height:'1px',backgroundColor:'#dee2e6'}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-muted small\",style:{fontSize:'0.875rem'},children:[page.creator&&/*#__PURE__*/_jsx(\"span\",{className:\"me-3\",children:page.creator.name}),/*#__PURE__*/_jsx(\"span\",{children:new Date(page.published_at||page.created_at).toLocaleDateString('en-US',{year:'numeric',month:'long',day:'numeric'})})]})]}),page.featured_image&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-5\",children:/*#__PURE__*/_jsx(\"img\",{src:page.featured_image,alt:page.title,className:\"img-fluid\",style:{width:'100%',height:'auto',borderRadius:'2px',boxShadow:'0 4px 20px rgba(0,0,0,0.1)'}})}),/*#__PURE__*/_jsx(\"div\",{className:\"minimal-page-content\",children:page.editor_type==='puck'?/*#__PURE__*/_jsx(PuckRenderer,{data:page.puck_data,renderedContent:content,className:\"puck-minimal-content\"}):/*#__PURE__*/_jsx(\"div\",{className:\"html-minimal-content\",dangerouslySetInnerHTML:{__html:content},style:{lineHeight:1.8,fontSize:'1.125rem',color:'#495057',fontWeight:300}})}),/*#__PURE__*/_jsxs(\"footer\",{className:\"mt-5 pt-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-center mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'40px',height:'1px',backgroundColor:'#dee2e6'}}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-3\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-circle\",style:{fontSize:'3px',color:'#adb5bd'}})}),/*#__PURE__*/_jsx(\"div\",{style:{width:'40px',height:'1px',backgroundColor:'#dee2e6'}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[page.meta_keywords&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-flex flex-wrap justify-content-center gap-3\",children:page.meta_keywords.split(',').map((keyword,index)=>/*#__PURE__*/_jsx(\"span\",{className:\"text-muted small\",style:{fontSize:'0.8rem',letterSpacing:'0.05em',textTransform:'uppercase'},children:keyword.trim()},index))})}),page.updated_at!==page.created_at&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-muted\",style:{fontSize:'0.8rem'},children:[\"Last updated \",new Date(page.updated_at).toLocaleDateString('en-US',{year:'numeric',month:'short',day:'numeric'}),page.updater&&/*#__PURE__*/_jsxs(\"span\",{children:[\" by \",page.updater.name]})]})]})]})]})})})}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:`\n        .minimal-template {\n          font-family: 'Georgia', 'Times New Roman', serif;\n        }\n        \n        .minimal-template h1,\n        .minimal-template h2,\n        .minimal-template h3,\n        .minimal-template h4,\n        .minimal-template h5,\n        .minimal-template h6 {\n          font-family: 'Helvetica Neue', 'Arial', sans-serif;\n          font-weight: 300;\n        }\n        \n        .minimal-template p {\n          margin-bottom: 1.5rem;\n        }\n        \n        .minimal-template blockquote {\n          border-left: 2px solid #dee2e6;\n          padding-left: 1.5rem;\n          margin: 2rem 0;\n          font-style: italic;\n          color: #6c757d;\n        }\n        \n        .minimal-template img {\n          margin: 2rem 0;\n        }\n        \n        .minimal-template a {\n          color: #495057;\n          text-decoration: underline;\n          text-decoration-thickness: 1px;\n          text-underline-offset: 2px;\n        }\n        \n        .minimal-template a:hover {\n          color: #212529;\n          text-decoration-thickness: 2px;\n        }\n      `})]});};export default MinimalTemplate;", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "MinimalTemplate", "_ref", "page", "content", "className", "children", "fluid", "lg", "xl", "style", "letterSpacing", "title", "meta_description", "fontSize", "width", "height", "backgroundColor", "color", "creator", "name", "Date", "published_at", "created_at", "toLocaleDateString", "year", "month", "day", "featured_image", "src", "alt", "borderRadius", "boxShadow", "editor_type", "data", "puck_data", "renderedContent", "dangerouslySetInnerHTML", "__html", "lineHeight", "fontWeight", "meta_keywords", "split", "map", "keyword", "index", "textTransform", "trim", "updated_at", "updater"], "sources": ["C:/laragon/www/frontend/src/components/cms/templates/MinimalTemplate.tsx"], "sourcesContent": ["import React from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport { CmsPage } from '../../../services/cmsService';\nimport PuckRenderer from '../../puck/PuckRenderer';\n\ninterface MinimalTemplateProps {\n  page: CmsPage;\n  content: string;\n  className?: string;\n}\n\nconst MinimalTemplate: React.FC<MinimalTemplateProps> = ({ page, content, className }) => {\n  return (\n    <div className={`minimal-template ${className || ''}`}>\n      <Container fluid className=\"px-0\">\n        <Row className=\"justify-content-center\">\n          <Col lg={8} xl={6}>\n            <div className=\"minimal-content py-5\">\n              {/* Minimal Header */}\n              <header className=\"text-center mb-5\">\n                <h1 className=\"display-6 fw-light mb-3\" style={{ letterSpacing: '-0.02em' }}>\n                  {page.title}\n                </h1>\n                \n                {page.meta_description && (\n                  <p className=\"lead text-muted fw-light mb-4\" style={{ fontSize: '1.25rem' }}>\n                    {page.meta_description}\n                  </p>\n                )}\n\n                {/* Minimal divider */}\n                <div className=\"d-flex align-items-center justify-content-center mb-4\">\n                  <div style={{ \n                    width: '60px', \n                    height: '1px', \n                    backgroundColor: '#dee2e6' \n                  }}></div>\n                  <div className=\"mx-3\">\n                    <i className=\"fas fa-circle\" style={{ \n                      fontSize: '4px', \n                      color: '#6c757d' \n                    }}></i>\n                  </div>\n                  <div style={{ \n                    width: '60px', \n                    height: '1px', \n                    backgroundColor: '#dee2e6' \n                  }}></div>\n                </div>\n\n                {/* Minimal meta info */}\n                <div className=\"text-muted small\" style={{ fontSize: '0.875rem' }}>\n                  {page.creator && (\n                    <span className=\"me-3\">\n                      {page.creator.name}\n                    </span>\n                  )}\n                  \n                  <span>\n                    {new Date(page.published_at || page.created_at).toLocaleDateString('en-US', {\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric',\n                    })}\n                  </span>\n                </div>\n              </header>\n\n              {/* Featured Image - Minimal Style */}\n              {page.featured_image && (\n                <div className=\"mb-5\">\n                  <img \n                    src={page.featured_image} \n                    alt={page.title}\n                    className=\"img-fluid\"\n                    style={{ \n                      width: '100%', \n                      height: 'auto',\n                      borderRadius: '2px',\n                      boxShadow: '0 4px 20px rgba(0,0,0,0.1)'\n                    }}\n                  />\n                </div>\n              )}\n\n              {/* Content */}\n              <div className=\"minimal-page-content\">\n                {page.editor_type === 'puck' ? (\n                  <PuckRenderer \n                    data={page.puck_data} \n                    renderedContent={content}\n                    className=\"puck-minimal-content\"\n                  />\n                ) : (\n                  <div\n                    className=\"html-minimal-content\"\n                    dangerouslySetInnerHTML={{ __html: content }}\n                    style={{\n                      lineHeight: 1.8,\n                      fontSize: '1.125rem',\n                      color: '#495057',\n                      fontWeight: 300,\n                    }}\n                  />\n                )}\n              </div>\n\n              {/* Minimal Footer */}\n              <footer className=\"mt-5 pt-5\">\n                {/* Minimal divider */}\n                <div className=\"d-flex align-items-center justify-content-center mb-4\">\n                  <div style={{ \n                    width: '40px', \n                    height: '1px', \n                    backgroundColor: '#dee2e6' \n                  }}></div>\n                  <div className=\"mx-3\">\n                    <i className=\"fas fa-circle\" style={{ \n                      fontSize: '3px', \n                      color: '#adb5bd' \n                    }}></i>\n                  </div>\n                  <div style={{ \n                    width: '40px', \n                    height: '1px', \n                    backgroundColor: '#dee2e6' \n                  }}></div>\n                </div>\n\n                <div className=\"text-center\">\n                  {/* Tags - Minimal Style */}\n                  {page.meta_keywords && (\n                    <div className=\"mb-4\">\n                      <div className=\"d-flex flex-wrap justify-content-center gap-3\">\n                        {page.meta_keywords.split(',').map((keyword, index) => (\n                          <span \n                            key={index} \n                            className=\"text-muted small\"\n                            style={{ \n                              fontSize: '0.8rem',\n                              letterSpacing: '0.05em',\n                              textTransform: 'uppercase'\n                            }}\n                          >\n                            {keyword.trim()}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Update info */}\n                  {page.updated_at !== page.created_at && (\n                    <div className=\"text-muted\" style={{ fontSize: '0.8rem' }}>\n                      Last updated {new Date(page.updated_at).toLocaleDateString('en-US', {\n                        year: 'numeric',\n                        month: 'short',\n                        day: 'numeric',\n                      })}\n                      {page.updater && (\n                        <span> by {page.updater.name}</span>\n                      )}\n                    </div>\n                  )}\n                </div>\n              </footer>\n            </div>\n          </Col>\n        </Row>\n      </Container>\n\n      {/* Custom CSS for minimal template */}\n      <style jsx>{`\n        .minimal-template {\n          font-family: 'Georgia', 'Times New Roman', serif;\n        }\n        \n        .minimal-template h1,\n        .minimal-template h2,\n        .minimal-template h3,\n        .minimal-template h4,\n        .minimal-template h5,\n        .minimal-template h6 {\n          font-family: 'Helvetica Neue', 'Arial', sans-serif;\n          font-weight: 300;\n        }\n        \n        .minimal-template p {\n          margin-bottom: 1.5rem;\n        }\n        \n        .minimal-template blockquote {\n          border-left: 2px solid #dee2e6;\n          padding-left: 1.5rem;\n          margin: 2rem 0;\n          font-style: italic;\n          color: #6c757d;\n        }\n        \n        .minimal-template img {\n          margin: 2rem 0;\n        }\n        \n        .minimal-template a {\n          color: #495057;\n          text-decoration: underline;\n          text-decoration-thickness: 1px;\n          text-underline-offset: 2px;\n        }\n        \n        .minimal-template a:hover {\n          color: #212529;\n          text-decoration-thickness: 2px;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default MinimalTemplate;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,KAAQ,iBAAiB,CAErD,MAAO,CAAAC,YAAY,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQnD,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAAkC,IAAjC,CAAEC,IAAI,CAAEC,OAAO,CAAEC,SAAU,CAAC,CAAAH,IAAA,CACnF,mBACEF,KAAA,QAAKK,SAAS,CAAE,oBAAoBA,SAAS,EAAI,EAAE,EAAG,CAAAC,QAAA,eACpDR,IAAA,CAACL,SAAS,EAACc,KAAK,MAACF,SAAS,CAAC,MAAM,CAAAC,QAAA,cAC/BR,IAAA,CAACJ,GAAG,EAACW,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrCR,IAAA,CAACH,GAAG,EAACa,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAH,QAAA,cAChBN,KAAA,QAAKK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eAEnCN,KAAA,WAAQK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAClCR,IAAA,OAAIO,SAAS,CAAC,yBAAyB,CAACK,KAAK,CAAE,CAAEC,aAAa,CAAE,SAAU,CAAE,CAAAL,QAAA,CACzEH,IAAI,CAACS,KAAK,CACT,CAAC,CAEJT,IAAI,CAACU,gBAAgB,eACpBf,IAAA,MAAGO,SAAS,CAAC,+BAA+B,CAACK,KAAK,CAAE,CAAEI,QAAQ,CAAE,SAAU,CAAE,CAAAR,QAAA,CACzEH,IAAI,CAACU,gBAAgB,CACrB,CACJ,cAGDb,KAAA,QAAKK,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eACpER,IAAA,QAAKY,KAAK,CAAE,CACVK,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbC,eAAe,CAAE,SACnB,CAAE,CAAM,CAAC,cACTnB,IAAA,QAAKO,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBR,IAAA,MAAGO,SAAS,CAAC,eAAe,CAACK,KAAK,CAAE,CAClCI,QAAQ,CAAE,KAAK,CACfI,KAAK,CAAE,SACT,CAAE,CAAI,CAAC,CACJ,CAAC,cACNpB,IAAA,QAAKY,KAAK,CAAE,CACVK,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbC,eAAe,CAAE,SACnB,CAAE,CAAM,CAAC,EACN,CAAC,cAGNjB,KAAA,QAAKK,SAAS,CAAC,kBAAkB,CAACK,KAAK,CAAE,CAAEI,QAAQ,CAAE,UAAW,CAAE,CAAAR,QAAA,EAC/DH,IAAI,CAACgB,OAAO,eACXrB,IAAA,SAAMO,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnBH,IAAI,CAACgB,OAAO,CAACC,IAAI,CACd,CACP,cAEDtB,IAAA,SAAAQ,QAAA,CACG,GAAI,CAAAe,IAAI,CAAClB,IAAI,CAACmB,YAAY,EAAInB,IAAI,CAACoB,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAE,CAC1EC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACE,CAAC,EACJ,CAAC,EACA,CAAC,CAGRxB,IAAI,CAACyB,cAAc,eAClB9B,IAAA,QAAKO,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBR,IAAA,QACE+B,GAAG,CAAE1B,IAAI,CAACyB,cAAe,CACzBE,GAAG,CAAE3B,IAAI,CAACS,KAAM,CAChBP,SAAS,CAAC,WAAW,CACrBK,KAAK,CAAE,CACLK,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACde,YAAY,CAAE,KAAK,CACnBC,SAAS,CAAE,4BACb,CAAE,CACH,CAAC,CACC,CACN,cAGDlC,IAAA,QAAKO,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClCH,IAAI,CAAC8B,WAAW,GAAK,MAAM,cAC1BnC,IAAA,CAACF,YAAY,EACXsC,IAAI,CAAE/B,IAAI,CAACgC,SAAU,CACrBC,eAAe,CAAEhC,OAAQ,CACzBC,SAAS,CAAC,sBAAsB,CACjC,CAAC,cAEFP,IAAA,QACEO,SAAS,CAAC,sBAAsB,CAChCgC,uBAAuB,CAAE,CAAEC,MAAM,CAAElC,OAAQ,CAAE,CAC7CM,KAAK,CAAE,CACL6B,UAAU,CAAE,GAAG,CACfzB,QAAQ,CAAE,UAAU,CACpBI,KAAK,CAAE,SAAS,CAChBsB,UAAU,CAAE,GACd,CAAE,CACH,CACF,CACE,CAAC,cAGNxC,KAAA,WAAQK,SAAS,CAAC,WAAW,CAAAC,QAAA,eAE3BN,KAAA,QAAKK,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eACpER,IAAA,QAAKY,KAAK,CAAE,CACVK,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbC,eAAe,CAAE,SACnB,CAAE,CAAM,CAAC,cACTnB,IAAA,QAAKO,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBR,IAAA,MAAGO,SAAS,CAAC,eAAe,CAACK,KAAK,CAAE,CAClCI,QAAQ,CAAE,KAAK,CACfI,KAAK,CAAE,SACT,CAAE,CAAI,CAAC,CACJ,CAAC,cACNpB,IAAA,QAAKY,KAAK,CAAE,CACVK,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbC,eAAe,CAAE,SACnB,CAAE,CAAM,CAAC,EACN,CAAC,cAENjB,KAAA,QAAKK,SAAS,CAAC,aAAa,CAAAC,QAAA,EAEzBH,IAAI,CAACsC,aAAa,eACjB3C,IAAA,QAAKO,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBR,IAAA,QAAKO,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAC3DH,IAAI,CAACsC,aAAa,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAChD/C,IAAA,SAEEO,SAAS,CAAC,kBAAkB,CAC5BK,KAAK,CAAE,CACLI,QAAQ,CAAE,QAAQ,CAClBH,aAAa,CAAE,QAAQ,CACvBmC,aAAa,CAAE,WACjB,CAAE,CAAAxC,QAAA,CAEDsC,OAAO,CAACG,IAAI,CAAC,CAAC,EARVF,KASD,CACP,CAAC,CACC,CAAC,CACH,CACN,CAGA1C,IAAI,CAAC6C,UAAU,GAAK7C,IAAI,CAACoB,UAAU,eAClCvB,KAAA,QAAKK,SAAS,CAAC,YAAY,CAACK,KAAK,CAAE,CAAEI,QAAQ,CAAE,QAAS,CAAE,CAAAR,QAAA,EAAC,eAC5C,CAAC,GAAI,CAAAe,IAAI,CAAClB,IAAI,CAAC6C,UAAU,CAAC,CAACxB,kBAAkB,CAAC,OAAO,CAAE,CAClEC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,SACP,CAAC,CAAC,CACDxB,IAAI,CAAC8C,OAAO,eACXjD,KAAA,SAAAM,QAAA,EAAM,MAAI,CAACH,IAAI,CAAC8C,OAAO,CAAC7B,IAAI,EAAO,CACpC,EACE,CACN,EACE,CAAC,EACA,CAAC,EACN,CAAC,CACH,CAAC,CACH,CAAC,CACG,CAAC,cAGZtB,IAAA,UAAOD,GAAG,MAAAS,QAAA,CAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAQ,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}