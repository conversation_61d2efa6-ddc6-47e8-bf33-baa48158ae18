{"ast": null, "code": "import React from'react';import{Snackbar,Alert}from'@mui/material';import{jsx as _jsx}from\"react/jsx-runtime\";const Toast=_ref=>{let{open,message,severity,onClose,autoHideDuration=6000}=_ref;return/*#__PURE__*/_jsx(Snackbar,{open:open,autoHideDuration:autoHideDuration,onClose:onClose,anchorOrigin:{vertical:'top',horizontal:'right'},children:/*#__PURE__*/_jsx(Alert,{onClose:onClose,severity:severity,sx:{width:'100%'},children:message})});};export default Toast;", "map": {"version": 3, "names": ["React", "Snackbar", "<PERSON><PERSON>", "jsx", "_jsx", "Toast", "_ref", "open", "message", "severity", "onClose", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "children", "sx", "width"], "sources": ["C:/laragon/www/frontend/src/components/common/Toast.tsx"], "sourcesContent": ["import React from 'react';\nimport { Snackbar, Alert, AlertColor } from '@mui/material';\n\ninterface ToastProps {\n  open: boolean;\n  message: string;\n  severity: AlertColor;\n  onClose: () => void;\n  autoHideDuration?: number;\n}\n\nconst Toast: React.FC<ToastProps> = ({\n  open,\n  message,\n  severity,\n  onClose,\n  autoHideDuration = 6000,\n}) => {\n  return (\n    <Snackbar\n      open={open}\n      autoHideDuration={autoHideDuration}\n      onClose={onClose}\n      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}\n    >\n      <Alert onClose={onClose} severity={severity} sx={{ width: '100%' }}>\n        {message}\n      </Alert>\n    </Snackbar>\n  );\n};\n\nexport default Toast;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,CAAEC,KAAK,KAAoB,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAU5D,KAAM,CAAAC,KAA2B,CAAGC,IAAA,EAM9B,IAN+B,CACnCC,IAAI,CACJC,OAAO,CACPC,QAAQ,CACRC,OAAO,CACPC,gBAAgB,CAAG,IACrB,CAAC,CAAAL,IAAA,CACC,mBACEF,IAAA,CAACH,QAAQ,EACPM,IAAI,CAAEA,IAAK,CACXI,gBAAgB,CAAEA,gBAAiB,CACnCD,OAAO,CAAEA,OAAQ,CACjBE,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CAAE,CAAAC,QAAA,cAEvDX,IAAA,CAACF,KAAK,EAACQ,OAAO,CAAEA,OAAQ,CAACD,QAAQ,CAAEA,QAAS,CAACO,EAAE,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAF,QAAA,CAChEP,OAAO,CACH,CAAC,CACA,CAAC,CAEf,CAAC,CAED,cAAe,CAAAH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}