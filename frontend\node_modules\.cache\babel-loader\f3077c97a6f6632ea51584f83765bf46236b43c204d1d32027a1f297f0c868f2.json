{"ast": null, "code": "export { default } from \"./Drawer.js\";\nexport { default as drawerClasses } from \"./drawerClasses.js\";\nexport * from \"./drawerClasses.js\";", "map": {"version": 3, "names": ["default", "drawerClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Drawer/index.js"], "sourcesContent": ["export { default } from \"./Drawer.js\";\nexport { default as drawerClasses } from \"./drawerClasses.js\";\nexport * from \"./drawerClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,aAAa,QAAQ,oBAAoB;AAC7D,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}