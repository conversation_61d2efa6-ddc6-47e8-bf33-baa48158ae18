{"ast": null, "code": "\"use client\";\n\nimport contains from 'dom-helpers/contains';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { cloneElement, useCallback, useRef } from 'react';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport warning from 'warning';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { getChildRef } from '@restart/ui/utils';\nimport Overlay from './Overlay';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction normalizeDelay(delay) {\n  return delay && typeof delay === 'object' ? delay : {\n    show: delay,\n    hide: delay\n  };\n}\n\n// Simple implementation of mouseEnter and mouseLeave.\n// React's built version is broken: https://github.com/facebook/react/issues/4251\n// for cases when the trigger is disabled and mouseOut/Over can cause flicker\n// moving from one child element to another.\nfunction handleMouseOverOut(handler, args, relatedNative) {\n  const [e] = args;\n  const target = e.currentTarget;\n  const related = e.relatedTarget || e.nativeEvent[relatedNative];\n  if ((!related || related !== target) && !contains(target, related)) {\n    handler(...args);\n  }\n}\nconst triggerType = PropTypes.oneOf(['click', 'hover', 'focus']);\nconst OverlayTrigger = _ref => {\n  let {\n    trigger = ['hover', 'focus'],\n    overlay,\n    children,\n    popperConfig = {},\n    show: propsShow,\n    defaultShow = false,\n    onToggle,\n    delay: propsDelay,\n    placement,\n    flip = placement && placement.indexOf('auto') !== -1,\n    ...props\n  } = _ref;\n  const triggerNodeRef = useRef(null);\n  const mergedRef = useMergedRefs(triggerNodeRef, getChildRef(children));\n  const timeout = useTimeout();\n  const hoverStateRef = useRef('');\n  const [show, setShow] = useUncontrolledProp(propsShow, defaultShow, onToggle);\n  const delay = normalizeDelay(propsDelay);\n  const {\n    onFocus,\n    onBlur,\n    onClick\n  } = typeof children !== 'function' ? React.Children.only(children).props : {};\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const handleShow = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'show';\n    if (!delay.show) {\n      setShow(true);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'show') setShow(true);\n    }, delay.show);\n  }, [delay.show, setShow, timeout]);\n  const handleHide = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'hide';\n    if (!delay.hide) {\n      setShow(false);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'hide') setShow(false);\n    }, delay.hide);\n  }, [delay.hide, setShow, timeout]);\n  const handleFocus = useCallback(function () {\n    handleShow();\n    onFocus == null || onFocus(...arguments);\n  }, [handleShow, onFocus]);\n  const handleBlur = useCallback(function () {\n    handleHide();\n    onBlur == null || onBlur(...arguments);\n  }, [handleHide, onBlur]);\n  const handleClick = useCallback(function () {\n    setShow(!show);\n    onClick == null || onClick(...arguments);\n  }, [onClick, setShow, show]);\n  const handleMouseOver = useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    handleMouseOverOut(handleShow, args, 'fromElement');\n  }, [handleShow]);\n  const handleMouseOut = useCallback(function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    handleMouseOverOut(handleHide, args, 'toElement');\n  }, [handleHide]);\n  const triggers = trigger == null ? [] : [].concat(trigger);\n  const triggerProps = {\n    ref: attachRef\n  };\n  if (triggers.indexOf('click') !== -1) {\n    triggerProps.onClick = handleClick;\n  }\n  if (triggers.indexOf('focus') !== -1) {\n    triggerProps.onFocus = handleFocus;\n    triggerProps.onBlur = handleBlur;\n  }\n  if (triggers.indexOf('hover') !== -1) {\n    process.env.NODE_ENV !== \"production\" ? warning(triggers.length > 1, '[react-bootstrap] Specifying only the `\"hover\"` trigger limits the visibility of the overlay to just mouse users. Consider also including the `\"focus\"` trigger so that touch and keyboard only users can see the overlay as well.') : void 0;\n    triggerProps.onMouseOver = handleMouseOver;\n    triggerProps.onMouseOut = handleMouseOut;\n  }\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [typeof children === 'function' ? children(triggerProps) : /*#__PURE__*/cloneElement(children, triggerProps), /*#__PURE__*/_jsx(Overlay, {\n      ...props,\n      show: show,\n      onHide: handleHide,\n      flip: flip,\n      placement: placement,\n      popperConfig: popperConfig,\n      target: triggerNodeRef.current,\n      children: overlay\n    })]\n  });\n};\nexport default OverlayTrigger;", "map": {"version": 3, "names": ["contains", "PropTypes", "React", "cloneElement", "useCallback", "useRef", "useTimeout", "warning", "useUncontrolledProp", "useMergedRefs", "getChildRef", "Overlay", "safeFindDOMNode", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "normalizeDelay", "delay", "show", "hide", "handleMouseOverOut", "handler", "args", "relatedNative", "e", "target", "currentTarget", "related", "relatedTarget", "nativeEvent", "triggerType", "oneOf", "OverlayTrigger", "_ref", "trigger", "overlay", "children", "popperConfig", "propsShow", "defaultShow", "onToggle", "props<PERSON><PERSON><PERSON>", "placement", "flip", "indexOf", "props", "triggerNodeRef", "mergedRef", "timeout", "hoverStateRef", "setShow", "onFocus", "onBlur", "onClick", "Children", "only", "attachRef", "r", "handleShow", "clear", "current", "set", "handleHide", "handleFocus", "arguments", "handleBlur", "handleClick", "handleMouseOver", "_len", "length", "Array", "_key", "handleMouseOut", "_len2", "_key2", "triggers", "concat", "triggerProps", "ref", "process", "env", "NODE_ENV", "onMouseOver", "onMouseOut", "onHide"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/OverlayTrigger.js"], "sourcesContent": ["\"use client\";\n\nimport contains from 'dom-helpers/contains';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { cloneElement, useCallback, useRef } from 'react';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport warning from 'warning';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { getChildRef } from '@restart/ui/utils';\nimport Overlay from './Overlay';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction normalizeDelay(delay) {\n  return delay && typeof delay === 'object' ? delay : {\n    show: delay,\n    hide: delay\n  };\n}\n\n// Simple implementation of mouseEnter and mouseLeave.\n// React's built version is broken: https://github.com/facebook/react/issues/4251\n// for cases when the trigger is disabled and mouseOut/Over can cause flicker\n// moving from one child element to another.\nfunction handleMouseOverOut(handler, args, relatedNative) {\n  const [e] = args;\n  const target = e.currentTarget;\n  const related = e.relatedTarget || e.nativeEvent[relatedNative];\n  if ((!related || related !== target) && !contains(target, related)) {\n    handler(...args);\n  }\n}\nconst triggerType = PropTypes.oneOf(['click', 'hover', 'focus']);\nconst OverlayTrigger = ({\n  trigger = ['hover', 'focus'],\n  overlay,\n  children,\n  popperConfig = {},\n  show: propsShow,\n  defaultShow = false,\n  onToggle,\n  delay: propsDelay,\n  placement,\n  flip = placement && placement.indexOf('auto') !== -1,\n  ...props\n}) => {\n  const triggerNodeRef = useRef(null);\n  const mergedRef = useMergedRefs(triggerNodeRef, getChildRef(children));\n  const timeout = useTimeout();\n  const hoverStateRef = useRef('');\n  const [show, setShow] = useUncontrolledProp(propsShow, defaultShow, onToggle);\n  const delay = normalizeDelay(propsDelay);\n  const {\n    onFocus,\n    onBlur,\n    onClick\n  } = typeof children !== 'function' ? React.Children.only(children).props : {};\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const handleShow = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'show';\n    if (!delay.show) {\n      setShow(true);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'show') setShow(true);\n    }, delay.show);\n  }, [delay.show, setShow, timeout]);\n  const handleHide = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'hide';\n    if (!delay.hide) {\n      setShow(false);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'hide') setShow(false);\n    }, delay.hide);\n  }, [delay.hide, setShow, timeout]);\n  const handleFocus = useCallback((...args) => {\n    handleShow();\n    onFocus == null || onFocus(...args);\n  }, [handleShow, onFocus]);\n  const handleBlur = useCallback((...args) => {\n    handleHide();\n    onBlur == null || onBlur(...args);\n  }, [handleHide, onBlur]);\n  const handleClick = useCallback((...args) => {\n    setShow(!show);\n    onClick == null || onClick(...args);\n  }, [onClick, setShow, show]);\n  const handleMouseOver = useCallback((...args) => {\n    handleMouseOverOut(handleShow, args, 'fromElement');\n  }, [handleShow]);\n  const handleMouseOut = useCallback((...args) => {\n    handleMouseOverOut(handleHide, args, 'toElement');\n  }, [handleHide]);\n  const triggers = trigger == null ? [] : [].concat(trigger);\n  const triggerProps = {\n    ref: attachRef\n  };\n  if (triggers.indexOf('click') !== -1) {\n    triggerProps.onClick = handleClick;\n  }\n  if (triggers.indexOf('focus') !== -1) {\n    triggerProps.onFocus = handleFocus;\n    triggerProps.onBlur = handleBlur;\n  }\n  if (triggers.indexOf('hover') !== -1) {\n    process.env.NODE_ENV !== \"production\" ? warning(triggers.length > 1, '[react-bootstrap] Specifying only the `\"hover\"` trigger limits the visibility of the overlay to just mouse users. Consider also including the `\"focus\"` trigger so that touch and keyboard only users can see the overlay as well.') : void 0;\n    triggerProps.onMouseOver = handleMouseOver;\n    triggerProps.onMouseOut = handleMouseOut;\n  }\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [typeof children === 'function' ? children(triggerProps) : /*#__PURE__*/cloneElement(children, triggerProps), /*#__PURE__*/_jsx(Overlay, {\n      ...props,\n      show: show,\n      onHide: handleHide,\n      flip: flip,\n      placement: placement,\n      popperConfig: popperConfig,\n      target: triggerNodeRef.current,\n      children: overlay\n    })]\n  });\n};\nexport default OverlayTrigger;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACzD,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,OAAO,MAAM,SAAS;AAC7B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAOA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG;IAClDC,IAAI,EAAED,KAAK;IACXE,IAAI,EAAEF;EACR,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,aAAa,EAAE;EACxD,MAAM,CAACC,CAAC,CAAC,GAAGF,IAAI;EAChB,MAAMG,MAAM,GAAGD,CAAC,CAACE,aAAa;EAC9B,MAAMC,OAAO,GAAGH,CAAC,CAACI,aAAa,IAAIJ,CAAC,CAACK,WAAW,CAACN,aAAa,CAAC;EAC/D,IAAI,CAAC,CAACI,OAAO,IAAIA,OAAO,KAAKF,MAAM,KAAK,CAAC5B,QAAQ,CAAC4B,MAAM,EAAEE,OAAO,CAAC,EAAE;IAClEN,OAAO,CAAC,GAAGC,IAAI,CAAC;EAClB;AACF;AACA,MAAMQ,WAAW,GAAGhC,SAAS,CAACiC,KAAK,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAChE,MAAMC,cAAc,GAAGC,IAAA,IAYjB;EAAA,IAZkB;IACtBC,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;IAC5BC,OAAO;IACPC,QAAQ;IACRC,YAAY,GAAG,CAAC,CAAC;IACjBnB,IAAI,EAAEoB,SAAS;IACfC,WAAW,GAAG,KAAK;IACnBC,QAAQ;IACRvB,KAAK,EAAEwB,UAAU;IACjBC,SAAS;IACTC,IAAI,GAAGD,SAAS,IAAIA,SAAS,CAACE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpD,GAAGC;EACL,CAAC,GAAAZ,IAAA;EACC,MAAMa,cAAc,GAAG5C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM6C,SAAS,GAAGzC,aAAa,CAACwC,cAAc,EAAEvC,WAAW,CAAC6B,QAAQ,CAAC,CAAC;EACtE,MAAMY,OAAO,GAAG7C,UAAU,CAAC,CAAC;EAC5B,MAAM8C,aAAa,GAAG/C,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM,CAACgB,IAAI,EAAEgC,OAAO,CAAC,GAAG7C,mBAAmB,CAACiC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,CAAC;EAC7E,MAAMvB,KAAK,GAAGD,cAAc,CAACyB,UAAU,CAAC;EACxC,MAAM;IACJU,OAAO;IACPC,MAAM;IACNC;EACF,CAAC,GAAG,OAAOjB,QAAQ,KAAK,UAAU,GAAGrC,KAAK,CAACuD,QAAQ,CAACC,IAAI,CAACnB,QAAQ,CAAC,CAACS,KAAK,GAAG,CAAC,CAAC;EAC7E,MAAMW,SAAS,GAAGC,CAAC,IAAI;IACrBV,SAAS,CAACtC,eAAe,CAACgD,CAAC,CAAC,CAAC;EAC/B,CAAC;EACD,MAAMC,UAAU,GAAGzD,WAAW,CAAC,MAAM;IACnC+C,OAAO,CAACW,KAAK,CAAC,CAAC;IACfV,aAAa,CAACW,OAAO,GAAG,MAAM;IAC9B,IAAI,CAAC3C,KAAK,CAACC,IAAI,EAAE;MACfgC,OAAO,CAAC,IAAI,CAAC;MACb;IACF;IACAF,OAAO,CAACa,GAAG,CAAC,MAAM;MAChB,IAAIZ,aAAa,CAACW,OAAO,KAAK,MAAM,EAAEV,OAAO,CAAC,IAAI,CAAC;IACrD,CAAC,EAAEjC,KAAK,CAACC,IAAI,CAAC;EAChB,CAAC,EAAE,CAACD,KAAK,CAACC,IAAI,EAAEgC,OAAO,EAAEF,OAAO,CAAC,CAAC;EAClC,MAAMc,UAAU,GAAG7D,WAAW,CAAC,MAAM;IACnC+C,OAAO,CAACW,KAAK,CAAC,CAAC;IACfV,aAAa,CAACW,OAAO,GAAG,MAAM;IAC9B,IAAI,CAAC3C,KAAK,CAACE,IAAI,EAAE;MACf+B,OAAO,CAAC,KAAK,CAAC;MACd;IACF;IACAF,OAAO,CAACa,GAAG,CAAC,MAAM;MAChB,IAAIZ,aAAa,CAACW,OAAO,KAAK,MAAM,EAAEV,OAAO,CAAC,KAAK,CAAC;IACtD,CAAC,EAAEjC,KAAK,CAACE,IAAI,CAAC;EAChB,CAAC,EAAE,CAACF,KAAK,CAACE,IAAI,EAAE+B,OAAO,EAAEF,OAAO,CAAC,CAAC;EAClC,MAAMe,WAAW,GAAG9D,WAAW,CAAC,YAAa;IAC3CyD,UAAU,CAAC,CAAC;IACZP,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC,GAAAa,SAAO,CAAC;EACrC,CAAC,EAAE,CAACN,UAAU,EAAEP,OAAO,CAAC,CAAC;EACzB,MAAMc,UAAU,GAAGhE,WAAW,CAAC,YAAa;IAC1C6D,UAAU,CAAC,CAAC;IACZV,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC,GAAAY,SAAO,CAAC;EACnC,CAAC,EAAE,CAACF,UAAU,EAAEV,MAAM,CAAC,CAAC;EACxB,MAAMc,WAAW,GAAGjE,WAAW,CAAC,YAAa;IAC3CiD,OAAO,CAAC,CAAChC,IAAI,CAAC;IACdmC,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC,GAAAW,SAAO,CAAC;EACrC,CAAC,EAAE,CAACX,OAAO,EAAEH,OAAO,EAAEhC,IAAI,CAAC,CAAC;EAC5B,MAAMiD,eAAe,GAAGlE,WAAW,CAAC,YAAa;IAAA,SAAAmE,IAAA,GAAAJ,SAAA,CAAAK,MAAA,EAAT/C,IAAI,OAAAgD,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAJjD,IAAI,CAAAiD,IAAA,IAAAP,SAAA,CAAAO,IAAA;IAAA;IAC1CnD,kBAAkB,CAACsC,UAAU,EAAEpC,IAAI,EAAE,aAAa,CAAC;EACrD,CAAC,EAAE,CAACoC,UAAU,CAAC,CAAC;EAChB,MAAMc,cAAc,GAAGvE,WAAW,CAAC,YAAa;IAAA,SAAAwE,KAAA,GAAAT,SAAA,CAAAK,MAAA,EAAT/C,IAAI,OAAAgD,KAAA,CAAAG,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJpD,IAAI,CAAAoD,KAAA,IAAAV,SAAA,CAAAU,KAAA;IAAA;IACzCtD,kBAAkB,CAAC0C,UAAU,EAAExC,IAAI,EAAE,WAAW,CAAC;EACnD,CAAC,EAAE,CAACwC,UAAU,CAAC,CAAC;EAChB,MAAMa,QAAQ,GAAGzC,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC0C,MAAM,CAAC1C,OAAO,CAAC;EAC1D,MAAM2C,YAAY,GAAG;IACnBC,GAAG,EAAEtB;EACP,CAAC;EACD,IAAImB,QAAQ,CAAC/B,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCiC,YAAY,CAACxB,OAAO,GAAGa,WAAW;EACpC;EACA,IAAIS,QAAQ,CAAC/B,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCiC,YAAY,CAAC1B,OAAO,GAAGY,WAAW;IAClCc,YAAY,CAACzB,MAAM,GAAGa,UAAU;EAClC;EACA,IAAIU,QAAQ,CAAC/B,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCmC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7E,OAAO,CAACuE,QAAQ,CAACN,MAAM,GAAG,CAAC,EAAE,oOAAoO,CAAC,GAAG,KAAK,CAAC;IACnTQ,YAAY,CAACK,WAAW,GAAGf,eAAe;IAC1CU,YAAY,CAACM,UAAU,GAAGX,cAAc;EAC1C;EACA,OAAO,aAAazD,KAAK,CAACF,SAAS,EAAE;IACnCuB,QAAQ,EAAE,CAAC,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACyC,YAAY,CAAC,GAAG,aAAa7E,YAAY,CAACoC,QAAQ,EAAEyC,YAAY,CAAC,EAAE,aAAalE,IAAI,CAACH,OAAO,EAAE;MACjJ,GAAGqC,KAAK;MACR3B,IAAI,EAAEA,IAAI;MACVkE,MAAM,EAAEtB,UAAU;MAClBnB,IAAI,EAAEA,IAAI;MACVD,SAAS,EAAEA,SAAS;MACpBL,YAAY,EAAEA,YAAY;MAC1BZ,MAAM,EAAEqB,cAAc,CAACc,OAAO;MAC9BxB,QAAQ,EAAED;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}