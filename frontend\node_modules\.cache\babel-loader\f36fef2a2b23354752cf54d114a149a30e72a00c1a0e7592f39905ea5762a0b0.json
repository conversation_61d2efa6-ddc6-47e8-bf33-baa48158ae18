{"ast": null, "code": "export { default } from \"./TabPanel.js\";\nexport { default as tabPanelClasses } from \"./tabPanelClasses.js\";\nexport * from \"./tabPanelClasses.js\";", "map": {"version": 3, "names": ["default", "tabPanelClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TabPanel/index.js"], "sourcesContent": ["export { default } from \"./TabPanel.js\";\nexport { default as tabPanelClasses } from \"./tabPanelClasses.js\";\nexport * from \"./tabPanelClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,eAAe,QAAQ,sBAAsB;AACjE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}