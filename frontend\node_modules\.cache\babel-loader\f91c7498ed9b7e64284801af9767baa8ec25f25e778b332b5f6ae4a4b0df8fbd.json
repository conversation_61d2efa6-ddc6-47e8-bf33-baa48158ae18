{"ast": null, "code": "import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js'; // **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nlet _nodeId;\nlet _clockseq; // Previous uuid creation time\n\nlet _lastMSecs = 0;\nlet _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details\n\nfunction v1(options, buf, offset) {\n  let i = buf && offset || 0;\n  const b = buf || new Array(16);\n  options = options || {};\n  let node = options.node || _nodeId;\n  let clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n\n  if (node == null || clockseq == null) {\n    const seedBytes = options.random || (options.rng || rng)();\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n\n  let msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n\n  let nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)\n\n  const dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression\n\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  } // Per 4.2.1.2 Throw error if too many uuids are requested\n\n  if (nsecs >= 10000) {\n    throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n  }\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n\n  msecs += 12219292800000; // `time_low`\n\n  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff; // `time_mid`\n\n  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff; // `time_high_and_version`\n\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n\n  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n\n  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`\n\n  b[i++] = clockseq & 0xff; // `node`\n\n  for (let n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n  return buf || unsafeStringify(b);\n}\nexport default v1;", "map": {"version": 3, "names": ["rng", "unsafeStringify", "_nodeId", "_clockseq", "_lastMSecs", "_lastNSecs", "v1", "options", "buf", "offset", "i", "b", "Array", "node", "clockseq", "undefined", "seedBytes", "random", "msecs", "Date", "now", "nsecs", "dt", "Error", "tl", "tmh", "n"], "sources": ["C:/laragon/www/frontend/node_modules/uuid/dist/esm-browser/v1.js"], "sourcesContent": ["import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js'; // **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nlet _nodeId;\n\nlet _clockseq; // Previous uuid creation time\n\n\nlet _lastMSecs = 0;\nlet _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details\n\nfunction v1(options, buf, offset) {\n  let i = buf && offset || 0;\n  const b = buf || new Array(16);\n  options = options || {};\n  let node = options.node || _nodeId;\n  let clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n\n  if (node == null || clockseq == null) {\n    const seedBytes = options.random || (options.rng || rng)();\n\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n\n\n  let msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n\n  let nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)\n\n  const dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression\n\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n\n\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  } // Per 4.2.1.2 Throw error if too many uuids are requested\n\n\n  if (nsecs >= 10000) {\n    throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n\n  msecs += 12219292800000; // `time_low`\n\n  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff; // `time_mid`\n\n  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff; // `time_high_and_version`\n\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n\n  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n\n  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`\n\n  b[i++] = clockseq & 0xff; // `node`\n\n  for (let n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf || unsafeStringify(b);\n}\n\nexport default v1;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,SAASC,eAAe,QAAQ,gBAAgB,CAAC,CAAC;AAClD;AACA;AACA;;AAEA,IAAIC,OAAO;AAEX,IAAIC,SAAS,CAAC,CAAC;;AAGf,IAAIC,UAAU,GAAG,CAAC;AAClB,IAAIC,UAAU,GAAG,CAAC,CAAC,CAAC;;AAEpB,SAASC,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAChC,IAAIC,CAAC,GAAGF,GAAG,IAAIC,MAAM,IAAI,CAAC;EAC1B,MAAME,CAAC,GAAGH,GAAG,IAAI,IAAII,KAAK,CAAC,EAAE,CAAC;EAC9BL,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIM,IAAI,GAAGN,OAAO,CAACM,IAAI,IAAIX,OAAO;EAClC,IAAIY,QAAQ,GAAGP,OAAO,CAACO,QAAQ,KAAKC,SAAS,GAAGR,OAAO,CAACO,QAAQ,GAAGX,SAAS,CAAC,CAAC;EAC9E;EACA;;EAEA,IAAIU,IAAI,IAAI,IAAI,IAAIC,QAAQ,IAAI,IAAI,EAAE;IACpC,MAAME,SAAS,GAAGT,OAAO,CAACU,MAAM,IAAI,CAACV,OAAO,CAACP,GAAG,IAAIA,GAAG,EAAE,CAAC;IAE1D,IAAIa,IAAI,IAAI,IAAI,EAAE;MAChB;MACAA,IAAI,GAAGX,OAAO,GAAG,CAACc,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9G;IAEA,IAAIF,QAAQ,IAAI,IAAI,EAAE;MACpB;MACAA,QAAQ,GAAGX,SAAS,GAAG,CAACa,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,IAAI,MAAM;IACpE;EACF,CAAC,CAAC;EACF;EACA;EACA;;EAGA,IAAIE,KAAK,GAAGX,OAAO,CAACW,KAAK,KAAKH,SAAS,GAAGR,OAAO,CAACW,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;EACtE;;EAEA,IAAIC,KAAK,GAAGd,OAAO,CAACc,KAAK,KAAKN,SAAS,GAAGR,OAAO,CAACc,KAAK,GAAGhB,UAAU,GAAG,CAAC,CAAC,CAAC;;EAE1E,MAAMiB,EAAE,GAAGJ,KAAK,GAAGd,UAAU,GAAG,CAACiB,KAAK,GAAGhB,UAAU,IAAI,KAAK,CAAC,CAAC;;EAE9D,IAAIiB,EAAE,GAAG,CAAC,IAAIf,OAAO,CAACO,QAAQ,KAAKC,SAAS,EAAE;IAC5CD,QAAQ,GAAGA,QAAQ,GAAG,CAAC,GAAG,MAAM;EAClC,CAAC,CAAC;EACF;;EAGA,IAAI,CAACQ,EAAE,GAAG,CAAC,IAAIJ,KAAK,GAAGd,UAAU,KAAKG,OAAO,CAACc,KAAK,KAAKN,SAAS,EAAE;IACjEM,KAAK,GAAG,CAAC;EACX,CAAC,CAAC;;EAGF,IAAIA,KAAK,IAAI,KAAK,EAAE;IAClB,MAAM,IAAIE,KAAK,CAAC,iDAAiD,CAAC;EACpE;EAEAnB,UAAU,GAAGc,KAAK;EAClBb,UAAU,GAAGgB,KAAK;EAClBlB,SAAS,GAAGW,QAAQ,CAAC,CAAC;;EAEtBI,KAAK,IAAI,cAAc,CAAC,CAAC;;EAEzB,MAAMM,EAAE,GAAG,CAAC,CAACN,KAAK,GAAG,SAAS,IAAI,KAAK,GAAGG,KAAK,IAAI,WAAW;EAC9DV,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGc,EAAE,KAAK,EAAE,GAAG,IAAI;EACzBb,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGc,EAAE,KAAK,EAAE,GAAG,IAAI;EACzBb,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGc,EAAE,KAAK,CAAC,GAAG,IAAI;EACxBb,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGc,EAAE,GAAG,IAAI,CAAC,CAAC;;EAEpB,MAAMC,GAAG,GAAGP,KAAK,GAAG,WAAW,GAAG,KAAK,GAAG,SAAS;EACnDP,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGe,GAAG,KAAK,CAAC,GAAG,IAAI;EACzBd,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGe,GAAG,GAAG,IAAI,CAAC,CAAC;;EAErBd,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGe,GAAG,KAAK,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;;EAElCd,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGe,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;;EAE5Bd,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGI,QAAQ,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;;EAEhCH,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGI,QAAQ,GAAG,IAAI,CAAC,CAAC;;EAE1B,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC1Bf,CAAC,CAACD,CAAC,GAAGgB,CAAC,CAAC,GAAGb,IAAI,CAACa,CAAC,CAAC;EACpB;EAEA,OAAOlB,GAAG,IAAIP,eAAe,CAACU,CAAC,CAAC;AAClC;AAEA,eAAeL,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}