import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Col,
  <PERSON>,
  <PERSON>ton,
  Alert,
  Spinner,
} from 'react-bootstrap';
import creditService, { CreditStatistics } from '../../services/creditService';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

interface WalletBalanceProps {
  refreshTrigger: number;
  onTopUpClick: () => void;
  onHistoryClick: () => void;
}

const WalletBalance: React.FC<WalletBalanceProps> = ({
  refreshTrigger,
  onTopUpClick,
  onHistoryClick,
}) => {
  const [statistics, setStatistics] = useState<CreditStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchStatistics = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      const data = await creditService.getStatistics();
      setStatistics(data);
    } catch (error) {
      console.error('Failed to fetch wallet statistics:', error);
    } finally {
      setLoading(false);
      if (showRefreshing) setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, [refreshTrigger]);

  const handleRefresh = () => {
    fetchStatistics(true);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
        <Spinner animation="border" variant="primary" />
      </div>
    );
  }

  if (!statistics) {
    return (
      <Alert variant="danger" className="mb-3">
        Failed to load wallet information. Please try refreshing the page.
      </Alert>
    );
  }

  const currentBalance = statistics.current_balance || 0;
  const totalSpent = statistics.total_spent || 0;
  const totalPurchased = statistics.total_purchased || 0;

  // Determine wallet status
  const getWalletStatus = () => {
    if (currentBalance <= 0) return { label: 'Empty', color: 'warning', icon: 'fas fa-exclamation-triangle' };
    if (currentBalance < 10) return { label: 'Low Balance', color: 'warning', icon: 'fas fa-exclamation-triangle' };
    return { label: 'Active', color: 'success', icon: 'fas fa-check-circle' };
  };

  const walletStatus = getWalletStatus();

  return (
    <div className="mb-4">
      {/* Inline styles to force lime green background */}
      <style>{`
        .wallet-balance-card {
          background: linear-gradient(135deg, #32CD32 0%, #228B22 100%) !important;
          background-color: #32CD32 !important;
          background-image: linear-gradient(135deg, #32CD32 0%, #228B22 100%) !important;
        }
      `}</style>
      {/* Main Balance Card */}
      <Card
        className="wallet-balance-card text-white mb-4 position-relative overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, #32CD32 0%, #228B22 100%)',
          backgroundColor: '#32CD32',
          backgroundImage: 'linear-gradient(135deg, #32CD32 0%, #228B22 100%)',
          borderRadius: dattaAbleTheme.borderRadius['2xl'],
          border: '3px solid #32CD32', // Debug: lime green border to see if styles apply
          boxShadow: dattaAbleTheme.shadows.lg,
        }}
      >
        <Card.Body className="p-4 position-relative" style={{ zIndex: 1 }}>
          {/* Header */}
          <div className="d-flex justify-content-between align-items-start mb-4">
            <div className="d-flex align-items-center gap-3">
              <div 
                className="d-flex align-items-center justify-content-center"
                style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: dattaAbleTheme.borderRadius.lg,
                  backgroundColor: 'rgba(255, 255, 255, 0.15)',
                  backdropFilter: 'blur(10px)',
                }}
              >
                <i className="fas fa-wallet" style={{ fontSize: '2rem' }}></i>
              </div>
              <div>
                <h6 className="mb-1 fw-semibold">My Wallet</h6>
                <div className="d-flex align-items-center gap-2">
                  <i className={walletStatus.icon} style={{ fontSize: '1rem' }}></i>
                  <small style={{ opacity: 0.9 }}>{walletStatus.label}</small>
                </div>
              </div>
            </div>
            <Button
              variant="outline-light"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="d-flex align-items-center gap-2"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderColor: 'rgba(255, 255, 255, 0.3)',
              }}
            >
              {refreshing ? (
                <Spinner animation="border" size="sm" />
              ) : (
                <i className="fas fa-sync-alt"></i>
              )}
              Refresh
            </Button>
          </div>

          {/* Main Balance Display */}
          <div className="text-center mb-4">
            <small className="d-block mb-2" style={{ opacity: 0.8 }}>
              Available Balance
            </small>
            <h1 
              className="display-3 fw-bold mb-3"
              style={{
                textShadow: '0 2px 4px rgba(0,0,0,0.1)',
                fontSize: 'clamp(2.5rem, 5vw, 4rem)',
              }}
            >
              {creditService.formatWalletBalance(currentBalance)}
            </h1>

            {/* Quick Action Buttons */}
            <div className="d-flex gap-3 justify-content-center flex-column flex-sm-row">
              <Button
                variant="light"
                size="lg"
                onClick={onTopUpClick}
                className="d-flex align-items-center justify-content-center gap-2 fw-semibold"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.15)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: dattaAbleTheme.borderRadius.lg,
                  padding: `${dattaAbleTheme.spacing[3]} ${dattaAbleTheme.spacing[5]}`,
                }}
              >
                <i className="fas fa-plus"></i>
                Top Up Wallet
              </Button>
              <Button
                variant="outline-light"
                size="lg"
                onClick={onHistoryClick}
                className="d-flex align-items-center justify-content-center gap-2 fw-semibold"
                style={{
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                  borderRadius: dattaAbleTheme.borderRadius.lg,
                  padding: `${dattaAbleTheme.spacing[3]} ${dattaAbleTheme.spacing[5]}`,
                }}
              >
                <i className="fas fa-history"></i>
                View History
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Statistics Grid */}
      <Row className="g-3 mb-3">
        <Col xs={12} sm={6} md={4}>
          <Card 
            className="h-100 border-0 shadow-sm"
            style={{ 
              borderRadius: dattaAbleTheme.borderRadius.lg,
              transition: 'all 0.3s ease',
            }}
          >
            <Card.Body className="p-3">
              <div className="d-flex align-items-center gap-3 mb-2">
                <div 
                  className="d-flex align-items-center justify-content-center"
                  style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: dattaAbleTheme.borderRadius.lg,
                    backgroundColor: `${dattaAbleTheme.colors.success.main}20`,
                    color: dattaAbleTheme.colors.success.main,
                  }}
                >
                  <i className="fas fa-arrow-up"></i>
                </div>
                <h6 className="mb-0 fw-semibold">Total Purchased</h6>
              </div>
              <h4 className="fw-bold mb-1" style={{ color: dattaAbleTheme.colors.success.main }}>
                {creditService.formatWalletBalance(totalPurchased)}
              </h4>
              <small className="text-muted">Lifetime wallet top-ups</small>
            </Card.Body>
          </Card>
        </Col>

        <Col xs={12} sm={6} md={4}>
          <Card 
            className="h-100 border-0 shadow-sm"
            style={{ 
              borderRadius: dattaAbleTheme.borderRadius.lg,
              transition: 'all 0.3s ease',
            }}
          >
            <Card.Body className="p-3">
              <div className="d-flex align-items-center gap-3 mb-2">
                <div 
                  className="d-flex align-items-center justify-content-center"
                  style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: dattaAbleTheme.borderRadius.lg,
                    backgroundColor: `${dattaAbleTheme.colors.warning.main}20`,
                    color: dattaAbleTheme.colors.warning.main,
                  }}
                >
                  <i className="fas fa-arrow-down"></i>
                </div>
                <h6 className="mb-0 fw-semibold">Total Spent</h6>
              </div>
              <h4 className="fw-bold mb-1" style={{ color: dattaAbleTheme.colors.warning.main }}>
                {creditService.formatWalletBalance(totalSpent)}
              </h4>
              <small className="text-muted">Used for services</small>
            </Card.Body>
          </Card>
        </Col>

        <Col xs={12} sm={12} md={4}>
          <Card 
            className="h-100 border-0 shadow-sm"
            style={{ 
              borderRadius: dattaAbleTheme.borderRadius.lg,
              transition: 'all 0.3s ease',
            }}
          >
            <Card.Body className="p-3">
              <div className="d-flex align-items-center gap-3 mb-2">
                <div 
                  className="d-flex align-items-center justify-content-center"
                  style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: dattaAbleTheme.borderRadius.lg,
                    backgroundColor: `${(dattaAbleTheme.colors as any)[walletStatus.color].main}20`,
                    color: (dattaAbleTheme.colors as any)[walletStatus.color].main,
                  }}
                >
                  <i className={walletStatus.icon}></i>
                </div>
                <h6 className="mb-0 fw-semibold">Wallet Status</h6>
              </div>
              <h4 className="fw-bold mb-1" style={{ color: (dattaAbleTheme.colors as any)[walletStatus.color].main }}>
                {walletStatus.label}
              </h4>
              <small className="text-muted">Current account status</small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Low Balance Warning */}
      {currentBalance > 0 && currentBalance < 10 && (
        <Alert variant="warning" className="d-flex align-items-center justify-content-between">
          <div>
            <h6 className="mb-1 fw-semibold">Low Balance Warning</h6>
            <small>Your wallet balance is running low. Consider topping up to avoid service interruptions.</small>
          </div>
          <Button variant="warning" size="sm" onClick={onTopUpClick} className="ms-3 flex-shrink-0">
            Top Up Now
          </Button>
        </Alert>
      )}

      {/* Empty Balance Message */}
      {currentBalance <= 0 && (
        <Alert variant="info" className="d-flex align-items-center justify-content-between">
          <div>
            <h6 className="mb-1 fw-semibold">Get Started with Your Wallet</h6>
            <small>Add money to your wallet to start using our services. All transactions are secure and processed through Billplz.</small>
          </div>
          <Button variant="info" size="sm" onClick={onTopUpClick} className="ms-3 flex-shrink-0">
            Add Money
          </Button>
        </Alert>
      )}
    </div>
  );
};

export default WalletBalance;
