[{"C:\\laragon\\www\\frontend\\src\\index.tsx": "1", "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts": "2", "C:\\laragon\\www\\frontend\\src\\App.tsx": "3", "C:\\laragon\\www\\frontend\\src\\utils\\errorHandlers.ts": "4", "C:\\laragon\\www\\frontend\\src\\contexts\\SettingsContext.tsx": "5", "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx": "6", "C:\\laragon\\www\\frontend\\src\\components\\common\\DynamicHead.tsx": "7", "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "8", "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx": "9", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx": "10", "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx": "11", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx": "12", "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx": "13", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx": "14", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx": "15", "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx": "16", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx": "17", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx": "18", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx": "19", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx": "20", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx": "21", "C:\\laragon\\www\\frontend\\src\\services\\settingsService.ts": "22", "C:\\laragon\\www\\frontend\\src\\services\\authService.ts": "23", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleLayout.tsx": "24", "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckRenderer.tsx": "25", "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts": "26", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx": "27", "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts": "28", "C:\\laragon\\www\\frontend\\src\\components\\common\\Toast.tsx": "29", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileHistory.tsx": "30", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx": "31", "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx": "32", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileReUpload.tsx": "33", "C:\\laragon\\www\\frontend\\src\\theme\\dattaAbleTheme.js": "34", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx": "35", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx": "36", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx": "37", "C:\\laragon\\www\\frontend\\src\\services\\api.ts": "38", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleFooter.tsx": "39", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleBreadcrumbs.tsx": "40", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleSidebar.tsx": "41", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleHeader.tsx": "42", "C:\\laragon\\www\\frontend\\src\\utils\\navigationCleanup.ts": "43", "C:\\laragon\\www\\frontend\\src\\utils\\navigationDiagnostics.ts": "44", "C:\\laragon\\www\\frontend\\src\\hooks\\useCleanNavigation.ts": "45", "C:\\laragon\\www\\frontend\\src\\utils\\strictModeWorkaround.ts": "46", "C:\\laragon\\www\\frontend\\src\\pages\\NotFound.tsx": "47", "C:\\laragon\\www\\frontend\\src\\components\\seo\\SEOHead.tsx": "48", "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx": "49", "C:\\laragon\\www\\frontend\\src\\pages\\Contact.tsx": "50", "C:\\laragon\\www\\frontend\\src\\pages\\FAQ.tsx": "51", "C:\\laragon\\www\\frontend\\src\\pages\\About.tsx": "52", "C:\\laragon\\www\\frontend\\src\\components\\pages\\StaticPage.tsx": "53", "C:\\laragon\\www\\frontend\\src\\components\\puck\\puckConfig.ts": "54", "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckEditor.tsx": "55"}, {"size": 554, "mtime": 1752503120783, "results": "56", "hashOfConfig": "57"}, {"size": 425, "mtime": 1752503120799, "results": "58", "hashOfConfig": "57"}, {"size": 5517, "mtime": 1753520189956, "results": "59", "hashOfConfig": "57"}, {"size": 5218, "mtime": 1753280345117, "results": "60", "hashOfConfig": "57"}, {"size": 4306, "mtime": 1753137950997, "results": "61", "hashOfConfig": "57"}, {"size": 4264, "mtime": 1752969422664, "results": "62", "hashOfConfig": "57"}, {"size": 2270, "mtime": 1753105704198, "results": "63", "hashOfConfig": "57"}, {"size": 1355, "mtime": 1752503120861, "results": "64", "hashOfConfig": "57"}, {"size": 4602, "mtime": 1753520233338, "results": "65", "hashOfConfig": "57"}, {"size": 430, "mtime": 1752856061163, "results": "66", "hashOfConfig": "57"}, {"size": 3494, "mtime": 1752503120861, "results": "67", "hashOfConfig": "57"}, {"size": 5805, "mtime": 1752503120814, "results": "68", "hashOfConfig": "57"}, {"size": 4077, "mtime": 1752504117348, "results": "69", "hashOfConfig": "57"}, {"size": 3447, "mtime": 1752503120814, "results": "70", "hashOfConfig": "57"}, {"size": 4586, "mtime": 1752969325386, "results": "71", "hashOfConfig": "57"}, {"size": 11533, "mtime": 1752504965088, "results": "72", "hashOfConfig": "57"}, {"size": 7895, "mtime": 1752969474297, "results": "73", "hashOfConfig": "57"}, {"size": 10483, "mtime": 1753517350528, "results": "74", "hashOfConfig": "57"}, {"size": 50676, "mtime": 1753057376603, "results": "75", "hashOfConfig": "57"}, {"size": 41159, "mtime": 1753055376399, "results": "76", "hashOfConfig": "57"}, {"size": 7813, "mtime": 1753312885093, "results": "77", "hashOfConfig": "57"}, {"size": 5490, "mtime": 1753137950932, "results": "78", "hashOfConfig": "57"}, {"size": 6813, "mtime": 1753518905730, "results": "79", "hashOfConfig": "57"}, {"size": 21069, "mtime": 1753544355741, "results": "80", "hashOfConfig": "57"}, {"size": 3884, "mtime": 1753199110606, "results": "81", "hashOfConfig": "57"}, {"size": 12488, "mtime": 1753024608606, "results": "82", "hashOfConfig": "57"}, {"size": 4729, "mtime": 1752536669944, "results": "83", "hashOfConfig": "57"}, {"size": 5582, "mtime": 1752847027187, "results": "84", "hashOfConfig": "57"}, {"size": 675, "mtime": 1753025888572, "results": "85", "hashOfConfig": "57"}, {"size": 8243, "mtime": 1753051474206, "results": "86", "hashOfConfig": "57"}, {"size": 29255, "mtime": 1753018297878, "results": "87", "hashOfConfig": "57"}, {"size": 1430, "mtime": 1752673572781, "results": "88", "hashOfConfig": "57"}, {"size": 10466, "mtime": 1753053921568, "results": "89", "hashOfConfig": "57"}, {"size": 5454, "mtime": 1753314619164, "results": "90", "hashOfConfig": "57"}, {"size": 11687, "mtime": 1752988950816, "results": "91", "hashOfConfig": "57"}, {"size": 12140, "mtime": 1753544667890, "results": "92", "hashOfConfig": "57"}, {"size": 10996, "mtime": 1752856638130, "results": "93", "hashOfConfig": "57"}, {"size": 2021, "mtime": 1753516973461, "results": "94", "hashOfConfig": "57"}, {"size": 2939, "mtime": 1752883952010, "results": "95", "hashOfConfig": "57"}, {"size": 2706, "mtime": 1752922816616, "results": "96", "hashOfConfig": "57"}, {"size": 7938, "mtime": 1753105982975, "results": "97", "hashOfConfig": "57"}, {"size": 10352, "mtime": 1752970126996, "results": "98", "hashOfConfig": "57"}, {"size": 5193, "mtime": 1753017078083, "results": "99", "hashOfConfig": "57"}, {"size": 7310, "mtime": 1753017533626, "results": "100", "hashOfConfig": "57"}, {"size": 3019, "mtime": 1753017058753, "results": "101", "hashOfConfig": "57"}, {"size": 2543, "mtime": 1753003973465, "results": "102", "hashOfConfig": "57"}, {"size": 4226, "mtime": 1753517742127, "results": "103", "hashOfConfig": "57"}, {"size": 3386, "mtime": 1753487070396, "results": "104", "hashOfConfig": "57"}, {"size": 3696, "mtime": 1753517695731, "results": "105", "hashOfConfig": "57"}, {"size": 187, "mtime": 1753519760527, "results": "106", "hashOfConfig": "57"}, {"size": 175, "mtime": 1753519921271, "results": "107", "hashOfConfig": "57"}, {"size": 181, "mtime": 1753519739656, "results": "108", "hashOfConfig": "57"}, {"size": 5042, "mtime": 1753524244901, "results": "109", "hashOfConfig": "57"}, {"size": 7680, "mtime": 1753523416379, "results": "110", "hashOfConfig": "57"}, {"size": 6934, "mtime": 1753521942761, "results": "111", "hashOfConfig": "57"}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tace3p", {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\frontend\\src\\index.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\laragon\\www\\frontend\\src\\App.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\errorHandlers.ts", [], [], "C:\\laragon\\www\\frontend\\src\\contexts\\SettingsContext.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\DynamicHead.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx", ["277"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx", ["278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx", ["290", "291", "292", "293", "294", "295"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\settingsService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\authService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleLayout.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckRenderer.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts", ["296"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts", ["297", "298"], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\Toast.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileHistory.tsx", ["299"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx", ["300", "301"], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileReUpload.tsx", ["302"], [], "C:\\laragon\\www\\frontend\\src\\theme\\dattaAbleTheme.js", [], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx", ["303", "304", "305"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx", ["306"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\api.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleFooter.tsx", ["307", "308", "309", "310"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleBreadcrumbs.tsx", ["311", "312"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleSidebar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleHeader.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\navigationCleanup.ts", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\navigationDiagnostics.ts", [], [], "C:\\laragon\\www\\frontend\\src\\hooks\\useCleanNavigation.ts", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\strictModeWorkaround.ts", ["313"], [], "C:\\laragon\\www\\frontend\\src\\pages\\NotFound.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\seo\\SEOHead.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx", ["314"], [], "C:\\laragon\\www\\frontend\\src\\pages\\Contact.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\FAQ.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\About.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\pages\\StaticPage.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\puck\\puckConfig.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckEditor.tsx", ["315", "316"], [], {"ruleId": "317", "severity": 1, "message": "318", "line": 8, "column": 10, "nodeType": "319", "messageId": "320", "endLine": 8, "endColumn": 14}, {"ruleId": "317", "severity": 1, "message": "321", "line": 27, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 27, "endColumn": 10}, {"ruleId": "317", "severity": 1, "message": "322", "line": 32, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 32, "endColumn": 19}, {"ruleId": "317", "severity": 1, "message": "323", "line": 40, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 40, "endColumn": 9}, {"ruleId": "317", "severity": 1, "message": "324", "line": 41, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 41, "endColumn": 10}, {"ruleId": "317", "severity": 1, "message": "325", "line": 42, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 42, "endColumn": 7}, {"ruleId": "317", "severity": 1, "message": "326", "line": 43, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 43, "endColumn": 6}, {"ruleId": "317", "severity": 1, "message": "327", "line": 44, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 44, "endColumn": 8}, {"ruleId": "317", "severity": 1, "message": "328", "line": 53, "column": 17, "nodeType": "319", "messageId": "320", "endLine": 53, "endColumn": 27}, {"ruleId": "317", "severity": 1, "message": "329", "line": 79, "column": 9, "nodeType": "319", "messageId": "320", "endLine": 79, "endColumn": 17}, {"ruleId": "317", "severity": 1, "message": "330", "line": 104, "column": 10, "nodeType": "319", "messageId": "320", "endLine": 104, "endColumn": 19}, {"ruleId": "317", "severity": 1, "message": "331", "line": 104, "column": 21, "nodeType": "319", "messageId": "320", "endLine": 104, "endColumn": 33}, {"ruleId": "332", "severity": 1, "message": "333", "line": 114, "column": 6, "nodeType": "334", "endLine": 114, "endColumn": 12, "suggestions": "335"}, {"ruleId": "317", "severity": 1, "message": "336", "line": 18, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 18, "endColumn": 14}, {"ruleId": "317", "severity": 1, "message": "337", "line": 19, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 19, "endColumn": 13}, {"ruleId": "317", "severity": 1, "message": "338", "line": 20, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 20, "endColumn": 9}, {"ruleId": "317", "severity": 1, "message": "339", "line": 21, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 21, "endColumn": 11}, {"ruleId": "317", "severity": 1, "message": "340", "line": 48, "column": 25, "nodeType": "319", "messageId": "320", "endLine": 48, "endColumn": 41}, {"ruleId": "317", "severity": 1, "message": "341", "line": 58, "column": 10, "nodeType": "319", "messageId": "320", "endLine": 58, "endColumn": 22}, {"ruleId": "342", "severity": 1, "message": "343", "line": 413, "column": 1, "nodeType": "344", "endLine": 413, "endColumn": 38}, {"ruleId": "317", "severity": 1, "message": "345", "line": 1, "column": 15, "nodeType": "319", "messageId": "320", "endLine": 1, "endColumn": 24}, {"ruleId": "342", "severity": 1, "message": "343", "line": 223, "column": 1, "nodeType": "344", "endLine": 223, "endColumn": 36}, {"ruleId": "332", "severity": 1, "message": "346", "line": 58, "column": 6, "nodeType": "334", "endLine": 58, "endColumn": 30, "suggestions": "347"}, {"ruleId": "332", "severity": 1, "message": "348", "line": 91, "column": 6, "nodeType": "334", "endLine": 91, "endColumn": 15, "suggestions": "349"}, {"ruleId": "332", "severity": 1, "message": "350", "line": 280, "column": 6, "nodeType": "334", "endLine": 280, "endColumn": 49, "suggestions": "351"}, {"ruleId": "317", "severity": 1, "message": "352", "line": 48, "column": 10, "nodeType": "319", "messageId": "320", "endLine": 48, "endColumn": 20}, {"ruleId": "317", "severity": 1, "message": "353", "line": 3, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 3, "endColumn": 12}, {"ruleId": "317", "severity": 1, "message": "327", "line": 11, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 11, "endColumn": 8}, {"ruleId": "317", "severity": 1, "message": "354", "line": 111, "column": 9, "nodeType": "319", "messageId": "320", "endLine": 111, "endColumn": 19}, {"ruleId": "317", "severity": 1, "message": "353", "line": 3, "column": 3, "nodeType": "319", "messageId": "320", "endLine": 3, "endColumn": 12}, {"ruleId": "355", "severity": 1, "message": "356", "line": 33, "column": 15, "nodeType": "357", "endLine": 42, "endColumn": 16}, {"ruleId": "355", "severity": 1, "message": "356", "line": 49, "column": 15, "nodeType": "357", "endLine": 59, "endColumn": 16}, {"ruleId": "355", "severity": 1, "message": "356", "line": 62, "column": 15, "nodeType": "357", "endLine": 72, "endColumn": 16}, {"ruleId": "355", "severity": 1, "message": "356", "line": 75, "column": 15, "nodeType": "357", "endLine": 84, "endColumn": 16}, {"ruleId": "317", "severity": 1, "message": "358", "line": 19, "column": 11, "nodeType": "319", "messageId": "320", "endLine": 19, "endColumn": 23}, {"ruleId": "317", "severity": 1, "message": "359", "line": 51, "column": 9, "nodeType": "319", "messageId": "320", "endLine": 51, "endColumn": 29}, {"ruleId": "342", "severity": 1, "message": "360", "line": 91, "column": 1, "nodeType": "344", "endLine": 97, "endColumn": 3}, {"ruleId": "317", "severity": 1, "message": "361", "line": 2, "column": 37, "nodeType": "319", "messageId": "320", "endLine": 2, "endColumn": 43}, {"ruleId": "332", "severity": 1, "message": "362", "line": 35, "column": 6, "nodeType": "334", "endLine": 35, "endColumn": 8, "suggestions": "363"}, {"ruleId": "332", "severity": 1, "message": "364", "line": 41, "column": 6, "nodeType": "334", "endLine": 41, "endColumn": 25, "suggestions": "365"}, "@typescript-eslint/no-unused-vars", "'Link' is defined but never used.", "Identifier", "unusedVar", "'Divider' is defined but never used.", "'CircularProgress' is defined but never used.", "'AppBar' is defined but never used.", "'Toolbar' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Badge' is defined but never used.", "'FilterIcon' is defined but never used.", "'isTablet' is assigned a value but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadOrders'. Either include it or remove the dependency array.", "ArrayExpression", ["366"], "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'setCreatingOrder' is assigned a value but never used.", "'createdOrder' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'endpoints' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadHistory'. Either include it or remove the dependency array.", ["367"], "React Hook useEffect has missing dependencies: 'loadSettings' and 'loadUploadedFiles'. Either include them or remove the dependency array.", ["368"], "React Hook useCallback has missing dependencies: 'uploadFiles' and 'validateFiles'. Either include them or remove the dependency array.", ["369"], "'dragActive' is assigned a value but never used.", "'Container' is defined but never used.", "'newBalance' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'pathSegments' is assigned a value but never used.", "'breadcrumbItemStyles' is assigned a value but never used.", "Assign object to a variable before exporting as module default", "'Button' is defined but never used.", "React Hook useEffect has a missing dependency: 'checkAdminStatus'. Either include it or remove the dependency array.", ["370"], "React Hook useEffect has a missing dependency: 'loadPageData'. Either include it or remove the dependency array.", ["371"], {"desc": "372", "fix": "373"}, {"desc": "374", "fix": "375"}, {"desc": "376", "fix": "377"}, {"desc": "378", "fix": "379"}, {"desc": "380", "fix": "381"}, {"desc": "382", "fix": "383"}, "Update the dependencies array to be: [loadOrders, page]", {"range": "384", "text": "385"}, "Update the dependencies array to be: [open, orderId, file.id, loadHistory]", {"range": "386", "text": "387"}, "Update the dependencies array to be: [loadSettings, loadUploadedFiles, orderId]", {"range": "388", "text": "389"}, "Update the dependencies array to be: [orderId, validateFiles, onError, uploadFiles]", {"range": "390", "text": "391"}, "Update the dependencies array to be: [checkAdminStatus]", {"range": "392", "text": "393"}, "Update the dependencies array to be: [isAdmin, loadPageData, pageSlug]", {"range": "394", "text": "395"}, [3415, 3421], "[loadOrders, page]", [1158, 1182], "[open, orderId, file.id, loadHistory]", [2685, 2694], "[loadSettings, loadUploadedFiles, orderId]", [9503, 9546], "[orderId, validateFiles, onError, uploadFiles]", [1163, 1165], "[checkAdminStatus]", [1253, 1272], "[isAdmin, loadPageData, pageSlug]"]