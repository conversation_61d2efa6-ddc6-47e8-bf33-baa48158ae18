{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The TreeItem component was moved from `@mui/lab` to `@mui/x-tree-view`.', '', \"You should use `import { TreeItem } from '@mui/x-tree-view'`\", \"or `import { TreeItem } from '@mui/x-tree-view/TreeItem'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-tree-view-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The TreeItem component was moved from `@mui/lab` to `@mui/x-tree-view`. More information about this migration on our blog: https://mui.com/blog/lab-tree-view-to-mui-x/.\n * @ignore - do not document.\n */\nconst TreeItem = /*#__PURE__*/React.forwardRef(function DeprecatedTreeItem() {\n  warn();\n  return null;\n});\nexport default TreeItem;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "TreeItem", "forwardRef", "DeprecatedTreeItem"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TreeItem/TreeItem.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The TreeItem component was moved from `@mui/lab` to `@mui/x-tree-view`.', '', \"You should use `import { TreeItem } from '@mui/x-tree-view'`\", \"or `import { TreeItem } from '@mui/x-tree-view/TreeItem'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-tree-view-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The TreeItem component was moved from `@mui/lab` to `@mui/x-tree-view`. More information about this migration on our blog: https://mui.com/blog/lab-tree-view-to-mui-x/.\n * @ignore - do not document.\n */\nconst TreeItem = /*#__PURE__*/React.forwardRef(function DeprecatedTreeItem() {\n  warn();\n  return null;\n});\nexport default TreeItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,8EAA8E,EAAE,EAAE,EAAE,8DAA8D,EAAE,2DAA2D,EAAE,EAAE,EAAE,kGAAkG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAClVH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,QAAQ,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,kBAAkBA,CAAA,EAAG;EAC3EL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}