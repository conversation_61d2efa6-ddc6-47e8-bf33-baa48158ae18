{"ast": null, "code": "export { default as common } from \"./common.js\";\nexport { default as red } from \"./red.js\";\nexport { default as pink } from \"./pink.js\";\nexport { default as purple } from \"./purple.js\";\nexport { default as deepPurple } from \"./deepPurple.js\";\nexport { default as indigo } from \"./indigo.js\";\nexport { default as blue } from \"./blue.js\";\nexport { default as lightBlue } from \"./lightBlue.js\";\nexport { default as cyan } from \"./cyan.js\";\nexport { default as teal } from \"./teal.js\";\nexport { default as green } from \"./green.js\";\nexport { default as lightGreen } from \"./lightGreen.js\";\nexport { default as lime } from \"./lime.js\";\nexport { default as yellow } from \"./yellow.js\";\nexport { default as amber } from \"./amber.js\";\nexport { default as orange } from \"./orange.js\";\nexport { default as deepOrange } from \"./deepOrange.js\";\nexport { default as brown } from \"./brown.js\";\nexport { default as grey } from \"./grey.js\";\nexport { default as blueGrey } from \"./blueGrey.js\";", "map": {"version": 3, "names": ["default", "common", "red", "pink", "purple", "deepPurple", "indigo", "blue", "lightBlue", "cyan", "teal", "green", "lightGreen", "lime", "yellow", "amber", "orange", "deepOrange", "brown", "grey", "blue<PERSON>rey"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/colors/index.js"], "sourcesContent": ["export { default as common } from \"./common.js\";\nexport { default as red } from \"./red.js\";\nexport { default as pink } from \"./pink.js\";\nexport { default as purple } from \"./purple.js\";\nexport { default as deepPurple } from \"./deepPurple.js\";\nexport { default as indigo } from \"./indigo.js\";\nexport { default as blue } from \"./blue.js\";\nexport { default as lightBlue } from \"./lightBlue.js\";\nexport { default as cyan } from \"./cyan.js\";\nexport { default as teal } from \"./teal.js\";\nexport { default as green } from \"./green.js\";\nexport { default as lightGreen } from \"./lightGreen.js\";\nexport { default as lime } from \"./lime.js\";\nexport { default as yellow } from \"./yellow.js\";\nexport { default as amber } from \"./amber.js\";\nexport { default as orange } from \"./orange.js\";\nexport { default as deepOrange } from \"./deepOrange.js\";\nexport { default as brown } from \"./brown.js\";\nexport { default as grey } from \"./grey.js\";\nexport { default as blueGrey } from \"./blueGrey.js\";"], "mappings": "AAAA,SAASA,OAAO,IAAIC,MAAM,QAAQ,aAAa;AAC/C,SAASD,OAAO,IAAIE,GAAG,QAAQ,UAAU;AACzC,SAASF,OAAO,IAAIG,IAAI,QAAQ,WAAW;AAC3C,SAASH,OAAO,IAAII,MAAM,QAAQ,aAAa;AAC/C,SAASJ,OAAO,IAAIK,UAAU,QAAQ,iBAAiB;AACvD,SAASL,OAAO,IAAIM,MAAM,QAAQ,aAAa;AAC/C,SAASN,OAAO,IAAIO,IAAI,QAAQ,WAAW;AAC3C,SAASP,OAAO,IAAIQ,SAAS,QAAQ,gBAAgB;AACrD,SAASR,OAAO,IAAIS,IAAI,QAAQ,WAAW;AAC3C,SAAST,OAAO,IAAIU,IAAI,QAAQ,WAAW;AAC3C,SAASV,OAAO,IAAIW,KAAK,QAAQ,YAAY;AAC7C,SAASX,OAAO,IAAIY,UAAU,QAAQ,iBAAiB;AACvD,SAASZ,OAAO,IAAIa,IAAI,QAAQ,WAAW;AAC3C,SAASb,OAAO,IAAIc,MAAM,QAAQ,aAAa;AAC/C,SAASd,OAAO,IAAIe,KAAK,QAAQ,YAAY;AAC7C,SAASf,OAAO,IAAIgB,MAAM,QAAQ,aAAa;AAC/C,SAAShB,OAAO,IAAIiB,UAAU,QAAQ,iBAAiB;AACvD,SAASjB,OAAO,IAAIkB,KAAK,QAAQ,YAAY;AAC7C,SAASlB,OAAO,IAAImB,IAAI,QAAQ,WAAW;AAC3C,SAASnB,OAAO,IAAIoB,QAAQ,QAAQ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}