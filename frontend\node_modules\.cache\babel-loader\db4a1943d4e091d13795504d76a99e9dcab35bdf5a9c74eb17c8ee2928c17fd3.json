{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableUtilityClass(slot) {\n  return generateUtilityClass('MuiTable', slot);\n}\nconst tableClasses = generateUtilityClasses('MuiTable', ['root', 'stickyHeader']);\nexport default tableClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTableUtilityClass", "slot", "tableClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Table/tableClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableUtilityClass(slot) {\n  return generateUtilityClass('MuiTable', slot);\n}\nconst tableClasses = generateUtilityClasses('MuiTable', ['root', 'stickyHeader']);\nexport default tableClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOF,oBAAoB,CAAC,UAAU,EAAEE,IAAI,CAAC;AAC/C;AACA,MAAMC,YAAY,GAAGJ,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AACjF,eAAeI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}