{"ast": null, "code": "import setRef from '@mui/utils/setRef';\nexport default setRef;", "map": {"version": 3, "names": ["setRef"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/utils/setRef.js"], "sourcesContent": ["import setRef from '@mui/utils/setRef';\nexport default setRef;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AACtC,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}