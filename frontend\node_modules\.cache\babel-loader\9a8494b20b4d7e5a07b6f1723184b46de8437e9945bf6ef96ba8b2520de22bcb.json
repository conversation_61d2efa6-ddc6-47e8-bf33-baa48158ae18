{"ast": null, "code": "import { getPath } from '@mui/system/style';\nimport { alpha } from '@mui/system/colorManipulator';\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = ownerState.color;\n  // check the `main` color first for a custom palette, then fallback to the color itself\n  const color = getPath(theme, `palette.${transformedColor}.main`, false) || getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}.mainChannel`) || getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "alpha", "getTextDecoration", "theme", "ownerState", "transformedColor", "color", "channelColor"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Link/getTextDecoration.js"], "sourcesContent": ["import { getPath } from '@mui/system/style';\nimport { alpha } from '@mui/system/colorManipulator';\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = ownerState.color;\n  // check the `main` color first for a custom palette, then fallback to the color itself\n  const color = getPath(theme, `palette.${transformedColor}.main`, false) || getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}.mainChannel`) || getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,KAAK,QAAQ,8BAA8B;AACpD,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAGD,UAAU,CAACE,KAAK;EACzC;EACA,MAAMA,KAAK,GAAGN,OAAO,CAACG,KAAK,EAAE,WAAWE,gBAAgB,OAAO,EAAE,KAAK,CAAC,IAAIL,OAAO,CAACG,KAAK,EAAE,WAAWE,gBAAgB,EAAE,EAAE,KAAK,CAAC,IAAID,UAAU,CAACE,KAAK;EACnJ,MAAMC,YAAY,GAAGP,OAAO,CAACG,KAAK,EAAE,WAAWE,gBAAgB,cAAc,CAAC,IAAIL,OAAO,CAACG,KAAK,EAAE,WAAWE,gBAAgB,SAAS,CAAC;EACtI,IAAI,MAAM,IAAIF,KAAK,IAAII,YAAY,EAAE;IACnC,OAAO,QAAQA,YAAY,SAAS;EACtC;EACA,OAAON,KAAK,CAACK,KAAK,EAAE,GAAG,CAAC;AAC1B,CAAC;AACD,eAAeJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}