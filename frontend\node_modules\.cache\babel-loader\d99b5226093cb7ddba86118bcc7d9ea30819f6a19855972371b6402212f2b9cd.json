{"ast": null, "code": "export { default } from \"./LocalizationProvider.js\";\nexport * from \"./LocalizationProvider.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/LocalizationProvider/index.js"], "sourcesContent": ["export { default } from \"./LocalizationProvider.js\";\nexport * from \"./LocalizationProvider.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,cAAc,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}