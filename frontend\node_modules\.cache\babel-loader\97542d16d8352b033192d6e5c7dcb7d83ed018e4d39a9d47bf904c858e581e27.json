{"ast": null, "code": "import { Rectangle } from '@dnd-kit/geometry';\nvar __typeError = msg => {\n  throw TypeError(msg);\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\n\n// src/utilities/type-guards/isKeyframeEffect.ts\nfunction isKeyframeEffect(effect) {\n  if (!effect) return false;\n  if (effect instanceof KeyframeEffect) return true;\n  return \"getKeyframes\" in effect && typeof effect.getKeyframes === \"function\";\n}\n\n// src/utilities/animations/getFinalKeyframe.ts\nfunction getFinalKeyframe(element, match) {\n  const animations2 = element.getAnimations();\n  if (animations2.length > 0) {\n    for (const animation of animations2) {\n      if (animation.playState !== \"running\") continue;\n      const {\n        effect\n      } = animation;\n      const keyframes = isKeyframeEffect(effect) ? effect.getKeyframes() : [];\n      const matchedKeyframes = keyframes.filter(match);\n      if (matchedKeyframes.length > 0) {\n        return [matchedKeyframes[matchedKeyframes.length - 1], animation];\n      }\n    }\n  }\n  return null;\n}\n\n// src/utilities/bounding-rectangle/getBoundingRectangle.ts\nfunction getBoundingRectangle(element) {\n  const {\n    width,\n    height,\n    top,\n    left,\n    bottom,\n    right\n  } = element.getBoundingClientRect();\n  return {\n    width,\n    height,\n    top,\n    left,\n    bottom,\n    right\n  };\n}\n\n// src/utilities/execution-context/canUseDOM.ts\nvar canUseDOM = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\n\n// src/utilities/type-guards/isWindow.ts\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === \"[object Window]\" ||\n  // In Electron context the Window object serializes to [object global]\n  elementString === \"[object global]\";\n}\n\n// src/utilities/type-guards/isNode.ts\nfunction isNode(node) {\n  return \"nodeType\" in node;\n}\n\n// src/utilities/execution-context/getWindow.ts\nfunction getWindow(target) {\n  var _a, _b, _c;\n  if (!target) {\n    return window;\n  }\n  if (isWindow(target)) {\n    return target;\n  }\n  if (!isNode(target)) {\n    return window;\n  }\n  if (\"defaultView\" in target) {\n    return (_a = target.defaultView) != null ? _a : window;\n  }\n  return (_c = (_b = target.ownerDocument) == null ? void 0 : _b.defaultView) != null ? _c : window;\n}\n\n// src/utilities/type-guards/isDocument.ts\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document || \"nodeType\" in node && node.nodeType === Node.DOCUMENT_NODE;\n}\n\n// src/utilities/type-guards/isHTMLElement.ts\nfunction isHTMLElement(node) {\n  if (!node || isWindow(node)) return false;\n  return node instanceof getWindow(node).HTMLElement || \"namespaceURI\" in node && typeof node.namespaceURI === \"string\" && node.namespaceURI.endsWith(\"html\");\n}\n\n// src/utilities/type-guards/isSVGElement.ts\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement || \"namespaceURI\" in node && typeof node.namespaceURI === \"string\" && node.namespaceURI.endsWith(\"svg\");\n}\n\n// src/utilities/execution-context/getDocument.ts\nfunction getDocument(target) {\n  if (!target) {\n    return document;\n  }\n  if (isWindow(target)) {\n    return target.document;\n  }\n  if (!isNode(target)) {\n    return document;\n  }\n  if (isDocument(target)) {\n    return target;\n  }\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n  return document;\n}\n\n// src/utilities/bounding-rectangle/getViewportBoundingRectangle.ts\nfunction getViewportBoundingRectangle(element) {\n  const {\n    documentElement\n  } = getDocument(element);\n  const width = documentElement.clientWidth;\n  const height = documentElement.clientHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\n\n// src/utilities/bounding-rectangle/isOverflowVisible.ts\nfunction isOverflowVisible(element, style) {\n  if (isDetailsElement(element) && element.open === false) {\n    return false;\n  }\n  const {\n    overflow,\n    overflowX,\n    overflowY\n  } = getComputedStyle(element);\n  return overflow === \"visible\" && overflowX === \"visible\" && overflowY === \"visible\";\n}\nfunction isDetailsElement(element) {\n  return element.tagName === \"DETAILS\";\n}\n\n// src/utilities/bounding-rectangle/getVisibleBoundingRectangle.ts\nfunction getVisibleBoundingRectangle(element) {\n  let boundingClientRect = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : element.getBoundingClientRect();\n  let margin = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  var _a;\n  let rect = boundingClientRect;\n  const {\n    ownerDocument\n  } = element;\n  const ownerWindow = (_a = ownerDocument.defaultView) != null ? _a : window;\n  let ancestor = element.parentElement;\n  while (ancestor && ancestor !== ownerDocument.documentElement) {\n    if (!isOverflowVisible(ancestor)) {\n      const ancestorRect = ancestor.getBoundingClientRect();\n      const marginTop = margin * (ancestorRect.bottom - ancestorRect.top);\n      const marginRight = margin * (ancestorRect.right - ancestorRect.left);\n      const marginBottom = margin * (ancestorRect.bottom - ancestorRect.top);\n      const marginLeft = margin * (ancestorRect.right - ancestorRect.left);\n      rect = {\n        top: Math.max(rect.top, ancestorRect.top - marginTop),\n        right: Math.min(rect.right, ancestorRect.right + marginRight),\n        bottom: Math.min(rect.bottom, ancestorRect.bottom + marginBottom),\n        left: Math.max(rect.left, ancestorRect.left - marginLeft),\n        width: 0,\n        // Will be calculated next\n        height: 0\n        // Will be calculated next\n      };\n      rect.width = rect.right - rect.left;\n      rect.height = rect.bottom - rect.top;\n    }\n    ancestor = ancestor.parentElement;\n  }\n  const viewportWidth = ownerWindow.innerWidth;\n  const viewportHeight = ownerWindow.innerHeight;\n  const viewportMarginY = margin * viewportHeight;\n  const viewportMarginX = margin * viewportWidth;\n  rect = {\n    top: Math.max(rect.top, 0 - viewportMarginY),\n    right: Math.min(rect.right, viewportWidth + viewportMarginX),\n    bottom: Math.min(rect.bottom, viewportHeight + viewportMarginY),\n    left: Math.max(rect.left, 0 - viewportMarginX),\n    width: 0,\n    // Will be calculated next\n    height: 0\n    // Will be calculated next\n  };\n  rect.width = rect.right - rect.left;\n  rect.height = rect.bottom - rect.top;\n  if (rect.width < 0) {\n    rect.width = 0;\n  }\n  if (rect.height < 0) {\n    rect.height = 0;\n  }\n  return rect;\n}\n\n// src/utilities/execution-context/isSafari.ts\nfunction isSafari() {\n  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n}\n\n// src/utilities/element/cloneElement.ts\nfunction cloneElement(element) {\n  const selector = \"input, textarea, select, canvas, [contenteditable]\";\n  const clonedElement = element.cloneNode(true);\n  const fields = Array.from(element.querySelectorAll(selector));\n  const clonedFields = Array.from(clonedElement.querySelectorAll(selector));\n  clonedFields.forEach((field, index) => {\n    const originalField = fields[index];\n    if (isField(field) && isField(originalField)) {\n      if (field.type !== \"file\") {\n        field.value = originalField.value;\n      }\n      if (field.type === \"radio\" && field.name) {\n        field.name = `Cloned__${field.name}`;\n      }\n    }\n    if (isCanvasElement(field) && isCanvasElement(originalField) && originalField.width > 0 && originalField.height > 0) {\n      const destCtx = field.getContext(\"2d\");\n      destCtx == null ? void 0 : destCtx.drawImage(originalField, 0, 0);\n    }\n  });\n  return clonedElement;\n}\nfunction isField(element) {\n  return \"value\" in element;\n}\nfunction isCanvasElement(element) {\n  return element.tagName === \"CANVAS\";\n}\n\n// src/utilities/element/getElementFromPoint.ts\nfunction getElementFromPoint(document2, _ref) {\n  let {\n    x,\n    y\n  } = _ref;\n  const element = document2.elementFromPoint(x, y);\n  if (isIFrameElement(element)) {\n    const {\n      contentDocument\n    } = element;\n    if (contentDocument) {\n      const {\n        left,\n        top\n      } = element.getBoundingClientRect();\n      return getElementFromPoint(contentDocument, {\n        x: x - left,\n        y: y - top\n      });\n    }\n  }\n  return element;\n}\nfunction isIFrameElement(element) {\n  return (element == null ? void 0 : element.tagName) === \"IFRAME\";\n}\n\n// src/utilities/element/proxiedElements.ts\nvar ProxiedElements = /* @__PURE__ */new WeakMap();\n\n// src/utilities/event-listeners/Listeners.ts\nvar Listeners = class {\n  constructor() {\n    this.entries = /* @__PURE__ */new Set();\n    this.clear = () => {\n      for (const entry of this.entries) {\n        const [target, {\n          type,\n          listener,\n          options\n        }] = entry;\n        target.removeEventListener(type, listener, options);\n      }\n      this.entries.clear();\n    };\n  }\n  bind(target, input) {\n    const listeners = Array.isArray(input) ? input : [input];\n    const entries = [];\n    for (const descriptor of listeners) {\n      const {\n        type,\n        listener,\n        options\n      } = descriptor;\n      const entry = [target, descriptor];\n      target.addEventListener(type, listener, options);\n      this.entries.add(entry);\n      entries.push(entry);\n    }\n    return function cleanup() {\n      for (const [target2, {\n        type,\n        listener,\n        options\n      }] of entries) {\n        target2.removeEventListener(type, listener, options);\n      }\n    };\n  }\n};\n\n// src/utilities/frame/getFrameElement.ts\nfunction getFrameElement(el) {\n  const refWindow = el == null ? void 0 : el.ownerDocument.defaultView;\n  if (refWindow && refWindow.self !== refWindow.parent) {\n    return refWindow.frameElement;\n  }\n}\n\n// src/utilities/frame/getFrameElements.ts\nfunction getFrameElements(el) {\n  const frames = /* @__PURE__ */new Set();\n  let frame = getFrameElement(el);\n  while (frame) {\n    frames.add(frame);\n    frame = getFrameElement(frame);\n  }\n  return frames;\n}\n\n// src/utilities/scheduling/timeout.ts\nfunction timeout(callback, duration) {\n  const id = setTimeout(callback, duration);\n  return () => clearTimeout(id);\n}\n\n// src/utilities/scheduling/throttle.ts\nfunction throttle(func, limit) {\n  const time = () => performance.now();\n  let cancel;\n  let lastRan;\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    const context = this;\n    if (!lastRan) {\n      func.apply(context, args);\n      lastRan = time();\n    } else {\n      cancel == null ? void 0 : cancel();\n      cancel = timeout(() => {\n        func.apply(context, args);\n        lastRan = time();\n      }, limit - (time() - lastRan));\n    }\n  };\n}\n\n// src/utilities/bounding-rectangle/isRectEqual.ts\nfunction isRectEqual(a, b) {\n  if (a === b) return true;\n  if (!a || !b) return false;\n  return a.top == b.top && a.left == b.left && a.right == b.right && a.bottom == b.bottom;\n}\n\n// src/utilities/bounding-rectangle/isVisible.ts\nfunction isVisible(element) {\n  let boundingClientRect = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : element.getBoundingClientRect();\n  const {\n    width,\n    height\n  } = getVisibleBoundingRectangle(element, boundingClientRect);\n  return width > 0 && height > 0;\n}\n\n// src/utilities/observers/ResizeNotifier.ts\nvar Observer = canUseDOM ? ResizeObserver : class MockResizeObserver {\n  observe() {}\n  unobserve() {}\n  disconnect() {}\n};\nvar _initialized;\nvar ResizeNotifier = class extends Observer {\n  constructor(callback) {\n    super(entries => {\n      if (!__privateGet(this, _initialized)) {\n        __privateSet(this, _initialized, true);\n        return;\n      }\n      callback(entries, this);\n    });\n    __privateAdd(this, _initialized, false);\n  }\n};\n_initialized = new WeakMap();\n\n// src/utilities/observers/PositionObserver.ts\nvar threshold = Array.from({\n  length: 100\n}, (_, index) => index / 100);\nvar THROTTLE_INTERVAL = 75;\nvar _visible, _previousBoundingClientRect, _resizeObserver, _positionObserver, _visibilityObserver, _debug, _disconnected, _observePosition, _PositionObserver_instances, notify_fn, updateDebug_fn;\nvar PositionObserver = class {\n  constructor(element, callback) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n      debug: false,\n      skipInitial: false\n    };\n    this.element = element;\n    this.callback = callback;\n    __privateAdd(this, _PositionObserver_instances);\n    this.disconnect = () => {\n      var _a, _b, _c;\n      __privateSet(this, _disconnected, true);\n      (_a = __privateGet(this, _resizeObserver)) == null ? void 0 : _a.disconnect();\n      (_b = __privateGet(this, _positionObserver)) == null ? void 0 : _b.disconnect();\n      __privateGet(this, _visibilityObserver).disconnect();\n      (_c = __privateGet(this, _debug)) == null ? void 0 : _c.remove();\n    };\n    __privateAdd(this, _visible, true);\n    __privateAdd(this, _previousBoundingClientRect);\n    __privateAdd(this, _resizeObserver);\n    __privateAdd(this, _positionObserver);\n    __privateAdd(this, _visibilityObserver);\n    __privateAdd(this, _debug);\n    __privateAdd(this, _disconnected, false);\n    __privateAdd(this, _observePosition, throttle(() => {\n      var _a, _b, _c;\n      const {\n        element\n      } = this;\n      (_a = __privateGet(this, _positionObserver)) == null ? void 0 : _a.disconnect();\n      if (__privateGet(this, _disconnected) || !__privateGet(this, _visible) || !element.isConnected) {\n        return;\n      }\n      const root = (_b = element.ownerDocument) != null ? _b : document;\n      const {\n        innerHeight,\n        innerWidth\n      } = (_c = root.defaultView) != null ? _c : window;\n      const clientRect = element.getBoundingClientRect();\n      const visibleRect = getVisibleBoundingRectangle(element, clientRect);\n      const {\n        top,\n        left,\n        bottom,\n        right\n      } = visibleRect;\n      const insetTop = -Math.floor(top);\n      const insetLeft = -Math.floor(left);\n      const insetRight = -Math.floor(innerWidth - right);\n      const insetBottom = -Math.floor(innerHeight - bottom);\n      const rootMargin = `${insetTop}px ${insetRight}px ${insetBottom}px ${insetLeft}px`;\n      this.boundingClientRect = clientRect;\n      __privateSet(this, _positionObserver, new IntersectionObserver(entries => {\n        const [entry] = entries;\n        const {\n          intersectionRect\n        } = entry;\n        const intersectionRatio = entry.intersectionRatio !== 1 ? entry.intersectionRatio : Rectangle.intersectionRatio(intersectionRect, getVisibleBoundingRectangle(element));\n        if (intersectionRatio !== 1) {\n          __privateGet(this, _observePosition).call(this);\n        }\n      }, {\n        threshold,\n        rootMargin,\n        root\n      }));\n      __privateGet(this, _positionObserver).observe(element);\n      __privateMethod(this, _PositionObserver_instances, notify_fn).call(this);\n    }, THROTTLE_INTERVAL));\n    this.boundingClientRect = element.getBoundingClientRect();\n    __privateSet(this, _visible, isVisible(element, this.boundingClientRect));\n    let initial = true;\n    this.callback = boundingClientRect => {\n      if (initial) {\n        initial = false;\n        if (options.skipInitial) return;\n      }\n      callback(boundingClientRect);\n    };\n    const root = element.ownerDocument;\n    if (options == null ? void 0 : options.debug) {\n      __privateSet(this, _debug, document.createElement(\"div\"));\n      __privateGet(this, _debug).style.background = \"rgba(0,0,0,0.15)\";\n      __privateGet(this, _debug).style.position = \"fixed\";\n      __privateGet(this, _debug).style.pointerEvents = \"none\";\n      root.body.appendChild(__privateGet(this, _debug));\n    }\n    __privateSet(this, _visibilityObserver, new IntersectionObserver(entries => {\n      var _a, _b;\n      const entry = entries[entries.length - 1];\n      const {\n        boundingClientRect,\n        isIntersecting: visible\n      } = entry;\n      const {\n        width,\n        height\n      } = boundingClientRect;\n      const previousVisible = __privateGet(this, _visible);\n      __privateSet(this, _visible, visible);\n      if (!width && !height) return;\n      if (previousVisible && !visible) {\n        (_a = __privateGet(this, _positionObserver)) == null ? void 0 : _a.disconnect();\n        this.callback(null);\n        (_b = __privateGet(this, _resizeObserver)) == null ? void 0 : _b.disconnect();\n        __privateSet(this, _resizeObserver, void 0);\n        if (__privateGet(this, _debug)) __privateGet(this, _debug).style.visibility = \"hidden\";\n      } else {\n        __privateGet(this, _observePosition).call(this);\n      }\n      if (visible && !__privateGet(this, _resizeObserver)) {\n        __privateSet(this, _resizeObserver, new ResizeNotifier(__privateGet(this, _observePosition)));\n        __privateGet(this, _resizeObserver).observe(element);\n      }\n    }, {\n      threshold,\n      root\n    }));\n    if (__privateGet(this, _visible) && !options.skipInitial) {\n      this.callback(this.boundingClientRect);\n    }\n    __privateGet(this, _visibilityObserver).observe(element);\n  }\n};\n_visible = new WeakMap();\n_previousBoundingClientRect = new WeakMap();\n_resizeObserver = new WeakMap();\n_positionObserver = new WeakMap();\n_visibilityObserver = new WeakMap();\n_debug = new WeakMap();\n_disconnected = new WeakMap();\n_observePosition = new WeakMap();\n_PositionObserver_instances = new WeakSet();\nnotify_fn = function () {\n  if (__privateGet(this, _disconnected)) return;\n  __privateMethod(this, _PositionObserver_instances, updateDebug_fn).call(this);\n  if (isRectEqual(this.boundingClientRect, __privateGet(this, _previousBoundingClientRect))) return;\n  this.callback(this.boundingClientRect);\n  __privateSet(this, _previousBoundingClientRect, this.boundingClientRect);\n};\nupdateDebug_fn = function () {\n  if (__privateGet(this, _debug)) {\n    const {\n      top,\n      left,\n      width,\n      height\n    } = getVisibleBoundingRectangle(this.element);\n    __privateGet(this, _debug).style.overflow = \"hidden\";\n    __privateGet(this, _debug).style.visibility = \"visible\";\n    __privateGet(this, _debug).style.top = `${Math.floor(top)}px`;\n    __privateGet(this, _debug).style.left = `${Math.floor(left)}px`;\n    __privateGet(this, _debug).style.width = `${Math.floor(width)}px`;\n    __privateGet(this, _debug).style.height = `${Math.floor(height)}px`;\n  }\n};\n\n// src/utilities/observers/FrameObserver.ts\nvar framePositionObservers = /* @__PURE__ */new WeakMap();\nvar scrollListeners = /* @__PURE__ */new WeakMap();\nfunction addFrameListener(frame, callback) {\n  let cached = framePositionObservers.get(frame);\n  if (!cached) {\n    const observer = new PositionObserver(frame, boundingClientRect => {\n      const cached2 = framePositionObservers.get(frame);\n      if (!cached2) return;\n      cached2.callbacks.forEach(callback2 => callback2(boundingClientRect));\n    }, {\n      skipInitial: true\n    });\n    cached = {\n      disconnect: observer.disconnect,\n      callbacks: /* @__PURE__ */new Set()\n    };\n  }\n  cached.callbacks.add(callback);\n  framePositionObservers.set(frame, cached);\n  return () => {\n    cached.callbacks.delete(callback);\n    if (cached.callbacks.size === 0) {\n      framePositionObservers.delete(frame);\n      cached.disconnect();\n    }\n  };\n}\nfunction observeParentFrames(frames, callback) {\n  const cleanup = /* @__PURE__ */new Set();\n  for (const frame of frames) {\n    const remove = addFrameListener(frame, callback);\n    cleanup.add(remove);\n  }\n  return () => cleanup.forEach(remove => remove());\n}\nfunction addScrollListener(element, callback) {\n  var _a;\n  const doc = element.ownerDocument;\n  if (!scrollListeners.has(doc)) {\n    const controller = new AbortController();\n    const listeners2 = /* @__PURE__ */new Set();\n    document.addEventListener(\"scroll\", event => listeners2.forEach(listener => listener(event)), {\n      capture: true,\n      passive: true,\n      signal: controller.signal\n    });\n    scrollListeners.set(doc, {\n      disconnect: () => controller.abort(),\n      listeners: listeners2\n    });\n  }\n  const {\n    listeners,\n    disconnect\n  } = (_a = scrollListeners.get(doc)) != null ? _a : {};\n  if (!listeners || !disconnect) return () => {};\n  listeners.add(callback);\n  return () => {\n    listeners.delete(callback);\n    if (listeners.size === 0) {\n      disconnect();\n      scrollListeners.delete(doc);\n    }\n  };\n}\nvar _elementObserver, _disconnected2, _frames, _handleScroll;\nvar FrameObserver = class {\n  constructor(element, callback, options) {\n    this.callback = callback;\n    __privateAdd(this, _elementObserver);\n    __privateAdd(this, _disconnected2, false);\n    __privateAdd(this, _frames);\n    __privateAdd(this, _handleScroll, throttle(event => {\n      if (__privateGet(this, _disconnected2)) return;\n      if (!event.target) return;\n      if (\"contains\" in event.target && typeof event.target.contains === \"function\") {\n        for (const frame of __privateGet(this, _frames)) {\n          if (event.target.contains(frame)) {\n            this.callback(__privateGet(this, _elementObserver).boundingClientRect);\n            break;\n          }\n        }\n      }\n    }, THROTTLE_INTERVAL));\n    const frames = getFrameElements(element);\n    const unobserveParentFrames = observeParentFrames(frames, callback);\n    const removeScrollListener = addScrollListener(element, __privateGet(this, _handleScroll));\n    __privateSet(this, _frames, frames);\n    __privateSet(this, _elementObserver, new PositionObserver(element, callback, options));\n    this.disconnect = () => {\n      if (__privateGet(this, _disconnected2)) return;\n      __privateSet(this, _disconnected2, true);\n      unobserveParentFrames();\n      removeScrollListener();\n      __privateGet(this, _elementObserver).disconnect();\n    };\n  }\n};\n_elementObserver = new WeakMap();\n_disconnected2 = new WeakMap();\n_frames = new WeakMap();\n_handleScroll = new WeakMap();\n\n// src/utilities/popover/supportsPopover.ts\nfunction supportsPopover(element) {\n  return \"showPopover\" in element && \"hidePopover\" in element && typeof element.showPopover === \"function\" && typeof element.hidePopover === \"function\";\n}\n\n// src/utilities/popover/showPopover.ts\nfunction showPopover(element) {\n  try {\n    if (supportsPopover(element) && element.isConnected && element.hasAttribute(\"popover\") &&\n    // This selector can throw an error in browsers that don't support it\n    !element.matches(\":popover-open\")) {\n      element.showPopover();\n    }\n  } catch (error) {}\n}\n\n// src/utilities/popover/hidePopover.ts\nfunction hidePopover(element) {\n  try {\n    if (supportsPopover(element) && element.isConnected && element.hasAttribute(\"popover\") &&\n    // This selector can throw an error in browsers that don't support it\n    element.matches(\":popover-open\")) {\n      element.hidePopover();\n    }\n  } catch (error) {}\n}\n\n// src/utilities/scroll/documentScrollingElement.ts\nfunction isDocumentScrollingElement(element) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n  return element === getDocument(element).scrollingElement;\n}\n\n// src/utilities/scroll/getScrollPosition.ts\nfunction getScrollPosition(scrollableElement) {\n  const window2 = getWindow(scrollableElement);\n  const rect = isDocumentScrollingElement(scrollableElement) ? getViewportBoundingRectangle(scrollableElement) : getBoundingRectangle(scrollableElement);\n  const dimensions = isDocumentScrollingElement(scrollableElement) ? {\n    height: window2.innerHeight,\n    width: window2.innerWidth\n  } : {\n    height: scrollableElement.clientHeight,\n    width: scrollableElement.clientWidth\n  };\n  const position = {\n    current: {\n      x: scrollableElement.scrollLeft,\n      y: scrollableElement.scrollTop\n    },\n    max: {\n      x: scrollableElement.scrollWidth - dimensions.width,\n      y: scrollableElement.scrollHeight - dimensions.height\n    }\n  };\n  const isTop = position.current.y <= 0;\n  const isLeft = position.current.x <= 0;\n  const isBottom = position.current.y >= position.max.y;\n  const isRight = position.current.x >= position.max.x;\n  return {\n    rect,\n    position,\n    isTop,\n    isLeft,\n    isBottom,\n    isRight\n  };\n}\n\n// src/utilities/scroll/canScroll.ts\nfunction canScroll(scrollableElement, by) {\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight,\n    position\n  } = getScrollPosition(scrollableElement);\n  const {\n    x,\n    y\n  } = by != null ? by : {\n    x: 0,\n    y: 0\n  };\n  const top = !isTop && position.current.y + y > 0;\n  const bottom = !isBottom && position.current.y + y < position.max.y;\n  const left = !isLeft && position.current.x + x > 0;\n  const right = !isRight && position.current.x + x < position.max.x;\n  return {\n    top,\n    bottom,\n    left,\n    right,\n    x: left || right,\n    y: top || bottom\n  };\n}\n\n// src/utilities/scheduling/scheduler.ts\nvar Scheduler = class {\n  constructor(scheduler4) {\n    this.scheduler = scheduler4;\n    this.pending = false;\n    this.tasks = /* @__PURE__ */new Set();\n    this.resolvers = /* @__PURE__ */new Set();\n    this.flush = () => {\n      const {\n        tasks,\n        resolvers\n      } = this;\n      this.pending = false;\n      this.tasks = /* @__PURE__ */new Set();\n      this.resolvers = /* @__PURE__ */new Set();\n      for (const task of tasks) {\n        task();\n      }\n      for (const resolve of resolvers) {\n        resolve();\n      }\n    };\n  }\n  schedule(task) {\n    this.tasks.add(task);\n    if (!this.pending) {\n      this.pending = true;\n      this.scheduler(this.flush);\n    }\n    return new Promise(resolve => this.resolvers.add(resolve));\n  }\n};\nvar scheduler = new Scheduler(callback => {\n  if (typeof requestAnimationFrame === \"function\") {\n    requestAnimationFrame(callback);\n  } else {\n    callback();\n  }\n});\n\n// src/utilities/styles/getComputedStyles.ts\nvar scheduler2 = new Scheduler(callback => setTimeout(callback, 50));\nvar cachedStyles = /* @__PURE__ */new Map();\nvar clear = cachedStyles.clear.bind(cachedStyles);\nfunction getComputedStyles(element) {\n  let cached = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (!cached) return computeStyles(element);\n  let styles = cachedStyles.get(element);\n  if (styles) return styles;\n  styles = computeStyles(element);\n  cachedStyles.set(element, styles);\n  scheduler2.schedule(clear);\n  return styles;\n}\nfunction computeStyles(element) {\n  return getWindow(element).getComputedStyle(element);\n}\n\n// src/utilities/scroll/isFixed.ts\nfunction isFixed(node) {\n  let computedStyle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getComputedStyles(node, true);\n  return computedStyle.position === \"fixed\" || computedStyle.position === \"sticky\";\n}\n\n// src/utilities/scroll/isScrollable.ts\nfunction isScrollable(element) {\n  let computedStyle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getComputedStyles(element, true);\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = [\"overflow\", \"overflowX\", \"overflowY\"];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === \"string\" ? overflowRegex.test(value) : false;\n  });\n}\n\n// src/utilities/scroll/getScrollableAncestors.ts\nvar defaultOptions = {\n  excludeElement: true\n};\nfunction getScrollableAncestors(element) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultOptions;\n  const {\n    limit,\n    excludeElement\n  } = options;\n  const scrollParents = /* @__PURE__ */new Set();\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.size >= limit) {\n      return scrollParents;\n    }\n    if (!node) {\n      return scrollParents;\n    }\n    if (isDocument(node) && node.scrollingElement != null && !scrollParents.has(node.scrollingElement)) {\n      scrollParents.add(node.scrollingElement);\n      return scrollParents;\n    }\n    if (!isHTMLElement(node)) {\n      if (isSVGElement(node)) {\n        return findScrollableAncestors(node.parentElement);\n      }\n      return scrollParents;\n    }\n    if (scrollParents.has(node)) {\n      return scrollParents;\n    }\n    const computedStyle = getComputedStyles(node, true);\n    if (excludeElement && node === element) ;else if (isScrollable(node, computedStyle)) {\n      scrollParents.add(node);\n    }\n    if (isFixed(node, computedStyle)) {\n      const {\n        scrollingElement\n      } = node.ownerDocument;\n      if (scrollingElement) scrollParents.add(scrollingElement);\n      return scrollParents;\n    }\n    return findScrollableAncestors(node.parentNode);\n  }\n  if (!element) {\n    return scrollParents;\n  }\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, {\n    limit: 1\n  });\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\n\n// src/utilities/frame/getFrameTransform.ts\nfunction getFrameTransform(el) {\n  let boundary = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window.frameElement;\n  const transform = {\n    x: 0,\n    y: 0,\n    scaleX: 1,\n    scaleY: 1\n  };\n  if (!el) return transform;\n  let frame = getFrameElement(el);\n  while (frame) {\n    if (frame === boundary) {\n      return transform;\n    }\n    const rect = getBoundingRectangle(frame);\n    const {\n      x: scaleX,\n      y: scaleY\n    } = getScale(frame, rect);\n    transform.x = transform.x + rect.left;\n    transform.y = transform.y + rect.top;\n    transform.scaleX = transform.scaleX * scaleX;\n    transform.scaleY = transform.scaleY * scaleY;\n    frame = getFrameElement(frame);\n  }\n  return transform;\n}\nfunction getScale(element) {\n  let boundingRectangle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getBoundingRectangle(element);\n  const width = Math.round(boundingRectangle.width);\n  const height = Math.round(boundingRectangle.height);\n  if (isHTMLElement(element)) {\n    return {\n      x: width / element.offsetWidth,\n      y: height / element.offsetHeight\n    };\n  }\n  const styles = getComputedStyles(element, true);\n  return {\n    x: (parseFloat(styles.width) || width) / width,\n    y: (parseFloat(styles.height) || height) / height\n  };\n}\n\n// src/utilities/transform/parseScale.ts\nfunction parseScale(scale) {\n  if (scale === \"none\") {\n    return null;\n  }\n  const values = scale.split(\" \");\n  const x = parseFloat(values[0]);\n  const y = parseFloat(values[1]);\n  if (isNaN(x) && isNaN(y)) {\n    return null;\n  }\n  return {\n    x: isNaN(x) ? y : x,\n    y: isNaN(y) ? x : y\n  };\n}\n\n// src/utilities/transform/parseTranslate.ts\nfunction parseTranslate(translate) {\n  if (translate === \"none\") {\n    return null;\n  }\n  const [x, y, z = \"0\"] = translate.split(\" \");\n  const output = {\n    x: parseFloat(x),\n    y: parseFloat(y),\n    z: parseInt(z, 10)\n  };\n  if (isNaN(output.x) && isNaN(output.y)) {\n    return null;\n  }\n  return {\n    x: isNaN(output.x) ? 0 : output.x,\n    y: isNaN(output.y) ? 0 : output.y,\n    z: isNaN(output.z) ? 0 : output.z\n  };\n}\n\n// src/utilities/transform/parseTransform.ts\nfunction parseTransform(computedStyles) {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _i;\n  const {\n    scale,\n    transform,\n    translate\n  } = computedStyles;\n  const parsedScale = parseScale(scale);\n  const parsedTranslate = parseTranslate(translate);\n  const parsedMatrix = parseTransformMatrix(transform);\n  if (!parsedMatrix && !parsedScale && !parsedTranslate) {\n    return null;\n  }\n  const normalizedScale = {\n    x: (_a = parsedScale == null ? void 0 : parsedScale.x) != null ? _a : 1,\n    y: (_b = parsedScale == null ? void 0 : parsedScale.y) != null ? _b : 1\n  };\n  const normalizedTranslate = {\n    x: (_c = parsedTranslate == null ? void 0 : parsedTranslate.x) != null ? _c : 0,\n    y: (_d = parsedTranslate == null ? void 0 : parsedTranslate.y) != null ? _d : 0\n  };\n  const normalizedMatrix = {\n    x: (_e = parsedMatrix == null ? void 0 : parsedMatrix.x) != null ? _e : 0,\n    y: (_f = parsedMatrix == null ? void 0 : parsedMatrix.y) != null ? _f : 0,\n    scaleX: (_g = parsedMatrix == null ? void 0 : parsedMatrix.scaleX) != null ? _g : 1,\n    scaleY: (_h = parsedMatrix == null ? void 0 : parsedMatrix.scaleY) != null ? _h : 1\n  };\n  return {\n    x: normalizedTranslate.x + normalizedMatrix.x,\n    y: normalizedTranslate.y + normalizedMatrix.y,\n    z: (_i = parsedTranslate == null ? void 0 : parsedTranslate.z) != null ? _i : 0,\n    scaleX: normalizedScale.x * normalizedMatrix.scaleX,\n    scaleY: normalizedScale.y * normalizedMatrix.scaleY\n  };\n}\nfunction parseTransformMatrix(transform) {\n  if (transform.startsWith(\"matrix3d(\")) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith(\"matrix(\")) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n  return null;\n}\n\n// src/utilities/scroll/detectScrollIntent.ts\nvar ScrollDirection = /* @__PURE__ */(ScrollDirection2 => {\n  ScrollDirection2[ScrollDirection2[\"Idle\"] = 0] = \"Idle\";\n  ScrollDirection2[ScrollDirection2[\"Forward\"] = 1] = \"Forward\";\n  ScrollDirection2[ScrollDirection2[\"Reverse\"] = -1] = \"Reverse\";\n  return ScrollDirection2;\n})(ScrollDirection || {});\nvar defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nvar defaultTolerance = {\n  x: 10,\n  y: 10\n};\nfunction detectScrollIntent(scrollableElement, coordinates, intent) {\n  let acceleration = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 25;\n  let thresholdPercentage = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : defaultThreshold;\n  let tolerance = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : defaultTolerance;\n  const {\n    x,\n    y\n  } = coordinates;\n  const {\n    rect,\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollableElement);\n  const frameTransform = getFrameTransform(scrollableElement);\n  const computedStyles = getComputedStyles(scrollableElement, true);\n  const parsedTransform = parseTransform(computedStyles);\n  const isXAxisInverted = parsedTransform !== null ? (parsedTransform == null ? void 0 : parsedTransform.scaleX) < 0 : false;\n  const isYAxisInverted = parsedTransform !== null ? (parsedTransform == null ? void 0 : parsedTransform.scaleY) < 0 : false;\n  const scrollContainerRect = new Rectangle(rect.left * frameTransform.scaleX + frameTransform.x, rect.top * frameTransform.scaleY + frameTransform.y, rect.width * frameTransform.scaleX, rect.height * frameTransform.scaleY);\n  const direction = {\n    x: 0 /* Idle */,\n    y: 0 /* Idle */\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold2 = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n  if ((!isTop || isYAxisInverted && !isBottom) && y <= scrollContainerRect.top + threshold2.height && (intent == null ? void 0 : intent.y) !== 1 /* Forward */ && x >= scrollContainerRect.left - tolerance.x && x <= scrollContainerRect.right + tolerance.x) {\n    direction.y = isYAxisInverted ? 1 /* Forward */ : -1 /* Reverse */;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold2.height - y) / threshold2.height);\n  } else if ((!isBottom || isYAxisInverted && !isTop) && y >= scrollContainerRect.bottom - threshold2.height && (intent == null ? void 0 : intent.y) !== -1 /* Reverse */ && x >= scrollContainerRect.left - tolerance.x && x <= scrollContainerRect.right + tolerance.x) {\n    direction.y = isYAxisInverted ? -1 /* Reverse */ : 1 /* Forward */;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold2.height - y) / threshold2.height);\n  }\n  if ((!isRight || isXAxisInverted && !isLeft) && x >= scrollContainerRect.right - threshold2.width && (intent == null ? void 0 : intent.x) !== -1 /* Reverse */ && y >= scrollContainerRect.top - tolerance.y && y <= scrollContainerRect.bottom + tolerance.y) {\n    direction.x = isXAxisInverted ? -1 /* Reverse */ : 1 /* Forward */;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold2.width - x) / threshold2.width);\n  } else if ((!isLeft || isXAxisInverted && !isRight) && x <= scrollContainerRect.left + threshold2.width && (intent == null ? void 0 : intent.x) !== 1 /* Forward */ && y >= scrollContainerRect.top - tolerance.y && y <= scrollContainerRect.bottom + tolerance.y) {\n    direction.x = isXAxisInverted ? 1 /* Forward */ : -1 /* Reverse */;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold2.width - x) / threshold2.width);\n  }\n  return {\n    direction,\n    speed\n  };\n}\n\n// src/utilities/scroll/scrollIntoViewIfNeeded.ts\nfunction supportsScrollIntoViewIfNeeded(element) {\n  return \"scrollIntoViewIfNeeded\" in element && typeof element.scrollIntoViewIfNeeded === \"function\";\n}\nfunction scrollIntoViewIfNeeded(el) {\n  let centerIfNeeded = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (supportsScrollIntoViewIfNeeded(el)) {\n    el.scrollIntoViewIfNeeded(centerIfNeeded);\n    return;\n  }\n  if (!isHTMLElement(el)) {\n    return el.scrollIntoView();\n  }\n  var parent = getFirstScrollableAncestor(el);\n  if (!isHTMLElement(parent)) {\n    return;\n  }\n  const parentComputedStyle = getComputedStyles(parent, true),\n    parentBorderTopWidth = parseInt(parentComputedStyle.getPropertyValue(\"border-top-width\")),\n    parentBorderLeftWidth = parseInt(parentComputedStyle.getPropertyValue(\"border-left-width\")),\n    overTop = el.offsetTop - parent.offsetTop < parent.scrollTop,\n    overBottom = el.offsetTop - parent.offsetTop + el.clientHeight - parentBorderTopWidth > parent.scrollTop + parent.clientHeight,\n    overLeft = el.offsetLeft - parent.offsetLeft < parent.scrollLeft,\n    overRight = el.offsetLeft - parent.offsetLeft + el.clientWidth - parentBorderLeftWidth > parent.scrollLeft + parent.clientWidth,\n    alignWithTop = overTop && !overBottom;\n  if ((overTop || overBottom) && centerIfNeeded) {\n    parent.scrollTop = el.offsetTop - parent.offsetTop - parent.clientHeight / 2 - parentBorderTopWidth + el.clientHeight / 2;\n  }\n  if ((overLeft || overRight) && centerIfNeeded) {\n    parent.scrollLeft = el.offsetLeft - parent.offsetLeft - parent.clientWidth / 2 - parentBorderLeftWidth + el.clientWidth / 2;\n  }\n  if ((overTop || overBottom || overLeft || overRight) && !centerIfNeeded) {\n    el.scrollIntoView(alignWithTop);\n  }\n}\n\n// src/utilities/transform/applyTransform.ts\nfunction applyTransform(rect, parsedTransform, transformOrigin) {\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left + translateX + (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top + translateY + (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(\" \") + 1));\n  const w = scaleX ? rect.width * scaleX : rect.width;\n  const h = scaleY ? rect.height * scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\n// src/utilities/transform/inverseTransform.ts\nfunction inverseTransform(rect, parsedTransform, transformOrigin) {\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(\" \") + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\n// src/utilities/transform/animateTransform.ts\nfunction animateTransform(_ref2) {\n  let {\n    element,\n    keyframes,\n    options\n  } = _ref2;\n  return element.animate(keyframes, options).finished;\n}\n\n// src/utilities/transform/computeTranslate.ts\nfunction computeTranslate(element) {\n  let translate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getComputedStyles(element).translate;\n  let projected = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (projected) {\n    const keyframe = getFinalKeyframe(element, keyframe2 => \"translate\" in keyframe2);\n    if (keyframe) {\n      const {\n        translate: translate2 = \"\"\n      } = keyframe[0];\n      if (typeof translate2 === \"string\") {\n        const finalTranslate = parseTranslate(translate2);\n        if (finalTranslate) {\n          return finalTranslate;\n        }\n      }\n    }\n  }\n  if (translate) {\n    const finalTranslate = parseTranslate(translate);\n    if (finalTranslate) {\n      return finalTranslate;\n    }\n  }\n  return {\n    x: 0,\n    y: 0,\n    z: 0\n  };\n}\n\n// src/utilities/animations/forceFinishAnimations.ts\nvar scheduler3 = new Scheduler(callback => setTimeout(callback, 0));\nvar animations = /* @__PURE__ */new Map();\nvar clear2 = animations.clear.bind(animations);\nfunction getDocumentAnimations(element) {\n  const document2 = element.ownerDocument;\n  let documentAnimations = animations.get(document2);\n  if (documentAnimations) return documentAnimations;\n  documentAnimations = document2.getAnimations();\n  animations.set(document2, documentAnimations);\n  scheduler3.schedule(clear2);\n  const elementAnimations = documentAnimations.filter(animation => isKeyframeEffect(animation.effect) && animation.effect.target === element);\n  animations.set(element, elementAnimations);\n  return documentAnimations;\n}\nfunction forceFinishAnimations(element, options) {\n  const animations2 = getDocumentAnimations(element).filter(animation => {\n    var _a, _b;\n    if (isKeyframeEffect(animation.effect)) {\n      const {\n        target\n      } = animation.effect;\n      const isValidTarget = (_b = target && ((_a = options.isValidTarget) == null ? void 0 : _a.call(options, target))) != null ? _b : true;\n      if (isValidTarget) {\n        return animation.effect.getKeyframes().some(keyframe => {\n          for (const property of options.properties) {\n            if (keyframe[property]) return true;\n          }\n        });\n      }\n    }\n  }).map(animation => {\n    const {\n      effect,\n      currentTime\n    } = animation;\n    const duration = effect == null ? void 0 : effect.getComputedTiming().duration;\n    if (animation.pending || animation.playState === \"finished\") return;\n    if (typeof duration == \"number\" && typeof currentTime == \"number\" && currentTime < duration) {\n      animation.currentTime = duration;\n      return () => {\n        animation.currentTime = currentTime;\n      };\n    }\n  });\n  if (animations2.length > 0) {\n    return () => animations2.forEach(reset => reset == null ? void 0 : reset());\n  }\n}\n\n// src/utilities/shapes/DOMRectangle.ts\nvar DOMRectangle = class extends Rectangle {\n  constructor(element) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var _a, _b, _c, _d;\n    const {\n      frameTransform = getFrameTransform(element),\n      ignoreTransforms,\n      getBoundingClientRect = getBoundingRectangle\n    } = options;\n    const resetAnimations = forceFinishAnimations(element, {\n      properties: [\"transform\", \"translate\", \"scale\", \"width\", \"height\"],\n      isValidTarget: target => (target !== element || isSafari()) && target.contains(element)\n    });\n    const boundingRectangle = getBoundingClientRect(element);\n    let {\n      top,\n      left,\n      width,\n      height\n    } = boundingRectangle;\n    let updated;\n    const computedStyles = getComputedStyles(element);\n    const parsedTransform = parseTransform(computedStyles);\n    const scale = {\n      x: (_a = parsedTransform == null ? void 0 : parsedTransform.scaleX) != null ? _a : 1,\n      y: (_b = parsedTransform == null ? void 0 : parsedTransform.scaleY) != null ? _b : 1\n    };\n    const projectedTransform = getProjectedTransform(element, computedStyles);\n    resetAnimations == null ? void 0 : resetAnimations();\n    if (parsedTransform) {\n      updated = inverseTransform(boundingRectangle, parsedTransform, computedStyles.transformOrigin);\n      if (ignoreTransforms || projectedTransform) {\n        top = updated.top;\n        left = updated.left;\n        width = updated.width;\n        height = updated.height;\n      }\n    }\n    const intrinsic = {\n      width: (_c = updated == null ? void 0 : updated.width) != null ? _c : width,\n      height: (_d = updated == null ? void 0 : updated.height) != null ? _d : height\n    };\n    if (projectedTransform && !ignoreTransforms && updated) {\n      const projected = applyTransform(updated, projectedTransform, computedStyles.transformOrigin);\n      top = projected.top;\n      left = projected.left;\n      width = projected.width;\n      height = projected.height;\n      scale.x = projectedTransform.scaleX;\n      scale.y = projectedTransform.scaleY;\n    }\n    if (frameTransform) {\n      if (!ignoreTransforms) {\n        left *= frameTransform.scaleX;\n        width *= frameTransform.scaleX;\n        top *= frameTransform.scaleY;\n        height *= frameTransform.scaleY;\n      }\n      left += frameTransform.x;\n      top += frameTransform.y;\n    }\n    super(left, top, width, height);\n    this.scale = scale;\n    this.intrinsicWidth = intrinsic.width;\n    this.intrinsicHeight = intrinsic.height;\n  }\n};\nfunction getProjectedTransform(element, computedStyles) {\n  var _a;\n  const animations2 = element.getAnimations();\n  let projectedTransform = null;\n  if (!animations2.length) return null;\n  for (const animation of animations2) {\n    if (animation.playState !== \"running\") continue;\n    const keyframes = isKeyframeEffect(animation.effect) ? animation.effect.getKeyframes() : [];\n    const keyframe = keyframes[keyframes.length - 1];\n    if (!keyframe) continue;\n    const {\n      transform,\n      translate,\n      scale\n    } = keyframe;\n    if (transform || translate || scale) {\n      const parsedTransform = parseTransform({\n        transform: typeof transform === \"string\" && transform ? transform : computedStyles.transform,\n        translate: typeof translate === \"string\" && translate ? translate : computedStyles.translate,\n        scale: typeof scale === \"string\" && scale ? scale : computedStyles.scale\n      });\n      if (parsedTransform) {\n        projectedTransform = projectedTransform ? {\n          x: projectedTransform.x + parsedTransform.x,\n          y: projectedTransform.y + parsedTransform.y,\n          z: (_a = projectedTransform.z) != null ? _a : parsedTransform.z,\n          scaleX: projectedTransform.scaleX * parsedTransform.scaleX,\n          scaleY: projectedTransform.scaleY * parsedTransform.scaleY\n        } : parsedTransform;\n      }\n    }\n  }\n  return projectedTransform;\n}\n\n// src/utilities/type-guards/supportsStyle.ts\nfunction supportsStyle(element) {\n  return \"style\" in element && typeof element.style === \"object\" && element.style !== null && \"setProperty\" in element.style && \"removeProperty\" in element.style && typeof element.style.setProperty === \"function\" && typeof element.style.removeProperty === \"function\";\n}\n\n// src/utilities/styles/Styles.ts\nvar Styles = class {\n  constructor(element) {\n    this.element = element;\n    this.initial = /* @__PURE__ */new Map();\n  }\n  set(properties) {\n    let prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n    const {\n      element\n    } = this;\n    if (!supportsStyle(element)) {\n      return;\n    }\n    for (const [key, value] of Object.entries(properties)) {\n      const property = `${prefix}${key}`;\n      if (!this.initial.has(property)) {\n        this.initial.set(property, element.style.getPropertyValue(property));\n      }\n      element.style.setProperty(property, typeof value === \"string\" ? value : `${value}px`);\n    }\n  }\n  remove(properties) {\n    let prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n    const {\n      element\n    } = this;\n    if (!supportsStyle(element)) {\n      return;\n    }\n    for (const key of properties) {\n      const property = `${prefix}${key}`;\n      element.style.removeProperty(property);\n    }\n  }\n  reset() {\n    const {\n      element\n    } = this;\n    if (!supportsStyle(element)) {\n      return;\n    }\n    for (const [key, value] of this.initial) {\n      element.style.setProperty(key, value);\n    }\n    if (element.getAttribute(\"style\") === \"\") {\n      element.removeAttribute(\"style\");\n    }\n  }\n};\n\n// src/utilities/type-guards/isElement.ts\nfunction isElement(target) {\n  if (!target) return false;\n  return target instanceof getWindow(target).Element || isNode(target) && target.nodeType === Node.ELEMENT_NODE;\n}\n\n// src/utilities/type-guards/isKeyboardEvent.ts\nfunction isKeyboardEvent(event) {\n  if (!event) return false;\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return event instanceof KeyboardEvent;\n}\n\n// src/utilities/type-guards/isPointerEvent.ts\nfunction isPointerEvent(event) {\n  if (!event) return false;\n  const {\n    PointerEvent\n  } = getWindow(event.target);\n  return event instanceof PointerEvent;\n}\n\n// src/utilities/type-guards/supportsViewTransition.ts\nfunction supportsViewTransition(document2) {\n  return \"startViewTransition\" in document2;\n}\n\n// src/utilities/type-guards/isTextInput.ts\nfunction isTextInput(target) {\n  if (!isElement(target)) return false;\n  const {\n    tagName\n  } = target;\n  return tagName === \"INPUT\" || tagName === \"TEXTAREA\" || isContentEditable(target);\n}\nfunction isContentEditable(element) {\n  return element.hasAttribute(\"contenteditable\") && element.getAttribute(\"contenteditable\") !== \"false\";\n}\n\n// src/utilities/misc/generateUniqueId.ts\nvar ids = {};\nfunction generateUniqueId(prefix) {\n  const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n  ids[prefix] = id;\n  return `${prefix}-${id}`;\n}\nexport { DOMRectangle, Listeners, FrameObserver as PositionObserver, ProxiedElements, ResizeNotifier, Scheduler, ScrollDirection, Styles, animateTransform, canScroll, canUseDOM, cloneElement, computeTranslate, detectScrollIntent, generateUniqueId, getBoundingRectangle, getComputedStyles, getDocument, getElementFromPoint, getFinalKeyframe, getFirstScrollableAncestor, getFrameElement, getFrameTransform, getScrollableAncestors, getViewportBoundingRectangle, getVisibleBoundingRectangle, getWindow, hidePopover, inverseTransform, isDocumentScrollingElement, isElement, isHTMLElement, isKeyboardEvent, isKeyframeEffect, isPointerEvent, isSafari, isTextInput, parseTransform, parseTranslate, scheduler, scrollIntoViewIfNeeded, showPopover, supportsPopover, supportsStyle, supportsViewTransition, timeout };\n\n//# sourceMappingURL=utilities.js.map", "map": {"version": 3, "names": ["isKeyframeEffect", "effect", "KeyframeEffect", "getKeyframes", "getFinalKeyframe", "element", "match", "animations2", "getAnimations", "length", "animation", "playState", "keyframes", "matchedKeyframes", "filter", "getBoundingRectangle", "width", "height", "top", "left", "bottom", "right", "getBoundingClientRect", "canUseDOM", "window", "document", "createElement", "isWindow", "elementString", "Object", "prototype", "toString", "call", "isNode", "node", "getWindow", "target", "_a", "_b", "_c", "defaultView", "ownerDocument", "isDocument", "Document", "nodeType", "Node", "DOCUMENT_NODE", "isHTMLElement", "HTMLElement", "namespaceURI", "endsWith", "isSVGElement", "SVGElement", "getDocument", "getViewportBoundingRectangle", "documentElement", "clientWidth", "clientHeight", "isOverflowVisible", "style", "isDetailsElement", "open", "overflow", "overflowX", "overflowY", "getComputedStyle", "tagName", "getVisibleBoundingRectangle", "boundingClientRect", "arguments", "undefined", "margin", "rect", "ownerWindow", "ancestor", "parentElement", "ancestorRect", "marginTop", "marginRight", "marginBottom", "marginLeft", "Math", "max", "min", "viewportWidth", "innerWidth", "viewportHeight", "innerHeight", "viewportMarginY", "viewportMarginX", "<PERSON><PERSON><PERSON><PERSON>", "test", "navigator", "userAgent", "cloneElement", "selector", "clonedElement", "cloneNode", "fields", "Array", "from", "querySelectorAll", "cloned<PERSON><PERSON>s", "for<PERSON>ach", "field", "index", "original<PERSON>ield", "isField", "type", "value", "name", "isCanvasElement", "destCtx", "getContext", "drawImage", "getElementFromPoint", "document2", "_ref", "x", "y", "elementFromPoint", "isIFrameElement", "contentDocument", "ProxiedElements", "WeakMap", "Listeners", "constructor", "entries", "Set", "clear", "entry", "listener", "options", "removeEventListener", "bind", "input", "listeners", "isArray", "descriptor", "addEventListener", "add", "push", "cleanup", "target2", "getFrameElement", "el", "refWindow", "self", "parent", "frameElement", "getFrameElements", "frames", "frame", "timeout", "callback", "duration", "id", "setTimeout", "clearTimeout", "throttle", "func", "limit", "time", "performance", "now", "cancel", "last<PERSON>an", "_len", "args", "_key", "context", "apply", "isRectEqual", "a", "b", "isVisible", "Observer", "ResizeObserver", "MockResizeObserver", "observe", "unobserve", "disconnect", "_initialized", "ResizeNotifier", "__privateGet", "__privateSet", "__privateAdd", "threshold", "_", "THROTTLE_INTERVAL", "_visible", "_previousBoundingClientRect", "_resizeObserver", "_positionObserver", "_visibilityObserver", "_debug", "_disconnected", "_observePosition", "_PositionObserver_instances", "notify_fn", "updateDebug_fn", "PositionObserver", "debug", "skipInitial", "remove", "isConnected", "root", "clientRect", "visibleRect", "insetTop", "floor", "insetLeft", "insetRight", "insetBottom", "rootMargin", "IntersectionObserver", "intersectionRect", "intersectionRatio", "Rectangle", "__privateMethod", "initial", "background", "position", "pointerEvents", "body", "append<PERSON><PERSON><PERSON>", "isIntersecting", "visible", "previousVisible", "visibility", "WeakSet", "framePositionObservers", "scrollListeners", "addFrameListener", "cached", "get", "observer", "cached2", "callbacks", "callback2", "set", "delete", "size", "observe<PERSON><PERSON>nt<PERSON>rames", "addScrollListener", "doc", "has", "controller", "AbortController", "listeners2", "event", "capture", "passive", "signal", "abort", "_elementObserver", "_disconnected2", "_frames", "_handleScroll", "FrameObserver", "contains", "unobserveParentFrames", "removeScrollListener", "supportsPopover", "showPopover", "hidePopover", "hasAttribute", "matches", "error", "isDocumentScrollingElement", "scrollingElement", "getScrollPosition", "scrollableElement", "window2", "dimensions", "current", "scrollLeft", "scrollTop", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "canScroll", "by", "Scheduler", "scheduler4", "scheduler", "pending", "tasks", "resolvers", "flush", "task", "resolve", "schedule", "Promise", "requestAnimationFrame", "scheduler2", "cachedStyles", "Map", "getComputedStyles", "computeStyles", "styles", "isFixed", "computedStyle", "isScrollable", "overflowRegex", "properties", "some", "property", "defaultOptions", "excludeElement", "getScrollableAncestors", "scrollParents", "findScrollableAncestors", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getFrameTransform", "boundary", "transform", "scaleX", "scaleY", "getScale", "boundingRectangle", "round", "offsetWidth", "offsetHeight", "parseFloat", "parseScale", "scale", "values", "split", "isNaN", "parseTranslate", "translate", "z", "output", "parseInt", "parseTransform", "computedStyles", "_d", "_e", "_f", "_g", "_h", "_i", "parsedScale", "parsedTranslate", "parsedMatrix", "parseTransformMatrix", "normalizedScale", "normalizedTranslate", "normalizedMatrix", "startsWith", "transformArray", "slice", "ScrollDirection", "ScrollDirection2", "defaultThreshold", "defaultTolerance", "detectScrollIntent", "coordinates", "intent", "acceleration", "thresholdPercentage", "tolerance", "frameTransform", "parsedTransform", "isXAxisInverted", "isYAxisInverted", "scrollContainerRect", "direction", "speed", "threshold2", "abs", "supportsScrollIntoViewIfNeeded", "scrollIntoViewIfNeeded", "centerIfNeeded", "scrollIntoView", "parentComputedStyle", "parentBorderTopWidth", "getPropertyValue", "parentBorderLeftWidth", "overTop", "offsetTop", "overBottom", "overLeft", "offsetLeft", "overRight", "alignWithTop", "applyTransform", "transform<PERSON><PERSON>in", "translateX", "translateY", "indexOf", "w", "h", "inverseTransform", "animateTransform", "_ref2", "animate", "finished", "computeTranslate", "projected", "keyframe", "keyframe2", "translate2", "finalTranslate", "scheduler3", "animations", "clear2", "getDocumentAnimations", "documentAnimations", "elementAnimations", "forceFinishAnimations", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "currentTime", "getComputedTiming", "reset", "DOMRectangle", "ignoreTransforms", "resetAnimations", "updated", "projectedTransform", "getProjectedTransform", "intrinsic", "intrinsicWidth", "intrinsicHeight", "supportsStyle", "setProperty", "removeProperty", "Styles", "prefix", "key", "getAttribute", "removeAttribute", "isElement", "Element", "ELEMENT_NODE", "isKeyboardEvent", "KeyboardEvent", "isPointerEvent", "PointerEvent", "supportsViewTransition", "isTextInput", "isContentEditable", "ids", "generateUniqueId"], "sources": ["C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isKeyframeEffect.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\animations\\getFinalKeyframe.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\bounding-rectangle\\getBoundingRectangle.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\execution-context\\canUseDOM.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isWindow.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isNode.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\execution-context\\getWindow.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isDocument.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isHTMLElement.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isSVGElement.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\execution-context\\getDocument.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\bounding-rectangle\\getViewportBoundingRectangle.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\bounding-rectangle\\isOverflowVisible.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\bounding-rectangle\\getVisibleBoundingRectangle.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\execution-context\\isSafari.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\element\\cloneElement.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\element\\getElementFromPoint.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\element\\proxiedElements.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\event-listeners\\Listeners.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\frame\\getFrameElement.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\frame\\getFrameElements.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scheduling\\timeout.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scheduling\\throttle.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\bounding-rectangle\\isRectEqual.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\bounding-rectangle\\isVisible.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\observers\\ResizeNotifier.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\observers\\PositionObserver.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\observers\\FrameObserver.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\popover\\supportsPopover.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\popover\\showPopover.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\popover\\hidePopover.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scroll\\documentScrollingElement.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scroll\\getScrollPosition.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scroll\\canScroll.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scheduling\\scheduler.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\styles\\getComputedStyles.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scroll\\isFixed.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scroll\\isScrollable.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scroll\\getScrollableAncestors.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\frame\\getFrameTransform.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\transform\\parseScale.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\transform\\parseTranslate.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\transform\\parseTransform.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scroll\\detectScrollIntent.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\scroll\\scrollIntoViewIfNeeded.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\transform\\applyTransform.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\transform\\inverseTransform.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\transform\\animateTransform.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\transform\\computeTranslate.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\animations\\forceFinishAnimations.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\shapes\\DOMRectangle.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\supportsStyle.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\styles\\Styles.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isElement.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isKeyboardEvent.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isPointerEvent.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\supportsViewTransition.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\type-guards\\isTextInput.ts", "C:\\laragon\\www\\frontend\\node_modules\\@dnd-kit\\dom\\src\\utilities\\misc\\generateUniqueId.ts"], "sourcesContent": ["export function isKeyframeEffect(\n  effect: AnimationEffect | null | undefined\n): effect is KeyframeEffect {\n  if (!effect) return false;\n\n  if (effect instanceof KeyframeEffect) return true;\n\n  return 'getKeyframes' in effect && typeof effect.getKeyframes === 'function';\n}\n", "import {isKeyframeEffect} from '../type-guards/isKeyframeEffect.ts';\n\nexport function getFinalKeyframe(\n  element: Element,\n  match: (keyframe: Keyframe) => boolean\n): [Keyframe, Animation] | null {\n  const animations = element.getAnimations();\n\n  if (animations.length > 0) {\n    for (const animation of animations) {\n      if (animation.playState !== 'running') continue;\n      const {effect} = animation;\n      const keyframes = isKeyframeEffect(effect) ? effect.getKeyframes() : [];\n      const matchedKeyframes = keyframes.filter(match);\n\n      if (matchedKeyframes.length > 0) {\n        return [matchedKeyframes[matchedKeyframes.length - 1], animation];\n      }\n    }\n  }\n\n  return null;\n}\n", "import {BoundingRectangle} from '@dnd-kit/geometry';\n\nexport function getBoundingRectangle(element: Element): BoundingRectangle {\n  const {width, height, top, left, bottom, right} =\n    element.getBoundingClientRect();\n\n  return {width, height, top, left, bottom, right};\n}\n", "// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nexport const canUseDOM =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined';\n", "export function isWindow(element: Object): element is typeof window {\n  const elementString = Object.prototype.toString.call(element);\n  return (\n    elementString === '[object Window]' ||\n    // In Electron context the Window object serializes to [object global]\n    elementString === '[object global]'\n  );\n}\n", "export function isNode(node: Object): node is Node {\n  return 'nodeType' in node;\n}\n", "import {isWindow} from '../type-guards/isWindow.ts';\nimport {isNode} from '../type-guards/isNode.ts';\n\nexport function getWindow(\n  target: Event['target'] | null | undefined\n): typeof window {\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  if ('defaultView' in target) {\n    return (target.defaultView as typeof window | null) ?? window;\n  }\n\n  return target.ownerDocument?.defaultView ?? window;\n}\n", "import {getWindow} from '../execution-context/getWindow.ts';\n\nexport function isDocument(node: Node): node is Document {\n  const {Document} = getWindow(node);\n\n  return (\n    node instanceof Document ||\n    ('nodeType' in node && node.nodeType === Node.DOCUMENT_NODE)\n  );\n}\n", "import {getWindow} from '../execution-context/getWindow.ts';\n\nimport {isWindow} from './isWindow.ts';\n\nexport function isHTMLElement(\n  node: Node | Window | null | undefined\n): node is HTMLElement {\n  if (!node || isWindow(node)) return false;\n\n  return (\n    node instanceof getWindow(node).HTMLElement ||\n    ('namespaceURI' in node &&\n      typeof node.namespaceURI === 'string' &&\n      node.namespaceURI.endsWith('html'))\n  );\n}\n", "import {getWindow} from '../execution-context/getWindow.ts';\n\nexport function isSVGElement(node: Node): node is SVGElement {\n  return (\n    node instanceof getWindow(node).SVGElement ||\n    ('namespaceURI' in node &&\n      typeof node.namespaceURI === 'string' &&\n      node.namespaceURI.endsWith('svg'))\n  );\n}\n", "import {isDocument} from '../type-guards/isDocument.ts';\nimport {isHTMLElement} from '../type-guards/isHTMLElement.ts';\nimport {isNode} from '../type-guards/isNode.ts';\nimport {isSVGElement} from '../type-guards/isSVGElement.ts';\nimport {isWindow} from '../type-guards/isWindow.ts';\n\nexport function getDocument(target: Event['target'] | undefined): Document {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n", "import type {BoundingRectangle} from '@dnd-kit/geometry';\n\nimport {getDocument} from '../execution-context/index.ts';\n\n/**\n * Returns the bounding rectangle of the viewport\n * @param element\n * @returns BoundingRectangle\n */\nexport function getViewportBoundingRectangle(\n  element: Element\n): BoundingRectangle {\n  const {documentElement} = getDocument(element);\n  const width = documentElement.clientWidth;\n  const height = documentElement.clientHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "/*\n * Check if an element has visible overflow.\n * @param element\n * @param style\n * @returns boolean\n */\nexport function isOverflowVisible(\n  element: Element,\n  style?: CSSStyleDeclaration\n) {\n  if (isDetailsElement(element) && element.open === false) {\n    return false;\n  }\n\n  const {overflow, overflowX, overflowY} = style ?? getComputedStyle(element);\n\n  return (\n    overflow === 'visible' && overflowX === 'visible' && overflowY === 'visible'\n  );\n}\n\nfunction isDetailsElement(element: Element): element is HTMLDetailsElement {\n  return element.tagName === 'DETAILS';\n}\n", "import type {BoundingRectangle} from '@dnd-kit/geometry';\n\nimport {isOverflowVisible} from './isOverflowVisible.ts';\n\n/*\n * Get the currently visible bounding rectangle of an element\n * @param element\n * @param boundingClientRect\n * @returns Rect\n */\nexport function getVisibleBoundingRectangle(\n  element: Element,\n  boundingClientRect = element.getBoundingClientRect(),\n  margin = 0\n): BoundingRectangle {\n  // Get the initial bounding client rect of the element\n  let rect: BoundingRectangle = boundingClientRect;\n  const {ownerDocument} = element;\n  const ownerWindow = ownerDocument.defaultView ?? window;\n\n  // Traverse up the DOM tree to clip the rect based on ancestors' bounding rects\n  let ancestor: HTMLElement | null = element.parentElement;\n\n  while (ancestor && ancestor !== ownerDocument.documentElement) {\n    if (!isOverflowVisible(ancestor)) {\n      const ancestorRect = ancestor.getBoundingClientRect();\n\n      const marginTop = margin * (ancestorRect.bottom - ancestorRect.top);\n      const marginRight = margin * (ancestorRect.right - ancestorRect.left);\n      const marginBottom = margin * (ancestorRect.bottom - ancestorRect.top);\n      const marginLeft = margin * (ancestorRect.right - ancestorRect.left);\n\n      // Clip the rect based on the ancestor's bounding rect\n      rect = {\n        top: Math.max(rect.top, ancestorRect.top - marginTop),\n        right: Math.min(rect.right, ancestorRect.right + marginRight),\n        bottom: Math.min(rect.bottom, ancestorRect.bottom + marginBottom),\n        left: Math.max(rect.left, ancestorRect.left - marginLeft),\n        width: 0, // Will be calculated next\n        height: 0, // Will be calculated next\n      };\n\n      // Calculate the width and height after clipping\n      rect.width = rect.right - rect.left;\n      rect.height = rect.bottom - rect.top;\n    }\n\n    // Move to the next ancestor\n    ancestor = ancestor.parentElement;\n  }\n\n  // Clip the rect based on the viewport (window)\n  const viewportWidth = ownerWindow.innerWidth;\n  const viewportHeight = ownerWindow.innerHeight;\n  const viewportMarginY = margin * viewportHeight;\n  const viewportMarginX = margin * viewportWidth;\n\n  rect = {\n    top: Math.max(rect.top, 0 - viewportMarginY),\n    right: Math.min(rect.right, viewportWidth + viewportMarginX),\n    bottom: Math.min(rect.bottom, viewportHeight + viewportMarginY),\n    left: Math.max(rect.left, 0 - viewportMarginX),\n    width: 0, // Will be calculated next\n    height: 0, // Will be calculated next\n  };\n\n  // Calculate the width and height after clipping\n  rect.width = rect.right - rect.left;\n  rect.height = rect.bottom - rect.top;\n\n  if (rect.width < 0) {\n    rect.width = 0;\n  }\n\n  if (rect.height < 0) {\n    rect.height = 0;\n  }\n\n  return rect;\n}\n", "export function isSafari() {\n  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n}\n", "export function cloneElement(element: Element): Element {\n  const selector = 'input, textarea, select, canvas, [contenteditable]';\n  const clonedElement = element.cloneNode(true) as HTMLElement;\n  const fields = Array.from(element.querySelectorAll(selector));\n  const clonedFields = Array.from(clonedElement.querySelectorAll(selector));\n\n  clonedFields.forEach((field, index) => {\n    const originalField = fields[index];\n\n    if (isField(field) && isField(originalField)) {\n      if (field.type !== 'file') {\n        field.value = originalField.value;\n      }\n\n      // Fixes an issue with original radio buttons losing their value once the\n      // clone is inserted in the DOM, as radio button `name` attributes must be unique\n      if (field.type === 'radio' && field.name) {\n        field.name = `Cloned__${field.name}`;\n      }\n    }\n\n    if (\n      isCanvasElement(field) &&\n      isCanvasElement(originalField) &&\n      originalField.width > 0 &&\n      originalField.height > 0\n    ) {\n      const destCtx = field.getContext('2d');\n      destCtx?.drawImage(originalField, 0, 0);\n    }\n  });\n\n  return clonedElement;\n}\n\nfunction isField(\n  element: Element\n): element is HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement {\n  return 'value' in element;\n}\n\nfunction isCanvasElement(element: Element): element is HTMLCanvasElement {\n  return element.tagName === 'CANVAS';\n}\n", "import type {Coordinates} from '@dnd-kit/geometry';\n\nexport function getElementFromPoint(\n  document: Document,\n  {x, y}: Coordinates\n): Element | null {\n  const element = document.elementFromPoint(x, y);\n\n  if (isIFrameElement(element)) {\n    const {contentDocument} = element;\n\n    if (contentDocument) {\n      const {left, top} = element.getBoundingClientRect();\n\n      return getElementFromPoint(contentDocument, {\n        x: x - left,\n        y: y - top,\n      });\n    }\n  }\n\n  return element;\n}\n\nfunction isIFrameElement(\n  element: Element | null\n): element is HTMLIFrameElement {\n  return element?.tagName === 'IFRAME';\n}\n", "export const ProxiedElements = new WeakMap<Element, Element>();\n", "export interface EventListenerDescriptor {\n  type: string;\n  listener(event: Event): void;\n  options?: AddEventListenerOptions;\n}\n\ntype EventListenerInput = EventListenerDescriptor[] | EventListenerDescriptor;\n\ntype EventListenerEntry = [EventTarget, EventListenerDescriptor];\n\nexport class Listeners {\n  private entries: Set<EventListenerEntry> = new Set();\n\n  constructor() {}\n\n  public bind(target: EventTarget, input: EventListenerInput) {\n    const listeners = Array.isArray(input) ? input : [input];\n    const entries: EventListenerEntry[] = [];\n\n    for (const descriptor of listeners) {\n      const {type, listener, options} = descriptor;\n      const entry: EventListenerEntry = [target, descriptor];\n\n      target.addEventListener(type, listener, options);\n      this.entries.add(entry);\n      entries.push(entry);\n    }\n\n    return function cleanup() {\n      for (const [target, {type, listener, options}] of entries) {\n        target.removeEventListener(type, listener, options);\n      }\n    };\n  }\n\n  public clear = () => {\n    for (const entry of this.entries) {\n      const [target, {type, listener, options}] = entry;\n\n      target.removeEventListener(type, listener, options);\n    }\n\n    this.entries.clear();\n  };\n}\n", "export function getFrameElement(el: Element | undefined) {\n  const refWindow = el?.ownerDocument.defaultView;\n\n  if (refWindow && refWindow.self !== refWindow.parent) {\n    return refWindow.frameElement;\n  }\n}\n", "import {getFrameElement} from './getFrameElement.ts';\n\nexport function getFrameElements(el: Element | undefined) {\n  const frames = new Set<Element>();\n  let frame = getFrameElement(el);\n\n  while (frame) {\n    frames.add(frame);\n    frame = getFrameElement(frame);\n  }\n\n  return frames;\n}\n", "export function timeout(callback: () => void, duration: number): () => void {\n  const id = setTimeout(callback, duration);\n\n  return () => clearTimeout(id);\n}\n", "import {timeout} from './timeout.ts';\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  const time = () => performance.now();\n  let cancel: () => void | undefined;\n  let lastRan: number;\n\n  return function (this: any, ...args: Parameters<T>) {\n    const context = this;\n    if (!lastRan) {\n      func.apply(context, args);\n      lastRan = time();\n    } else {\n      cancel?.();\n      cancel = timeout(\n        () => {\n          func.apply(context, args);\n          lastRan = time();\n        },\n        limit - (time() - lastRan)\n      );\n    }\n  };\n}\n", "import type {BoundingRectangle} from '@dnd-kit/geometry';\n\nexport function isRectEqual(\n  a: BoundingRectangle | undefined,\n  b: BoundingRectangle | undefined\n) {\n  if (a === b) return true;\n  if (!a || !b) return false;\n\n  return (\n    a.top == b.top &&\n    a.left == b.left &&\n    a.right == b.right &&\n    a.bottom == b.bottom\n  );\n}\n", "import {getVisibleBoundingRectangle} from './getVisibleBoundingRectangle.ts';\n\nexport function isVisible(\n  element: Element,\n  boundingClientRect = element.getBoundingClientRect()\n): boolean {\n  const {width, height} = getVisibleBoundingRectangle(\n    element,\n    boundingClientRect\n  );\n\n  return width > 0 && height > 0;\n}\n", "import {canUseDOM} from '../execution-context/canUseDOM.ts';\n\nconst Observer = canUseDOM\n  ? ResizeObserver\n  : class MockResizeObserver implements ResizeObserver {\n      observe() {}\n      unobserve() {}\n      disconnect() {}\n    };\n\nexport class ResizeNotifier extends Observer {\n  #initialized = false;\n\n  constructor(callback: ResizeObserverCallback) {\n    super((entries) => {\n      if (!this.#initialized) {\n        this.#initialized = true;\n        return;\n      }\n      callback(entries, this);\n    });\n  }\n}\n", "import {BoundingRectangle, Rectangle} from '@dnd-kit/geometry';\n\nimport {throttle} from '../scheduling/throttle.ts';\n\nimport {isRectEqual} from '../bounding-rectangle/isRectEqual.ts';\nimport {isVisible} from '../bounding-rectangle/isVisible.ts';\nimport {getVisibleBoundingRectangle} from '../bounding-rectangle/getVisibleBoundingRectangle.ts';\n\nimport {ResizeNotifier} from './ResizeNotifier.ts';\n\nexport type PositionObserverCallback = (\n  boundingClientRect: BoundingRectangle | null\n) => void;\n\nconst threshold = Array.from({length: 100}, (_, index) => index / 100);\nexport const THROTTLE_INTERVAL = 75;\n\nexport class PositionObserver {\n  constructor(\n    public element: Element,\n    public callback: PositionObserverCallback,\n    options: {debug?: boolean; skipInitial?: boolean} = {\n      debug: false,\n      skipInitial: false,\n    }\n  ) {\n    this.boundingClientRect = element.getBoundingClientRect();\n    this.#visible = isVisible(element, this.boundingClientRect);\n\n    let initial = true;\n    this.callback = (boundingClientRect) => {\n      if (initial) {\n        initial = false;\n        if (options.skipInitial) return;\n      }\n\n      callback(boundingClientRect);\n    };\n\n    const root = element.ownerDocument;\n\n    if (options?.debug) {\n      this.#debug = document.createElement('div');\n      this.#debug.style.background = 'rgba(0,0,0,0.15)';\n      this.#debug.style.position = 'fixed';\n      this.#debug.style.pointerEvents = 'none';\n      root.body.appendChild(this.#debug);\n    }\n\n    this.#visibilityObserver = new IntersectionObserver(\n      (entries: IntersectionObserverEntry[]) => {\n        const entry = entries[entries.length - 1];\n        const {boundingClientRect, isIntersecting: visible} = entry;\n        const {width, height} = boundingClientRect;\n        const previousVisible = this.#visible;\n\n        this.#visible = visible;\n\n        if (!width && !height) return;\n\n        if (previousVisible && !visible) {\n          this.#positionObserver?.disconnect();\n          this.callback(null);\n          this.#resizeObserver?.disconnect();\n          this.#resizeObserver = undefined;\n\n          if (this.#debug) this.#debug.style.visibility = 'hidden';\n        } else {\n          this.#observePosition();\n        }\n\n        if (visible && !this.#resizeObserver) {\n          this.#resizeObserver = new ResizeNotifier(this.#observePosition);\n          this.#resizeObserver.observe(element);\n        }\n      },\n      {\n        threshold,\n        root,\n      }\n    );\n\n    if (this.#visible && !options.skipInitial) {\n      this.callback(this.boundingClientRect);\n    }\n\n    this.#visibilityObserver.observe(element);\n  }\n\n  public boundingClientRect: DOMRectReadOnly;\n\n  public disconnect = () => {\n    this.#disconnected = true;\n    this.#resizeObserver?.disconnect();\n    this.#positionObserver?.disconnect();\n    this.#visibilityObserver.disconnect();\n    this.#debug?.remove();\n  };\n\n  #visible = true;\n  #previousBoundingClientRect: DOMRectReadOnly | undefined;\n  #resizeObserver: ResizeNotifier | undefined;\n  #positionObserver: IntersectionObserver | undefined;\n  #visibilityObserver: IntersectionObserver;\n  #debug: HTMLElement | undefined;\n  #disconnected = false;\n\n  #observePosition = throttle(() => {\n    const {element} = this;\n\n    this.#positionObserver?.disconnect();\n\n    if (this.#disconnected || !this.#visible || !element.isConnected) {\n      return;\n    }\n\n    const root = element.ownerDocument ?? document;\n    const {innerHeight, innerWidth} = root.defaultView ?? window;\n    const clientRect = element.getBoundingClientRect();\n    const visibleRect = getVisibleBoundingRectangle(element, clientRect);\n    const {top, left, bottom, right} = visibleRect;\n    const insetTop = -Math.floor(top);\n    const insetLeft = -Math.floor(left);\n    const insetRight = -Math.floor(innerWidth - right);\n    const insetBottom = -Math.floor(innerHeight - bottom);\n    const rootMargin = `${insetTop}px ${insetRight}px ${insetBottom}px ${insetLeft}px`;\n\n    this.boundingClientRect = clientRect;\n    this.#positionObserver = new IntersectionObserver(\n      (entries: IntersectionObserverEntry[]) => {\n        const [entry] = entries;\n        const {intersectionRect} = entry;\n        /*\n         * The intersection ratio returned by the intersection observer entry\n         * represents the ratio of the intersectionRect to the boundingClientRect,\n         * which is not what we want. We want the ratio of the intersectionRect\n         * to the rootBounds (visible rect).\n         */\n        const intersectionRatio =\n          entry.intersectionRatio !== 1\n            ? entry.intersectionRatio\n            : Rectangle.intersectionRatio(\n                intersectionRect,\n                getVisibleBoundingRectangle(element)\n              );\n\n        if (intersectionRatio !== 1) {\n          this.#observePosition();\n        }\n      },\n      {\n        threshold,\n        rootMargin,\n        root,\n      }\n    );\n\n    this.#positionObserver.observe(element);\n    this.#notify();\n  }, THROTTLE_INTERVAL);\n\n  #notify() {\n    if (this.#disconnected) return;\n\n    this.#updateDebug();\n\n    if (isRectEqual(this.boundingClientRect, this.#previousBoundingClientRect))\n      return;\n\n    this.callback(this.boundingClientRect);\n    this.#previousBoundingClientRect = this.boundingClientRect;\n  }\n\n  #updateDebug() {\n    if (this.#debug) {\n      const {top, left, width, height} = getVisibleBoundingRectangle(\n        this.element\n      );\n\n      this.#debug.style.overflow = 'hidden';\n      this.#debug.style.visibility = 'visible';\n      this.#debug.style.top = `${Math.floor(top)}px`;\n      this.#debug.style.left = `${Math.floor(left)}px`;\n      this.#debug.style.width = `${Math.floor(width)}px`;\n      this.#debug.style.height = `${Math.floor(height)}px`;\n    }\n  }\n}\n", "import {getFrameElements} from '../frame/getFrameElements.ts';\nimport {throttle} from '../scheduling/throttle.ts';\nimport {\n  PositionObserver,\n  THROTTLE_INTERVAL,\n  type PositionObserverCallback,\n} from './PositionObserver.ts';\n\nconst framePositionObservers = new WeakMap<\n  Element,\n  {\n    disconnect: () => void;\n    callbacks: Set<PositionObserverCallback>;\n  }\n>();\n\nconst scrollListeners = new WeakMap<\n  Document,\n  {\n    disconnect: () => void;\n    listeners: Set<EventListener>;\n  }\n>();\n\nfunction addFrameListener(frame: Element, callback: PositionObserverCallback) {\n  // Check if already observed globally\n  let cached = framePositionObservers.get(frame);\n\n  if (!cached) {\n    const observer = new PositionObserver(\n      frame,\n      (boundingClientRect) => {\n        const cached = framePositionObservers.get(frame);\n        if (!cached) return;\n\n        cached.callbacks.forEach((callback) => callback(boundingClientRect));\n      },\n      {skipInitial: true}\n    );\n\n    cached = {disconnect: observer.disconnect, callbacks: new Set()};\n  }\n\n  cached.callbacks.add(callback);\n  framePositionObservers.set(frame, cached);\n\n  return () => {\n    cached.callbacks.delete(callback);\n\n    if (cached.callbacks.size === 0) {\n      framePositionObservers.delete(frame);\n      cached.disconnect();\n    }\n  };\n}\n\nfunction observeParentFrames(\n  frames: Set<Element>,\n  callback: PositionObserverCallback\n) {\n  const cleanup = new Set<() => void>();\n\n  for (const frame of frames) {\n    const remove = addFrameListener(frame, callback);\n    cleanup.add(remove);\n  }\n\n  return () => cleanup.forEach((remove) => remove());\n}\n\nfunction addScrollListener(element: Element, callback: EventListener) {\n  const doc = element.ownerDocument;\n\n  if (!scrollListeners.has(doc)) {\n    const controller = new AbortController();\n    const listeners = new Set<EventListener>();\n\n    document.addEventListener(\n      'scroll',\n      (event) => listeners.forEach((listener) => listener(event)),\n      {\n        capture: true,\n        passive: true,\n        signal: controller.signal,\n      }\n    );\n\n    scrollListeners.set(doc, {disconnect: () => controller.abort(), listeners});\n  }\n\n  const {listeners, disconnect} = scrollListeners.get(doc) ?? {};\n\n  if (!listeners || !disconnect) return () => {};\n\n  listeners.add(callback);\n\n  return () => {\n    listeners.delete(callback);\n\n    if (listeners.size === 0) {\n      disconnect();\n      scrollListeners.delete(doc);\n    }\n  };\n}\n\nexport class FrameObserver {\n  #elementObserver: PositionObserver;\n  #disconnected = false;\n  #frames: Set<Element>;\n\n  constructor(\n    element: Element,\n    private callback: PositionObserverCallback,\n    options?: {debug?: boolean}\n  ) {\n    const frames = getFrameElements(element);\n    const unobserveParentFrames = observeParentFrames(frames, callback);\n    const removeScrollListener = addScrollListener(element, this.#handleScroll);\n\n    this.#frames = frames;\n    this.#elementObserver = new PositionObserver(element, callback, options);\n    this.disconnect = () => {\n      if (this.#disconnected) return;\n      this.#disconnected = true;\n\n      unobserveParentFrames();\n      removeScrollListener();\n      this.#elementObserver.disconnect();\n    };\n  }\n\n  disconnect: () => void;\n\n  #handleScroll = throttle((event: Event) => {\n    if (this.#disconnected) return;\n    if (!event.target) return;\n    if (\n      'contains' in event.target &&\n      typeof event.target.contains === 'function'\n    ) {\n      for (const frame of this.#frames) {\n        if (event.target.contains(frame)) {\n          this.callback(this.#elementObserver.boundingClientRect);\n          break;\n        }\n      }\n    }\n  }, THROTTLE_INTERVAL);\n}\n", "export function supportsPopover(element: Element): element is Element & {\n  showPopover(): void;\n  hidePopover(): void;\n} {\n  return (\n    'showPopover' in element &&\n    'hidePopover' in element &&\n    typeof element.showPopover === 'function' &&\n    typeof element.hidePopover === 'function'\n  );\n}\n", "import {supportsPopover} from './supportsPopover.ts';\n\nexport function showPopover(element: Element) {\n  try {\n    if (\n      supportsPopover(element) &&\n      element.isConnected &&\n      element.hasAttribute('popover') &&\n      // This selector can throw an error in browsers that don't support it\n      !element.matches(':popover-open')\n    ) {\n      element.showPopover();\n    }\n  } catch (error) {\n    // no-op\n  }\n}\n", "import {supportsPopover} from './supportsPopover.ts';\n\nexport function hidePopover(element: Element) {\n  try {\n    if (\n      supportsPopover(element) &&\n      element.isConnected &&\n      element.hasAttribute('popover') &&\n      // This selector can throw an error in browsers that don't support it\n      element.matches(':popover-open')\n    ) {\n      element.hidePopover();\n    }\n  } catch (error) {\n    // no-op\n  }\n}\n", "import {canUseDOM} from '../execution-context/canUseDOM.ts';\nimport {getDocument} from '../execution-context/getDocument.ts';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === getDocument(element).scrollingElement;\n}\n", "import {getBoundingRectangle} from '../bounding-rectangle/getBoundingRectangle.ts';\nimport {getViewportBoundingRectangle} from '../bounding-rectangle/getViewportBoundingRectangle.ts';\nimport {getWindow} from '../execution-context/getWindow.ts';\nimport {isDocumentScrollingElement} from './documentScrollingElement.ts';\n\nexport function getScrollPosition(scrollableElement: Element) {\n  const window = getWindow(scrollableElement);\n  const rect = isDocumentScrollingElement(scrollableElement)\n    ? getViewportBoundingRectangle(scrollableElement)\n    : getBoundingRectangle(scrollableElement);\n\n  const dimensions = isDocumentScrollingElement(scrollableElement)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollableElement.clientHeight,\n        width: scrollableElement.clientWidth,\n      };\n  const position = {\n    current: {\n      x: scrollableElement.scrollLeft,\n      y: scrollableElement.scrollTop,\n    },\n    max: {\n      x: scrollableElement.scrollWidth - dimensions.width,\n      y: scrollableElement.scrollHeight - dimensions.height,\n    },\n  };\n\n  const isTop = position.current.y <= 0;\n  const isLeft = position.current.x <= 0;\n  const isBottom = position.current.y >= position.max.y;\n  const isRight = position.current.x >= position.max.x;\n\n  return {\n    rect,\n    position,\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n  };\n}\n", "import type {Coordinates} from '@dnd-kit/geometry';\n\nimport {getScrollPosition} from './getScrollPosition.ts';\n\nexport function canScroll(scrollableElement: Element, by?: Coordinates) {\n  const {isTop, isBottom, isLeft, isRight, position} =\n    getScrollPosition(scrollableElement);\n\n  const {x, y} = by ?? {x: 0, y: 0};\n\n  const top = !isTop && position.current.y + y > 0;\n  const bottom = !isBottom && position.current.y + y < position.max.y;\n  const left = !isLeft && position.current.x + x > 0;\n  const right = !isRight && position.current.x + x < position.max.x;\n\n  return {\n    top,\n    bottom,\n    left,\n    right,\n    x: left || right,\n    y: top || bottom,\n  };\n}\n", "type Callback = () => void;\n\nexport class Scheduler<T extends (callback: Callback) => any> {\n  constructor(private scheduler: T) {}\n\n  private pending: boolean = false;\n  private tasks: Set<() => void> = new Set();\n  private resolvers: Set<() => void> = new Set();\n\n  public schedule(task: () => void): Promise<void> {\n    this.tasks.add(task);\n\n    if (!this.pending) {\n      this.pending = true;\n      this.scheduler(this.flush);\n    }\n\n    return new Promise<void>((resolve) => this.resolvers.add(resolve));\n  }\n\n  public flush = () => {\n    const {tasks, resolvers} = this;\n\n    this.pending = false;\n    this.tasks = new Set();\n    this.resolvers = new Set();\n\n    for (const task of tasks) {\n      task();\n    }\n\n    for (const resolve of resolvers) {\n      resolve();\n    }\n  };\n}\n\nexport const scheduler = new Scheduler((callback) => {\n  if (typeof requestAnimationFrame === 'function') {\n    requestAnimationFrame(callback);\n  } else {\n    callback();\n  }\n});\n", "import {getWindow} from '../execution-context/getWindow.ts';\nimport {Scheduler} from '../scheduling/scheduler.ts';\n\nconst scheduler = new Scheduler((callback) => setTimeout(callback, 50));\nconst cachedStyles = new Map<Element, CSSStyleDeclaration>();\nconst clear = cachedStyles.clear.bind(cachedStyles);\n\n/**\n * Get the computed styles for an element.\n * @param element - The element to get the computed styles for.\n * @param cached - Whether to cache the computed styles.\n * @returns The computed styles for the element.\n */\nexport function getComputedStyles(\n  element: Element,\n  cached = false\n): CSSStyleDeclaration {\n  if (!cached) return computeStyles(element);\n\n  let styles = cachedStyles.get(element);\n\n  if (styles) return styles;\n\n  styles = computeStyles(element);\n  cachedStyles.set(element, styles);\n  scheduler.schedule(clear);\n\n  return styles;\n}\n\nfunction computeStyles(element: Element): CSSStyleDeclaration {\n  return getWindow(element).getComputedStyle(element);\n}\n", "import {getComputedStyles} from '../styles/getComputedStyles.ts';\n\nexport function isFixed(\n  node: Element,\n  computedStyle: CSSStyleDeclaration = getComputedStyles(node, true)\n): boolean {\n  return (\n    computedStyle.position === 'fixed' || computedStyle.position === 'sticky'\n  );\n}\n", "import {getComputedStyles} from '../styles/getComputedStyles.ts';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getComputedStyles(element, true)\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {getWindow} from '../execution-context/getWindow.ts';\nimport {isDocument} from '../type-guards/isDocument.ts';\nimport {isHTMLElement} from '../type-guards/isHTMLElement.ts';\nimport {isSVGElement} from '../type-guards/isSVGElement.ts';\nimport {getComputedStyles} from '../styles/getComputedStyles.ts';\nimport {isFixed} from './isFixed.ts';\nimport {isScrollable} from './isScrollable.ts';\n\ninterface Options {\n  limit?: number;\n  excludeElement?: boolean;\n}\n\nconst defaultOptions: Options = {\n  excludeElement: true,\n};\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  options: Options = defaultOptions\n): Set<Element> {\n  const {limit, excludeElement} = options;\n  const scrollParents = new Set<Element>();\n\n  function findScrollableAncestors(node: Node | null): Set<Element> {\n    if (limit != null && scrollParents.size >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.has(node.scrollingElement)\n    ) {\n      scrollParents.add(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node)) {\n      if (isSVGElement(node)) {\n        return findScrollableAncestors(node.parentElement);\n      }\n\n      return scrollParents;\n    }\n\n    if (scrollParents.has(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getComputedStyles(node, true);\n\n    if (excludeElement && node === element) {\n      // no-op\n    } else if (isScrollable(node, computedStyle)) {\n      scrollParents.add(node);\n    }\n\n    if (isFixed(node, computedStyle)) {\n      const {scrollingElement} = node.ownerDocument;\n\n      if (scrollingElement) scrollParents.add(scrollingElement);\n\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, {limit: 1});\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {getComputedStyles} from '../styles/getComputedStyles.ts';\nimport {isHTMLElement} from '../type-guards/isHTMLElement.ts';\nimport type {Transform} from '../transform/index.ts';\n\nimport {getFrameElement} from './getFrameElement.ts';\nimport {getBoundingRectangle} from '../bounding-rectangle/getBoundingRectangle.ts';\n\nexport function getFrameTransform(\n  el: Element | undefined,\n  boundary: Element | null = window.frameElement\n): Transform {\n  const transform: Transform = {\n    x: 0,\n    y: 0,\n    scaleX: 1,\n    scaleY: 1,\n  };\n\n  if (!el) return transform;\n\n  let frame = getFrameElement(el);\n\n  while (frame) {\n    if (frame === boundary) {\n      return transform;\n    }\n\n    const rect = getBoundingRectangle(frame);\n    const {x: scaleX, y: scaleY} = getScale(frame, rect);\n\n    transform.x = transform.x + rect.left;\n    transform.y = transform.y + rect.top;\n    transform.scaleX = transform.scaleX * scaleX;\n    transform.scaleY = transform.scaleY * scaleY;\n\n    frame = getFrameElement(frame);\n  }\n\n  return transform;\n}\n\nfunction getScale(\n  element: Element,\n  boundingRectangle = getBoundingRectangle(element)\n) {\n  const width = Math.round(boundingRectangle.width);\n  const height = Math.round(boundingRectangle.height);\n\n  if (isHTMLElement(element)) {\n    return {\n      x: width / element.offsetWidth,\n      y: height / element.offsetHeight,\n    };\n  }\n\n  const styles = getComputedStyles(element, true);\n\n  return {\n    x: (parseFloat(styles.width) || width) / width,\n    y: (parseFloat(styles.height) || height) / height,\n  };\n}\n", "export function parseScale(scale: string) {\n  if (scale === 'none') {\n    return null;\n  }\n\n  const values = scale.split(' ');\n  const x = parseFloat(values[0]);\n  const y = parseFloat(values[1]);\n\n  if (isNaN(x) && isNaN(y)) {\n    return null;\n  }\n\n  return {\n    x: isNaN(x) ? y : x,\n    y: isNaN(y) ? x : y,\n  };\n}\n", "export function parseTranslate(translate: string) {\n  if (translate === 'none') {\n    return null;\n  }\n\n  const [x, y, z = '0'] = translate.split(' ');\n  const output = {x: parseFloat(x), y: parseFloat(y), z: parseInt(z, 10)};\n\n  if (isNaN(output.x) && isNaN(output.y)) {\n    return null;\n  }\n\n  return {\n    x: isNaN(output.x) ? 0 : output.x,\n    y: isNaN(output.y) ? 0 : output.y,\n    z: isNaN(output.z) ? 0 : output.z,\n  };\n}\n", "import type {Coordinates} from '@dnd-kit/geometry';\n\nimport {parseScale} from './parseScale.ts';\nimport {parseTranslate} from './parseTranslate.ts';\n\nexport interface Transform extends Coordinates {\n  z?: number;\n  scaleX: number;\n  scaleY: number;\n}\n\nexport function parseTransform(computedStyles: {\n  scale: string;\n  transform: string;\n  translate: string;\n}): Transform | null {\n  const {scale, transform, translate} = computedStyles;\n  const parsedScale = parseScale(scale);\n  const parsedTranslate = parseTranslate(translate);\n  const parsedMatrix = parseTransformMatrix(transform);\n\n  if (!parsedMatrix && !parsedScale && !parsedTranslate) {\n    return null;\n  }\n\n  const normalizedScale = {\n    x: parsedScale?.x ?? 1,\n    y: parsedScale?.y ?? 1,\n  };\n\n  const normalizedTranslate = {\n    x: parsedTranslate?.x ?? 0,\n    y: parsedTranslate?.y ?? 0,\n  };\n\n  const normalizedMatrix = {\n    x: parsedMatrix?.x ?? 0,\n    y: parsedMatrix?.y ?? 0,\n    scaleX: parsedMatrix?.scaleX ?? 1,\n    scaleY: parsedMatrix?.scaleY ?? 1,\n  };\n\n  return {\n    x: normalizedTranslate.x + normalizedMatrix.x,\n    y: normalizedTranslate.y + normalizedMatrix.y,\n    z: parsedTranslate?.z ?? 0,\n    scaleX: normalizedScale.x * normalizedMatrix.scaleX,\n    scaleY: normalizedScale.y * normalizedMatrix.scaleY,\n  };\n}\n\nfunction parseTransformMatrix(transform: string) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import {Rectangle, type Axis, type Coordinates} from '@dnd-kit/geometry';\n\nimport {getScrollPosition} from './getScrollPosition.ts';\nimport {getFrameTransform} from '../frame/getFrameTransform.ts';\nimport {getComputedStyles} from '../styles/getComputedStyles.ts';\nimport {parseTransform} from '../transform/parseTransform.ts';\n\nexport enum ScrollDirection {\n  Idle = 0,\n  Forward = 1,\n  Reverse = -1,\n}\n\nconst defaultThreshold: Record<Axis, number> = {\n  x: 0.2,\n  y: 0.2,\n};\n\nconst defaultTolerance: Record<Axis, number> = {\n  x: 10,\n  y: 10,\n};\n\ninterface ScrollIntent {\n  x: ScrollDirection;\n  y: ScrollDirection;\n}\n\nexport function detectScrollIntent(\n  scrollableElement: Element,\n  coordinates: Coordinates,\n  intent?: ScrollIntent,\n  acceleration = 25,\n  thresholdPercentage = defaultThreshold,\n  tolerance = defaultTolerance\n) {\n  const {x, y} = coordinates;\n  const {rect, isTop, isBottom, isLeft, isRight} =\n    getScrollPosition(scrollableElement);\n  const frameTransform = getFrameTransform(scrollableElement);\n  const computedStyles = getComputedStyles(scrollableElement, true);\n  const parsedTransform = parseTransform(computedStyles);\n  const isXAxisInverted =\n    parsedTransform !== null ? parsedTransform?.scaleX < 0 : false;\n  const isYAxisInverted =\n    parsedTransform !== null ? parsedTransform?.scaleY < 0 : false;\n  const scrollContainerRect = new Rectangle(\n    rect.left * frameTransform.scaleX + frameTransform.x,\n    rect.top * frameTransform.scaleY + frameTransform.y,\n    rect.width * frameTransform.scaleX,\n    rect.height * frameTransform.scaleY\n  );\n  const direction: Record<Axis, ScrollDirection> = {\n    x: ScrollDirection.Idle,\n    y: ScrollDirection.Idle,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (\n    (!isTop || (isYAxisInverted && !isBottom)) &&\n    y <= scrollContainerRect.top + threshold.height &&\n    intent?.y !== ScrollDirection.Forward &&\n    x >= scrollContainerRect.left - tolerance.x &&\n    x <= scrollContainerRect.right + tolerance.x\n  ) {\n    // Scroll Up (or Down if inverted)\n    direction.y = isYAxisInverted\n      ? ScrollDirection.Forward\n      : ScrollDirection.Reverse;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - y) / threshold.height\n      );\n  } else if (\n    (!isBottom || (isYAxisInverted && !isTop)) &&\n    y >= scrollContainerRect.bottom - threshold.height &&\n    intent?.y !== ScrollDirection.Reverse &&\n    x >= scrollContainerRect.left - tolerance.x &&\n    x <= scrollContainerRect.right + tolerance.x\n  ) {\n    // Scroll Down (or Up if inverted)\n    direction.y = isYAxisInverted\n      ? ScrollDirection.Reverse\n      : ScrollDirection.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - y) / threshold.height\n      );\n  }\n\n  if (\n    (!isRight || (isXAxisInverted && !isLeft)) &&\n    x >= scrollContainerRect.right - threshold.width &&\n    intent?.x !== ScrollDirection.Reverse &&\n    y >= scrollContainerRect.top - tolerance.y &&\n    y <= scrollContainerRect.bottom + tolerance.y\n  ) {\n    // Scroll Right (or Left if inverted)\n    direction.x = isXAxisInverted\n      ? ScrollDirection.Reverse\n      : ScrollDirection.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - x) / threshold.width\n      );\n  } else if (\n    (!isLeft || (isXAxisInverted && !isRight)) &&\n    x <= scrollContainerRect.left + threshold.width &&\n    intent?.x !== ScrollDirection.Forward &&\n    y >= scrollContainerRect.top - tolerance.y &&\n    y <= scrollContainerRect.bottom + tolerance.y\n  ) {\n    // Scroll Left (or Right if inverted)\n    direction.x = isXAxisInverted\n      ? ScrollDirection.Forward\n      : ScrollDirection.Reverse;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - x) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "import {getComputedStyles} from '../styles/getComputedStyles.ts';\nimport {isHTMLElement} from '../type-guards/isHTMLElement.ts';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors.ts';\n\nfunction supportsScrollIntoViewIfNeeded(\n  element: Element\n): element is Element & {\n  scrollIntoViewIfNeeded: (centerIfNeeded?: boolean) => void;\n} {\n  return (\n    'scrollIntoViewIfNeeded' in element &&\n    typeof element.scrollIntoViewIfNeeded === 'function'\n  );\n}\n\nexport function scrollIntoViewIfNeeded(el: Element, centerIfNeeded = false) {\n  if (supportsScrollIntoViewIfNeeded(el)) {\n    el.scrollIntoViewIfNeeded(centerIfNeeded);\n    return;\n  }\n\n  if (!isHTMLElement(el)) {\n    return el.scrollIntoView();\n  }\n\n  var parent = getFirstScrollableAncestor(el);\n\n  if (!isHTMLElement(parent)) {\n    return;\n  }\n\n  const parentComputedStyle = getComputedStyles(parent, true),\n    parentBorderTopWidth = parseInt(\n      parentComputedStyle.getPropertyValue('border-top-width')\n    ),\n    parentBorderLeftWidth = parseInt(\n      parentComputedStyle.getPropertyValue('border-left-width')\n    ),\n    overTop = el.offsetTop - parent.offsetTop < parent.scrollTop,\n    overBottom =\n      el.offsetTop - parent.offsetTop + el.clientHeight - parentBorderTopWidth >\n      parent.scrollTop + parent.clientHeight,\n    overLeft = el.offsetLeft - parent.offsetLeft < parent.scrollLeft,\n    overRight =\n      el.offsetLeft -\n        parent.offsetLeft +\n        el.clientWidth -\n        parentBorderLeftWidth >\n      parent.scrollLeft + parent.clientWidth,\n    alignWithTop = overTop && !overBottom;\n\n  if ((overTop || overBottom) && centerIfNeeded) {\n    parent.scrollTop =\n      el.offsetTop -\n      parent.offsetTop -\n      parent.clientHeight / 2 -\n      parentBorderTopWidth +\n      el.clientHeight / 2;\n  }\n\n  if ((overLeft || overRight) && centerIfNeeded) {\n    parent.scrollLeft =\n      el.offsetLeft -\n      parent.offsetLeft -\n      parent.clientWidth / 2 -\n      parentBorderLeftWidth +\n      el.clientWidth / 2;\n  }\n\n  if ((overTop || overBottom || overLeft || overRight) && !centerIfNeeded) {\n    el.scrollIntoView(alignWithTop);\n  }\n}\n", "import type {BoundingRectangle} from '@dnd-kit/geometry';\n\nimport {type Transform} from './parseTransform.ts';\n\nexport function applyTransform(\n  rect: BoundingRectangle,\n  parsedTransform: Transform,\n  transformOrigin: string\n): BoundingRectangle {\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n  const x = rect.left + translateX + (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top +\n    translateY +\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width * scaleX : rect.width;\n  const h = scaleY ? rect.height * scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import type {BoundingRectangle} from '@dnd-kit/geometry';\n\nimport {type Transform} from './parseTransform.ts';\n\nexport function inverseTransform(\n  rect: BoundingRectangle,\n  parsedTransform: Transform,\n  transformOrigin: string\n): BoundingRectangle {\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "interface Arguments {\n  element: Element;\n  keyframes: PropertyIndexedKeyframes | Keyframe[];\n  options: KeyframeAnimationOptions;\n}\n\nexport function animateTransform({element, keyframes, options}: Arguments) {\n  return element.animate(keyframes, options).finished;\n}\n", "import {getFinalKeyframe} from '../animations/getFinalKeyframe.ts';\nimport {getComputedStyles} from '../styles/getComputedStyles.ts';\n\nimport {parseTranslate} from './parseTranslate.ts';\n\nexport function computeTranslate(\n  element: Element,\n  translate = getComputedStyles(element).translate,\n  projected = true\n): {\n  x: number;\n  y: number;\n  z: number;\n} {\n  if (projected) {\n    const keyframe = getFinalKeyframe(\n      element,\n      (keyframe) => 'translate' in keyframe\n    );\n\n    if (keyframe) {\n      const {translate = ''} = keyframe[0];\n\n      if (typeof translate === 'string') {\n        const finalTranslate = parseTranslate(translate);\n\n        if (finalTranslate) {\n          return finalTranslate;\n        }\n      }\n    }\n  }\n\n  if (translate) {\n    const finalTranslate = parseTranslate(translate);\n\n    if (finalTranslate) {\n      return finalTranslate;\n    }\n  }\n\n  return {x: 0, y: 0, z: 0};\n}\n", "import {Scheduler} from '../scheduling/scheduler.ts';\nimport {isKeyframeEffect} from '../type-guards/isKeyframeEffect.ts';\n\nconst scheduler = new Scheduler((callback) => setTimeout(callback, 0));\nconst animations = new Map<Document | Element, Animation[]>();\nconst clear = animations.clear.bind(animations);\n\nfunction getDocumentAnimations(element: Element): Animation[] {\n  const document = element.ownerDocument;\n  let documentAnimations = animations.get(document);\n\n  if (documentAnimations) return documentAnimations;\n\n  documentAnimations = document.getAnimations();\n  animations.set(document, documentAnimations);\n  scheduler.schedule(clear);\n\n  const elementAnimations = documentAnimations.filter(\n    (animation) =>\n      isKeyframeEffect(animation.effect) && animation.effect.target === element\n  );\n\n  animations.set(element, elementAnimations);\n\n  return documentAnimations;\n}\n\n/*\n * Force animations on ancestors of the element into their end state\n * and return a function to reset them back to their current state.\n *\n * This is useful as it allows us to immediately calculate the final position\n * of an element without having to wait for the animations to finish.\n */\nexport function forceFinishAnimations(\n  element: Element,\n  options: {\n    properties: string[];\n    isValidTarget?: (target: Element) => boolean;\n  }\n): (() => void) | undefined {\n  const animations = getDocumentAnimations(element)\n    .filter((animation) => {\n      if (isKeyframeEffect(animation.effect)) {\n        const {target} = animation.effect;\n        const isValidTarget =\n          (target && options.isValidTarget?.(target)) ?? true;\n\n        if (isValidTarget) {\n          return animation.effect.getKeyframes().some((keyframe) => {\n            for (const property of options.properties) {\n              if (keyframe[property]) return true;\n            }\n          });\n        }\n      }\n    })\n    .map((animation) => {\n      const {effect, currentTime} = animation;\n      const duration = effect?.getComputedTiming().duration;\n\n      if (animation.pending || animation.playState === 'finished') return;\n\n      if (\n        typeof duration == 'number' &&\n        typeof currentTime == 'number' &&\n        currentTime < duration\n      ) {\n        animation.currentTime = duration;\n\n        return () => {\n          animation.currentTime = currentTime;\n        };\n      }\n    });\n\n  if (animations.length > 0) {\n    return () => animations.forEach((reset) => reset?.());\n  }\n}\n", "import {Rectangle, type BoundingRectangle} from '@dnd-kit/geometry';\n\nimport {applyTransform} from '../transform/applyTransform.ts';\nimport {inverseTransform} from '../transform/inverseTransform.ts';\nimport {getComputedStyles} from '../styles/getComputedStyles.ts';\nimport {parseTransform, type Transform} from '../transform/index.ts';\nimport {getBoundingRectangle} from '../bounding-rectangle/getBoundingRectangle.ts';\nimport {getFrameTransform} from '../frame/getFrameTransform.ts';\nimport {isKeyframeEffect} from '../type-guards/isKeyframeEffect.ts';\nimport {forceFinishAnimations} from '../animations/forceFinishAnimations.ts';\nimport {isSafari} from '../execution-context/isSafari.ts';\n\nexport interface DOMRectangleOptions {\n  getBoundingClientRect?: (element: Element) => BoundingRectangle;\n  /* Whether to ignore transforms when calculating the rectangle */\n  ignoreTransforms?: boolean;\n  frameTransform?: Transform | null;\n}\n\nexport class DOMRectangle extends Rectangle {\n  constructor(element: Element, options: DOMRectangleOptions = {}) {\n    const {\n      frameTransform = getFrameTransform(element),\n      ignoreTransforms,\n      getBoundingClientRect = getBoundingRectangle,\n    } = options;\n    const resetAnimations = forceFinishAnimations(element, {\n      properties: ['transform', 'translate', 'scale', 'width', 'height'],\n      isValidTarget: (target) =>\n        (target !== element || isSafari()) && target.contains(element),\n    });\n    const boundingRectangle = getBoundingClientRect(element);\n    let {top, left, width, height} = boundingRectangle;\n    let updated: BoundingRectangle | undefined;\n\n    const computedStyles = getComputedStyles(element);\n    const parsedTransform = parseTransform(computedStyles);\n\n    const scale = {\n      x: parsedTransform?.scaleX ?? 1,\n      y: parsedTransform?.scaleY ?? 1,\n    };\n\n    const projectedTransform = getProjectedTransform(element, computedStyles);\n\n    resetAnimations?.();\n\n    if (parsedTransform) {\n      updated = inverseTransform(\n        boundingRectangle,\n        parsedTransform,\n        computedStyles.transformOrigin\n      );\n\n      if (ignoreTransforms || projectedTransform) {\n        top = updated.top;\n        left = updated.left;\n        width = updated.width;\n        height = updated.height;\n      }\n    }\n\n    const intrinsic = {\n      width: updated?.width ?? width,\n      height: updated?.height ?? height,\n    };\n\n    if (projectedTransform && !ignoreTransforms && updated) {\n      const projected = applyTransform(\n        updated,\n        projectedTransform,\n        computedStyles.transformOrigin\n      );\n\n      top = projected.top;\n      left = projected.left;\n      width = projected.width;\n      height = projected.height;\n      scale.x = projectedTransform.scaleX;\n      scale.y = projectedTransform.scaleY;\n    }\n\n    if (frameTransform) {\n      if (!ignoreTransforms) {\n        left *= frameTransform.scaleX;\n        width *= frameTransform.scaleX;\n        top *= frameTransform.scaleY;\n        height *= frameTransform.scaleY;\n      }\n\n      left += frameTransform.x;\n      top += frameTransform.y;\n    }\n\n    super(left, top, width, height);\n\n    this.scale = scale;\n    this.intrinsicWidth = intrinsic.width;\n    this.intrinsicHeight = intrinsic.height;\n  }\n\n  public intrinsicWidth: number;\n  public intrinsicHeight: number;\n}\n\n/*\n * Get the projected transform of an element based on its final keyframe\n */\nfunction getProjectedTransform(\n  element: Element,\n  computedStyles: CSSStyleDeclaration\n): Transform | null {\n  // Always get the latest animations on the element itself\n  const animations = element.getAnimations();\n  let projectedTransform: Transform | null = null;\n\n  if (!animations.length) return null;\n\n  for (const animation of animations) {\n    if (animation.playState !== 'running') continue;\n    const keyframes = isKeyframeEffect(animation.effect)\n      ? animation.effect.getKeyframes()\n      : [];\n    const keyframe = keyframes[keyframes.length - 1];\n\n    if (!keyframe) continue;\n\n    const {transform, translate, scale} = keyframe;\n\n    if (transform || translate || scale) {\n      const parsedTransform = parseTransform({\n        transform:\n          typeof transform === 'string' && transform\n            ? transform\n            : computedStyles.transform,\n        translate:\n          typeof translate === 'string' && translate\n            ? translate\n            : computedStyles.translate,\n        scale:\n          typeof scale === 'string' && scale ? scale : computedStyles.scale,\n      });\n\n      if (parsedTransform) {\n        projectedTransform = projectedTransform\n          ? {\n              x: projectedTransform.x + parsedTransform.x,\n              y: projectedTransform.y + parsedTransform.y,\n              z: projectedTransform.z ?? parsedTransform.z,\n              scaleX: projectedTransform.scaleX * parsedTransform.scaleX,\n              scaleY: projectedTransform.scaleY * parsedTransform.scaleY,\n            }\n          : parsedTransform;\n      }\n    }\n  }\n\n  return projectedTransform;\n}\n", "export function supportsStyle(\n  element: Element\n): element is Element & {style: CSSStyleDeclaration} {\n  return (\n    'style' in element &&\n    typeof element.style === 'object' &&\n    element.style !== null &&\n    'setProperty' in element.style &&\n    'removeProperty' in element.style &&\n    typeof element.style.setProperty === 'function' &&\n    typeof element.style.removeProperty === 'function'\n  );\n}\n", "import {supportsStyle} from '../type-guards/supportsStyle.ts';\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\nexport type StyleDeclaration = ExtractStringProperties<CSSStyleDeclaration> & {\n  viewTransitionName?: string;\n};\n\nexport class Styles {\n  private initial = new Map<string, string>();\n\n  constructor(private element: Element) {}\n\n  public set(properties: Record<string, string | number>, prefix = '') {\n    const {element} = this;\n\n    if (!supportsStyle(element)) {\n      return;\n    }\n\n    for (const [key, value] of Object.entries(properties)) {\n      const property = `${prefix}${key}`;\n\n      if (!this.initial.has(property)) {\n        this.initial.set(property, element.style.getPropertyValue(property));\n      }\n\n      element.style.setProperty(\n        property,\n        typeof value === 'string' ? value : `${value}px`\n      );\n    }\n  }\n\n  public remove(properties: string[], prefix = '') {\n    const {element} = this;\n\n    if (!supportsStyle(element)) {\n      return;\n    }\n\n    for (const key of properties) {\n      const property = `${prefix}${key}`;\n\n      element.style.removeProperty(property);\n    }\n  }\n\n  public reset() {\n    const {element} = this;\n\n    if (!supportsStyle(element)) {\n      return;\n    }\n\n    for (const [key, value] of this.initial) {\n      element.style.setProperty(key, value);\n    }\n\n    if (element.getAttribute('style') === '') {\n      element.removeAttribute('style');\n    }\n  }\n}\n", "import {getWindow} from '../execution-context/getWindow.ts';\nimport {isNode} from './isNode.ts';\n\nexport function isElement(target: EventTarget | null): target is Element {\n  if (!target) return false;\n\n  return (\n    target instanceof getWindow(target).Element ||\n    (isNode(target) && target.nodeType === Node.ELEMENT_NODE)\n  );\n}\n", "import {getWindow} from '../execution-context/getWindow.ts';\n\nexport function isKeyboardEvent(\n  event: Event | null | undefined\n): event is KeyboardEvent {\n  if (!event) return false;\n\n  const {KeyboardEvent} = getWindow(event.target);\n\n  return event instanceof KeyboardEvent;\n}\n", "import {getWindow} from '../execution-context/getWindow.ts';\n\nexport function isPointerEvent(\n  event: Event | null | undefined\n): event is PointerEvent {\n  if (!event) return false;\n\n  const {PointerEvent} = getWindow(event.target);\n\n  return event instanceof PointerEvent;\n}\n", "interface ViewTransition {\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  finished: Promise<void>;\n  skipTransition(): void;\n}\n\nexport function supportsViewTransition(\n  document: Document\n): document is Document & {\n  startViewTransition(updateCallback: () => void): ViewTransition;\n} {\n  return 'startViewTransition' in document;\n}\n", "import {isElement} from './isElement.js';\n\nexport function isTextInput(target: EventTarget | null) {\n  if (!isElement(target)) return false;\n\n  const {tagName} = target;\n\n  return (\n    tagName === 'INPUT' || tagName === 'TEXTAREA' || isContentEditable(target)\n  );\n}\n\nfunction isContentEditable(element: Element) {\n  return (\n    element.hasAttribute('contenteditable') &&\n    element.getAttribute('contenteditable') !== 'false'\n  );\n}\n", "const ids: Record<string, number> = {};\n\nexport function generateUniqueId(prefix: string) {\n  const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n  ids[prefix] = id;\n\n  return `${prefix}-${id}`;\n}\n"], "mappings": ";;;;;;;;;;;AAAO,SAASA,iBACdC,MAC0B;EACtB,KAACA,MAAA,EAAe;EAEhB,IAAAA,MAAA,YAAkBC,cAAA,EAAuB;EAE7C,OAAO,cAAkB,IAAAD,MAAA,IAAU,OAAOA,MAAA,CAAOE,YAAiB;AACpE;;;ACNO,SAASC,iBACdC,OAAA,EACAC,KAC8B;EACxB,MAAAC,WAAA,GAAaF,OAAA,CAAQG,aAAc;EAErC,IAAAD,WAAA,CAAWE,MAAA,GAAS,CAAG;IACzB,WAAWC,SAAA,IAAaH,WAAY;MAC9B,IAAAG,SAAA,CAAUC,SAAA,KAAc,SAAW;MACjC;QAACV;MAAA,CAAU,GAAAS,SAAA;MACjB,MAAME,SAAA,GAAYZ,gBAAiB,CAAAC,MAAM,IAAIA,MAAO,CAAAE,YAAA,KAAiB,EAAC;MAChE,MAAAU,gBAAA,GAAmBD,SAAU,CAAAE,MAAA,CAAOR,KAAK;MAE3C,IAAAO,gBAAA,CAAiBJ,MAAA,GAAS,CAAG;QAC/B,OAAO,CAACI,gBAAiB,CAAAA,gBAAA,CAAiBJ,MAAS,IAAC,GAAGC,SAAS;MAAA;IAClE;EACF;EAGK;AACT;;;ACpBO,SAASK,qBAAqBV,OAAqC;EAClE;IAACW,KAAA;IAAOC,MAAQ;IAAAC,GAAA;IAAKC,IAAA;IAAMC,MAAQ;IAAAC;EAAA,CACvC,GAAAhB,OAAA,CAAQiB,qBAAsB;EAEhC,OAAO;IAACN,KAAO;IAAAC,MAAA;IAAQC,GAAK;IAAAC,IAAA;IAAMC,MAAA;IAAQC;EAAK;AACjD;;;ACNa,IAAAE,SAAA,GACX,OAAOC,MAAA,KAAW,WAClB,WAAOA,MAAO,CAAAC,QAAA,KAAa,WAC3B,WAAOD,MAAO,CAAAC,QAAA,CAASC,aAAkB;;;ACJpC,SAASC,SAAStB,OAA2C;EAClE,MAAMuB,aAAgB,GAAAC,MAAA,CAAOC,SAAU,CAAAC,QAAA,CAASC,IAAA,CAAK3B,OAAO;EAC5D,OACEuB,aAAkB;EAAA;EAElBA,aAAkB;AAEtB;;;ACPO,SAASK,OAAOC,IAA4B;EACjD,OAAO,UAAc,IAAAA,IAAA;AACvB;;;ACCO,SAASC,UACdC,MACe;EALjB,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAME,IAAI,CAACH,MAAQ;IACJ,OAAAZ,MAAA;EAAA;EAGL,IAAAG,QAAA,CAASS,MAAM,CAAG;IACb,OAAAA,MAAA;EAAA;EAGL,KAACH,MAAO,CAAAG,MAAM,CAAG;IACZ,OAAAZ,MAAA;EAAA;EAGT,IAAI,iBAAiBY,MAAQ;IACnB,QAAAC,EAAA,GAAAD,MAAA,CAAOI,WAAA,KAAP,IAA+C,GAAAH,EAAA,GAAAb,MAAA;EAAA;EAGzD,QAAOe,EAAO,IAAAD,EAAA,GAAAF,MAAA,CAAAK,aAAA,KAAP,IAAsB,YAAAH,EAAA,CAAAE,WAAA,KAAtB,IAAqC,GAAAD,EAAA,GAAAf,MAAA;AAC9C;;;ACrBO,SAASkB,WAAWR,IAA8B;EACvD,MAAM;IAACS;EAAA,CAAY,GAAAR,SAAA,CAAUD,IAAI;EAEjC,OACEA,IAAA,YAAgBS,QACf,kBAAcT,IAAQ,IAAAA,IAAA,CAAKU,QAAA,KAAaC,IAAK,CAAAC,aAAA;AAElD;;;ACLO,SAASC,cACdb,IACqB;EACrB,IAAI,CAACA,IAAA,IAAQP,QAAS,CAAAO,IAAI,GAAU;EAEpC,OACEA,IAAgB,YAAAC,SAAA,CAAUD,IAAI,EAAEc,WAAA,IAC/B,cAAkB,IAAAd,IAAA,IACjB,OAAOA,IAAA,CAAKe,YAAiB,iBAC7Bf,IAAK,CAAAe,YAAA,CAAaC,QAAA,CAAS,MAAM;AAEvC;;;ACbO,SAASC,aAAajB,IAAgC;EAC3D,OACEA,IAAgB,YAAAC,SAAA,CAAUD,IAAI,EAAEkB,UAAA,IAC/B,cAAkB,IAAAlB,IAAA,IACjB,OAAOA,IAAA,CAAKe,YAAiB,iBAC7Bf,IAAK,CAAAe,YAAA,CAAaC,QAAA,CAAS,KAAK;AAEtC;;;ACHO,SAASG,YAAYjB,MAA+C;EACzE,IAAI,CAACA,MAAQ;IACJ,OAAAX,QAAA;EAAA;EAGL,IAAAE,QAAA,CAASS,MAAM,CAAG;IACpB,OAAOA,MAAO,CAAAX,QAAA;EAAA;EAGZ,KAACQ,MAAO,CAAAG,MAAM,CAAG;IACZ,OAAAX,QAAA;EAAA;EAGL,IAAAiB,UAAA,CAAWN,MAAM,CAAG;IACf,OAAAA,MAAA;EAAA;EAGT,IAAIW,aAAc,CAAAX,MAAM,CAAK,IAAAe,YAAA,CAAaf,MAAM,CAAG;IACjD,OAAOA,MAAO,CAAAK,aAAA;EAAA;EAGT,OAAAhB,QAAA;AACT;;;ACnBO,SAAS6B,6BACdjD,OACmB;EACnB,MAAM;IAACkD;EAAA,CAAmB,GAAAF,WAAA,CAAYhD,OAAO;EAC7C,MAAMW,KAAA,GAAQuC,eAAgB,CAAAC,WAAA;EAC9B,MAAMvC,MAAA,GAASsC,eAAgB,CAAAE,YAAA;EAExB;IACLvC,GAAK;IACLC,IAAM;IACNE,KAAO,EAAAL,KAAA;IACPI,MAAQ,EAAAH,MAAA;IACRD,KAAA;IACAC;EAAA,CACF;AACF;;;AClBO,SAASyC,kBACdrD,OAAA,EACAsD,KACA;EACA,IAAIC,gBAAiB,CAAAvD,OAAO,CAAK,IAAAA,OAAA,CAAQwD,IAAA,KAAS,KAAO;IAChD;EAAA;EAGT,MAAM;IAACC,QAAU;IAAAC,SAAA;IAAWC;EAAA,CAAa,GAASC,gBAAA,CAAiB5D,OAAO;EAE1E,OACEyD,QAAa,kBAAaC,SAAc,kBAAaC,SAAc;AAEvE;AAEA,SAASJ,iBAAiBvD,OAAiD;EACzE,OAAOA,OAAA,CAAQ6D,OAAY;AAC7B;;;ACbO,SAASC,4BACd9D,OACA,EAEmB;EAAA,IAFnB+D,kBAAA,GAAAC,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAqBhE,OAAA,CAAQiB,qBAAsB;EAAA,IACnDiD,MAAA,GAAAF,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAS,CACU;EAdrB,IAAAhC,EAAA;EAgBE,IAAImC,IAA0B,GAAAJ,kBAAA;EACxB;IAAC3B;EAAA,CAAiB,GAAApC,OAAA;EAClB,MAAAoE,WAAA,IAAcpC,EAAc,GAAAI,aAAA,CAAAD,WAAA,KAAd,IAA6B,GAAAH,EAAA,GAAAb,MAAA;EAGjD,IAAIkD,QAAA,GAA+BrE,OAAQ,CAAAsE,aAAA;EAEpC,OAAAD,QAAA,IAAYA,QAAa,KAAAjC,aAAA,CAAcc,eAAiB;IACzD,KAACG,iBAAkB,CAAAgB,QAAQ,CAAG;MAC1B,MAAAE,YAAA,GAAeF,QAAA,CAASpD,qBAAsB;MAEpD,MAAMuD,SAAY,GAAAN,MAAA,IAAUK,YAAa,CAAAxD,MAAA,GAASwD,YAAa,CAAA1D,GAAA;MAC/D,MAAM4D,WAAc,GAAAP,MAAA,IAAUK,YAAa,CAAAvD,KAAA,GAAQuD,YAAa,CAAAzD,IAAA;MAChE,MAAM4D,YAAe,GAAAR,MAAA,IAAUK,YAAa,CAAAxD,MAAA,GAASwD,YAAa,CAAA1D,GAAA;MAClE,MAAM8D,UAAa,GAAAT,MAAA,IAAUK,YAAa,CAAAvD,KAAA,GAAQuD,YAAa,CAAAzD,IAAA;MAGxDqD,IAAA;QACLtD,GAAA,EAAK+D,IAAK,CAAAC,GAAA,CAAIV,IAAA,CAAKtD,GAAK,EAAA0D,YAAA,CAAa1D,GAAA,GAAM2D,SAAS;QACpDxD,KAAA,EAAO4D,IAAK,CAAAE,GAAA,CAAIX,IAAA,CAAKnD,KAAO,EAAAuD,YAAA,CAAavD,KAAA,GAAQyD,WAAW;QAC5D1D,MAAA,EAAQ6D,IAAK,CAAAE,GAAA,CAAIX,IAAA,CAAKpD,MAAQ,EAAAwD,YAAA,CAAaxD,MAAA,GAAS2D,YAAY;QAChE5D,IAAA,EAAM8D,IAAK,CAAAC,GAAA,CAAIV,IAAA,CAAKrD,IAAM,EAAAyD,YAAA,CAAazD,IAAA,GAAO6D,UAAU;QACxDhE,KAAO;QAAA;QACPC,MAAQ;QAAA;MAAA,CACV;MAGKuD,IAAA,CAAAxD,KAAA,GAAQwD,IAAK,CAAAnD,KAAA,GAAQmD,IAAK,CAAArD,IAAA;MAC1BqD,IAAA,CAAAvD,MAAA,GAASuD,IAAK,CAAApD,MAAA,GAASoD,IAAK,CAAAtD,GAAA;IAAA;IAInCwD,QAAA,GAAWA,QAAS,CAAAC,aAAA;EAAA;EAItB,MAAMS,aAAA,GAAgBX,WAAY,CAAAY,UAAA;EAClC,MAAMC,cAAA,GAAiBb,WAAY,CAAAc,WAAA;EACnC,MAAMC,eAAA,GAAkBjB,MAAS,GAAAe,cAAA;EACjC,MAAMG,eAAA,GAAkBlB,MAAS,GAAAa,aAAA;EAE1BZ,IAAA;IACLtD,GAAA,EAAK+D,IAAK,CAAAC,GAAA,CAAIV,IAAK,CAAAtD,GAAA,EAAK,IAAIsE,eAAe;IAC3CnE,KAAA,EAAO4D,IAAK,CAAAE,GAAA,CAAIX,IAAK,CAAAnD,KAAA,EAAO+D,aAAA,GAAgBK,eAAe;IAC3DrE,MAAA,EAAQ6D,IAAK,CAAAE,GAAA,CAAIX,IAAK,CAAApD,MAAA,EAAQkE,cAAA,GAAiBE,eAAe;IAC9DrE,IAAA,EAAM8D,IAAK,CAAAC,GAAA,CAAIV,IAAK,CAAArD,IAAA,EAAM,IAAIsE,eAAe;IAC7CzE,KAAO;IAAA;IACPC,MAAQ;IAAA;EAAA,CACV;EAGKuD,IAAA,CAAAxD,KAAA,GAAQwD,IAAK,CAAAnD,KAAA,GAAQmD,IAAK,CAAArD,IAAA;EAC1BqD,IAAA,CAAAvD,MAAA,GAASuD,IAAK,CAAApD,MAAA,GAASoD,IAAK,CAAAtD,GAAA;EAE7B,IAAAsD,IAAA,CAAKxD,KAAA,GAAQ,CAAG;IAClBwD,IAAA,CAAKxD,KAAQ;EAAA;EAGX,IAAAwD,IAAA,CAAKvD,MAAA,GAAS,CAAG;IACnBuD,IAAA,CAAKvD,MAAS;EAAA;EAGT,OAAAuD,IAAA;AACT;;;AC/EO,SAASkB,QAAWA,CAAA;EAClB,wCAAiCC,IAAK,CAAAC,SAAA,CAAUC,SAAS;AAClE;;;ACFO,SAASC,aAAazF,OAA2B;EACtD,MAAM0F,QAAW;EACX,MAAAC,aAAA,GAAgB3F,OAAQ,CAAA4F,SAAA,CAAU,IAAI;EAC5C,MAAMC,MAAA,GAASC,KAAM,CAAAC,IAAA,CAAK/F,OAAQ,CAAAgG,gBAAA,CAAiBN,QAAQ,CAAC;EAC5D,MAAMO,YAAA,GAAeH,KAAM,CAAAC,IAAA,CAAKJ,aAAc,CAAAK,gBAAA,CAAiBN,QAAQ,CAAC;EAE3DO,YAAA,CAAAC,OAAA,CAAQ,CAACC,KAAA,EAAOC,KAAU;IAC/B,MAAAC,aAAA,GAAgBR,MAAA,CAAOO,KAAK;IAElC,IAAIE,OAAQ,CAAAH,KAAK,CAAK,IAAAG,OAAA,CAAQD,aAAa,CAAG;MACxC,IAAAF,KAAA,CAAMI,IAAA,KAAS,MAAQ;QACzBJ,KAAA,CAAMK,KAAA,GAAQH,aAAc,CAAAG,KAAA;MAAA;MAK9B,IAAIL,KAAM,CAAAI,IAAA,KAAS,OAAW,IAAAJ,KAAA,CAAMM,IAAM;QAClCN,KAAA,CAAAM,IAAA,GAAO,WAAWN,KAAA,CAAMM,IAAI;MAAA;IACpC;IAIA,IAAAC,eAAA,CAAgBP,KAAK,KACrBO,eAAgB,CAAAL,aAAa,CAC7B,IAAAA,aAAA,CAAc1F,KAAQ,QACtB0F,aAAc,CAAAzF,MAAA,GAAS,CACvB;MACM,MAAA+F,OAAA,GAAUR,KAAM,CAAAS,UAAA,CAAW,IAAI;MAC5BD,OAAA,oBAAAA,OAAA,CAAAE,SAAA,CAAUR,aAAA,EAAe,CAAG;IAAA;EACvC,CACD;EAEM,OAAAV,aAAA;AACT;AAEA,SAASW,QACPtG,OACuE;EACvE,OAAO,OAAW,IAAAA,OAAA;AACpB;AAEA,SAAS0G,gBAAgB1G,OAAgD;EACvE,OAAOA,OAAA,CAAQ6D,OAAY;AAC7B;;;ACzCO,SAASiD,mBACdA,CAAAC,SAAA,EAAAC,IAAA,EAEgB;EAAA,IADhB;IAACC,CAAA;IAAGC;EAAA,CACY,GAAAF,IAAA;EAChB,MAAMhH,OAAU,GAAA+G,SAAA,CAASI,gBAAiB,CAAAF,CAAA,EAAGC,CAAC;EAE1C,IAAAE,eAAA,CAAgBpH,OAAO,CAAG;IACtB;MAACqH;IAAA,CAAmB,GAAArH,OAAA;IAE1B,IAAIqH,eAAiB;MACnB,MAAM;QAACvG,IAAA;QAAMD;MAAG,IAAIb,OAAA,CAAQiB,qBAAsB;MAElD,OAAO6F,mBAAA,CAAoBO,eAAiB;QAC1CJ,CAAA,EAAGA,CAAI,GAAAnG,IAAA;QACPoG,CAAA,EAAGA,CAAI,GAAArG;MAAA,CACR;IAAA;EACH;EAGK,OAAAb,OAAA;AACT;AAEA,SAASoH,gBACPpH,OAC8B;EAC9B,QAAOA,OAAA,oBAAAA,OAAA,CAAS6D,OAAY;AAC9B;;;AC5Ba,IAAAyD,eAAA,sBAAsBC,OAA0B;;;ACUtD,IAAMC,SAAA,GAAN,MAAgB;EAGrBC,WAAcA,CAAA;IAFN,KAAAC,OAAA,sBAAuCC,GAAI;IAwBnD,KAAOC,KAAA,GAAQ,MAAM;MACR,WAAAC,KAAA,IAAS,KAAKH,OAAS;QAChC,MAAM,CAAC3F,MAAQ;UAACwE,IAAA;UAAMuB,QAAU;UAAAC;QAAA,CAAQ,CAAI,GAAAF,KAAA;QAErC9F,MAAA,CAAAiG,mBAAA,CAAoBzB,IAAM,EAAAuB,QAAA,EAAUC,OAAO;MAAA;MAGpD,KAAKL,OAAA,CAAQE,KAAM;IAAA,CACrB;EAAA;EA5BOK,KAAKlG,MAAA,EAAqBmG,KAA2B;IAC1D,MAAMC,SAAA,GAAYrC,KAAM,CAAAsC,OAAA,CAAQF,KAAK,CAAI,GAAAA,KAAA,GAAQ,CAACA,KAAK;IACvD,MAAMR,OAAA,GAAgC,EAAC;IAEvC,WAAWW,UAAA,IAAcF,SAAW;MAClC,MAAM;QAAC5B,IAAA;QAAMuB,QAAU;QAAAC;MAAA,CAAW,GAAAM,UAAA;MAC5B,MAAAR,KAAA,GAA4B,CAAC9F,MAAA,EAAQsG,UAAU;MAE9CtG,MAAA,CAAAuG,gBAAA,CAAiB/B,IAAM,EAAAuB,QAAA,EAAUC,OAAO;MAC1C,KAAAL,OAAA,CAAQa,GAAA,CAAIV,KAAK;MACtBH,OAAA,CAAQc,IAAA,CAAKX,KAAK;IAAA;IAGpB,OAAO,SAASY,OAAUA,CAAA;MACb,YAACC,OAAA,EAAQ;QAACnC,IAAA;QAAMuB,QAAA;QAAUC;MAAO,CAAC,KAAKL,OAAS;QACzDgB,OAAO,CAAAV,mBAAA,CAAoBzB,IAAM,EAAAuB,QAAA,EAAUC,OAAO;MAAA;IACpD,CACF;EAAA;AAYJ;;;AC5CO,SAASY,gBAAgBC,EAAyB;EACjD,MAAAC,SAAA,GAAYD,EAAA,oBAAAA,EAAA,CAAIxG,aAAc,CAAAD,WAAA;EAEpC,IAAI0G,SAAa,IAAAA,SAAA,CAAUC,IAAS,KAAAD,SAAA,CAAUE,MAAQ;IACpD,OAAOF,SAAU,CAAAG,YAAA;EAAA;AAErB;;;ACJO,SAASC,iBAAiBL,EAAyB;EAClD,MAAAM,MAAA,sBAAavB,GAAa;EAC5B,IAAAwB,KAAA,GAAQR,eAAA,CAAgBC,EAAE;EAE9B,OAAOO,KAAO;IACZD,MAAA,CAAOX,GAAA,CAAIY,KAAK;IAChBA,KAAA,GAAQR,eAAA,CAAgBQ,KAAK;EAAA;EAGxB,OAAAD,MAAA;AACT;;;ACZO,SAASE,QAAQC,QAAA,EAAsBC,QAA8B;EACpE,MAAAC,EAAA,GAAKC,UAAW,CAAAH,QAAA,EAAUC,QAAQ;EAEjC,aAAMG,YAAA,CAAaF,EAAE;AAC9B;;;ACFO,SAASG,SACdC,IAAA,EACAC,KACkC;EAC5B,MAAAC,IAAA,GAAOA,CAAA,KAAMC,WAAA,CAAYC,GAAI;EAC/B,IAAAC,MAAA;EACA,IAAAC,OAAA;EAEJ,OAAO,YAA6C;IAAA,SAAAC,IAAA,GAAAlG,SAAA,CAAA5D,MAAA,EAArB+J,IAAqB,OAAArE,KAAA,CAAAoE,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAArBD,IAAqB,CAAAC,IAAA,IAAApG,SAAA,CAAAoG,IAAA;IAAA;IAClD,MAAMC,OAAU;IAChB,IAAI,CAACJ,OAAS;MACPN,IAAA,CAAAW,KAAA,CAAMD,OAAA,EAASF,IAAI;MACxBF,OAAA,GAAUJ,IAAK;IAAA,CACV;MACLG,MAAA,oBAAAA,MAAA;MACSA,MAAA,GAAAZ,OAAA,CACP,MAAM;QACCO,IAAA,CAAAW,KAAA,CAAMD,OAAA,EAASF,IAAI;QACxBF,OAAA,GAAUJ,IAAK;MAAA,CACjB,EACAD,KAAA,IAASC,IAAA,EAAS,GAAAI,OAAA,EACpB;IAAA;EACF,CACF;AACF;;;ACxBO,SAASM,YACdC,CAAA,EACAC,CACA;EACI,IAAAD,CAAA,KAAMC,CAAA,EAAU;EACpB,IAAI,CAACD,CAAA,IAAK,CAACC,CAAA,EAAU;EAErB,OACED,CAAE,CAAA3J,GAAA,IAAO4J,CAAE,CAAA5J,GAAA,IACX2J,CAAA,CAAE1J,IAAQ,IAAA2J,CAAA,CAAE3J,IACZ,IAAA0J,CAAA,CAAExJ,KAAS,IAAAyJ,CAAA,CAAEzJ,KACb,IAAAwJ,CAAA,CAAEzJ,MAAA,IAAU0J,CAAE,CAAA1J,MAAA;AAElB;;;ACbO,SAAS2J,SACdA,CAAA1K,OAAA,EAES;EAAA,IADT+D,kBAAqB,GAAAC,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAAhE,OAAA,CAAQiB,qBAAA,EACpB;EACH;IAACN,KAAO;IAAAC;EAAA,CAAU,GAAAkD,2BAAA,CACtB9D,OAAA,EACA+D,kBAAA,CACF;EAEO,OAAApD,KAAA,GAAQ,KAAKC,MAAS;AAC/B;;;ACVA,IAAM+J,QAAW,GAAAzJ,SAAA,GACb0J,cACA,SAAMC,kBAA6C;EACjDC,OAAUA,CAAA;EACVC,SAAYA,CAAA;EACZC,UAAaA,CAAA;AACf;AARJ,IAAAC,YAAA;AAUa,IAAAC,cAAA,GAAN,cAA6BP,QAAS;EAG3ClD,YAAY4B,QAAkC;IAC5C,MAAO3B,OAAY;MACb,KAACyD,YAAA,OAAKF,YAAc;QACtBG,YAAA,OAAKH,YAAe;QACpB;MAAA;MAEF5B,QAAA,CAAS3B,OAAA,EAAS,IAAI;IAAA,CACvB;IATY2D,YAAA,OAAAJ,YAAA;EAAA;AAWjB;AAXEA,YAAA,OAAA1D,OAAA;;;ACGF,IAAM+D,SAAA,GAAYxF,KAAM,CAAAC,IAAA,CAAK;EAAC3F,MAAA,EAAQ;AAAG,GAAG,CAACmL,CAAA,EAAGnF,KAAU,KAAAA,KAAA,GAAQ,GAAG;AAC9D,IAAMoF,iBAAoB;AAfjC,IAAAC,QAAA,EAAAC,2BAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,mBAAA,EAAAC,MAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,2BAAA,EAAAC,SAAA,EAAAC,cAAA;AAiBO,IAAMC,gBAAA,GAAN,MAAuB;EAC5B3E,YACSzH,OACA,EAAAqJ,QAAA,EAKP;IAAA,IAJAtB,OAAoD,GAAA/D,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA;MAClDqI,KAAO;MACPC,WAAa;IAAA,CAEf;IANO,KAAAtM,OAAA,GAAAA,OAAA;IACA,KAAAqJ,QAAA,GAAAA,QAAA;IAHJgC,YAAA,OAAAY,2BAAA;IA0EL,KAAOjB,UAAA,GAAa,MAAM;MA3F5B,IAAAhJ,EAAA,EAAAC,EAAA,EAAAC,EAAA;MA4FIkJ,YAAA,OAAKW,aAAgB;MACrB,CAAA/J,EAAA,GAAAmJ,YAAA,OAAKQ,eAAA,MAAL,IAAsB,YAAA3J,EAAA,CAAAgJ,UAAA;MACtB,CAAA/I,EAAA,GAAAkJ,YAAA,OAAKS,iBAAA,MAAL,IAAwB,YAAA3J,EAAA,CAAA+I,UAAA;MACxBG,YAAA,OAAKU,mBAAA,EAAoBb,UAAW;MACpC,CAAA9I,EAAA,GAAAiJ,YAAA,OAAKW,MAAA,MAAL,IAAa,YAAA5J,EAAA,CAAAqK,MAAA;IAAA,CACf;IAEWlB,YAAA,OAAAI,QAAA;IACXJ,YAAA,OAAAK,2BAAA;IACAL,YAAA,OAAAM,eAAA;IACAN,YAAA,OAAAO,iBAAA;IACAP,YAAA,OAAAQ,mBAAA;IACAR,YAAA,OAAAS,MAAA;IACgBT,YAAA,OAAAU,aAAA;IAEhBV,YAAA,OAAAW,gBAAA,EAAmBtC,QAAA,CAAS,MAAM;MA3GpC,IAAA1H,EAAA,EAAAC,EAAA,EAAAC,EAAA;MA4GU;QAAClC;MAAA,CAAW;MAElB,CAAAgC,EAAA,GAAAmJ,YAAA,OAAKS,iBAAA,MAAL,IAAwB,YAAA5J,EAAA,CAAAgJ,UAAA;MAExB,IAAIG,YAAA,OAAKY,aAAiB,MAACZ,YAAA,OAAKM,QAAY,MAACzL,OAAA,CAAQwM,WAAa;QAChE;MAAA;MAGI,MAAAC,IAAA,IAAOxK,EAAQ,GAAAjC,OAAA,CAAAoC,aAAA,KAAR,IAAyB,GAAAH,EAAA,GAAAb,QAAA;MACtC,MAAM;QAAC8D,WAAa;QAAAF;MAAA,CAAc,IAAA9C,EAAA,GAAAuK,IAAA,CAAKtK,WAAA,KAAL,IAAoB,GAAAD,EAAA,GAAAf,MAAA;MAChD,MAAAuL,UAAA,GAAa1M,OAAA,CAAQiB,qBAAsB;MAC3C,MAAA0L,WAAA,GAAc7I,2BAA4B,CAAA9D,OAAA,EAAS0M,UAAU;MACnE,MAAM;QAAC7L,GAAA;QAAKC,IAAM;QAAAC,MAAA;QAAQC;MAAA,CAAS,GAAA2L,WAAA;MACnC,MAAMC,QAAW,IAAChI,IAAK,CAAAiI,KAAA,CAAMhM,GAAG;MAChC,MAAMiM,SAAY,IAAClI,IAAK,CAAAiI,KAAA,CAAM/L,IAAI;MAClC,MAAMiM,UAAa,IAACnI,IAAK,CAAAiI,KAAA,CAAM7H,UAAA,GAAahE,KAAK;MACjD,MAAMgM,WAAc,IAACpI,IAAK,CAAAiI,KAAA,CAAM3H,WAAA,GAAcnE,MAAM;MAC9C,MAAAkM,UAAA,GAAa,GAAGL,QAAQ,MAAMG,UAAU,MAAMC,WAAW,MAAMF,SAAS;MAE9E,KAAK/I,kBAAqB,GAAA2I,UAAA;MAC1BtB,YAAA,OAAKQ,iBAAA,EAAoB,IAAIsB,oBAAA,CAC1BxF,OAAyC;QAClC,OAACG,KAAK,CAAI,GAAAH,OAAA;QACV;UAACyF;QAAA,CAAoB,GAAAtF,KAAA;QAO3B,MAAMuF,iBAAA,GACJvF,KAAM,CAAAuF,iBAAA,KAAsB,CACxB,GAAAvF,KAAA,CAAMuF,iBAAA,GACNC,SAAU,CAAAD,iBAAA,CACRD,gBAAA,EACArJ,2BAAA,CAA4B9D,OAAO,EACrC;QAEN,IAAIoN,iBAAA,KAAsB,CAAG;UAC3BjC,YAAA,OAAKa,gBAAL,EAAArK,IAAA;QAAA;MACF,CACF,EACA;QACE2J,SAAA;QACA2B,UAAA;QACAR;MAAA,CACF,CACF;MAEKtB,YAAA,OAAAS,iBAAA,EAAkBd,OAAA,CAAQ9K,OAAO;MACtCsN,eAAA,OAAKrB,2BAAL,EAAAC,SAAA,EAAAvK,IAAA;IAAA,GACC6J,iBAAiB;IArIb,KAAAzH,kBAAA,GAAqB/D,OAAA,CAAQiB,qBAAsB;IACxDmK,YAAA,OAAKK,QAAW,EAAAf,SAAA,CAAU1K,OAAS,OAAK+D,kBAAkB;IAE1D,IAAIwJ,OAAU;IACT,KAAAlE,QAAA,GAAYtF,kBAAuB;MACtC,IAAIwJ,OAAS;QACDA,OAAA;QACV,IAAIxF,OAAA,CAAQuE,WAAa;MAAA;MAG3BjD,QAAA,CAAStF,kBAAkB;IAAA,CAC7B;IAEA,MAAM0I,IAAA,GAAOzM,OAAQ,CAAAoC,aAAA;IAErB,IAAI2F,OAAA,oBAAAA,OAAA,CAASsE,KAAO;MACbjB,YAAA,OAAAU,MAAA,EAAS1K,QAAS,CAAAC,aAAA,CAAc,KAAK;MACrC8J,YAAA,OAAAW,MAAA,EAAOxI,KAAA,CAAMkK,UAAa;MAC1BrC,YAAA,OAAAW,MAAA,EAAOxI,KAAA,CAAMmK,QAAW;MACxBtC,YAAA,OAAAW,MAAA,EAAOxI,KAAA,CAAMoK,aAAgB;MAC7BjB,IAAA,CAAAkB,IAAA,CAAKC,WAAY,CAAAzC,YAAA,OAAKW,MAAM;IAAA;IAGnCV,YAAA,OAAKS,mBAAA,EAAsB,IAAIqB,oBAAA,CAC5BxF,OAAyC;MAlDhD,IAAA1F,EAAA,EAAAC,EAAA;MAmDQ,MAAM4F,KAAQ,GAAAH,OAAA,CAAQA,OAAQ,CAAAtH,MAAA,GAAS,CAAC;MACxC,MAAM;QAAC2D,kBAAA;QAAoB8J,cAAgB,EAAAC;MAAA,CAAW,GAAAjG,KAAA;MAChD;QAAClH,KAAO;QAAAC;MAAA,CAAU,GAAAmD,kBAAA;MACxB,MAAMgK,eAAA,GAAkB5C,YAAK,OAAAM,QAAA;MAE7BL,YAAA,OAAKK,QAAW,EAAAqC,OAAA;MAEZ,KAACnN,KAAS,KAACC,MAAQ;MAEnB,IAAAmN,eAAA,IAAmB,CAACD,OAAS;QAC/B,CAAA9L,EAAA,GAAAmJ,YAAA,OAAKS,iBAAA,MAAL,IAAwB,YAAA5J,EAAA,CAAAgJ,UAAA;QACxB,KAAK3B,QAAA,CAAS,IAAI;QAClB,CAAApH,EAAA,GAAAkJ,YAAA,OAAKQ,eAAA,MAAL,IAAsB,YAAA1J,EAAA,CAAA+I,UAAA;QACtBI,YAAA,OAAKO,eAAkB;QAEvB,IAAIR,YAAK,OAAAW,MAAA,GAAaX,YAAA,OAAAW,MAAA,EAAOxI,KAAA,CAAM0K,UAAa;MAAA,CAC3C;QACL7C,YAAA,OAAKa,gBAAL,EAAArK,IAAA;MAAA;MAGE,IAAAmM,OAAA,IAAW,CAAC3C,YAAA,OAAKQ,eAAiB;QACpCP,YAAA,OAAKO,eAAkB,MAAIT,cAAe,CAAAC,YAAA,OAAKa,gBAAgB;QAC1Db,YAAA,OAAAQ,eAAA,EAAgBb,OAAA,CAAQ9K,OAAO;MAAA;IACtC,CACF,EACA;MACEsL,SAAA;MACAmB;IAAA,CACF,CACF;IAEA,IAAItB,YAAK,OAAAM,QAAA,KAAY,CAAC1D,OAAA,CAAQuE,WAAa;MACpC,KAAAjD,QAAA,CAAS,KAAKtF,kBAAkB;IAAA;IAGlCoH,YAAA,OAAAU,mBAAA,EAAoBf,OAAA,CAAQ9K,OAAO;EAAA;AAqG5C;AAxFEyL,QAAA,OAAAlE,OAAA;AACAmE,2BAAA,OAAAnE,OAAA;AACAoE,eAAA,OAAApE,OAAA;AACAqE,iBAAA,OAAArE,OAAA;AACAsE,mBAAA,OAAAtE,OAAA;AACAuE,MAAA,OAAAvE,OAAA;AACAwE,aAAA,OAAAxE,OAAA;AAEAyE,gBAAA,OAAAzE,OAAA;AA1FK0E,2BAAA,OAAAgC,OAAA;AAgJL/B,SAAA,GAAO,SAAAA,CAAA,EAAG;EACR,IAAIf,YAAA,OAAKY,aAAe;EAExBuB,eAAA,OAAKrB,2BAAL,EAAAE,cAAA,EAAAxK,IAAA;EAEA,IAAI4I,WAAY,MAAKxG,kBAAoB,EAAAoH,YAAA,OAAKO,2BAA2B,IACvE;EAEG,KAAArC,QAAA,CAAS,KAAKtF,kBAAkB;EACrCqH,YAAA,OAAKM,2BAAA,EAA8B,IAAK,CAAA3H,kBAAA;AAC1C;AAEAoI,cAAA,GAAY,SAAAA,CAAA,EAAG;EACb,IAAIhB,YAAA,OAAKW,MAAQ;IACf,MAAM;MAACjL,GAAA;MAAKC,IAAM;MAAAH,KAAA;MAAOC;IAAA,CAAU,GAAAkD,2BAAA,CACjC,IAAK,CAAA9D,OAAA,CACP;IAEKmL,YAAA,OAAAW,MAAA,EAAOxI,KAAA,CAAMG,QAAW;IACxB0H,YAAA,OAAAW,MAAA,EAAOxI,KAAA,CAAM0K,UAAa;IAC/B7C,YAAA,OAAKW,MAAA,EAAOxI,KAAM,CAAAzC,GAAA,GAAM,GAAG+D,IAAK,CAAAiI,KAAA,CAAMhM,GAAG,CAAC;IAC1CsK,YAAA,OAAKW,MAAA,EAAOxI,KAAM,CAAAxC,IAAA,GAAO,GAAG8D,IAAK,CAAAiI,KAAA,CAAM/L,IAAI,CAAC;IAC5CqK,YAAA,OAAKW,MAAA,EAAOxI,KAAM,CAAA3C,KAAA,GAAQ,GAAGiE,IAAK,CAAAiI,KAAA,CAAMlM,KAAK,CAAC;IAC9CwK,YAAA,OAAKW,MAAA,EAAOxI,KAAM,CAAA1C,MAAA,GAAS,GAAGgE,IAAK,CAAAiI,KAAA,CAAMjM,MAAM,CAAC;EAAA;AAEpD;;;AClLF,IAAMsN,sBAAA,sBAA6B3G,OAMjC;AAEF,IAAM4G,eAAA,sBAAsB5G,OAM1B;AAEF,SAAS6G,iBAAiBjF,KAAA,EAAgBE,QAAoC;EAExE,IAAAgF,MAAA,GAASH,sBAAuB,CAAAI,GAAA,CAAInF,KAAK;EAE7C,IAAI,CAACkF,MAAQ;IACX,MAAME,QAAA,GAAW,IAAInC,gBAAA,CACnBjD,KAAA,EACCpF,kBAAuB;MAChB,MAAAyK,OAAA,GAASN,sBAAuB,CAAAI,GAAA,CAAInF,KAAK;MAC/C,IAAI,CAACqF,OAAQ;MAEbA,OAAA,CAAOC,SAAU,CAAAvI,OAAA,CAASwI,SAAa,IAAAA,SAAA,CAAS3K,kBAAkB,CAAC;IAAA,CACrE,EACA;MAACuI,WAAA,EAAa;IAAI,EACpB;IAEA+B,MAAA,GAAS;MAACrD,UAAY,EAAAuD,QAAA,CAASvD,UAAA;MAAYyD,SAAW,qBAAI9G,GAAA;IAAK;EAAA;EAG1D0G,MAAA,CAAAI,SAAA,CAAUlG,GAAA,CAAIc,QAAQ;EACN6E,sBAAA,CAAAS,GAAA,CAAIxF,KAAA,EAAOkF,MAAM;EAExC,OAAO,MAAM;IACJA,MAAA,CAAAI,SAAA,CAAUG,MAAA,CAAOvF,QAAQ;IAE5B,IAAAgF,MAAA,CAAOI,SAAU,CAAAI,IAAA,KAAS,CAAG;MAC/BX,sBAAA,CAAuBU,MAAA,CAAOzF,KAAK;MACnCkF,MAAA,CAAOrD,UAAW;IAAA;EACpB,CACF;AACF;AAEA,SAAS8D,oBACP5F,MAAA,EACAG,QACA;EACM,MAAAZ,OAAA,sBAAcd,GAAgB;EAEpC,WAAWwB,KAAA,IAASD,MAAQ;IACpB,MAAAqD,MAAA,GAAS6B,gBAAiB,CAAAjF,KAAA,EAAOE,QAAQ;IAC/CZ,OAAA,CAAQF,GAAA,CAAIgE,MAAM;EAAA;EAGpB,OAAO,MAAM9D,OAAQ,CAAAvC,OAAA,CAASqG,MAAA,IAAWA,MAAA,EAAQ;AACnD;AAEA,SAASwC,kBAAkB/O,OAAA,EAAkBqJ,QAAyB;EAtEtE,IAAArH,EAAA;EAuEE,MAAMgN,GAAA,GAAMhP,OAAQ,CAAAoC,aAAA;EAEpB,IAAI,CAAC+L,eAAA,CAAgBc,GAAI,CAAAD,GAAG,CAAG;IACvB,MAAAE,UAAA,GAAa,IAAIC,eAAgB;IACjC,MAAAC,UAAA,sBAAgBzH,GAAmB;IAEhCvG,QAAA,CAAAkH,gBAAA,CACP,UACC+G,KAAA,IAAUD,UAAU,CAAAlJ,OAAA,CAAS4B,QAAa,IAAAA,QAAA,CAASuH,KAAK,CAAC,GAC1D;MACEC,OAAS;MACTC,OAAS;MACTC,MAAA,EAAQN,UAAW,CAAAM;IAAA,CACrB,CACF;IAEgBrB,eAAA,CAAAQ,GAAA,CAAIK,GAAK;MAAChE,UAAY,EAAAA,CAAA,KAAMkE,UAAA,CAAWO,KAAM;MAAGtH,SAAA,EAAAiH;IAAA,CAAU;EAAA;EAGtE;IAACjH,SAAA;IAAW6C;EAAU,KAAIhJ,EAAA,GAAAmM,eAAA,CAAgBG,GAAI,CAAAU,GAAG,CAAvB,YAAAhN,EAAA,GAA4B,EAAC;EAE7D,IAAI,CAACmG,SAAA,IAAa,CAAC6C,UAAA,SAAmB,MAAM,EAAC;EAE7C7C,SAAA,CAAUI,GAAA,CAAIc,QAAQ;EAEtB,OAAO,MAAM;IACXlB,SAAA,CAAUyG,MAAA,CAAOvF,QAAQ;IAErB,IAAAlB,SAAA,CAAU0G,IAAA,KAAS,CAAG;MACb7D,UAAA;MACXmD,eAAA,CAAgBS,MAAA,CAAOI,GAAG;IAAA;EAC5B,CACF;AACF;AAxGA,IAAAU,gBAAA,EAAAC,cAAA,EAAAC,OAAA,EAAAC,aAAA;AA0GO,IAAMC,aAAA,GAAN,MAAoB;EAKzBrI,YACEzH,OACQ,EAAAqJ,QAAA,EACRtB,OACA;IAFQ,KAAAsB,QAAA,GAAAA,QAAA;IANVgC,YAAA,OAAAqE,gBAAA;IACArE,YAAA,OAAAsE,cAAgB;IAChBtE,YAAA,OAAAuE,OAAA;IAyBgBvE,YAAA,OAAAwE,aAAA,EAAAnG,QAAA,CAAU2F,KAAiB;MACzC,IAAIlE,YAAA,OAAKwE,cAAe;MACpB,KAACN,KAAA,CAAMtN,MAAQ;MACnB,IACE,cAAcsN,KAAM,CAAAtN,MAAA,IACpB,OAAOsN,KAAM,CAAAtN,MAAA,CAAOgO,QAAA,KAAa,UACjC;QACW,WAAA5G,KAAA,IAASgC,YAAA,OAAKyE,OAAS;UAChC,IAAIP,KAAM,CAAAtN,MAAA,CAAOgO,QAAS,CAAA5G,KAAK,CAAG;YAC3B,KAAAE,QAAA,CAAS8B,YAAK,OAAAuE,gBAAA,EAAiB3L,kBAAkB;YACtD;UAAA;QACF;MACF;IACF,GACCyH,iBAAiB;IAhCZ,MAAAtC,MAAA,GAASD,gBAAA,CAAiBjJ,OAAO;IACjC,MAAAgQ,qBAAA,GAAwBlB,mBAAoB,CAAA5F,MAAA,EAAQG,QAAQ;IAClE,MAAM4G,oBAAuB,GAAAlB,iBAAA,CAAkB/O,OAAS,EAAAmL,YAAA,OAAK0E,aAAa;IAE1EzE,YAAA,OAAKwE,OAAU,EAAA1G,MAAA;IACfkC,YAAA,OAAKsE,gBAAmB,MAAItD,gBAAiB,CAAApM,OAAA,EAASqJ,QAAA,EAAUtB,OAAO;IACvE,KAAKiD,UAAA,GAAa,MAAM;MACtB,IAAIG,YAAA,OAAKwE,cAAe;MACxBvE,YAAA,OAAKuE,cAAgB;MAECK,qBAAA;MACDC,oBAAA;MACrB9E,YAAA,OAAKuE,gBAAA,EAAiB1E,UAAW;IAAA,CACnC;EAAA;AAoBJ;AA1CE0E,gBAAA,OAAAnI,OAAA;AACAoI,cAAA,OAAApI,OAAA;AACAqI,OAAA,OAAArI,OAAA;AAyBAsI,aAAA,OAAAtI,OAAA;;;ACtIK,SAAS2I,gBAAgBlQ,OAG9B;EAEE,wBAAiBA,OACjB,qBAAiBA,OACjB,WAAOA,OAAA,CAAQmQ,WAAgB,mBAC/B,OAAOnQ,OAAA,CAAQoQ,WAAgB;AAEnC;;;ACRO,SAASD,YAAYnQ,OAAkB;EACxC;IACF,IACEkQ,eAAA,CAAgBlQ,OAAO,KACvBA,OAAA,CAAQwM,WACR,IAAAxM,OAAA,CAAQqQ,YAAA,CAAa,SAAS;IAAA;IAE9B,CAACrQ,OAAA,CAAQsQ,OAAQ,gBAAe,CAChC;MACAtQ,OAAA,CAAQmQ,WAAY;IAAA;EACtB,SACOI,KAAO;AAGlB;;;ACdO,SAASH,YAAYpQ,OAAkB;EACxC;IACF,IACEkQ,eAAA,CAAgBlQ,OAAO,KACvBA,OAAA,CAAQwM,WACR,IAAAxM,OAAA,CAAQqQ,YAAA,CAAa,SAAS;IAAA;IAE9BrQ,OAAA,CAAQsQ,OAAQ,gBAAe,CAC/B;MACAtQ,OAAA,CAAQoQ,WAAY;IAAA;EACtB,SACOG,KAAO;AAGlB;;;ACbO,SAASC,2BAA2BxQ,OAAyB;EAC9D,KAACkB,SAAa,KAAClB,OAAS;IACnB;EAAA;EAGF,OAAAA,OAAA,KAAYgD,WAAY,CAAAhD,OAAO,CAAE,CAAAyQ,gBAAA;AAC1C;;;ACJO,SAASC,kBAAkBC,iBAA4B;EACtD,MAAAC,OAAA,GAAS9O,SAAA,CAAU6O,iBAAiB;EACpC,MAAAxM,IAAA,GAAOqM,0BAAA,CAA2BG,iBAAiB,IACrD1N,4BAAA,CAA6B0N,iBAAiB,IAC9CjQ,oBAAA,CAAqBiQ,iBAAiB;EAEpC,MAAAE,UAAA,GAAaL,0BAA2B,CAAAG,iBAAiB,CAC3D;IACE/P,MAAA,EAAQgQ,OAAO,CAAA1L,WAAA;IACfvE,KAAA,EAAOiQ,OAAO,CAAA5L;EAAA,CAEhB;IACEpE,MAAA,EAAQ+P,iBAAkB,CAAAvN,YAAA;IAC1BzC,KAAA,EAAOgQ,iBAAkB,CAAAxN;EAAA,CAC3B;EACJ,MAAMsK,QAAW;IACfqD,OAAS;MACP7J,CAAA,EAAG0J,iBAAkB,CAAAI,UAAA;MACrB7J,CAAA,EAAGyJ,iBAAkB,CAAAK;IAAA,CACvB;IACAnM,GAAK;MACHoC,CAAA,EAAG0J,iBAAkB,CAAAM,WAAA,GAAcJ,UAAW,CAAAlQ,KAAA;MAC9CuG,CAAA,EAAGyJ,iBAAkB,CAAAO,YAAA,GAAeL,UAAW,CAAAjQ;IAAA;EACjD,CACF;EAEM,MAAAuQ,KAAA,GAAQ1D,QAAS,CAAAqD,OAAA,CAAQ5J,CAAK;EAC9B,MAAAkK,MAAA,GAAS3D,QAAS,CAAAqD,OAAA,CAAQ7J,CAAK;EACrC,MAAMoK,QAAW,GAAA5D,QAAA,CAASqD,OAAQ,CAAA5J,CAAA,IAAKuG,QAAA,CAAS5I,GAAI,CAAAqC,CAAA;EACpD,MAAMoK,OAAU,GAAA7D,QAAA,CAASqD,OAAQ,CAAA7J,CAAA,IAAKwG,QAAA,CAAS5I,GAAI,CAAAoC,CAAA;EAE5C;IACL9C,IAAA;IACAsJ,QAAA;IACA0D,KAAA;IACAC,MAAA;IACAC,QAAA;IACAC;EAAA,CACF;AACF;;;ACxCO,SAASC,UAAUZ,iBAAA,EAA4Ba,EAAkB;EAChE;IAACL,KAAA;IAAOE,QAAU;IAAAD,MAAA;IAAQE,OAAA;IAAS7D;EAAQ,IAC/CiD,iBAAA,CAAkBC,iBAAiB;EAE/B;IAAC1J,CAAA;IAAGC;EAAC,IAAIsK,EAAA,WAAAA,EAAA,GAAM;IAACvK,CAAA,EAAG,CAAG;IAAAC,CAAA,EAAG;EAAC;EAEhC,MAAMrG,GAAA,GAAM,CAACsQ,KAAA,IAAS1D,QAAS,CAAAqD,OAAA,CAAQ5J,CAAA,GAAIA,CAAI;EACzC,MAAAnG,MAAA,GAAS,CAACsQ,QAAY,IAAA5D,QAAA,CAASqD,OAAA,CAAQ5J,CAAI,GAAAA,CAAA,GAAIuG,QAAA,CAAS5I,GAAI,CAAAqC,CAAA;EAClE,MAAMpG,IAAA,GAAO,CAACsQ,MAAA,IAAU3D,QAAS,CAAAqD,OAAA,CAAQ7J,CAAA,GAAIA,CAAI;EAC3C,MAAAjG,KAAA,GAAQ,CAACsQ,OAAW,IAAA7D,QAAA,CAASqD,OAAA,CAAQ7J,CAAI,GAAAA,CAAA,GAAIwG,QAAA,CAAS5I,GAAI,CAAAoC,CAAA;EAEzD;IACLpG,GAAA;IACAE,MAAA;IACAD,IAAA;IACAE,KAAA;IACAiG,CAAA,EAAGnG,IAAQ,IAAAE,KAAA;IACXkG,CAAA,EAAGrG,GAAO,IAAAE;EAAA,CACZ;AACF;;;ACrBO,IAAM0Q,SAAA,GAAN,MAAuD;EAC5DhK,YAAoBiK,UAAc;IAAd,KAAAC,SAAA,GAAAD,UAAA;IAEpB,KAAQE,OAAmB;IACnB,KAAAC,KAAA,sBAA6BlK,GAAI;IACjC,KAAAmK,SAAA,sBAAiCnK,GAAI;IAa7C,KAAOoK,KAAA,GAAQ,MAAM;MACb;QAACF,KAAO;QAAAC;MAAA,CAAa;MAE3B,KAAKF,OAAU;MACV,KAAAC,KAAA,sBAAYlK,GAAI;MAChB,KAAAmK,SAAA,sBAAgBnK,GAAI;MAEzB,WAAWqK,IAAA,IAAQH,KAAO;QACnBG,IAAA;MAAA;MAGP,WAAWC,OAAA,IAAWH,SAAW;QACvBG,OAAA;MAAA;IACV,CACF;EAAA;EAzBOC,SAASF,IAAiC;IAC1C,KAAAH,KAAA,CAAMtJ,GAAA,CAAIyJ,IAAI;IAEf,KAAC,KAAKJ,OAAS;MACjB,KAAKA,OAAU;MACV,KAAAD,SAAA,CAAU,KAAKI,KAAK;IAAA;IAGpB,WAAII,OAAA,CAAeF,OAAA,IAAY,KAAKH,SAAU,CAAAvJ,GAAA,CAAI0J,OAAO,CAAC;EAAA;AAkBrE;AAEO,IAAMN,SAAY,OAAIF,SAAU,CAACpI,QAAa;EAC/C,WAAO+I,qBAAA,KAA0B,UAAY;IAC/CA,qBAAA,CAAsB/I,QAAQ;EAAA,CACzB;IACIA,QAAA;EAAA;AAEb,CAAC;;;ACxCD,IAAMgJ,UAAA,GAAY,IAAIZ,SAAU,CAACpI,QAAA,IAAaG,UAAW,CAAAH,QAAA,EAAU,EAAE,CAAC;AACtE,IAAMiJ,YAAA,sBAAmBC,GAAkC;AAC3D,IAAM3K,KAAQ,GAAA0K,YAAA,CAAa1K,KAAM,CAAAK,IAAA,CAAKqK,YAAY;AAQ3C,SAASE,kBACdxS,OACA,EACqB;EAAA,IADrBqO,MAAA,GAAArK,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAS,KACY;EACrB,IAAI,CAACqK,MAAA,EAAe,OAAAoE,aAAA,CAAczS,OAAO;EAErC,IAAA0S,MAAA,GAASJ,YAAa,CAAAhE,GAAA,CAAItO,OAAO;EAErC,IAAI0S,MAAA,EAAe,OAAAA,MAAA;EAEnBA,MAAA,GAASD,aAAA,CAAczS,OAAO;EACjBsS,YAAA,CAAA3D,GAAA,CAAI3O,OAAA,EAAS0S,MAAM;EAChCL,UAAA,CAAUH,QAAA,CAAStK,KAAK;EAEjB,OAAA8K,MAAA;AACT;AAEA,SAASD,cAAczS,OAAuC;EAC5D,OAAO8B,SAAU,CAAA9B,OAAO,CAAE,CAAA4D,gBAAA,CAAiB5D,OAAO;AACpD;;;AC9BO,SAAS2S,QACd9Q,IACA,EACS;EAAA,IADT+Q,aAAA,GAAA5O,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAqCwO,iBAAkB,CAAA3Q,IAAA,EAAM,IAAI,CACxD;EACT,OACE+Q,aAAc,CAAAnF,QAAA,KAAa,OAAW,IAAAmF,aAAA,CAAcnF,QAAa;AAErE;;;ACPO,SAASoF,aACd7S,OACA,EACS;EAAA,IADT4S,aAAA,GAAA5O,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAqCwO,iBAAkB,CAAAxS,OAAA,EAAS,IAAI,CAC3D;EACT,MAAM8S,aAAgB;EACtB,MAAMC,UAAa,IAAC,UAAY,eAAa,WAAW;EAEjD,OAAAA,UAAA,CAAWC,IAAK,CAACC,QAAa;IAC7B,MAAAzM,KAAA,GAAQoM,aAAA,CAAcK,QAAqC;IAEjE,OAAO,OAAOzM,KAAU,gBAAWsM,aAAc,CAAAxN,IAAA,CAAKkB,KAAK,CAAI;EAAA,CAChE;AACH;;;ACDA,IAAM0M,cAA0B;EAC9BC,cAAgB;AAClB;AAEO,SAASC,uBACdpT,OACA,EACc;EAAA,IADd+H,OAAA,GAAA/D,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAmBkP,cACL;EACR;IAACtJ,KAAO;IAAAuJ;EAAA,CAAkB,GAAApL,OAAA;EAC1B,MAAAsL,aAAA,sBAAoB1L,GAAa;EAEvC,SAAS2L,wBAAwBzR,IAAiC;IAChE,IAAI+H,KAAS,YAAQyJ,aAAc,CAAAxE,IAAA,IAAQjF,KAAO;MACzC,OAAAyJ,aAAA;IAAA;IAGT,IAAI,CAACxR,IAAM;MACF,OAAAwR,aAAA;IAAA;IAIP,IAAAhR,UAAA,CAAWR,IAAI,KACfA,IAAK,CAAA4O,gBAAA,IAAoB,IACzB,KAAC4C,aAAc,CAAApE,GAAA,CAAIpN,IAAK,CAAA4O,gBAAgB,CACxC;MACc4C,aAAA,CAAA9K,GAAA,CAAI1G,IAAA,CAAK4O,gBAAgB;MAEhC,OAAA4C,aAAA;IAAA;IAGL,KAAC3Q,aAAc,CAAAb,IAAI,CAAG;MACpB,IAAAiB,YAAA,CAAajB,IAAI,CAAG;QACf,OAAAyR,uBAAA,CAAwBzR,IAAA,CAAKyC,aAAa;MAAA;MAG5C,OAAA+O,aAAA;IAAA;IAGL,IAAAA,aAAA,CAAcpE,GAAI,CAAApN,IAAI,CAAG;MACpB,OAAAwR,aAAA;IAAA;IAGH,MAAAT,aAAA,GAAgBJ,iBAAkB,CAAA3Q,IAAA,EAAM,IAAI;IAE9C,IAAAsR,cAAA,IAAkBtR,IAAA,KAAS7B,OAAS,GAE7B,SAAA6S,YAAA,CAAahR,IAAM,EAAA+Q,aAAa,CAAG;MAC5CS,aAAA,CAAc9K,GAAA,CAAI1G,IAAI;IAAA;IAGpB,IAAA8Q,OAAA,CAAQ9Q,IAAM,EAAA+Q,aAAa,CAAG;MAC1B;QAACnC;MAAgB,IAAI5O,IAAK,CAAAO,aAAA;MAE5B,IAAAqO,gBAAA,EAAgC4C,aAAA,CAAA9K,GAAA,CAAIkI,gBAAgB;MAEjD,OAAA4C,aAAA;IAAA;IAGF,OAAAC,uBAAA,CAAwBzR,IAAA,CAAK0R,UAAU;EAAA;EAGhD,IAAI,CAACvT,OAAS;IACL,OAAAqT,aAAA;EAAA;EAGT,OAAOC,uBAAA,CAAwBtT,OAAO;AACxC;AAEO,SAASwT,2BAA2B3R,IAAmC;EACtE,OAAC4R,uBAAuB,CAAI,GAAAL,sBAAA,CAAuBvR,IAAA,EAAM;IAAC+H,KAAA,EAAO;EAAA,CAAE;EAEzE,OAAO6J,uBAA2B,WAAAA,uBAAA;AACpC;;;AC9EO,SAASC,iBACdA,CAAA9K,EAAA,EAEW;EAAA,IADX+K,QAA2B,GAAA3P,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAA7C,MAAA,CAAO6H,YACvB;EACX,MAAM4K,SAAuB;IAC3B3M,CAAG;IACHC,CAAG;IACH2M,MAAQ;IACRC,MAAQ;EAAA,CACV;EAEI,KAAClL,EAAA,EAAW,OAAAgL,SAAA;EAEZ,IAAAzK,KAAA,GAAQR,eAAA,CAAgBC,EAAE;EAE9B,OAAOO,KAAO;IACZ,IAAIA,KAAA,KAAUwK,QAAU;MACf,OAAAC,SAAA;IAAA;IAGH,MAAAzP,IAAA,GAAOzD,oBAAA,CAAqByI,KAAK;IACjC;MAAClC,CAAA,EAAG4M,MAAQ;MAAA3M,CAAA,EAAG4M;IAAA,CAAU,GAAAC,QAAA,CAAS5K,KAAA,EAAOhF,IAAI;IAEzCyP,SAAA,CAAA3M,CAAA,GAAI2M,SAAU,CAAA3M,CAAA,GAAI9C,IAAK,CAAArD,IAAA;IACvB8S,SAAA,CAAA1M,CAAA,GAAI0M,SAAU,CAAA1M,CAAA,GAAI/C,IAAK,CAAAtD,GAAA;IACvB+S,SAAA,CAAAC,MAAA,GAASD,SAAA,CAAUC,MAAS,GAAAA,MAAA;IAC5BD,SAAA,CAAAE,MAAA,GAASF,SAAA,CAAUE,MAAS,GAAAA,MAAA;IAEtC3K,KAAA,GAAQR,eAAA,CAAgBQ,KAAK;EAAA;EAGxB,OAAAyK,SAAA;AACT;AAEA,SAASG,QACPA,CAAA/T,OAAA,EAEA;EAAA,IADAgU,iBAAoB,GAAAhQ,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAAtD,oBAAA,CAAqBV,OAAO,CAChD;EACA,MAAMW,KAAQ,GAAAiE,IAAA,CAAKqP,KAAM,CAAAD,iBAAA,CAAkBrT,KAAK;EAChD,MAAMC,MAAS,GAAAgE,IAAA,CAAKqP,KAAM,CAAAD,iBAAA,CAAkBpT,MAAM;EAE9C,IAAA8B,aAAA,CAAc1C,OAAO,CAAG;IACnB;MACLiH,CAAA,EAAGtG,KAAA,GAAQX,OAAQ,CAAAkU,WAAA;MACnBhN,CAAA,EAAGtG,MAAA,GAASZ,OAAQ,CAAAmU;IAAA,CACtB;EAAA;EAGI,MAAAzB,MAAA,GAASF,iBAAkB,CAAAxS,OAAA,EAAS,IAAI;EAEvC;IACLiH,CAAI,GAAAmN,UAAA,CAAW1B,MAAO,CAAA/R,KAAK,KAAKA,KAAS,IAAAA,KAAA;IACzCuG,CAAI,GAAAkN,UAAA,CAAW1B,MAAO,CAAA9R,MAAM,KAAKA,MAAU,IAAAA;EAAA,CAC7C;AACF;;;AC7DO,SAASyT,WAAWC,KAAe;EACxC,IAAIA,KAAA,KAAU,MAAQ;IACb;EAAA;EAGH,MAAAC,MAAA,GAASD,KAAM,CAAAE,KAAA,CAAM,GAAG;EAC9B,MAAMvN,CAAI,GAAAmN,UAAA,CAAWG,MAAO,EAAC,CAAC;EAC9B,MAAMrN,CAAI,GAAAkN,UAAA,CAAWG,MAAO,EAAC,CAAC;EAE9B,IAAIE,KAAM,CAAAxN,CAAC,CAAK,IAAAwN,KAAA,CAAMvN,CAAC,CAAG;IACjB;EAAA;EAGF;IACLD,CAAG,EAAAwN,KAAA,CAAMxN,CAAC,IAAIC,CAAI,GAAAD,CAAA;IAClBC,CAAG,EAAAuN,KAAA,CAAMvN,CAAC,IAAID,CAAI,GAAAC;EAAA,CACpB;AACF;;;ACjBO,SAASwN,eAAeC,SAAmB;EAChD,IAAIA,SAAA,KAAc,MAAQ;IACjB;EAAA;EAGH,OAAC1N,CAAA,EAAGC,CAAG,EAAA0N,CAAA,GAAI,GAAG,CAAI,GAAAD,SAAA,CAAUH,KAAA,CAAM,GAAG;EAC3C,MAAMK,MAAS;IAAC5N,CAAG,EAAAmN,UAAA,CAAWnN,CAAC,CAAG;IAAAC,CAAA,EAAGkN,UAAW,CAAAlN,CAAC,CAAG;IAAA0N,CAAA,EAAGE,QAAS,CAAAF,CAAA,EAAG,EAAE;EAAC;EAEtE,IAAIH,KAAA,CAAMI,MAAO,CAAA5N,CAAC,KAAKwN,KAAM,CAAAI,MAAA,CAAO3N,CAAC,CAAG;IAC/B;EAAA;EAGF;IACLD,CAAA,EAAGwN,KAAM,CAAAI,MAAA,CAAO5N,CAAC,IAAI,IAAI4N,MAAO,CAAA5N,CAAA;IAChCC,CAAA,EAAGuN,KAAM,CAAAI,MAAA,CAAO3N,CAAC,IAAI,IAAI2N,MAAO,CAAA3N,CAAA;IAChC0N,CAAA,EAAGH,KAAM,CAAAI,MAAA,CAAOD,CAAC,IAAI,IAAIC,MAAO,CAAAD;EAAA,CAClC;AACF;;;ACNO,SAASG,eAAeC,cAIV;EAfrB,IAAAhT,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAA+S,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAgBE,MAAM;IAAChB,KAAA;IAAOV,SAAW;IAAAe;EAAA,CAAa,GAAAK,cAAA;EAChC,MAAAO,WAAA,GAAclB,UAAA,CAAWC,KAAK;EAC9B,MAAAkB,eAAA,GAAkBd,cAAA,CAAeC,SAAS;EAC1C,MAAAc,YAAA,GAAeC,oBAAA,CAAqB9B,SAAS;EAEnD,IAAI,CAAC6B,YAAA,IAAgB,CAACF,WAAA,IAAe,CAACC,eAAiB;IAC9C;EAAA;EAGT,MAAMG,eAAkB;IACtB1O,CAAA,GAAGjF,EAAa,GAAAuT,WAAA,oBAAAA,WAAA,CAAAtO,CAAA,KAAb,IAAkB,GAAAjF,EAAA;IACrBkF,CAAA,GAAGjF,EAAa,GAAAsT,WAAA,oBAAAA,WAAA,CAAArO,CAAA,KAAb,IAAkB,GAAAjF,EAAA;EAAA,CACvB;EAEA,MAAM2T,mBAAsB;IAC1B3O,CAAA,GAAG/E,EAAiB,GAAAsT,eAAA,oBAAAA,eAAA,CAAAvO,CAAA,KAAjB,IAAsB,GAAA/E,EAAA;IACzBgF,CAAA,GAAG+N,EAAiB,GAAAO,eAAA,oBAAAA,eAAA,CAAAtO,CAAA,KAAjB,IAAsB,GAAA+N,EAAA;EAAA,CAC3B;EAEA,MAAMY,gBAAmB;IACvB5O,CAAA,GAAGiO,EAAc,GAAAO,YAAA,oBAAAA,YAAA,CAAAxO,CAAA,KAAd,IAAmB,GAAAiO,EAAA;IACtBhO,CAAA,GAAGiO,EAAc,GAAAM,YAAA,oBAAAA,YAAA,CAAAvO,CAAA,KAAd,IAAmB,GAAAiO,EAAA;IACtBtB,MAAA,GAAQuB,EAAc,GAAAK,YAAA,oBAAAA,YAAA,CAAA5B,MAAA,KAAd,IAAwB,GAAAuB,EAAA;IAChCtB,MAAA,GAAQuB,EAAc,GAAAI,YAAA,oBAAAA,YAAA,CAAA3B,MAAA,KAAd,IAAwB,GAAAuB,EAAA;EAAA,CAClC;EAEO;IACLpO,CAAA,EAAG2O,mBAAoB,CAAA3O,CAAA,GAAI4O,gBAAiB,CAAA5O,CAAA;IAC5CC,CAAA,EAAG0O,mBAAoB,CAAA1O,CAAA,GAAI2O,gBAAiB,CAAA3O,CAAA;IAC5C0N,CAAA,GAAGU,EAAiB,GAAAE,eAAA,oBAAAA,eAAA,CAAAZ,CAAA,KAAjB,IAAsB,GAAAU,EAAA;IACzBzB,MAAA,EAAQ8B,eAAgB,CAAA1O,CAAA,GAAI4O,gBAAiB,CAAAhC,MAAA;IAC7CC,MAAA,EAAQ6B,eAAgB,CAAAzO,CAAA,GAAI2O,gBAAiB,CAAA/B;EAAA,CAC/C;AACF;AAEA,SAAS4B,qBAAqB9B,SAAmB;EAC3C,IAAAA,SAAA,CAAUkC,UAAW,YAAW,CAAG;IACrC,MAAMC,cAAA,GAAiBnC,SAAU,CAAAoC,KAAA,CAAM,GAAG,EAAE,EAAExB,KAAA,CAAM,IAAI;IAEjD;MACLvN,CAAA,EAAG,CAAC8O,cAAA,CAAe,EAAE;MACrB7O,CAAA,EAAG,CAAC6O,cAAA,CAAe,EAAE;MACrBlC,MAAA,EAAQ,CAACkC,cAAA,CAAe,CAAC;MACzBjC,MAAA,EAAQ,CAACiC,cAAA,CAAe,CAAC;IAAA,CAC3B;EAAA,CACS,UAAAnC,SAAA,CAAUkC,UAAW,UAAS,CAAG;IAC1C,MAAMC,cAAA,GAAiBnC,SAAU,CAAAoC,KAAA,CAAM,GAAG,EAAE,EAAExB,KAAA,CAAM,IAAI;IAEjD;MACLvN,CAAA,EAAG,CAAC8O,cAAA,CAAe,CAAC;MACpB7O,CAAA,EAAG,CAAC6O,cAAA,CAAe,CAAC;MACpBlC,MAAA,EAAQ,CAACkC,cAAA,CAAe,CAAC;MACzBjC,MAAA,EAAQ,CAACiC,cAAA,CAAe,CAAC;IAAA,CAC3B;EAAA;EAGK;AACT;;;AClEY,IAAAE,eAAA,mBAAAC,gBAAL;EACLA,gBAAA,CAAAA,gBAAA,WAAO,CAAP;EACAA,gBAAA,CAAAA,gBAAA,cAAU,CAAV;EACAA,gBAAA,CAAAA,gBAAA,cAAU,EAAV;EAHU,OAAAA,gBAAA;AAAA,GAAAD,eAAA;AAMZ,IAAME,gBAAyC;EAC7ClP,CAAG;EACHC,CAAG;AACL;AAEA,IAAMkP,gBAAyC;EAC7CnP,CAAG;EACHC,CAAG;AACL;AAOO,SAASmP,mBACd1F,iBAAA,EACA2F,WACA,EAAAC,MAAA,EAIA;EAAA,IAHAC,YAAA,GAAAxS,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAe,EACf;EAAA,IAAAyS,mBAAA,GAAAzS,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAsBmS,gBACtB;EAAA,IAAAO,SAAA,GAAA1S,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAYoS,gBACZ;EACM;IAACnP,CAAG;IAAAC;EAAA,CAAK,GAAAoP,WAAA;EACT;IAACnS,IAAA;IAAMgN,KAAO;IAAAE,QAAA;IAAUD,MAAA;IAAQE;EAAO,IAC3CZ,iBAAA,CAAkBC,iBAAiB;EAC/B,MAAAgG,cAAA,GAAiBjD,iBAAA,CAAkB/C,iBAAiB;EACpD,MAAAqE,cAAA,GAAiBxC,iBAAkB,CAAA7B,iBAAA,EAAmB,IAAI;EAC1D,MAAAiG,eAAA,GAAkB7B,cAAA,CAAeC,cAAc;EACrD,MAAM6B,eACJ,GAAAD,eAAA,KAAoB,IAAO,IAAAA,eAAA,oBAAAA,eAAA,CAAiB/C,MAAA,IAAS,CAAI;EAC3D,MAAMiD,eACJ,GAAAF,eAAA,KAAoB,IAAO,IAAAA,eAAA,oBAAAA,eAAA,CAAiB9C,MAAA,IAAS,CAAI;EAC3D,MAAMiD,mBAAA,GAAsB,IAAI1J,SAAA,CAC9BlJ,IAAK,CAAArD,IAAA,GAAO6V,cAAe,CAAA9C,MAAA,GAAS8C,cAAe,CAAA1P,CAAA,EACnD9C,IAAK,CAAAtD,GAAA,GAAM8V,cAAe,CAAA7C,MAAA,GAAS6C,cAAe,CAAAzP,CAAA,EAClD/C,IAAA,CAAKxD,KAAA,GAAQgW,cAAe,CAAA9C,MAAA,EAC5B1P,IAAA,CAAKvD,MAAA,GAAS+V,cAAe,CAAA7C,MAAA,CAC/B;EACA,MAAMkD,SAA2C;IAC/C/P,CAAG;IACHC,CAAG;EAAA,CACL;EACA,MAAM+P,KAAQ;IACZhQ,CAAG;IACHC,CAAG;EAAA,CACL;EACA,MAAMgQ,UAAY;IAChBtW,MAAA,EAAQmW,mBAAoB,CAAAnW,MAAA,GAAS6V,mBAAoB,CAAAvP,CAAA;IACzDvG,KAAA,EAAOoW,mBAAoB,CAAApW,KAAA,GAAQ8V,mBAAoB,CAAAxP;EAAA,CACzD;EAGG,MAACkK,KAAA,IAAU2F,eAAmB,KAACzF,QAAA,KAChCnK,CAAK,IAAA6P,mBAAA,CAAoBlW,GAAM,GAAAqW,UAAA,CAAUtW,MACzC,KAAA2V,MAAA,oBAAAA,MAAA,CAAQrP,CAAA,MAAM,CACd,kBAAAD,CAAA,IAAK8P,mBAAA,CAAoBjW,IAAO,GAAA4V,SAAA,CAAUzP,CAAA,IAC1CA,CAAK,IAAA8P,mBAAA,CAAoB/V,KAAQ,GAAA0V,SAAA,CAAUzP,CAC3C;IAEU+P,SAAA,CAAA9P,CAAA,GAAI4P,eAAA,GACV,CACA;IACEG,KAAA,CAAA/P,CAAA,GACJsP,YAAA,GACA5R,IAAK,CAAAuS,GAAA,EACFJ,mBAAoB,CAAAlW,GAAA,GAAMqW,UAAU,CAAAtW,MAAA,GAASsG,CAAA,IAAKgQ,UAAU,CAAAtW,MAAA,CAC/D;EAAA,CACJ,WACG,CAACyQ,QAAa,IAAAyF,eAAA,IAAmB,CAAC3F,KACnC,KAAAjK,CAAA,IAAK6P,mBAAoB,CAAAhW,MAAA,GAASmW,UAAU,CAAAtW,MAAA,KAC5C2V,MAAA,oBAAAA,MAAA,CAAQrP,CAAM,0BACdD,CAAK,IAAA8P,mBAAA,CAAoBjW,IAAO,GAAA4V,SAAA,CAAUzP,CAAA,IAC1CA,CAAK,IAAA8P,mBAAA,CAAoB/V,KAAQ,GAAA0V,SAAA,CAAUzP,CAC3C;IAEU+P,SAAA,CAAA9P,CAAA,GAAI4P,eAAA,GACV,EACA;IACEG,KAAA,CAAA/P,CAAA,GACJsP,YAAA,GACA5R,IAAK,CAAAuS,GAAA,EACFJ,mBAAoB,CAAAhW,MAAA,GAASmW,UAAU,CAAAtW,MAAA,GAASsG,CAAA,IAAKgQ,UAAU,CAAAtW,MAAA,CAClE;EAAA;EAID,MAAC0Q,OAAA,IAAYuF,eAAmB,KAACzF,MAAA,KAClCnK,CAAK,IAAA8P,mBAAA,CAAoB/V,KAAQ,GAAAkW,UAAA,CAAUvW,KAC3C,KAAA4V,MAAA,oBAAAA,MAAA,CAAQtP,CAAA,MAAM,EACd,kBAAAC,CAAA,IAAK6P,mBAAA,CAAoBlW,GAAM,GAAA6V,SAAA,CAAUxP,CAAA,IACzCA,CAAK,IAAA6P,mBAAA,CAAoBhW,MAAS,GAAA2V,SAAA,CAAUxP,CAC5C;IAEU8P,SAAA,CAAA/P,CAAA,GAAI4P,eAAA,GACV,EACA;IACEI,KAAA,CAAAhQ,CAAA,GACJuP,YAAA,GACA5R,IAAK,CAAAuS,GAAA,EACFJ,mBAAoB,CAAA/V,KAAA,GAAQkW,UAAU,CAAAvW,KAAA,GAAQsG,CAAA,IAAKiQ,UAAU,CAAAvW,KAAA,CAChE;EAAA,CACJ,WACG,CAACyQ,MAAW,IAAAyF,eAAA,IAAmB,CAACvF,OACjC,KAAArK,CAAA,IAAK8P,mBAAoB,CAAAjW,IAAA,GAAOoW,UAAU,CAAAvW,KAAA,KAC1C4V,MAAA,oBAAAA,MAAA,CAAQtP,CAAM,yBACdC,CAAK,IAAA6P,mBAAA,CAAoBlW,GAAM,GAAA6V,SAAA,CAAUxP,CAAA,IACzCA,CAAK,IAAA6P,mBAAA,CAAoBhW,MAAS,GAAA2V,SAAA,CAAUxP,CAC5C;IAEU8P,SAAA,CAAA/P,CAAA,GAAI4P,eAAA,GACV,CACA;IACEI,KAAA,CAAAhQ,CAAA,GACJuP,YAAA,GACA5R,IAAK,CAAAuS,GAAA,EACFJ,mBAAoB,CAAAjW,IAAA,GAAOoW,UAAU,CAAAvW,KAAA,GAAQsG,CAAA,IAAKiQ,UAAU,CAAAvW,KAAA,CAC/D;EAAA;EAGG;IACLqW,SAAA;IACAC;EAAA,CACF;AACF;;;ACrIA,SAASG,+BACPpX,OAGA;EACA,OACE,wBAA4B,IAAAA,OAAA,IAC5B,OAAOA,OAAA,CAAQqX,sBAA2B;AAE9C;AAEO,SAASA,uBAAuBzO,EAAa,EAAwB;EAAA,IAAxB0O,cAAA,GAAAtT,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAiB,KAAO;EACtE,IAAAoT,8BAAA,CAA+BxO,EAAE,CAAG;IACtCA,EAAA,CAAGyO,sBAAA,CAAuBC,cAAc;IACxC;EAAA;EAGE,KAAC5U,aAAc,CAAAkG,EAAE,CAAG;IACtB,OAAOA,EAAA,CAAG2O,cAAe;EAAA;EAGvB,IAAAxO,MAAA,GAASyK,0BAAA,CAA2B5K,EAAE;EAEtC,KAAClG,aAAc,CAAAqG,MAAM,CAAG;IAC1B;EAAA;EAGF,MAAMyO,mBAAsB,GAAAhF,iBAAA,CAAkBzJ,MAAQ,MAAI;IACxD0O,oBAAuB,GAAA3C,QAAA,CACrB0C,mBAAA,CAAoBE,gBAAA,CAAiB,kBAAkB;IAEzDC,qBAAwB,GAAA7C,QAAA,CACtB0C,mBAAA,CAAoBE,gBAAA,CAAiB,mBAAmB;IAE1DE,OAAU,GAAAhP,EAAA,CAAGiP,SAAA,GAAY9O,MAAO,CAAA8O,SAAA,GAAY9O,MAAA,CAAOiI,SACnD;IAAA8G,UAAA,GACElP,EAAG,CAAAiP,SAAA,GAAY9O,MAAA,CAAO8O,SAAY,GAAAjP,EAAA,CAAGxF,YAAA,GAAeqU,oBACpD,GAAA1O,MAAA,CAAOiI,SAAA,GAAYjI,MAAO,CAAA3F,YAAA;IAC5B2U,QAAW,GAAAnP,EAAA,CAAGoP,UAAA,GAAajP,MAAO,CAAAiP,UAAA,GAAajP,MAAA,CAAOgI,UACtD;IAAAkH,SAAA,GACErP,EAAA,CAAGoP,UACD,GAAAjP,MAAA,CAAOiP,UAAA,GACPpP,EAAG,CAAAzF,WAAA,GACHwU,qBAAA,GACF5O,MAAO,CAAAgI,UAAA,GAAahI,MAAA,CAAO5F,WAC7B;IAAA+U,YAAA,GAAeN,OAAA,IAAW,CAACE,UAAA;EAExB,KAAAF,OAAA,IAAWE,UAAA,KAAeR,cAAgB;IACtCvO,MAAA,CAAAiI,SAAA,GACLpI,EAAG,CAAAiP,SAAA,GACH9O,MAAO,CAAA8O,SAAA,GACP9O,MAAA,CAAO3F,YAAe,OACtBqU,oBACA,GAAA7O,EAAA,CAAGxF,YAAe;EAAA;EAGjB,KAAA2U,QAAA,IAAYE,SAAA,KAAcX,cAAgB;IACtCvO,MAAA,CAAAgI,UAAA,GACLnI,EAAG,CAAAoP,UAAA,GACHjP,MAAO,CAAAiP,UAAA,GACPjP,MAAA,CAAO5F,WAAc,OACrBwU,qBACA,GAAA/O,EAAA,CAAGzF,WAAc;EAAA;EAGrB,KAAKyU,OAAW,IAAAE,UAAA,IAAcC,QAAY,IAAAE,SAAA,KAAc,CAACX,cAAgB;IACvE1O,EAAA,CAAG2O,cAAA,CAAeW,YAAY;EAAA;AAElC;;;ACpEO,SAASC,eACdhU,IACA,EAAAyS,eAAA,EACAwB,eACmB;EACnB,MAAM;IAACvE,MAAQ;IAAAC,MAAA;IAAQ7M,CAAA,EAAGoR,UAAY;IAAAnR,CAAA,EAAGoR;EAAA,CAAc,GAAA1B,eAAA;EACvD,MAAM3P,CAAA,GAAI9C,IAAK,CAAArD,IAAA,GAAOuX,UAAA,IAAc,CAAI,GAAAxE,MAAA,IAAUO,UAAA,CAAWgE,eAAe;EAC5E,MAAMlR,CACJ,GAAA/C,IAAA,CAAKtD,GACL,GAAAyX,UAAA,IACC,IAAIxE,MACH,IAAAM,UAAA,CAAWgE,eAAgB,CAAApC,KAAA,CAAMoC,eAAgB,CAAAG,OAAA,CAAQ,GAAG,IAAI,CAAC,CAAC;EACtE,MAAMC,CAAI,GAAA3E,MAAA,GAAS1P,IAAK,CAAAxD,KAAA,GAAQkT,MAAA,GAAS1P,IAAK,CAAAxD,KAAA;EAC9C,MAAM8X,CAAI,GAAA3E,MAAA,GAAS3P,IAAK,CAAAvD,MAAA,GAASkT,MAAA,GAAS3P,IAAK,CAAAvD,MAAA;EAExC;IACLD,KAAO,EAAA6X,CAAA;IACP5X,MAAQ,EAAA6X,CAAA;IACR5X,GAAK,EAAAqG,CAAA;IACLlG,KAAA,EAAOiG,CAAI,GAAAuR,CAAA;IACXzX,MAAA,EAAQmG,CAAI,GAAAuR,CAAA;IACZ3X,IAAM,EAAAmG;EAAA,CACR;AACF;;;ACvBO,SAASyR,iBACdvU,IACA,EAAAyS,eAAA,EACAwB,eACmB;EACnB,MAAM;IAACvE,MAAQ;IAAAC,MAAA;IAAQ7M,CAAA,EAAGoR,UAAY;IAAAnR,CAAA,EAAGoR;EAAA,CAAc,GAAA1B,eAAA;EACvD,MAAM3P,CAAA,GAAI9C,IAAK,CAAArD,IAAA,GAAOuX,UAAA,IAAc,CAAI,GAAAxE,MAAA,IAAUO,UAAA,CAAWgE,eAAe;EAC5E,MAAMlR,CACJ,GAAA/C,IAAA,CAAKtD,GACL,GAAAyX,UAAA,IACC,IAAIxE,MACH,IAAAM,UAAA,CAAWgE,eAAgB,CAAApC,KAAA,CAAMoC,eAAgB,CAAAG,OAAA,CAAQ,GAAG,IAAI,CAAC,CAAC;EACtE,MAAMC,CAAI,GAAA3E,MAAA,GAAS1P,IAAK,CAAAxD,KAAA,GAAQkT,MAAA,GAAS1P,IAAK,CAAAxD,KAAA;EAC9C,MAAM8X,CAAI,GAAA3E,MAAA,GAAS3P,IAAK,CAAAvD,MAAA,GAASkT,MAAA,GAAS3P,IAAK,CAAAvD,MAAA;EAExC;IACLD,KAAO,EAAA6X,CAAA;IACP5X,MAAQ,EAAA6X,CAAA;IACR5X,GAAK,EAAAqG,CAAA;IACLlG,KAAA,EAAOiG,CAAI,GAAAuR,CAAA;IACXzX,MAAA,EAAQmG,CAAI,GAAAuR,CAAA;IACZ3X,IAAM,EAAAmG;EAAA,CACR;AACF;;;ACrBO,SAAS0R,gBAAiBA,CAAAC,KAAA,EAA0C;EAAA,IAA1C;IAAC5Y,OAAS;IAAAO,SAAA;IAAWwH;EAAA,CAAqB,GAAA6Q,KAAA;EACzE,OAAO5Y,OAAQ,CAAA6Y,OAAA,CAAQtY,SAAW,EAAAwH,OAAO,CAAE,CAAA+Q,QAAA;AAC7C;;;ACHO,SAASC,iBACd/Y,OAAA,EAOA;EAAA,IANA2U,SAAY,GAAA3Q,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAAwO,iBAAA,CAAkBxS,OAAO,CAAE,CAAA2U,SAAA;EAAA,IACvCqE,SAAA,GAAAhV,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAY,IAKZ;EACA,IAAIgV,SAAW;IACb,MAAMC,QAAW,GAAAlZ,gBAAA,CACfC,OAAA,EACCkZ,SAAA,IAAa,WAAe,IAAAA,SAAA,CAC/B;IAEA,IAAID,QAAU;MACZ,MAAM;QAACtE,SAAA,EAAAwE,UAAA,GAAY;MAAE,IAAIF,QAAA,CAAS,CAAC;MAE/B,WAAOE,UAAA,KAAc,QAAU;QAC3B,MAAAC,cAAA,GAAiB1E,cAAA,CAAeyE,UAAS;QAE/C,IAAIC,cAAgB;UACX,OAAAA,cAAA;QAAA;MACT;IACF;EACF;EAGF,IAAIzE,SAAW;IACP,MAAAyE,cAAA,GAAiB1E,cAAA,CAAeC,SAAS;IAE/C,IAAIyE,cAAgB;MACX,OAAAA,cAAA;IAAA;EACT;EAGF,OAAO;IAACnS,CAAG;IAAGC,CAAG;IAAG0N,CAAA,EAAG;EAAC;AAC1B;;;ACvCA,IAAMyE,UAAA,GAAY,IAAI5H,SAAU,CAACpI,QAAA,IAAaG,UAAW,CAAAH,QAAA,EAAU,CAAC,CAAC;AACrE,IAAMiQ,UAAA,sBAAiB/G,GAAqC;AAC5D,IAAMgH,MAAQ,GAAAD,UAAA,CAAW1R,KAAM,CAAAK,IAAA,CAAKqR,UAAU;AAE9C,SAASE,sBAAsBxZ,OAA+B;EAC5D,MAAM+G,SAAA,GAAW/G,OAAQ,CAAAoC,aAAA;EACrB,IAAAqX,kBAAA,GAAqBH,UAAW,CAAAhL,GAAA,CAAIvH,SAAQ;EAEhD,IAAI0S,kBAAA,EAA2B,OAAAA,kBAAA;EAE/BA,kBAAA,GAAqB1S,SAAA,CAAS5G,aAAc;EACjCmZ,UAAA,CAAA3K,GAAA,CAAI5H,SAAA,EAAU0S,kBAAkB;EAC3CJ,UAAA,CAAUnH,QAAA,CAASqH,MAAK;EAExB,MAAMG,iBAAA,GAAoBD,kBAAmB,CAAAhZ,MAAA,CAC1CJ,SAAA,IACCV,gBAAiB,CAAAU,SAAA,CAAUT,MAAM,CAAK,IAAAS,SAAA,CAAUT,MAAA,CAAOmC,MAAW,KAAA/B,OAAA,CACtE;EAEWsZ,UAAA,CAAA3K,GAAA,CAAI3O,OAAA,EAAS0Z,iBAAiB;EAElC,OAAAD,kBAAA;AACT;AASO,SAASE,sBACd3Z,OAAA,EACA+H,OAI0B;EAC1B,MAAM7H,WAAA,GAAasZ,qBAAsB,CAAAxZ,OAAO,CAC7C,CAAAS,MAAA,CAAQJ,SAAc;IA1C3B,IAAA2B,EAAA,EAAAC,EAAA;IA2CU,IAAAtC,gBAAA,CAAiBU,SAAU,CAAAT,MAAM,CAAG;MAChC;QAACmC;MAAM,IAAI1B,SAAU,CAAAT,MAAA;MAC3B,MAAMga,aAAA,IACH3X,EAAU,GAAAF,MAAA,MAAAC,EAAA,GAAA+F,OAAA,CAAQ6R,aAAR,qBAAA5X,EAAA,CAAAL,IAAA,CAAAoG,OAAA,EAAwBhG,MAAA,OAAlC,IAA8C,GAAAE,EAAA;MAEjD,IAAI2X,aAAe;QACjB,OAAOvZ,SAAA,CAAUT,MAAO,CAAAE,YAAA,EAAe,CAAAkT,IAAA,CAAMiG,QAAa;UAC7C,WAAAhG,QAAA,IAAYlL,OAAA,CAAQgL,UAAY;YACrC,IAAAkG,QAAA,CAAShG,QAAQ,GAAU;UAAA;QACjC,CACD;MAAA;IACH;EACF,CACD,EACA4G,GAAI,CAACxZ,SAAc;IACZ;MAACT,MAAQ;MAAAka;IAAA,CAAe,GAAAzZ,SAAA;IACxB,MAAAiJ,QAAA,GAAW1J,MAAA,oBAAAA,MAAA,CAAQma,iBAAoB,GAAAzQ,QAAA;IAE7C,IAAIjJ,SAAU,CAAAuR,OAAA,IAAWvR,SAAU,CAAAC,SAAA,KAAc,UAAY;IAE7D,IACE,OAAOgJ,QAAY,gBACnB,OAAOwQ,WAAe,gBACtBA,WAAA,GAAcxQ,QACd;MACAjJ,SAAA,CAAUyZ,WAAc,GAAAxQ,QAAA;MAExB,OAAO,MAAM;QACXjJ,SAAA,CAAUyZ,WAAc,GAAAA,WAAA;MAAA,CAC1B;IAAA;EACF,CACD;EAEC,IAAA5Z,WAAA,CAAWE,MAAA,GAAS,CAAG;IACzB,OAAO,MAAMF,WAAA,CAAWgG,OAAQ,CAAC8T,KAAA,IAAUA,KAAS,oBAAAA,KAAA;EAAA;AAExD;;;AC5Da,IAAAC,YAAA,GAAN,cAA2B5M,SAAU;EAC1C5F,WAAYA,CAAAzH,OAAA,EAAqD;IAAA,IAAnC+H,OAA+B,GAAA/D,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,QAAI;IApBnE,IAAAhC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAA+S,EAAA;IAqBU;MACJ0B,cAAA,GAAiBjD,iBAAA,CAAkB1T,OAAO;MAC1Cka,gBAAA;MACAjZ,qBAAwB,GAAAP;IAAA,CACtB,GAAAqH,OAAA;IACE,MAAAoS,eAAA,GAAkBR,qBAAA,CAAsB3Z,OAAS;MACrD+S,UAAA,EAAY,CAAC,aAAa,WAAa,WAAS,SAAS,QAAQ;MACjE6G,aAAA,EAAgB7X,MACb,KAAAA,MAAA,KAAW/B,OAAA,IAAWqF,QAAS,OAAMtD,MAAO,CAAAgO,QAAA,CAAS/P,OAAO;IAAA,CAChE;IACK,MAAAgU,iBAAA,GAAoB/S,qBAAA,CAAsBjB,OAAO;IACvD,IAAI;MAACa,GAAA;MAAKC,IAAM;MAAAH,KAAA;MAAOC;IAAA,CAAU,GAAAoT,iBAAA;IAC7B,IAAAoG,OAAA;IAEE,MAAApF,cAAA,GAAiBxC,iBAAA,CAAkBxS,OAAO;IAC1C,MAAA4W,eAAA,GAAkB7B,cAAA,CAAeC,cAAc;IAErD,MAAMV,KAAQ;MACZrN,CAAA,GAAGjF,EAAiB,GAAA4U,eAAA,oBAAAA,eAAA,CAAA/C,MAAA,KAAjB,IAA2B,GAAA7R,EAAA;MAC9BkF,CAAA,GAAGjF,EAAiB,GAAA2U,eAAA,oBAAAA,eAAA,CAAA9C,MAAA,KAAjB,IAA2B,GAAA7R,EAAA;IAAA,CAChC;IAEM,MAAAoY,kBAAA,GAAqBC,qBAAsB,CAAAta,OAAA,EAASgV,cAAc;IAExEmF,eAAA,oBAAAA,eAAA;IAEA,IAAIvD,eAAiB;MACTwD,OAAA,GAAA1B,gBAAA,CACR1E,iBAAA,EACA4C,eAAA,EACA5B,cAAe,CAAAoD,eAAA,CACjB;MAEA,IAAI8B,gBAAA,IAAoBG,kBAAoB;QAC1CxZ,GAAA,GAAMuZ,OAAQ,CAAAvZ,GAAA;QACdC,IAAA,GAAOsZ,OAAQ,CAAAtZ,IAAA;QACfH,KAAA,GAAQyZ,OAAQ,CAAAzZ,KAAA;QAChBC,MAAA,GAASwZ,OAAQ,CAAAxZ,MAAA;MAAA;IACnB;IAGF,MAAM2Z,SAAY;MAChB5Z,KAAA,GAAOuB,EAAS,GAAAkY,OAAA,oBAAAA,OAAA,CAAAzZ,KAAA,KAAT,IAAkB,GAAAuB,EAAA,GAAAvB,KAAA;MACzBC,MAAA,GAAQqU,EAAS,GAAAmF,OAAA,oBAAAA,OAAA,CAAAxZ,MAAA,KAAT,IAAmB,GAAAqU,EAAA,GAAArU;IAAA,CAC7B;IAEI,IAAAyZ,kBAAA,IAAsB,CAACH,gBAAA,IAAoBE,OAAS;MACtD,MAAMpB,SAAY,GAAAb,cAAA,CAChBiC,OAAA,EACAC,kBAAA,EACArF,cAAe,CAAAoD,eAAA,CACjB;MAEAvX,GAAA,GAAMmY,SAAU,CAAAnY,GAAA;MAChBC,IAAA,GAAOkY,SAAU,CAAAlY,IAAA;MACjBH,KAAA,GAAQqY,SAAU,CAAArY,KAAA;MAClBC,MAAA,GAASoY,SAAU,CAAApY,MAAA;MACnB0T,KAAA,CAAMrN,CAAA,GAAIoT,kBAAmB,CAAAxG,MAAA;MAC7BS,KAAA,CAAMpN,CAAA,GAAImT,kBAAmB,CAAAvG,MAAA;IAAA;IAG/B,IAAI6C,cAAgB;MAClB,IAAI,CAACuD,gBAAkB;QACrBpZ,IAAA,IAAQ6V,cAAe,CAAA9C,MAAA;QACvBlT,KAAA,IAASgW,cAAe,CAAA9C,MAAA;QACxBhT,GAAA,IAAO8V,cAAe,CAAA7C,MAAA;QACtBlT,MAAA,IAAU+V,cAAe,CAAA7C,MAAA;MAAA;MAG3BhT,IAAA,IAAQ6V,cAAe,CAAA1P,CAAA;MACvBpG,GAAA,IAAO8V,cAAe,CAAAzP,CAAA;IAAA;IAGlB,MAAApG,IAAA,EAAMD,GAAK,EAAAF,KAAA,EAAOC,MAAM;IAE9B,KAAK0T,KAAQ,GAAAA,KAAA;IACb,KAAKkG,cAAA,GAAiBD,SAAU,CAAA5Z,KAAA;IAChC,KAAK8Z,eAAA,GAAkBF,SAAU,CAAA3Z,MAAA;EAAA;AAKrC;AAKA,SAAS0Z,sBACPta,OAAA,EACAgV,cACkB;EA/GpB,IAAAhT,EAAA;EAiHQ,MAAA9B,WAAA,GAAaF,OAAA,CAAQG,aAAc;EACzC,IAAIka,kBAAuC;EAEvC,KAACna,WAAW,CAAAE,MAAA,EAAe;EAE/B,WAAWC,SAAA,IAAaH,WAAY;IAC9B,IAAAG,SAAA,CAAUC,SAAA,KAAc,SAAW;IACjC,MAAAC,SAAA,GAAYZ,gBAAA,CAAiBU,SAAU,CAAAT,MAAM,IAC/CS,SAAU,CAAAT,MAAA,CAAOE,YAAa,KAC9B,EAAC;IACL,MAAMmZ,QAAW,GAAA1Y,SAAA,CAAUA,SAAU,CAAAH,MAAA,GAAS,CAAC;IAE/C,IAAI,CAAC6Y,QAAU;IAEf,MAAM;MAACrF,SAAA;MAAWe,SAAW;MAAAL;IAAA,CAAS,GAAA2E,QAAA;IAElC,IAAArF,SAAA,IAAae,SAAA,IAAaL,KAAO;MACnC,MAAMsC,eAAA,GAAkB7B,cAAe;QACrCnB,SAAA,EACE,OAAOA,SAAA,KAAc,QAAY,IAAAA,SAAA,GAC7BA,SAAA,GACAoB,cAAe,CAAApB,SAAA;QACrBe,SAAA,EACE,OAAOA,SAAA,KAAc,QAAY,IAAAA,SAAA,GAC7BA,SAAA,GACAK,cAAe,CAAAL,SAAA;QACrBL,KAAA,EACE,OAAOA,KAAA,KAAU,QAAY,IAAAA,KAAA,GAAQA,KAAA,GAAQU,cAAe,CAAAV;MAAA,CAC/D;MAED,IAAIsC,eAAiB;QACnByD,kBAAA,GAAqBA,kBACjB;UACEpT,CAAA,EAAGoT,kBAAmB,CAAApT,CAAA,GAAI2P,eAAgB,CAAA3P,CAAA;UAC1CC,CAAA,EAAGmT,kBAAmB,CAAAnT,CAAA,GAAI0P,eAAgB,CAAA1P,CAAA;UAC1C0N,CAAG,GAAA5S,EAAA,GAAAqY,kBAAA,CAAmBzF,CAAnB,YAAA5S,EAAA,GAAwB4U,eAAgB,CAAAhC,CAAA;UAC3Cf,MAAA,EAAQwG,kBAAmB,CAAAxG,MAAA,GAAS+C,eAAgB,CAAA/C,MAAA;UACpDC,MAAA,EAAQuG,kBAAmB,CAAAvG,MAAA,GAAS8C,eAAgB,CAAA9C;QAAA,CAEtD,GAAA8C,eAAA;MAAA;IACN;EACF;EAGK,OAAAyD,kBAAA;AACT;;;AC9JO,SAASK,cACd1a,OACmD;EAEjD,kBAAWA,OAAA,IACX,OAAOA,OAAA,CAAQsD,KAAA,KAAU,QACzB,IAAAtD,OAAA,CAAQsD,KAAU,aAClB,aAAiB,IAAAtD,OAAA,CAAQsD,KAAA,IACzB,gBAAoB,IAAAtD,OAAA,CAAQsD,KAC5B,WAAOtD,OAAQ,CAAAsD,KAAA,CAAMqX,WAAA,KAAgB,UACrC,WAAO3a,OAAQ,CAAAsD,KAAA,CAAMsX,cAAmB;AAE5C;;;ACFO,IAAMC,MAAA,GAAN,MAAa;EAGlBpT,YAAoBzH,OAAkB;IAAlB,KAAAA,OAAA,GAAAA,OAAA;IAFZ,KAAAuN,OAAA,sBAAcgF,GAAoB;EAAA;EAInC5D,IAAIoE,UAA6C,EAAa;IAAA,IAAb+H,MAAA,GAAA9W,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAS,EAAI;IAC7D;MAAChE;IAAA,CAAW;IAEd,KAAC0a,aAAc,CAAA1a,OAAO,CAAG;MAC3B;IAAA;IAGF,WAAW,CAAC+a,GAAK,EAAAvU,KAAK,KAAKhF,MAAO,CAAAkG,OAAA,CAAQqL,UAAU,CAAG;MACrD,MAAME,QAAW,MAAG6H,MAAM,GAAGC,GAAG;MAEhC,IAAI,CAAC,KAAKxN,OAAQ,CAAA0B,GAAA,CAAIgE,QAAQ,CAAG;QAC/B,KAAK1F,OAAA,CAAQoB,GAAI,CAAAsE,QAAA,EAAUjT,OAAA,CAAQsD,KAAM,CAAAoU,gBAAA,CAAiBzE,QAAQ,CAAC;MAAA;MAGrEjT,OAAA,CAAQsD,KAAM,CAAAqX,WAAA,CACZ1H,QAAA,EACA,OAAOzM,KAAA,KAAU,QAAW,GAAAA,KAAA,GAAQ,GAAGA,KAAK,KAC9C;IAAA;EACF;EAGK+F,OAAOwG,UAAsB,EAAa;IAAA,IAAb+H,MAAA,GAAA9W,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAS,EAAI;IACzC;MAAChE;IAAA,CAAW;IAEd,KAAC0a,aAAc,CAAA1a,OAAO,CAAG;MAC3B;IAAA;IAGF,WAAW+a,GAAA,IAAOhI,UAAY;MAC5B,MAAME,QAAW,MAAG6H,MAAM,GAAGC,GAAG;MAExB/a,OAAA,CAAAsD,KAAA,CAAMsX,cAAA,CAAe3H,QAAQ;IAAA;EACvC;EAGK+G,KAAQA,CAAA;IACP;MAACha;IAAA,CAAW;IAEd,KAAC0a,aAAc,CAAA1a,OAAO,CAAG;MAC3B;IAAA;IAGF,WAAW,CAAC+a,GAAA,EAAKvU,KAAK,KAAK,KAAK+G,OAAS;MAC/BvN,OAAA,CAAAsD,KAAA,CAAMqX,WAAY,CAAAI,GAAA,EAAKvU,KAAK;IAAA;IAGtC,IAAIxG,OAAQ,CAAAgb,YAAA,CAAa,OAAO,MAAM,EAAI;MACxChb,OAAA,CAAQib,eAAA,CAAgB,OAAO;IAAA;EACjC;AAEJ;;;AC9DO,SAASC,UAAUnZ,MAA+C;EACnE,KAACA,MAAA,EAAe;EAGlB,OAAAA,MAAA,YAAkBD,SAAU,CAAAC,MAAM,CAAE,CAAAoZ,OAAA,IACnCvZ,MAAA,CAAOG,MAAM,KAAKA,MAAO,CAAAQ,QAAA,KAAaC,IAAK,CAAA4Y,YAAA;AAEhD;;;ACRO,SAASC,gBACdhM,KACwB;EACpB,KAACA,KAAA,EAAc;EAEnB,MAAM;IAACiM;EAAA,CAAiB,GAAAxZ,SAAA,CAAUuN,KAAA,CAAMtN,MAAM;EAE9C,OAAOsN,KAAiB,YAAAiM,aAAA;AAC1B;;;ACRO,SAASC,eACdlM,KACuB;EACnB,KAACA,KAAA,EAAc;EAEnB,MAAM;IAACmM;EAAA,CAAgB,GAAA1Z,SAAA,CAAUuN,KAAA,CAAMtN,MAAM;EAE7C,OAAOsN,KAAiB,YAAAmM,YAAA;AAC1B;;;ACHO,SAASC,uBACd1U,SAGA;EACA,OAAO,qBAAyB,IAAAA,SAAA;AAClC;;;ACXO,SAAS2U,YAAY3Z,MAA4B;EACtD,IAAI,CAACmZ,SAAA,CAAUnZ,MAAM,GAAU;EAEzB;IAAC8B;EAAA,CAAW,GAAA9B,MAAA;EAElB,OACE8B,OAAY,gBAAWA,OAAY,mBAAc8X,iBAAA,CAAkB5Z,MAAM;AAE7E;AAEA,SAAS4Z,kBAAkB3b,OAAkB;EAC3C,OACEA,OAAA,CAAQqQ,YAAa,kBAAiB,KACtCrQ,OAAQ,CAAAgb,YAAA,CAAa,iBAAiB,CAAM;AAEhD;;;ACjBA,IAAMY,GAAA,GAA8B,EAAC;AAE9B,SAASC,iBAAiBf,MAAgB;EACzC,MAAAvR,EAAA,GAAKqS,GAAA,CAAId,MAAM,KAAK,OAAO,CAAI,GAAAc,GAAA,CAAId,MAAM,CAAI;EACnDc,GAAA,CAAId,MAAM,CAAI,GAAAvR,EAAA;EAEP,UAAGuR,MAAM,IAAIvR,EAAE;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}