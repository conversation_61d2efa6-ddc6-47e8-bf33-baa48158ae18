{"ast": null, "code": "import React from'react';import{Container,<PERSON>,<PERSON>,Card}from'react-bootstrap';import{<PERSON>}from'react-router-dom';import{useAuth}from'../contexts/AuthContext';import{useSiteName}from'../contexts/SettingsContext';import{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const Home=()=>{const{isAuthenticated,user}=useAuth();const siteName=useSiteName();return/*#__PURE__*/_jsxs(Container,{className:\"mt-4\",children:[/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-5\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"display-4\",children:[\"Welcome to \",siteName]}),/*#__PURE__*/_jsx(\"p\",{className:\"lead\",children:\"Your printing and document management solution\"})]})})}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(Card,{className:\"h-100\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(Card.Title,{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-print me-2\"}),\"Printing Services\"]}),/*#__PURE__*/_jsx(Card.Text,{children:\"Upload and print your documents with our professional printing services. High-quality prints delivered fast.\"}),isAuthenticated?/*#__PURE__*/_jsx(Link,{to:\"/dashboard\",className:\"btn btn-primary\",children:\"Go to Dashboard\"}):/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"btn btn-primary\",children:\"Get Started\"})]})})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(Card,{className:\"h-100\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(Card.Title,{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-wallet me-2\"}),\"Wallet System\"]}),/*#__PURE__*/_jsx(Card.Text,{children:\"Manage your credits and payments easily with our integrated wallet system. Top up and track your spending.\"}),isAuthenticated?/*#__PURE__*/_jsx(Link,{to:\"/dashboard/wallet\",className:\"btn btn-success\",children:\"View Wallet\"}):/*#__PURE__*/_jsx(Link,{to:\"/register\",className:\"btn btn-success\",children:\"Sign Up\"})]})})})]}),!isAuthenticated&&/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{className:\"bg-light\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:\"Ready to get started?\"}),/*#__PURE__*/_jsx(Card.Text,{children:\"Join thousands of users who trust us with their printing needs.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2 justify-content-center\",children:[/*#__PURE__*/_jsx(Link,{to:\"/register\",className:\"btn btn-primary\",children:\"Create Account\"}),/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"btn btn-outline-primary\",children:\"Sign In\"})]})]})})})}),isAuthenticated&&user&&/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{className:\"bg-primary text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(Card.Title,{children:[\"Welcome back, \",user.name,\"!\"]}),/*#__PURE__*/_jsx(Card.Text,{children:\"Ready to manage your printing orders and wallet?\"}),/*#__PURE__*/_jsx(Link,{to:\"/dashboard\",className:\"btn btn-light\",children:\"Go to Dashboard\"})]})})})})]});};export default Home;", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "Card", "Link", "useAuth", "useSiteName", "jsxs", "_jsxs", "jsx", "_jsx", "Home", "isAuthenticated", "user", "siteName", "className", "children", "md", "Body", "Title", "Text", "to", "name"], "sources": ["C:/laragon/www/frontend/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport { Container, Row, <PERSON>, <PERSON>, Button } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useSiteName } from '../contexts/SettingsContext';\n\nconst Home: React.FC = () => {\n  const { isAuthenticated, user } = useAuth();\n  const siteName = useSiteName();\n\n  return (\n    <Container className=\"mt-4\">\n      <Row>\n        <Col>\n          <div className=\"text-center mb-5\">\n            <h1 className=\"display-4\">Welcome to {siteName}</h1>\n            <p className=\"lead\">Your printing and document management solution</p>\n          </div>\n        </Col>\n      </Row>\n\n      <Row className=\"mb-4\">\n        <Col md={6}>\n          <Card className=\"h-100\">\n            <Card.Body>\n              <Card.Title>\n                <i className=\"fas fa-print me-2\"></i>\n                Printing Services\n              </Card.Title>\n              <Card.Text>\n                Upload and print your documents with our professional printing services.\n                High-quality prints delivered fast.\n              </Card.Text>\n              {isAuthenticated ? (\n                <Link to=\"/dashboard\" className=\"btn btn-primary\">\n                  Go to Dashboard\n                </Link>\n              ) : (\n                <Link to=\"/login\" className=\"btn btn-primary\">\n                  Get Started\n                </Link>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={6}>\n          <Card className=\"h-100\">\n            <Card.Body>\n              <Card.Title>\n                <i className=\"fas fa-wallet me-2\"></i>\n                Wallet System\n              </Card.Title>\n              <Card.Text>\n                Manage your credits and payments easily with our integrated wallet system.\n                Top up and track your spending.\n              </Card.Text>\n              {isAuthenticated ? (\n                <Link to=\"/dashboard/wallet\" className=\"btn btn-success\">\n                  View Wallet\n                </Link>\n              ) : (\n                <Link to=\"/register\" className=\"btn btn-success\">\n                  Sign Up\n                </Link>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {!isAuthenticated && (\n        <Row>\n          <Col className=\"text-center\">\n            <Card className=\"bg-light\">\n              <Card.Body>\n                <Card.Title>Ready to get started?</Card.Title>\n                <Card.Text>\n                  Join thousands of users who trust us with their printing needs.\n                </Card.Text>\n                <div className=\"d-flex gap-2 justify-content-center\">\n                  <Link to=\"/register\" className=\"btn btn-primary\">\n                    Create Account\n                  </Link>\n                  <Link to=\"/login\" className=\"btn btn-outline-primary\">\n                    Sign In\n                  </Link>\n                </div>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      )}\n\n      {isAuthenticated && user && (\n        <Row>\n          <Col className=\"text-center\">\n            <Card className=\"bg-primary text-white\">\n              <Card.Body>\n                <Card.Title>Welcome back, {user.name}!</Card.Title>\n                <Card.Text>\n                  Ready to manage your printing orders and wallet?\n                </Card.Text>\n                <Link to=\"/dashboard\" className=\"btn btn-light\">\n                  Go to Dashboard\n                </Link>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      )}\n    </Container>\n  );\n};\n\nexport default Home;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,KAAgB,iBAAiB,CACnE,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,OAAO,KAAQ,yBAAyB,CACjD,OAASC,WAAW,KAAQ,6BAA6B,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAE1D,KAAM,CAAAC,IAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,eAAe,CAAEC,IAAK,CAAC,CAAGR,OAAO,CAAC,CAAC,CAC3C,KAAM,CAAAS,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAE9B,mBACEE,KAAA,CAACR,SAAS,EAACe,SAAS,CAAC,MAAM,CAAAC,QAAA,eACzBN,IAAA,CAACT,GAAG,EAAAe,QAAA,cACFN,IAAA,CAACR,GAAG,EAAAc,QAAA,cACFR,KAAA,QAAKO,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BR,KAAA,OAAIO,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,aAAW,CAACF,QAAQ,EAAK,CAAC,cACpDJ,IAAA,MAAGK,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,gDAA8C,CAAG,CAAC,EACnE,CAAC,CACH,CAAC,CACH,CAAC,cAENR,KAAA,CAACP,GAAG,EAACc,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBN,IAAA,CAACR,GAAG,EAACe,EAAE,CAAE,CAAE,CAAAD,QAAA,cACTN,IAAA,CAACP,IAAI,EAACY,SAAS,CAAC,OAAO,CAAAC,QAAA,cACrBR,KAAA,CAACL,IAAI,CAACe,IAAI,EAAAF,QAAA,eACRR,KAAA,CAACL,IAAI,CAACgB,KAAK,EAAAH,QAAA,eACTN,IAAA,MAAGK,SAAS,CAAC,mBAAmB,CAAI,CAAC,oBAEvC,EAAY,CAAC,cACbL,IAAA,CAACP,IAAI,CAACiB,IAAI,EAAAJ,QAAA,CAAC,8GAGX,CAAW,CAAC,CACXJ,eAAe,cACdF,IAAA,CAACN,IAAI,EAACiB,EAAE,CAAC,YAAY,CAACN,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,iBAElD,CAAM,CAAC,cAEPN,IAAA,CAACN,IAAI,EAACiB,EAAE,CAAC,QAAQ,CAACN,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,aAE9C,CAAM,CACP,EACQ,CAAC,CACR,CAAC,CACJ,CAAC,cACNN,IAAA,CAACR,GAAG,EAACe,EAAE,CAAE,CAAE,CAAAD,QAAA,cACTN,IAAA,CAACP,IAAI,EAACY,SAAS,CAAC,OAAO,CAAAC,QAAA,cACrBR,KAAA,CAACL,IAAI,CAACe,IAAI,EAAAF,QAAA,eACRR,KAAA,CAACL,IAAI,CAACgB,KAAK,EAAAH,QAAA,eACTN,IAAA,MAAGK,SAAS,CAAC,oBAAoB,CAAI,CAAC,gBAExC,EAAY,CAAC,cACbL,IAAA,CAACP,IAAI,CAACiB,IAAI,EAAAJ,QAAA,CAAC,4GAGX,CAAW,CAAC,CACXJ,eAAe,cACdF,IAAA,CAACN,IAAI,EAACiB,EAAE,CAAC,mBAAmB,CAACN,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,aAEzD,CAAM,CAAC,cAEPN,IAAA,CAACN,IAAI,EAACiB,EAAE,CAAC,WAAW,CAACN,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,SAEjD,CAAM,CACP,EACQ,CAAC,CACR,CAAC,CACJ,CAAC,EACH,CAAC,CAEL,CAACJ,eAAe,eACfF,IAAA,CAACT,GAAG,EAAAe,QAAA,cACFN,IAAA,CAACR,GAAG,EAACa,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BN,IAAA,CAACP,IAAI,EAACY,SAAS,CAAC,UAAU,CAAAC,QAAA,cACxBR,KAAA,CAACL,IAAI,CAACe,IAAI,EAAAF,QAAA,eACRN,IAAA,CAACP,IAAI,CAACgB,KAAK,EAAAH,QAAA,CAAC,uBAAqB,CAAY,CAAC,cAC9CN,IAAA,CAACP,IAAI,CAACiB,IAAI,EAAAJ,QAAA,CAAC,iEAEX,CAAW,CAAC,cACZR,KAAA,QAAKO,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDN,IAAA,CAACN,IAAI,EAACiB,EAAE,CAAC,WAAW,CAACN,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,gBAEjD,CAAM,CAAC,cACPN,IAAA,CAACN,IAAI,EAACiB,EAAE,CAAC,QAAQ,CAACN,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,SAEtD,CAAM,CAAC,EACJ,CAAC,EACG,CAAC,CACR,CAAC,CACJ,CAAC,CACH,CACN,CAEAJ,eAAe,EAAIC,IAAI,eACtBH,IAAA,CAACT,GAAG,EAAAe,QAAA,cACFN,IAAA,CAACR,GAAG,EAACa,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BN,IAAA,CAACP,IAAI,EAACY,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACrCR,KAAA,CAACL,IAAI,CAACe,IAAI,EAAAF,QAAA,eACRR,KAAA,CAACL,IAAI,CAACgB,KAAK,EAAAH,QAAA,EAAC,gBAAc,CAACH,IAAI,CAACS,IAAI,CAAC,GAAC,EAAY,CAAC,cACnDZ,IAAA,CAACP,IAAI,CAACiB,IAAI,EAAAJ,QAAA,CAAC,kDAEX,CAAW,CAAC,cACZN,IAAA,CAACN,IAAI,EAACiB,EAAE,CAAC,YAAY,CAACN,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iBAEhD,CAAM,CAAC,EACE,CAAC,CACR,CAAC,CACJ,CAAC,CACH,CACN,EACQ,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}