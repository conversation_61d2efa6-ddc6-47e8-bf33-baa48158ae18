{"ast": null, "code": "import { capitalize } from '@mui/material/utils';\nexport default function convertTimelinePositionToClass(position) {\n  return position === 'alternate-reverse' ? 'positionAlternateReverse' : `position${capitalize(position)}`;\n}", "map": {"version": 3, "names": ["capitalize", "convertTimelinePositionToClass", "position"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/internal/convertTimelinePositionToClass.js"], "sourcesContent": ["import { capitalize } from '@mui/material/utils';\nexport default function convertTimelinePositionToClass(position) {\n  return position === 'alternate-reverse' ? 'positionAlternateReverse' : `position${capitalize(position)}`;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,eAAe,SAASC,8BAA8BA,CAACC,QAAQ,EAAE;EAC/D,OAAOA,QAAQ,KAAK,mBAAmB,GAAG,0BAA0B,GAAG,WAAWF,UAAU,CAACE,QAAQ,CAAC,EAAE;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}